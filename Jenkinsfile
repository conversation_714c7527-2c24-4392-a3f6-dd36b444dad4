node {
    stage('pull-code') {
        checkout scm
    }

//     try {
//         stage('deploy-account-proxy') {
//             sh "/usr/local/maven/bin/mvn -U -DskipTests -f ${env.WORKSPACE}/pom.xml clean deploy"
//         }
//     } catch (e){
//         echo 'akucun-account-proxy deploy failed'
//     }

    try {
        stage('account-proxy-facade-stub') {
           sh "/usr/local/maven/bin/mvn -U -DskipTests -f ${env.WORKSPACE}/account-proxy-facade-stub/pom.xml clean deploy"
        }
    } catch (e){
        echo 'account-proxy-facade-stub deploy failed'
    }
}