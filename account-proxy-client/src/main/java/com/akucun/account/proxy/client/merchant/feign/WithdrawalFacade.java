package com.akucun.account.proxy.client.merchant.feign;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.client.merchant.entity.QueryMerchantWithdrawalDTO;
import com.akucun.account.proxy.client.merchant.entity.ResQueryMerchantWithdrawalDTO;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @Author: silei
 * @Date: 2021/10/19
 * @desc:
 */
@FeignClient(name = "bcs-channel-wechat")
public interface WithdrawalFacade {

    @PostMapping({"/api/withdrawal/queryMerchantWithdrawal"})
    Result<ResQueryMerchantWithdrawalDTO> queryMerchantWithdrawal(@RequestBody QueryMerchantWithdrawalDTO var1);
}
