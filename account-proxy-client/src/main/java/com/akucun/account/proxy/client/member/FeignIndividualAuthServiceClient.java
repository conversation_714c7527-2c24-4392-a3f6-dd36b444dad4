package com.akucun.account.proxy.client.member;

import com.aikucun.common2.log.Logger;
import com.akucun.account.proxy.facade.stub.enums.CustomerType;
import com.akucun.common.Result;
import com.akucun.member.audit.facade.stub.fallback.api.FeignIndividualAuthService;
import com.akucun.member.audit.model.dto.auth.IndividualAuthInfoReqDTO;
import com.akucun.member.audit.model.dto.auth.IndividualAuthInfoRespDTO;
import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class FeignIndividualAuthServiceClient {

    @Autowired
    private FeignIndividualAuthService feignIndividualAuthService;

    public IndividualAuthInfoRespDTO queryIndividualInfo(String userId, String customerType){
        Logger.info("FeignIndividualAuthServiceClient.queryIndividualInfo接口入参userId:{}, customerType:{}",
                userId, customerType);
        if(StringUtils.isBlank(userId) || StringUtils.isBlank(customerType)){
            Logger.info("FeignIndividualAuthServiceClient.queryIndividualInfo接口入参不能为空");
            return null;
        }
        IndividualAuthInfoReqDTO reqDTO = new IndividualAuthInfoReqDTO();
        reqDTO.setUserId(userId);
        if(CustomerType.NM.getName().equals(customerType)){
            reqDTO.setUserType(2);//店主
        }else{
            reqDTO.setUserType(3);//店长
        }
        reqDTO.setChannel(1);//固定传1
        Result<IndividualAuthInfoRespDTO> respDTOResult = feignIndividualAuthService.info(reqDTO);
        if(respDTOResult == null || !respDTOResult.isSuccess()){
            Logger.warn("调用feignIndividualAuthService.info查询个体工商户认证信息返回失败，msg:{}", JSON.toJSONString(respDTOResult));
            return null;
        }
        if(respDTOResult.getData() == null){
            Logger.info("调用feignIndividualAuthService.info没有查询到个体工商户认证信息");
        }
        return respDTOResult.getData();
    }
}
