package com.akucun.account.proxy.client.oa;

import cn.com.weaver.services.webservices.ArrayOfArrayOfString;
import cn.com.weaver.services.webservices.ArrayOfString;
import cn.com.weaver.services.webservices.WorkflowService;
import cn.com.weaver.services.webservices.WorkflowServicePortType;
import com.aikucun.common2.log.Logger;
import com.akucun.account.proxy.client.oa.entity.WorkflowCreateRequest;
import com.akucun.account.proxy.client.oa.entity.WorkflowCreateResponse;
import com.akucun.account.proxy.client.oa.entity.WorkflowResponseCodeEnum;
import com.akucun.account.proxy.common.enums.ResponseEnum;
import com.akucun.account.proxy.common.exception.AccountProxyException;
import com.akucun.account.proxy.common.utils.HttpClient;
import com.akucun.account.proxy.facade.stub.others.oa.req.OAWorkflowCreateRequest;
import com.akucun.account.proxy.facade.stub.others.oa.resp.OAWorkflowBaseInfo;
import com.akucun.account.proxy.facade.stub.others.oa.resp.OAWorkflowCreateResponse;
import com.akucun.account.proxy.facade.stub.others.oa.resp.OAWorkflowResponseInfo;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import weaver.workflow.webservices.*;

import javax.xml.bind.JAXBElement;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Create on : 2025/1/14 17:41
 **/
@Component
public class WorkflowClient {

    // OA流程管理员用户ID
    @Value("${oa.workflow.admin.user.id:6110}")
    private Integer oaWorkflowAdminUserId;

    @Value("${oa.workflow.custom.create.address:https://oa.aikucun.com/WorkflowCustomServiceServlet}")
    private String oaWorkflowCustomCreateAddress;

    //WorkflowService 实例是线程安全的，可以被多个线程共享使用，故只需要创建一次WebService 客户端
    private static final WorkflowService INSTANCE = new WorkflowService();

    public static WorkflowService getInstance() {
        return INSTANCE;
    }

    public OAWorkflowCreateResponse createWorkflow(OAWorkflowCreateRequest request) {
        WorkflowCreateRequest workflowCreateRequest = buildWorkflowCreateRequest(request);
        Logger.info("【WorkflowClient】创建OA流程请求bizNo：{}，request：{}", request.getBizNo(), JSON.toJSONString(workflowCreateRequest));
        String responseBody = HttpClient.doPost(oaWorkflowCustomCreateAddress, JSONObject.toJSONString(workflowCreateRequest));
        Logger.info("【WorkflowClient】创建OA流程请求bizNo：{}，result：{}", request.getBizNo(), responseBody);
        if (StringUtils.isBlank(responseBody)) {
            throw new AccountProxyException(ResponseEnum.SYSTEM_EXCEPTION.getCode(), "创建OA流程失败，失败原因：响应为空");
        }
        WorkflowCreateResponse workflowCreateResponse = JSONObject.parseObject(responseBody, WorkflowCreateResponse.class);
        if (WorkflowResponseCodeEnum.SUCCESS.getCode().equals(workflowCreateResponse.getCode())) {
            OAWorkflowCreateResponse oaWorkflowCreateResponse = new OAWorkflowCreateResponse();
            oaWorkflowCreateResponse.setRequestId(workflowCreateResponse.getMsg());
            return oaWorkflowCreateResponse;
        } else {
            WorkflowResponseCodeEnum responseCodeEnum = WorkflowResponseCodeEnum.getEnumByCode(workflowCreateResponse.getCode());
            throw new AccountProxyException(ResponseEnum.SYSTEM_EXCEPTION.getCode(), responseCodeEnum != null ? "创建OA流程失败，失败原因：" + responseCodeEnum.getDesc() : "创建OA流程失败");
        }
    }

    public OAWorkflowResponseInfo queryWorkflowByRequestId(Integer requestId, Integer userId) {
        WorkflowServicePortType workflowServiceHttpPort = WorkflowClient.getInstance().getWorkflowServiceHttpPort();
        Logger.info("【WorkflowClient】查询OA流程详细信息，requestId:{}, userId:{}", requestId, userId);

        // 调用 WebService 方法，查询流程信息，未传入用户ID，默认为管理员
        userId = Objects.nonNull(userId) ? userId : oaWorkflowAdminUserId;
        WorkflowRequestInfo workflowRequestInfo = workflowServiceHttpPort.getWorkflowRequest(requestId, userId, 0);
        Logger.info("【WorkflowClient】查询OA流程详细信息，requestId:{}, userId:{}, workflowRequestInfo:{}", requestId, userId, workflowRequestInfo);
        if (workflowRequestInfo == null) {
            throw new AccountProxyException(ResponseEnum.SYSTEM_EXCEPTION.getCode(), "查询OA流程不存在");
        }

        OAWorkflowResponseInfo OAWorkflowResponseInfo = new OAWorkflowResponseInfo();
        OAWorkflowResponseInfo.setRequestId(convertToString(workflowRequestInfo.getRequestId()));
        OAWorkflowResponseInfo.setRequestName(convertToString(workflowRequestInfo.getRequestName()));
        OAWorkflowResponseInfo.setRequestLevel(convertToString(workflowRequestInfo.getRequestLevel()));
        OAWorkflowResponseInfo.setMessageType(convertToString(workflowRequestInfo.getMessageType()));

        OAWorkflowBaseInfo workflowBaseInfo = null;
        JAXBElement<WorkflowBaseInfo> workflowBaseInfoJAXBElement = workflowRequestInfo.getWorkflowBaseInfo();
        if (workflowBaseInfoJAXBElement != null && workflowBaseInfoJAXBElement.getValue() != null) {
            WorkflowBaseInfo baseInfo = workflowBaseInfoJAXBElement.getValue();
            workflowBaseInfo = new OAWorkflowBaseInfo();
            workflowBaseInfo.setWorkflowId(convertToString(baseInfo.getWorkflowId()));
            workflowBaseInfo.setWorkflowName(convertToString(baseInfo.getWorkflowName()));
            workflowBaseInfo.setWorkflowTypeId(convertToString(baseInfo.getWorkflowTypeId()));
            workflowBaseInfo.setWorkflowTypeName(convertToString(baseInfo.getWorkflowTypeName()));
        }
        OAWorkflowResponseInfo.setWorkflowBaseInfo(workflowBaseInfo);

        OAWorkflowResponseInfo.setCurrentNodeName(convertToString(workflowRequestInfo.getCurrentNodeName()));
        OAWorkflowResponseInfo.setCurrentNodeId(convertToString(workflowRequestInfo.getCurrentNodeId()));
        OAWorkflowResponseInfo.setStatus(convertToString(workflowRequestInfo.getStatus()));
        OAWorkflowResponseInfo.setCreatorId(convertToString(workflowRequestInfo.getCreatorId()));
        OAWorkflowResponseInfo.setCreateTime(convertToString(workflowRequestInfo.getCreateTime()));
        OAWorkflowResponseInfo.setLastOperatorName(convertToString(workflowRequestInfo.getLastOperatorName()));
        OAWorkflowResponseInfo.setLastOperateTime(convertToString(workflowRequestInfo.getLastOperateTime()));

        OAWorkflowResponseInfo.setCanView(workflowRequestInfo.isCanView());
        OAWorkflowResponseInfo.setCanEdit(workflowRequestInfo.isCanEdit());
        OAWorkflowResponseInfo.setMustInputRemark(workflowRequestInfo.isMustInputRemark());

        List<List<String>> workflowPhrases = null;
        JAXBElement<ArrayOfArrayOfString> workflowPhrasesElement = workflowRequestInfo.getWorkflowPhrases();
        if (workflowPhrasesElement != null && workflowPhrasesElement.getValue() != null ) {
            List<ArrayOfString> arrayOfStringList = workflowPhrasesElement.getValue().getArrayOfString();
            if (CollectionUtils.isNotEmpty(arrayOfStringList)) {
                workflowPhrases = arrayOfStringList.stream().filter(arrayOfString -> Objects.nonNull(arrayOfString.getString()))
                        .map(ArrayOfString::getString).collect(Collectors.toList());
            }
        }
        OAWorkflowResponseInfo.setWorkflowPhrases(workflowPhrases);

        boolean isSkip = false;
        String workflowNo = null;
        JAXBElement<WorkflowMainTableInfo> mainTableInfoJAXBElement = workflowRequestInfo.getWorkflowMainTableInfo();
        if (mainTableInfoJAXBElement != null && mainTableInfoJAXBElement.getValue() != null) {
            WorkflowMainTableInfo workflowMainTableInfo = mainTableInfoJAXBElement.getValue();
            JAXBElement<ArrayOfWorkflowRequestTableRecord> requestTableRecordJAXBElement = workflowMainTableInfo.getRequestRecords();
            if (requestTableRecordJAXBElement != null && requestTableRecordJAXBElement.getValue() != null) {
                Collection<WorkflowRequestTableRecord> workflowRequestTableRecords = CollectionUtils.emptyIfNull(requestTableRecordJAXBElement.getValue().getWorkflowRequestTableRecord());
                for (WorkflowRequestTableRecord record : workflowRequestTableRecords) {
                    JAXBElement<ArrayOfWorkflowRequestTableField> tableFieldJAXBElement = record.getWorkflowRequestTableFields();
                    if (tableFieldJAXBElement != null && tableFieldJAXBElement.getValue() != null) {

                        WorkflowRequestTableField workflowNoField = CollectionUtils.emptyIfNull(tableFieldJAXBElement.getValue().getWorkflowRequestTableField()).stream()
                                .filter(field -> StringUtils.equalsIgnoreCase(convertToString(field.getFieldName()), "bh")).findFirst().orElse(null);
                        if (workflowNoField != null) {
                            isSkip = true;
                            workflowNo = convertToString(workflowNoField.getFieldValue());
                        }
                    }
                    if (isSkip) {
                        break;
                    }
                }
            }
        }
        OAWorkflowResponseInfo.setWorkflowNo(workflowNo);
        Logger.info("【WorkflowClient】查询OA流程详细信息，requestId:{}, userId:{}, 转换后response:{}", requestId, userId, JSON.toJSONString(OAWorkflowResponseInfo));
        return OAWorkflowResponseInfo;
    }

    private static WorkflowCreateRequest buildWorkflowCreateRequest(OAWorkflowCreateRequest request) {
        WorkflowCreateRequest workflowCreateRequest = new WorkflowCreateRequest();
        WorkflowCreateRequest.Message message = new WorkflowCreateRequest.Message();
        message.setWorkflowid(request.getWorkflowId());
        message.setRequestname(request.getWorkflowTitle());
        message.setRequestlevel(request.getRequestLevel());
        message.setCreator(request.getCreator());
        message.setIsnextflow(request.getIsNextFlow());

        List<WorkflowCreateRequest.Field> main = new ArrayList<>();
        CollectionUtils.emptyIfNull(request.getMainFields()).forEach(mainField -> {
            WorkflowCreateRequest.Field field = new WorkflowCreateRequest.Field();
            field.setFieldname(mainField.getFieldName());
            field.setFieldvalue(mainField.getFieldValue());
            field.setFieldtype(mainField.getFieldType());
            main.add(field);
        });
        if (CollectionUtils.isNotEmpty(main)) {
            message.setMain(main);
        }

        List<WorkflowCreateRequest.DetailTable> detailTables = new ArrayList<>();
        CollectionUtils.emptyIfNull(request.getDetailTables()).forEach(detailTable -> {
            WorkflowCreateRequest.DetailTable detailTableEntity = new WorkflowCreateRequest.DetailTable();
            detailTableEntity.setDetailtableid(detailTable.getDetailTableId());

            List<List<WorkflowCreateRequest.Field>> firstFields = new ArrayList<>();
            CollectionUtils.emptyIfNull(detailTable.getFields()).forEach(detailFields -> {
                List<WorkflowCreateRequest.Field> secondFields = new ArrayList<>();
                CollectionUtils.emptyIfNull(detailFields).forEach(detailField -> {
                    WorkflowCreateRequest.Field field = new WorkflowCreateRequest.Field();
                    field.setFieldname(detailField.getFieldName());
                    field.setFieldvalue(detailField.getFieldValue());
                    field.setFieldtype(detailField.getFieldType());
                    secondFields.add(field);
                });
                if (CollectionUtils.isNotEmpty(secondFields)) {
                    firstFields.add(secondFields);
                }
            });
            if (CollectionUtils.isNotEmpty(firstFields)) {
                detailTableEntity.setData(firstFields);
            }
            detailTables.add(detailTableEntity);
        });
        if (CollectionUtils.isNotEmpty(detailTables)) {
            message.setDetail(detailTables);
        }
        workflowCreateRequest.setMessage(message);
        return workflowCreateRequest;
    }

    private String convertToString(JAXBElement<String> element) {
        if (element == null) {
            return null;
        }
        return element.getValue();
    }

    public static void main(String[] args) {
        WorkflowServicePortType workflowServiceHttpPort = WorkflowClient.getInstance().getWorkflowServiceHttpPort();
        // 调用 WebService 方法，查询流程信息，未传入用户ID，默认为管理员
        WorkflowRequestInfo workflowRequestInfo = workflowServiceHttpPort.getWorkflowRequest(263516, 6110, 0);
        System.out.println(workflowRequestInfo);
    }

}
