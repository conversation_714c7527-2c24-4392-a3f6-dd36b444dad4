package com.akucun.account.proxy.client.marketaccount;

import com.akucun.account.proxy.client.marketaccount.feign.AwardEventCallbackFeignClient;
import com.akucun.account.proxy.client.marketaccount.feign.entity.AssetAccountEventCallbackRequest;
import com.akucun.account.proxy.common.enums.ResponseEnum;
import com.akucun.account.proxy.common.exception.AccountProxyException;
import com.alibaba.fastjson.JSON;
import com.mengxiang.base.common.log.Logger;
import com.mengxiang.base.common.model.result.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

@Service
public class AwardEventCallbackClient {

    @Autowired
    private AwardEventCallbackFeignClient awardEventCallbackFeignClient;

    public void awardEventCallback(AssetAccountEventCallbackRequest request) {
        Logger.info("AwardEventCallback 账户操作 request: {}", JSON.toJSONString(request));
        Result result = awardEventCallbackFeignClient.awardEventCallback(request);
        Logger.info("AwardEventCallback 账户操作 request: {} response:{}", JSON.toJSONString(request), JSON.toJSONString(result));
        if(!result.isSuccess()){
            throw new AccountProxyException(ResponseEnum.SYSTEM_EXCEPTION, result.getMessage());
        }
    }

    public static AssetAccountEventCallbackRequest buildAssetAccountEventCallbackRequest(String actCode, String principalId, String principalType,
                                                                                          String bizNo, BigDecimal amount, String awardWay,
                                                                                          String sourceScene, String sourceNo) {
        AssetAccountEventCallbackRequest request = new AssetAccountEventCallbackRequest();
        request.setActCode(actCode);
        request.setPrincipalId(principalId);
        request.setPrincipalType(principalType);
        request.setBizNo(bizNo);
        request.setAmount(amount);
        request.setSourceScene(sourceScene);
        request.setSourceNo(sourceNo);
        request.setAwardWay(awardWay);
        request.setRequestSource("ACCOUNT-PROXY");
        return request;
    }
}
