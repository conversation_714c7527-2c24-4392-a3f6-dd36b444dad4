package com.akucun.account.proxy.client.invoice.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Create on : 2025/2/13 22:47
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("待申请销项发票申请求实体")
public class OutInvoiceAwaitingApplyReq<T> implements Serializable {

    @ApiModelProperty(value = "关联业务流水号")
    @NotBlank
    private String relatedBusinessNo;

    @ApiModelProperty(value = "业务明细科目")
    @NotBlank
    private Integer businessType;

    @ApiModelProperty(value = "申请人类型：10-商家 20-平台")
    @NotBlank
    private Integer applySubjectType;

    @ApiModelProperty(value = "申请人")
    @NotBlank
    private String applySubjectCode;

    @ApiModelProperty(value = "申请人名称")
    private String applySubjectName;

    @ApiModelProperty(value = "业务单生成时间")
    @NotBlank
    //@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    //@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date businessTime;

    @ApiModelProperty(value = "金额")
    @NotBlank
    private BigDecimal amount;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "扩展字段")
    private Map<String,Object> extensionField;

    @ApiModelProperty(value = "商品记录")
    private List<T> subDetails;

    @ApiModelProperty(value = "回调地址")
    private String notifyUrl;

}
