package com.akucun.account.proxy.client.customer;

import com.aikucun.common2.log.Logger;
import com.akucun.account.proxy.client.customer.entity.SubmitCertificationAuditReq;
import com.akucun.account.proxy.client.customer.entity.SubmitCertificationAuditVO;
import com.akucun.account.proxy.client.customer.feign.CertificationAuditFeign;
import com.akucun.common.Result;
import com.alibaba.fastjson.JSON;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Component
public class CustomerServiceClient {

    @Resource
    private CertificationAuditFeign certificationAuditFeign;

    public boolean submitApply(SubmitCertificationAuditReq submitCertificationAuditReq) {
        Logger.info("certificationAuditFeign.submit入参为:{}", JSON.toJSONString(submitCertificationAuditReq));
        Result<SubmitCertificationAuditVO> result = certificationAuditFeign.submit(submitCertificationAuditReq);
        if (result == null || !result.isSuccess()) {
            Logger.info("调用客服侧提交申请工单失败，返回结果result:{}", JSON.toJSONString(result));
            return false;
        }
        SubmitCertificationAuditVO submitCertificationAuditVO = result.getData();
        Logger.info("调用客服侧提交申请工单提交成功，返回结果为result.getData:{}", JSON.toJSONString(submitCertificationAuditVO));
        return submitCertificationAuditVO == null ? false : (submitCertificationAuditVO.getSubmitResult() == null ?
                false : submitCertificationAuditVO.getSubmitResult());
    }
}
