package com.akucun.account.proxy.client.customer.feign;

import com.akucun.account.proxy.client.customer.entity.SubmitCertificationAuditReq;
import com.akucun.account.proxy.client.customer.entity.SubmitCertificationAuditVO;
import com.akucun.common.Result;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @Author: silei
 * @Date: 2021/4/4
 * @desc: 重写 客服Feign client
 */
@FeignClient(
        value = "akucun-custservice-platform",
        path = "/custservice/v1.0/certificationaudit"
)
public interface CertificationAuditFeign {

    @PostMapping({"/submit"})
    Result<SubmitCertificationAuditVO> submit(@RequestBody SubmitCertificationAuditReq var1);

    @GetMapping({"/sync"})
    Result<Void> sync();
}
