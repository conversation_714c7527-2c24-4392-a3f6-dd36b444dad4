package com.akucun.account.proxy.client.saas;

import com.akucun.account.proxy.client.saas.entity.TenantResp;
import com.akucun.account.proxy.client.saas.feign.TenantFeign;
import com.akucun.account.proxy.common.exception.AccountProxyException;
import com.alibaba.fastjson.JSON;
import com.mengxiang.base.common.log.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @Author: silei
 * @Date: 2021/5/13
 * @desc:
 */
@Component
public class TenantClient {

    @Resource
    private TenantFeign tenantFeign;

    public static final Integer SAAS_COMPANY = 2;

    public boolean isCompanyTenant(Long tenantId){
        com.mengxiang.base.common.model.result.Result<TenantResp> result = tenantFeign.queryByTenantId(tenantId);
        Logger.info("TenantClient queryByTenantId result:{}", JSON.toJSONString(result));
        if (result == null || !result.isSuccess() || result.getData() == null){
           throw new AccountProxyException("租户信息查询失败");
        }
        return SAAS_COMPANY.equals(result.getData().getTenantType());
    }
}
