package com.akucun.account.proxy.client.oa.entity;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Create on : 2025/1/14 19:49
 **/
@Data
public class WorkflowCreateRequest {

    private Message message;

    @Data
    public static class Message {

        // 请求方法
        private String method = "createRequest";

        // 流程id
        private String workflowid;

        // 流程标题
        private String requestname;

        // 请求级别
        private String requestlevel;

        // 创建人
        private String creator;

        // 是否流转下一节点
        private String isnextflow;

        // 主表字段
        private List<Field> main;

        // 明细表字段
        private List<DetailTable> detail;

    }

    @Data
    public static class DetailTable {

        // 明细表id
        private String detailtableid;

        // 明细表数据
        private List<List<Field>> data;

    }

    @Data
    public static class Field {

        // 字段名
        private String fieldname;

        // 字段值
        private String fieldvalue;

        // 字段类型
        private String fieldtype; // 可选字段，例如 "http:1.jpeg"

    }

}
