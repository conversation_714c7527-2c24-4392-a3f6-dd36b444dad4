package com.akucun.account.proxy.client.invoice;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.client.invoice.model.InvoiceAwaitingApplyInfoRes;
import com.akucun.account.proxy.client.invoice.model.MerConfirmInvoiceReq;
import com.akucun.account.proxy.client.invoice.model.OutInvoiceAwaitingApplyReq;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : 发票服务接口
 * @Create on : 2025/2/12 22:46
 **/
@FeignClient(value = "invoice-center")
public interface InvoiceCenterFacade {

    /**
     * 销项发票申请
     * @param request
     * @return
     */
    @PostMapping("/api/out/Invoice/awaiting/apply")
    Result<InvoiceAwaitingApplyInfoRes> outInvoiceAwaitingApply(@RequestBody OutInvoiceAwaitingApplyReq request);

    /**
     * 商家确认销项发票
     * @param request
     * @return
     */
    @PostMapping("/api/newinvoice/merchant/confirm")
    Result<String> merConfirmInvoice(@RequestBody MerConfirmInvoiceReq request);

}

