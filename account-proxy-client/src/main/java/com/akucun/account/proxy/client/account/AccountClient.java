package com.akucun.account.proxy.client.account;

import com.akucun.account.center.client.gray.GrayJoinCommonService;
import com.akucun.account.center.client.model.TradeInfo;
import com.akucun.account.center.client.model.account.AccountBookDetailDO;
import com.akucun.account.center.client.model.query.AccountDetailQuery;
import com.akucun.account.center.common.entity.Query;
import com.akucun.account.center.common.entity.ResultList;
import com.akucun.account.center.feign.AccountCenterService;
import com.akucun.common.Result;
import com.akucun.fps.common.util.MD5Util;
import com.akucun.member.api.PayService;
import com.akucun.member.api.vo.MoneyAddVO;
import com.akucun.member.api.vo.MoneyConsumedVO;
import com.akucun.member.api.vo.UserAccountRecordVO;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.mengxiang.base.common.log.Logger;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2020/12/23 20:54
 */
@Service
public class AccountClient {

    @Reference(check = false)
    private GrayJoinCommonService grayJoinCommonService;
    @Reference(check = false)
    private PayService payService;
    @Autowired
    private AccountCenterService accountCenterService;

    /**
     * 支付获取账户分支
     *
     * @param customerCode
     * @return
     */
    public String getBalanceBranch(String customerCode) {
        String branch = null;
        try {
            Logger.info("余额支付获取账户分支，请求参数：{}", customerCode);
            Result<String> result = grayJoinCommonService.getAdminSystem(customerCode);
            Logger.info("余额支付获取账户分支，返回参数：{}", JSON.toJSONString(result));

            if (Objects.nonNull(result) && result.isSuccess() && StringUtils.isNotEmpty(result.getData())) {
                branch = result.getData();
            }
        } catch (Exception e) {
            Logger.error("余额支付获取账户分异常，异常信息：{}", e);
        }
        return branch;
    }

    /**
     * 余额支付（奖励金）
     * @param req
     * @return
     */
    public Boolean balanceTrade(MoneyConsumedVO req) {
        Logger.info("余额支付请求参数request：{}", JSON.toJSONString(req));
        Integer reqlen = JSON.toJSONString(req).length();
        //加密
        String strM = MD5Util.md5("aikucunpay:" + reqlen);
        boolean isSuccess = payService.ConsumeMoney(req, strM);
        Logger.info("余额支付返回结果isSuccess：{}", isSuccess);
        return isSuccess;
    }

    /**
     * 余额退款（奖励金）
     * @param req
     * @return
     */
    public Boolean balanceRefund(MoneyAddVO req) {
        Logger.info("余额退款请求参数request：{}", JSON.toJSONString(req));
        Integer reqlen = JSON.toJSONString(req).length();
        //加密
        String strM = MD5Util.md5("aikucunpay:" + reqlen);
        boolean isSuccess = payService.AddMoney(req, strM);
        Logger.info("额退款返回结果isSuccess：{}", isSuccess);
        return isSuccess;
    }

    /**
     * 新余额支付（奖励金）
     * @param tradeInfo
     * @return
     */
    public Result<Void> newBalanceTrade(TradeInfo tradeInfo) {
        Logger.info("新余额支付开始，request：{}", JSON.toJSONString(tradeInfo));
        Result<Void> tradeResult = accountCenterService.dealTrade(tradeInfo);
        Logger.info("新余额支付结束，response：{}", JSON.toJSONString(tradeResult));
        return tradeResult;
    }

    /**
     * 余额查询（奖励金）
     * @param tradeNo
     * @param payAmount
     * @return
     */
    public boolean findAccountList(String tradeNo, BigDecimal payAmount) {
        boolean isSuccess = false;
        Logger.info("余额支付查询请求参数，tradeNo：{}", tradeNo);
        List<UserAccountRecordVO> accountList = payService.findAccountRecordDetail(tradeNo);
        Logger.info("余额支付查询结果参数，payId：{},accountList：{}", tradeNo, JSON.toJSONString(accountList));
        if(null != accountList && accountList.size() > 0) {
            if(payAmount.multiply(new BigDecimal("100")).intValue() == accountList.get(0).getJine()) {
                return true;
            }
        }
        return isSuccess;
    }

    /**
     * 新账户余额查询（奖励金）
     * @param customerCode
     * @param tradeNo
     * @param payAmount
     * @return
     */
    public boolean queryAccountDetailList(String customerCode, String tradeNo, BigDecimal payAmount, String accountTypeKey) {
        Query<AccountDetailQuery> query = new Query<>();
        AccountDetailQuery accountDetail = new AccountDetailQuery();
        accountDetail.setAccountTypeKey(accountTypeKey);
        accountDetail.setCustomerCode(customerCode);
        accountDetail.setTradeNo(tradeNo);
        query.setData(accountDetail);
        Logger.info("新余额支付查询参数,accountDetail：{}", JSON.toJSONString(accountDetail));
        ResultList<AccountBookDetailDO> resultList = accountCenterService.queryAccountDetail(query);
        Logger.info("新余额支付查询结果参数, payId：{}, resultList：{}", tradeNo, JSON.toJSONString(resultList));
        boolean isSuccess = false;
        if(Objects.nonNull(resultList) && !CollectionUtils.isEmpty(resultList.getDatalist())) {
            for (AccountBookDetailDO detailDO : resultList.getDatalist()) {
                BigDecimal amount = detailDO.getAmount().abs();
                if(tradeNo.equals(detailDO.getTradeNo()) && payAmount.compareTo(amount) == 0) {
                    Logger.warn("新余额支付查询结成功payId：{}", tradeNo);
                    isSuccess = true;
                } else {
                    Logger.warn("新余额支付查询结果异常result：{}",JSON.toJSONString(detailDO));
                }
            }
        }
        return isSuccess;
    }

    /**
     * 新账户余额查询（奖励金）
     * @param customerCode
     * @param tradeNo
     * @param accountTypeKey
     * @return
     */
    public AccountBookDetailDO queryAccountDetail(String customerCode, String tradeNo, String accountTypeKey) {
        Query<AccountDetailQuery> query = new Query<>();
        AccountDetailQuery accountDetail = new AccountDetailQuery();
        accountDetail.setAccountTypeKey(accountTypeKey);
        accountDetail.setCustomerCode(customerCode);
        accountDetail.setTradeNo(tradeNo);
        query.setData(accountDetail);
        Logger.info("新余额支付查询1参数,accountDetail：{}", JSON.toJSONString(accountDetail));
        ResultList<AccountBookDetailDO> resultList = accountCenterService.queryAccountDetail(query);
        Logger.info("新余额支付查询1结果参数, payId：{}, resultList：{}", tradeNo, JSON.toJSONString(resultList));
        if(Objects.isNull(resultList) || CollectionUtils.isEmpty(resultList.getDatalist())) {
            return null;
        }
        for (AccountBookDetailDO detailDO : resultList.getDatalist()) {
            if(tradeNo.equals(detailDO.getTradeNo())) {
                return detailDO;
            }
        }
        return null;
    }

}
