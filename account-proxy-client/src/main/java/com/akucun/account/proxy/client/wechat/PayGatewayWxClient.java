package com.akucun.account.proxy.client.wechat;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.aikucun.common2.base.Result;
import com.aikucun.common2.base.exception.IErrorCode;
import com.akucun.account.proxy.common.enums.ResponseEnum;
import com.akucun.pay.gateway.wx.facade.stub.others.dto.req.transfer.BillReceiptReq;
import com.akucun.pay.gateway.wx.facade.stub.others.dto.res.transfer.BillReceiptResp;
import com.akucun.pay.gateway.wx.facade.stub.wechat.WechatTransferFacade;
import com.mengxiang.base.common.log.Logger;

/*
 * @Author: Lee
 * @Date: 2025-03-18 15:16:06
 * @Description: 微信支付网关客户端
 * @License: Copyright (c) 2025, Lee
 */
@Component
public class PayGatewayWxClient {

    @Autowired
    private WechatTransferFacade wechatTransferFacade;

    /**
     * 下载微信转账回单
     * @param batchNo 批次号
     * @param withdrawNo 提现单号
     * @param merchantCode 商户号
     * @param isApplySuccess 是否调用pay-gateway-wx申请转账回单成功
     * @return
     */
    public Result<String> downloadTransferReceipt(String batchNo, String withdrawNo, String merchantCode, Boolean isApplySuccess) {
        try {
            BillReceiptReq req = new BillReceiptReq();
            req.setOutBatchMo(batchNo);
            req.setOutDetailNo(withdrawNo);
            req.setMerCode(merchantCode);
            req.setIsDownloadOBS(true);
            // 如果未申请成功, 则申请转账回单, 否则直接查询
            if (BooleanUtils.isNotTrue(isApplySuccess)) {
                Result<BillReceiptResp> applyResult = wechatTransferFacade.electronicReceipts(req);
                if (applyResult.getSuccess() && applyResult.getData() != null) {
                    return Result.error(ResponseEnum.BATCH_TRANSFER_RECEIPT_APPLY_SUBMITTED.getCode(), ResponseEnum.BATCH_TRANSFER_RECEIPT_APPLY_SUBMITTED.getMessage());
                } else if(!applyResult.getSuccess() && StringUtils.equals(applyResult.getMessage(), "转账电子回单申请单数据已存在")) {
                    // 调用pay-gateway-wx申请转账回单成功, 可直接进行下载
                } else {
                    return Result.error(IErrorCode.SYSTEM_ERROR, "调用pay-gateway-wx申请转账回单失败:" + applyResult.getMessage());
                }
            }
    
            Result<BillReceiptResp> queryResult = wechatTransferFacade.electronicReceiptsQueryAndDownload(req);
            if (!queryResult.getSuccess() || queryResult.getData() == null) {
                return Result.error(IErrorCode.SYSTEM_ERROR, "调用pay-gateway-wx查询转账回单失败:" + queryResult.getMessage());
            }

            BillReceiptResp data = queryResult.getData();
            if (StringUtils.isBlank(data.getDownloadUrl())) {
                return Result.error(IErrorCode.SYSTEM_ERROR, "调用pay-gateway-wx查询转账回单失败, 无有效地址");
            }
            return Result.success(data.getDownloadUrl());
        } catch (Exception e) {
            Logger.error("PayGatewayWxClient.downloadTransferReceipt error", e);
            return Result.error(IErrorCode.SYSTEM_ERROR, "下载微信转账回单失败:" + e.getMessage());
        }
    }

}
