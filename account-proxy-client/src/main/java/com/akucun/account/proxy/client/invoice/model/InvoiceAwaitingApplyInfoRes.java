package com.akucun.account.proxy.client.invoice.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Create on : 2025/2/13 22:48
 **/
@Data
public class InvoiceAwaitingApplyInfoRes implements Serializable {

    @ApiModelProperty(value = "ID")
    private Long id;

    @ApiModelProperty(value = "待申请流水号")
    private String awaitingApplyNo;

    @ApiModelProperty(value = "关联业务流水号")
    private String relatedBusinessNo;

    @ApiModelProperty(value = "业务明细科目")
    private Integer businessType;

    @ApiModelProperty(value = "业务明细科目名")
    private String businessTypeName;

    @ApiModelProperty(value = "待申请开票主体类型")
    private Integer applySubjectType;

    @ApiModelProperty(value = "待申请开票主体编码")
    private String applySubjectCode;

    @ApiModelProperty(value = "待申请开票主体名称")
    private String applySubjectName;

    @ApiModelProperty(value = "金额")
    @NotBlank
    private BigDecimal amount;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "待申请状态：100-禁止开票;00-待生效;10-待申请;20-已申请;30-已驳回")
    private Integer status;

    @ApiModelProperty(value = "待申请状态描述：100-禁止开票;00-待生效;10-待申请;20-已申请;30-已驳回")
    private String statusDesc;

    @ApiModelProperty("业务时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date businessTime;

    @ApiModelProperty("创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty("更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @ApiModelProperty("卖家编码")
    private String sellerMerchantCode;

    @ApiModelProperty("买家编码")
    private String buyerMerchantCode;

}