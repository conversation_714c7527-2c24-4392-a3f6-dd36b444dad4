package com.akucun.account.proxy.client.invoice.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Create on : 2025/2/13 22:50
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MerConfirmInvoiceReq {
    @ApiModelProperty(value = "请求唯一id：幂等标识")
    @NotBlank(message = "请求唯一id(requestId)不能为空")
    @Length(min = 1, max = 64, message = "请求唯一id(requestId)长度1-64")
    private String requestId;

    //tab业务类型--- 新增
    @ApiModelProperty(value = "开票渠道:P-平台 M-商家 E-爱豆")
    private String invoiceChannel;

    //===开票方:新增字段(开票方税号)
    @ApiModelProperty(value = "开票方M码")
    @Length(min = 0, max = 64, message = "开票方M码长度1-64")
    private String sellerMerchantCode;

    @ApiModelProperty(value = "开票方税号")
    private String sellerTaxNo;

    //===收票方 --- 原字段定义不变
    @ApiModelProperty(value = "商家M码")
    @NotBlank(message = "商家M码(merchantCode)不能为空")
    @Length(min = 1, max = 32, message = "商家M码(merchantCode)长度1-32")
    private String buyerMerchantCode;

    @ApiModelProperty(value = "购买方发票抬头")
    @NotBlank(message = "购买方发票抬头(buyerName)不能为空")
    @Length(min = 1,max = 100,message = "购买方发票抬头(buyerName)长度范围1-100")
    private String buyerName;

    @ApiModelProperty(value = "购买方纳税人识别号")
    // @Length(min = 15,max = 20,message = "购买方纳税人识别号(buyerTaxNo)长度范围15-20")
    private String buyerTaxNo;

    @ApiModelProperty(value = "开具发票种类：10-普通电子票;20-普票;30-增票;40-纸质发票")
    @NotBlank(message = "开具发票种类(invoiceIssueKindCode)不能为空")
    @Length(min = 2, max = 2, message = "发票请求流水号(invoiceIssueKindCode)固定长度2")
    private String invoiceIssueKindCode;

    @ApiModelProperty(value = "开具给自然人/企业:1/2")
    @NotBlank(message = "是否开具给自然人(buyerPersonFlag)不能为空")
    @Length(min = 1,max = 1,message = "是否开具给自然人(buyerPersonFlag)长度范围1-1")
    private String buyerPersonFlag = "2";

    @ApiModelProperty(value = "购买方开户行")
    @Length(min = 0,max = 100,message = "购买方开户行(buyerBankName)长度范围1-100")
    private String buyerBankName;

    @ApiModelProperty(value = "购买方银行账号")
    @Length(min = 0,max = 50,message = "购买方银行账号(buyerBankAccount)长度范围1-50")
    private String buyerBankAccount;

    @ApiModelProperty(value = "购买方电话")
    @Length(min = 0,max = 20,message = "购买方电话(buyerTel)长度范围1-20")
    private String buyerTel;

    @ApiModelProperty(value = "购买方地址")
    @Length(min = 0,max = 100,message = "购买方地址(buyerAddress)长度范围1-100")
    private String buyerAddress;

    @ApiModelProperty(value = "购买方邮箱")
    @Length(min = 0,max = 100,message = "购买方邮箱(buyerEmail)长度范围1-100")
    private String buyerEmail;

    @ApiModelProperty(value = "批量选中的申请开票记录的Id集合")
    private List<Long> subRecordIds;

    @ApiModelProperty(value = "选中记录的不含税总金额")
    private BigDecimal totalAmount;

    //--- 新增
    @ApiModelProperty(value = "选中记录条数")
    private Integer selectedNum;

    @ApiModelProperty(value = "是否尝试生效")
    private Boolean checkEffect = Boolean.FALSE;

}
