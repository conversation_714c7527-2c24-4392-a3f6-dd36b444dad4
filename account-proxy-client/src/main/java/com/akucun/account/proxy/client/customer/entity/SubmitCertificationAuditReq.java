package com.akucun.account.proxy.client.customer.entity;

import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Author: silei
 * @Date: 2021/4/4
 * @desc: 重写客服 税库银 审核请求
 */
public class SubmitCertificationAuditReq implements Serializable {

    private static final long serialVersionUID = 5135001102497985085L;

    @ApiModelProperty("审核编号")
    @NotBlank(
            message = "审核编号为空"
    )
    private String auditNo;
    @ApiModelProperty("用户身份(1:消费者,2:店主,3:店长,4:管理员)")
    @Range(
            min = 2L,
            max = 3L,
            message = "非法的userRole值"
    )
    private Integer userRole;
    @ApiModelProperty("店长id(distributorId)/店主爱豆编码")
    @NotBlank(
            message = "userId为空"
    )
    private String userId;
    @ApiModelProperty("审核类型(0:实名认证,1:临时认证,2:个体户认证,3:企业认证,100:私人银行卡绑定)")
    @NotNull(
            message = "auditType为空"
    )
    private Integer auditType;
    @ApiModelProperty("手机号(加密串)")
    private String mobileEncrypt;
    @ApiModelProperty("卡片类型，0-身份证 1-港澳居民通行证 2-中国护照 3-企业的社会统一信用代码  4-外国护照 5-台湾居民通行证 100-税库银三分协议")
    private Integer cardType;
    @ApiModelProperty("身份证姓名")
    @NotBlank(
            message = "idCardName为空"
    )
    private String idCardName;
    @ApiModelProperty("身份证号码/银行卡号")
    @NotBlank(
            message = "idCardNumber为空"
    )
    private String idCardNumber;
    @ApiModelProperty("社会信用代码")
    private String socialCreditCode;
    @ApiModelProperty("商户名称")
    private String merchantName;
    @ApiModelProperty("法人名称")
    private String legalPersonName;
    @ApiModelProperty("身份证正面照图片/税库银三方协议图片")
    @NotBlank(
            message = "idCardFrontPic为空"
    )
    private String idCardFrontPic;
    @ApiModelProperty("身份证反面照图片")
    @NotNull(
            message = "idCardBackPic为空"
    )
    private String idCardBackPic;
    @ApiModelProperty("营业执照图片")
    private String businessLicensePic;

    public String getAuditNo() {
        return auditNo;
    }

    public void setAuditNo(String auditNo) {
        this.auditNo = auditNo;
    }

    public Integer getUserRole() {
        return userRole;
    }

    public void setUserRole(Integer userRole) {
        this.userRole = userRole;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public Integer getAuditType() {
        return auditType;
    }

    public void setAuditType(Integer auditType) {
        this.auditType = auditType;
    }

    public String getMobileEncrypt() {
        return mobileEncrypt;
    }

    public void setMobileEncrypt(String mobileEncrypt) {
        this.mobileEncrypt = mobileEncrypt;
    }

    public Integer getCardType() {
        return cardType;
    }

    public void setCardType(Integer cardType) {
        this.cardType = cardType;
    }

    public String getIdCardName() {
        return idCardName;
    }

    public void setIdCardName(String idCardName) {
        this.idCardName = idCardName;
    }

    public String getIdCardNumber() {
        return idCardNumber;
    }

    public void setIdCardNumber(String idCardNumber) {
        this.idCardNumber = idCardNumber;
    }

    public String getSocialCreditCode() {
        return socialCreditCode;
    }

    public void setSocialCreditCode(String socialCreditCode) {
        this.socialCreditCode = socialCreditCode;
    }

    public String getMerchantName() {
        return merchantName;
    }

    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }

    public String getLegalPersonName() {
        return legalPersonName;
    }

    public void setLegalPersonName(String legalPersonName) {
        this.legalPersonName = legalPersonName;
    }

    public String getIdCardFrontPic() {
        return idCardFrontPic;
    }

    public void setIdCardFrontPic(String idCardFrontPic) {
        this.idCardFrontPic = idCardFrontPic;
    }

    public String getIdCardBackPic() {
        return idCardBackPic;
    }

    public void setIdCardBackPic(String idCardBackPic) {
        this.idCardBackPic = idCardBackPic;
    }

    public String getBusinessLicensePic() {
        return businessLicensePic;
    }

    public void setBusinessLicensePic(String businessLicensePic) {
        this.businessLicensePic = businessLicensePic;
    }

    @Override
    public String toString() {
        return "SubmitCertificationAuditReq{" +
                "auditNo='" + auditNo + '\'' +
                ", userRole=" + userRole +
                ", userId='" + userId + '\'' +
                ", auditType=" + auditType +
                ", mobileEncrypt='" + mobileEncrypt + '\'' +
                ", cardType=" + cardType +
                ", idCardName='" + idCardName + '\'' +
                ", idCardNumber='" + idCardNumber + '\'' +
                ", socialCreditCode='" + socialCreditCode + '\'' +
                ", merchantName='" + merchantName + '\'' +
                ", legalPersonName='" + legalPersonName + '\'' +
                ", idCardFrontPic='" + idCardFrontPic + '\'' +
                ", idCardBackPic='" + idCardBackPic + '\'' +
                ", businessLicensePic='" + businessLicensePic + '\'' +
                '}';
    }
}
