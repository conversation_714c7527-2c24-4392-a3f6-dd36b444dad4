package com.akucun.account.proxy.client.invoice;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.client.invoice.model.InvoiceAwaitingApplyInfoRes;
import com.akucun.account.proxy.client.invoice.model.MerConfirmInvoiceReq;
import com.akucun.account.proxy.client.invoice.model.OutInvoiceAwaitingApplyReq;
import com.akucun.account.proxy.facade.stub.others.dto.req.BonusPayReq;
import com.alibaba.fastjson.JSON;
import com.mengxiang.base.common.log.Logger;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : 发票服务
 * @Create on : 2025/2/12 22:55
 **/
@Component
public class InvoiceService {
    @Resource
    private InvoiceCenterFacade invoiceCenterFacade;
    @Value("${promo.trade.nm.defMcode:NM5555555}")
    private String defNmMerchantCode;
    @Value("${promo.trade.nm.defMname:通用的营销店长开票账户}")
    private String defNmMerchantName;
    @Value("${promo.trade.nm.taxno:************}")
    private String defNmtaxNo;
    @Value("${promo.trade.platform.defMname:M5555555}")
    private String defPMerchantCode;
    @Value("${promo.trade.platform.defMname:上海饷纵横信息技术有限公司}")
    private String defPMerchantName;
    @Value("${promo.trade.platform.taxno:91310000MA7C39TG7B}")
    private String defPtaxNo;

    /**
     * 平台申请开票
     * @return
     */
    public Result<Pair<String,String>> invoiceApply4Platform(BonusPayReq bonusPayReq){
        //平台申请店主开票
        OutInvoiceAwaitingApplyReq awaitingApplyReq = buildOutInvoiceAwaitingApplyReq(bonusPayReq);
        Logger.info("月勤奖-平台申请店主开票req:{}", JSON.toJSONString(awaitingApplyReq));
        Result<InvoiceAwaitingApplyInfoRes> invoiceAwaitingApplyInfoResResult = invoiceCenterFacade.outInvoiceAwaitingApply(awaitingApplyReq);
        Logger.info("月勤奖-平台申请店主开票resp:{}", JSON.toJSONString(invoiceAwaitingApplyInfoResResult));
        if(!invoiceAwaitingApplyInfoResResult.getSuccess()){
            return Result.error(invoiceAwaitingApplyInfoResResult.getCode(),invoiceAwaitingApplyInfoResResult.getMessage());
        }
        Long applyId = invoiceAwaitingApplyInfoResResult.getData().getId();
        String awaitingApplyNo = invoiceAwaitingApplyInfoResResult.getData().getAwaitingApplyNo();

        //商家确认开票
        MerConfirmInvoiceReq request = buildMerConfirmInvoiceReq(applyId);
        Logger.info("月勤奖-平台申请店主开票-店主确认开票req:{}", JSON.toJSONString(request));
        Result<String> comfirmResult = invoiceCenterFacade.merConfirmInvoice(request);
        Logger.info("月勤奖-平台申请店主开票-店主确认开票resp:{}", JSON.toJSONString(comfirmResult));
        if(!comfirmResult.getSuccess()){
            return Result.error(comfirmResult.getCode(),comfirmResult.getMessage());
        }
        String applyNo = comfirmResult.getData();

        return Result.success(Pair.of(awaitingApplyNo,applyNo));
    }


    private OutInvoiceAwaitingApplyReq buildOutInvoiceAwaitingApplyReq(BonusPayReq bonusPayReq){
        OutInvoiceAwaitingApplyReq awaitingApplyReq = new OutInvoiceAwaitingApplyReq();
        awaitingApplyReq.setRelatedBusinessNo(bonusPayReq.getTradeNo());
        awaitingApplyReq.setBusinessType(15);
        awaitingApplyReq.setApplySubjectType(20);
        awaitingApplyReq.setApplySubjectCode(defNmMerchantCode);
        awaitingApplyReq.setApplySubjectName(defNmMerchantName);
        awaitingApplyReq.setBusinessTime(new Date());
        awaitingApplyReq.setAmount(bonusPayReq.getAmount());
        awaitingApplyReq.setRemark(bonusPayReq.getRemark());
        Map<String,Object> extensionField = new HashMap<>();
        extensionField.put("bizType",bonusPayReq.getBizType());
        extensionField.put("transBillDate",bonusPayReq.getTransBillDate());
        extensionField.put("userType",bonusPayReq.getUserType());
        extensionField.put("userGrade",bonusPayReq.getUserGrade());
        awaitingApplyReq.setExtensionField(extensionField);
        //awaitingApplyReq.setNotifyUrl("http://1122222:99/12223");

        return awaitingApplyReq;
    }

    private MerConfirmInvoiceReq buildMerConfirmInvoiceReq(long applyId){
        MerConfirmInvoiceReq request = new MerConfirmInvoiceReq();
        request.setRequestId(UUID.randomUUID().toString().replace("-", ""));
        request.setInvoiceChannel("E");
        request.setSellerMerchantCode(defNmMerchantCode);
        request.setSellerTaxNo(defNmtaxNo);
        request.setBuyerMerchantCode(defPMerchantCode);
        request.setBuyerName(defPMerchantName);
        request.setBuyerTaxNo(defPtaxNo);
        request.setInvoiceIssueKindCode("10");
        List<Long> subids = new ArrayList<>();
        subids.add(applyId);
        request.setSubRecordIds(subids);
        //检查开票是否可以生效
        request.setCheckEffect(Boolean.TRUE);

        return request;
    }
}
