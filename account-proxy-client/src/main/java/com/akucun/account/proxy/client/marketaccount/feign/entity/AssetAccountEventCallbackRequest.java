package com.akucun.account.proxy.client.marketaccount.feign.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description 结算回调数据
 * @date 2021/8/25 下午2:12
 */
@Data
public class AssetAccountEventCallbackRequest {

    @NotBlank(message = "活动Code不可为空")
    @ApiModelProperty(value = "活动Code")
    private String actCode;

    @ApiModelProperty(value = "用户ID")
    private String principalId;

    @ApiModelProperty(value = "用户类型 店主 SELLER 店长 DISTRIBUTOR")
    private String principalType;

    @NotBlank(message = "结算用 业务唯一ID不可为空")
    @ApiModelProperty(value = "业务唯一ID")
    private String bizNo;

    @NotBlank(message = "来源业务唯一ID不可为空")
    @ApiModelProperty(value = "来源业务唯一ID")
    private String sourceNo;

    @NotBlank(message = "业务唯一ID不可为空")
    @ApiModelProperty(value = "业务唯一ID")
    private String sourceScene;

    @ApiModelProperty(value = "金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "奖励类型-奖励金BONUS/现金CASH")
    private String awardWay;

    @ApiModelProperty(value = "系统来源")
    private String requestSource;
}