package com.akucun.account.proxy.client.transfer.bo;

import com.akucun.pay.gateway.wxv2.facade.stub.dto.res.WxTransferResp;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/02/01 20:23
 */
public class WxTransferRespBO extends WxTransferResp implements Serializable {

    private Boolean isSuccess;

    private String gatewayMsg;

    public Boolean getSuccess() {
        return isSuccess;
    }

    public void setSuccess(Boolean success) {
        isSuccess = success;
    }

    public String getGatewayMsg() {
        return gatewayMsg;
    }

    public void setGatewayMsg(String gatewayMsg) {
        this.gatewayMsg = gatewayMsg;
    }
}
