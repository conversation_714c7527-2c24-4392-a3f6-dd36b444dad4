package com.akucun.account.proxy.client.marketaccount.feign;

import com.akucun.account.proxy.client.marketaccount.feign.entity.AssetAccountEventCallbackRequest;
import com.mengxiang.base.common.model.result.Result;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


@FeignClient(name = "promo-activity-center")
public interface AwardEventCallbackFeignClient {

    @PostMapping(path = "/asset/account/awardEventCallback")
    Result awardEventCallback(@RequestBody AssetAccountEventCallbackRequest request);

}
