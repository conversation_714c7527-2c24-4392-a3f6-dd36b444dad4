package com.akucun.account.proxy.client.oa.entity;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Create on : 2025/1/14 20:03
 **/
@Getter
public enum WorkflowStatusEnum {

    CREATE(0,"创建"),
    APPROVE(1,"批准"),
    SUBMIT(2,"提交"),
    ARCHIVE(3,"归档"),
    WAIT(5,"等待"),
    AUTO(6,"自动处理");

    private Integer code;

    private String desc;

    WorkflowStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
