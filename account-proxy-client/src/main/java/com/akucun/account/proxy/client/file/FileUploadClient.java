package com.akucun.account.proxy.client.file;

import com.aikucun.common2.base.Result;
import com.aikucun.common2.base.exception.IErrorCode;
import com.aikucun.common2.log.Logger;
import com.mengxiang.fileupload.FileUploadFeignClient;
import com.mengxiang.fileupload.dto.ByteArrayMultipartFile;
import com.mengxiang.fileupload.dto.FileInfo;
import com.mengxiang.fileupload.signature.Signature;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.Files;

@Component
public class FileUploadClient {

    @Value("${file.upload.private.app.id:common-private}")
    private String privateAppId;

    @Value("${file.upload.private.app.secret:a33df944sdkdlxkcassb0666c37f85}")
    private String privateAppSecret;

    // 临时文件过期时间, 默认 1 小时, 单位: 秒
    @Value("${file.upload.tmp.default.expires:3600}")
    private Long tmpDefaultExpires;

    //平安提现回执单父级目录
    public static final String WITHDRAW_RECEIPT_PARENT_DIR = "pingan-withdraw-receipt";

    //微信提现回单父级目录
    public static final String WECHAT_WITHDRAW_RECEIPT_PARENT_DIR = "wechat-withdraw-receipt";

    @Resource
    private FileUploadFeignClient fileUploadFeignClient;

    public Result<String> uploadFile(File sourceFile, String originalFilename, String parentDirectory) {
        Result<String> result = null;
        try {
            ByteArrayMultipartFile file = new ByteArrayMultipartFile(originalFilename, Files.readAllBytes(sourceFile.toPath()));
            result = fileUploadFeignClient.uploadFile(file, privateAppId, parentDirectory);
        } catch (Exception e) {
            Logger.error("obs file upload error", e);
            result = Result.error(IErrorCode.SYSTEM_ERROR, e.getMessage());
        }
        return result;
    }

    public Result<String> uploadFile(byte[] sourceFileBytes, String originalFilename, String parentDirectory) {
        Result<String> result = null;
        try {
            ByteArrayMultipartFile file = new ByteArrayMultipartFile(originalFilename, sourceFileBytes);
            result = fileUploadFeignClient.uploadFile(file, privateAppId, parentDirectory);
        } catch (Exception e) {
            Logger.error("obs file upload error", e);
            result = Result.error(IErrorCode.SYSTEM_ERROR, e.getMessage());
        }
        return result;
    }

    /**
     * 文件上传
     * @param sourceFile
     * @param originalFilename
     * @param parentDirectory
     * @param newFileName        自定义的文件名，为空时使用随机文件名
     * @return
     * @see Signature
     */
    public Result<FileInfo> upload(File sourceFile, String originalFilename, String newFileName, String parentDirectory) {
        try {
            ByteArrayMultipartFile file = new ByteArrayMultipartFile(originalFilename, Files.readAllBytes(sourceFile.toPath()));
            Result<FileInfo> result = fileUploadFeignClient.upload(file, privateAppId, parentDirectory, newFileName, Signature.sign(file, privateAppId, privateAppSecret));
            return result;
        } catch (Exception e) {
            Logger.error("obs file upload error", e);
            return Result.error(IErrorCode.SYSTEM_ERROR, e.getMessage());
        }
    }

    /**
     * 文件上传
     * @param sourceFileBytes
     * @param originalFilename
     * @param parentDirectory
     * @param newFileName        自定义的文件名，为空时使用随机文件名
     * @return
     * @see Signature
     */
    public Result<FileInfo> upload(byte[] sourceFileBytes, String originalFilename, String newFileName, String parentDirectory) {
        try {
            ByteArrayMultipartFile file = new ByteArrayMultipartFile(originalFilename, sourceFileBytes);
            return fileUploadFeignClient.upload(file, privateAppId, parentDirectory, newFileName, Signature.sign(file, privateAppId, privateAppSecret));
        } catch (Exception e) {
            Logger.error("obs file upload error", e);
            return Result.error(IErrorCode.SYSTEM_ERROR, e.getMessage());
        }
    }

    /**
     * @param parentDirectory
     * @param expires         过期时间（秒）
     * @return
     */
   public Result<FileInfo> uploadForTmpSign(byte[] sourceFileBytes, String originalFilename, String parentDirectory, Long expires) {
        try {
            ByteArrayMultipartFile file = new ByteArrayMultipartFile(originalFilename, sourceFileBytes);
            return fileUploadFeignClient.uploadForTmpSign(file, privateAppId, parentDirectory, expires == null ? tmpDefaultExpires : expires);
        } catch (Exception e) {
            Logger.error("obs file upload for tmp sign error", e);
            return Result.error(IErrorCode.SYSTEM_ERROR, e.getMessage());
        }
    }

    /**
     * @param fileName
     * @param parentDirectory
     * @param expires         过期时间（秒）
     * @return
     */
    public Result<FileInfo> getTmpSign(String fileName, Long expires, String parentDirectory) {
        try {
            return fileUploadFeignClient.getTmpSign(fileName, privateAppId, parentDirectory, expires == null ? tmpDefaultExpires : expires);
        } catch (Exception e) {
            Logger.error("obs file getTmpSign error", e);
            return Result.error(IErrorCode.SYSTEM_ERROR, e.getMessage());
        }
    }

    /**
     * 将http链接转为byte[]
     * @param httpUrl
     * @return
     */
    public byte[] getHttpFileBytes(String httpUrl) {
        HttpURLConnection connection = null;
        InputStream inputStream = null;
        try {
            URL url = new URL(httpUrl);
            connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(5000); 
            connection.setReadTimeout(5000);
            connection.setDoInput(true);
            connection.setDoOutput(false);
            connection.connect();
            inputStream = connection.getInputStream();

            // 使用 ByteArrayOutputStream 读取所有字节
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                byteArrayOutputStream.write(buffer, 0, bytesRead);
            }
            return byteArrayOutputStream.toByteArray();
        } catch (Exception e) {
            Logger.error("obs file getHttpFileBytes error", e);
            return null;
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    Logger.error("obs file getHttpFileBytes close inputStream error", e);
                }
            }
            if (connection != null) {
                connection.disconnect();
            }
        }
    }
}
