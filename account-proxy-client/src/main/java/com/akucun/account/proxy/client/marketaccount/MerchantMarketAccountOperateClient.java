package com.akucun.account.proxy.client.marketaccount;

import com.akucun.account.proxy.common.enums.ResponseEnum;
import com.akucun.account.proxy.common.exception.AccountProxyException;
import com.akucun.bond.client.model.request.MarketAmontChangeReq;
import com.akucun.bond.client.model.response.AccountBookRes;
import com.akucun.bond.common.entity.Result;
import com.akucun.bond.feign.MarketAccountUniService;
import com.alibaba.fastjson.JSON;
import com.mengxiang.base.common.log.Logger;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Create on : 2024/5/7 10:55
 **/
@Component
public class MerchantMarketAccountOperateClient {

    @Value("${fps.bond.market.account.trade.sign.key:MERKETACCOUNTAKC}")
    public String signKey;

    @Autowired
    private MarketAccountUniService marketAccountUniService;

    public  String getSign(String timestamp){
        return DigestUtils.md5Hex(timestamp + signKey);
    }

    public  String getTimeStamp(){
        return String.valueOf(new Date().getTime());
    }


    public void marketUpdateAccount(BigDecimal amount, String traderType, String bizNo, String sourceBillNo,
                                    String customerCode, String activityName, String activityCode, String remark, Boolean verifyBillFlag) {
        if(amount.compareTo(BigDecimal.ZERO)==0){
            return;
        }
        MarketAmontChangeReq req = new MarketAmontChangeReq();
        req.setActualAmount(amount);
        req.setBizType("MARKET");
        req.setActivityCode(activityCode);
        req.setApplyNo(bizNo);
        req.setTradeNo(bizNo);
        req.setTradeType(traderType);
        req.setMerchantCode(customerCode);
        req.setActivityName(activityName);
        req.setRemark(remark);

        String timeStamp = getTimeStamp();
        req.setTimeStamp(timeStamp);
        req.setSign(getSign(timeStamp));
        //多透传一个参数，用来控制正逆向余额校验的
        req.setVerifyBillFlag(verifyBillFlag);
        req.setSourceBillNo(sourceBillNo);

        Logger.info("marketUpdateAccount 账户操作 request: {}", JSON.toJSONString(req));
        Result<Void> result = marketAccountUniService.marketUpdateAccount(req);
        Logger.info("marketUpdateAccount 账户操作 request: {}  result{}", JSON.toJSONString(req), JSON.toJSONString(result));
        //10001是重复失败,这里做幂等判断 当作已经成功处理
        if(!result.isSuccess() && result.getErrorCode()!= 100001){
            throw new AccountProxyException(ResponseEnum.SYSTEM_EXCEPTION, result.getErrorMessage());
        }
    }

    public AccountBookRes queryMarketAccountBook(String merchantCode) {
        MarketAmontChangeReq req = new MarketAmontChangeReq();
        req.setMerchantCode(merchantCode);
        Result<AccountBookRes> accountBookResResult = marketAccountUniService.queryMerketAccount(req);
        if (accountBookResResult == null || !accountBookResResult.isSuccess()) {
            throw new AccountProxyException(ResponseEnum.SYSTEM_EXCEPTION, "查询商家营销账户异常");
        }
        return accountBookResResult.getData();
    }
}
