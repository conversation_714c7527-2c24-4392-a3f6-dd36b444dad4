package com.akucun.account.proxy.client.transfer;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.client.transfer.bo.WxTransferRespBO;
import com.akucun.account.proxy.common.enums.ResponseEnum;
import com.akucun.pay.gateway.wxv2.facade.stub.WxTransferClient;
import com.akucun.pay.gateway.wxv2.facade.stub.dto.req.WxTransferQueryReq;
import com.akucun.pay.gateway.wxv2.facade.stub.dto.req.WxTransferReq;
import com.akucun.pay.gateway.wxv2.facade.stub.dto.res.WxTransferResp;
import com.alibaba.fastjson.JSON;
import com.mengxiang.base.common.log.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/01/28 11:52
 */
@Service
public class WechatTransferClient {
    @Autowired
    private WxTransferClient wxTransferClient;

    public WxTransferRespBO transfer(WxTransferReq request) {
        Logger.info("调用微信网关付款请求参数：{}", JSON.toJSONString(request));
        Result<WxTransferResp> transferResp = null;
        try {
            transferResp = wxTransferClient.transfer(request);
        } catch (Exception e) {
            Logger.error("调用微信网关付款异常：", e);
        }
        Logger.info("调用微信网关付款请求参数：{}, 返回参数：{}", request.getPartnerTradeNo(), JSON.toJSONString(transferResp));
        WxTransferRespBO result = new WxTransferRespBO();
        if(Objects.isNull(transferResp)) {
            result.setSuccess(false);
            result.setGatewayMsg(ResponseEnum.TRANSFER_ERROR.getCode()+"_"+ResponseEnum.TRANSFER_ERROR.getMessage());
        } else if(!transferResp.getSuccess()) {
            Logger.warn("调用微信网关付款失败，请求信息：{}，错误信息：{}", request.getPartnerTradeNo(), transferResp.getMessage());
            BeanUtils.copyProperties(transferResp.getData(), result);
            result.setSuccess(false);
            result.setGatewayMsg(transferResp.getMessage());
        } else {
            BeanUtils.copyProperties(transferResp.getData(), result);
            result.setSuccess(true);
        }
        return result;
    }

    public WxTransferRespBO queryTransfer(WxTransferQueryReq request) {
        Logger.info("调用微信网关付款查询请求参数：{}", JSON.toJSONString(request));
        Result<WxTransferResp> transferResp = null;
        try {
            transferResp = wxTransferClient.queryTransfer(request);
        } catch (Exception e) {
            Logger.error("调用微信网关付款查询异常：", e);
        }
        Logger.info("调用微信网关付款查询请求参数：{}, 返回参数：{}", request.getTradeNo(), JSON.toJSONString(transferResp));
        WxTransferRespBO result = new WxTransferRespBO();
        if(Objects.isNull(transferResp)) {
            result.setSuccess(false);
            result.setGatewayMsg(ResponseEnum.TRANSFER_ERROR.getCode()+"_"+ResponseEnum.TRANSFER_ERROR.getMessage());
        } else if(!transferResp.getSuccess()) {
            Logger.warn("调用微信网关付款查询失败，请求信息：{}，错误信息：{}", request.getTradeNo(), transferResp.getMessage());
            BeanUtils.copyProperties(transferResp.getData(), result);
            result.setSuccess(false);
        } else {
            BeanUtils.copyProperties(transferResp.getData(), result);
            result.setSuccess(true);
        }
        return result;
    }

}
