package com.akucun.account.proxy.client.account;

import com.akucun.fps.account.client.api.TransferAccountService;
import com.akucun.fps.account.client.model.TransferAccountDO;
import com.akucun.fps.account.client.model.query.TransferAccountQueryDO;
import com.akucun.fps.common.entity.Query;
import com.akucun.fps.common.entity.Result;
import com.akucun.fps.common.entity.ResultList;
import com.alibaba.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;

/**
 * @Author: silei
 * @Date: 2021/2/3
 * @desc:
 */
@Component
public class AccountTransferClient {

    @Reference(check = false)
    private TransferAccountService transferAccountService;


    /**
     * 转账申请
     * @param transferAccountDO
     * @return
     */
    public Result<String> transferApply(TransferAccountDO transferAccountDO) {
       return transferAccountService.transferApply(transferAccountDO);
    }

    /**
     * 转账申请分页查询
     * @param query
     * @return
     */
    public ResultList<TransferAccountDO> selectPage(Query<TransferAccountQueryDO> query) {
        return transferAccountService.selectPage(query);
    }
}
