package com.akucun.account.proxy.client.saas.feign;

import com.akucun.account.proxy.client.saas.entity.TenantResp;
import com.mengxiang.base.common.model.result.Result;
import io.swagger.annotations.Api;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @Author: silei
 * @Date: 2021/5/13
 * @desc:
 */
@Api(value = "租户服务", tags = {"租户服务"})
@FeignClient(name = "tenant-core")
public interface TenantFeign {
    /**
     * 根据租户id查询租户信息
     *
     * @param tenantId 租户id
     * @return resp
     */
    @GetMapping("/api/tenant/queryByTenantId")
    Result<TenantResp> queryByTenantId(@RequestParam(name = "tenantId") Long tenantId);

}