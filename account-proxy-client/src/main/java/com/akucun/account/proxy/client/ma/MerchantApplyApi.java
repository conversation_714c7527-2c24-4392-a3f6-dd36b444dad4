package com.akucun.account.proxy.client.ma;

import com.aikucun.common2.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Create on : 2025/1/20 19:27
 **/
@FeignClient(name = "merchant-applyment",url = "http://zuul.infra.aikucun.com/merchant-applyment")
public interface MerchantApplyApi {
    @ApiOperation(value = "字段加解密")
    @PostMapping(value = "/api/v2/backdoor/dataencrypt")
    Result<String> dataencrypt(@RequestParam("data") String data, @RequestParam(value = "type", required = false) String type);
}
