package com.akucun.account.proxy.client.customer.entity;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * @Author: silei
 * @Date: 2021/4/4
 * @desc: 重写客服 税库银 审核响应
 */
public class SubmitCertificationAuditVO implements Serializable {

    private static final long serialVersionUID = -6154712248270647114L;

    @ApiModelProperty("客服审核主键")
    private Long id;
    @ApiModelProperty("审核编号")
    private String auditNo;
    @ApiModelProperty("用户身份(1:消费者,2:店主,3:店长,4:管理员)")
    private Integer userRole;
    @ApiModelProperty("店长id(distributorId)/店主爱豆编码")
    private String userId;
    @ApiModelProperty("审核类型(0:实名认证,1:临时认证,2:个体户认证,3:企业认证)")
    private Integer auditType;
    @ApiModelProperty("手机号(加密串)")
    private String mobileEncrypt;
    @ApiModelProperty("身份证类型，0-身份证 1-港澳居民通行证 2-中国护照 3-企业的社会统一信用代码  4-外国护照 5-台湾居民通行证")
    private Integer cardType;
    @ApiModelProperty("身份证姓名")
    private String idCardName;
    @ApiModelProperty("身份证号码")
    private String idCardNumber;
    @ApiModelProperty("社会信用代码")
    private String socialCreditCode;
    @ApiModelProperty("商户名称")
    private String merchantName;
    @ApiModelProperty("法人名称")
    private String legalPersonName;
    @ApiModelProperty("身份证正面照图片")
    private String idCardFrontPic;
    @ApiModelProperty("身份证反面照图片")
    private String idCardBackPic;
    @ApiModelProperty("营业执照图片")
    private String businessLicensePic;
    @ApiModelProperty("提交结果")
    private Boolean submitResult;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAuditNo() {
        return auditNo;
    }

    public void setAuditNo(String auditNo) {
        this.auditNo = auditNo;
    }

    public Integer getUserRole() {
        return userRole;
    }

    public void setUserRole(Integer userRole) {
        this.userRole = userRole;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public Integer getAuditType() {
        return auditType;
    }

    public void setAuditType(Integer auditType) {
        this.auditType = auditType;
    }

    public String getMobileEncrypt() {
        return mobileEncrypt;
    }

    public void setMobileEncrypt(String mobileEncrypt) {
        this.mobileEncrypt = mobileEncrypt;
    }

    public Integer getCardType() {
        return cardType;
    }

    public void setCardType(Integer cardType) {
        this.cardType = cardType;
    }

    public String getIdCardName() {
        return idCardName;
    }

    public void setIdCardName(String idCardName) {
        this.idCardName = idCardName;
    }

    public String getIdCardNumber() {
        return idCardNumber;
    }

    public void setIdCardNumber(String idCardNumber) {
        this.idCardNumber = idCardNumber;
    }

    public String getSocialCreditCode() {
        return socialCreditCode;
    }

    public void setSocialCreditCode(String socialCreditCode) {
        this.socialCreditCode = socialCreditCode;
    }

    public String getMerchantName() {
        return merchantName;
    }

    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }

    public String getLegalPersonName() {
        return legalPersonName;
    }

    public void setLegalPersonName(String legalPersonName) {
        this.legalPersonName = legalPersonName;
    }

    public String getIdCardFrontPic() {
        return idCardFrontPic;
    }

    public void setIdCardFrontPic(String idCardFrontPic) {
        this.idCardFrontPic = idCardFrontPic;
    }

    public String getIdCardBackPic() {
        return idCardBackPic;
    }

    public void setIdCardBackPic(String idCardBackPic) {
        this.idCardBackPic = idCardBackPic;
    }

    public String getBusinessLicensePic() {
        return businessLicensePic;
    }

    public void setBusinessLicensePic(String businessLicensePic) {
        this.businessLicensePic = businessLicensePic;
    }

    public Boolean getSubmitResult() {
        return submitResult;
    }

    public void setSubmitResult(Boolean submitResult) {
        this.submitResult = submitResult;
    }

    @Override
    public String toString() {
        return "SubmitCertificationAuditVO{" +
                "id=" + id +
                ", auditNo='" + auditNo + '\'' +
                ", userRole=" + userRole +
                ", userId='" + userId + '\'' +
                ", auditType=" + auditType +
                ", mobileEncrypt='" + mobileEncrypt + '\'' +
                ", cardType=" + cardType +
                ", idCardName='" + idCardName + '\'' +
                ", idCardNumber='" + idCardNumber + '\'' +
                ", socialCreditCode='" + socialCreditCode + '\'' +
                ", merchantName='" + merchantName + '\'' +
                ", legalPersonName='" + legalPersonName + '\'' +
                ", idCardFrontPic='" + idCardFrontPic + '\'' +
                ", idCardBackPic='" + idCardBackPic + '\'' +
                ", businessLicensePic='" + businessLicensePic + '\'' +
                ", submitResult=" + submitResult +
                '}';
    }
}
