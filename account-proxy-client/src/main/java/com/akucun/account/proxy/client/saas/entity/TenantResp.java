package com.akucun.account.proxy.client.saas.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.mengxiang.base.common.model.request.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * 租户信息
 * <AUTHOR>
 */
@ApiModel(description = "租户信息")
public class TenantResp extends BaseRequest {
    /**
     * tenantId 租户id.
     */
    @ApiModelProperty("租户id")
    private Long tenantId;
    /**
     * createBy 创建人.
     */
    @ApiModelProperty("创建人")
    private String createBy;
    /**
     * updateBy 更新人.
     */
    @ApiModelProperty("更新人")
    private String updateBy;
    /**
     * tenantName 租户名字.
     */
    @ApiModelProperty("租户名字")
    private String tenantName;
    /**
     * tenantType 租户类型.
     */
    @ApiModelProperty("租户类型")
    private Integer tenantType;
    /**
     * cDivideFlag c是否隔离.
     */
    @ApiModelProperty("c是否隔离")
    private Integer cDivideFlag;
    /**
     * togetherFlag 是否共同使用饷店.
     */
    @ApiModelProperty("是否共同使用饷店")
    private Integer togetherFlag;
    /**
     * aloneSaleFlag 是否独立售后.
     */
    @ApiModelProperty("是否独立售后")
    private Integer aloneSaleFlag;
    /**
     * orderDivideFlag 订单是否隔离.
     */
    @ApiModelProperty("订单是否隔离")
    private Integer orderDivideFlag;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;
    /**
     * updateTime 更新时间.
     */
    @ApiModelProperty("更新时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date updateTime;

    /**
     * domain 域名.
     */
    @ApiModelProperty("域名")
    private String domain;
    /**
     * wxAppId 微信appid.
     */
    @ApiModelProperty("微信appid")
    private String wxAppId;


    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public String getTenantName() {
        return tenantName;
    }

    public void setTenantName(String tenantName) {
        this.tenantName = tenantName;
    }

    public Integer getTenantType() {
        return tenantType;
    }

    public void setTenantType(Integer tenantType) {
        this.tenantType = tenantType;
    }

    @SuppressWarnings("PMD")
    public Integer getCDivideFlag() {
        return cDivideFlag;
    }

    @SuppressWarnings("PMD")
    public void setCDivideFlag(Integer cDivideFlag) {
        this.cDivideFlag = cDivideFlag;
    }

    public Integer getTogetherFlag() {
        return togetherFlag;
    }

    public void setTogetherFlag(Integer togetherFlag) {
        this.togetherFlag = togetherFlag;
    }

    public Integer getAloneSaleFlag() {
        return aloneSaleFlag;
    }

    public void setAloneSaleFlag(Integer aloneSaleFlag) {
        this.aloneSaleFlag = aloneSaleFlag;
    }

    public Integer getOrderDivideFlag() {
        return orderDivideFlag;
    }

    public void setOrderDivideFlag(Integer orderDivideFlag) {
        this.orderDivideFlag = orderDivideFlag;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }

    public String getWxAppId() {
        return wxAppId;
    }

    public void setWxAppId(String wxAppId) {
        this.wxAppId = wxAppId;
    }
}
