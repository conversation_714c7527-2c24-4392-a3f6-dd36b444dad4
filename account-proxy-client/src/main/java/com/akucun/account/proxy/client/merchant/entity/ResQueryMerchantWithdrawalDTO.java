package com.akucun.account.proxy.client.merchant.entity;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: silei
 * @Date: 2021/10/19
 * @desc:
 */
public class ResQueryMerchantWithdrawalDTO implements Serializable {

    private static final long serialVersionUID = -6560499921026076026L;

    @JsonProperty("sub_mchid")
    private String subMchid;
    @JsonProperty("sp_mchid")
    private String spMchid;
    private String status;
    @JsonProperty("withdraw_id")
    private String withdrawId;
    @JsonProperty("out_request_no")
    private String outRequestNo;
    private BigDecimal amount;
    @JsonProperty("create_time")
    private Date createTime;
    @JsonProperty("update_time")
    private Date updateTime;
    private String reason;
    private String remark;
    @JsonProperty("bank_memo")
    private String bankMemo;

    public String getSubMchid() {
        return subMchid;
    }

    public void setSubMchid(String subMchid) {
        this.subMchid = subMchid;
    }

    public String getSpMchid() {
        return spMchid;
    }

    public void setSpMchid(String spMchid) {
        this.spMchid = spMchid;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getWithdrawId() {
        return withdrawId;
    }

    public void setWithdrawId(String withdrawId) {
        this.withdrawId = withdrawId;
    }

    public String getOutRequestNo() {
        return outRequestNo;
    }

    public void setOutRequestNo(String outRequestNo) {
        this.outRequestNo = outRequestNo;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getBankMemo() {
        return bankMemo;
    }

    public void setBankMemo(String bankMemo) {
        this.bankMemo = bankMemo;
    }

    @Override
    public String toString() {
        return "ResQueryMerchantWithdrawalDTO{" +
                "subMchid='" + subMchid + '\'' +
                ", spMchid='" + spMchid + '\'' +
                ", status='" + status + '\'' +
                ", withdrawId='" + withdrawId + '\'' +
                ", outRequestNo='" + outRequestNo + '\'' +
                ", amount=" + amount +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", reason='" + reason + '\'' +
                ", remark='" + remark + '\'' +
                ", bankMemo='" + bankMemo + '\'' +
                '}';
    }
}
