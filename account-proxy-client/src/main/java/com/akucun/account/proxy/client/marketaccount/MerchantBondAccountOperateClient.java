package com.akucun.account.proxy.client.marketaccount;

import com.akucun.account.proxy.common.enums.ResponseEnum;
import com.akucun.bond.client.model.request.BondActualAmountChangeNewReq;
import com.akucun.bond.common.entity.Result;
import com.akucun.bond.feign.dubbo2Feign.BondAccountServiceApi;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * @Description : TODO
 * @Create on : 2025/5/19 16:59
 * <AUTHOR>
 * @version v1.0.0
 **/
@Component
public class MerchantBondAccountOperateClient {

    @Autowired
    private BondAccountServiceApi bondAccountServiceApi;

    public com.aikucun.common2.base.Result<Void> changeBondAccountAmount(String customerCode, String tradeType, BigDecimal amount, String tradeNo,
                                                                   String sourceBillNo, String remark, String createBy) {
        BondActualAmountChangeNewReq actualAmountChangeNewReq = new BondActualAmountChangeNewReq();
        actualAmountChangeNewReq.setMerchantCode(customerCode);
        actualAmountChangeNewReq.setActualAmount(amount);
        actualAmountChangeNewReq.setRemark(remark);
        actualAmountChangeNewReq.setApplyNo(tradeNo);
        actualAmountChangeNewReq.setTradeType(tradeType);
        actualAmountChangeNewReq.setCreateBy(createBy);
        actualAmountChangeNewReq.setSourceBillNo(sourceBillNo);
        Result<Void> voidResult = bondAccountServiceApi.actualAmountChangeNew(actualAmountChangeNewReq);
        if (voidResult == null || !voidResult.isSuccess()) {
            return com.aikucun.common2.base.Result.error(ResponseEnum.SYSTEM_ERROR.getCode(), "保证金调账异常:" + voidResult.getErrorMessage());
        }
        return com.aikucun.common2.base.Result.success();
    }


}
