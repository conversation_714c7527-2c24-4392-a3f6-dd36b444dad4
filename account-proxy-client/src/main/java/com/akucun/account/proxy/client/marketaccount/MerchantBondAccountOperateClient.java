package com.akucun.account.proxy.client.marketaccount;

import com.akucun.account.proxy.common.enums.ResponseEnum;
import com.akucun.account.proxy.common.exception.AccountProxyException;
import com.akucun.bond.client.model.request.BondActualAmountChangeNewReq;
import com.akucun.bond.common.entity.Result;
import com.akucun.bond.feign.dubbo2Feign.BondAccountServiceApi;
import com.alibaba.fastjson.JSON;
import com.mengxiang.base.common.log.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * @Description : TODO
 * @Create on : 2025/5/19 16:59
 * <AUTHOR>
 * @version v1.0.0
 **/
@Component
public class MerchantBondAccountOperateClient {

    @Autowired
    private BondAccountServiceApi bondAccountServiceApi;

    public void changeBondAccountAmount(String customerCode, String tradeType, BigDecimal amount, String tradeNo,
                                                                   String sourceBillNo, String remark, String createBy) {
        BondActualAmountChangeNewReq actualAmountChangeNewReq = new BondActualAmountChangeNewReq();
        actualAmountChangeNewReq.setMerchantCode(customerCode);
        actualAmountChangeNewReq.setActualAmount(amount);
        actualAmountChangeNewReq.setRemark(remark);
        actualAmountChangeNewReq.setApplyNo(tradeNo);
        actualAmountChangeNewReq.setTradeType(tradeType);
        actualAmountChangeNewReq.setCreateBy(createBy);
        actualAmountChangeNewReq.setSourceBillNo(sourceBillNo);
        Logger.info("保证金调账请求参数：{}", JSON.toJSONString(actualAmountChangeNewReq));
        Result<Void> voidResult = bondAccountServiceApi.actualAmountChangeNew(actualAmountChangeNewReq);
        Logger.info("保证金调账请求结果：{}, req: {}", JSON.toJSONString(voidResult), JSON.toJSONString(actualAmountChangeNewReq));
        if (voidResult == null || !voidResult.isSuccess()) {
            throw new AccountProxyException(ResponseEnum.SYSTEM_EXCEPTION, "保证金调账失败:" + voidResult.getErrorMessage());
        }
    }

}
