package com.akucun.account.proxy.client.pingan;

import cn.hutool.json.JSONUtil;
import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.common.constant.CustomerType;
import com.akucun.account.proxy.common.enums.AuthRespEnum;
import com.akucun.account.proxy.common.enums.ResponseEnum;
import com.akucun.account.proxy.common.utils.AccountUtils;
import com.akucun.account.proxy.dao.mapper.AccountTenantCustomerMapper;
import com.akucun.account.proxy.dao.model.AccountTenantCustomer;
import com.akucun.account.proxy.facade.stub.others.aggregation.req.BindCardInfoDTO;
import com.akucun.account.proxy.facade.stub.others.aggregation.req.BindCardVerifyDTO;
import com.akucun.account.proxy.facade.stub.others.aggregation.res.BindAuthResp;
import com.akucun.fps.pingan.client.api.expose.MerchantService;
import com.akucun.fps.pingan.client.constants.TransferAuthStatus;
import com.akucun.fps.pingan.client.model.po.PinganAccount;
import com.akucun.fps.pingan.client.vo.AuthStatusResultVO;
import com.akucun.fps.pingan.client.vo.PinganCardVO;
import com.akucun.fps.pingan.client.vo.PinganCardVercVO;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mengxiang.base.common.log.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


/**
 * <AUTHOR>
 */
@Component
public class MerchantClient {

    @Reference(check = false)
    private MerchantService merchantService;
    @Resource
    private AccountTenantCustomerMapper accountTenantCustomerMapper;


    public boolean accountInfoCheck(String customerCode, String customerType) {
        //非店主店长不支持
        if(!CustomerType.NM.getName().equals(customerType) && !CustomerType.NMDL.getName().equals(customerType)){
            Logger.warn("非店主店长不支持账户升级，{}-{}", customerType, customerCode);
            return false;
        }

        LambdaQueryWrapper<AccountTenantCustomer> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AccountTenantCustomer::getCustomerCode, AccountUtils.getSellerCode(customerCode, customerType))
                .eq(AccountTenantCustomer::getCustomerType, customerType);
        AccountTenantCustomer accountTenantCustomer = accountTenantCustomerMapper.selectOne(wrapper);
        //租户下的店主店长不支持
        if (accountTenantCustomer != null){
            Logger.warn("租户店主店长不支持账户升级，{}-{}", customerType, customerCode);
            return false;
        }
        Logger.info("查询平安账户信息,客户编码:{},客户类型:{}", customerCode, customerType);
        com.akucun.fps.common.entity.Result<PinganAccount> result = merchantService.selectPinganAccount(AccountUtils.getSellerCode(customerCode, customerType), customerType);
        Logger.info("查询平安账户信息,客户编码:{},客户类型:{},返回信息{}", customerCode, customerType, JSONUtil.toJsonStr(result));
        //账户存在
        return result.isSuccess() && result.getData() != null;
    }

    public Result<PinganAccount> selectPinganAccount(String customerCode, String customerType) {
        try {
            Logger.info("查询平安账户信息,客户编码:{},客户类型:{}", customerCode, customerType);
            com.akucun.fps.common.entity.Result<PinganAccount> result = merchantService.selectPinganAccount(AccountUtils.getSellerCode(customerCode, customerType), customerType);
            Logger.info("查询平安账户信息,客户编码:{},客户类型:{},返回信息{}", customerCode, customerType, JSONUtil.toJsonStr(result));
            if(result != null && result.isSuccess()) {
                return Result.success(result.getData());
            } else {
                return Result.error(ResponseEnum.FAIL.getCode(), ResponseEnum.FAIL.getMessage());
            }
        } catch (Exception e) {
            return Result.error(ResponseEnum.FAIL.getCode(), ResponseEnum.FAIL.getMessage());
        }
    }

    /**
     * 绑卡发送小额鉴权申请
     *
     * @param bindCardInfoDTO
     * @return
     */
    public Result<Void> bindCardAndCheck(BindCardInfoDTO bindCardInfoDTO) {
        PinganCardVO pinganCard = generateParam(bindCardInfoDTO);
        Logger.info("鉴权绑卡参数：{}", JSON.toJSON(pinganCard));

        //24小时内同一张卡只能绑卡一次
        com.akucun.fps.common.entity.Result<AuthStatusResultVO> bankCardCode = merchantService.selectAuthStatusByCode(bindCardInfoDTO.getCustomerCode(), bindCardInfoDTO.getCustomerType());
        if (bankCardCode != null && bankCardCode.isSuccess() && bankCardCode.getData() != null) {
            if (TransferAuthStatus.DONE.name().equals(bankCardCode.getData().getReturnStatus())
                    && bankCardCode.getData().getIsLate() == 0
                    && bankCardCode.getData().getCardNum().equals(bindCardInfoDTO.getBankAccount())) {
                return Result.error(300001, "同一张银行卡24小时内只能成功绑定一次，请24小时后再次绑定或用新卡进行绑定");
            }
        }
        //绑卡并发送小额鉴权请求
        try {
            com.akucun.fps.common.entity.Result<String> bindCardResult = merchantService.bindCardForMerFir(pinganCard);
            if (!bindCardResult.isSuccess()) {
                return Result.error(bindCardResult.getErrorCode(), bindCardResult.getErrorMessage());
            }
        } catch (Exception e) {
            Logger.error("商户绑卡小额鉴权服务异常,请稍后重试", e);
            return Result.error(300002, "商户绑卡小额鉴权服务异常,请稍后重试");
        }
        return Result.success();

    }

    private PinganCardVO generateParam(BindCardInfoDTO bindCardInfoDTO) {
        PinganCardVO pinganCard = new PinganCardVO();
        //客户信息
        pinganCard.setCustomerCode(bindCardInfoDTO.getCustomerCode());
        pinganCard.setCustomerName(bindCardInfoDTO.getCustomerName());
        pinganCard.setCustomerType(bindCardInfoDTO.getCustomerType());
        pinganCard.setIdCode(bindCardInfoDTO.getIdCode());
        pinganCard.setIdType(bindCardInfoDTO.getIdType());
        //大小额联行号
        pinganCard.setBankCode(bindCardInfoDTO.getBankLineNumber());
        //超级网银号
        if ("undefined".equals(bindCardInfoDTO.getSuperBankCode())) {
            pinganCard.setsBankCode("");
        } else {
            pinganCard.setsBankCode(bindCardInfoDTO.getSuperBankCode());
        }
        //银行账号
        pinganCard.setBankCardCode(bindCardInfoDTO.getBankAccount().replaceAll(" ", ""));
        //银行名称
        pinganCard.setSbankName(bindCardInfoDTO.getBankName());
        //分支行名称
        pinganCard.setBankName(bindCardInfoDTO.getBankBranchName());
        //手机号
        pinganCard.setMobilePhone(bindCardInfoDTO.getReservedPhone());
        return pinganCard;
    }

    /**
     * 小额鉴权鉴权金额回写验证
     *
     * @param dto
     * @return
     */
    public Result<Void> bindCardForMerSec(BindCardVerifyDTO dto) {
        try {
            com.akucun.fps.common.entity.Result<AuthStatusResultVO> resultLogNo = merchantService.selectAuthStatusByCode(dto.getCustomerCode(), dto.getCustomerType());
            String logNo = null;
            if (resultLogNo != null && resultLogNo.isSuccess() && resultLogNo.getData() != null) {
                logNo = resultLogNo.getData().getLogNo();
            }
            PinganCardVercVO pinganCardVerc = new PinganCardVercVO();
            pinganCardVerc.setTranAmount(dto.getTranAmount());
            pinganCardVerc.setCustomerCode(dto.getCustomerCode());
            pinganCardVerc.setBankCardCode(dto.getCardNo());
            pinganCardVerc.setLogNo(logNo);
            pinganCardVerc.setCustomerType(dto.getCustomerType());

            com.akucun.fps.common.entity.Result<String> bindCardForMerSec = merchantService.bindCardForMerSec(pinganCardVerc);
            if (!bindCardForMerSec.isSuccess()) {
                return Result.error(200, bindCardForMerSec.getErrorMessage());
            }
        } catch (Exception e) {
            Logger.error("绑卡小额鉴权验证接口异常：", e);
            return Result.error(300002, "绑卡小额鉴权验证接口异常：" + e.getMessage());
        }
        return Result.success();
    }

    /**
     * @param customerCode
     * @param customerType
     * @return
     */
    public Result<BindAuthResp> authStatusCheck(String customerCode, String customerType) {
        BindAuthResp resp = new BindAuthResp();
        try {
            //验证小额鉴权是否成功
            com.akucun.fps.common.entity.Result<AuthStatusResultVO> result = merchantService.selectAuthStatusByCode(customerCode, customerType);
            Logger.info("校验小额鉴权是否成功：{}", JSON.toJSON(result));
            if (result == null) {
                return Result.error(300003, "小额鉴权异常，请稍后重试");
            }
            //未进行小额鉴权
            if (!result.isSuccess() || result.getData() == null) {
                //未进行小额鉴权
                resp.setIsAuth(AuthRespEnum.NONE.getValue());
                //当前商户未进行过小额鉴权操作，可进行绑卡操作
                return Result.success(resp);
            }
            //流程结束
            if (TransferAuthStatus.DONE.name().equals(result.getData().getReturnStatus())) {
                //小额鉴权已结束，
                resp.setIsAuth(AuthRespEnum.NONE.getValue());
                //"当前商户上次流程已结束，可进行绑卡操作"
                return Result.success(resp);
            }

            //小额鉴权失败
            if (TransferAuthStatus.FAIL.name().equals(result.getData().getReturnStatus())) {
                //失败
                resp.setIsAuth(AuthRespEnum.FAIL.getValue());
                resp.setResStatus(result.getData().getReturnStatus());
                resp.setResMsg(result.getData().getReturnMsg() + ",请重新绑定");
                return Result.success(resp);
            }
            //小额鉴权成功
            if (TransferAuthStatus.SUCCESS.name().equals(result.getData().getReturnStatus())) {
                //超时
                if (result.getData().getIsLate() == 1) {
                    //失败
                    resp.setIsAuth(AuthRespEnum.FAIL.getValue());
                    resp.setResStatus(result.getData().getReturnStatus());
                    resp.setResMsg("当前商户验证金额已失效，请重新绑定");
                    //当前商户上次小额鉴权已过有效期，可进行绑卡操作
                    return Result.success(resp);
                }
                //未超时
                if (result.getData().getIsLate() == 0) {
                    //成功
                    resp.setIsAuth(AuthRespEnum.SUCCESS.getValue());
                    resp.setResStatus(result.getData().getReturnStatus());
                    resp.setResMsg("当前商户小额鉴权发送成功，请输入验证金额完成绑卡流程");
                    resp.setData(result.getData());
                    //当前商户有未结束的小额鉴权操作，请完成绑卡流程
                    return Result.success(resp);
                }
            }
            //等待中
            if (TransferAuthStatus.NO_CONFIRMED.name().equals(result.getData().getReturnStatus())) {
                //处理中
                resp.setIsAuth(AuthRespEnum.PROCESSING.getValue());
                resp.setResStatus(result.getData().getReturnStatus());
                resp.setData(result.getData());
                resp.setResMsg("小额鉴权转账处理中，请稍后再试");
                //小额鉴权转账处理中，请稍后再试
                return Result.success(resp);
            }

        } catch (Exception e) {
            Logger.error("小额鉴权验证异常信息", e);
            return Result.error(300004, "小额鉴权验证服务异常，请稍后重试");
        }
        return Result.success(resp);
    }
}
