package com.akucun.account.proxy.client.merchant.entity;

import com.alibaba.fastjson.annotation.JSONField;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Author: silei
 * @Date: 2021/10/19
 * @desc:
 */
public class QueryMerchantWithdrawalDTO implements Serializable {

    private static final long serialVersionUID = 1859376693160865167L;


    @JSONField(
            name = "sub_mchid"
    )
    @NotNull(
            message = "二级商户号不能为空"
    )
    private String subMchid;
    @JSONField(
            name = "out_request_no"
    )
    private String outRequestNo;
    @JSONField(
            name = "withdraw_id"
    )
    private String withdrawId;

    public String getSubMchid() {
        return subMchid;
    }

    public void setSubMchid(String subMchid) {
        this.subMchid = subMchid;
    }

    public String getOutRequestNo() {
        return outRequestNo;
    }

    public void setOutRequestNo(String outRequestNo) {
        this.outRequestNo = outRequestNo;
    }

    public String getWithdrawId() {
        return withdrawId;
    }

    public void setWithdrawId(String withdrawId) {
        this.withdrawId = withdrawId;
    }
}
