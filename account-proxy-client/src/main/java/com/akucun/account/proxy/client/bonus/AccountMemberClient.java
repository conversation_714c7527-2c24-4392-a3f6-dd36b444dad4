package com.akucun.account.proxy.client.bonus;

import com.akucun.account.center.client.model.TradeInfo;
import com.akucun.account.center.client.model.account.AccountBookDO;
import com.akucun.account.center.client.model.account.AccountBookDetailDO;
import com.akucun.account.center.client.model.query.AccountDetailQuery;
import com.akucun.account.center.client.model.query.AccountQuery;
import com.akucun.account.center.common.entity.Query;
import com.akucun.account.center.common.entity.ResultList;
import com.akucun.account.center.common.util.DateUtils;
import com.akucun.account.center.common.util.MathHelper;
import com.akucun.account.proxy.client.center.AccountCenterClient;
import com.akucun.account.proxy.common.enums.ResponseEnum;
import com.akucun.account.proxy.common.exception.AccountProxyException;
import com.akucun.account.proxy.common.utils.AmountUtils;
import com.akucun.account.proxy.common.utils.BonusTradeTypeUtils;
import com.akucun.account.proxy.facade.stub.enums.DetailTypeConstants;
import com.akucun.account.proxy.facade.stub.others.account.req.MemberBonusAccountTradeInfo;
import com.akucun.common.Result;
import com.akucun.member.api.AccountService;
import com.akucun.member.api.PayService;
import com.akucun.member.api.vo.AccountDetail;
import com.akucun.member.api.vo.AccountRecordVO;
import com.akucun.member.api.vo.QueryAccountRecordsReq;
import com.akucun.member.api.vo.UserAccountRecordVO;
import com.akucun.member.api.vo.UserAccountVO;
import com.akucun.member.center.api.MemberInfoService;
import com.akucun.member.center.model.vo.user.MemberUserInfoVO;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.mengxiang.base.common.log.Logger;

import lombok.NonNull;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 * @Author: silei
 * @Date: 2021/1/11
 * @desc: 老奖励金账户相关
 */
@Component
public class AccountMemberClient {

    @Resource
    private MemberInfoService memberInfoService;

    @Reference(check = false)
    private PayService payService;

    @Reference(check = false)
    private AccountService accountService;

    private List<String> sourceSystems;

    @Resource
    private AccountCenterClient accountCenterClient;

    @Value("${award.source.systems:RED_PACKET,MARKETING,ACCOUNT}")
    public void setSourceSystems(String sourceSystemStr){
        Logger.info("sourceSystemStr :{}",sourceSystemStr);
        if (StringUtils.isBlank(sourceSystemStr)){
            sourceSystems = new ArrayList<>();
        } else {
            sourceSystems = Arrays.asList(sourceSystemStr.split(","));
        }
    }
    //奖励金支出类型
    private List<String> outcomeTradeTypes;

    @Value("${bonus.outcome.tradeTypes:TRADE_TYPE_008,TRADE_TYPE_011,TRADE_TYPE_012,TRADE_TYPE_014,TRADE_TYPE_018}")
    private void setOutcomeTradeTypes(String outcomeTradeTypeStr) {
        if (StringUtils.isBlank(outcomeTradeTypeStr)) {
            outcomeTradeTypes = new ArrayList<>();
        } else {
            outcomeTradeTypes = Arrays.asList(outcomeTradeTypeStr.split(","));
        }
    }

    //是否开启老奖励金交易幂等校验
    @Value("${old.bonus.trade.idempotent.check.global.switch:true}")
    private boolean oldBonusTradeIdempotentCheckGlobalSwitch;

    //老奖励金交易需要校验幂等的tradeType
    //5,10,12,15,17,20,21,24
    @Value("#{'${old.bonus.trade.idempotent.check.tradeTypes:TRADE_TYPE_003,TRADE_TYPE_004,TRADE_TYPE_005,TRADE_TYPE_006,TRADE_TYPE_010,TRADE_TYPE_015,TRADE_TYPE_016,TRADE_TYPE_019}'.split(',')}")
    private List<String> oldBonusTradeIdempotentCheckTradeTypes;

    //是否开启老奖励金交易检查账户中心奖励金账户状态
    @Value("${old.bonus.trade.check.new.account.status.switch:true}")
    private boolean oldBonusTradeCheckNewAccountStatusSwitch;

    /**
     * customerCode查询userId
     *
     * @param customerCode
     * @return
     */
    public String convertCustomerCodeToUserId(String customerCode) {
        //验证一下会员查询不到余额的问题 去掉NM
        String memberCustomerCode = customerCode.replace("NM","");
        Result<MemberUserInfoVO> result = memberInfoService.selectByUserCode(memberCustomerCode);
        if (result != null && result.isSuccess() && result.getData() != null) {
            return result.getData().getId();
        }else {
            Logger.info("convertCustomerCodeToUserId exception, customerCode:{}",customerCode);
        }
        return null;
    }

    /**
     * 老奖励金账户交易
     *
     * @param tradeInfo
     */
    public void bonusAccountTrade(TradeInfo tradeInfo) {

        try {
            if (sourceSystems.contains(tradeInfo.getSourceSystem()) && DetailTypeConstants.TRADE_TYPE_005.getName().equals(tradeInfo.getTradeType())){
                //账户中心处理
                return;
            }
            Logger.info("bonusAccountTrade 请求老奖励金账户账户customerCode:{}", tradeInfo.getCustomerCode());
            String userId = convertCustomerCodeToUserId(tradeInfo.getCustomerCode());
            if (StringUtils.isBlank(userId)){
                Logger.info("bonusAccountTrade 查询userId异常, customerCode:{}" , tradeInfo.getCustomerCode());

                //TODO 明确要调老系统，如果参数不对，是不是得抛错，不然这种情况就都直接跳过老系统调用，跑账户中心了，两边数据肯定不一致
                throw new AccountProxyException(ResponseEnum.BONUS_ACCOUNT_TRADE_ERROR.getCode(), "查询会员userId异常, customerCode:" + tradeInfo.getCustomerCode());
            }
            Integer reason = BonusTradeTypeUtils.type2Reason(tradeInfo.getTradeType());
            if (reason == null) {
                Logger.info("bonusAccountTrade 未匹配到奖励金交易类型：{}", tradeInfo.getTradeType());

                //TODO 明确要调老系统，如果参数不对，是不是得抛错，不然这种情况就都直接跳过老系统调用，跑账户中心了，两边数据肯定不一致
                throw new AccountProxyException(ResponseEnum.BONUS_ACCOUNT_TRADE_ERROR.getCode(), "未匹配到奖励金交易类型, tradeType:" + tradeInfo.getTradeType());
            }

            // 当全局开关打开或者tradeType在白名单中时，进行幂等校验
            if (oldBonusTradeIdempotentCheckGlobalSwitch || oldBonusTradeIdempotentCheckTradeTypes.contains(tradeInfo.getTradeType())) {
                //查询老奖励金交易是否存在, 如果存在则不处理
                AccountRecordVO oldBonusTrade = queryOldBonusTrade(userId, reason, tradeInfo.getTradeNo());
                if (oldBonusTrade != null) {
                    Logger.warn("bonusAccountTrade 老奖励金交易已存在，userId:{},reason:{},transId:{}", userId, reason, tradeInfo.getTradeNo());
                    return;
                }
            
            }

            AccountBookDO bonusAccountBook = null;
            // 如果开关打开，则检查账户中心奖励金账户是否被冻结, 如果被冻结则抛异常
            if (oldBonusTradeCheckNewAccountStatusSwitch) {
                bonusAccountBook = accountCenterClient.queryAccountBook(tradeInfo.getCustomerCode(), tradeInfo.getAccountTypeKey());
                if (bonusAccountBook != null && bonusAccountBook.isOptionFreeze()) {
                    Logger.warn("bonusAccountTrade 账户中心奖励金账户被冻结, customerCode:{}, userId:{},reason:{},transId:{}", tradeInfo.getCustomerCode(), userId, reason, tradeInfo.getTradeNo());
                    throw new AccountProxyException(ResponseEnum.ACCOUNT_FREEZE);
                }
            }
            
            int amount = (int) MathHelper.mul(tradeInfo.getAmount().doubleValue(), 100);
            Map<String, Object> params = new HashMap<>();
            params.put("biangengmiaoshu", tradeInfo.getRemark());
            params.put("transId", tradeInfo.getTradeNo());
            params.put("dingdanID", tradeInfo.getSourceBillNo());
            if (outcomeTradeTypes.contains(tradeInfo.getTradeType())) {
                // 奖励金支出交易时需要校验账户中心奖励金账户是否被被锁定，如果账户时锁定状态则抛异常
                // 锁定状态下交易可入不可出, 故需要校验支出交易
                if (oldBonusTradeCheckNewAccountStatusSwitch) {
                    if (bonusAccountBook != null && bonusAccountBook.isOptionLock()) {
                        Logger.warn("bonusAccountTrade 账户中心奖励金账户被锁定, customerCode:{}, userId:{},reason:{},transId:{}", tradeInfo.getCustomerCode(), userId, reason, tradeInfo.getTradeNo());
                        throw new AccountProxyException(ResponseEnum.ACCOUNT_UPGRADE_LOCK);
                    }
                }

                Logger.info("bonusAccountTrade 支出金额 userId:{}(CustomerCode={}),amount:{},reason:{}", userId, tradeInfo.getCustomerCode(),amount, reason);
                accountService.subJine(userId, amount, reason, params);
            } else {
                Logger.info("bonusAccountTrade 收入金额 userId:{}(CustomerCode={}),amount:{},reason:{}", userId, tradeInfo.getCustomerCode(),amount, reason);
                accountService.addJine(userId, amount, reason, params);
            }
        } catch (Exception e) {
            Logger.error("bonusAccountTrade老奖励金发放异常:{}",JSON.toJSONString(tradeInfo),e);
           throw new AccountProxyException(ResponseEnum.BONUS_ACCOUNT_TRADE_ERROR);
        }
    }

    /**
     * 查询账户
     * @param query
     * @return
     */
    public ResultList<AccountBookDetailDO> queryAccountDetail(Query<AccountDetailQuery> query) {
        ResultList<AccountBookDetailDO> result = new ResultList<>();
        //1.参数组装
        AccountDetailQuery  detailQuery = query.getData();
        String userId = convertCustomerCodeToUserId(detailQuery.getCustomerCode());
        if (StringUtils.isBlank(userId)){
            Logger.info("AccountMemberClient queryAccountDetail 查询userId异常, customerCode:{}" , detailQuery.getCustomerCode());
            return null;
        }
        com.akucun.member.api.entity.Query<AccountDetail> bonusQuery = new com.akucun.member.api.entity.Query<>();
        BeanUtils.copyProperties(query,bonusQuery);
        AccountDetail detail = new AccountDetail();
        detail.setBeginTime(detailQuery.getBeginTime());
        detail.setEndTime(detailQuery.getEndTime());
        if ("TYPE_0".equals(detailQuery.getChangeType())){
            //入款
            detail.setFlag(0);
        }else if ("TYPE_2".equals(detailQuery.getChangeType())){
            //出款
            detail.setFlag(1);
        }
        if (StringUtils.isNotEmpty(detailQuery.getTradeType())){
            detail.setReason(BonusTradeTypeUtils.type2Reason(detailQuery.getTradeType()));
        }
        detail.setUserid(userId);
        bonusQuery.setData(detail);
        //2.请求
        Logger.info("AccountMemberClient queryAccountDetail 请求老账户参数:{}", JSON.toJSONString(bonusQuery));
        com.akucun.member.api.entity.ResultList<UserAccountRecordVO> resultList = accountService.queryAccountRecordList(bonusQuery);
        if (resultList == null) {
            Logger.warn("AccountMemberClient queryAccountDetail 请求老账户结果为空！");
            result.setSuccess(false);
            result.setErrorMessage("请求老账户结果为空");
            return result;
        }
        if (0 == resultList.getTotal()) {
            result.setSuccess(true);
            result.setErrorMessage("请求老账户结果为0");
            return result;
        }
        if (!resultList.isSuccess()){
            Logger.warn("AccountMemberClient queryAccountDetail 请求老账户结果异常 resultList:{}", JSON.toJSONString(resultList));
            result.setSuccess(false);
            result.setErrorMessage("请求老账户结果异常");
            return result;
        }
        //结果处理
        List<AccountBookDetailDO> list = new ArrayList<>();
        for (UserAccountRecordVO vo : resultList.getDatalist()) {
            AccountBookDetailDO detailDO = new AccountBookDetailDO();
            detailDO.setDirect(detailQuery.getChangeType());
            detailDO.setAmount(new BigDecimal(AmountUtils.fen2yuan(String.valueOf(vo.getJine()))));
            detailDO.setBalance(new BigDecimal(AmountUtils.fen2yuan(String.valueOf(vo.getZongyue()))));
            detailDO.setTradeType(BonusTradeTypeUtils.reason2Type(vo.getBiangengyuanyin()));
            detailDO.setRemark(vo.getMiaoshu());
            detailDO.setTradeNo(vo.getTransId());
            detailDO.setCreateDate(DateUtils.pareseDate(vo.getTime(),DateUtils.HMS_FORMAT));
            list.add(detailDO);
        }
        result.setDatalist(list);
        result.setStart(query.getStart());
        result.setPageSize(query.getPageSize());
        result.setTotal(resultList.getTotal());

        return result;
    }

    /**
     *  查询账本信息
     * @param accountQuery
     * @return
     */
    public Result<AccountBookDO> queryAccount(AccountQuery accountQuery) {
        Logger.info("AccountMemberClient queryAccount customerCode:{}", accountQuery.getCustomerCode());
        String userId = convertCustomerCodeToUserId(accountQuery.getCustomerCode());
        if (org.apache.commons.lang3.StringUtils.isEmpty(userId)) {
            return Result.error(ResponseEnum.ACCOUNT_INFORMATION_EXCEPTION.getCode(), ResponseEnum.ACCOUNT_INFORMATION_EXCEPTION.getMessage());
        }
        UserAccountVO vo = accountService.getAllJineByUser(userId);
        Logger.info("AccountMemberClient queryAccount vo:{}",JSON.toJSONString(vo));
        if (vo == null) {
            return Result.error(ResponseEnum.BONUS_ACCT_QUERY_ERROR.getCode(), ResponseEnum.BONUS_ACCT_QUERY_ERROR.getMessage());
        }
        AccountBookDO accountBookDO = new AccountBookDO();
        accountBookDO.setCustomerCode(accountQuery.getCustomerCode());
        accountBookDO.setAmount(new BigDecimal(AmountUtils.fen2yuan(String.valueOf(vo.getYue()))));
        accountBookDO.setBalance(new BigDecimal(AmountUtils.fen2yuan(String.valueOf(vo.getKeyongyue()))));
        accountBookDO.setFreezeAmount(new BigDecimal(AmountUtils.fen2yuan(String.valueOf(vo.getSuodingyue()))));
        accountBookDO.setOption(5L);
        return Result.success(accountBookDO);
    }

    /**
     * 查询奖励金总额
     * @param customerCode
     * @return
     */
    public Result<Long> getAllBonus(String customerCode) {
        Long bonus;
        String userId = convertCustomerCodeToUserId(customerCode);
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(userId)) {
            bonus = accountService.getAllBonus(userId);
        } else {
            return Result.error(ResponseEnum.BONUS_QUERY_ERROR.getCode(), ResponseEnum.BONUS_QUERY_ERROR.getMessage());
        }
        return Result.success(bonus);
    }

    /**
     *  查询账本信息
     * @param userId
     * @return
     */
    public Result<AccountBookDO> queryMemberBonusAccount(String userId) {
        Logger.info("AccountMemberClient queryMemberBonusAccount userId:{}", userId);
        UserAccountVO vo = accountService.getAllJineByUser(userId);
        Logger.info("AccountMemberClient queryMemberBonusAccount vo:{}",JSON.toJSONString(vo));
        if (vo == null) {
            return Result.error(ResponseEnum.BONUS_ACCT_QUERY_ERROR.getCode(), "奖励金账户不存在");
        }
        AccountBookDO accountBookDO = new AccountBookDO();
        accountBookDO.setCustomerCode(userId);
        accountBookDO.setAmount(new BigDecimal(AmountUtils.fen2yuan(String.valueOf(vo.getYue()))));
        accountBookDO.setBalance(new BigDecimal(AmountUtils.fen2yuan(String.valueOf(vo.getKeyongyue()))));
        accountBookDO.setFreezeAmount(new BigDecimal(AmountUtils.fen2yuan(String.valueOf(vo.getSuodingyue()))));
        accountBookDO.setOption(5L);
        return Result.success(accountBookDO);
    }

    /**
     * 老奖励金账户交易
     *
     * @param tradeInfo
     */
    public void bonusAccountTradeByUserId(MemberBonusAccountTradeInfo tradeInfo) {
        try {
            if (sourceSystems.contains(tradeInfo.getSourceSystem()) && DetailTypeConstants.TRADE_TYPE_005.getName().equals(tradeInfo.getTradeType())) {
                //账户中心处理
                return;
            }

            String userId = tradeInfo.getUserId();
            if (StringUtils.isBlank(userId)) {
                Logger.info("bonusAccountTradeByUserId 用户userId为空：{}", userId);
                throw new AccountProxyException(ResponseEnum.BONUS_ACCOUNT_TRADE_ERROR.getCode(), "用户userId为空");
            }

            Integer reason = BonusTradeTypeUtils.type2Reason(tradeInfo.getTradeType());
            if (reason == null) {
                Logger.info("bonusAccountTradeByUserId 未匹配到奖励金交易类型：{}", tradeInfo.getTradeType());
                throw new AccountProxyException(ResponseEnum.BONUS_ACCOUNT_TRADE_ERROR.getCode(), "未匹配到奖励金交易类型");
            }

            // 当全局开关打开或者tradeType在白名单中时，进行幂等校验
            if (oldBonusTradeIdempotentCheckGlobalSwitch || oldBonusTradeIdempotentCheckTradeTypes.contains(tradeInfo.getTradeType())) {
                //查询老奖励金交易是否存在, 如果存在则不处理
                AccountRecordVO oldBonusTrade = queryOldBonusTrade(userId, reason, tradeInfo.getTradeNo());
                if (oldBonusTrade != null) {
                    Logger.warn("bonusAccountTradeByUserId 老奖励金交易已存在，userId:{},reason:{},transId:{}", userId, reason, tradeInfo.getTradeNo());
                    return;
                }
            }

            int amount = (int) MathHelper.mul(tradeInfo.getAmount().doubleValue(), 100);
            Map<String, Object> params = new HashMap<>();
            params.put("biangengmiaoshu", tradeInfo.getRemark());
            params.put("transId", tradeInfo.getTradeNo());
            params.put("dingdanID", tradeInfo.getSourceBillNo());
            if (outcomeTradeTypes.contains(tradeInfo.getTradeType())) {
                Logger.info("bonusAccountTradeByUserId 支出金额 userId:{},amount:{},reason:{}", userId, amount, reason);
                accountService.subJine(userId, amount, reason, params);
            } else {
                Logger.info("bonusAccountTradeByUserId 收入金额 userId:{},amount:{},reason:{}", userId, amount, reason);
                accountService.addJine(userId, amount, reason, params);
            }
        } catch (AccountProxyException e) {
            throw e;
        } catch (Exception e) {
            throw new AccountProxyException(ResponseEnum.BONUS_ACCOUNT_TRADE_ERROR);
        }
    }

    /**
     * 根据userId, reason, transId查询老奖励金交易
     * @param userId
     * @param reason
     * @param transId
     * @return
     */
    public AccountRecordVO queryOldBonusTrade(@NonNull String userId, @NonNull Integer reason, @NonNull String transId) {
        QueryAccountRecordsReq var1 = new QueryAccountRecordsReq();
        var1.setUserid(userId);
        var1.setReasons(Arrays.asList(reason));
        var1.setTransId(transId);
        //默认查询第一页，每页10条, 理论上只会查出一条, 如果查出多条则异常
        var1.setPageNo(1);
        var1.setPageSize(10);

        //查询老奖励金交易
        List<AccountRecordVO> queryAccountRecords = accountService.queryAccountRecords(var1);
        if (CollectionUtils.isEmpty(queryAccountRecords)) {
            return null;
        } else {
            if (queryAccountRecords.size() > 1) {
                Logger.error("老奖励金交易异常，存在重复交易，userId:{},reason:{},transId:{}", userId, reason, transId);
                throw new AccountProxyException(ResponseEnum.BONUS_ACCOUNT_TRADE_ERROR.getCode(), "老奖励金交易异常，存在重复交易");
            }
            return queryAccountRecords.get(0);
        }
    }

    public Result<List<AccountRecordVO>> queryOldBonusTrade(@NonNull String customerCode, @NonNull String tradeNo) {
        //解析userId
        Logger.info("queryOldBonusTrade 请求老奖励金账户账户customerCode:{}", customerCode);
        String userId = convertCustomerCodeToUserId(customerCode);
        if (StringUtils.isBlank(userId)){
            return Result.error(ResponseEnum.BONUS_ACCT_QUERY_ERROR.getCode(), "会员账户不存在");
        }

        //查询记录
        QueryAccountRecordsReq var1 = new QueryAccountRecordsReq();
        var1.setUserid(userId);
        //var1.setReasons(Arrays.asList(reason));
        var1.setTransId(tradeNo);
        //默认查询第一页，每页10条, 理论上只会查出一条, 如果查出多条则异常
        var1.setPageNo(1);
        var1.setPageSize(10);

        //查询老奖励金交易
        Logger.info("queryOldBonusTrade检查老奖励金记录是否存在req:{}", JSON.toJSONString(var1));
        List<AccountRecordVO> queryAccountRecords = accountService.queryAccountRecords(var1);
        Logger.info("queryOldBonusTrade检查老奖励金记录是否存在resp:{}", JSON.toJSONString(queryAccountRecords));

        return Result.success(queryAccountRecords);
    }

    /**
     * 根据tradeInfo查询老奖励金交易
     * @param tradeInfo
     * @return
     */
    public AccountRecordVO queryOldBonusTradeByTradeInfo(TradeInfo tradeInfo) {
        Logger.info("bonusAccountTrade 请求老奖励金账户账户customerCode:{}", tradeInfo.getCustomerCode());
        String userId = convertCustomerCodeToUserId(tradeInfo.getCustomerCode());
        if (StringUtils.isBlank(userId)){
            throw new AccountProxyException(ResponseEnum.BONUS_ACCT_QUERY_ERROR.getCode(), "老奖励金交易查询异常，userId为空");
        }
        Integer reason = BonusTradeTypeUtils.type2Reason(tradeInfo.getTradeType());
        if (reason == null) {
            throw new AccountProxyException(ResponseEnum.BONUS_ACCT_QUERY_ERROR.getCode(), "老奖励金交易查询异常，未匹配到奖励金交易类型");
        }
        return queryOldBonusTrade(userId, reason, tradeInfo.getTradeNo());
    }

}
