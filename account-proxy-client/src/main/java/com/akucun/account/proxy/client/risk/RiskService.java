package com.akucun.account.proxy.client.risk;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.common.help.StringHelper;
import com.akucun.risk.gateway.facade.stub.enums.DecisionEnum;
import com.akucun.risk.gateway.facade.stub.namelist.dto.request.RiskNamelistCheckRequest;
import com.akucun.risk.gateway.facade.stub.namelist.dto.request.RiskNamelistCheckV2Data;
import com.akucun.risk.gateway.facade.stub.namelist.dto.request.RiskNamelistCheckV2Request;
import com.akucun.risk.gateway.facade.stub.namelist.dto.response.RiskNamelistCheckResponse;
import com.akucun.risk.gateway.facade.stub.namelist.feign.RiskNamelistServiceFacade;
import com.alibaba.fastjson.JSON;
import com.mengxiang.base.common.log.Logger;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : 风控接口
 * @Create on : 2025/2/18 11:41
 **/
@Service
public class RiskService {
    @Autowired
    private RiskNamelistServiceFacade riskNamelistServiceFacade;
    @Value("${spring.application.name:account-proxy}")
    private String appId;
    @Value("${promo.yqj.risk.scene:1000003}")
    private String riskScene;

    /**
     * 风控处理
     * @param nmCode    店长编码
     * @param transBillDate    发奖日期
     * @return
     */
    public Pair<Boolean, String> checkName(String nmCode,String transBillDate) {
        try {
            RiskNamelistCheckV2Request request = new RiskNamelistCheckV2Request();
            request.setRequestId(StringHelper.uuid());
            request.setAppId(appId);
            RiskNamelistCheckV2Data checkData = new RiskNamelistCheckV2Data();
            request.setData(checkData);
            checkData.setListLibraryCode(riskScene);
            checkData.setDataId(nmCode);
            checkData.setDt(transBillDate);
            Logger.info("营销月勤奖赋能营-奖励下发-风控请求:req={}", JSON.toJSONString(request));
            Result<RiskNamelistCheckResponse> result = riskNamelistServiceFacade.checkNamelistV2(request);
            Logger.info("营销月勤奖赋能营-奖励下发-风控结果:resp={}", JSON.toJSONString(result));
            if (ObjectUtils.isEmpty(result) || !result.getSuccess() || ObjectUtils.isEmpty(result.getData())) {
                return Pair.of(false, ObjectUtils.isEmpty(result) ? "调用风控异常" : (!result.getSuccess() ? result.getMessage() : result.getData().getSuggestion().name()));
            }
            if (result.getData().getSuggestion() == DecisionEnum.Accept) {
                return Pair.of(true, result.getData().getSuggestion().name());
            }

            return Pair.of(false, result.getData().getSuggestion().name());
        }catch (Exception e){
            Logger.warn("营销月勤奖赋能营-奖励下发-风控请求失败:nmCode={}",nmCode,e);
            throw e;
        }
    }
}
