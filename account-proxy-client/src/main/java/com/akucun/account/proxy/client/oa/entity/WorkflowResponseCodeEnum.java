package com.akucun.account.proxy.client.oa.entity;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Create on : 2025/1/14 19:57
 **/
@Getter
public enum WorkflowResponseCodeEnum {

    SUCCESS("0","成功"),

    CREATE_FAIL("-1","创建流程失败"),

    NO_PERMISSION("-2","没有创建权限"),

    CREATE_FAIL_2("-3","创建流程失败"),

    FIELD_ERROR("-4","字段或表名不正确"),

    UPDATE_WORKFLOW_LEVEL_FAIL("-5","更新流程级别失败"),

    NO_CREATE_WAIT_TASK("-6","无法创建流程待办任务"),

    NEXT_NODE_ERROR("-7","流程下一节点出错，请检查流程的配置，在OA中发起流程进行测试"),

    FILL_ERROR("-8","流程节点自动赋值操作错误");

    private String code;

    private String desc;

    WorkflowResponseCodeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static WorkflowResponseCodeEnum getEnumByCode(String code) {
    	for(WorkflowResponseCodeEnum item : WorkflowResponseCodeEnum.values()) {
    		if(item.getCode().equals(code)) {
    			return item;
    		}
    	}
    	return null;
    }
}
