package com.akucun.account.proxy.client.center;

import com.akucun.account.center.client.model.AccountInfo;
import com.akucun.account.center.client.model.TradeInfo;
import com.akucun.account.center.client.model.TransferInfo;
import com.akucun.account.center.client.model.account.AccountBookDO;
import com.akucun.account.center.client.model.account.AccountBookDetailDO;
import com.akucun.account.center.client.model.query.AccountDetailQuery;
import com.akucun.account.center.client.model.query.AccountQuery;
import com.akucun.account.center.client.model.req.QueryTradeSumReq;
import com.akucun.account.center.client.model.rsp.QueryTradeSumRsp;
import com.akucun.account.center.feign.AccountCenterService;
import com.akucun.account.proxy.client.bonus.AccountMemberClient;
import com.akucun.account.proxy.client.saas.TenantClient;
import com.akucun.account.proxy.common.enums.AccountTypeEnum;
import com.akucun.account.proxy.common.enums.BusiTypeEnum;
import com.akucun.account.proxy.common.enums.ErrorCodeConstants;
import com.akucun.account.proxy.common.enums.ResponseEnum;
import com.akucun.account.proxy.common.exception.AccountProxyException;
import com.akucun.account.proxy.common.utils.CustomerTypeUtils;
import com.akucun.account.proxy.dao.mapper.AccountTenantCustomerMapper;
import com.akucun.account.proxy.dao.model.AccountTenantCustomer;
import com.akucun.account.proxy.facade.stub.enums.AccountKeyConstants;
import com.akucun.account.proxy.facade.stub.enums.CustomerType;
import com.akucun.account.proxy.facade.stub.others.account.req.AYTTransferReq;
import com.akucun.account.proxy.facade.stub.others.account.req.AccountOperateInfoReq;
import com.akucun.account.proxy.facade.stub.others.account.req.QueryTradeSumRequest;
import com.akucun.account.proxy.facade.stub.others.account.req.TradeQueryRequest;
import com.akucun.cloud.security.CodeUtils;
import com.akucun.common.Result;
import com.akucun.fps.pingan.client.constants.AccountCustPropertyType;
import com.akucun.fps.pingan.client.constants.CustomerNatureType;
import com.akucun.fps.pingan.client.vo.MemBerRegisterVO;
import com.akucun.fps.pingan.feign.api.merchant.MerchantServiceApi;
import com.akucun.member.api.AccountService;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.mengxiang.base.common.log.Logger;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 * @Author: silei
 * @Date: 2021/1/13
 * @desc: 账户中心client
 */
@Component
public class AccountCenterClient {

    @Resource
    private AccountCenterService accountCenterService;
    @Resource
    private MerchantServiceApi merchantServiceApi;
    @Reference(check = false)
    private AccountService bonusAccountService;
    @Autowired
    private TenantClient tenantClient;
    @Autowired
    private AccountMemberClient accountMemberClient;
    @Resource
    private AccountTenantCustomerMapper accountTenantCustomerMapper;

    @Value("${account.bonus.switch:true}")
    private boolean accountBonusSwitch;

    @Value("${ayt.awd.budget.accountTypeKey:5C1AA510D78BD40DEC1220699DB78743}")
    private String aytAwdBudgetAccountTypeKey;

    @Value("${ayt.awd.budget.tradeType:TRADE_TYPE_701}")
    private String aytAwdBudgetTradeType;

    @Value("${ayt.awd.budget.customCode:151738493257170900}")
    private String aytAwdBudgetCustomCode;

    @Value("${ayt.awd.shoper.accountTypeKey:8D256656F0A9E0A959024F16A8C910B3}")
    private String xdShoperAccountTypeKey;

    @Value("${ayt.awd.shoper.tradeType:TRADE_TYPE_051}")
    private String xdShoperTradeType;

    @Value("${ayt.trade.sum.trade.types:TRADE_TYPE_046,TRADE_TYPE_050,TRADE_TYPE_051,TRADE_TYPE_052,TRADE_TYPE_461,TRADE_TYPE_462,TRADE_TYPE_463,TRADE_TYPE_464,TRADE_TYPE_472,TRADE_TYPE_473,TRADE_TYPE_474,TRADE_TYPE_475,TRADE_TYPE_476,TRADE_TYPE_477,TRADE_TYPE_478,TRADE_TYPE_479,TRADE_TYPE_490}")
    private String aytTradeSumTradeTypes;
    /**
     * 需要开平安账户的托管账户key
     */
    private List<String> escrowKeyList;

    @ApolloJsonValue("${trade.sum.accountkey.mapper}")
    private Map<String, Map<String,List<String>>> tradeTypeByBusiType;

    @Value("${open.pingan.account.accountKeys:8D256656F0A9E0A959024F16A8C910B3}")
    public void setEscrowKeyList(String accountKeys) {
    	//ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        Logger.info("open pingan account accountKeys:{}", accountKeys);
        if (StringUtils.isEmpty(accountKeys)) {
            escrowKeyList = new ArrayList<>();
        } else {
            escrowKeyList = Arrays.asList(accountKeys.split(","));
        }
    }

    /**
     * 账户中心记账账户key
     */
    private List<String> accountKeyList;

    @Value("${open.center.account.accountKeys:E91767EBCDD5F2A2EAE2099362CA06F2}")
    public void setAccountKeyList(String accountKeys) {
        Logger.info("open center account accountKeys:{}", accountKeys);
        if (StringUtils.isEmpty(accountKeys)) {
            accountKeyList = new ArrayList<>();
        } else {
            accountKeyList = Arrays.asList(accountKeys.split(","));
        }
    }

    /**
     * 账户操作
     *
     * @param req
     * @return
     */
    public Result<Void> accountOperate(AccountOperateInfoReq req) {
        try {
            //1.老奖励金账户开户
            if (accountBonusSwitch && AccountKeyConstants.AWARD.getName().equals(req.getAccountTypeKey())) {
                Logger.info("AccountCenterClient addAccount customerCode:{}", req.getCustomerCode());
                String userId = accountMemberClient.convertCustomerCodeToUserId(req.getCustomerCode());
                if (StringUtils.isNotEmpty(userId)) {
                    bonusAccountService.addAccount(userId);
                }
            }
            //租户开户检查
            tenantAccountProcess(req);
            //2.账户开户
            AccountInfo accountInfo = new AccountInfo();
            BeanUtils.copyProperties(req, accountInfo);
            if (escrowKeyList.contains(req.getAccountTypeKey())) {
                //账户中心开户
                Logger.info("AccountCenterClient dealAccount accountInfo:{}", accountInfo);
                Result<Void> result = accountCenterService.dealAccount(accountInfo);
                Logger.info("AccountCenterClient dealAccount result:{}", JSON.toJSONString(result));
                if (result == null || !result.isSuccess()) {
                    return Result.error(ResponseEnum.DEAL_ACCOUNT_ERROR.getCode(), ResponseEnum.DEAL_ACCOUNT_ERROR.getMessage());
                }
                //平安银行账户
                MemBerRegisterVO vo = new MemBerRegisterVO();
                BeanUtils.copyProperties(req, vo);
                vo.setNickName(req.getCustomerName());
                if (StringUtils.isNotEmpty(req.getPhone())){
                    vo.setMobile(StringUtils.isNumeric(req.getPhone()) ? req.getPhone() : CodeUtils.decrypt(req.getPhone()).getData());
                }
                vo.setCustomerNatureType((CustomerNatureType) CustomerNatureType.getEnum(CustomerNatureType.class, CustomerTypeUtils.accountKey2Type(req.getAccountTypeKey())));
                Logger.info("AccountCenterClient registerPinganAccount req:{}", JSON.toJSONString(vo));
                com.akucun.fps.common.entity.Result<String> registerResult =  merchantServiceApi.registerPinganAccount(vo);
                Logger.info("AccountCenterClient registerPinganAccount registerResult:{}", JSON.toJSONString(registerResult));
                if (registerResult == null || !registerResult.isSuccess()) {
                    return Result.error(ResponseEnum.DEAL_ACCOUNT_ERROR.getCode(), ResponseEnum.DEAL_ACCOUNT_ERROR.getMessage());
                }
            }else if (accountKeyList.contains(req.getAccountTypeKey())){
                //账户中心记账账户
                return accountCenterService.dealAccount(accountInfo);
            } else{
                return Result.error(ResponseEnum.ACCOUNT_TYPE_KEY_ERROR.getCode(), ResponseEnum.ACCOUNT_TYPE_KEY_ERROR.getMessage());
            }
            return Result.success();
        } catch (Exception e) {
            Logger.error("AccountCenterController accountOperate exception accountInfo:{}, exceptionMsg:", req, e);
            return Result.error(ResponseEnum.DEAL_ACCOUNT_ERROR.getCode(), ResponseEnum.DEAL_ACCOUNT_ERROR.getMessage());
        }

    }

    /**
     * 租户下店主店长开户处理
     * @param req
     */
    private void tenantAccountProcess(AccountOperateInfoReq req) {
        if (StringUtils.isEmpty(req.getTenantId())){
            return;
        }
        if (!tenantClient.isCompanyTenant(Long.valueOf(req.getTenantId()))){
            //非租户店主店长开户直接返回
            return;
        }
        AccountTenantCustomer customer = new AccountTenantCustomer();
        customer.setCustomerCode(req.getCustomerCode());
        if(AccountKeyConstants.NM.getName().equals(req.getAccountTypeKey())){
            customer.setCustomerType(CustomerType.NM.getName());
        }else if (AccountKeyConstants.NMDL.getName().equals(req.getAccountTypeKey())){
            customer.setCustomerType(CustomerType.NMDL.getName());
        }else {
            //其他类型直接返回
            return;
        }
        //租户店主店长平安开SH户
        req.setCustProperty(AccountCustPropertyType.MERCHANTS.getCode());

        customer.setTenantId(req.getTenantId());
        //查询是否存在
        LambdaQueryWrapper<AccountTenantCustomer> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AccountTenantCustomer::getCustomerCode, req.getCustomerCode())
                .eq(AccountTenantCustomer::getCustomerType, customer.getCustomerType())
                .eq(AccountTenantCustomer::getTenantId, req.getTenantId());
        AccountTenantCustomer accountTenantCustomer = accountTenantCustomerMapper.selectOne(wrapper);
        if (accountTenantCustomer != null) {
            return;
        }
        //插入表中
        accountTenantCustomerMapper.insert(customer);
    }

    public Result<Void> aytAwardTransfer(AYTTransferReq aytTransferInfo) {
        Logger.info("啊呀团团长奖励发放，aytTransferInfo:{}", aytTransferInfo);
        TradeInfo from = new TradeInfo();
        from.setAccountTypeKey(aytAwdBudgetAccountTypeKey);
        from.setTradeType(aytAwdBudgetTradeType);
        from.setAmount(aytTransferInfo.getAmount());
        from.setSourceBillNo(aytTransferInfo.getSourceBillNo());
        from.setTradeNo(aytTransferInfo.getTradeNo());
        from.setRemark(aytTransferInfo.getRemark());
        from.setCustomerCode(aytAwdBudgetCustomCode);

        TradeInfo to = new TradeInfo();
        to.setAccountTypeKey(xdShoperAccountTypeKey);
        to.setTradeType(xdShoperTradeType);
        to.setAmount(aytTransferInfo.getAmount());
        to.setSourceBillNo(aytTransferInfo.getSourceBillNo());
        to.setTradeNo(aytTransferInfo.getTradeNo());
        to.setRemark(aytTransferInfo.getRemark());
        to.setCustomerCode(CustomerType.NM.getName() + aytTransferInfo.getCustomerCode());

        TransferInfo transferInfo = new TransferInfo();
        transferInfo.setFrom(from);
        transferInfo.setTo(to);
        transferInfo.setAccountTypeKey(to.getAccountTypeKey());
        transferInfo.setCustomerCode(to.getCustomerCode());
        transferInfo.setTradeNo(to.getTradeNo());
        transferInfo.setDeposit(false);
        Result<Void> result = accountCenterService.transferV2(transferInfo);
        if (!result.isSuccess()) {
            Logger.error("调用账户中心transferV2接口返回，code:{},message:{}", result.getCode(), result.getMessage());
        }
        return result;
    }

    public List<QueryTradeSumRsp> queryTradeSumForAyaTuan(List<QueryTradeSumRequest> tradeSumRequests){
        List<QueryTradeSumReq> tradeSumReqList=new ArrayList<>();
        List<String> tradeTypeList= Arrays.asList(aytTradeSumTradeTypes.split(","));
        for (QueryTradeSumRequest tradeSumRequest : tradeSumRequests) {
            QueryTradeSumReq queryTradeSumReq=new QueryTradeSumReq();
            queryTradeSumReq.setCustomerCode(tradeSumRequest.getCustomerCode());
            if(!StringUtils.isEmpty(tradeSumRequest.getBeginDate())){
                queryTradeSumReq.setBeginDate(tradeSumRequest.getBeginDate());
            }
            if(!StringUtils.isEmpty(tradeSumRequest.getEndDate())){
                queryTradeSumReq.setEndDate(tradeSumRequest.getEndDate());
            }
            queryTradeSumReq.setTradeTypes(tradeTypeList);
            queryTradeSumReq.setAccountType(AccountTypeEnum.XD_NM.getValue());
            //是否根据资金方向分组
            queryTradeSumReq.setGroupByDirect(true);
            tradeSumReqList.add(queryTradeSumReq);
        }
        Logger.info("调用账户中心queryTradeSumForAyaTuan接口参数：{}", JSON.toJSONString(tradeSumReqList));
        Result<List<QueryTradeSumRsp>> tradeSumRspResult = accountCenterService.queryTradeSumForAyaTuan(tradeSumReqList);
        Logger.info("调用账户中心queryTradeSumForAyaTuan接口返回：{}", JSON.toJSONString(tradeSumRspResult));
        if(tradeSumRspResult==null && !tradeSumRspResult.isSuccess()){
            Logger.error("调用账户中心queryTradeSumForAyaTuan接口返回，code:{},message:{}", tradeSumRspResult.getCode(), tradeSumRspResult.getMessage());
            return Collections.emptyList();
        }
        return tradeSumRspResult.getData();
    }

    public Result<List<QueryTradeSumRsp>> queryTradeSum(List<TradeQueryRequest> tradeSumRequests){
        //01-参数基础校验
        if(org.springframework.util.CollectionUtils.isEmpty(tradeSumRequests)){
            return Result.error(ErrorCodeConstants.ACCORE_101900.getErrorCode(),"参数缺失");
        }
        //兼容历史接口：阿团默认的AccountType是饷店店主
        tradeSumRequests.stream().forEach(item->{
            if(org.springframework.util.StringUtils.isEmpty(item.getAccountType())){
                if(BusiTypeEnum.AYTUAN.getCode().equals(item.getBusiType())){
                    item.setAccountType(AccountTypeEnum.XD_NM.getValue());
                }
            }
        });
        boolean paramNullCheckResult = tradeSumRequests.stream()
                .filter(item -> (org.springframework.util.StringUtils.isEmpty(item.getCustomerCode()) || org.springframework.util.StringUtils.isEmpty(item.getBusiType())
                        || org.springframework.util.StringUtils.isEmpty(item.getAccountType())))
                .findAny().isPresent();
        if(paramNullCheckResult){
            return Result.error(ErrorCodeConstants.ACCORE_101900.getErrorCode(),"参数缺失:customerCode或accountType不能为空");
        }

        //02-业务处理
        List<QueryTradeSumReq> tradeSumReqList=new ArrayList<>();
        for (TradeQueryRequest tradeSumRequest : tradeSumRequests) {
            QueryTradeSumReq queryTradeSumReq=new QueryTradeSumReq();
            queryTradeSumReq.setCustomerCode(tradeSumRequest.getCustomerCode());
            if(!StringUtils.isEmpty(tradeSumRequest.getBeginDate())){
                queryTradeSumReq.setBeginDate(tradeSumRequest.getBeginDate());
            }
            if(!StringUtils.isEmpty(tradeSumRequest.getEndDate())){
                queryTradeSumReq.setEndDate(tradeSumRequest.getEndDate());
            }
            if(tradeTypeByBusiType.containsKey(tradeSumRequest.getBusiType())
                    && tradeTypeByBusiType.get(tradeSumRequest.getBusiType()).containsKey(tradeSumRequest.getAccountType())){
                queryTradeSumReq.setTradeTypes(tradeTypeByBusiType.get(tradeSumRequest.getBusiType()).get(tradeSumRequest.getAccountType()));
            }else{
                continue;
            }
            queryTradeSumReq.setAccountType(AccountTypeEnum.XD_NM.getValue());
            //是否根据资金方向分组
            queryTradeSumReq.setGroupByDirect(true);
            tradeSumReqList.add(queryTradeSumReq);
        }
        Logger.info("调用账户中心queryTradeSumForAyaTuan接口参数：{}", JSON.toJSONString(tradeSumReqList));
        Result<List<QueryTradeSumRsp>> tradeSumRspResult = accountCenterService.queryTradeSumForAyaTuan(tradeSumReqList);
        Logger.info("调用账户中心queryTradeSumForAyaTuan接口返回：{}", JSON.toJSONString(tradeSumRspResult));
        if(tradeSumRspResult==null && !tradeSumRspResult.isSuccess()){
            Logger.error("调用账户中心queryTradeSumForAyaTuan接口返回，code:{},message:{}", tradeSumRspResult.getCode(), tradeSumRspResult.getMessage());
            return Result.error(ErrorCodeConstants.SYSTEMERROR_999999.getErrorCode(),"调用账户中心异常:"
                    +((tradeSumRspResult==null)?"相应结果为空":tradeSumRspResult.getCode() +"-" +tradeSumRspResult.getMessage()));
        }

        return Result.success(tradeSumRspResult.getData());
    }

    /**
     * 账户交易明细查询
     * @param customerCode
     * @param tradeNo
     * @param accountTypeKey
     * @return
     */
    public AccountBookDetailDO queryAccountDetailByUnqCondition(String customerCode, String tradeNo, String accountTypeKey, String tradeType) {
        AccountDetailQuery accountDetail = new AccountDetailQuery();
        accountDetail.setAccountTypeKey(accountTypeKey);
        accountDetail.setCustomerCode(customerCode);
        accountDetail.setTradeNo(tradeNo);
        accountDetail.setTradeType(tradeType);
        Logger.info("查询账户交易明细queryAccountDetailByUnqCondition, queryReq：{}", JSON.toJSONString(accountDetail));
        Result<List<AccountBookDetailDO>> resultList = accountCenterService.queryAccountBookDetailNoPage(accountDetail);
        Logger.info("查询账户交易明细queryAccountDetailByUnqCondition, queryReq：, resultList：{}", JSON.toJSONString(accountDetail), JSON.toJSONString(resultList));
        if (Objects.isNull(resultList) || !resultList.isSuccess()) {
            throw new AccountProxyException(ResponseEnum.ACCOUNT_CENTER_EXCEPTION, "查询账户交易明细异常");
        }
        List<AccountBookDetailDO> datalist = resultList.getData();
        if(CollectionUtils.isEmpty(datalist)) {
            return null;
        }
        if (datalist.size() > 1) {
            throw new AccountProxyException(ResponseEnum.ACCOUNT_CENTER_EXCEPTION, "账户交易明细非唯一");
        }
        return datalist.get(0);
    }

    /**
     * 账户交易明细查询
     * @param customerCode
     * @param tradeNo
     * @param accountTypeKey
     * @return
     */
    public List<AccountBookDetailDO> queryAccountDetails(String customerCode, String accountTypeKey,
                                                   String tradeNo, String sourceBillNo, List<String> tradeTypes,
                                                   boolean skipNoAccountBookException) {
        AccountDetailQuery accountDetail = new AccountDetailQuery();
        accountDetail.setAccountTypeKey(accountTypeKey);
        accountDetail.setCustomerCode(customerCode);
        accountDetail.setTradeNo(tradeNo);
        accountDetail.setTradeTypes(tradeTypes);
        accountDetail.setSourceBillNo(sourceBillNo);
        Logger.info("查询账户交易明细queryAccountDetails, queryReq：{}", JSON.toJSONString(accountDetail));
        Result<List<AccountBookDetailDO>> resultList = accountCenterService.queryAccountBookDetailNoPage(accountDetail);
        Logger.info("查询账户交易明细queryAccountDetails, queryReq：{}, resultList：{}", JSON.toJSONString(accountDetail), JSON.toJSONString(resultList));
        if (Objects.isNull(resultList) || !resultList.isSuccess()) {
            if (Objects.nonNull(resultList) && Objects.equals(resultList.getCode(), 100012)
                    && StringUtils.equals(resultList.getMessage(), "没有查询到账本信息！")) {
                if (skipNoAccountBookException) {
                    return Collections.emptyList();
                }
            }
            throw new AccountProxyException(ResponseEnum.ACCOUNT_CENTER_EXCEPTION, Objects.nonNull(resultList) ?  resultList.getMessage() : "查询账户交易明细异常");
        }
        if (resultList.getData() == null) {
            return Collections.emptyList();
        }
        return resultList.getData();
    }

    /**
     * 账户交易
     * @param customerCode
     * @param accountTypeKey
     * @param tradeNo
     * @param sourceBillNo
     * @param amount
     * @param traderType
     * @param remark
     */
    public void dealTrade(String customerCode,String accountTypeKey, String tradeNo, String sourceBillNo,
                          BigDecimal amount, String traderType, String remark) {
        TradeInfo request = buildTradeInfo(customerCode, accountTypeKey, tradeNo, sourceBillNo, amount, traderType, remark);
        Logger.info("dealTrade 账户操作 request: {}", JSON.toJSONString(request));
        Result<Void> result =  accountCenterService.dealTrade(request);
        Logger.info("dealTrade 账户操作 request: {} response{}", JSON.toJSONString(request), JSON.toJSONString(result));
        if(!result.isSuccess()){
            throw new AccountProxyException(ResponseEnum.ACCOUNT_CENTER_EXCEPTION, result.getMessage());
        }
    }

    /**
     * 账户批量交易
     * @param tradeInfos
     */
    public void batchDealTrade(List<TradeInfo> tradeInfos) {
        Logger.info("batchDealTrade 账户操作 request: {}", JSON.toJSONString(tradeInfos));
        Result<Void> result =  accountCenterService.batchDealTrade(tradeInfos);
        Logger.info("batchDealTrade 账户操作 request: {} response{}", JSON.toJSONString(tradeInfos), JSON.toJSONString(result));
        if(!result.isSuccess()){
            throw new AccountProxyException(ResponseEnum.ACCOUNT_CENTER_EXCEPTION, result.getMessage());
        }
    }

    /**
     * 查询账户
     * @param customerCode
     * @param accountTypeKey
     * @return
     */
    public AccountBookDO queryAccountBook(String customerCode, String accountTypeKey) {
        AccountQuery accountQuery = new AccountQuery();
        accountQuery.setCustomerCode(customerCode);
        accountQuery.setAccountTypeKey(accountTypeKey);
        Result<AccountBookDO> result = accountCenterService.queryAccount(accountQuery);
        if (!result.isSuccess()) {
            throw new AccountProxyException(ResponseEnum.ACCOUNT_CENTER_EXCEPTION, "查询账本异常:" + result.getMessage());
        }
        return result.getData();
    }

    public static TradeInfo buildTradeInfo(String customerCode, String accountTypeKey, String tradeNo, String sourceBillNo, BigDecimal amount, String traderType, String remark) {
        TradeInfo request = new TradeInfo();
        request.setAmount(amount);
        request.setTradeType(traderType);
        request.setTradeNo(tradeNo);
        request.setCustomerCode(customerCode);
        request.setAccountTypeKey(accountTypeKey);
        request.setSourceBillNo(sourceBillNo);
        request.setRemark(remark);
        return request;
    }
}
