
package cn.com.weaver.services.webservices;

import javax.xml.bind.annotation.XmlRegistry;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the cn.com.weaver.services.webservices package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {


    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: cn.com.weaver.services.webservices
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link GetHendledWorkflowRequestCountResponse }
     * 
     */
    public GetHendledWorkflowRequestCountResponse createGetHendledWorkflowRequestCountResponse() {
        return new GetHendledWorkflowRequestCountResponse();
    }

    /**
     * Create an instance of {@link GetWorkflowRequest }
     * 
     */
    public GetWorkflowRequest createGetWorkflowRequest() {
        return new GetWorkflowRequest();
    }

    /**
     * Create an instance of {@link GetHendledWorkflowRequestListResponse }
     * 
     */
    public GetHendledWorkflowRequestListResponse createGetHendledWorkflowRequestListResponse() {
        return new GetHendledWorkflowRequestListResponse();
    }

    /**
     * Create an instance of {@link GetCreateWorkflowRequestInfoResponse }
     * 
     */
    public GetCreateWorkflowRequestInfoResponse createGetCreateWorkflowRequestInfoResponse() {
        return new GetCreateWorkflowRequestInfoResponse();
    }

    /**
     * Create an instance of {@link GetCreateWorkflowTypeListResponse }
     * 
     */
    public GetCreateWorkflowTypeListResponse createGetCreateWorkflowTypeListResponse() {
        return new GetCreateWorkflowTypeListResponse();
    }

    /**
     * Create an instance of {@link GetHendledWorkflowRequestList }
     * 
     */
    public GetHendledWorkflowRequestList createGetHendledWorkflowRequestList() {
        return new GetHendledWorkflowRequestList();
    }

    /**
     * Create an instance of {@link ArrayOfString }
     * 
     */
    public ArrayOfString createArrayOfString() {
        return new ArrayOfString();
    }

    /**
     * Create an instance of {@link GetCCWorkflowRequestCountResponse }
     * 
     */
    public GetCCWorkflowRequestCountResponse createGetCCWorkflowRequestCountResponse() {
        return new GetCCWorkflowRequestCountResponse();
    }

    /**
     * Create an instance of {@link GetToDoWorkflowRequestListResponse }
     * 
     */
    public GetToDoWorkflowRequestListResponse createGetToDoWorkflowRequestListResponse() {
        return new GetToDoWorkflowRequestListResponse();
    }

    /**
     * Create an instance of {@link GetLeaveDaysResponse }
     * 
     */
    public GetLeaveDaysResponse createGetLeaveDaysResponse() {
        return new GetLeaveDaysResponse();
    }

    /**
     * Create an instance of {@link GetWorkflowRequest4Split }
     * 
     */
    public GetWorkflowRequest4Split createGetWorkflowRequest4Split() {
        return new GetWorkflowRequest4Split();
    }

    /**
     * Create an instance of {@link SubmitWorkflowRequest }
     * 
     */
    public SubmitWorkflowRequest createSubmitWorkflowRequest() {
        return new SubmitWorkflowRequest();
    }

    /**
     * Create an instance of {@link GetLeaveDays }
     * 
     */
    public GetLeaveDays createGetLeaveDays() {
        return new GetLeaveDays();
    }

    /**
     * Create an instance of {@link GetCreateWorkflowList }
     * 
     */
    public GetCreateWorkflowList createGetCreateWorkflowList() {
        return new GetCreateWorkflowList();
    }

    /**
     * Create an instance of {@link DoForceOverResponse }
     * 
     */
    public DoForceOverResponse createDoForceOverResponse() {
        return new DoForceOverResponse();
    }

    /**
     * Create an instance of {@link GetProcessedWorkflowRequestCount }
     * 
     */
    public GetProcessedWorkflowRequestCount createGetProcessedWorkflowRequestCount() {
        return new GetProcessedWorkflowRequestCount();
    }

    /**
     * Create an instance of {@link GetCreateWorkflowCountResponse }
     * 
     */
    public GetCreateWorkflowCountResponse createGetCreateWorkflowCountResponse() {
        return new GetCreateWorkflowCountResponse();
    }

    /**
     * Create an instance of {@link GetMyWorkflowRequestListResponse }
     * 
     */
    public GetMyWorkflowRequestListResponse createGetMyWorkflowRequestListResponse() {
        return new GetMyWorkflowRequestListResponse();
    }

    /**
     * Create an instance of {@link DoCreateWorkflowRequest }
     * 
     */
    public DoCreateWorkflowRequest createDoCreateWorkflowRequest() {
        return new DoCreateWorkflowRequest();
    }

    /**
     * Create an instance of {@link DoForceOver }
     * 
     */
    public DoForceOver createDoForceOver() {
        return new DoForceOver();
    }

    /**
     * Create an instance of {@link GetProcessedWorkflowRequestList }
     * 
     */
    public GetProcessedWorkflowRequestList createGetProcessedWorkflowRequestList() {
        return new GetProcessedWorkflowRequestList();
    }

    /**
     * Create an instance of {@link GetWorkflowRequest4SplitResponse }
     * 
     */
    public GetWorkflowRequest4SplitResponse createGetWorkflowRequest4SplitResponse() {
        return new GetWorkflowRequest4SplitResponse();
    }

    /**
     * Create an instance of {@link GetCreateWorkflowRequestInfo }
     * 
     */
    public GetCreateWorkflowRequestInfo createGetCreateWorkflowRequestInfo() {
        return new GetCreateWorkflowRequestInfo();
    }

    /**
     * Create an instance of {@link GetAllWorkflowRequestListResponse }
     * 
     */
    public GetAllWorkflowRequestListResponse createGetAllWorkflowRequestListResponse() {
        return new GetAllWorkflowRequestListResponse();
    }

    /**
     * Create an instance of {@link GetWorkflowRequestResponse }
     * 
     */
    public GetWorkflowRequestResponse createGetWorkflowRequestResponse() {
        return new GetWorkflowRequestResponse();
    }

    /**
     * Create an instance of {@link GetCreateWorkflowTypeList }
     * 
     */
    public GetCreateWorkflowTypeList createGetCreateWorkflowTypeList() {
        return new GetCreateWorkflowTypeList();
    }

    /**
     * Create an instance of {@link GetWorkflowRequestLogsResponse }
     * 
     */
    public GetWorkflowRequestLogsResponse createGetWorkflowRequestLogsResponse() {
        return new GetWorkflowRequestLogsResponse();
    }

    /**
     * Create an instance of {@link GetCreateWorkflowListResponse }
     * 
     */
    public GetCreateWorkflowListResponse createGetCreateWorkflowListResponse() {
        return new GetCreateWorkflowListResponse();
    }

    /**
     * Create an instance of {@link GetMyWorkflowRequestCountResponse }
     * 
     */
    public GetMyWorkflowRequestCountResponse createGetMyWorkflowRequestCountResponse() {
        return new GetMyWorkflowRequestCountResponse();
    }

    /**
     * Create an instance of {@link GetToDoWorkflowRequestCount }
     * 
     */
    public GetToDoWorkflowRequestCount createGetToDoWorkflowRequestCount() {
        return new GetToDoWorkflowRequestCount();
    }

    /**
     * Create an instance of {@link GivingOpinions }
     * 
     */
    public GivingOpinions createGivingOpinions() {
        return new GivingOpinions();
    }

    /**
     * Create an instance of {@link GetCCWorkflowRequestList }
     * 
     */
    public GetCCWorkflowRequestList createGetCCWorkflowRequestList() {
        return new GetCCWorkflowRequestList();
    }

    /**
     * Create an instance of {@link Forward2WorkflowRequest }
     * 
     */
    public Forward2WorkflowRequest createForward2WorkflowRequest() {
        return new Forward2WorkflowRequest();
    }

    /**
     * Create an instance of {@link GetAllWorkflowRequestList }
     * 
     */
    public GetAllWorkflowRequestList createGetAllWorkflowRequestList() {
        return new GetAllWorkflowRequestList();
    }

    /**
     * Create an instance of {@link GetToDoWorkflowRequestCountResponse }
     * 
     */
    public GetToDoWorkflowRequestCountResponse createGetToDoWorkflowRequestCountResponse() {
        return new GetToDoWorkflowRequestCountResponse();
    }

    /**
     * Create an instance of {@link GetToDoWorkflowRequestList }
     * 
     */
    public GetToDoWorkflowRequestList createGetToDoWorkflowRequestList() {
        return new GetToDoWorkflowRequestList();
    }

    /**
     * Create an instance of {@link GetProcessedWorkflowRequestListResponse }
     * 
     */
    public GetProcessedWorkflowRequestListResponse createGetProcessedWorkflowRequestListResponse() {
        return new GetProcessedWorkflowRequestListResponse();
    }

    /**
     * Create an instance of {@link GetHendledWorkflowRequestCount }
     * 
     */
    public GetHendledWorkflowRequestCount createGetHendledWorkflowRequestCount() {
        return new GetHendledWorkflowRequestCount();
    }

    /**
     * Create an instance of {@link GetAllWorkflowRequestCountResponse }
     * 
     */
    public GetAllWorkflowRequestCountResponse createGetAllWorkflowRequestCountResponse() {
        return new GetAllWorkflowRequestCountResponse();
    }

    /**
     * Create an instance of {@link GetCreateWorkflowCount }
     * 
     */
    public GetCreateWorkflowCount createGetCreateWorkflowCount() {
        return new GetCreateWorkflowCount();
    }

    /**
     * Create an instance of {@link ForwardWorkflowRequest }
     * 
     */
    public ForwardWorkflowRequest createForwardWorkflowRequest() {
        return new ForwardWorkflowRequest();
    }

    /**
     * Create an instance of {@link SubmitWorkflowRequestResponse }
     * 
     */
    public SubmitWorkflowRequestResponse createSubmitWorkflowRequestResponse() {
        return new SubmitWorkflowRequestResponse();
    }

    /**
     * Create an instance of {@link GetUserIdResponse }
     * 
     */
    public GetUserIdResponse createGetUserIdResponse() {
        return new GetUserIdResponse();
    }

    /**
     * Create an instance of {@link GetCreateWorkflowTypeCountResponse }
     * 
     */
    public GetCreateWorkflowTypeCountResponse createGetCreateWorkflowTypeCountResponse() {
        return new GetCreateWorkflowTypeCountResponse();
    }

    /**
     * Create an instance of {@link GetCCWorkflowRequestListResponse }
     * 
     */
    public GetCCWorkflowRequestListResponse createGetCCWorkflowRequestListResponse() {
        return new GetCCWorkflowRequestListResponse();
    }

    /**
     * Create an instance of {@link GetCCWorkflowRequestCount }
     * 
     */
    public GetCCWorkflowRequestCount createGetCCWorkflowRequestCount() {
        return new GetCCWorkflowRequestCount();
    }

    /**
     * Create an instance of {@link GetProcessedWorkflowRequestCountResponse }
     * 
     */
    public GetProcessedWorkflowRequestCountResponse createGetProcessedWorkflowRequestCountResponse() {
        return new GetProcessedWorkflowRequestCountResponse();
    }

    /**
     * Create an instance of {@link GetAllWorkflowRequestCount }
     * 
     */
    public GetAllWorkflowRequestCount createGetAllWorkflowRequestCount() {
        return new GetAllWorkflowRequestCount();
    }

    /**
     * Create an instance of {@link DeleteRequestResponse }
     * 
     */
    public DeleteRequestResponse createDeleteRequestResponse() {
        return new DeleteRequestResponse();
    }

    /**
     * Create an instance of {@link ForwardWorkflowRequestResponse }
     * 
     */
    public ForwardWorkflowRequestResponse createForwardWorkflowRequestResponse() {
        return new ForwardWorkflowRequestResponse();
    }

    /**
     * Create an instance of {@link GetMyWorkflowRequestList }
     * 
     */
    public GetMyWorkflowRequestList createGetMyWorkflowRequestList() {
        return new GetMyWorkflowRequestList();
    }

    /**
     * Create an instance of {@link GetMyWorkflowRequestCount }
     * 
     */
    public GetMyWorkflowRequestCount createGetMyWorkflowRequestCount() {
        return new GetMyWorkflowRequestCount();
    }

    /**
     * Create an instance of {@link GetWorkflowNewFlag }
     * 
     */
    public GetWorkflowNewFlag createGetWorkflowNewFlag() {
        return new GetWorkflowNewFlag();
    }

    /**
     * Create an instance of {@link WriteWorkflowReadFlag }
     * 
     */
    public WriteWorkflowReadFlag createWriteWorkflowReadFlag() {
        return new WriteWorkflowReadFlag();
    }

    /**
     * Create an instance of {@link WriteWorkflowReadFlagResponse }
     * 
     */
    public WriteWorkflowReadFlagResponse createWriteWorkflowReadFlagResponse() {
        return new WriteWorkflowReadFlagResponse();
    }

    /**
     * Create an instance of {@link GetWorkflowNewFlagResponse }
     * 
     */
    public GetWorkflowNewFlagResponse createGetWorkflowNewFlagResponse() {
        return new GetWorkflowNewFlagResponse();
    }

    /**
     * Create an instance of {@link GivingOpinionsResponse }
     * 
     */
    public GivingOpinionsResponse createGivingOpinionsResponse() {
        return new GivingOpinionsResponse();
    }

    /**
     * Create an instance of {@link Forward2WorkflowRequestResponse }
     * 
     */
    public Forward2WorkflowRequestResponse createForward2WorkflowRequestResponse() {
        return new Forward2WorkflowRequestResponse();
    }

    /**
     * Create an instance of {@link DoCreateWorkflowRequestResponse }
     * 
     */
    public DoCreateWorkflowRequestResponse createDoCreateWorkflowRequestResponse() {
        return new DoCreateWorkflowRequestResponse();
    }

    /**
     * Create an instance of {@link GetCreateWorkflowTypeCount }
     * 
     */
    public GetCreateWorkflowTypeCount createGetCreateWorkflowTypeCount() {
        return new GetCreateWorkflowTypeCount();
    }

    /**
     * Create an instance of {@link GetWorkflowRequestLogs }
     * 
     */
    public GetWorkflowRequestLogs createGetWorkflowRequestLogs() {
        return new GetWorkflowRequestLogs();
    }

    /**
     * Create an instance of {@link DeleteRequest }
     * 
     */
    public DeleteRequest createDeleteRequest() {
        return new DeleteRequest();
    }

    /**
     * Create an instance of {@link GetUserId }
     * 
     */
    public GetUserId createGetUserId() {
        return new GetUserId();
    }

    /**
     * Create an instance of {@link ArrayOfArrayOfString }
     * 
     */
    public ArrayOfArrayOfString createArrayOfArrayOfString() {
        return new ArrayOfArrayOfString();
    }

}
