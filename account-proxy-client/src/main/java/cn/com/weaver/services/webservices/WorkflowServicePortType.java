
package cn.com.weaver.services.webservices;

import weaver.workflow.webservices.ArrayOfWorkflowBaseInfo;
import weaver.workflow.webservices.ArrayOfWorkflowRequestInfo;
import weaver.workflow.webservices.ArrayOfWorkflowRequestLog;
import weaver.workflow.webservices.WorkflowRequestInfo;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.ws.RequestWrapper;
import javax.xml.ws.ResponseWrapper;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.9-b130926.1035
 * Generated source version: 2.2
 * 
 */
@WebService(name = "WorkflowServicePortType", targetNamespace = "webservices.services.weaver.com.cn")
@XmlSeeAlso({
    ObjectFactory.class,
    weaver.workflow.webservices.ObjectFactory.class
})
public interface WorkflowServicePortType {


    /**
     * 
     * @param in0
     * @param in2
     * @param in1
     * @param in4
     * @param in3
     * @return
     *     returns java.lang.String
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.forward2WorkflowRequest")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "forward2WorkflowRequest", targetNamespace = "webservices.services.weaver.com.cn", className = "cn.com.weaver.services.webservices.Forward2WorkflowRequest")
    @ResponseWrapper(localName = "forward2WorkflowRequestResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "cn.com.weaver.services.webservices.Forward2WorkflowRequestResponse")
    public String forward2WorkflowRequest(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        String in1,
        @WebParam(name = "in2", targetNamespace = "webservices.services.weaver.com.cn")
        String in2,
        @WebParam(name = "in3", targetNamespace = "webservices.services.weaver.com.cn")
        int in3,
        @WebParam(name = "in4", targetNamespace = "webservices.services.weaver.com.cn")
        String in4);

    /**
     * 
     * @param in0
     * @param in2
     * @param in1
     * @param in4
     * @param in3
     * @return
     *     returns weaver.workflow.webservices.ArrayOfWorkflowRequestInfo
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.getAllWorkflowRequestList")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getAllWorkflowRequestList", targetNamespace = "webservices.services.weaver.com.cn", className = "cn.com.weaver.services.webservices.GetAllWorkflowRequestList")
    @ResponseWrapper(localName = "getAllWorkflowRequestListResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "cn.com.weaver.services.webservices.GetAllWorkflowRequestListResponse")
    public ArrayOfWorkflowRequestInfo getAllWorkflowRequestList(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        int in1,
        @WebParam(name = "in2", targetNamespace = "webservices.services.weaver.com.cn")
        int in2,
        @WebParam(name = "in3", targetNamespace = "webservices.services.weaver.com.cn")
        int in3,
        @WebParam(name = "in4", targetNamespace = "webservices.services.weaver.com.cn")
        ArrayOfString in4);

    /**
     * 
     * @param in0
     * @param in2
     * @param in1
     * @return
     *     returns weaver.workflow.webservices.WorkflowRequestInfo
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.getWorkflowRequest")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getWorkflowRequest", targetNamespace = "webservices.services.weaver.com.cn", className = "cn.com.weaver.services.webservices.GetWorkflowRequest")
    @ResponseWrapper(localName = "getWorkflowRequestResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "cn.com.weaver.services.webservices.GetWorkflowRequestResponse")
    public WorkflowRequestInfo getWorkflowRequest(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        int in1,
        @WebParam(name = "in2", targetNamespace = "webservices.services.weaver.com.cn")
        int in2);

    /**
     * 
     * @param in0
     * @param in2
     * @param in1
     * @param in4
     * @param in3
     * @return
     *     returns weaver.workflow.webservices.ArrayOfWorkflowRequestInfo
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.getHendledWorkflowRequestList")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getHendledWorkflowRequestList", targetNamespace = "webservices.services.weaver.com.cn", className = "cn.com.weaver.services.webservices.GetHendledWorkflowRequestList")
    @ResponseWrapper(localName = "getHendledWorkflowRequestListResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "cn.com.weaver.services.webservices.GetHendledWorkflowRequestListResponse")
    public ArrayOfWorkflowRequestInfo getHendledWorkflowRequestList(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        int in1,
        @WebParam(name = "in2", targetNamespace = "webservices.services.weaver.com.cn")
        int in2,
        @WebParam(name = "in3", targetNamespace = "webservices.services.weaver.com.cn")
        int in3,
        @WebParam(name = "in4", targetNamespace = "webservices.services.weaver.com.cn")
        ArrayOfString in4);

    /**
     * 
     * @param in0
     * @param in2
     * @param in1
     * @param in4
     * @param in3
     * @return
     *     returns weaver.workflow.webservices.ArrayOfWorkflowRequestInfo
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.getToDoWorkflowRequestList")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getToDoWorkflowRequestList", targetNamespace = "webservices.services.weaver.com.cn", className = "cn.com.weaver.services.webservices.GetToDoWorkflowRequestList")
    @ResponseWrapper(localName = "getToDoWorkflowRequestListResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "cn.com.weaver.services.webservices.GetToDoWorkflowRequestListResponse")
    public ArrayOfWorkflowRequestInfo getToDoWorkflowRequestList(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        int in1,
        @WebParam(name = "in2", targetNamespace = "webservices.services.weaver.com.cn")
        int in2,
        @WebParam(name = "in3", targetNamespace = "webservices.services.weaver.com.cn")
        int in3,
        @WebParam(name = "in4", targetNamespace = "webservices.services.weaver.com.cn")
        ArrayOfString in4);

    /**
     * 
     * @param in0
     * @param in2
     * @param in1
     * @param in3
     * @return
     *     returns weaver.workflow.webservices.WorkflowRequestInfo
     */
    @WebMethod(operationName = "getWorkflowRequest4split", action = "urn:weaver.workflow.webservices.WorkflowService.getWorkflowRequest4split")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getWorkflowRequest4split", targetNamespace = "webservices.services.weaver.com.cn", className = "cn.com.weaver.services.webservices.GetWorkflowRequest4Split")
    @ResponseWrapper(localName = "getWorkflowRequest4splitResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "cn.com.weaver.services.webservices.GetWorkflowRequest4SplitResponse")
    public WorkflowRequestInfo getWorkflowRequest4Split(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        int in1,
        @WebParam(name = "in2", targetNamespace = "webservices.services.weaver.com.cn")
        int in2,
        @WebParam(name = "in3", targetNamespace = "webservices.services.weaver.com.cn")
        int in3);

    /**
     * 
     * @param in0
     * @param in2
     * @param in1
     * @param in4
     * @param in3
     * @return
     *     returns java.lang.String
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.submitWorkflowRequest")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "submitWorkflowRequest", targetNamespace = "webservices.services.weaver.com.cn", className = "cn.com.weaver.services.webservices.SubmitWorkflowRequest")
    @ResponseWrapper(localName = "submitWorkflowRequestResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "cn.com.weaver.services.webservices.SubmitWorkflowRequestResponse")
    public String submitWorkflowRequest(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        WorkflowRequestInfo in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        int in1,
        @WebParam(name = "in2", targetNamespace = "webservices.services.weaver.com.cn")
        int in2,
        @WebParam(name = "in3", targetNamespace = "webservices.services.weaver.com.cn")
        String in3,
        @WebParam(name = "in4", targetNamespace = "webservices.services.weaver.com.cn")
        String in4);

    /**
     * 
     * @param in0
     * @param in1
     * @return
     *     returns int
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.getHendledWorkflowRequestCount")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getHendledWorkflowRequestCount", targetNamespace = "webservices.services.weaver.com.cn", className = "cn.com.weaver.services.webservices.GetHendledWorkflowRequestCount")
    @ResponseWrapper(localName = "getHendledWorkflowRequestCountResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "cn.com.weaver.services.webservices.GetHendledWorkflowRequestCountResponse")
    public int getHendledWorkflowRequestCount(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        ArrayOfString in1);

    /**
     * 
     * @param in0
     * @param in2
     * @param in1
     * @param in4
     * @param in3
     * @return
     *     returns java.lang.String
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.getLeaveDays")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getLeaveDays", targetNamespace = "webservices.services.weaver.com.cn", className = "cn.com.weaver.services.webservices.GetLeaveDays")
    @ResponseWrapper(localName = "getLeaveDaysResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "cn.com.weaver.services.webservices.GetLeaveDaysResponse")
    public String getLeaveDays(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        String in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        String in1,
        @WebParam(name = "in2", targetNamespace = "webservices.services.weaver.com.cn")
        String in2,
        @WebParam(name = "in3", targetNamespace = "webservices.services.weaver.com.cn")
        String in3,
        @WebParam(name = "in4", targetNamespace = "webservices.services.weaver.com.cn")
        String in4);

    /**
     * 
     * @param in5
     * @param in0
     * @param in2
     * @param in1
     * @param in4
     * @param in3
     * @return
     *     returns weaver.workflow.webservices.ArrayOfWorkflowBaseInfo
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.getCreateWorkflowList")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getCreateWorkflowList", targetNamespace = "webservices.services.weaver.com.cn", className = "cn.com.weaver.services.webservices.GetCreateWorkflowList")
    @ResponseWrapper(localName = "getCreateWorkflowListResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "cn.com.weaver.services.webservices.GetCreateWorkflowListResponse")
    public ArrayOfWorkflowBaseInfo getCreateWorkflowList(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        int in1,
        @WebParam(name = "in2", targetNamespace = "webservices.services.weaver.com.cn")
        int in2,
        @WebParam(name = "in3", targetNamespace = "webservices.services.weaver.com.cn")
        int in3,
        @WebParam(name = "in4", targetNamespace = "webservices.services.weaver.com.cn")
        int in4,
        @WebParam(name = "in5", targetNamespace = "webservices.services.weaver.com.cn")
        ArrayOfString in5);

    /**
     * 
     * @param in0
     * @param in2
     * @param in1
     * @return
     *     returns int
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.getCreateWorkflowCount")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getCreateWorkflowCount", targetNamespace = "webservices.services.weaver.com.cn", className = "cn.com.weaver.services.webservices.GetCreateWorkflowCount")
    @ResponseWrapper(localName = "getCreateWorkflowCountResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "cn.com.weaver.services.webservices.GetCreateWorkflowCountResponse")
    public int getCreateWorkflowCount(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        int in1,
        @WebParam(name = "in2", targetNamespace = "webservices.services.weaver.com.cn")
        ArrayOfString in2);

    /**
     * 
     * @param in0
     * @param in1
     * @return
     *     returns int
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.getProcessedWorkflowRequestCount")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getProcessedWorkflowRequestCount", targetNamespace = "webservices.services.weaver.com.cn", className = "cn.com.weaver.services.webservices.GetProcessedWorkflowRequestCount")
    @ResponseWrapper(localName = "getProcessedWorkflowRequestCountResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "cn.com.weaver.services.webservices.GetProcessedWorkflowRequestCountResponse")
    public int getProcessedWorkflowRequestCount(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        ArrayOfString in1);

    /**
     * 
     * @param in0
     * @param in2
     * @param in1
     * @param in4
     * @param in3
     * @return
     *     returns java.lang.String
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.forwardWorkflowRequest")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "forwardWorkflowRequest", targetNamespace = "webservices.services.weaver.com.cn", className = "cn.com.weaver.services.webservices.ForwardWorkflowRequest")
    @ResponseWrapper(localName = "forwardWorkflowRequestResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "cn.com.weaver.services.webservices.ForwardWorkflowRequestResponse")
    public String forwardWorkflowRequest(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        String in1,
        @WebParam(name = "in2", targetNamespace = "webservices.services.weaver.com.cn")
        String in2,
        @WebParam(name = "in3", targetNamespace = "webservices.services.weaver.com.cn")
        int in3,
        @WebParam(name = "in4", targetNamespace = "webservices.services.weaver.com.cn")
        String in4);

    /**
     * 
     * @param in0
     * @param in1
     * @return
     *     returns java.lang.String
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.doCreateWorkflowRequest")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "doCreateWorkflowRequest", targetNamespace = "webservices.services.weaver.com.cn", className = "cn.com.weaver.services.webservices.DoCreateWorkflowRequest")
    @ResponseWrapper(localName = "doCreateWorkflowRequestResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "cn.com.weaver.services.webservices.DoCreateWorkflowRequestResponse")
    public String doCreateWorkflowRequest(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        WorkflowRequestInfo in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        int in1);

    /**
     * 
     * @param in0
     * @param in1
     * @return
     *     returns java.lang.String
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.doForceOver")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "doForceOver", targetNamespace = "webservices.services.weaver.com.cn", className = "cn.com.weaver.services.webservices.DoForceOver")
    @ResponseWrapper(localName = "doForceOverResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "cn.com.weaver.services.webservices.DoForceOverResponse")
    public String doForceOver(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        int in1);

    /**
     * 
     * @param in0
     * @param in1
     * @return
     *     returns int
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.getCCWorkflowRequestCount")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getCCWorkflowRequestCount", targetNamespace = "webservices.services.weaver.com.cn", className = "cn.com.weaver.services.webservices.GetCCWorkflowRequestCount")
    @ResponseWrapper(localName = "getCCWorkflowRequestCountResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "cn.com.weaver.services.webservices.GetCCWorkflowRequestCountResponse")
    public int getCCWorkflowRequestCount(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        ArrayOfString in1);

    /**
     * 
     * @param in0
     * @param in2
     * @param in1
     * @param in4
     * @param in3
     * @return
     *     returns weaver.workflow.webservices.ArrayOfWorkflowRequestInfo
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.getProcessedWorkflowRequestList")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getProcessedWorkflowRequestList", targetNamespace = "webservices.services.weaver.com.cn", className = "cn.com.weaver.services.webservices.GetProcessedWorkflowRequestList")
    @ResponseWrapper(localName = "getProcessedWorkflowRequestListResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "cn.com.weaver.services.webservices.GetProcessedWorkflowRequestListResponse")
    public ArrayOfWorkflowRequestInfo getProcessedWorkflowRequestList(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        int in1,
        @WebParam(name = "in2", targetNamespace = "webservices.services.weaver.com.cn")
        int in2,
        @WebParam(name = "in3", targetNamespace = "webservices.services.weaver.com.cn")
        int in3,
        @WebParam(name = "in4", targetNamespace = "webservices.services.weaver.com.cn")
        ArrayOfString in4);

    /**
     * 
     * @param in0
     * @param in1
     * @return
     *     returns int
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.getAllWorkflowRequestCount")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getAllWorkflowRequestCount", targetNamespace = "webservices.services.weaver.com.cn", className = "cn.com.weaver.services.webservices.GetAllWorkflowRequestCount")
    @ResponseWrapper(localName = "getAllWorkflowRequestCountResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "cn.com.weaver.services.webservices.GetAllWorkflowRequestCountResponse")
    public int getAllWorkflowRequestCount(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        ArrayOfString in1);

    /**
     * 
     * @param in0
     * @param in1
     * @return
     *     returns weaver.workflow.webservices.WorkflowRequestInfo
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.getCreateWorkflowRequestInfo")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getCreateWorkflowRequestInfo", targetNamespace = "webservices.services.weaver.com.cn", className = "cn.com.weaver.services.webservices.GetCreateWorkflowRequestInfo")
    @ResponseWrapper(localName = "getCreateWorkflowRequestInfoResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "cn.com.weaver.services.webservices.GetCreateWorkflowRequestInfoResponse")
    public WorkflowRequestInfo getCreateWorkflowRequestInfo(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        int in1);

    /**
     * 
     * @param in0
     * @param in2
     * @param in1
     * @param in4
     * @param in3
     * @return
     *     returns weaver.workflow.webservices.ArrayOfWorkflowRequestInfo
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.getMyWorkflowRequestList")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getMyWorkflowRequestList", targetNamespace = "webservices.services.weaver.com.cn", className = "cn.com.weaver.services.webservices.GetMyWorkflowRequestList")
    @ResponseWrapper(localName = "getMyWorkflowRequestListResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "cn.com.weaver.services.webservices.GetMyWorkflowRequestListResponse")
    public ArrayOfWorkflowRequestInfo getMyWorkflowRequestList(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        int in1,
        @WebParam(name = "in2", targetNamespace = "webservices.services.weaver.com.cn")
        int in2,
        @WebParam(name = "in3", targetNamespace = "webservices.services.weaver.com.cn")
        int in3,
        @WebParam(name = "in4", targetNamespace = "webservices.services.weaver.com.cn")
        ArrayOfString in4);

    /**
     * 
     * @param in0
     * @param in2
     * @param in1
     * @param in4
     * @param in3
     * @return
     *     returns weaver.workflow.webservices.ArrayOfWorkflowBaseInfo
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.getCreateWorkflowTypeList")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getCreateWorkflowTypeList", targetNamespace = "webservices.services.weaver.com.cn", className = "cn.com.weaver.services.webservices.GetCreateWorkflowTypeList")
    @ResponseWrapper(localName = "getCreateWorkflowTypeListResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "cn.com.weaver.services.webservices.GetCreateWorkflowTypeListResponse")
    public ArrayOfWorkflowBaseInfo getCreateWorkflowTypeList(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        int in1,
        @WebParam(name = "in2", targetNamespace = "webservices.services.weaver.com.cn")
        int in2,
        @WebParam(name = "in3", targetNamespace = "webservices.services.weaver.com.cn")
        int in3,
        @WebParam(name = "in4", targetNamespace = "webservices.services.weaver.com.cn")
        ArrayOfString in4);

    /**
     * 
     * @param in0
     * @param in1
     * @return
     *     returns int
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.getMyWorkflowRequestCount")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getMyWorkflowRequestCount", targetNamespace = "webservices.services.weaver.com.cn", className = "cn.com.weaver.services.webservices.GetMyWorkflowRequestCount")
    @ResponseWrapper(localName = "getMyWorkflowRequestCountResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "cn.com.weaver.services.webservices.GetMyWorkflowRequestCountResponse")
    public int getMyWorkflowRequestCount(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        ArrayOfString in1);

    /**
     * 
     * @param in0
     * @param in1
     * @return
     *     returns cn.com.weaver.services.webservices.ArrayOfString
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.getWorkflowNewFlag")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getWorkflowNewFlag", targetNamespace = "webservices.services.weaver.com.cn", className = "cn.com.weaver.services.webservices.GetWorkflowNewFlag")
    @ResponseWrapper(localName = "getWorkflowNewFlagResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "cn.com.weaver.services.webservices.GetWorkflowNewFlagResponse")
    public ArrayOfString getWorkflowNewFlag(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        ArrayOfString in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        String in1);

    /**
     * 
     * @param in0
     * @param in1
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.writeWorkflowReadFlag")
    @RequestWrapper(localName = "writeWorkflowReadFlag", targetNamespace = "webservices.services.weaver.com.cn", className = "cn.com.weaver.services.webservices.WriteWorkflowReadFlag")
    @ResponseWrapper(localName = "writeWorkflowReadFlagResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "cn.com.weaver.services.webservices.WriteWorkflowReadFlagResponse")
    public void writeWorkflowReadFlag(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        String in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        String in1);

    /**
     * 
     * @param in0
     * @param in1
     * @return
     *     returns int
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.getToDoWorkflowRequestCount")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getToDoWorkflowRequestCount", targetNamespace = "webservices.services.weaver.com.cn", className = "cn.com.weaver.services.webservices.GetToDoWorkflowRequestCount")
    @ResponseWrapper(localName = "getToDoWorkflowRequestCountResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "cn.com.weaver.services.webservices.GetToDoWorkflowRequestCountResponse")
    public int getToDoWorkflowRequestCount(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        ArrayOfString in1);

    /**
     * 
     * @param in0
     * @param in2
     * @param in1
     * @return
     *     returns java.lang.String
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.givingOpinions")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "givingOpinions", targetNamespace = "webservices.services.weaver.com.cn", className = "cn.com.weaver.services.webservices.GivingOpinions")
    @ResponseWrapper(localName = "givingOpinionsResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "cn.com.weaver.services.webservices.GivingOpinionsResponse")
    public String givingOpinions(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        int in1,
        @WebParam(name = "in2", targetNamespace = "webservices.services.weaver.com.cn")
        String in2);

    /**
     * 
     * @param in0
     * @param in1
     * @return
     *     returns int
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.getCreateWorkflowTypeCount")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getCreateWorkflowTypeCount", targetNamespace = "webservices.services.weaver.com.cn", className = "cn.com.weaver.services.webservices.GetCreateWorkflowTypeCount")
    @ResponseWrapper(localName = "getCreateWorkflowTypeCountResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "cn.com.weaver.services.webservices.GetCreateWorkflowTypeCountResponse")
    public int getCreateWorkflowTypeCount(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        ArrayOfString in1);

    /**
     * 
     * @param in0
     * @param in2
     * @param in1
     * @param in4
     * @param in3
     * @return
     *     returns weaver.workflow.webservices.ArrayOfWorkflowRequestLog
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.getWorkflowRequestLogs")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getWorkflowRequestLogs", targetNamespace = "webservices.services.weaver.com.cn", className = "cn.com.weaver.services.webservices.GetWorkflowRequestLogs")
    @ResponseWrapper(localName = "getWorkflowRequestLogsResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "cn.com.weaver.services.webservices.GetWorkflowRequestLogsResponse")
    public ArrayOfWorkflowRequestLog getWorkflowRequestLogs(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        String in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        String in1,
        @WebParam(name = "in2", targetNamespace = "webservices.services.weaver.com.cn")
        int in2,
        @WebParam(name = "in3", targetNamespace = "webservices.services.weaver.com.cn")
        int in3,
        @WebParam(name = "in4", targetNamespace = "webservices.services.weaver.com.cn")
        int in4);

    /**
     * 
     * @param in0
     * @param in1
     * @return
     *     returns boolean
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.deleteRequest")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "deleteRequest", targetNamespace = "webservices.services.weaver.com.cn", className = "cn.com.weaver.services.webservices.DeleteRequest")
    @ResponseWrapper(localName = "deleteRequestResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "cn.com.weaver.services.webservices.DeleteRequestResponse")
    public boolean deleteRequest(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        int in1);

    /**
     * 
     * @param in0
     * @param in2
     * @param in1
     * @param in4
     * @param in3
     * @return
     *     returns weaver.workflow.webservices.ArrayOfWorkflowRequestInfo
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.getCCWorkflowRequestList")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getCCWorkflowRequestList", targetNamespace = "webservices.services.weaver.com.cn", className = "cn.com.weaver.services.webservices.GetCCWorkflowRequestList")
    @ResponseWrapper(localName = "getCCWorkflowRequestListResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "cn.com.weaver.services.webservices.GetCCWorkflowRequestListResponse")
    public ArrayOfWorkflowRequestInfo getCCWorkflowRequestList(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        int in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        int in1,
        @WebParam(name = "in2", targetNamespace = "webservices.services.weaver.com.cn")
        int in2,
        @WebParam(name = "in3", targetNamespace = "webservices.services.weaver.com.cn")
        int in3,
        @WebParam(name = "in4", targetNamespace = "webservices.services.weaver.com.cn")
        ArrayOfString in4);

    /**
     * 
     * @param in0
     * @param in1
     * @return
     *     returns java.lang.String
     */
    @WebMethod(action = "urn:weaver.workflow.webservices.WorkflowService.getUserid")
    @WebResult(name = "out", targetNamespace = "webservices.services.weaver.com.cn")
    @RequestWrapper(localName = "getUserId", targetNamespace = "webservices.services.weaver.com.cn", className = "cn.com.weaver.services.webservices.GetUserId")
    @ResponseWrapper(localName = "getUserIdResponse", targetNamespace = "webservices.services.weaver.com.cn", className = "cn.com.weaver.services.webservices.GetUserIdResponse")
    public String getUserId(
        @WebParam(name = "in0", targetNamespace = "webservices.services.weaver.com.cn")
        String in0,
        @WebParam(name = "in1", targetNamespace = "webservices.services.weaver.com.cn")
        String in1);

}
