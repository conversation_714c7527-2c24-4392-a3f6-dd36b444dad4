
package cn.com.weaver.services.webservices;

import weaver.workflow.webservices.WorkflowRequestInfo;

import javax.xml.bind.annotation.*;


/**
 * <p>anonymous complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="out" type="{http://webservices.workflow.weaver}WorkflowRequestInfo"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "out"
})
@XmlRootElement(name = "getWorkflowRequest4splitResponse")
public class GetWorkflowRequest4SplitResponse {

    @XmlElement(required = true, nillable = true)
    protected WorkflowRequestInfo out;

    /**
     * 获取out属性的值。
     * 
     * @return
     *     possible object is
     *     {@link WorkflowRequestInfo }
     *     
     */
    public WorkflowRequestInfo getOut() {
        return out;
    }

    /**
     * 设置out属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link WorkflowRequestInfo }
     *     
     */
    public void setOut(WorkflowRequestInfo value) {
        this.out = value;
    }

}
