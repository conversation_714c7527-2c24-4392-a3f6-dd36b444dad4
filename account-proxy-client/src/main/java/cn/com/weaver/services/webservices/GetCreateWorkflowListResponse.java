
package cn.com.weaver.services.webservices;

import weaver.workflow.webservices.ArrayOfWorkflowBaseInfo;

import javax.xml.bind.annotation.*;


/**
 * <p>anonymous complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="out" type="{http://webservices.workflow.weaver}ArrayOfWorkflowBaseInfo"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "out"
})
@XmlRootElement(name = "getCreateWorkflowListResponse")
public class GetCreateWorkflowListResponse {

    @XmlElement(required = true, nillable = true)
    protected ArrayOfWorkflowBaseInfo out;

    /**
     * 获取out属性的值。
     * 
     * @return
     *     possible object is
     *     {@link ArrayOfWorkflowBaseInfo }
     *     
     */
    public ArrayOfWorkflowBaseInfo getOut() {
        return out;
    }

    /**
     * 设置out属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link ArrayOfWorkflowBaseInfo }
     *     
     */
    public void setOut(ArrayOfWorkflowBaseInfo value) {
        this.out = value;
    }

}
