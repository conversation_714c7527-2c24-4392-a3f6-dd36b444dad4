<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>account-proxy</artifactId>
        <groupId>com.akucun.account.proxy</groupId>
        <version>1.0.6</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>account-proxy-client</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.akucun.account.proxy</groupId>
            <artifactId>account-proxy-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.spring.boot</groupId>
            <artifactId>dubbo-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.akucun.member.center</groupId>
            <artifactId>member-center-stub</artifactId>
        </dependency>
        <dependency>
            <groupId>com.akucun.account.center</groupId>
            <artifactId>account-center-feign</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.akucun</groupId>
            <artifactId>member-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.akucun.fps</groupId>
            <artifactId>pingan-feign</artifactId>
        </dependency>
        <dependency>
            <groupId>com.akucun.account.proxy</groupId>
            <artifactId>account-proxy-facade-stub</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mengxiang.base</groupId>
            <artifactId>common-log</artifactId>
        </dependency>
        <dependency>
            <groupId>com.akucun.fps</groupId>
            <artifactId>account-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
        <dependency>
            <groupId>com.akucun.pay.gateway.wxv2</groupId>
            <artifactId>pay-gateway-wx-v2-facade-stub</artifactId>
        </dependency>
        <dependency>
            <groupId>com.akucun</groupId>
            <artifactId>akucun-common-security</artifactId>
        </dependency>
        <dependency>
            <artifactId>member-center-audit-facade-stub</artifactId>
            <groupId>com.akucun.member.audit</groupId>
        </dependency>
        <dependency>
            <groupId>com.akucun.account.proxy</groupId>
            <artifactId>account-proxy-dao</artifactId>
        </dependency>

        <dependency>
            <groupId>com.akucun.bond</groupId>
            <artifactId>bond-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>zkclient</artifactId>
                    <groupId>com.github.sgroschupf</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.akucun.bond</groupId>
            <artifactId>bond-feign</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>zkclient</artifactId>
                    <groupId>com.github.sgroschupf</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.akucun.risk.gateway</groupId>
            <artifactId>risk-gateway-facade-stub</artifactId>
            <version>1.0.9</version>
        </dependency>

        <dependency>
            <groupId>com.aikucun</groupId>
            <artifactId>fileupload-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.akucun.pay.gateway.wx</groupId>
            <artifactId>pay-gateway-wx-facade-stub</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
