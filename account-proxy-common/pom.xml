<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>account-proxy</artifactId>
        <groupId>com.akucun.account.proxy</groupId>
        <version>1.0.6</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>account-proxy-common</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.aikucun.common2</groupId>
            <artifactId>common2-base</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aikucun.common2</groupId>
            <artifactId>common2-utils</artifactId>
        </dependency>
        <dependency>
            <groupId>com.akucun.pay.base</groupId>
            <artifactId>pay-base-common</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.aikucun.common2</groupId>
                    <artifactId>common2-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.akucun.fps</groupId>
            <artifactId>fps-common</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.akucun.account.proxy</groupId>
            <artifactId>account-proxy-facade-stub</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mengxiang.base</groupId>
            <artifactId>common-log</artifactId>
        </dependency>
        <!-- 分布式ID生成器 -->
        <dependency>
            <groupId>com.mengxiang.base</groupId>
            <artifactId>common-sequence</artifactId>
        </dependency>
        <dependency>
            <groupId>com.akucun</groupId>
            <artifactId>akucun-common-security</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
