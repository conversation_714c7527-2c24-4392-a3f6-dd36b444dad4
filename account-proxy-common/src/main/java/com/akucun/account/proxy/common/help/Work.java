package com.akucun.account.proxy.common.help;

import java.math.BigDecimal;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class Work {
	
	public static Map<String,BigDecimal> map = new ConcurrentHashMap<>();
	
	static {
		map.put("****************",new BigDecimal(1200));
		map.put("****************",new BigDecimal(1200));
		map.put("****************",new BigDecimal(1200));
		map.put("****************",new BigDecimal(1200));
		map.put("****************",new BigDecimal(1200));
		map.put("****************",new BigDecimal(1200));
		map.put("****************",new BigDecimal(1200));
		map.put("****************",new BigDecimal(1200));
		map.put("****************",new BigDecimal(1200));
		map.put("****************",new BigDecimal(1200));
		map.put("****************",new BigDecimal(1200));
		map.put("****************",new BigDecimal(1200));
		map.put("****************",new BigDecimal(1200));
		map.put("****************",new BigDecimal(1200));
		map.put("****************",new BigDecimal(1200));
		map.put("3562000037871614",new BigDecimal(1200));
		map.put("3562000038127226",new BigDecimal(1200));
		map.put("3562000041386265",new BigDecimal(1200));
		map.put("3562000041519805",new BigDecimal(1200));
		map.put("3562000045188944",new BigDecimal(1200));
		map.put("3562000041393176",new BigDecimal(1200));
		map.put("3562000040538764",new BigDecimal(1200));
		map.put("3562000042168155",new BigDecimal(1200));
		map.put("3562000042176525",new BigDecimal(1199));
		map.put("3562000042412477",new BigDecimal(1199));
		map.put("3562000045165683",new BigDecimal(1199));
		map.put("3562000042864365",new BigDecimal(1194));
		map.put("3562000041562915",new BigDecimal(1191));
		map.put("3562000040554845",new BigDecimal(1191));
		map.put("3562000045176354",new BigDecimal(1190));
		map.put("3562000044974226",new BigDecimal(1190));
		map.put("3562000040545715",new BigDecimal(1187));
		map.put("3562000039627415",new BigDecimal(1187));
		map.put("3562000045184475",new BigDecimal(1185));
		map.put("3562000041404856",new BigDecimal(1183));
		map.put("3562000041362606",new BigDecimal(1180));
		map.put("3562000045171554",new BigDecimal(1176));
		map.put("3562000041182107",new BigDecimal(1174));
		map.put("3562000037964553",new BigDecimal(1174));
		map.put("3562000042169925",new BigDecimal(1174));
		map.put("3562000041364655",new BigDecimal(1174));
		map.put("3562000038300906",new BigDecimal(1174));
		map.put("3562000041559793",new BigDecimal(1174));
		map.put("3562000041606495",new BigDecimal(1174));
		map.put("3562000045194854",new BigDecimal(1173));
		map.put("3562000045147106",new BigDecimal(1172));
		map.put("3562000042849954",new BigDecimal(1171));
		map.put("3562000042550764",new BigDecimal(1170));
		map.put("3562000043651316",new BigDecimal(1167));
		map.put("3562000042878325",new BigDecimal(1165));
		map.put("3562000040984905",new BigDecimal(1165));
		map.put("3562000041071247",new BigDecimal(1165));
		map.put("3562000042714555",new BigDecimal(1165));
		map.put("3562000040778345",new BigDecimal(1165));
		map.put("3562000041422927",new BigDecimal(1161));
		map.put("3562000040523526",new BigDecimal(1160));
		map.put("3562000043986374",new BigDecimal(1160));
		map.put("3562000038055015",new BigDecimal(1160));
		map.put("3562000045213955",new BigDecimal(1159));
		map.put("3562000041510007",new BigDecimal(1155));
		map.put("3562000040866135",new BigDecimal(1155));
		map.put("3562000040519854",new BigDecimal(1152));
		map.put("3562000043744017",new BigDecimal(1150));
		map.put("3562000040690395",new BigDecimal(1148));
		map.put("3562000041655394",new BigDecimal(1148));
		map.put("3562000045039116",new BigDecimal(1142));
		map.put("3562000037449684",new BigDecimal(1140));
		map.put("3562000039597692",new BigDecimal(1139));
		map.put("3562000041420008",new BigDecimal(1139));
		map.put("3562000040523875",new BigDecimal(1139));
		map.put("3562000041424786",new BigDecimal(1139));
		map.put("3562000045046864",new BigDecimal(1139));
		map.put("3562000043703496",new BigDecimal(1138));
		map.put("3562000045189893",new BigDecimal(1136));
		map.put("3562000045153925",new BigDecimal(1134));
		map.put("3562000040533695",new BigDecimal(1134));
		map.put("3562000045191106",new BigDecimal(1134));
		map.put("3562000045154515",new BigDecimal(1134));
		map.put("3562000045135355",new BigDecimal(1131));
		map.put("3562000040680864",new BigDecimal(1131));
		map.put("3562000039319505",new BigDecimal(1130));
		map.put("3562000044956873",new BigDecimal(1130));
		map.put("3562000045116974",new BigDecimal(1126));
		map.put("3562000043670874",new BigDecimal(1124));
		map.put("3562000038204606",new BigDecimal(1122));
		map.put("3562000045164436",new BigDecimal(1121));
		map.put("3562000045169534",new BigDecimal(1121));
		map.put("3562000044040418",new BigDecimal(1120));
		map.put("3562000044973564",new BigDecimal(1120));
		map.put("3562000045146306",new BigDecimal(1120));
		map.put("3562000043976644",new BigDecimal(1120));
		map.put("3562000045161664",new BigDecimal(1120));
		map.put("3562000045161815",new BigDecimal(1120));
		map.put("3562000042862554",new BigDecimal(1120));
		map.put("3562000045162106",new BigDecimal(1120));
		map.put("3562000038467983",new BigDecimal(1117));
		map.put("3562000041260736",new BigDecimal(1117));
		map.put("3562000038271505",new BigDecimal(1114));
		map.put("3562000045190465",new BigDecimal(1114));
		map.put("3562000041342637",new BigDecimal(1113));
		map.put("3562000041060985",new BigDecimal(1113));
		map.put("3562000040995983",new BigDecimal(1113));
		map.put("3562000044983774",new BigDecimal(1111));
		map.put("3562000040563815",new BigDecimal(1110));
		map.put("3562000037669752",new BigDecimal(1110));
		map.put("3562000042914606",new BigDecimal(1107));
		map.put("3562000045154694",new BigDecimal(1106));
		map.put("3562000040547605",new BigDecimal(1104));
		map.put("3562000040527065",new BigDecimal(1104));
		map.put("3562000041568315",new BigDecimal(1104));
		map.put("3562000041432177",new BigDecimal(1100));
		map.put("3562000040529136",new BigDecimal(1100));
		map.put("3562000045179015",new BigDecimal(1100));
		map.put("3562000042130586",new BigDecimal(1100));
		map.put("3562000045156883",new BigDecimal(1100));
		map.put("3562000040554146",new BigDecimal(1100));
		map.put("3562000041194636",new BigDecimal(1100));
		map.put("3562000045156445",new BigDecimal(1100));
		map.put("3562000038390275",new BigDecimal(1100));
		map.put("3562000045157074",new BigDecimal(1100));
		map.put("3562000041493196",new BigDecimal(1100));
		map.put("3562000041414876",new BigDecimal(1100));
		map.put("3562000040549994",new BigDecimal(1100));
		map.put("3562000040769993",new BigDecimal(1100));
		map.put("3562000036862264",new BigDecimal(1100));
		map.put("3562000041456436",new BigDecimal(1100));
		map.put("3562000045156804",new BigDecimal(1100));
		map.put("3562000037240765",new BigDecimal(1100));
		map.put("3562000045152216",new BigDecimal(1100));
		map.put("3562000045156973",new BigDecimal(1100));
		map.put("3562000040544816",new BigDecimal(1100));
		map.put("3562000038382475",new BigDecimal(1100));
		map.put("3562000040522565",new BigDecimal(1100));
		map.put("3562000040545226",new BigDecimal(1100));
		map.put("3562000040551594",new BigDecimal(1100));
		map.put("3562000036735724",new BigDecimal(1100));
		map.put("3562000045136216",new BigDecimal(1100));
		map.put("3562000040523755",new BigDecimal(1100));
		map.put("3562000038282126",new BigDecimal(1100));
		map.put("3562000036862634",new BigDecimal(1100));
		map.put("3562000040770185",new BigDecimal(1099));
		map.put("3562000037828693",new BigDecimal(1095));
		map.put("3562000041544955",new BigDecimal(1094));
		map.put("3562000045137146",new BigDecimal(1093));
		map.put("3562000039370945",new BigDecimal(1084));
		map.put("3562000045192964",new BigDecimal(1080));
		map.put("3562000037005155",new BigDecimal(1080));
		map.put("3562000040521137",new BigDecimal(1078));
		map.put("3562000045181216",new BigDecimal(1072));
		map.put("3562000045033166",new BigDecimal(1072));
		map.put("3562000045185145",new BigDecimal(1071));
		map.put("3562000045192326",new BigDecimal(1071));
		map.put("3562000045181805",new BigDecimal(1071));
		map.put("3562000045199934",new BigDecimal(1071));
		map.put("3562000041556235",new BigDecimal(1070));
		map.put("3562000041183546",new BigDecimal(1069));
		map.put("3562000045189115",new BigDecimal(1067));
		map.put("3562000036115155",new BigDecimal(1067));
		map.put("3562000036115964",new BigDecimal(1064));
		map.put("3562000041491007",new BigDecimal(1061));
		map.put("3562000041386654",new BigDecimal(1061));
		map.put("3562000040784046",new BigDecimal(1061));
		map.put("3562000041534595",new BigDecimal(1061));
		map.put("3562000040518265",new BigDecimal(1061));
		map.put("3562000036114376",new BigDecimal(1060));
		map.put("3562000041393655",new BigDecimal(1060));
		map.put("3562000036728573",new BigDecimal(1060));
		map.put("3562000040550845",new BigDecimal(1060));
		map.put("3562000042850406",new BigDecimal(1059));
		map.put("3562000045196274",new BigDecimal(1059));
		map.put("3562000045137754",new BigDecimal(1056));
		map.put("3562000042850605",new BigDecimal(1055));
		map.put("3562000044995924",new BigDecimal(1053));
		map.put("3562000045170835",new BigDecimal(1052));
		map.put("3562000044288006",new BigDecimal(1052));
		map.put("3562000045167863",new BigDecimal(1052));
		map.put("3562000045161416",new BigDecimal(1050));
		map.put("3562000042736155",new BigDecimal(1050));
		map.put("3562000044509964",new BigDecimal(1050));
		map.put("3562000042870994",new BigDecimal(1050));
		map.put("3562000042843506",new BigDecimal(1050));
		map.put("3562000042855384",new BigDecimal(1050));
		map.put("3562000045156035",new BigDecimal(1050));
		map.put("3562000042854715",new BigDecimal(1050));
		map.put("3562000043499605",new BigDecimal(1045));
		map.put("3562000041495605",new BigDecimal(1044));
		map.put("3562000040523037",new BigDecimal(1044));
		map.put("3562000045139574",new BigDecimal(1044));
		map.put("3562000039185394",new BigDecimal(1044));
		map.put("3562000041571116",new BigDecimal(1044));
		map.put("3562000041605525",new BigDecimal(1044));
		map.put("3562000040530366",new BigDecimal(1044));
		map.put("3562000041248546",new BigDecimal(1044));
		map.put("3562000041403228",new BigDecimal(1043));
		map.put("3562000040545426",new BigDecimal(1043));
		map.put("3562000044981355",new BigDecimal(1041));
		map.put("3562000038288653",new BigDecimal(1040));
		map.put("3562000045160116",new BigDecimal(1040));
		map.put("3562000045195425",new BigDecimal(1040));
		map.put("3562000042955583",new BigDecimal(1040));
		map.put("3562000040805185",new BigDecimal(1040));
		map.put("3562000045205754",new BigDecimal(1039));
		map.put("3562000043703476",new BigDecimal(1037));
		map.put("3562000040519036",new BigDecimal(1035));
		map.put("3562000040532936",new BigDecimal(1035));
		map.put("3562000040760006",new BigDecimal(1035));
		map.put("3562000040633546",new BigDecimal(1035));
		map.put("3562000041407875",new BigDecimal(1035));
		map.put("3562000040964246",new BigDecimal(1035));
		map.put("3562000040536295",new BigDecimal(1035));
		map.put("3562000041685245",new BigDecimal(1035));
		map.put("3562000045009594",new BigDecimal(1035));
		map.put("3562000040554046",new BigDecimal(1035));
		map.put("3562000037981245",new BigDecimal(1034));
		map.put("3562000041456694",new BigDecimal(1034));
		map.put("3562000043091726",new BigDecimal(1032));
		map.put("3562000045138635",new BigDecimal(1030));
		map.put("3562000041511736",new BigDecimal(1030));
		map.put("3562000045153175",new BigDecimal(1028));
		map.put("3562000045154864",new BigDecimal(1027));
		map.put("3562000045175064",new BigDecimal(1027));
		map.put("3562000041679634",new BigDecimal(1026));
		map.put("3562000041626835",new BigDecimal(1026));
		map.put("3562000040541755",new BigDecimal(1026));
		map.put("3562000045192984",new BigDecimal(1025));
		map.put("3562000039665114",new BigDecimal(1025));
		map.put("3562000040542936",new BigDecimal(1020));
		map.put("3562000040550605",new BigDecimal(1017));
		map.put("3562000040682684",new BigDecimal(1017));
		map.put("3562000042854994",new BigDecimal(1016));
		map.put("3562000040534585",new BigDecimal(1016));
		map.put("3562000042001886",new BigDecimal(1013));
		map.put("3562000045181046",new BigDecimal(1010));
		map.put("3562000041290317",new BigDecimal(1009));
		map.put("3562000045197744",new BigDecimal(1009));
		map.put("3562000045221855",new BigDecimal(1008));
		map.put("3562000045205246",new BigDecimal(1008));
		map.put("3562000045191146",new BigDecimal(1008));
		map.put("3562000045181095",new BigDecimal(1008));
		map.put("3562000042914347",new BigDecimal(1005));
		map.put("3562000043406846",new BigDecimal(1001));
		map.put("3562000037234636",new BigDecimal(1000));
		map.put("3562000041392885",new BigDecimal(1000));
		map.put("3562000040541076",new BigDecimal(1000));
		map.put("3562000040535065",new BigDecimal(1000));
		map.put("3562000043424287",new BigDecimal(1000));
		map.put("3562000039366415",new BigDecimal(1000));
		map.put("3562000041357436",new BigDecimal(1000));
		map.put("3562000040539065",new BigDecimal(1000));
		map.put("3562000041417775",new BigDecimal(1000));
		map.put("3562000040522816",new BigDecimal(1000));
		map.put("3562000039158264",new BigDecimal(1000));
		map.put("3562000037840036",new BigDecimal(1000));
		map.put("3562000040791564",new BigDecimal(1000));
		map.put("3562000037669503",new BigDecimal(1000));
		map.put("3562000039243166",new BigDecimal(1000));
		map.put("3562000041449955",new BigDecimal(1000));
		map.put("3562000039839504",new BigDecimal(1000));
		map.put("3562000040545246",new BigDecimal(1000));
		map.put("3562000041677264",new BigDecimal(1000));
		map.put("3562000039413437",new BigDecimal(1000));
		map.put("3562000041420727",new BigDecimal(1000));
		map.put("3562000040713137",new BigDecimal(1000));
		map.put("3562000041024896",new BigDecimal(1000));
		map.put("3562000040527316",new BigDecimal(1000));
		map.put("3562000037923815",new BigDecimal(1000));
		map.put("3562000040597663",new BigDecimal(1000));
		map.put("3562000041468715",new BigDecimal(1000));
		map.put("3562000040528316",new BigDecimal(1000));
		map.put("3562000041464376",new BigDecimal(1000));
		map.put("3562000040546715",new BigDecimal(1000));
		map.put("3562000036113785",new BigDecimal(1000));
		map.put("3562000040670984",new BigDecimal(1000));
		map.put("3562000038240575",new BigDecimal(1000));
		map.put("3562000041078905",new BigDecimal(1000));
		map.put("3562000041476594",new BigDecimal(1000));
		map.put("3562000041186835",new BigDecimal(1000));
		map.put("3562000041718346",new BigDecimal(1000));
		map.put("3562000038439246",new BigDecimal(1000));
		map.put("3562000043623356",new BigDecimal(1000));
		map.put("3562000040517874",new BigDecimal(1000));
		map.put("3562000040857384",new BigDecimal(1000));
		map.put("3562000042067884",new BigDecimal(1000));
		map.put("3562000042012637",new BigDecimal(1000));
		map.put("3562000040733176",new BigDecimal(1000));
		map.put("3562000038432616",new BigDecimal(1000));
		map.put("3562000037388824",new BigDecimal(1000));
		map.put("3562000040520946",new BigDecimal(1000));
		map.put("3562000041526395",new BigDecimal(1000));
		map.put("3562000038124237",new BigDecimal(1000));
		map.put("3562000038318285",new BigDecimal(1000));
		map.put("3562000040523846",new BigDecimal(1000));
		map.put("3562000041503147",new BigDecimal(1000));
		map.put("3562000037954025",new BigDecimal(1000));
		map.put("3562000041414428",new BigDecimal(1000));
		map.put("3562000037670015",new BigDecimal(1000));
		map.put("3562000041245066",new BigDecimal(1000));
		map.put("3562000041332556",new BigDecimal(1000));
		map.put("3562000040552984",new BigDecimal(1000));
		map.put("3562000042170056",new BigDecimal(1000));
		map.put("3562000040519416",new BigDecimal(1000));
		map.put("3562000041117066",new BigDecimal(1000));
		map.put("3562000045132227",new BigDecimal(1000));
		map.put("3562000040526774",new BigDecimal(1000));
		map.put("3562000037370495",new BigDecimal(1000));
		map.put("3562000037687383",new BigDecimal(1000));
		map.put("3562000038582553",new BigDecimal(1000));
		map.put("3562000041135347",new BigDecimal(1000));
		map.put("3562000039687373",new BigDecimal(1000));
		map.put("3562000041706835",new BigDecimal(1000));
		map.put("3562000037671305",new BigDecimal(1000));
		map.put("3562000041952784",new BigDecimal(1000));
		map.put("3562000040931765",new BigDecimal(1000));
		map.put("3562000041535116",new BigDecimal(1000));
		map.put("3562000040915754",new BigDecimal(1000));
		map.put("3562000041048826",new BigDecimal(1000));
		map.put("3562000041392536",new BigDecimal(1000));
		map.put("3562000041613117",new BigDecimal(1000));
		map.put("3562000041507106",new BigDecimal(1000));
		map.put("3562000038050275",new BigDecimal(1000));
		map.put("3562000037687393",new BigDecimal(1000));
		map.put("3562000041117685",new BigDecimal(1000));
		map.put("3562000041535664",new BigDecimal(1000));
		map.put("3562000041623736",new BigDecimal(1000));
		map.put("3562000036727405",new BigDecimal(1000));
		map.put("3562000040760016",new BigDecimal(1000));
		map.put("3562000038070036",new BigDecimal(1000));
		map.put("3562000040530096",new BigDecimal(1000));
		map.put("3562000041413996",new BigDecimal(1000));
		map.put("3562000040527835",new BigDecimal(1000));
		map.put("3562000041229885",new BigDecimal(1000));
		map.put("3562000037980425",new BigDecimal(1000));
		map.put("3562000042169255",new BigDecimal(1000));
		map.put("3562000040524885",new BigDecimal(1000));
		map.put("3562000041728505",new BigDecimal(1000));
		map.put("3562000041686873",new BigDecimal(1000));
		map.put("3562000040946236",new BigDecimal(1000));
		map.put("3562000041646764",new BigDecimal(1000));
		map.put("3562000043802655",new BigDecimal(1000));
		map.put("3562000041387295",new BigDecimal(1000));
		map.put("3562000040536495",new BigDecimal(1000));
		map.put("3562000040545635",new BigDecimal(1000));
		map.put("3562000040533765",new BigDecimal(1000));
		map.put("3562000041705326",new BigDecimal(1000));
		map.put("3562000041371356",new BigDecimal(1000));
		map.put("3562000043417746",new BigDecimal(1000));
		map.put("3562000041662764",new BigDecimal(1000));
		map.put("3562000044983945",new BigDecimal(998));
		map.put("3562000042904636",new BigDecimal(997));
		map.put("3562000044987673",new BigDecimal(994));
		map.put("3562000042856125",new BigDecimal(991));
		map.put("3562000040555215",new BigDecimal(991));
		map.put("3562000041555474",new BigDecimal(991));
		map.put("3562000045177045",new BigDecimal(990));
		map.put("3562000043958804",new BigDecimal(990));
		map.put("3562000042727725",new BigDecimal(990));
		map.put("3562000040537554",new BigDecimal(986));
		map.put("3562000045012795",new BigDecimal(985));
		map.put("3562000038965933",new BigDecimal(983));
		map.put("3562000038314765",new BigDecimal(983));
		map.put("3562000041043666",new BigDecimal(983));
		map.put("3562000040518684",new BigDecimal(983));
		map.put("3562000045052055",new BigDecimal(983));
		map.put("3562000041415975",new BigDecimal(983));
		map.put("3562000041051186",new BigDecimal(982));
		map.put("3562000038284216",new BigDecimal(982));
		map.put("3562000043947055",new BigDecimal(982));
		map.put("3562000042879254",new BigDecimal(980));
		map.put("3562000045162455",new BigDecimal(980));
		map.put("3562000045015216",new BigDecimal(980));
		map.put("3562000042361536",new BigDecimal(980));
		map.put("3562000037512874",new BigDecimal(980));
		map.put("3562000038106605",new BigDecimal(978));
		map.put("3562000045046645",new BigDecimal(976));
		map.put("3562000045030266",new BigDecimal(976));
		map.put("3562000040534875",new BigDecimal(974));
		map.put("3562000040536974",new BigDecimal(974));
		map.put("3562000038122127",new BigDecimal(974));
		map.put("3562000043012287",new BigDecimal(972));
		map.put("3562000043671316",new BigDecimal(970));
		map.put("3562000042850625",new BigDecimal(968));
		map.put("3562000042412277",new BigDecimal(968));
		map.put("3562000044588724",new BigDecimal(967));
		map.put("3562000045161835",new BigDecimal(966));
		map.put("3562000041452695",new BigDecimal(965));
		map.put("3562000045186973",new BigDecimal(964));
		map.put("3562000041719954",new BigDecimal(964));
		map.put("3562000045151395",new BigDecimal(964));
		map.put("3562000042068185",new BigDecimal(961));
		map.put("3562000038039745",new BigDecimal(960));
		map.put("3562000042734655",new BigDecimal(960));
		map.put("3562000045162894",new BigDecimal(960));
		map.put("3562000042955415",new BigDecimal(960));
		map.put("3562000045146185",new BigDecimal(960));
		map.put("3562000044785005",new BigDecimal(960));
		map.put("3562000043510047",new BigDecimal(960));
		map.put("3562000045128165",new BigDecimal(960));
		map.put("3562000036394805",new BigDecimal(960));
		map.put("3562000041028127",new BigDecimal(957));
		map.put("3562000045165025",new BigDecimal(957));
		map.put("3562000045137236",new BigDecimal(957));
		map.put("3562000041407056",new BigDecimal(957));
		map.put("3562000045164825",new BigDecimal(957));
		map.put("3562000040998284",new BigDecimal(956));
		map.put("3562000040554864",new BigDecimal(956));
		map.put("3562000042870935",new BigDecimal(955));
		map.put("3562000044951794",new BigDecimal(955));
		map.put("3562000043519735",new BigDecimal(955));
		map.put("3562000042010057",new BigDecimal(952));
		map.put("3562000045155744",new BigDecimal(951));
		map.put("3562000037672883",new BigDecimal(950));
		map.put("3562000041667683",new BigDecimal(948));
		map.put("3562000040537915",new BigDecimal(948));
		map.put("3562000037203447",new BigDecimal(946));
		map.put("3562000045178963",new BigDecimal(945));
		map.put("3562000045166944",new BigDecimal(945));
		map.put("3562000045198763",new BigDecimal(945));
		map.put("3562000045184984",new BigDecimal(945));
		map.put("3562000042948195",new BigDecimal(944));
		map.put("3562000045164016",new BigDecimal(944));
		map.put("3562000041454107",new BigDecimal(942));
		map.put("3562000040531127",new BigDecimal(940));
		map.put("3562000041335417",new BigDecimal(939));
		map.put("3562000041652745",new BigDecimal(938));
		map.put("3562000040531017",new BigDecimal(935));
		map.put("3562000044553495",new BigDecimal(934));
		map.put("3562000040548664",new BigDecimal(934));
		map.put("3562000039366953",new BigDecimal(932));
		map.put("3562000036727415",new BigDecimal(930));
		map.put("3562000045088553",new BigDecimal(930));
		map.put("3562000037982573",new BigDecimal(930));
		map.put("3562000040918046",new BigDecimal(929));
		map.put("3562000038307236",new BigDecimal(929));
		map.put("3562000045152574",new BigDecimal(928));
		map.put("3562000041323656",new BigDecimal(926));
		map.put("3562000038588772",new BigDecimal(922));
		map.put("3562000045131056",new BigDecimal(922));
		map.put("3562000041354936",new BigDecimal(921));
		map.put("3562000037343516",new BigDecimal(920));
		map.put("3562000038277953",new BigDecimal(920));
		map.put("3562000041103218",new BigDecimal(913));
		map.put("3562000040543456",new BigDecimal(913));
		map.put("3562000040525564",new BigDecimal(913));
		map.put("3562000042463836",new BigDecimal(913));
		map.put("3562000040521746",new BigDecimal(913));
		map.put("3562000041526725",new BigDecimal(913));
		map.put("3562000042923237",new BigDecimal(913));
		map.put("3562000042727994",new BigDecimal(910));
		map.put("3562000045161336",new BigDecimal(910));
		map.put("3562000042914806",new BigDecimal(910));
		map.put("3562000044483565",new BigDecimal(910));
		map.put("3562000037004695",new BigDecimal(910));
		map.put("3562000045162535",new BigDecimal(910));
		map.put("3562000045176963",new BigDecimal(907));
		map.put("3562000042871026",new BigDecimal(906));
		map.put("3562000038204665",new BigDecimal(905));
		map.put("3562000041636664",new BigDecimal(904));
		map.put("3562000042886763",new BigDecimal(904));
		map.put("3562000041583874",new BigDecimal(904));
		map.put("3562000041562735",new BigDecimal(904));
		map.put("3562000045145275",new BigDecimal(903));
		map.put("3562000044970426",new BigDecimal(900));
		map.put("3562000037429036",new BigDecimal(900));
		map.put("3562000040719594",new BigDecimal(900));
		map.put("3562000041417765",new BigDecimal(900));
		map.put("3562000044036166",new BigDecimal(900));
		map.put("3562000040551835",new BigDecimal(900));
		map.put("3562000041567015",new BigDecimal(900));
		map.put("3562000040547664",new BigDecimal(900));
		map.put("3562000037679134",new BigDecimal(900));
		map.put("3562000042456954",new BigDecimal(900));
		map.put("3562000044984705",new BigDecimal(900));
		map.put("3562000040531616",new BigDecimal(900));
		map.put("3562000041506236",new BigDecimal(900));
		map.put("3562000037006754",new BigDecimal(900));
		map.put("3562000041588953",new BigDecimal(900));
		map.put("3562000036862934",new BigDecimal(900));
		map.put("3562000041382726",new BigDecimal(900));
		map.put("3562000043670825",new BigDecimal(900));
		map.put("3562000045013266",new BigDecimal(900));
		map.put("3562000042456935",new BigDecimal(900));
		map.put("3562000042727475",new BigDecimal(900));
		map.put("3562000045163495",new BigDecimal(900));
		map.put("3562000040554065",new BigDecimal(900));
		map.put("3562000045018065",new BigDecimal(899));
		map.put("3562000037203865",new BigDecimal(899));
		map.put("3562000042463916",new BigDecimal(896));
		map.put("3562000041003118",new BigDecimal(895));
		map.put("3562000039449835",new BigDecimal(891));
		map.put("3562000043469825",new BigDecimal(890));
		map.put("3562000040619694",new BigDecimal(890));
		map.put("3562000037971364",new BigDecimal(887));
		map.put("3562000044773255",new BigDecimal(885));
		map.put("3562000045152446",new BigDecimal(882));
		map.put("3562000042870884",new BigDecimal(882));
		map.put("3562000037669224",new BigDecimal(880));
		map.put("3562000045129455",new BigDecimal(880));
		map.put("3562000042785215",new BigDecimal(880));
		map.put("3562000045146055",new BigDecimal(880));
		map.put("3562000038049545",new BigDecimal(878));
		map.put("3562000041117156",new BigDecimal(877));
		map.put("3562000041558035",new BigDecimal(870));
		map.put("3562000036114965",new BigDecimal(870));
		map.put("3562000040554185",new BigDecimal(870));
		map.put("3562000041415936",new BigDecimal(870));
		map.put("3562000041516764",new BigDecimal(870));
		map.put("3562000041091855",new BigDecimal(870));
		map.put("3562000040553406",new BigDecimal(870));
		map.put("3562000042164795",new BigDecimal(870));
		map.put("3562000041576154",new BigDecimal(870));
		map.put("3562000040871654",new BigDecimal(870));
		map.put("3562000041722047",new BigDecimal(870));
		map.put("3562000045176054",new BigDecimal(869));
		map.put("3562000045189544",new BigDecimal(869));
		map.put("3562000045150336",new BigDecimal(869));
		map.put("3562000043721156",new BigDecimal(869));
		map.put("3562000040540147",new BigDecimal(869));
		map.put("3562000045146964",new BigDecimal(868));
		map.put("3562000043962805",new BigDecimal(864));
		map.put("3562000042849355",new BigDecimal(863));
		map.put("3562000042835236",new BigDecimal(863));
		map.put("3562000039273446",new BigDecimal(863));
		map.put("3562000045060545",new BigDecimal(862));
		map.put("3562000041167764",new BigDecimal(861));
		map.put("3562000040661835",new BigDecimal(861));
		map.put("3562000042141177",new BigDecimal(861));
		map.put("3562000041163826",new BigDecimal(861));
		map.put("3562000040809055",new BigDecimal(861));
		map.put("3562000038081455",new BigDecimal(861));
		map.put("3562000040697663",new BigDecimal(860));
		map.put("3562000041259794",new BigDecimal(860));
		map.put("3562000045140256",new BigDecimal(860));
		map.put("3562000045182645",new BigDecimal(857));
		map.put("3562000045200337",new BigDecimal(857));
		map.put("3562000041616126",new BigDecimal(856));
		map.put("3562000040549246",new BigDecimal(856));
		map.put("3562000042137237",new BigDecimal(852));
		map.put("3562000040881684",new BigDecimal(852));
		map.put("3562000042130817",new BigDecimal(852));
		map.put("3562000040979154",new BigDecimal(852));
		map.put("3562000038988093",new BigDecimal(852));
		map.put("3562000042727026",new BigDecimal(850));
		map.put("3562000036115815",new BigDecimal(850));
		map.put("3562000042703427",new BigDecimal(850));
		map.put("3562000043729864",new BigDecimal(848));
		map.put("3562000042954625",new BigDecimal(846));
		map.put("3562000040530156",new BigDecimal(846));
		map.put("3562000045183455",new BigDecimal(844));
		map.put("3562000040761146",new BigDecimal(843));
		map.put("3562000045160584",new BigDecimal(840));
		map.put("3562000036782573",new BigDecimal(840));
		map.put("3562000045160745",new BigDecimal(840));
		map.put("3562000043005266",new BigDecimal(835));
		map.put("3562000040657693",new BigDecimal(834));
		map.put("3562000038492155",new BigDecimal(833));
		map.put("3562000038294625",new BigDecimal(830));
		map.put("3562000045046825",new BigDecimal(830));
		map.put("3562000037983135",new BigDecimal(827));
		map.put("3562000041045875",new BigDecimal(826));
		map.put("3562000042804936",new BigDecimal(826));
		map.put("3562000040527365",new BigDecimal(826));
		map.put("3562000040548246",new BigDecimal(826));
		map.put("3562000042498684",new BigDecimal(822));
		map.put("3562000041464695",new BigDecimal(821));
		map.put("3562000041375016",new BigDecimal(821));
		map.put("3562000045200466",new BigDecimal(819));
		map.put("3562000045219165",new BigDecimal(819));
		map.put("3562000040553075",new BigDecimal(817));
		map.put("3562000040524307",new BigDecimal(817));
		map.put("3562000041591645",new BigDecimal(817));
		map.put("3562000045154984",new BigDecimal(816));
		map.put("3562000037353725",new BigDecimal(814));
		map.put("3562000039609524",new BigDecimal(813));
		map.put("3562000045168844",new BigDecimal(813));
		map.put("3562000041329536",new BigDecimal(813));
		map.put("3562000042714846",new BigDecimal(813));
		map.put("3562000045161475",new BigDecimal(812));
		map.put("3562000042629905",new BigDecimal(810));
		map.put("3562000038335725",new BigDecimal(809));
		map.put("3562000040545794",new BigDecimal(809));
		map.put("3562000042849684",new BigDecimal(805));
		map.put("3562000042923266",new BigDecimal(804));
		map.put("3562000037549824",new BigDecimal(800));
		map.put("3562000045222555",new BigDecimal(800));
		map.put("3562000042958384",new BigDecimal(800));
		map.put("3562000041134308",new BigDecimal(800));
		map.put("3562000041127407",new BigDecimal(800));
		map.put("3562000040532926",new BigDecimal(800));
		map.put("3562000041325137",new BigDecimal(800));
		map.put("3562000042064476",new BigDecimal(800));
		map.put("3562000036862164",new BigDecimal(800));
		map.put("3562000044989734",new BigDecimal(800));
		map.put("3562000045177883",new BigDecimal(800));
		map.put("3562000041370286",new BigDecimal(800));
		map.put("3562000045191136",new BigDecimal(800));
		map.put("3562000042955863",new BigDecimal(800));
		map.put("3562000041752984",new BigDecimal(800));
		map.put("3562000039716045",new BigDecimal(800));
		map.put("3562000041609155",new BigDecimal(800));
		map.put("3562000041502007",new BigDecimal(800));
		map.put("3562000041304497",new BigDecimal(800));
		map.put("3562000045182065",new BigDecimal(800));
		map.put("3562000042151386",new BigDecimal(800));
		map.put("3562000045073964",new BigDecimal(800));
		map.put("3562000041065635",new BigDecimal(800));
		map.put("3562000044977135",new BigDecimal(800));
		map.put("3562000037837125",new BigDecimal(800));
		map.put("3562000041449407",new BigDecimal(800));
		map.put("3562000036745194",new BigDecimal(800));
		map.put("3562000042734626",new BigDecimal(800));
		map.put("3562000045166254",new BigDecimal(800));
		map.put("3562000045178934",new BigDecimal(800));
		map.put("3562000041585264",new BigDecimal(800));
		map.put("3562000041836674",new BigDecimal(800));
		map.put("3562000040527355",new BigDecimal(800));
		map.put("3562000039649804",new BigDecimal(800));
		map.put("3562000040540665",new BigDecimal(800));
		map.put("3562000045154455",new BigDecimal(800));
		map.put("3562000041330057",new BigDecimal(800));
		map.put("3562000045155045",new BigDecimal(800));
		map.put("3562000045177205",new BigDecimal(800));
		map.put("3562000045181006",new BigDecimal(800));
		map.put("3562000037182764",new BigDecimal(800));
		map.put("3562000040555844",new BigDecimal(800));
		map.put("3562000038208326",new BigDecimal(800));
		map.put("3562000043850265",new BigDecimal(800));
		map.put("3562000041047207",new BigDecimal(800));
		map.put("3562000040551825",new BigDecimal(800));
		map.put("3562000040544916",new BigDecimal(800));
		map.put("3562000045194825",new BigDecimal(800));
		map.put("3562000045185983",new BigDecimal(800));
		map.put("3562000040518854",new BigDecimal(800));
		map.put("3562000037686943",new BigDecimal(800));
		map.put("3562000042132287",new BigDecimal(800));
		map.put("3562000038039095",new BigDecimal(800));
		map.put("3562000041704646",new BigDecimal(800));
		map.put("3562000042834117",new BigDecimal(800));
		map.put("3562000037820794",new BigDecimal(800));
		map.put("3562000040552915",new BigDecimal(800));
		map.put("3562000041534546",new BigDecimal(800));
		map.put("3562000039006784",new BigDecimal(800));
		map.put("3562000045190515",new BigDecimal(793));
		map.put("3562000045125495",new BigDecimal(792));
		map.put("3562000041610056",new BigDecimal(791));
		map.put("3562000042158905",new BigDecimal(790));
		map.put("3562000044972625",new BigDecimal(790));
		map.put("3562000038275953",new BigDecimal(790));
		map.put("3562000038023865",new BigDecimal(787));
		map.put("3562000041078206",new BigDecimal(783));
		map.put("3562000041515485",new BigDecimal(783));
		map.put("3562000041351946",new BigDecimal(783));
		map.put("3562000039179604",new BigDecimal(783));
		map.put("3562000041674126",new BigDecimal(783));
		map.put("3562000041108127",new BigDecimal(783));
		map.put("3562000040552185",new BigDecimal(782));
		map.put("3562000041265864",new BigDecimal(782));
		map.put("3562000041497116",new BigDecimal(782));
		map.put("3562000041417137",new BigDecimal(782));
		map.put("3562000045143946",new BigDecimal(781));
		map.put("3562000038162446",new BigDecimal(780));
		map.put("3562000036195524",new BigDecimal(780));
		map.put("3562000037953783",new BigDecimal(779));
		map.put("3562000045160554",new BigDecimal(777));
		map.put("3562000042851255",new BigDecimal(776));
		map.put("3562000038204716",new BigDecimal(775));
		map.put("3562000042067984",new BigDecimal(774));
		map.put("3562000041353037",new BigDecimal(774));
		map.put("3562000041215606",new BigDecimal(774));
		map.put("3562000042168864",new BigDecimal(774));
		map.put("3562000040734675",new BigDecimal(774));
		map.put("3562000041348476",new BigDecimal(774));
		map.put("3562000040987354",new BigDecimal(774));
		map.put("3562000041240348",new BigDecimal(774));
		map.put("3562000040536185",new BigDecimal(773));
		map.put("3562000041704447",new BigDecimal(772));
		map.put("3562000045046694",new BigDecimal(771));
		map.put("3562000045161884",new BigDecimal(770));
		map.put("3562000040523655",new BigDecimal(770));
		map.put("3562000045166604",new BigDecimal(770));
		map.put("3562000042167275",new BigDecimal(770));
		map.put("3562000036784853",new BigDecimal(770));
		map.put("3562000045161206",new BigDecimal(770));
		map.put("3562000045160525",new BigDecimal(770));
		map.put("3562000045160845",new BigDecimal(770));
		map.put("3562000044040527",new BigDecimal(770));
		map.put("3562000044105486",new BigDecimal(767));
		map.put("3562000042638216",new BigDecimal(765));
		map.put("3562000044413008",new BigDecimal(764));
		map.put("3562000042856205",new BigDecimal(764));
		map.put("3562000043976364",new BigDecimal(763));
		map.put("3562000036862614",new BigDecimal(760));
		map.put("3562000044663326",new BigDecimal(759));
		map.put("3562000044804296",new BigDecimal(758));
		map.put("3562000045176274",new BigDecimal(758));
		map.put("3562000045192615",new BigDecimal(756));
		map.put("3562000040518705",new BigDecimal(756));
		map.put("3562000045170255",new BigDecimal(753));
		map.put("3562000041582825",new BigDecimal(752));
		map.put("3562000037358474",new BigDecimal(750));
		map.put("3562000036115036",new BigDecimal(750));
		map.put("3562000045151295",new BigDecimal(750));
		map.put("3562000041843636",new BigDecimal(748));
		map.put("3562000044479195",new BigDecimal(748));
		map.put("3562000037838953",new BigDecimal(746));
		map.put("3562000045108016",new BigDecimal(744));
		map.put("3562000042273237",new BigDecimal(744));
		map.put("3562000045014256",new BigDecimal(744));
		map.put("3562000040995464",new BigDecimal(739));
		map.put("3562000041420267",new BigDecimal(736));
		map.put("3562000041028916",new BigDecimal(735));
		map.put("3562000042371056",new BigDecimal(733));
		map.put("3562000043980426",new BigDecimal(731));
		map.put("3562000037985652",new BigDecimal(730));
		map.put("3562000040521806",new BigDecimal(730));
		map.put("3562000040521665",new BigDecimal(730));
		map.put("3562000041154955",new BigDecimal(730));
		map.put("3562000041320976",new BigDecimal(730));
		map.put("3562000042856115",new BigDecimal(724));
		map.put("3562000041376625",new BigDecimal(722));
		map.put("3562000040550735",new BigDecimal(722));
		map.put("3562000040533437",new BigDecimal(722));
		map.put("3562000041217447",new BigDecimal(722));
		map.put("3562000045165993",new BigDecimal(721));
		map.put("3562000041597064",new BigDecimal(720));
		map.put("3562000043973564",new BigDecimal(720));
		map.put("3562000045155693",new BigDecimal(720));
		map.put("3562000045166245",new BigDecimal(720));
		map.put("3562000043127595",new BigDecimal(720));
		map.put("3562000044688464",new BigDecimal(719));
		map.put("3562000044319337",new BigDecimal(717));
		map.put("3562000040529674",new BigDecimal(713));
		map.put("3562000041476945",new BigDecimal(713));
		map.put("3562000041716664",new BigDecimal(712));
		map.put("3562000040533096",new BigDecimal(712));
		map.put("3562000038363884",new BigDecimal(712));
		map.put("3562000044087594",new BigDecimal(711));
		map.put("3562000037008905",new BigDecimal(710));
		map.put("3562000043950574",new BigDecimal(709));
		map.put("3562000041024586",new BigDecimal(708));
		map.put("3562000045087844",new BigDecimal(708));
		map.put("3562000045160385",new BigDecimal(707));
		map.put("3562000042855205",new BigDecimal(706));
		map.put("3562000045178883",new BigDecimal(706));
		map.put("3562000037981254",new BigDecimal(704));
		map.put("3562000041138317",new BigDecimal(704));
		map.put("3562000045174426",new BigDecimal(704));
		map.put("3562000040526945",new BigDecimal(704));
		map.put("3562000042914595",new BigDecimal(701));
		map.put("3562000038279793",new BigDecimal(700));
		map.put("3562000040546754",new BigDecimal(700));
		map.put("3562000042870864",new BigDecimal(700));
		map.put("3562000044095206",new BigDecimal(700));
		map.put("3562000037964563",new BigDecimal(700));
		map.put("3562000040536036",new BigDecimal(700));
		map.put("3562000042879384",new BigDecimal(700));
		map.put("3562000043560216",new BigDecimal(700));
		map.put("3562000043948346",new BigDecimal(700));
		map.put("3562000045113875",new BigDecimal(700));
		map.put("3562000045054664",new BigDecimal(700));
		map.put("3562000038044176",new BigDecimal(700));
		map.put("3562000045163155",new BigDecimal(700));
		map.put("3562000040540606",new BigDecimal(700));
		map.put("3562000043825574",new BigDecimal(700));
		map.put("3562000038662125",new BigDecimal(700));
		map.put("3562000041588345",new BigDecimal(700));
		map.put("3562000044048855",new BigDecimal(700));
		map.put("3562000043395406",new BigDecimal(700));
		map.put("3562000040547625",new BigDecimal(700));
		map.put("3562000040551336",new BigDecimal(700));
		map.put("3562000045192475",new BigDecimal(700));
		map.put("3562000040901775",new BigDecimal(700));
		map.put("3562000041966544",new BigDecimal(700));
		map.put("3562000045183255",new BigDecimal(700));
		map.put("3562000041421177",new BigDecimal(700));
		map.put("3562000044188964",new BigDecimal(700));
		map.put("3562000040896074",new BigDecimal(700));
		map.put("3562000041952984",new BigDecimal(700));
		map.put("3562000041326946",new BigDecimal(700));
		map.put("3562000037214606",new BigDecimal(700));
		map.put("3562000041319166",new BigDecimal(700));
		map.put("3562000038953834",new BigDecimal(700));
		map.put("3562000041018366",new BigDecimal(700));
		map.put("3562000045233565",new BigDecimal(700));
		map.put("3562000041012428",new BigDecimal(700));
		map.put("3562000038018165",new BigDecimal(700));
		map.put("3562000043820107",new BigDecimal(700));
		map.put("3562000041414308",new BigDecimal(700));
		map.put("3562000040542007",new BigDecimal(700));
		map.put("3562000038305945",new BigDecimal(700));
		map.put("3562000037997762",new BigDecimal(700));
		map.put("3562000044149117",new BigDecimal(700));
		map.put("3562000036920136",new BigDecimal(700));
		map.put("3562000043446516",new BigDecimal(700));
		map.put("3562000044095385",new BigDecimal(700));
		map.put("3562000041210118",new BigDecimal(700));
		map.put("3562000040528355",new BigDecimal(700));
		map.put("3562000041185446",new BigDecimal(700));
		map.put("3562000040994375",new BigDecimal(700));
		map.put("3562000038238945",new BigDecimal(700));
		map.put("3562000039734085",new BigDecimal(700));
		map.put("3562000038104995",new BigDecimal(700));
		map.put("3562000045162625",new BigDecimal(700));
		map.put("3562000039300466",new BigDecimal(700));
		map.put("3562000040595315",new BigDecimal(700));
		map.put("3562000041483806",new BigDecimal(700));
		map.put("3562000040529874",new BigDecimal(700));
		map.put("3562000042908935",new BigDecimal(700));
		map.put("3562000037419625",new BigDecimal(700));
		map.put("3562000039119835",new BigDecimal(700));
		map.put("3562000043047606",new BigDecimal(697));
		map.put("3562000040538774",new BigDecimal(696));
		map.put("3562000043058106",new BigDecimal(696));
		map.put("3562000045138095",new BigDecimal(696));
		map.put("3562000041024837",new BigDecimal(696));
		map.put("3562000040580874",new BigDecimal(696));
		map.put("3562000040408127",new BigDecimal(696));
		map.put("3562000039628154",new BigDecimal(696));
		map.put("3562000040538625",new BigDecimal(696));
		map.put("3562000045170725",new BigDecimal(693));
		map.put("3562000045178953",new BigDecimal(693));
		map.put("3562000044783475",new BigDecimal(693));
		map.put("3562000045197814",new BigDecimal(693));
		map.put("3562000045223726",new BigDecimal(693));
		map.put("3562000045175335",new BigDecimal(693));
		map.put("3562000045181236",new BigDecimal(693));
		map.put("3562000045184725",new BigDecimal(693));
		map.put("3562000045160564",new BigDecimal(693));
		map.put("3562000045173525",new BigDecimal(693));
		map.put("3562000039363165",new BigDecimal(691));
		map.put("3562000041749505",new BigDecimal(691));
		map.put("3562000039247216",new BigDecimal(690));
		map.put("3562000045108026",new BigDecimal(690));
		map.put("3562000045033107",new BigDecimal(690));
		map.put("3562000037338395",new BigDecimal(690));
		map.put("3562000045136725",new BigDecimal(684));
		map.put("3562000043435955",new BigDecimal(682));
		map.put("3562000045000337",new BigDecimal(682));
		map.put("3562000040522447",new BigDecimal(680));
		map.put("3562000040524347",new BigDecimal(680));
		map.put("3562000042637705",new BigDecimal(680));
		map.put("3562000045079035",new BigDecimal(678));
		map.put("3562000040980255",new BigDecimal(678));
		map.put("3562000041598445",new BigDecimal(678));
		map.put("3562000040761705",new BigDecimal(678));
		map.put("3562000041750894",new BigDecimal(678));
		map.put("3562000038496653",new BigDecimal(678));
		map.put("3562000038053864",new BigDecimal(678));
		map.put("3562000045046446",new BigDecimal(675));
		map.put("3562000045156225",new BigDecimal(672));
		map.put("3562000045046925",new BigDecimal(670));
		map.put("3562000037669652",new BigDecimal(670));
		map.put("3562000037953844",new BigDecimal(670));
		map.put("3562000042850006",new BigDecimal(669));
		map.put("3562000044105855",new BigDecimal(669));
		map.put("3562000041753185",new BigDecimal(669));
		map.put("3562000038316605",new BigDecimal(665));
		map.put("3562000043580654",new BigDecimal(664));
		map.put("3562000041072417",new BigDecimal(661));
		map.put("3562000040543595",new BigDecimal(660));
		map.put("3562000045175783",new BigDecimal(660));
		map.put("3562000045087504",new BigDecimal(654));
		map.put("3562000040538854",new BigDecimal(652));
		map.put("3562000040990674",new BigDecimal(652));
		map.put("3562000041519615",new BigDecimal(652));
		map.put("3562000037725435",new BigDecimal(652));
		map.put("3562000039376934",new BigDecimal(652));
		map.put("3562000040889744",new BigDecimal(652));
		map.put("3562000041025726",new BigDecimal(652));
		map.put("3562000040692545",new BigDecimal(652));
		map.put("3562000041036386",new BigDecimal(652));
		map.put("3562000043670694",new BigDecimal(651));
		map.put("3562000040555724",new BigDecimal(651));
		map.put("3562000036716445",new BigDecimal(650));
		map.put("3562000037670035",new BigDecimal(650));
		map.put("3562000043778534",new BigDecimal(648));
		map.put("3562000043550346",new BigDecimal(648));
		map.put("3562000043609884",new BigDecimal(647));
		map.put("3562000044002318",new BigDecimal(647));
		map.put("3562000040549265",new BigDecimal(643));
		map.put("3562000041021057",new BigDecimal(643));
		map.put("3562000040555294",new BigDecimal(643));
		map.put("3562000041453816",new BigDecimal(643));
		map.put("3562000042166116",new BigDecimal(643));
		map.put("3562000040556074",new BigDecimal(643));
		map.put("3562000040545705",new BigDecimal(643));
		map.put("3562000044875983",new BigDecimal(642));
		map.put("3562000045118825",new BigDecimal(642));
		map.put("3562000042959814",new BigDecimal(641));
		map.put("3562000038548084",new BigDecimal(640));
		map.put("3562000037118535",new BigDecimal(640));
		map.put("3562000037693135",new BigDecimal(640));
		map.put("3562000037385015",new BigDecimal(640));
		map.put("3562000041567663",new BigDecimal(639));
		map.put("3562000045155305",new BigDecimal(638));
		map.put("3562000045024347",new BigDecimal(636));
		map.put("3562000041233438",new BigDecimal(635));
		map.put("3562000043830565",new BigDecimal(634));
		map.put("3562000044095794",new BigDecimal(630));
		map.put("3562000045161705",new BigDecimal(630));
		map.put("3562000040531726",new BigDecimal(630));
		map.put("3562000045210526",new BigDecimal(630));
		map.put("3562000045176724",new BigDecimal(630));
		map.put("3562000043091685",new BigDecimal(630));
		map.put("3562000040537316",new BigDecimal(628));
		map.put("3562000042729016",new BigDecimal(628));
		map.put("3562000040745236",new BigDecimal(626));
		map.put("3562000040917226",new BigDecimal(626));
		map.put("3562000040555184",new BigDecimal(626));
		map.put("3562000040529835",new BigDecimal(626));
		map.put("3562000042835446",new BigDecimal(626));
		map.put("3562000041561365",new BigDecimal(625));
		map.put("3562000044890505",new BigDecimal(625));
		map.put("3562000040857194",new BigDecimal(624));
		map.put("3562000043743946",new BigDecimal(623));
		map.put("3562000037235206",new BigDecimal(622));
		map.put("3562000045047825",new BigDecimal(622));
		map.put("3562000042841437",new BigDecimal(621));
		map.put("3562000040527195",new BigDecimal(620));
		map.put("3562000041029096",new BigDecimal(620));
		map.put("3562000044990375",new BigDecimal(619));
		map.put("3562000040552006",new BigDecimal(617));
		map.put("3562000041455615",new BigDecimal(617));
		map.put("3562000044095326",new BigDecimal(612));
		map.put("3562000041127196",new BigDecimal(609));
		map.put("3562000041336565",new BigDecimal(609));
		map.put("3562000041032837",new BigDecimal(609));
		map.put("3562000040669315",new BigDecimal(609));
		map.put("3562000042130896",new BigDecimal(609));
		map.put("3562000040534396",new BigDecimal(609));
		map.put("3562000041303527",new BigDecimal(609));
		map.put("3562000041605346",new BigDecimal(609));
		map.put("3562000040947754",new BigDecimal(608));
		map.put("3562000042879115",new BigDecimal(608));
		map.put("3562000043642166",new BigDecimal(607));
		map.put("3562000045153554",new BigDecimal(607));
		map.put("3562000042805745",new BigDecimal(606));
		map.put("3562000045039465",new BigDecimal(604));
		map.put("3562000036859792",new BigDecimal(600));
		map.put("3562000038548834",new BigDecimal(600));
		map.put("3562000042778663",new BigDecimal(600));
		map.put("3562000042067635",new BigDecimal(600));
		map.put("3562000041846554",new BigDecimal(600));
		map.put("3562000041016636",new BigDecimal(600));
		map.put("3562000041119237",new BigDecimal(600));
		map.put("3562000045048745",new BigDecimal(600));
		map.put("3562000045177035",new BigDecimal(600));
		map.put("3562000040488036",new BigDecimal(600));
		map.put("3562000043017846",new BigDecimal(600));
		map.put("3562000040542965",new BigDecimal(600));
		map.put("3562000041585074",new BigDecimal(600));
		map.put("3562000036744306",new BigDecimal(600));
		map.put("3562000041156185",new BigDecimal(600));
		map.put("3562000045007075",new BigDecimal(600));
		map.put("3562000037368873",new BigDecimal(600));
		map.put("3562000038682454",new BigDecimal(600));
		map.put("3562000045186174",new BigDecimal(600));
		map.put("3562000036855723",new BigDecimal(600));
		map.put("3562000041604616",new BigDecimal(600));
		map.put("3562000043565744",new BigDecimal(600));
		map.put("3562000038966093",new BigDecimal(600));
		map.put("3562000045177904",new BigDecimal(600));
		map.put("3562000045139055",new BigDecimal(600));
		map.put("3562000036677063",new BigDecimal(600));
		map.put("3562000042251846",new BigDecimal(600));
		map.put("3562000041108506",new BigDecimal(600));
		map.put("3562000039725514",new BigDecimal(600));
		map.put("3562000045212565",new BigDecimal(600));
		map.put("3562000040657883",new BigDecimal(600));
		map.put("3562000040935416",new BigDecimal(600));
		map.put("3562000041216247",new BigDecimal(600));
		map.put("3562000043060695",new BigDecimal(600));
		map.put("3562000045158045",new BigDecimal(600));
		map.put("3562000040681416",new BigDecimal(600));
		map.put("3562000041681784",new BigDecimal(600));
		map.put("3562000041020617",new BigDecimal(600));
		map.put("3562000045158194",new BigDecimal(600));
		map.put("3562000041470875",new BigDecimal(600));
		map.put("3562000038513915",new BigDecimal(600));
		map.put("3562000045186194",new BigDecimal(600));
		map.put("3562000045202227",new BigDecimal(600));
		map.put("3562000038293935",new BigDecimal(600));
		map.put("3562000038112147",new BigDecimal(600));
		map.put("3562000040554455",new BigDecimal(600));
		map.put("3562000041653994",new BigDecimal(600));
		map.put("3562000041690964",new BigDecimal(600));
		map.put("3562000036782544",new BigDecimal(600));
		map.put("3562000041680406",new BigDecimal(600));
		map.put("3562000045156415",new BigDecimal(600));
		map.put("3562000041149526",new BigDecimal(600));
		map.put("3562000041028086",new BigDecimal(600));
		map.put("3562000045015654",new BigDecimal(600));
		map.put("3562000036920185",new BigDecimal(600));
		map.put("3562000040533466",new BigDecimal(600));
		map.put("3562000043978644",new BigDecimal(596));
		map.put("3562000044255725",new BigDecimal(596));
		map.put("3562000044095805",new BigDecimal(596));
		map.put("3562000045161006",new BigDecimal(595));
		map.put("3562000045048375",new BigDecimal(595));
		map.put("3562000045005016",new BigDecimal(593));
		map.put("3562000040544906",new BigDecimal(592));
		map.put("3562000040547515",new BigDecimal(591));
		map.put("3562000040546346",new BigDecimal(591));
		map.put("3562000040547584",new BigDecimal(591));
		map.put("3562000038306994",new BigDecimal(591));
		map.put("3562000040525355",new BigDecimal(591));
		map.put("3562000038194126",new BigDecimal(591));
		map.put("3562000045009894",new BigDecimal(586));
		map.put("3562000045047416",new BigDecimal(586));
		map.put("3562000040539925",new BigDecimal(582));
		map.put("3562000040127746",new BigDecimal(579));
		map.put("3562000041434367",new BigDecimal(578));
		map.put("3562000042844546",new BigDecimal(578));
		map.put("3562000038204736",new BigDecimal(575));
		map.put("3562000040797064",new BigDecimal(574));
		map.put("3562000040547715",new BigDecimal(574));
		map.put("3562000040540056",new BigDecimal(573));
		map.put("3562000045178054",new BigDecimal(573));
		map.put("3562000044996514",new BigDecimal(571));
		map.put("3562000037332127",new BigDecimal(570));
		map.put("3562000042647605",new BigDecimal(569));
		map.put("3562000045178944",new BigDecimal(567));
		map.put("3562000045190136",new BigDecimal(567));
		map.put("3562000040547306",new BigDecimal(565));
		map.put("3562000043619285",new BigDecimal(565));
		map.put("3562000040968145",new BigDecimal(565));
		map.put("3562000041657045",new BigDecimal(565));
		map.put("3562000044761684",new BigDecimal(563));
		map.put("3562000038006935",new BigDecimal(561));
		map.put("3562000045166734",new BigDecimal(560));
		map.put("3562000042878315",new BigDecimal(560));
		map.put("3562000037310156",new BigDecimal(560));
		map.put("3562000037021137",new BigDecimal(560));
		map.put("3562000040531466",new BigDecimal(560));
		map.put("3562000045160406",new BigDecimal(560));
		map.put("3562000045160475",new BigDecimal(560));
		map.put("3562000042855005",new BigDecimal(560));
		map.put("3562000044282755",new BigDecimal(560));
		map.put("3562000038294815",new BigDecimal(560));
		map.put("3562000042923307",new BigDecimal(560));
		map.put("3562000043591825",new BigDecimal(560));
		map.put("3562000045022337",new BigDecimal(560));
		map.put("3562000040560515",new BigDecimal(560));
		map.put("3562000042055954",new BigDecimal(560));
		map.put("3562000038277893",new BigDecimal(560));
		map.put("3562000045161735",new BigDecimal(560));
		map.put("3562000045157563",new BigDecimal(560));
		map.put("3562000045166345",new BigDecimal(560));
		map.put("3562000045013007",new BigDecimal(559));
		map.put("3562000040530286",new BigDecimal(556));
		map.put("3562000038600784",new BigDecimal(556));
		map.put("3562000043010497",new BigDecimal(555));
		map.put("3562000045046884",new BigDecimal(555));
		map.put("3562000040950635",new BigDecimal(552));
		map.put("3562000040523595",new BigDecimal(550));
		map.put("3562000041744775",new BigDecimal(550));
		map.put("3562000042402966",new BigDecimal(550));
		map.put("3562000040522056",new BigDecimal(548));
		map.put("3562000040650984",new BigDecimal(548));
		map.put("3562000040523096",new BigDecimal(547));
		map.put("3562000041160755",new BigDecimal(547));
		map.put("3562000045143526",new BigDecimal(544));
		map.put("3562000040530606",new BigDecimal(543));
		map.put("3562000045087593",new BigDecimal(540));
		map.put("3562000037384564",new BigDecimal(540));
		map.put("3562000041575164",new BigDecimal(539));
		map.put("3562000037960154",new BigDecimal(539));
		map.put("3562000042870954",new BigDecimal(535));
		map.put("3562000042849984",new BigDecimal(533));
		map.put("3562000045082085",new BigDecimal(532));
		map.put("3562000043467155",new BigDecimal(531));
		map.put("3562000040523575",new BigDecimal(530));
		map.put("3562000044076026",new BigDecimal(529));
		map.put("3562000037200595",new BigDecimal(529));
		map.put("3562000045073735",new BigDecimal(525));
		map.put("3562000037837135",new BigDecimal(524));
		map.put("3562000043670674",new BigDecimal(523));
		map.put("3562000041286185",new BigDecimal(522));
		map.put("3562000041085964",new BigDecimal(522));
		map.put("3562000041475136",new BigDecimal(522));
		map.put("3562000043888534",new BigDecimal(522));
		map.put("3562000037654315",new BigDecimal(522));
		map.put("3562000040961935",new BigDecimal(522));
		map.put("3562000045031076",new BigDecimal(522));
		map.put("3562000041513906",new BigDecimal(522));
		map.put("3562000040519684",new BigDecimal(522));
		map.put("3562000037358973",new BigDecimal(522));
		map.put("3562000041498065",new BigDecimal(522));
		map.put("3562000040792426",new BigDecimal(521));
		map.put("3562000041188525",new BigDecimal(521));
		map.put("3562000038053075",new BigDecimal(520));
		map.put("3562000037309065",new BigDecimal(520));
		map.put("3562000037252725",new BigDecimal(520));
		map.put("3562000037904574",new BigDecimal(520));
		map.put("3562000040552355",new BigDecimal(520));
		map.put("3562000040986074",new BigDecimal(520));
		map.put("3562000037494794",new BigDecimal(520));
		map.put("3562000045090774",new BigDecimal(520));
		map.put("3562000043499594",new BigDecimal(518));
		map.put("3562000039290705",new BigDecimal(518));
		map.put("3562000044984465",new BigDecimal(516));
		map.put("3562000042456155",new BigDecimal(516));
		map.put("3562000043029665",new BigDecimal(513));
		map.put("3562000037953804",new BigDecimal(513));
		map.put("3562000040555793",new BigDecimal(513));
		map.put("3562000041230397",new BigDecimal(513));
		map.put("3562000040550265",new BigDecimal(513));
		map.put("3562000041197116",new BigDecimal(513));
		map.put("3562000040528236",new BigDecimal(513));
		map.put("3562000040996054",new BigDecimal(513));
		map.put("3562000040524836",new BigDecimal(513));
		map.put("3562000038089335",new BigDecimal(513));
		map.put("3562000041052217",new BigDecimal(513));
		map.put("3562000040528584",new BigDecimal(513));
		map.put("3562000040541196",new BigDecimal(512));
		map.put("3562000041759853",new BigDecimal(512));
		map.put("3562000040524695",new BigDecimal(511));
		map.put("3562000037369973",new BigDecimal(510));
		map.put("3562000045148945",new BigDecimal(510));
		map.put("3562000045131526",new BigDecimal(510));
		map.put("3562000037221865",new BigDecimal(508));
		map.put("3562000040520906",new BigDecimal(508));
		map.put("3562000045099245",new BigDecimal(506));
		map.put("3562000045188804",new BigDecimal(504));
		map.put("3562000045169544",new BigDecimal(504));
		map.put("3562000040528265",new BigDecimal(504));
		map.put("3562000042714716",new BigDecimal(504));
		map.put("3562000045172515",new BigDecimal(504));
		map.put("3562000045176714",new BigDecimal(504));
		map.put("3562000045069245",new BigDecimal(504));
		map.put("3562000041708854",new BigDecimal(504));
		map.put("3562000045177973",new BigDecimal(504));
		map.put("3562000045207994",new BigDecimal(504));
		map.put("3562000041123308",new BigDecimal(500));
		map.put("3562000041511196",new BigDecimal(500));
		map.put("3562000040865494",new BigDecimal(500));
		map.put("3562000038262525",new BigDecimal(500));
		map.put("3562000045190645",new BigDecimal(500));
		map.put("3562000041433138",new BigDecimal(500));
		map.put("3562000040986235",new BigDecimal(500));
		map.put("3562000036115255",new BigDecimal(500));
		map.put("3562000040534207",new BigDecimal(500));
		map.put("3562000036723095",new BigDecimal(500));
		map.put("3562000039692693",new BigDecimal(500));
		map.put("3562000041117855",new BigDecimal(500));
		map.put("3562000038049036",new BigDecimal(500));
		map.put("3562000041667753",new BigDecimal(500));
		map.put("3562000038710854",new BigDecimal(500));
		map.put("3562000037671464",new BigDecimal(500));
		map.put("3562000045221716",new BigDecimal(500));
		map.put("3562000039677193",new BigDecimal(500));
		map.put("3562000037669173",new BigDecimal(500));
		map.put("3562000041526285",new BigDecimal(500));
		map.put("3562000040545974",new BigDecimal(500));
		map.put("3562000038549563",new BigDecimal(500));
		map.put("3562000038261825",new BigDecimal(500));
		map.put("3562000038053085",new BigDecimal(500));
		map.put("3562000043387905",new BigDecimal(500));
		map.put("3562000038598662",new BigDecimal(500));
		map.put("3562000041430338",new BigDecimal(500));
		map.put("3562000040745165",new BigDecimal(500));
		map.put("3562000037308805",new BigDecimal(500));
		map.put("3562000039452674",new BigDecimal(500));
		map.put("3562000041750635",new BigDecimal(500));
		map.put("3562000043828306",new BigDecimal(500));
		map.put("3562000042454736",new BigDecimal(500));
		map.put("3562000041114817",new BigDecimal(500));
		map.put("3562000040528545",new BigDecimal(500));
		map.put("3562000043919326",new BigDecimal(500));
		map.put("3562000044973645",new BigDecimal(500));
		map.put("3562000041616195",new BigDecimal(500));
		map.put("3562000042139447",new BigDecimal(500));
		map.put("3562000042170486",new BigDecimal(500));
		map.put("3562000041448565",new BigDecimal(500));
		map.put("3562000040521337",new BigDecimal(500));
		map.put("3562000038242636",new BigDecimal(500));
		map.put("3562000040938155",new BigDecimal(500));
		map.put("3562000041464166",new BigDecimal(500));
		map.put("3562000041685284",new BigDecimal(500));
		map.put("3562000040557683",new BigDecimal(500));
		map.put("3562000036585453",new BigDecimal(500));
		map.put("3562000038888144",new BigDecimal(500));
		map.put("3562000040544437",new BigDecimal(500));
		map.put("3562000038600465",new BigDecimal(500));
		map.put("3562000037672693",new BigDecimal(500));
		map.put("3562000041349906",new BigDecimal(500));
		map.put("3562000040948106",new BigDecimal(500));
		map.put("3562000036127984",new BigDecimal(500));
		map.put("3562000042322487",new BigDecimal(500));
		map.put("3562000038479883",new BigDecimal(500));
		map.put("3562000038043037",new BigDecimal(500));
		map.put("3562000041759734",new BigDecimal(500));
		map.put("3562000043772645",new BigDecimal(500));
		map.put("3562000041281785",new BigDecimal(500));
		map.put("3562000038139964",new BigDecimal(500));
		map.put("3562000038530275",new BigDecimal(500));
		map.put("3562000039626983",new BigDecimal(500));
		map.put("3562000037578952",new BigDecimal(500));
		map.put("3562000040560625",new BigDecimal(500));
		map.put("3562000041467446",new BigDecimal(500));
		map.put("3562000041050546",new BigDecimal(500));
		map.put("3562000038122895",new BigDecimal(500));
		map.put("3562000041525994",new BigDecimal(500));
		map.put("3562000038409185",new BigDecimal(500));
		map.put("3562000038549415",new BigDecimal(500));
		map.put("3562000037359445",new BigDecimal(500));
		map.put("3562000039488814",new BigDecimal(500));
		map.put("3562000040528446",new BigDecimal(500));
		map.put("3562000044775245",new BigDecimal(500));
		map.put("3562000038382625",new BigDecimal(500));
		map.put("3562000040528815",new BigDecimal(500));
		map.put("3562000036531055",new BigDecimal(500));
		map.put("3562000038281654",new BigDecimal(500));
		map.put("3562000040517954",new BigDecimal(500));
		map.put("3562000041378784",new BigDecimal(500));
		map.put("3562000038302655",new BigDecimal(500));
		map.put("3562000038512815",new BigDecimal(500));
		map.put("3562000041027616",new BigDecimal(500));
		map.put("3562000038123836",new BigDecimal(500));
		map.put("3562000041549146",new BigDecimal(500));
		map.put("3562000037669603",new BigDecimal(500));
		map.put("3562000041459255",new BigDecimal(500));
		map.put("3562000035900964",new BigDecimal(500));
		map.put("3562000040975663",new BigDecimal(500));
		map.put("3562000045152564",new BigDecimal(500));
		map.put("3562000045124266",new BigDecimal(500));
		map.put("3562000037370226",new BigDecimal(500));
		map.put("3562000040558135",new BigDecimal(500));
		map.put("3562000037379235",new BigDecimal(499));
		map.put("3562000043374955",new BigDecimal(496));
		map.put("3562000045047854",new BigDecimal(496));
		map.put("3562000041267436",new BigDecimal(495));
		map.put("3562000041037626",new BigDecimal(495));
		map.put("3562000043856844",new BigDecimal(493));
		map.put("3562000045113906",new BigDecimal(493));
		map.put("3562000037194075",new BigDecimal(492));
		map.put("3562000045200137",new BigDecimal(491));
		map.put("3562000045154574",new BigDecimal(490));
		map.put("3562000045153735",new BigDecimal(490));
		map.put("3562000040545185",new BigDecimal(490));
		map.put("3562000045048365",new BigDecimal(490));
		map.put("3562000044344077",new BigDecimal(489));
		map.put("3562000042412138",new BigDecimal(488));
		map.put("3562000040536126",new BigDecimal(487));
		map.put("3562000041039027",new BigDecimal(487));
		map.put("3562000045130806",new BigDecimal(486));
		map.put("3562000040544536",new BigDecimal(486));
		map.put("3562000042804147",new BigDecimal(484));
		map.put("3562000040530337",new BigDecimal(483));
		map.put("3562000037203906",new BigDecimal(480));
		map.put("3562000036411946",new BigDecimal(480));
		map.put("3562000045162784",new BigDecimal(480));
		map.put("3562000045124147",new BigDecimal(480));
		map.put("3562000045167425",new BigDecimal(480));
		map.put("3562000041633127",new BigDecimal(480));
		map.put("3562000045126545",new BigDecimal(480));
		map.put("3562000045143486",new BigDecimal(480));
		map.put("3562000037302086",new BigDecimal(480));
		map.put("3562000041546326",new BigDecimal(478));
		map.put("3562000045187325",new BigDecimal(478));
		map.put("3562000045014286",new BigDecimal(478));
		map.put("3562000044167815",new BigDecimal(478));
		map.put("3562000042853874",new BigDecimal(477));
		map.put("3562000042850306",new BigDecimal(474));
		map.put("3562000045194915",new BigDecimal(474));
		map.put("3562000045218365",new BigDecimal(474));
		map.put("3562000043394775",new BigDecimal(471));
		map.put("3562000042844516",new BigDecimal(467));
		map.put("3562000037062954",new BigDecimal(467));
		map.put("3562000045013736",new BigDecimal(466));
		map.put("3562000041285216",new BigDecimal(464));
		map.put("3562000045051095",new BigDecimal(464));
		map.put("3562000045039375",new BigDecimal(464));
		map.put("3562000043930086",new BigDecimal(461));
		map.put("3562000042794574",new BigDecimal(461));
		map.put("3562000044600946",new BigDecimal(460));
		map.put("3562000043780226",new BigDecimal(460));
		map.put("3562000045154106",new BigDecimal(460));
		map.put("3562000041756435",new BigDecimal(460));
		map.put("3562000044245685",new BigDecimal(459));
		map.put("3562000043643695",new BigDecimal(458));
		map.put("3562000044982236",new BigDecimal(455));
		map.put("3562000045046436",new BigDecimal(454));
		map.put("3562000045189553",new BigDecimal(454));
		map.put("3562000041149855",new BigDecimal(452));
		map.put("3562000045155125",new BigDecimal(452));
		map.put("3562000041379564",new BigDecimal(452));
		map.put("3562000039193615",new BigDecimal(452));
		map.put("3562000041165635",new BigDecimal(452));
		map.put("3562000042849854",new BigDecimal(450));
		map.put("3562000045082426",new BigDecimal(450));
		map.put("3562000038584824",new BigDecimal(450));
		map.put("3562000040545815",new BigDecimal(450));
		map.put("3562000039076315",new BigDecimal(450));
		map.put("3562000044576883",new BigDecimal(450));
		map.put("3562000044723047",new BigDecimal(450));
		map.put("3562000038241926",new BigDecimal(445));
		map.put("3562000045167834",new BigDecimal(445));
		map.put("3562000045085274",new BigDecimal(443));
		map.put("3562000040522695",new BigDecimal(443));
		map.put("3562000040725805",new BigDecimal(443));
		map.put("3562000038404466",new BigDecimal(442));
		map.put("3562000045023726",new BigDecimal(441));
		map.put("3562000045171265",new BigDecimal(441));
		map.put("3562000045176074",new BigDecimal(441));
		map.put("3562000045152385",new BigDecimal(441));
		map.put("3562000043127575",new BigDecimal(440));
		map.put("3562000040538155",new BigDecimal(440));
		map.put("3562000038283845",new BigDecimal(440));
		map.put("3562000045089245",new BigDecimal(440));
		map.put("3562000045159064",new BigDecimal(440));
		map.put("3562000042880385",new BigDecimal(438));
		map.put("3562000044976504",new BigDecimal(438));
		map.put("3562000042870945",new BigDecimal(436));
		map.put("3562000042397265",new BigDecimal(435));
		map.put("3562000040531047",new BigDecimal(435));
		map.put("3562000044110218",new BigDecimal(435));
		map.put("3562000041589614",new BigDecimal(435));
		map.put("3562000037875992",new BigDecimal(435));
		map.put("3562000040540396",new BigDecimal(435));
		map.put("3562000041382017",new BigDecimal(435));
		map.put("3562000043787215",new BigDecimal(435));
		map.put("3562000045139784",new BigDecimal(435));
		map.put("3562000041635905",new BigDecimal(435));
		map.put("3562000042792984",new BigDecimal(433));
		map.put("3562000045154594",new BigDecimal(432));
		map.put("3562000040555105",new BigDecimal(430));
		map.put("3562000043890754",new BigDecimal(430));
		map.put("3562000043497864",new BigDecimal(430));
		map.put("3562000043891505",new BigDecimal(430));
		map.put("3562000037203875",new BigDecimal(430));
		map.put("3562000041404348",new BigDecimal(430));
		map.put("3562000040525764",new BigDecimal(430));
		map.put("3562000037368863",new BigDecimal(430));
		map.put("3562000045137854",new BigDecimal(429));
		map.put("3562000041247655",new BigDecimal(426));
		map.put("3562000045020595",new BigDecimal(426));
		map.put("3562000045138715",new BigDecimal(425));
		map.put("3562000045161346",new BigDecimal(421));
		map.put("3562000037215085",new BigDecimal(420));
		map.put("3562000036999183",new BigDecimal(420));
		map.put("3562000043061066",new BigDecimal(420));
		map.put("3562000044057805",new BigDecimal(420));
		map.put("3562000045162505",new BigDecimal(420));
		map.put("3562000044097136",new BigDecimal(420));
		map.put("3562000045160864",new BigDecimal(420));
		map.put("3562000045153974",new BigDecimal(420));
		map.put("3562000045013366",new BigDecimal(420));
		map.put("3562000037425545",new BigDecimal(418));
		map.put("3562000038409605",new BigDecimal(417));
		map.put("3562000040555464",new BigDecimal(417));
		map.put("3562000040528055",new BigDecimal(417));
		map.put("3562000045086573",new BigDecimal(416));
		map.put("3562000042554964",new BigDecimal(414));
		map.put("3562000045087394",new BigDecimal(414));
		map.put("3562000042961505",new BigDecimal(412));
		map.put("3562000044972095",new BigDecimal(412));
		map.put("3562000039879962",new BigDecimal(411));
		map.put("3562000044111907",new BigDecimal(411));
		map.put("3562000036644075",new BigDecimal(410));
		map.put("3562000040524186",new BigDecimal(410));
		map.put("3562000040899354",new BigDecimal(408));
		map.put("3562000040546165",new BigDecimal(408));
		map.put("3562000041394706",new BigDecimal(408));
		map.put("3562000038973094",new BigDecimal(408));
		map.put("3562000041987544",new BigDecimal(408));
		map.put("3562000042727905",new BigDecimal(407));
		map.put("3562000038115046",new BigDecimal(404));
		map.put("3562000043461266",new BigDecimal(403));
		map.put("3562000038084794",new BigDecimal(400));
		map.put("3562000045152106",new BigDecimal(400));
		map.put("3562000045130895",new BigDecimal(400));
		map.put("3562000038417475",new BigDecimal(400));
		map.put("3562000040609326",new BigDecimal(400));
		map.put("3562000038162426",new BigDecimal(400));
		map.put("3562000038414496",new BigDecimal(400));
		map.put("3562000039692683",new BigDecimal(400));
		map.put("3562000038292075",new BigDecimal(400));
		map.put("3562000038679903",new BigDecimal(400));
		map.put("3562000040099275",new BigDecimal(400));
		map.put("3562000041118995",new BigDecimal(400));
		map.put("3562000045130965",new BigDecimal(400));
		map.put("3562000038000076",new BigDecimal(400));
		map.put("3562000036100296",new BigDecimal(400));
		map.put("3562000038223706",new BigDecimal(400));
		map.put("3562000041569834",new BigDecimal(400));
		map.put("3562000042176495",new BigDecimal(400));
		map.put("3562000037156335",new BigDecimal(400));
		map.put("3562000038286005",new BigDecimal(400));
		map.put("3562000043814746",new BigDecimal(400));
		map.put("3562000045136505",new BigDecimal(400));
		map.put("3562000037972384",new BigDecimal(400));
		map.put("3562000038639215",new BigDecimal(400));
		map.put("3562000045149306",new BigDecimal(400));
		map.put("3562000045141765",new BigDecimal(400));
		map.put("3562000036864084",new BigDecimal(400));
		map.put("3562000038356653",new BigDecimal(400));
		map.put("3562000036585353",new BigDecimal(400));
		map.put("3562000036486345",new BigDecimal(400));
		map.put("3562000040528436",new BigDecimal(400));
		map.put("3562000041431607",new BigDecimal(400));
		map.put("3562000036863254",new BigDecimal(400));
		map.put("3562000040668824",new BigDecimal(400));
		map.put("3562000039103636",new BigDecimal(400));
		map.put("3562000042411607",new BigDecimal(400));
		map.put("3562000040521017",new BigDecimal(400));
		map.put("3562000044927905",new BigDecimal(400));
		map.put("3562000041656793",new BigDecimal(400));
		map.put("3562000037156205",new BigDecimal(400));
		map.put("3562000043620386",new BigDecimal(400));
		map.put("3562000043591884",new BigDecimal(400));
		map.put("3562000042456994",new BigDecimal(400));
		map.put("3562000043869534",new BigDecimal(400));
		map.put("3562000041414947",new BigDecimal(400));
		map.put("3562000042851416",new BigDecimal(400));
		map.put("3562000040530616",new BigDecimal(400));
		map.put("3562000041536046",new BigDecimal(400));
		map.put("3562000042400756",new BigDecimal(400));
		map.put("3562000037203536",new BigDecimal(400));
		map.put("3562000045023716",new BigDecimal(399));
		map.put("3562000045047764",new BigDecimal(398));
		map.put("3562000045146136",new BigDecimal(396));
		map.put("3562000045145285",new BigDecimal(396));
		map.put("3562000045166924",new BigDecimal(396));
		map.put("3562000045189834",new BigDecimal(395));
		map.put("3562000045189734",new BigDecimal(395));
		map.put("3562000045194574",new BigDecimal(395));
		map.put("3562000040541726",new BigDecimal(391));
		map.put("3562000041590535",new BigDecimal(391));
		map.put("3562000041394347",new BigDecimal(391));
		map.put("3562000036713255",new BigDecimal(391));
		map.put("3562000041191995",new BigDecimal(391));
		map.put("3562000040534047",new BigDecimal(391));
		map.put("3562000041330676",new BigDecimal(391));
		map.put("3562000041232856",new BigDecimal(391));
		map.put("3562000041541296",new BigDecimal(391));
		map.put("3562000044972945",new BigDecimal(391));
		map.put("3562000045037336",new BigDecimal(390));
		map.put("3562000043990265",new BigDecimal(386));
		map.put("3562000042862436",new BigDecimal(384));
		map.put("3562000042856264",new BigDecimal(384));
		map.put("3562000045178604",new BigDecimal(384));
		map.put("3562000040537805",new BigDecimal(382));
		map.put("3562000041116875",new BigDecimal(382));
		map.put("3562000038333755",new BigDecimal(382));
		map.put("3562000041747355",new BigDecimal(382));
		map.put("3562000041683705",new BigDecimal(382));
		map.put("3562000040525016",new BigDecimal(382));
		map.put("3562000037733395",new BigDecimal(380));
		map.put("3562000040536206",new BigDecimal(380));
		map.put("3562000045188045",new BigDecimal(378));
		map.put("3562000045174564",new BigDecimal(378));
		map.put("3562000045187354",new BigDecimal(378));
		map.put("3562000045156394",new BigDecimal(378));
		map.put("3562000045195893",new BigDecimal(378));
		map.put("3562000045177573",new BigDecimal(378));
		map.put("3562000045150455",new BigDecimal(378));
		map.put("3562000045176445",new BigDecimal(378));
		map.put("3562000045181136",new BigDecimal(378));
		map.put("3562000045154265",new BigDecimal(378));
		map.put("3562000045033156",new BigDecimal(378));
		map.put("3562000045173426",new BigDecimal(378));
		map.put("3562000045164974",new BigDecimal(377));
		map.put("3562000044978514",new BigDecimal(375));
		map.put("3562000040533636",new BigDecimal(374));
		map.put("3562000040521575",new BigDecimal(373));
		map.put("3562000043037585",new BigDecimal(372));
		map.put("3562000045073805",new BigDecimal(370));
		map.put("3562000045155345",new BigDecimal(370));
		map.put("3562000042132048",new BigDecimal(369));
		map.put("3562000045038126",new BigDecimal(369));
		map.put("3562000039112626",new BigDecimal(366));
		map.put("3562000040939505",new BigDecimal(365));
		map.put("3562000045189624",new BigDecimal(365));
		map.put("3562000042854725",new BigDecimal(365));
		map.put("3562000041284655",new BigDecimal(365));
		map.put("3562000040542585",new BigDecimal(365));
		map.put("3562000041266835",new BigDecimal(364));
		map.put("3562000045153674",new BigDecimal(364));
		map.put("3562000039119845",new BigDecimal(364));
		map.put("3562000045046554",new BigDecimal(362));
		map.put("3562000044014448",new BigDecimal(361));
		map.put("3562000044875914",new BigDecimal(360));
		map.put("3562000045056154",new BigDecimal(360));
		map.put("3562000044952854",new BigDecimal(360));
		map.put("3562000042148706",new BigDecimal(360));
		map.put("3562000043919355",new BigDecimal(360));
		map.put("3562000045012806",new BigDecimal(360));
		map.put("3562000045182935",new BigDecimal(360));
		map.put("3562000043898504",new BigDecimal(360));
		map.put("3562000038440366",new BigDecimal(360));
		map.put("3562000042849784",new BigDecimal(359));
		map.put("3562000038204706",new BigDecimal(358));
		map.put("3562000044002966",new BigDecimal(356));
		map.put("3562000040537854",new BigDecimal(356));
		map.put("3562000045178345",new BigDecimal(353));
		map.put("3562000045046835",new BigDecimal(352));
		map.put("3562000043007096",new BigDecimal(350));
		map.put("3562000045179924",new BigDecimal(350));
		map.put("3562000036879214",new BigDecimal(350));
		map.put("3562000040096635",new BigDecimal(350));
		map.put("3562000043644386",new BigDecimal(350));
		map.put("3562000040545046",new BigDecimal(350));
		map.put("3562000045161455",new BigDecimal(350));
		map.put("3562000042907326",new BigDecimal(350));
		map.put("3562000045000427",new BigDecimal(350));
		map.put("3562000043959045",new BigDecimal(350));
		map.put("3562000037669662",new BigDecimal(350));
		map.put("3562000037841195",new BigDecimal(350));
		map.put("3562000045199793",new BigDecimal(350));
		map.put("3562000045026316",new BigDecimal(350));
		map.put("3562000040908515",new BigDecimal(348));
		map.put("3562000040746485",new BigDecimal(348));
		map.put("3562000041369945",new BigDecimal(348));
		map.put("3562000044158236",new BigDecimal(348));
		map.put("3562000042503626",new BigDecimal(348));
		map.put("3562000041658553",new BigDecimal(348));
		map.put("3562000041470127",new BigDecimal(347));
		map.put("3562000045113456",new BigDecimal(345));
		map.put("3562000045130885",new BigDecimal(344));
		map.put("3562000045136564",new BigDecimal(344));
		map.put("3562000045016046",new BigDecimal(340));
		map.put("3562000036115095",new BigDecimal(340));
		map.put("3562000045015635",new BigDecimal(340));
		map.put("3562000040564805",new BigDecimal(339));
		map.put("3562000042539295",new BigDecimal(339));
		map.put("3562000041498894",new BigDecimal(339));
		map.put("3562000041554874",new BigDecimal(339));
		map.put("3562000040991026",new BigDecimal(339));
		map.put("3562000038371346",new BigDecimal(339));
		map.put("3562000040541437",new BigDecimal(339));
		map.put("3562000045073884",new BigDecimal(336));
		map.put("3562000042862316",new BigDecimal(335));
		map.put("3562000044232297",new BigDecimal(332));
		map.put("3562000037234486",new BigDecimal(331));
		map.put("3562000043404676",new BigDecimal(331));
		map.put("3562000045033137",new BigDecimal(331));
		map.put("3562000036859943",new BigDecimal(330));
		map.put("3562000037067993",new BigDecimal(330));
		map.put("3562000041200208",new BigDecimal(330));
		map.put("3562000040555035",new BigDecimal(330));
		map.put("3562000041642176",new BigDecimal(330));
		map.put("3562000038000296",new BigDecimal(330));
		map.put("3562000041316286",new BigDecimal(330));
		map.put("3562000040523885",new BigDecimal(330));
		map.put("3562000042131148",new BigDecimal(330));
		map.put("3562000041614775",new BigDecimal(330));
		map.put("3562000040517664",new BigDecimal(329));
		map.put("3562000044042917",new BigDecimal(326));
		map.put("3562000045128116",new BigDecimal(324));
		map.put("3562000044216356",new BigDecimal(323));
		map.put("3562000038535834",new BigDecimal(322));
		map.put("3562000041117496",new BigDecimal(321));
		map.put("3562000042956154",new BigDecimal(320));
		map.put("3562000043515095",new BigDecimal(320));
		map.put("3562000045046535",new BigDecimal(320));
		map.put("3562000040554085",new BigDecimal(320));
		map.put("3562000045158663",new BigDecimal(320));
		map.put("3562000037005185",new BigDecimal(318));
		map.put("3562000045122636",new BigDecimal(317));
		map.put("3562000044095265",new BigDecimal(316));
		map.put("3562000045170265",new BigDecimal(316));
		map.put("3562000044095854",new BigDecimal(316));
		map.put("3562000045185893",new BigDecimal(315));
		map.put("3562000045155215",new BigDecimal(315));
		map.put("3562000045213456",new BigDecimal(315));
		map.put("3562000045201975",new BigDecimal(315));
		map.put("3562000041010707",new BigDecimal(313));
		map.put("3562000041352936",new BigDecimal(313));
		map.put("3562000041055085",new BigDecimal(313));
		map.put("3562000043549465",new BigDecimal(313));
		map.put("3562000041187594",new BigDecimal(313));
		map.put("3562000039130117",new BigDecimal(313));
		map.put("3562000041638564",new BigDecimal(313));
		map.put("3562000041737605",new BigDecimal(312));
		map.put("3562000043765734",new BigDecimal(311));
		map.put("3562000044982545",new BigDecimal(310));
		map.put("3562000037244675",new BigDecimal(310));
		map.put("3562000040552884",new BigDecimal(310));
		map.put("3562000037015485",new BigDecimal(306));
		map.put("3562000042734595",new BigDecimal(306));
		map.put("3562000037797903",new BigDecimal(304));
		map.put("3562000040531865",new BigDecimal(304));
		map.put("3562000042322428",new BigDecimal(304));
		map.put("3562000040679534",new BigDecimal(304));
		map.put("3562000040539545",new BigDecimal(300));
		map.put("3562000039108964",new BigDecimal(300));
		map.put("3562000038402785",new BigDecimal(300));
		map.put("3562000041326565",new BigDecimal(300));
		map.put("3562000038529763",new BigDecimal(300));
		map.put("3562000037378744",new BigDecimal(300));
		map.put("3562000038860474",new BigDecimal(300));
		map.put("3562000045142946",new BigDecimal(300));
		map.put("3562000040975534",new BigDecimal(300));
		map.put("3562000041380565",new BigDecimal(300));
		map.put("3562000037938205",new BigDecimal(300));
		map.put("3562000038598633",new BigDecimal(300));
		map.put("3562000045104755",new BigDecimal(300));
		map.put("3562000037272854",new BigDecimal(300));
		map.put("3562000038233926",new BigDecimal(300));
		map.put("3562000038096873",new BigDecimal(300));
		map.put("3562000038597872",new BigDecimal(300));
		map.put("3562000039759603",new BigDecimal(300));
		map.put("3562000040913695",new BigDecimal(300));
		map.put("3562000041106107",new BigDecimal(300));
		map.put("3562000045012276",new BigDecimal(300));
		map.put("3562000040913156",new BigDecimal(300));
		map.put("3562000044972964",new BigDecimal(300));
		map.put("3562000041418396",new BigDecimal(300));
		map.put("3562000045104227",new BigDecimal(300));
		map.put("3562000041085874",new BigDecimal(300));
		map.put("3562000041501675",new BigDecimal(300));
		map.put("3562000038051794",new BigDecimal(300));
		map.put("3562000040671465",new BigDecimal(300));
		map.put("3562000042123087",new BigDecimal(300));
		map.put("3562000045166563",new BigDecimal(300));
		map.put("3562000043966374",new BigDecimal(300));
		map.put("3562000040528884",new BigDecimal(300));
		map.put("3562000035966463",new BigDecimal(300));
		map.put("3562000038548853",new BigDecimal(300));
		map.put("3562000045177264",new BigDecimal(300));
		map.put("3562000045012266",new BigDecimal(300));
		map.put("3562000041211438",new BigDecimal(300));
		map.put("3562000038860435",new BigDecimal(300));
		map.put("3562000039113476",new BigDecimal(300));
		map.put("3562000045013376",new BigDecimal(299));
		map.put("3562000045030086",new BigDecimal(298));
		map.put("3562000043403666",new BigDecimal(296));
		map.put("3562000041086065",new BigDecimal(295));
		map.put("3562000037840735",new BigDecimal(293));
		map.put("3562000043767074",new BigDecimal(291));
		map.put("3562000045065084",new BigDecimal(290));
		map.put("3562000045162594",new BigDecimal(290));
		map.put("3562000043590465",new BigDecimal(288));
		map.put("3562000044746884",new BigDecimal(288));
		map.put("3562000040542885",new BigDecimal(287));
		map.put("3562000040948835",new BigDecimal(287));
		map.put("3562000041405695",new BigDecimal(287));
		map.put("3562000037005206",new BigDecimal(286));
		map.put("3562000043986464",new BigDecimal(282));
		map.put("3562000042495854",new BigDecimal(282));
		map.put("3562000045163106",new BigDecimal(280));
		map.put("3562000044095226",new BigDecimal(280));
		map.put("3562000042855235",new BigDecimal(280));
		map.put("3562000045145715",new BigDecimal(280));
		map.put("3562000045166753",new BigDecimal(280));
		map.put("3562000043963006",new BigDecimal(280));
		map.put("3562000045162326",new BigDecimal(280));
		map.put("3562000045166514",new BigDecimal(280));
		map.put("3562000042894505",new BigDecimal(280));
		map.put("3562000040555425",new BigDecimal(280));
		map.put("3562000043978573",new BigDecimal(280));
		map.put("3562000043978953",new BigDecimal(280));
		map.put("3562000043958225",new BigDecimal(280));
		map.put("3562000044188874",new BigDecimal(280));
		map.put("3562000042854945",new BigDecimal(280));
		map.put("3562000042854825",new BigDecimal(279));
		map.put("3562000041292027",new BigDecimal(278));
		map.put("3562000041280795",new BigDecimal(278));
		map.put("3562000041062906",new BigDecimal(278));
		map.put("3562000040533806",new BigDecimal(278));
		map.put("3562000040551884",new BigDecimal(278));
		map.put("3562000041557164",new BigDecimal(278));
		map.put("3562000041551206",new BigDecimal(277));
		map.put("3562000045008095",new BigDecimal(276));
		map.put("3562000045100307",new BigDecimal(276));
		map.put("3562000045162185",new BigDecimal(273));
		map.put("3562000045053854",new BigDecimal(273));
		map.put("3562000037983115",new BigDecimal(270));
		map.put("3562000045183535",new BigDecimal(270));
		map.put("3562000043549055",new BigDecimal(270));
		map.put("3562000044252785",new BigDecimal(268));
		map.put("3562000045073925",new BigDecimal(267));
		map.put("3562000042608645",new BigDecimal(266));
		map.put("3562000045221176",new BigDecimal(265));
		map.put("3562000043529146",new BigDecimal(261));
		map.put("3562000041049965",new BigDecimal(261));
		map.put("3562000041269725",new BigDecimal(261));
		map.put("3562000040553175",new BigDecimal(261));
		map.put("3562000041354107",new BigDecimal(261));
		map.put("3562000040529705",new BigDecimal(261));
		map.put("3562000040529864",new BigDecimal(261));
		map.put("3562000040524437",new BigDecimal(261));
		map.put("3562000038416574",new BigDecimal(261));
		map.put("3562000041035816",new BigDecimal(261));
		map.put("3562000040769544",new BigDecimal(261));
		map.put("3562000041037337",new BigDecimal(261));
		map.put("3562000041952395",new BigDecimal(261));
		map.put("3562000038235605",new BigDecimal(261));
		map.put("3562000040519436",new BigDecimal(261));
		map.put("3562000040545615",new BigDecimal(261));
		map.put("3562000041239595",new BigDecimal(261));
		map.put("3562000042879824",new BigDecimal(260));
		map.put("3562000037357824",new BigDecimal(260));
		map.put("3562000040553316",new BigDecimal(260));
		map.put("3562000045046735",new BigDecimal(260));
		map.put("3562000045048735",new BigDecimal(260));
		map.put("3562000045155624",new BigDecimal(260));
		map.put("3562000037169064",new BigDecimal(260));
		map.put("3562000040539385",new BigDecimal(260));
		map.put("3562000038060564",new BigDecimal(260));
		map.put("3562000045153984",new BigDecimal(259));
		map.put("3562000042878284",new BigDecimal(257));
		map.put("3562000043907195",new BigDecimal(256));
		map.put("3562000042669174",new BigDecimal(255));
		map.put("3562000045116385",new BigDecimal(255));
		map.put("3562000042879294",new BigDecimal(255));
		map.put("3562000044080536",new BigDecimal(254));
		map.put("3562000044294237",new BigDecimal(253));
		map.put("3562000045174705",new BigDecimal(252));
		map.put("3562000045046455",new BigDecimal(252));
		map.put("3562000044054885",new BigDecimal(252));
		map.put("3562000036115126",new BigDecimal(252));
		map.put("3562000045170336",new BigDecimal(252));
		map.put("3562000041418407",new BigDecimal(252));
		map.put("3562000038057025",new BigDecimal(250));
		map.put("3562000042958225",new BigDecimal(250));
		map.put("3562000045183206",new BigDecimal(250));
		map.put("3562000042966054",new BigDecimal(250));
		map.put("3562000045130626",new BigDecimal(250));
		map.put("3562000045048385",new BigDecimal(250));
		map.put("3562000037406426",new BigDecimal(250));
		map.put("3562000045147036",new BigDecimal(250));
		map.put("3562000043671455",new BigDecimal(250));
		map.put("3562000045001486",new BigDecimal(249));
		map.put("3562000042673835",new BigDecimal(248));
		map.put("3562000042871036",new BigDecimal(244));
		map.put("3562000042870874",new BigDecimal(244));
		map.put("3562000044037007",new BigDecimal(243));
		map.put("3562000042131018",new BigDecimal(243));
		map.put("3562000040617715",new BigDecimal(243));
		map.put("3562000038318095",new BigDecimal(243));
		map.put("3562000040929306",new BigDecimal(243));
		map.put("3562000040552954",new BigDecimal(242));
		map.put("3562000045128126",new BigDecimal(240));
		map.put("3562000039130237",new BigDecimal(240));
		map.put("3562000042670615",new BigDecimal(240));
		map.put("3562000044135037",new BigDecimal(240));
		map.put("3562000042957924",new BigDecimal(240));
		map.put("3562000043683316",new BigDecimal(240));
		map.put("3562000043651155",new BigDecimal(240));
		map.put("3562000043712685",new BigDecimal(240));
		map.put("3562000045154485",new BigDecimal(240));
		map.put("3562000043671475",new BigDecimal(240));
		map.put("3562000039589903",new BigDecimal(240));
		map.put("3562000039653364",new BigDecimal(240));
		map.put("3562000045168135",new BigDecimal(239));
		map.put("3562000045039945",new BigDecimal(238));
		map.put("3562000045155673",new BigDecimal(237));
		map.put("3562000044224917",new BigDecimal(237));
		map.put("3562000043990306",new BigDecimal(237));
		map.put("3562000045136485",new BigDecimal(235));
		map.put("3562000043091636",new BigDecimal(234));
		map.put("3562000041317906",new BigDecimal(234));
		map.put("3562000042853925",new BigDecimal(234));
		map.put("3562000041586454",new BigDecimal(234));
		map.put("3562000041663065",new BigDecimal(234));
		map.put("3562000045047835",new BigDecimal(231));
		map.put("3562000037156194",new BigDecimal(230));
		map.put("3562000042794615",new BigDecimal(230));
		map.put("3562000045046915",new BigDecimal(230));
		map.put("3562000045130855",new BigDecimal(230));
		map.put("3562000037296384",new BigDecimal(230));
		map.put("3562000044987445",new BigDecimal(230));
		map.put("3562000045174935",new BigDecimal(229));
		map.put("3562000045101595",new BigDecimal(228));
		map.put("3562000042170237",new BigDecimal(226));
		map.put("3562000041282685",new BigDecimal(226));
		map.put("3562000040525406",new BigDecimal(226));
		map.put("3562000038846264",new BigDecimal(226));
		map.put("3562000041571406",new BigDecimal(226));
		map.put("3562000041230448",new BigDecimal(226));
		map.put("3562000040238675",new BigDecimal(225));
		map.put("3562000040974715",new BigDecimal(225));
		map.put("3562000043671055",new BigDecimal(225));
		map.put("3562000042412507",new BigDecimal(221));
		map.put("3562000045147016",new BigDecimal(221));
		map.put("3562000042156416",new BigDecimal(220));
		map.put("3562000040546864",new BigDecimal(220));
		map.put("3562000041561945",new BigDecimal(220));
		map.put("3562000040522017",new BigDecimal(220));
		map.put("3562000042801765",new BigDecimal(220));
		map.put("3562000045203895",new BigDecimal(220));
		map.put("3562000040546316",new BigDecimal(220));
		map.put("3562000040546984",new BigDecimal(218));
		map.put("3562000036115226",new BigDecimal(217));
		map.put("3562000044149266",new BigDecimal(217));
		map.put("3562000036864235",new BigDecimal(215));
		map.put("3562000045047754",new BigDecimal(212));
		map.put("3562000045087064",new BigDecimal(211));
		map.put("3562000044095355",new BigDecimal(210));
		map.put("3562000045183095",new BigDecimal(210));
		map.put("3562000044048526",new BigDecimal(210));
		map.put("3562000044031338",new BigDecimal(210));
		map.put("3562000037005584",new BigDecimal(210));
		map.put("3562000045160375",new BigDecimal(210));
		map.put("3562000045161426",new BigDecimal(210));
		map.put("3562000045156254",new BigDecimal(210));
		map.put("3562000045162406",new BigDecimal(210));
		map.put("3562000044095954",new BigDecimal(210));
		map.put("3562000036922574",new BigDecimal(210));
		map.put("3562000045152745",new BigDecimal(210));
		map.put("3562000042879054",new BigDecimal(210));
		map.put("3562000043978663",new BigDecimal(210));
		map.put("3562000045168563",new BigDecimal(210));
		map.put("3562000042862306",new BigDecimal(210));
		map.put("3562000042852006",new BigDecimal(210));
		map.put("3562000044111148",new BigDecimal(210));
		map.put("3562000045161574",new BigDecimal(210));
		map.put("3562000042955883",new BigDecimal(210));
		map.put("3562000045179454",new BigDecimal(210));
		map.put("3562000042849894",new BigDecimal(210));
		map.put("3562000044046946",new BigDecimal(210));
		map.put("3562000045167724",new BigDecimal(210));
		map.put("3562000042955683",new BigDecimal(208));
		map.put("3562000044991735",new BigDecimal(208));
		map.put("3562000039482436",new BigDecimal(208));
		map.put("3562000040104228",new BigDecimal(208));
		map.put("3562000041374037",new BigDecimal(208));
		map.put("3562000041021937",new BigDecimal(208));
		map.put("3562000045087544",new BigDecimal(207));
		map.put("3562000044157945",new BigDecimal(204));
		map.put("3562000045146365",new BigDecimal(204));
		map.put("3562000038030626",new BigDecimal(200));
		map.put("3562000040542665",new BigDecimal(200));
		map.put("3562000038594573",new BigDecimal(200));
		map.put("3562000040540256",new BigDecimal(200));
		map.put("3562000040553515",new BigDecimal(200));
		map.put("3562000041648815",new BigDecimal(200));
		map.put("3562000045016745",new BigDecimal(200));
		map.put("3562000041093186",new BigDecimal(200));
		map.put("3562000042155406",new BigDecimal(200));
		map.put("3562000035912805",new BigDecimal(200));
		map.put("3562000038203247",new BigDecimal(200));
		map.put("3562000038266693",new BigDecimal(200));
		map.put("3562000045127974",new BigDecimal(200));
		map.put("3562000045038146",new BigDecimal(200));
		map.put("3562000038173684",new BigDecimal(200));
		map.put("3562000044400576",new BigDecimal(200));
		map.put("3562000041063086",new BigDecimal(200));
		map.put("3562000040116227",new BigDecimal(200));
		map.put("3562000038403516",new BigDecimal(200));
		map.put("3562000045147295",new BigDecimal(200));
		map.put("3562000038318535",new BigDecimal(200));
		map.put("3562000041559394",new BigDecimal(200));
		map.put("3562000037247735",new BigDecimal(200));
		map.put("3562000043017826",new BigDecimal(200));
		map.put("3562000038600255",new BigDecimal(200));
		map.put("3562000040551455",new BigDecimal(200));
		map.put("3562000037234266",new BigDecimal(200));
		map.put("3562000040538984",new BigDecimal(200));
		map.put("3562000037892215",new BigDecimal(200));
		map.put("3562000044663336",new BigDecimal(200));
		map.put("3562000043800456",new BigDecimal(200));
		map.put("3562000036888144",new BigDecimal(200));
		map.put("3562000038526734",new BigDecimal(200));
		map.put("3562000044978504",new BigDecimal(200));
		map.put("3562000037239784",new BigDecimal(200));
		map.put("3562000037961563",new BigDecimal(200));
		map.put("3562000039692704",new BigDecimal(200));
		map.put("3562000037952514",new BigDecimal(200));
		map.put("3562000040525336",new BigDecimal(200));
		map.put("3562000036863783",new BigDecimal(200));
		map.put("3562000041717255",new BigDecimal(200));
		map.put("3562000036115116",new BigDecimal(200));
		map.put("3562000040524716",new BigDecimal(200));
		map.put("3562000043904007",new BigDecimal(200));
		map.put("3562000037094216",new BigDecimal(200));
		map.put("3562000040555074",new BigDecimal(200));
		map.put("3562000045148116",new BigDecimal(200));
		map.put("3562000045066573",new BigDecimal(200));
		map.put("3562000039291075",new BigDecimal(198));
		map.put("3562000045181336",new BigDecimal(196));
		map.put("3562000038116874",new BigDecimal(195));
		map.put("3562000037385025",new BigDecimal(195));
		map.put("3562000045046545",new BigDecimal(192));
		map.put("3562000042955514",new BigDecimal(192));
		map.put("3562000043441956",new BigDecimal(192));
		map.put("3562000042548285",new BigDecimal(191));
		map.put("3562000040524936",new BigDecimal(191));
		map.put("3562000045186425",new BigDecimal(190));
		map.put("3562000045073864",new BigDecimal(190));
		map.put("3562000040529994",new BigDecimal(190));
		map.put("3562000045046894",new BigDecimal(190));
		map.put("3562000044262137",new BigDecimal(190));
		map.put("3562000045150116",new BigDecimal(189));
		map.put("3562000045178284",new BigDecimal(189));
		map.put("3562000045151894",new BigDecimal(189));
		map.put("3562000045167593",new BigDecimal(189));
		map.put("3562000045168983",new BigDecimal(189));
		map.put("3562000045176504",new BigDecimal(189));
		map.put("3562000045195045",new BigDecimal(189));
		map.put("3562000045189374",new BigDecimal(189));
		map.put("3562000045184574",new BigDecimal(189));
		map.put("3562000043984805",new BigDecimal(188));
		map.put("3562000042632137",new BigDecimal(187));
		map.put("3562000041097954",new BigDecimal(186));
		map.put("3562000045015594",new BigDecimal(186));
		map.put("3562000038204655",new BigDecimal(184));
		map.put("3562000043688435",new BigDecimal(184));
		map.put("3562000042862285",new BigDecimal(183));
		map.put("3562000039103575",new BigDecimal(182));
		map.put("3562000041170926",new BigDecimal(182));
		map.put("3562000042943396",new BigDecimal(182));
		map.put("3562000037303975",new BigDecimal(180));
		map.put("3562000043953954",new BigDecimal(180));
		map.put("3562000043984835",new BigDecimal(180));
		map.put("3562000043935735",new BigDecimal(180));
		map.put("3562000037400546",new BigDecimal(180));
		map.put("3562000044987724",new BigDecimal(180));
		map.put("3562000037376164",new BigDecimal(180));
		map.put("3562000044484516",new BigDecimal(180));
		map.put("3562000042802056",new BigDecimal(179));
		map.put("3562000036349505",new BigDecimal(178));
		map.put("3562000044254347",new BigDecimal(178));
		map.put("3562000039886882",new BigDecimal(178));
		map.put("3562000044095246",new BigDecimal(176));
		map.put("3562000042948605",new BigDecimal(176));
		map.put("3562000045175274",new BigDecimal(176));
		map.put("3562000042794594",new BigDecimal(175));
		map.put("3562000038933026",new BigDecimal(174));
		map.put("3562000037670573",new BigDecimal(174));
		map.put("3562000040523995",new BigDecimal(174));
		map.put("3562000039243885",new BigDecimal(173));
		map.put("3562000042360726",new BigDecimal(173));
		map.put("3562000042367246",new BigDecimal(173));
		map.put("3562000045046715",new BigDecimal(171));
		map.put("3562000045181026",new BigDecimal(170));
		map.put("3562000037192884",new BigDecimal(170));
		map.put("3562000045046525",new BigDecimal(170));
		map.put("3562000036069405",new BigDecimal(170));
		map.put("3562000045090935",new BigDecimal(170));
		map.put("3562000045039106",new BigDecimal(169));
		map.put("3562000045025336",new BigDecimal(166));
		map.put("3562000043669145",new BigDecimal(165));
		map.put("3562000040553684",new BigDecimal(165));
		map.put("3562000040406066",new BigDecimal(164));
		map.put("3562000042973055",new BigDecimal(162));
		map.put("3562000045154854",new BigDecimal(160));
		map.put("3562000043005496",new BigDecimal(160));
		map.put("3562000037660464",new BigDecimal(160));
		map.put("3562000045126535",new BigDecimal(160));
		map.put("3562000043609954",new BigDecimal(160));
		map.put("3562000045018055",new BigDecimal(160));
		map.put("3562000045015236",new BigDecimal(160));
		map.put("3562000045126605",new BigDecimal(160));
		map.put("3562000045150574",new BigDecimal(158));
		map.put("3562000045155883",new BigDecimal(158));
		map.put("3562000045151564",new BigDecimal(158));
		map.put("3562000038530246",new BigDecimal(156));
		map.put("3562000038049525",new BigDecimal(156));
		map.put("3562000041017296",new BigDecimal(156));
		map.put("3562000038532336",new BigDecimal(156));
		map.put("3562000038322476",new BigDecimal(156));
		map.put("3562000042341537",new BigDecimal(156));
		map.put("3562000045197693",new BigDecimal(154));
		map.put("3562000045137455",new BigDecimal(153));
		map.put("3562000043913665",new BigDecimal(152));
		map.put("3562000045175405",new BigDecimal(151));
		map.put("3562000042809426",new BigDecimal(150));
		map.put("3562000045039136",new BigDecimal(150));
		map.put("3562000043808845",new BigDecimal(150));
		map.put("3562000038246994",new BigDecimal(147));
		map.put("3562000040530826",new BigDecimal(147));
		map.put("3562000038295474",new BigDecimal(147));
		map.put("3562000043558045",new BigDecimal(147));
		map.put("3562000041699084",new BigDecimal(147));
		map.put("3562000043953835",new BigDecimal(146));
		map.put("3562000044977893",new BigDecimal(145));
		map.put("3562000045165724",new BigDecimal(145));
		map.put("3562000044095295",new BigDecimal(144));
		map.put("3562000044023387",new BigDecimal(144));
		map.put("3562000044690805",new BigDecimal(144));
		map.put("3562000042935416",new BigDecimal(144));
		map.put("3562000043969683",new BigDecimal(144));
		map.put("3562000042859863",new BigDecimal(140));
		map.put("3562000045162116",new BigDecimal(140));
		map.put("3562000045162475",new BigDecimal(140));
		map.put("3562000045168663",new BigDecimal(140));
		map.put("3562000045161554",new BigDecimal(140));
		map.put("3562000045160894",new BigDecimal(140));
		map.put("3562000037271874",new BigDecimal(140));
		map.put("3562000045166384",new BigDecimal(140));
		map.put("3562000045161106",new BigDecimal(140));
		map.put("3562000044095784",new BigDecimal(140));
		map.put("3562000043976704",new BigDecimal(140));
		map.put("3562000045161265",new BigDecimal(140));
		map.put("3562000043957384",new BigDecimal(140));
		map.put("3562000043958683",new BigDecimal(140));
		map.put("3562000045162545",new BigDecimal(140));
		map.put("3562000045167924",new BigDecimal(140));
		map.put("3562000044041177",new BigDecimal(140));
		map.put("3562000043990735",new BigDecimal(140));
		map.put("3562000044199055",new BigDecimal(140));
		map.put("3562000045151825",new BigDecimal(140));
		map.put("3562000045169704",new BigDecimal(140));
		map.put("3562000044120348",new BigDecimal(140));
		map.put("3562000041586504",new BigDecimal(139));
		map.put("3562000040538055",new BigDecimal(139));
		map.put("3562000038084236",new BigDecimal(139));
		map.put("3562000038204695",new BigDecimal(138));
		map.put("3562000042423097",new BigDecimal(138));
		map.put("3562000042539385",new BigDecimal(138));
		map.put("3562000044286016",new BigDecimal(135));
		map.put("3562000040539336",new BigDecimal(135));
		map.put("3562000039383845",new BigDecimal(134));
		map.put("3562000038162285",new BigDecimal(130));
		map.put("3562000043454417",new BigDecimal(130));
		map.put("3562000041455426",new BigDecimal(130));
		map.put("3562000041577714",new BigDecimal(130));
		map.put("3562000040541836",new BigDecimal(130));
		map.put("3562000036333317",new BigDecimal(130));
		map.put("3562000045154905",new BigDecimal(130));
		map.put("3562000041676294",new BigDecimal(130));
		map.put("3562000041025855",new BigDecimal(130));
		map.put("3562000037172336",new BigDecimal(129));
		map.put("3562000042871046",new BigDecimal(127));
		map.put("3562000045182545",new BigDecimal(126));
		map.put("3562000045153495",new BigDecimal(126));
		map.put("3562000045171016",new BigDecimal(126));
		map.put("3562000045151346",new BigDecimal(126));
		map.put("3562000045204447",new BigDecimal(126));
		map.put("3562000045176094",new BigDecimal(126));
		map.put("3562000045161915",new BigDecimal(126));
		map.put("3562000045172175",new BigDecimal(126));
		map.put("3562000045205815",new BigDecimal(126));
		map.put("3562000042641885",new BigDecimal(125));
		map.put("3562000042850365",new BigDecimal(122));
		map.put("3562000042853894",new BigDecimal(122));
		map.put("3562000042802476",new BigDecimal(121));
		map.put("3562000037249065",new BigDecimal(120));
		map.put("3562000042804716",new BigDecimal(120));
		map.put("3562000037803645",new BigDecimal(120));
		map.put("3562000038102127",new BigDecimal(120));
		map.put("3562000042802695",new BigDecimal(120));
		map.put("3562000038271525",new BigDecimal(120));
		map.put("3562000045033117",new BigDecimal(120));
		map.put("3562000037930925",new BigDecimal(119));
		map.put("3562000045151026",new BigDecimal(118));
		map.put("3562000045046705",new BigDecimal(117));
		map.put("3562000045151584",new BigDecimal(117));
		map.put("3562000044076095",new BigDecimal(116));
		map.put("3562000045073835",new BigDecimal(116));
		map.put("3562000044105506",new BigDecimal(116));
		map.put("3562000043672925",new BigDecimal(115));
		map.put("3562000045179953",new BigDecimal(113));
		map.put("3562000042140457",new BigDecimal(113));
		map.put("3562000041455116",new BigDecimal(113));
		map.put("3562000045176904",new BigDecimal(113));
		map.put("3562000040544885",new BigDecimal(113));
		map.put("3562000038121237",new BigDecimal(113));
		map.put("3562000038407735",new BigDecimal(112));
		map.put("3562000043903437",new BigDecimal(110));
		map.put("3562000043499825",new BigDecimal(110));
		map.put("3562000037670115",new BigDecimal(110));
		map.put("3562000037669692",new BigDecimal(110));
		map.put("3562000043013907",new BigDecimal(108));
		map.put("3562000039703395",new BigDecimal(108));
		map.put("3562000044105846",new BigDecimal(108));
		map.put("3562000037960135",new BigDecimal(104));
		map.put("3562000040537365",new BigDecimal(104));
		map.put("3562000041708784",new BigDecimal(104));
		map.put("3562000041622466",new BigDecimal(104));
		map.put("3562000040977753",new BigDecimal(104));
		map.put("3562000043600486",new BigDecimal(104));
		map.put("3562000042676724",new BigDecimal(104));
		map.put("3562000040531646",new BigDecimal(104));
		map.put("3562000038411575",new BigDecimal(104));
		map.put("3562000040717146",new BigDecimal(104));
		map.put("3562000037005226",new BigDecimal(102));
		map.put("3562000042637745",new BigDecimal(102));
		map.put("3562000045098724",new BigDecimal(101));
		map.put("3562000045149774",new BigDecimal(100));
		map.put("3562000036862914",new BigDecimal(100));
		map.put("3562000037357563",new BigDecimal(100));
		map.put("3562000045047705",new BigDecimal(100));
		map.put("3562000037306674",new BigDecimal(100));
		map.put("3562000036862834",new BigDecimal(100));
		map.put("3562000044035785",new BigDecimal(100));
		map.put("3562000038241985",new BigDecimal(100));
		map.put("3562000041146906",new BigDecimal(100));
		map.put("3562000041171716",new BigDecimal(100));
		map.put("3562000045021296",new BigDecimal(100));
		map.put("3562000045049236",new BigDecimal(100));
		map.put("3562000040669544",new BigDecimal(100));
		map.put("3562000036767572",new BigDecimal(100));
		map.put("3562000037340156",new BigDecimal(100));
		map.put("3562000038194146",new BigDecimal(100));
		map.put("3562000040549854",new BigDecimal(100));
		map.put("3562000036879193",new BigDecimal(100));
		map.put("3562000040726815",new BigDecimal(100));
		map.put("3562000037954734",new BigDecimal(100));
		map.put("3562000045030276",new BigDecimal(100));
		map.put("3562000038544884",new BigDecimal(100));
		map.put("3562000037454465",new BigDecimal(100));
		map.put("3562000036863844",new BigDecimal(100));
		map.put("3562000040611217",new BigDecimal(100));
		map.put("3562000045007365",new BigDecimal(99));
		map.put("3562000038293055",new BigDecimal(98));
		map.put("3562000045153065",new BigDecimal(95));
		map.put("3562000036078883",new BigDecimal(95));
		map.put("3562000040531427",new BigDecimal(95));
		map.put("3562000043702565",new BigDecimal(94));
		map.put("3562000044959614",new BigDecimal(94));
		map.put("3562000039369215",new BigDecimal(94));
		map.put("3562000045073784",new BigDecimal(94));
		map.put("3562000044590136",new BigDecimal(93));
		map.put("3562000043935465",new BigDecimal(92));
		map.put("3562000040782974",new BigDecimal(91));
		map.put("3562000037997803",new BigDecimal(91));
		map.put("3562000041263946",new BigDecimal(91));
		map.put("3562000039759073",new BigDecimal(90));
		map.put("3562000037422675",new BigDecimal(90));
		map.put("3562000045191436",new BigDecimal(90));
		map.put("3562000045160515",new BigDecimal(90));
		map.put("3562000036114056",new BigDecimal(90));
		map.put("3562000045167944",new BigDecimal(90));
		map.put("3562000037376364",new BigDecimal(90));
		map.put("3562000038307065",new BigDecimal(90));
		map.put("3562000045172684",new BigDecimal(88));
		map.put("3562000041230228",new BigDecimal(87));
		map.put("3562000039627084",new BigDecimal(87));
		map.put("3562000042453307",new BigDecimal(87));
		map.put("3562000038385883",new BigDecimal(87));
		map.put("3562000038513026",new BigDecimal(87));
		map.put("3562000045014217",new BigDecimal(87));
		map.put("3562000038318226",new BigDecimal(87));
		map.put("3562000045031526",new BigDecimal(85));
		map.put("3562000042411627",new BigDecimal(85));
		map.put("3562000036765273",new BigDecimal(85));
		map.put("3562000045166354",new BigDecimal(84));
		map.put("3562000042954615",new BigDecimal(83));
		map.put("3562000043548116",new BigDecimal(83));
		map.put("3562000037074964",new BigDecimal(82));
		map.put("3562000040547594",new BigDecimal(82));
		map.put("3562000041599993",new BigDecimal(81));
		map.put("3562000042681075",new BigDecimal(81));
		map.put("3562000039076164",new BigDecimal(81));
		map.put("3562000038271146",new BigDecimal(80));
		map.put("3562000045183515",new BigDecimal(80));
		map.put("3562000042961495",new BigDecimal(80));
		map.put("3562000045019754",new BigDecimal(80));
		map.put("3562000045047794",new BigDecimal(80));
		map.put("3562000043439946",new BigDecimal(80));
		map.put("3562000040188825",new BigDecimal(80));
		map.put("3562000038240806",new BigDecimal(80));
		map.put("3562000037805744",new BigDecimal(80));
		map.put("3562000045150945",new BigDecimal(79));
		map.put("3562000044150985",new BigDecimal(79));
		map.put("3562000044148775",new BigDecimal(79));
		map.put("3562000045021307",new BigDecimal(78));
		map.put("3562000040972306",new BigDecimal(78));
		map.put("3562000044149646",new BigDecimal(78));
		map.put("3562000039343196",new BigDecimal(78));
		map.put("3562000039105226",new BigDecimal(78));
		map.put("3562000041298815",new BigDecimal(78));
		map.put("3562000038854035",new BigDecimal(76));
		map.put("3562000043684126",new BigDecimal(75));
		map.put("3562000042637195",new BigDecimal(75));
		map.put("3562000042696325",new BigDecimal(74));
		map.put("3562000042683545",new BigDecimal(74));
		map.put("3562000044105865",new BigDecimal(73));
		map.put("3562000044074555",new BigDecimal(73));
		map.put("3562000043980436",new BigDecimal(72));
		map.put("3562000044048675",new BigDecimal(72));
		map.put("3562000044106486",new BigDecimal(72));
		map.put("3562000043913685",new BigDecimal(72));
		map.put("3562000037950763",new BigDecimal(72));
		map.put("3562000040548515",new BigDecimal(70));
		map.put("3562000036723754",new BigDecimal(70));
		map.put("3562000045160915",new BigDecimal(70));
		map.put("3562000045046654",new BigDecimal(70));
		map.put("3562000042907645",new BigDecimal(70));
		map.put("3562000045051106",new BigDecimal(70));
		map.put("3562000038286963",new BigDecimal(70));
		map.put("3562000037980174",new BigDecimal(69));
		map.put("3562000040555364",new BigDecimal(69));
		map.put("3562000041101287",new BigDecimal(69));
		map.put("3562000040552694",new BigDecimal(67));
		map.put("3562000041425785",new BigDecimal(65));
		map.put("3562000040700156",new BigDecimal(64));
		map.put("3562000042804746",new BigDecimal(63));
		map.put("3562000045159793",new BigDecimal(63));
		map.put("3562000045151146",new BigDecimal(63));
		map.put("3562000041142277",new BigDecimal(63));
		map.put("3562000045173055",new BigDecimal(63));
		map.put("3562000040548584",new BigDecimal(61));
		map.put("3562000045080136",new BigDecimal(60));
		map.put("3562000045024317",new BigDecimal(60));
		map.put("3562000045073774",new BigDecimal(60));
		map.put("3562000036862035",new BigDecimal(60));
		map.put("3562000045021317",new BigDecimal(60));
		map.put("3562000040607306",new BigDecimal(60));
		map.put("3562000045076763",new BigDecimal(60));
		map.put("3562000037312376",new BigDecimal(60));
		map.put("3562000038406674",new BigDecimal(60));
		map.put("3562000045158315",new BigDecimal(60));
		map.put("3562000037249864",new BigDecimal(60));
		map.put("3562000037234506",new BigDecimal(60));
		map.put("3562000037824545",new BigDecimal(60));
		map.put("3562000038306964",new BigDecimal(60));
		map.put("3562000038849364",new BigDecimal(60));
		map.put("3562000042571206",new BigDecimal(60));
		map.put("3562000037958063",new BigDecimal(60));
		map.put("3562000044827316",new BigDecimal(59));
		map.put("3562000044746874",new BigDecimal(58));
		map.put("3562000040539915",new BigDecimal(57));
		map.put("3562000045146395",new BigDecimal(57));
		map.put("3562000045146635",new BigDecimal(57));
		map.put("3562000040613546",new BigDecimal(56));
		map.put("3562000042434947",new BigDecimal(55));
		map.put("3562000042820606",new BigDecimal(55));
		map.put("3562000039367734",new BigDecimal(54));
		map.put("3562000037172295",new BigDecimal(54));
		map.put("3562000040531476",new BigDecimal(53));
		map.put("3562000040539175",new BigDecimal(52));
		map.put("3562000041588445",new BigDecimal(52));
		map.put("3562000044308636",new BigDecimal(52));
		map.put("3562000040525085",new BigDecimal(52));
		map.put("3562000040532826",new BigDecimal(52));
		map.put("3562000037801485",new BigDecimal(52));
		map.put("3562000041539954",new BigDecimal(52));
		map.put("3562000040636185",new BigDecimal(52));
		map.put("3562000041388635",new BigDecimal(52));
		map.put("3562000040681794",new BigDecimal(52));
		map.put("3562000038188834",new BigDecimal(52));
		map.put("3562000044790835",new BigDecimal(52));
		map.put("3562000036999193",new BigDecimal(51));
		map.put("3562000037359484",new BigDecimal(50));
		map.put("3562000045040726",new BigDecimal(50));
		map.put("3562000036317745",new BigDecimal(50));
		map.put("3562000037103347",new BigDecimal(50));
		map.put("3562000045048346",new BigDecimal(50));
		map.put("3562000045180625",new BigDecimal(50));
		map.put("3562000040518446",new BigDecimal(50));
		map.put("3562000037954904",new BigDecimal(50));
		map.put("3562000037104196",new BigDecimal(50));
		map.put("3562000045153854",new BigDecimal(49));
		map.put("3562000043670925",new BigDecimal(48));
		map.put("3562000045231037",new BigDecimal(48));
		map.put("3562000043400817",new BigDecimal(48));
		map.put("3562000044999415",new BigDecimal(48));
		map.put("3562000044982694",new BigDecimal(47));
		map.put("3562000045150645",new BigDecimal(47));
		map.put("3562000040525964",new BigDecimal(47));
		map.put("3562000042804726",new BigDecimal(46));
		map.put("3562000042746884",new BigDecimal(44));
		map.put("3562000038050554",new BigDecimal(43));
		map.put("3562000040523646",new BigDecimal(43));
		map.put("3562000042792964",new BigDecimal(42));
		map.put("3562000042982784",new BigDecimal(42));
		map.put("3562000042644795",new BigDecimal(42));
		map.put("3562000045175563",new BigDecimal(42));
		map.put("3562000042955494",new BigDecimal(42));
		map.put("3562000044080546",new BigDecimal(42));
		map.put("3562000042714506",new BigDecimal(42));
		map.put("3562000042412766",new BigDecimal(42));
		map.put("3562000038267254",new BigDecimal(40));
		map.put("3562000038283226",new BigDecimal(40));
		map.put("3562000038274794",new BigDecimal(40));
		map.put("3562000038271654",new BigDecimal(40));
		map.put("3562000038292355",new BigDecimal(40));
		map.put("3562000038279763",new BigDecimal(40));
		map.put("3562000038280925",new BigDecimal(40));
		map.put("3562000045048754",new BigDecimal(40));
		map.put("3562000038277904",new BigDecimal(40));
		map.put("3562000038292406",new BigDecimal(40));
		map.put("3562000038267364",new BigDecimal(40));
		map.put("3562000038270894",new BigDecimal(40));
		map.put("3562000038274915",new BigDecimal(40));
		map.put("3562000038294705",new BigDecimal(40));
		map.put("3562000038268983",new BigDecimal(40));
		map.put("3562000045139336",new BigDecimal(40));
		map.put("3562000038288734",new BigDecimal(40));
		map.put("3562000043907165",new BigDecimal(40));
		map.put("3562000038293236",new BigDecimal(40));
		map.put("3562000038266734",new BigDecimal(40));
		map.put("3562000038274664",new BigDecimal(40));
		map.put("3562000038266783",new BigDecimal(40));
		map.put("3562000038288663",new BigDecimal(40));
		map.put("3562000037495054",new BigDecimal(40));
		map.put("3562000038274994",new BigDecimal(40));
		map.put("3562000038277863",new BigDecimal(40));
		map.put("3562000038266924",new BigDecimal(40));
		map.put("3562000038266614",new BigDecimal(40));
		map.put("3562000038267245",new BigDecimal(40));
		map.put("3562000038274835",new BigDecimal(40));
		map.put("3562000038283185",new BigDecimal(40));
		map.put("3562000038275973",new BigDecimal(40));
		map.put("3562000038277934",new BigDecimal(40));
		map.put("3562000038266753",new BigDecimal(40));
		map.put("3562000038274894",new BigDecimal(40));
		map.put("3562000038279704",new BigDecimal(40));
		map.put("3562000038292236",new BigDecimal(40));
		map.put("3562000038279693",new BigDecimal(40));
		map.put("3562000038275025",new BigDecimal(40));
		map.put("3562000038266544",new BigDecimal(40));
		map.put("3562000037658623",new BigDecimal(40));
		map.put("3562000038274935",new BigDecimal(40));
		map.put("3562000038279634",new BigDecimal(40));
		map.put("3562000038274654",new BigDecimal(40));
		map.put("3562000038280945",new BigDecimal(40));
		map.put("3562000038269005",new BigDecimal(40));
		map.put("3562000038266863",new BigDecimal(40));
		map.put("3562000038274905",new BigDecimal(40));
		map.put("3562000038266824",new BigDecimal(40));
		map.put("3562000038274884",new BigDecimal(40));
		map.put("3562000038274674",new BigDecimal(40));
		map.put("3562000038281684",new BigDecimal(40));
		map.put("3562000038271515",new BigDecimal(40));
		map.put("3562000038279734",new BigDecimal(40));
		map.put("3562000038293346",new BigDecimal(40));
		map.put("3562000042805216",new BigDecimal(40));
		map.put("3562000037724584",new BigDecimal(40));
		map.put("3562000038266814",new BigDecimal(40));
		map.put("3562000038266934",new BigDecimal(40));
		map.put("3562000038267284",new BigDecimal(40));
		map.put("3562000038294694",new BigDecimal(40));
		map.put("3562000038269015",new BigDecimal(40));
		map.put("3562000038293395",new BigDecimal(40));
		map.put("3562000038274495",new BigDecimal(40));
		map.put("3562000045150346",new BigDecimal(40));
		map.put("3562000038155704",new BigDecimal(40));
		map.put("3562000038274974",new BigDecimal(40));
		map.put("3562000038292485",new BigDecimal(40));
		map.put("3562000037673425",new BigDecimal(40));
		map.put("3562000038266744",new BigDecimal(40));
		map.put("3562000038270436",new BigDecimal(40));
		map.put("3562000038275345",new BigDecimal(40));
		map.put("3562000042772954",new BigDecimal(39));
		map.put("3562000042854735",new BigDecimal(37));
		map.put("3562000043999264",new BigDecimal(36));
		map.put("3562000044058016",new BigDecimal(36));
		map.put("3562000044289584",new BigDecimal(36));
		map.put("3562000044110507",new BigDecimal(36));
		map.put("3562000043918055",new BigDecimal(36));
		map.put("3562000044189065",new BigDecimal(36));
		map.put("3562000044061606",new BigDecimal(36));
		map.put("3562000044062585",new BigDecimal(36));
		map.put("3562000044009186",new BigDecimal(36));
		map.put("3562000044097146",new BigDecimal(36));
		map.put("3562000044031018",new BigDecimal(36));
		map.put("3562000044289375",new BigDecimal(36));
		map.put("3562000044043707",new BigDecimal(36));
		map.put("3562000044248585",new BigDecimal(36));
		map.put("3562000044149247",new BigDecimal(36));
		map.put("3562000043990226",new BigDecimal(36));
		map.put("3562000043990285",new BigDecimal(36));
		map.put("3562000043991295",new BigDecimal(36));
		map.put("3562000040554884",new BigDecimal(35));
		map.put("3562000040538835",new BigDecimal(35));
		map.put("3562000040554155",new BigDecimal(32));
		map.put("3562000045050026",new BigDecimal(32));
		map.put("3562000043939545",new BigDecimal(32));
		map.put("3562000045015615",new BigDecimal(31));
		map.put("3562000040535645",new BigDecimal(31));
		map.put("3562000040539416",new BigDecimal(31));
		map.put("3562000036115475",new BigDecimal(30));
		map.put("3562000043949774",new BigDecimal(30));
		map.put("3562000037495235",new BigDecimal(30));
		map.put("3562000037359464",new BigDecimal(30));
		map.put("3562000045149954",new BigDecimal(30));
		map.put("3562000042135276",new BigDecimal(30));
		map.put("3562000037450805",new BigDecimal(30));
		map.put("3562000045118794",new BigDecimal(30));
		map.put("3562000038318206",new BigDecimal(30));
		map.put("3562000037023376",new BigDecimal(29));
		map.put("3562000044876084",new BigDecimal(29));
		map.put("3562000042956274",new BigDecimal(28));
		map.put("3562000044079306",new BigDecimal(28));
		map.put("3562000038918284",new BigDecimal(27));
		map.put("3562000045010606",new BigDecimal(27));
		map.put("3562000040534536",new BigDecimal(27));
		map.put("3562000041211637",new BigDecimal(26));
		map.put("3562000040541007",new BigDecimal(26));
		map.put("3562000040104218",new BigDecimal(26));
		map.put("3562000040420097",new BigDecimal(25));
		map.put("3562000043593036",new BigDecimal(24));
		map.put("3562000043644147",new BigDecimal(21));
		map.put("3562000045156074",new BigDecimal(21));
		map.put("3562000038136615",new BigDecimal(20));
		map.put("3562000042949974",new BigDecimal(20));
		map.put("3562000045073894",new BigDecimal(20));
		map.put("3562000040734317",new BigDecimal(20));
		map.put("3562000043474386",new BigDecimal(20));
		map.put("3562000045033176",new BigDecimal(20));
		map.put("3562000038157953",new BigDecimal(20));
		map.put("3562000037004716",new BigDecimal(20));
		map.put("3562000043918155",new BigDecimal(20));
		map.put("3562000036716205",new BigDecimal(20));
		map.put("3562000037363764",new BigDecimal(20));
		map.put("3562000044980226",new BigDecimal(19));
		map.put("3562000043978804",new BigDecimal(18));
		map.put("3562000042638275",new BigDecimal(18));
		map.put("3562000044295994",new BigDecimal(18));
		map.put("3562000043994336",new BigDecimal(16));
		map.put("3562000043456036",new BigDecimal(16));
		map.put("3562000038512574",new BigDecimal(16));
		map.put("3562000044230087",new BigDecimal(16));
		map.put("3562000037998913",new BigDecimal(15));
		map.put("3562000036862653",new BigDecimal(15));
		map.put("3562000040536146",new BigDecimal(14));
		map.put("3562000040536794",new BigDecimal(13));
		map.put("3562000039896004",new BigDecimal(13));
		map.put("3562000045150974",new BigDecimal(13));
		map.put("3562000038512994",new BigDecimal(12));
		map.put("3562000042956164",new BigDecimal(12));
		map.put("3562000045078863",new BigDecimal(11));
		map.put("3562000042641646",new BigDecimal(11));
		map.put("3562000036920126",new BigDecimal(10));
		map.put("3562000043903555",new BigDecimal(10));
		map.put("3562000038508663",new BigDecimal(10));
		map.put("3562000044055625",new BigDecimal(10));
		map.put("3562000040520616",new BigDecimal(10));
		map.put("3562000036716215",new BigDecimal(10));
		map.put("3562000038283265",new BigDecimal(10));
		map.put("3562000036833185",new BigDecimal(10));
		map.put("3562000036753084",new BigDecimal(10));
		map.put("3562000045047815",new BigDecimal(10));
		map.put("3562000037359405",new BigDecimal(10));
		map.put("3562000044698763",new BigDecimal(9));
		map.put("3562000043520546",new BigDecimal(9));
		map.put("3562000044041817",new BigDecimal(8));
		map.put("3562000044280296",new BigDecimal(8));
		map.put("3562000040524466",new BigDecimal(8));
		map.put("3562000043652954",new BigDecimal(8));
		map.put("3562000039090564",new BigDecimal(6));
		map.put("3562000045136515",new BigDecimal(6));
		map.put("3562000044430727",new BigDecimal(5));
		map.put("3562000041750705",new BigDecimal(5));
		map.put("3562000041428536",new BigDecimal(4));
		map.put("3562000040528406",new BigDecimal(4));
		map.put("3562000045082446",new BigDecimal(4));
		map.put("3562000042955534",new BigDecimal(4));
		map.put("3562000040529455",new BigDecimal(4));
		map.put("3562000040535684",new BigDecimal(3));
		map.put("3562000045088693",new BigDecimal(3));
		map.put("3562000043998744",new BigDecimal(3));
		map.put("3562000042362836",new BigDecimal(2));
		map.put("3562000043688105",new BigDecimal(2));
		map.put("3562000040631865",new BigDecimal(2));
		map.put("3562000041104408",new BigDecimal(2));
		map.put("3562000045159683",new BigDecimal(1));
		map.put("3562000045162316",new BigDecimal(1));
		map.put("3562000040547126",new BigDecimal(1));
		map.put("3562000045150185",new BigDecimal(1));
	}

}
