package com.akucun.account.proxy.common.utils;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.common.enums.ResponseEnum;

import java.util.Objects;

/**
 * 通用返回
 * <AUTHOR>
 */
public class Results {
    public static final Result OK = Result.success();
    public static final Result TEST_USER = Result.error(998, "测试用户已重置");
    public static final Result OFFLINE = Result.error(999, "此功能已下线,请更新最新版本");

    public static Result error(ResponseEnum resp){
        Result result = new Result();
        result.setSuccess(false);
        result.setMessage(resp.getMessage());
        result.setCode(resp.getCode());
        return result;
    }

    public static Result success(){
        Result result = new Result();
        result.setSuccess(true);
        result.setCode(0);
        result.setMessage("成功");
        return result;
    }

    public static Result error(int code, String errorMsg) {
        Result result = new Result();
        result.setSuccess(false);
        result.setMessage(errorMsg);
        result.setCode(code);
        return result;
    }

    public static Result error(String errorMsg) {
        Result result = new Result();
        result.setSuccess(false);
        result.setCode(ResponseEnum.SYSTEM_ERROR.getCode());
        result.setMessage(errorMsg);
        return result;
    }

    public static Result error(){
        Result result = new Result();
        result.setSuccess(false);
        result.setMessage(ResponseEnum.SYSTEM_ERROR.getMessage());
        result.setCode(ResponseEnum.SYSTEM_ERROR.getCode());
        return result;
    }

    public static boolean checkResult(Result result){
        return Objects.nonNull(result) && result.getSuccess() && Objects.nonNull(result.getData());
    }

    public static boolean checkFpsResult(com.akucun.fps.common.entity.Result result){
        return Objects.nonNull(result) && result.isSuccess();
    }


}
