package com.akucun.account.proxy.common.utils;

import com.akucun.account.proxy.common.constant.CommonConstants;
import com.mengxiang.base.common.log.Logger;

import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.List;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;

/**
 * <AUTHOR>
 * @date 2021/01/04 20:39
 */
public class AESUtils {
    /**
     * 加密
     * @param content
     * @param key
     * @return
     * @throws Exception
     */
    public static String encrypt(String content, String key)  {
        try {
            Cipher cipher = Cipher.getInstance("AES");
            cipher.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(generateKey(key).getEncoded(), "AES"));
            byte[] b = cipher.doFinal(content.getBytes("UTF-8"));

            if (b == null || b.length == 0) {
                return "";
            }

            StringBuffer buf = new StringBuffer();
            for (int i = 0; i < b.length; i++) {

                String hex = Integer.toHexString(b[i] & 0xFF);

                if (hex.length() == 1) {
                    hex = '0' + hex;
                }
                buf.append(hex.toUpperCase());
            }
            return buf.toString();
        } catch (Exception e) {
            Logger.error("AES加密异常，异常信息：{}", e);
        }
        return null;
    }

    /**
     * 解密
     * @param content
     * @param key
     * @return
     * @throws Exception
     */
    public static String decrypt(String content, String key) {
        try {
            if (content == null || content.length() == 0) {
                return null;
            }

            byte[] b = new byte[content.length() / 2];
            for (int i = 0; i < content.length() / 2; i++) {
                int high = Integer.parseInt(content.substring(i * 2, i * 2 + 1), 16);
                int low = Integer.parseInt(content.substring(i * 2 + 1, i * 2 + 2), 16);
                b[i] = (byte) (high * 16 + low);
            }

            Cipher cipher = Cipher.getInstance("AES");
            cipher.init(Cipher.DECRYPT_MODE, new SecretKeySpec(generateKey(key).getEncoded(), "AES"));

            return new String(cipher.doFinal(b));
        } catch (Exception e) {
            Logger.error("AES解密异常，异常信息：{}", e);
        }
        return null;
    }

    private static SecretKey generateKey(String key) throws NoSuchAlgorithmException {
        SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG");
        KeyGenerator kg = KeyGenerator.getInstance("AES");
        secureRandom.setSeed(key.getBytes());
        kg.init(128, secureRandom);
        return kg.generateKey();
    }

    public static void main(String[] args) {
        List<String> list = new ArrayList<>();
        list.add("QQ12WQQE123DE21");
        list.add("GG1231SFSWER1213R");
        list.add("HH12QIER12313SFSF5");
        list.add("GETWE1212141SRRR");
        for(String code : list) {
            System.out.println(encrypt(code, CommonConstants.VIP_CARD_AES_KEY));
        }
    }
}
