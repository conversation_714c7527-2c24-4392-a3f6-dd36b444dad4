package com.akucun.account.proxy.common.enums;

/**
 * 平安开户会员属性类型：
 * 00-普通子账户（默认）
 * SH-商户子账户
 */
public enum AccountPropertyType {

    GENERAL("00", "普通子账户（默认）"),
    MERCHANTS("SH", "商户子账户");

    private String code;
    private String desc;

    AccountPropertyType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}

