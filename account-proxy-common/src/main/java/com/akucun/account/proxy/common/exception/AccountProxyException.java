package com.akucun.account.proxy.common.exception;

import com.akucun.account.proxy.common.enums.ResponseEnum;

/**
 * @Author: silei
 * @Date: 2020/8/27
 * @desc:
 */
public class AccountProxyException extends RuntimeException {

    private static final long serialVersionUID = 6673224725842963908L;
    //错误码
    private Integer errorCode;
    //错误描述
    private String errorMsg;

    public AccountProxyException(ResponseEnum responseEnum) {
        super(responseEnum.getMessage());
        this.errorCode = responseEnum.getCode();
        this.errorMsg = responseEnum.getMessage();
    }

    public AccountProxyException(ResponseEnum responseEnum, String errorDetail) {
        super(responseEnum.getMessage() + "," + errorDetail);
        this.errorCode = responseEnum.getCode();
        this.errorMsg = responseEnum.getMessage() + "," + errorDetail;
    }

    public AccountProxyException(Integer errorCode, String errorMsg) {
        super(errorMsg);
        this.errorCode = errorCode;
        this.errorMsg = errorMsg;
    }

    public AccountProxyException(String errorMsg) {
        super(errorMsg);
        this.errorMsg = errorMsg;
    }

    public Integer getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(Integer errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }
}
