package com.akucun.account.proxy.common.enums;

import com.akucun.fps.common.constants.MEnum;

/**
 * @Author: silei
 * @Date: 2020/12/8
 * @desc:
 */
public class PostActionExecStatus extends MEnum<PostActionExecStatus> {
    private static final long serialVersionUID = -125126236336113344L;

    public static final PostActionExecStatus DEFAULT = (PostActionExecStatus)create("DEFAULT", 0, "初始状态");
    public static final PostActionExecStatus EXECUTE = (PostActionExecStatus)create("EXECUTE", 1, "可执行状态");
    public static final PostActionExecStatus DELETE = (PostActionExecStatus)create("DELETE", -1, "删除");
}
