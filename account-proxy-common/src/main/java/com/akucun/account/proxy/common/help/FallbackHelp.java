package com.akucun.account.proxy.common.help;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

import org.apache.commons.collections4.CollectionUtils;

import com.akucun.account.proxy.common.constant.CustomerType;
import com.akucun.account.proxy.common.utils.HttpClient;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import cn.hutool.core.io.FileUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class FallbackHelp {
	
	public static Map<String,BigDecimal> totalAmountMap = new ConcurrentHashMap<>();
	
	public static Map<String,BigDecimal> map = new ConcurrentHashMap<>();
	
	public static Map<String,BigDecimal> spring = new ConcurrentHashMap<>();
	
	public static Map<String,BigDecimal> work = new ConcurrentHashMap<>();
	
	
	
	private static Map<String,BigDecimal> currentAmountMap = new ConcurrentHashMap<>();
	
	private static BigDecimal total = new BigDecimal(0);
	
	static {
		
		totalAmountMap.put("****************",new BigDecimal(*********));
		totalAmountMap.put("****************",new BigDecimal(*********));
		totalAmountMap.put("****************",new BigDecimal(********));
		totalAmountMap.put("****************",new BigDecimal(********));
		totalAmountMap.put("****************",new BigDecimal(11892603));
		totalAmountMap.put("3562000042498645",new BigDecimal(9433040));
		totalAmountMap.put("3562000044983535",new BigDecimal(9225720));
		totalAmountMap.put("3562000045009564",new BigDecimal(8578713));
		totalAmountMap.put("3562000040368545",new BigDecimal(8311682));
		totalAmountMap.put("3562000043857863",new BigDecimal(7081930));
		totalAmountMap.put("3562000042769484",new BigDecimal(5702639));
		totalAmountMap.put("3562000040418546",new BigDecimal(5651005));
		totalAmountMap.put("3562000043416946",new BigDecimal(4867556));
		totalAmountMap.put("3562000044991436",new BigDecimal(4731929));
		totalAmountMap.put("3562000042769474",new BigDecimal(4728284));
		totalAmountMap.put("3562000042769534",new BigDecimal(4491392));
		totalAmountMap.put("3562000042769504",new BigDecimal(4424186));
		totalAmountMap.put("3562000039095683",new BigDecimal(4348221));
		totalAmountMap.put("3562000045111995",new BigDecimal(4286593));
		totalAmountMap.put("3562000042770564",new BigDecimal(4275923));
		totalAmountMap.put("3562000045151246",new BigDecimal(4213621));
		totalAmountMap.put("3562000044661525",new BigDecimal(3996778));
		totalAmountMap.put("3562000045076783",new BigDecimal(3987096));
		totalAmountMap.put("3562000040491437",new BigDecimal(3942753));
		totalAmountMap.put("3562000043819336",new BigDecimal(3818710));
		totalAmountMap.put("3562000044648725",new BigDecimal(3775619));
		totalAmountMap.put("3562000044988435",new BigDecimal(3621129));
		totalAmountMap.put("3562000043388436",new BigDecimal(3369087));
		totalAmountMap.put("3562000044977553",new BigDecimal(3230096));
		totalAmountMap.put("3562000042426137",new BigDecimal(3148905));
		totalAmountMap.put("3562000045119106",new BigDecimal(3064128));
		totalAmountMap.put("3562000044660935",new BigDecimal(3063468));
		totalAmountMap.put("3562000045093106",new BigDecimal(3058406));
		totalAmountMap.put("3562000044990835",new BigDecimal(2920663));
		totalAmountMap.put("3562000045108945",new BigDecimal(2895195));
		totalAmountMap.put("3562000044407286",new BigDecimal(2842391));
		totalAmountMap.put("3562000045111926",new BigDecimal(2692333));
		totalAmountMap.put("3562000045112037",new BigDecimal(2673580));
		totalAmountMap.put("3562000045112536",new BigDecimal(2624308));
		totalAmountMap.put("3562000044977824",new BigDecimal(2488781));
		totalAmountMap.put("3562000045108774",new BigDecimal(2432696));
		totalAmountMap.put("3562000045112076",new BigDecimal(2412080));
		totalAmountMap.put("3562000044989834",new BigDecimal(2400791));
		totalAmountMap.put("3562000045104447",new BigDecimal(2353400));
		totalAmountMap.put("3562000045112007",new BigDecimal(2340385));
		totalAmountMap.put("3562000042821237",new BigDecimal(2322963));
		totalAmountMap.put("3562000043889934",new BigDecimal(2289851));
		totalAmountMap.put("3562000045118126",new BigDecimal(2175905));
		totalAmountMap.put("3562000045112017",new BigDecimal(2166655));
		totalAmountMap.put("3562000045119915",new BigDecimal(2152270));
		totalAmountMap.put("3562000045174446",new BigDecimal(2139200));
		totalAmountMap.put("3562000045085145",new BigDecimal(2135586));
		totalAmountMap.put("3562000036114017",new BigDecimal(2085657));
		totalAmountMap.put("3562000045086384",new BigDecimal(2028049));
		totalAmountMap.put("3562000045118116",new BigDecimal(2000429));
		totalAmountMap.put("3562000045066494",new BigDecimal(1998789));
		totalAmountMap.put("3562000045111975",new BigDecimal(1969314));
		totalAmountMap.put("3562000045125864",new BigDecimal(1953996));
		totalAmountMap.put("3562000044979335",new BigDecimal(1934867));
		totalAmountMap.put("3562000045108674",new BigDecimal(1871525));
		totalAmountMap.put("3562000044770246",new BigDecimal(1845688));
		totalAmountMap.put("3562000045118146",new BigDecimal(1838559));
		totalAmountMap.put("3562000043388446",new BigDecimal(1751654));
		totalAmountMap.put("3562000045069863",new BigDecimal(1667696));
		totalAmountMap.put("3562000045111965",new BigDecimal(1622171));
		totalAmountMap.put("3562000045109915",new BigDecimal(1577807));
		totalAmountMap.put("3562000045068154",new BigDecimal(1571865));
		totalAmountMap.put("3562000045011565",new BigDecimal(1436351));
		totalAmountMap.put("3562000043812147",new BigDecimal(1429362));
		totalAmountMap.put("3562000045097553",new BigDecimal(1397357));
		totalAmountMap.put("3562000044982226",new BigDecimal(1395214));
		totalAmountMap.put("3562000042866514",new BigDecimal(1312542));
		totalAmountMap.put("3562000045023176",new BigDecimal(1266111));
		totalAmountMap.put("3562000044972226",new BigDecimal(1249069));
		totalAmountMap.put("3562000044977814",new BigDecimal(1247448));
		totalAmountMap.put("3562000038635135",new BigDecimal(1237004));
		totalAmountMap.put("3562000045086354",new BigDecimal(1236000));
		totalAmountMap.put("3562000044517395",new BigDecimal(1202918));
		totalAmountMap.put("3562000035963145",new BigDecimal(1184562));
		totalAmountMap.put("3562000045212256",new BigDecimal(1125353));
		totalAmountMap.put("3562000042131377",new BigDecimal(1100599));
		totalAmountMap.put("3562000036870264",new BigDecimal(1081593));
		totalAmountMap.put("3562000044990554",new BigDecimal(1060740));
		totalAmountMap.put("3562000045004585",new BigDecimal(1025004));
		totalAmountMap.put("3562000045120027",new BigDecimal(1009511));
		totalAmountMap.put("3562000045087993",new BigDecimal(1003575));
		totalAmountMap.put("3562000043692815",new BigDecimal(983268));
		totalAmountMap.put("3562000045111985",new BigDecimal(852310));
		totalAmountMap.put("3562000044450526",new BigDecimal(833093));
		totalAmountMap.put("3562000045076863",new BigDecimal(809567));
		totalAmountMap.put("3562000040491546",new BigDecimal(809024));
		totalAmountMap.put("3562000044980095",new BigDecimal(780990));
		totalAmountMap.put("3562000044991654",new BigDecimal(780811));
		totalAmountMap.put("3562000037368494",new BigDecimal(768052));
		totalAmountMap.put("3562000043078116",new BigDecimal(765804));
		totalAmountMap.put("3562000044971774",new BigDecimal(738755));
		totalAmountMap.put("3562000044432038",new BigDecimal(729473));
		totalAmountMap.put("3562000044980006",new BigDecimal(727349));
		totalAmountMap.put("3562000043633317",new BigDecimal(695982));
		totalAmountMap.put("3562000045054674",new BigDecimal(695933));
		totalAmountMap.put("3562000036114147",new BigDecimal(679649));
		totalAmountMap.put("3562000038683145",new BigDecimal(674691));
		totalAmountMap.put("3562000042506446",new BigDecimal(671806));
		totalAmountMap.put("3562000040520486",new BigDecimal(642603));
		totalAmountMap.put("3562000040538316",new BigDecimal(639699));
		totalAmountMap.put("3562000045108645",new BigDecimal(630030));
		totalAmountMap.put("3562000044365465",new BigDecimal(626849));
		totalAmountMap.put("3562000045149095",new BigDecimal(624894));
		totalAmountMap.put("3562000045138116",new BigDecimal(617716));
		totalAmountMap.put("3562000045109175",new BigDecimal(616284));
		totalAmountMap.put("3562000044609006",new BigDecimal(588027));
		totalAmountMap.put("3562000045166983",new BigDecimal(586790));
		totalAmountMap.put("3562000045084106",new BigDecimal(585659));
		totalAmountMap.put("3562000045023865",new BigDecimal(577925));
		totalAmountMap.put("3562000045011685",new BigDecimal(570442));
		totalAmountMap.put("3562000040540746",new BigDecimal(557970));
		totalAmountMap.put("3562000044956714",new BigDecimal(548872));
		totalAmountMap.put("3562000045156384",new BigDecimal(528900));
		totalAmountMap.put("3562000040543706",new BigDecimal(525067));
		totalAmountMap.put("3562000042511636",new BigDecimal(524236));
		totalAmountMap.put("3562000045008974",new BigDecimal(510310));
		totalAmountMap.put("3562000044978484",new BigDecimal(494998));
		totalAmountMap.put("3562000040520995",new BigDecimal(486081));
		totalAmountMap.put("3562000040521795",new BigDecimal(482656));
		totalAmountMap.put("3562000036113826",new BigDecimal(480268));
		totalAmountMap.put("3562000045090874",new BigDecimal(472419));
		totalAmountMap.put("3562000045037554",new BigDecimal(469163));
		totalAmountMap.put("3562000042628255",new BigDecimal(463656));
		totalAmountMap.put("3562000036996044",new BigDecimal(459073));
		totalAmountMap.put("3562000044810027",new BigDecimal(453999));
		totalAmountMap.put("3562000036113875",new BigDecimal(453245));
		totalAmountMap.put("3562000036760783",new BigDecimal(440146));
		totalAmountMap.put("3562000045134256",new BigDecimal(421490));
		totalAmountMap.put("3562000045112595",new BigDecimal(409845));
		totalAmountMap.put("3562000036114755",new BigDecimal(401692));
		totalAmountMap.put("3562000040556084",new BigDecimal(399033));
		totalAmountMap.put("3562000044987814",new BigDecimal(398080));
		totalAmountMap.put("3562000043567573",new BigDecimal(392942));
		totalAmountMap.put("3562000045099704",new BigDecimal(392842));
		totalAmountMap.put("3562000039513805",new BigDecimal(391755));
		totalAmountMap.put("3562000045149295",new BigDecimal(375585));
		totalAmountMap.put("3562000044276615",new BigDecimal(374572));
		totalAmountMap.put("3562000040527794",new BigDecimal(367281));
		totalAmountMap.put("3562000042429356",new BigDecimal(364910));
		totalAmountMap.put("3562000045106465",new BigDecimal(364269));
		totalAmountMap.put("3562000043008247",new BigDecimal(362405));
		totalAmountMap.put("3562000040519116",new BigDecimal(360253));
		totalAmountMap.put("3562000036463036",new BigDecimal(355992));
		totalAmountMap.put("3562000042714486",new BigDecimal(354807));
		totalAmountMap.put("3562000040548694",new BigDecimal(352723));
		totalAmountMap.put("3562000040547206",new BigDecimal(343675));
		totalAmountMap.put("3562000045065305",new BigDecimal(332947));
		totalAmountMap.put("3562000043440238",new BigDecimal(332881));
		totalAmountMap.put("3562000045180535",new BigDecimal(330110));
		totalAmountMap.put("3562000036765263",new BigDecimal(326545));
		totalAmountMap.put("3562000040546436",new BigDecimal(325374));
		totalAmountMap.put("3562000044775274",new BigDecimal(323500));
		totalAmountMap.put("3562000045111946",new BigDecimal(311010));
		totalAmountMap.put("3562000044992584",new BigDecimal(310204));
		totalAmountMap.put("3562000045077534",new BigDecimal(304661));
		totalAmountMap.put("3562000044999374",new BigDecimal(300577));
		totalAmountMap.put("3562000040537265",new BigDecimal(296268));
		totalAmountMap.put("3562000040517894",new BigDecimal(291133));
		totalAmountMap.put("3562000045138515",new BigDecimal(289149));
		totalAmountMap.put("3562000042437376",new BigDecimal(286208));
		totalAmountMap.put("3562000045198364",new BigDecimal(283400));
		totalAmountMap.put("3562000040534765",new BigDecimal(281757));
		totalAmountMap.put("3562000040528146",new BigDecimal(280782));
		totalAmountMap.put("3562000043763905",new BigDecimal(274048));
		totalAmountMap.put("3562000045011936",new BigDecimal(273758));
		totalAmountMap.put("3562000040536825",new BigDecimal(272719));
		totalAmountMap.put("3562000045125654",new BigDecimal(270009));
		totalAmountMap.put("3562000045078154",new BigDecimal(270000));
		totalAmountMap.put("3562000038292416",new BigDecimal(268475));
		totalAmountMap.put("3562000045133516",new BigDecimal(268033));
		totalAmountMap.put("3562000040525605",new BigDecimal(266509));
		totalAmountMap.put("3562000040523516",new BigDecimal(266248));
		totalAmountMap.put("3562000040553794",new BigDecimal(265407));
		totalAmountMap.put("3562000040536485",new BigDecimal(264717));
		totalAmountMap.put("3562000044165735",new BigDecimal(264611));
		totalAmountMap.put("3562000044995753",new BigDecimal(263780));
		totalAmountMap.put("3562000041614575",new BigDecimal(260102));
		totalAmountMap.put("3562000036114166",new BigDecimal(259056));
		totalAmountMap.put("3562000036113685",new BigDecimal(258090));
		totalAmountMap.put("3562000042778284",new BigDecimal(257569));
		totalAmountMap.put("3562000042511626",new BigDecimal(255973));
		totalAmountMap.put("3562000040524227",new BigDecimal(250427));
		totalAmountMap.put("3562000040543965",new BigDecimal(250398));
		totalAmountMap.put("3562000036113975",new BigDecimal(249101));
		totalAmountMap.put("3562000043404167",new BigDecimal(246847));
		totalAmountMap.put("3562000044678734",new BigDecimal(245473));
		totalAmountMap.put("3562000040540427",new BigDecimal(243397));
		totalAmountMap.put("3562000040552326",new BigDecimal(243032));
		totalAmountMap.put("3562000045106495",new BigDecimal(241690));
		totalAmountMap.put("3562000036337825",new BigDecimal(240588));
		totalAmountMap.put("3562000040547246",new BigDecimal(240572));
		totalAmountMap.put("3562000045170175",new BigDecimal(240100));
		totalAmountMap.put("3562000041650864",new BigDecimal(234399));
		totalAmountMap.put("3562000040522156",new BigDecimal(232120));
		totalAmountMap.put("3562000040092906",new BigDecimal(232000));
		totalAmountMap.put("3562000045172954",new BigDecimal(230133));
		totalAmountMap.put("3562000045094085",new BigDecimal(229904));
		totalAmountMap.put("3562000040542176",new BigDecimal(229125));
		totalAmountMap.put("3562000040538864",new BigDecimal(228993));
		totalAmountMap.put("3562000045124965",new BigDecimal(225458));
		totalAmountMap.put("3562000040523795",new BigDecimal(225416));
		totalAmountMap.put("3562000040548006",new BigDecimal(225165));
		totalAmountMap.put("3562000042575145",new BigDecimal(224000));
		totalAmountMap.put("3562000036114736",new BigDecimal(221908));
		totalAmountMap.put("3562000045127984",new BigDecimal(221800));
		totalAmountMap.put("3562000040520795",new BigDecimal(221768));
		totalAmountMap.put("3562000040530186",new BigDecimal(215543));
		totalAmountMap.put("3562000040518535",new BigDecimal(214564));
		totalAmountMap.put("3562000041261816",new BigDecimal(214303));
		totalAmountMap.put("3562000040528416",new BigDecimal(214043));
		totalAmountMap.put("3562000045107925",new BigDecimal(213938));
		totalAmountMap.put("3562000042890446",new BigDecimal(213829));
		totalAmountMap.put("3562000044186754",new BigDecimal(213300));
		totalAmountMap.put("3562000044454437",new BigDecimal(212356));
		totalAmountMap.put("3562000043348047",new BigDecimal(211266));
		totalAmountMap.put("3562000040526505",new BigDecimal(211103));
		totalAmountMap.put("3562000044982645",new BigDecimal(210078));
		totalAmountMap.put("3562000040543865",new BigDecimal(210065));
		totalAmountMap.put("3562000045055045",new BigDecimal(209949));
		totalAmountMap.put("3562000040536016",new BigDecimal(207100));
		totalAmountMap.put("3562000045005036",new BigDecimal(207023));
		totalAmountMap.put("3562000036114066",new BigDecimal(204840));
		totalAmountMap.put("3562000040555514",new BigDecimal(204618));
		totalAmountMap.put("3562000040547784",new BigDecimal(204031));
		totalAmountMap.put("3562000045002007",new BigDecimal(204000));
		totalAmountMap.put("3562000038199583",new BigDecimal(203781));
		totalAmountMap.put("3562000040532875",new BigDecimal(203611));
		totalAmountMap.put("3562000044787205",new BigDecimal(201245));
		totalAmountMap.put("3562000040519864",new BigDecimal(201142));
		totalAmountMap.put("3562000040519246",new BigDecimal(201052));
		totalAmountMap.put("3562000042727945",new BigDecimal(200892));
		totalAmountMap.put("3562000036119246",new BigDecimal(200660));
		totalAmountMap.put("3562000040522356",new BigDecimal(199459));
		totalAmountMap.put("3562000040547175",new BigDecimal(198739));
		totalAmountMap.put("3562000040543176",new BigDecimal(197360));
		totalAmountMap.put("3562000040532256",new BigDecimal(196014));
		totalAmountMap.put("3562000036114855",new BigDecimal(195910));
		totalAmountMap.put("3562000040552095",new BigDecimal(195017));
		totalAmountMap.put("3562000040533506",new BigDecimal(191823));
		totalAmountMap.put("3562000040542486",new BigDecimal(191701));
		totalAmountMap.put("3562000040522855",new BigDecimal(190479));
		totalAmountMap.put("3562000040550505",new BigDecimal(190422));
		totalAmountMap.put("3562000040524655",new BigDecimal(189841));
		totalAmountMap.put("3562000040518584",new BigDecimal(189668));
		totalAmountMap.put("3562000040538925",new BigDecimal(189651));
		totalAmountMap.put("3562000040533616",new BigDecimal(189648));
		totalAmountMap.put("3562000045171365",new BigDecimal(188800));
		totalAmountMap.put("3562000040548155",new BigDecimal(188262));
		totalAmountMap.put("3562000040537545",new BigDecimal(187796));
		totalAmountMap.put("3562000040547006",new BigDecimal(187741));
		totalAmountMap.put("3562000044996274",new BigDecimal(186923));
		totalAmountMap.put("3562000040538295",new BigDecimal(186358));
		totalAmountMap.put("3562000040524795",new BigDecimal(185415));
		totalAmountMap.put("3562000040550306",new BigDecimal(180938));
		totalAmountMap.put("3562000045056315",new BigDecimal(180200));
		totalAmountMap.put("3562000045030007",new BigDecimal(179741));
		totalAmountMap.put("3562000040535994",new BigDecimal(177885));
		totalAmountMap.put("3562000044995274",new BigDecimal(176500));
		totalAmountMap.put("3562000040544366",new BigDecimal(176101));
		totalAmountMap.put("3562000040526574",new BigDecimal(175545));
		totalAmountMap.put("3562000045135805",new BigDecimal(175410));
		totalAmountMap.put("3562000045039085",new BigDecimal(175246));
		totalAmountMap.put("3562000045170355",new BigDecimal(175138));
		totalAmountMap.put("3562000040522086",new BigDecimal(174477));
		totalAmountMap.put("3562000040525465",new BigDecimal(174316));
		totalAmountMap.put("3562000043439955",new BigDecimal(173618));
		totalAmountMap.put("3562000045148874",new BigDecimal(173360));
		totalAmountMap.put("3562000040410008",new BigDecimal(172683));
		totalAmountMap.put("3562000042802317",new BigDecimal(170773));
		totalAmountMap.put("3562000040521007",new BigDecimal(170660));
		totalAmountMap.put("3562000040543736",new BigDecimal(170039));
		totalAmountMap.put("3562000044785354",new BigDecimal(169375));
		totalAmountMap.put("3562000040533347",new BigDecimal(168793));
		totalAmountMap.put("3562000040535075",new BigDecimal(168655));
		totalAmountMap.put("3562000040522196",new BigDecimal(168654));
		totalAmountMap.put("3562000040523855",new BigDecimal(168518));
		totalAmountMap.put("3562000040534675",new BigDecimal(168467));
		totalAmountMap.put("3562000045115564",new BigDecimal(168109));
		totalAmountMap.put("3562000040531376",new BigDecimal(165994));
		totalAmountMap.put("3562000036115355",new BigDecimal(165470));
		totalAmountMap.put("3562000040524027",new BigDecimal(165199));
		totalAmountMap.put("3562000036931654",new BigDecimal(164210));
		totalAmountMap.put("3562000045080126",new BigDecimal(163886));
		totalAmountMap.put("3562000040545116",new BigDecimal(162629));
		totalAmountMap.put("3562000041000937",new BigDecimal(161983));
		totalAmountMap.put("3562000040526146",new BigDecimal(160378));
		totalAmountMap.put("3562000045106475",new BigDecimal(158965));
		totalAmountMap.put("3562000043416366",new BigDecimal(158565));
		totalAmountMap.put("3562000040537784",new BigDecimal(158443));
		totalAmountMap.put("3562000040531117",new BigDecimal(157715));
		totalAmountMap.put("3562000040517815",new BigDecimal(157690));
		totalAmountMap.put("3562000036113865",new BigDecimal(157680));
		totalAmountMap.put("3562000040530975",new BigDecimal(157383));
		totalAmountMap.put("3562000040522076",new BigDecimal(157251));
		totalAmountMap.put("3562000037032846",new BigDecimal(157075));
		totalAmountMap.put("3562000040525664",new BigDecimal(156831));
		totalAmountMap.put("3562000040540936",new BigDecimal(155562));
		totalAmountMap.put("3562000040527175",new BigDecimal(155532));
		totalAmountMap.put("3562000040546146",new BigDecimal(155257));
		totalAmountMap.put("3562000040538515",new BigDecimal(155247));
		totalAmountMap.put("3562000040529935",new BigDecimal(154118));
		totalAmountMap.put("3562000040549136",new BigDecimal(153888));
		totalAmountMap.put("3562000045212626",new BigDecimal(153800));
		totalAmountMap.put("3562000040533626",new BigDecimal(153429));
		totalAmountMap.put("3562000040528385",new BigDecimal(153011));
		totalAmountMap.put("3562000042431756",new BigDecimal(152920));
		totalAmountMap.put("3562000040520775",new BigDecimal(152570));
		totalAmountMap.put("3562000036116026",new BigDecimal(152410));
		totalAmountMap.put("3562000040547026",new BigDecimal(152089));
		totalAmountMap.put("3562000038318165",new BigDecimal(151236));
		totalAmountMap.put("3562000037675943",new BigDecimal(150264));
		totalAmountMap.put("3562000040121827",new BigDecimal(149415));
		totalAmountMap.put("3562000040518085",new BigDecimal(149404));
		totalAmountMap.put("3562000041613675",new BigDecimal(149241));
		totalAmountMap.put("3562000045094165",new BigDecimal(148862));
		totalAmountMap.put("3562000041271096",new BigDecimal(148771));
		totalAmountMap.put("3562000045086534",new BigDecimal(148500));
		totalAmountMap.put("3562000040531086",new BigDecimal(148127));
		totalAmountMap.put("3562000040550385",new BigDecimal(148052));
		totalAmountMap.put("3562000045066604",new BigDecimal(147715));
		totalAmountMap.put("3562000038564544",new BigDecimal(147146));
		totalAmountMap.put("3562000040521047",new BigDecimal(147021));
		totalAmountMap.put("3562000040534855",new BigDecimal(146826));
		totalAmountMap.put("3562000043104837",new BigDecimal(146500));
		totalAmountMap.put("3562000040723985",new BigDecimal(146323));
		totalAmountMap.put("3562000040529165",new BigDecimal(145373));
		totalAmountMap.put("3562000045132846",new BigDecimal(145225));
		totalAmountMap.put("3562000036436016",new BigDecimal(144260));
		totalAmountMap.put("3562000043841137",new BigDecimal(143435));
		totalAmountMap.put("3562000040540516",new BigDecimal(143337));
		totalAmountMap.put("3562000040544665",new BigDecimal(143107));
		totalAmountMap.put("3562000040536854",new BigDecimal(143018));
		totalAmountMap.put("3562000040533736",new BigDecimal(142897));
		totalAmountMap.put("3562000040525754",new BigDecimal(142788));
		totalAmountMap.put("3562000040543207",new BigDecimal(142026));
		totalAmountMap.put("3562000040543895",new BigDecimal(141767));
		totalAmountMap.put("3562000041510407",new BigDecimal(141131));
		totalAmountMap.put("3562000036114785",new BigDecimal(140452));
		totalAmountMap.put("3562000037778004",new BigDecimal(139928));
		totalAmountMap.put("3562000040524826",new BigDecimal(139672));
		totalAmountMap.put("3562000040543906",new BigDecimal(139588));
		totalAmountMap.put("3562000036116016",new BigDecimal(139526));
		totalAmountMap.put("3562000045132366",new BigDecimal(138615));
		totalAmountMap.put("3562000044989853",new BigDecimal(138611));
		totalAmountMap.put("3562000045135326",new BigDecimal(138390));
		totalAmountMap.put("3562000045132327",new BigDecimal(138345));
		totalAmountMap.put("3562000040547185",new BigDecimal(137691));
		totalAmountMap.put("3562000036115446",new BigDecimal(137427));
		totalAmountMap.put("3562000040548026",new BigDecimal(136840));
		totalAmountMap.put("3562000044990326",new BigDecimal(136617));
		totalAmountMap.put("3562000040547615",new BigDecimal(135916));
		totalAmountMap.put("3562000045129006",new BigDecimal(135424));
		totalAmountMap.put("3562000040534575",new BigDecimal(134255));
		totalAmountMap.put("3562000040522476",new BigDecimal(133940));
		totalAmountMap.put("3562000041418047",new BigDecimal(133742));
		totalAmountMap.put("3562000045126085",new BigDecimal(132312));
		totalAmountMap.put("3562000040518635",new BigDecimal(132203));
		totalAmountMap.put("3562000040536275",new BigDecimal(131847));
		totalAmountMap.put("3562000042850505",new BigDecimal(131349));
		totalAmountMap.put("3562000036114366",new BigDecimal(131221));
		totalAmountMap.put("3562000045092635",new BigDecimal(130630));
		totalAmountMap.put("3562000040537864",new BigDecimal(130281));
		totalAmountMap.put("3562000040521317",new BigDecimal(130086));
		totalAmountMap.put("3562000040550664",new BigDecimal(129187));
		totalAmountMap.put("3562000040531007",new BigDecimal(129156));
		totalAmountMap.put("3562000040552036",new BigDecimal(128946));
		totalAmountMap.put("3562000040528984",new BigDecimal(128085));
		totalAmountMap.put("3562000040531855",new BigDecimal(127865));
		totalAmountMap.put("3562000040553275",new BigDecimal(127468));
		totalAmountMap.put("3562000045126515",new BigDecimal(127311));
		totalAmountMap.put("3562000040545455",new BigDecimal(127004));
		totalAmountMap.put("3562000043118765",new BigDecimal(126800));
		totalAmountMap.put("3562000041676074",new BigDecimal(126789));
		totalAmountMap.put("3562000040553106",new BigDecimal(126686));
		totalAmountMap.put("3562000044953146",new BigDecimal(126269));
		totalAmountMap.put("3562000040538545",new BigDecimal(126203));
		totalAmountMap.put("3562000040521955",new BigDecimal(126186));
		totalAmountMap.put("3562000036113836",new BigDecimal(126180));
		totalAmountMap.put("3562000040545664",new BigDecimal(125834));
		totalAmountMap.put("3562000040545336",new BigDecimal(125630));
		totalAmountMap.put("3562000038810455",new BigDecimal(125031));
		totalAmountMap.put("3562000041412547",new BigDecimal(124747));
		totalAmountMap.put("3562000040553645",new BigDecimal(124386));
		totalAmountMap.put("3562000040552055",new BigDecimal(123029));
		totalAmountMap.put("3562000036113955",new BigDecimal(122604));
		totalAmountMap.put("3562000040523685",new BigDecimal(122495));
		totalAmountMap.put("3562000040545285",new BigDecimal(121764));
		totalAmountMap.put("3562000040535984",new BigDecimal(121678));
		totalAmountMap.put("3562000044948535",new BigDecimal(121222));
		totalAmountMap.put("3562000040547684",new BigDecimal(121107));
		totalAmountMap.put("3562000037345884",new BigDecimal(121043));
		totalAmountMap.put("3562000040543147",new BigDecimal(120617));
		totalAmountMap.put("3562000040537446",new BigDecimal(120328));
		totalAmountMap.put("3562000040526036",new BigDecimal(120232));
		totalAmountMap.put("3562000040527306",new BigDecimal(119910));
		totalAmountMap.put("3562000045136594",new BigDecimal(119777));
		totalAmountMap.put("3562000040551255",new BigDecimal(119754));
		totalAmountMap.put("3562000040528674",new BigDecimal(119553));
		totalAmountMap.put("3562000040524096",new BigDecimal(118985));
		totalAmountMap.put("3562000040530476",new BigDecimal(118929));
		totalAmountMap.put("3562000041418846",new BigDecimal(118032));
		totalAmountMap.put("3562000041612427",new BigDecimal(117566));
		totalAmountMap.put("3562000040531506",new BigDecimal(117284));
		totalAmountMap.put("3562000040542227",new BigDecimal(116910));
		totalAmountMap.put("3562000040520975",new BigDecimal(116637));
		totalAmountMap.put("3562000036317605",new BigDecimal(116360));
		totalAmountMap.put("3562000040524785",new BigDecimal(115910));
		totalAmountMap.put("3562000043129096",new BigDecimal(115430));
		totalAmountMap.put("3562000041647615",new BigDecimal(114932));
		totalAmountMap.put("3562000040522186",new BigDecimal(114687));
		totalAmountMap.put("3562000040551535",new BigDecimal(114471));
		totalAmountMap.put("3562000040548545",new BigDecimal(114083));
		totalAmountMap.put("3562000040538974",new BigDecimal(113906));
		totalAmountMap.put("3562000045132337",new BigDecimal(113865));
		totalAmountMap.put("3562000040518545",new BigDecimal(113567));
		totalAmountMap.put("3562000040523296",new BigDecimal(113507));
		totalAmountMap.put("3562000045091455",new BigDecimal(112462));
		totalAmountMap.put("3562000043934946",new BigDecimal(112234));
		totalAmountMap.put("3562000040533456",new BigDecimal(112181));
		totalAmountMap.put("3562000040547705",new BigDecimal(112011));
		totalAmountMap.put("3562000045112376",new BigDecimal(111850));
		totalAmountMap.put("3562000045094055",new BigDecimal(111825));
		totalAmountMap.put("3562000040520755",new BigDecimal(111800));
		totalAmountMap.put("3562000040554485",new BigDecimal(111658));
		totalAmountMap.put("3562000040523217",new BigDecimal(111549));
		totalAmountMap.put("3562000040524985",new BigDecimal(110560));
		totalAmountMap.put("3562000040529216",new BigDecimal(110041));
		totalAmountMap.put("3562000044997534",new BigDecimal(110000));
		totalAmountMap.put("3562000044983905",new BigDecimal(109515));
		totalAmountMap.put("3562000041420856",new BigDecimal(109503));
		totalAmountMap.put("3562000039124327",new BigDecimal(109491));
		totalAmountMap.put("3562000040550854",new BigDecimal(108875));
		totalAmountMap.put("3562000040534775",new BigDecimal(108604));
		totalAmountMap.put("3562000036113816",new BigDecimal(108315));
		totalAmountMap.put("3562000040522407",new BigDecimal(108314));
		totalAmountMap.put("3562000040519216",new BigDecimal(108129));
		totalAmountMap.put("3562000040552805",new BigDecimal(107973));
		totalAmountMap.put("3562000040535845",new BigDecimal(107919));
		totalAmountMap.put("3562000039036226",new BigDecimal(107900));
		totalAmountMap.put("3562000040535894",new BigDecimal(107589));
		totalAmountMap.put("3562000040555245",new BigDecimal(106993));
		totalAmountMap.put("3562000040533906",new BigDecimal(106710));
		totalAmountMap.put("3562000040520916",new BigDecimal(106645));
		totalAmountMap.put("3562000040541247",new BigDecimal(106629));
		totalAmountMap.put("3562000040550285",new BigDecimal(106465));
		totalAmountMap.put("3562000040522846",new BigDecimal(106370));
		totalAmountMap.put("3562000040523955",new BigDecimal(106243));
		totalAmountMap.put("3562000040902836",new BigDecimal(105977));
		totalAmountMap.put("3562000040535365",new BigDecimal(105862));
		totalAmountMap.put("3562000045017346",new BigDecimal(105564));
		totalAmountMap.put("3562000040528645",new BigDecimal(104723));
		totalAmountMap.put("3562000036114237",new BigDecimal(103670));
		totalAmountMap.put("3562000045078145",new BigDecimal(103660));
		totalAmountMap.put("3562000041584446",new BigDecimal(103264));
		totalAmountMap.put("3562000040527645",new BigDecimal(103063));
		totalAmountMap.put("3562000043830655",new BigDecimal(102845));
		totalAmountMap.put("3562000040521286",new BigDecimal(102724));
		totalAmountMap.put("3562000040549175",new BigDecimal(102315));
		totalAmountMap.put("3562000040524536",new BigDecimal(102185));
		totalAmountMap.put("3562000036115326",new BigDecimal(102067));
		totalAmountMap.put("3562000045097604",new BigDecimal(101836));
		totalAmountMap.put("3562000040537574",new BigDecimal(101819));
		totalAmountMap.put("3562000040533047",new BigDecimal(101731));
		totalAmountMap.put("3562000040536375",new BigDecimal(101423));
		totalAmountMap.put("3562000040547694",new BigDecimal(101044));
		totalAmountMap.put("3562000040534706",new BigDecimal(100984));
		totalAmountMap.put("3562000040548645",new BigDecimal(100820));
		totalAmountMap.put("3562000041416885",new BigDecimal(100777));
		totalAmountMap.put("3562000040521516",new BigDecimal(100763));
		totalAmountMap.put("3562000040545594",new BigDecimal(100700));
		totalAmountMap.put("3562000035966373",new BigDecimal(100300));
		totalAmountMap.put("3562000040524447",new BigDecimal(100132));
		totalAmountMap.put("3562000040525715",new BigDecimal(99937));
		totalAmountMap.put("3562000040534027",new BigDecimal(99877));
		totalAmountMap.put("3562000040519564",new BigDecimal(99777));
		totalAmountMap.put("3562000045159953",new BigDecimal(99470));
		totalAmountMap.put("3562000040522736",new BigDecimal(99252));
		totalAmountMap.put("3562000040542417",new BigDecimal(99020));
		totalAmountMap.put("3562000041394546",new BigDecimal(98817));
		totalAmountMap.put("3562000045132356",new BigDecimal(98405));
		totalAmountMap.put("3562000040555264",new BigDecimal(98282));
		totalAmountMap.put("3562000040544875",new BigDecimal(98242));
		totalAmountMap.put("3562000045084285",new BigDecimal(98138));
		totalAmountMap.put("3562000040532955",new BigDecimal(98025));
		totalAmountMap.put("3562000040536316",new BigDecimal(98012));
		totalAmountMap.put("3562000040533266",new BigDecimal(98002));
		totalAmountMap.put("3562000040549306",new BigDecimal(97934));
		totalAmountMap.put("3562000040548984",new BigDecimal(97888));
		totalAmountMap.put("3562000040532447",new BigDecimal(97780));
		totalAmountMap.put("3562000045168115",new BigDecimal(97465));
		totalAmountMap.put("3562000043779973",new BigDecimal(97294));
		totalAmountMap.put("3562000045090416",new BigDecimal(97040));
		totalAmountMap.put("3562000040552346",new BigDecimal(97030));
		totalAmountMap.put("3562000045174355",new BigDecimal(96901));
		totalAmountMap.put("3562000040534946",new BigDecimal(96867));
		totalAmountMap.put("3562000040530127",new BigDecimal(96865));
		totalAmountMap.put("3562000045168993",new BigDecimal(96600));
		totalAmountMap.put("3562000045107764",new BigDecimal(96466));
		totalAmountMap.put("3562000040526515",new BigDecimal(96443));
		totalAmountMap.put("3562000045130037",new BigDecimal(96390));
		totalAmountMap.put("3562000036114027",new BigDecimal(96268));
		totalAmountMap.put("3562000045179445",new BigDecimal(96200));
		totalAmountMap.put("3562000040529825",new BigDecimal(95670));
		totalAmountMap.put("3562000045106455",new BigDecimal(95505));
		totalAmountMap.put("3562000040536436",new BigDecimal(95246));
		totalAmountMap.put("3562000040521156",new BigDecimal(94889));
		totalAmountMap.put("3562000040529375",new BigDecimal(94695));
		totalAmountMap.put("3562000040538275",new BigDecimal(94634));
		totalAmountMap.put("3562000045101276",new BigDecimal(94500));
		totalAmountMap.put("3562000040521107",new BigDecimal(94391));
		totalAmountMap.put("3562000044980894",new BigDecimal(94020));
		totalAmountMap.put("3562000040540726",new BigDecimal(93983));
		totalAmountMap.put("3562000037987633",new BigDecimal(93962));
		totalAmountMap.put("3562000040522995",new BigDecimal(93610));
		totalAmountMap.put("3562000040539725",new BigDecimal(93029));
		totalAmountMap.put("3562000045171426",new BigDecimal(92980));
		totalAmountMap.put("3562000040542296",new BigDecimal(92979));
		totalAmountMap.put("3562000040525136",new BigDecimal(92834));
		totalAmountMap.put("3562000045015175",new BigDecimal(92822));
		totalAmountMap.put("3562000040538715",new BigDecimal(92739));
		totalAmountMap.put("3562000040529525",new BigDecimal(92702));
		totalAmountMap.put("3562000040529915",new BigDecimal(92457));
		totalAmountMap.put("3562000041577793",new BigDecimal(92442));
		totalAmountMap.put("3562000040542427",new BigDecimal(91940));
		totalAmountMap.put("3562000040531816",new BigDecimal(91895));
		totalAmountMap.put("3562000045146784",new BigDecimal(91820));
		totalAmountMap.put("3562000045102437",new BigDecimal(91400));
		totalAmountMap.put("3562000041223817",new BigDecimal(91052));
		totalAmountMap.put("3562000045097693",new BigDecimal(91000));
		totalAmountMap.put("3562000040543636",new BigDecimal(90948));
		totalAmountMap.put("3562000039436275",new BigDecimal(90899));
		totalAmountMap.put("3562000037669253",new BigDecimal(90889));
		totalAmountMap.put("3562000040525515",new BigDecimal(90672));
		totalAmountMap.put("3562000040518375",new BigDecimal(90490));
		totalAmountMap.put("3562000040538884",new BigDecimal(90048));
		totalAmountMap.put("3562000045146175",new BigDecimal(89999));
		totalAmountMap.put("3562000040532816",new BigDecimal(89965));
		totalAmountMap.put("3562000040541516",new BigDecimal(89854));
		totalAmountMap.put("3562000045171475",new BigDecimal(89780));
		totalAmountMap.put("3562000040545365",new BigDecimal(89749));
		totalAmountMap.put("3562000040528835",new BigDecimal(89696));
		totalAmountMap.put("3562000040545095",new BigDecimal(89530));
		totalAmountMap.put("3562000040533816",new BigDecimal(89157));
		totalAmountMap.put("3562000040518915",new BigDecimal(89094));
		totalAmountMap.put("3562000040553475",new BigDecimal(89089));
		totalAmountMap.put("3562000040547365",new BigDecimal(88381));
		totalAmountMap.put("3562000040547446",new BigDecimal(88368));
		totalAmountMap.put("3562000036113695",new BigDecimal(88210));
		totalAmountMap.put("3562000041636426",new BigDecimal(87949));
		totalAmountMap.put("3562000040544317",new BigDecimal(87774));
		totalAmountMap.put("3562000041353386",new BigDecimal(87766));
		totalAmountMap.put("3562000040526446",new BigDecimal(87694));
		totalAmountMap.put("3562000040536065",new BigDecimal(87593));
		totalAmountMap.put("3562000040536515",new BigDecimal(87123));
		totalAmountMap.put("3562000045086544",new BigDecimal(87000));
		totalAmountMap.put("3562000040522037",new BigDecimal(86924));
		totalAmountMap.put("3562000040529515",new BigDecimal(86758));
		totalAmountMap.put("3562000040521975",new BigDecimal(86131));
		totalAmountMap.put("3562000045108705",new BigDecimal(85880));
		totalAmountMap.put("3562000040536894",new BigDecimal(85704));
		totalAmountMap.put("3562000041988973",new BigDecimal(85683));
		totalAmountMap.put("3562000040537694",new BigDecimal(85575));
		totalAmountMap.put("3562000040528874",new BigDecimal(84995));
		totalAmountMap.put("3562000045144785",new BigDecimal(84776));
		totalAmountMap.put("3562000041750935",new BigDecimal(84767));
		totalAmountMap.put("3562000037624055",new BigDecimal(84409));
		totalAmountMap.put("3562000040528664",new BigDecimal(84154));
		totalAmountMap.put("3562000040545884",new BigDecimal(84150));
		totalAmountMap.put("3562000041719236",new BigDecimal(84140));
		totalAmountMap.put("3562000045189164",new BigDecimal(83800));
		totalAmountMap.put("3562000035963673",new BigDecimal(83695));
		totalAmountMap.put("3562000040522486",new BigDecimal(83655));
		totalAmountMap.put("3562000040544096",new BigDecimal(82957));
		totalAmountMap.put("3562000045169035",new BigDecimal(82100));
		totalAmountMap.put("3562000040552306",new BigDecimal(81962));
		totalAmountMap.put("3562000040539625",new BigDecimal(81627));
		totalAmountMap.put("3562000041415317",new BigDecimal(81281));
		totalAmountMap.put("3562000040525505",new BigDecimal(81168));
		totalAmountMap.put("3562000045086294",new BigDecimal(81000));
		totalAmountMap.put("3562000040523785",new BigDecimal(80920));
		totalAmountMap.put("3562000037073146",new BigDecimal(80827));
		totalAmountMap.put("3562000040530936",new BigDecimal(80727));
		totalAmountMap.put("3562000041615306",new BigDecimal(80707));
		totalAmountMap.put("3562000044120148",new BigDecimal(80688));
		totalAmountMap.put("3562000040548505",new BigDecimal(80617));
		totalAmountMap.put("3562000041323428",new BigDecimal(80447));
		totalAmountMap.put("3562000040535136",new BigDecimal(80360));
		totalAmountMap.put("3562000045002017",new BigDecimal(80300));
		totalAmountMap.put("3562000043406855",new BigDecimal(80280));
		totalAmountMap.put("3562000043428117",new BigDecimal(80224));
		totalAmountMap.put("3562000045048805",new BigDecimal(79997));
		totalAmountMap.put("3562000040536564",new BigDecimal(79646));
		totalAmountMap.put("3562000040543826",new BigDecimal(79590));
		totalAmountMap.put("3562000036113906",new BigDecimal(79572));
		totalAmountMap.put("3562000040520147",new BigDecimal(79396));
		totalAmountMap.put("3562000040536116",new BigDecimal(79345));
		totalAmountMap.put("3562000040523007",new BigDecimal(79225));
		totalAmountMap.put("3562000040522546",new BigDecimal(79197));
		totalAmountMap.put("3562000040541127",new BigDecimal(79175));
		totalAmountMap.put("3562000040531396",new BigDecimal(79103));
		totalAmountMap.put("3562000040534147",new BigDecimal(78969));
		totalAmountMap.put("3562000041559583",new BigDecimal(78930));
		totalAmountMap.put("3562000045025346",new BigDecimal(78724));
		totalAmountMap.put("3562000040532965",new BigDecimal(78680));
		totalAmountMap.put("3562000040524286",new BigDecimal(78481));
		totalAmountMap.put("3562000040536615",new BigDecimal(78461));
		totalAmountMap.put("3562000040540066",new BigDecimal(78193));
		totalAmountMap.put("3562000040956025",new BigDecimal(78166));
		totalAmountMap.put("3562000045091006",new BigDecimal(78000));
		totalAmountMap.put("3562000040541765",new BigDecimal(77998));
		totalAmountMap.put("3562000041188116",new BigDecimal(77748));
		totalAmountMap.put("3562000040525395",new BigDecimal(77741));
		totalAmountMap.put("3562000045181894",new BigDecimal(77662));
		totalAmountMap.put("3562000040523906",new BigDecimal(77559));
		totalAmountMap.put("3562000040532117",new BigDecimal(77535));
		totalAmountMap.put("3562000040520736",new BigDecimal(77489));
		totalAmountMap.put("3562000040526495",new BigDecimal(77436));
		totalAmountMap.put("3562000045179474",new BigDecimal(77339));
		totalAmountMap.put("3562000045009085",new BigDecimal(77313));
		totalAmountMap.put("3562000045005416",new BigDecimal(77217));
		totalAmountMap.put("3562000040524895",new BigDecimal(77159));
		totalAmountMap.put("3562000041697673",new BigDecimal(77116));
		totalAmountMap.put("3562000040525535",new BigDecimal(77084));
		totalAmountMap.put("3562000040542196",new BigDecimal(77057));
		totalAmountMap.put("3562000040530736",new BigDecimal(76961));
		totalAmountMap.put("3562000043842855",new BigDecimal(76834));
		totalAmountMap.put("3562000040533386",new BigDecimal(76681));
		totalAmountMap.put("3562000044982625",new BigDecimal(76393));
		totalAmountMap.put("3562000040542836",new BigDecimal(76157));
		totalAmountMap.put("3562000045089814",new BigDecimal(76000));
		totalAmountMap.put("3562000036113995",new BigDecimal(75732));
		totalAmountMap.put("3562000045086145",new BigDecimal(75500));
		totalAmountMap.put("3562000045035195",new BigDecimal(75500));
		totalAmountMap.put("3562000045089734",new BigDecimal(75500));
		totalAmountMap.put("3562000045086174",new BigDecimal(75500));
		totalAmountMap.put("3562000040554925",new BigDecimal(75230));
		totalAmountMap.put("3562000040522726",new BigDecimal(75047));
		totalAmountMap.put("3562000040530317",new BigDecimal(74792));
		totalAmountMap.put("3562000041483396",new BigDecimal(74735));
		totalAmountMap.put("3562000036114007",new BigDecimal(74712));
		totalAmountMap.put("3562000045135285",new BigDecimal(74493));
		totalAmountMap.put("3562000040547095",new BigDecimal(74345));
		totalAmountMap.put("3562000040527535",new BigDecimal(74341));
		totalAmountMap.put("3562000040541066",new BigDecimal(74139));
		totalAmountMap.put("3562000040534846",new BigDecimal(73644));
		totalAmountMap.put("3562000040546385",new BigDecimal(73591));
		totalAmountMap.put("3562000040551754",new BigDecimal(73557));
		totalAmountMap.put("3562000045091016",new BigDecimal(73500));
		totalAmountMap.put("3562000044987714",new BigDecimal(73123));
		totalAmountMap.put("3562000040529905",new BigDecimal(73069));
		totalAmountMap.put("3562000035964015",new BigDecimal(72560));
		totalAmountMap.put("3562000045111736",new BigDecimal(72470));
		totalAmountMap.put("3562000037669134",new BigDecimal(72444));
		totalAmountMap.put("3562000036113936",new BigDecimal(72422));
		totalAmountMap.put("3562000040540447",new BigDecimal(72300));
		totalAmountMap.put("3562000041085336",new BigDecimal(72278));
		totalAmountMap.put("3562000041579693",new BigDecimal(71808));
		totalAmountMap.put("3562000040533555",new BigDecimal(71680));
		totalAmountMap.put("3562000040523746",new BigDecimal(71677));
		totalAmountMap.put("3562000040550095",new BigDecimal(71454));
		totalAmountMap.put("3562000045036554",new BigDecimal(71361));
		totalAmountMap.put("3562000040528845",new BigDecimal(71254));
		totalAmountMap.put("3562000040539954",new BigDecimal(71177));
		totalAmountMap.put("3562000044975744",new BigDecimal(71125));
		totalAmountMap.put("3562000040538674",new BigDecimal(70809));
		totalAmountMap.put("3562000040545505",new BigDecimal(70699));
		totalAmountMap.put("3562000045136625",new BigDecimal(70690));
		totalAmountMap.put("3562000040533916",new BigDecimal(70478));
		totalAmountMap.put("3562000036186284",new BigDecimal(70310));
		totalAmountMap.put("3562000040520636",new BigDecimal(69955));
		totalAmountMap.put("3562000040537095",new BigDecimal(69655));
		totalAmountMap.put("3562000040520646",new BigDecimal(69596));
		totalAmountMap.put("3562000041354017",new BigDecimal(69556));
		totalAmountMap.put("3562000045179094",new BigDecimal(69400));
		totalAmountMap.put("3562000040518316",new BigDecimal(69398));
		totalAmountMap.put("3562000037820175",new BigDecimal(69331));
		totalAmountMap.put("3562000043985904",new BigDecimal(69255));
		totalAmountMap.put("3562000040539246",new BigDecimal(69066));
		totalAmountMap.put("3562000040518006",new BigDecimal(68855));
		totalAmountMap.put("3562000040524237",new BigDecimal(68815));
		totalAmountMap.put("3562000040526016",new BigDecimal(68760));
		totalAmountMap.put("3562000040531895",new BigDecimal(68485));
		totalAmountMap.put("3562000040524007",new BigDecimal(68398));
		totalAmountMap.put("3562000040541955",new BigDecimal(68141));
		totalAmountMap.put("3562000043347865",new BigDecimal(67942));
		totalAmountMap.put("3562000040526754",new BigDecimal(67939));
		totalAmountMap.put("3562000041042617",new BigDecimal(67873));
		totalAmountMap.put("3562000040532027",new BigDecimal(67123));
		totalAmountMap.put("3562000045035835",new BigDecimal(67073));
		totalAmountMap.put("3562000044775264",new BigDecimal(67000));
		totalAmountMap.put("3562000041415606",new BigDecimal(66897));
		totalAmountMap.put("3562000044897035",new BigDecimal(66590));
		totalAmountMap.put("3562000045090974",new BigDecimal(66500));
		totalAmountMap.put("3562000043780326",new BigDecimal(66346));
		totalAmountMap.put("3562000040526295",new BigDecimal(66156));
		totalAmountMap.put("3562000040530427",new BigDecimal(66150));
		totalAmountMap.put("3562000040535664",new BigDecimal(66031));
		totalAmountMap.put("3562000040540347",new BigDecimal(66027));
		totalAmountMap.put("3562000040530965",new BigDecimal(66016));
		totalAmountMap.put("3562000036048495",new BigDecimal(66000));
		totalAmountMap.put("3562000040742565",new BigDecimal(65877));
		totalAmountMap.put("3562000040534995",new BigDecimal(65810));
		totalAmountMap.put("3562000040549774",new BigDecimal(65531));
		totalAmountMap.put("3562000040529095",new BigDecimal(65410));
		totalAmountMap.put("3562000043003108",new BigDecimal(65400));
		totalAmountMap.put("3562000040529436",new BigDecimal(65382));
		totalAmountMap.put("3562000040543536",new BigDecimal(65370));
		totalAmountMap.put("3562000040537106",new BigDecimal(65251));
		totalAmountMap.put("3562000045099693",new BigDecimal(65000));
		totalAmountMap.put("3562000040540585",new BigDecimal(64930));
		totalAmountMap.put("3562000040539505",new BigDecimal(64879));
		totalAmountMap.put("3562000040522307",new BigDecimal(64848));
		totalAmountMap.put("3562000040523076",new BigDecimal(64638));
		totalAmountMap.put("3562000045059604",new BigDecimal(64400));
		totalAmountMap.put("3562000040548794",new BigDecimal(64238));
		totalAmountMap.put("3562000040526745",new BigDecimal(64078));
		totalAmountMap.put("3562000045084185",new BigDecimal(63966));
		totalAmountMap.put("3562000045099934",new BigDecimal(63963));
		totalAmountMap.put("3562000040522386",new BigDecimal(63929));
		totalAmountMap.put("3562000040536136",new BigDecimal(63920));
		totalAmountMap.put("3562000040547485",new BigDecimal(63893));
		totalAmountMap.put("3562000040549455",new BigDecimal(63879));
		totalAmountMap.put("3562000040531327",new BigDecimal(63876));
		totalAmountMap.put("3562000044767464",new BigDecimal(63790));
		totalAmountMap.put("3562000040531575",new BigDecimal(63740));
		totalAmountMap.put("3562000040538635",new BigDecimal(63653));
		totalAmountMap.put("3562000040528515",new BigDecimal(63616));
		totalAmountMap.put("3562000045086264",new BigDecimal(63500));
		totalAmountMap.put("3562000040517905",new BigDecimal(63445));
		totalAmountMap.put("3562000038454265",new BigDecimal(63407));
		totalAmountMap.put("3562000045009584",new BigDecimal(63405));
		totalAmountMap.put("3562000038370316",new BigDecimal(63402));
		totalAmountMap.put("3562000041207546",new BigDecimal(63284));
		totalAmountMap.put("3562000040528075",new BigDecimal(63230));
		totalAmountMap.put("3562000040543955",new BigDecimal(62938));
		totalAmountMap.put("3562000041416356",new BigDecimal(62937));
		totalAmountMap.put("3562000040527945",new BigDecimal(62911));
		totalAmountMap.put("3562000040541716",new BigDecimal(62849));
		totalAmountMap.put("3562000040543096",new BigDecimal(62722));
		totalAmountMap.put("3562000040534496",new BigDecimal(62602));
		totalAmountMap.put("3562000041454685",new BigDecimal(62535));
		totalAmountMap.put("3562000040542117",new BigDecimal(62395));
		totalAmountMap.put("3562000040526475",new BigDecimal(62338));
		totalAmountMap.put("3562000040550825",new BigDecimal(62327));
		totalAmountMap.put("3562000045067524",new BigDecimal(62124));
		totalAmountMap.put("3562000041423696",new BigDecimal(62046));
		totalAmountMap.put("3562000041654446",new BigDecimal(61932));
		totalAmountMap.put("3562000040521726",new BigDecimal(61798));
		totalAmountMap.put("3562000040519346",new BigDecimal(61768));
		totalAmountMap.put("3562000040528825",new BigDecimal(61756));
		totalAmountMap.put("3562000041621117",new BigDecimal(61485));
		totalAmountMap.put("3562000040533376",new BigDecimal(61234));
		totalAmountMap.put("3562000040533107",new BigDecimal(61075));
		totalAmountMap.put("3562000045077115",new BigDecimal(60830));
		totalAmountMap.put("3562000045171346",new BigDecimal(60800));
		totalAmountMap.put("3562000042146437",new BigDecimal(60710));
		totalAmountMap.put("3562000042911176",new BigDecimal(60594));
		totalAmountMap.put("3562000040553574",new BigDecimal(60578));
		totalAmountMap.put("3562000040525774",new BigDecimal(60566));
		totalAmountMap.put("3562000040525705",new BigDecimal(60557));
		totalAmountMap.put("3562000042130028",new BigDecimal(60539));
		totalAmountMap.put("3562000040551954",new BigDecimal(60506));
		totalAmountMap.put("3562000037670174",new BigDecimal(60495));
		totalAmountMap.put("3562000040551365",new BigDecimal(60443));
		totalAmountMap.put("3562000040546016",new BigDecimal(60373));
		totalAmountMap.put("3562000045140626",new BigDecimal(60280));
		totalAmountMap.put("3562000036113846",new BigDecimal(60260));
		totalAmountMap.put("3562000041479535",new BigDecimal(60092));
		totalAmountMap.put("3562000041422707",new BigDecimal(60034));
		totalAmountMap.put("3562000043841746",new BigDecimal(59995));
		totalAmountMap.put("3562000040534437",new BigDecimal(59831));
		totalAmountMap.put("3562000040539136",new BigDecimal(59780));
		totalAmountMap.put("3562000040545684",new BigDecimal(59772));
		totalAmountMap.put("3562000045167464",new BigDecimal(59600));
		totalAmountMap.put("3562000045148475",new BigDecimal(59561));
		totalAmountMap.put("3562000040539026",new BigDecimal(59538));
		totalAmountMap.put("3562000040528306",new BigDecimal(59535));
		totalAmountMap.put("3562000041532396",new BigDecimal(59523));
		totalAmountMap.put("3562000040539195",new BigDecimal(59492));
		totalAmountMap.put("3562000040531885",new BigDecimal(59460));
		totalAmountMap.put("3562000040518754",new BigDecimal(59404));
		totalAmountMap.put("3562000045191316",new BigDecimal(59400));
		totalAmountMap.put("3562000040531266",new BigDecimal(59211));
		totalAmountMap.put("3562000040533846",new BigDecimal(59134));
		totalAmountMap.put("3562000042564554",new BigDecimal(59124));
		totalAmountMap.put("3562000044775305",new BigDecimal(59000));
		totalAmountMap.put("3562000040541137",new BigDecimal(58919));
		totalAmountMap.put("3562000040535915",new BigDecimal(58910));
		totalAmountMap.put("3562000040522626",new BigDecimal(58898));
		totalAmountMap.put("3562000042772265",new BigDecimal(58840));
		totalAmountMap.put("3562000040520516",new BigDecimal(58690));
		totalAmountMap.put("3562000045099844",new BigDecimal(58500));
		totalAmountMap.put("3562000040542726",new BigDecimal(58495));
		totalAmountMap.put("3562000040522007",new BigDecimal(58376));
		totalAmountMap.put("3562000040552554",new BigDecimal(58239));
		totalAmountMap.put("3562000045188793",new BigDecimal(58200));
		totalAmountMap.put("3562000044996953",new BigDecimal(58165));
		totalAmountMap.put("3562000040532456",new BigDecimal(58130));
		totalAmountMap.put("3562000040534376",new BigDecimal(58042));
		totalAmountMap.put("3562000045035216",new BigDecimal(58000));
		totalAmountMap.put("3562000040544555",new BigDecimal(57684));
		totalAmountMap.put("3562000045135674",new BigDecimal(57609));
		totalAmountMap.put("3562000045145954",new BigDecimal(57600));
		totalAmountMap.put("3562000040528295",new BigDecimal(57598));
		totalAmountMap.put("3562000044758215",new BigDecimal(57597));
		totalAmountMap.put("3562000040525065",new BigDecimal(57521));
		totalAmountMap.put("3562000039078893",new BigDecimal(57248));
		totalAmountMap.put("3562000045184764",new BigDecimal(57210));
		totalAmountMap.put("3562000040542027",new BigDecimal(57187));
		totalAmountMap.put("3562000037272905",new BigDecimal(57180));
		totalAmountMap.put("3562000045189883",new BigDecimal(57055));
		totalAmountMap.put("3562000044399594",new BigDecimal(56994));
		totalAmountMap.put("3562000045101347",new BigDecimal(56855));
		totalAmountMap.put("3562000040554416",new BigDecimal(56686));
		totalAmountMap.put("3562000044956115",new BigDecimal(56626));
		totalAmountMap.put("3562000040529545",new BigDecimal(56572));
		totalAmountMap.put("3562000040538605",new BigDecimal(56566));
		totalAmountMap.put("3562000040521655",new BigDecimal(56338));
		totalAmountMap.put("3562000040531447",new BigDecimal(56320));
		totalAmountMap.put("3562000040538216",new BigDecimal(56212));
		totalAmountMap.put("3562000037804395",new BigDecimal(56205));
		totalAmountMap.put("3562000041705416",new BigDecimal(56194));
		totalAmountMap.put("3562000045094385",new BigDecimal(56000));
		totalAmountMap.put("3562000040550136",new BigDecimal(55999));
		totalAmountMap.put("3562000040547984",new BigDecimal(55851));
		totalAmountMap.put("3562000041166515",new BigDecimal(55752));
		totalAmountMap.put("3562000037863084",new BigDecimal(55720));
		totalAmountMap.put("3562000040548715",new BigDecimal(55707));
		totalAmountMap.put("3562000040531237",new BigDecimal(55660));
		totalAmountMap.put("3562000040524296",new BigDecimal(55611));
		totalAmountMap.put("3562000040523137",new BigDecimal(55516));
		totalAmountMap.put("3562000045097653",new BigDecimal(55500));
		totalAmountMap.put("3562000041604347",new BigDecimal(55491));
		totalAmountMap.put("3562000040525006",new BigDecimal(55452));
		totalAmountMap.put("3562000040521685",new BigDecimal(55361));
		totalAmountMap.put("3562000039204826",new BigDecimal(55350));
		totalAmountMap.put("3562000040877763",new BigDecimal(55334));
		totalAmountMap.put("3562000040544396",new BigDecimal(55270));
		totalAmountMap.put("3562000041382985",new BigDecimal(55173));
		totalAmountMap.put("3562000040531875",new BigDecimal(55133));
		totalAmountMap.put("3562000045189583",new BigDecimal(55051));
		totalAmountMap.put("3562000045100256",new BigDecimal(55000));
		totalAmountMap.put("3562000045101007",new BigDecimal(55000));
		totalAmountMap.put("3562000043032177",new BigDecimal(54955));
		totalAmountMap.put("3562000045086644",new BigDecimal(54793));
		totalAmountMap.put("3562000041610675",new BigDecimal(54786));
		totalAmountMap.put("3562000040526674",new BigDecimal(54759));
		totalAmountMap.put("3562000040543836",new BigDecimal(54631));
		totalAmountMap.put("3562000045231826",new BigDecimal(54600));
		totalAmountMap.put("3562000040554564",new BigDecimal(54589));
		totalAmountMap.put("3562000036113746",new BigDecimal(54580));
		totalAmountMap.put("3562000041344656",new BigDecimal(54357));
		totalAmountMap.put("3562000037669234",new BigDecimal(54259));
		totalAmountMap.put("3562000041603237",new BigDecimal(54250));
		totalAmountMap.put("3562000045175853",new BigDecimal(54147));
		totalAmountMap.put("3562000043435916",new BigDecimal(54000));
		totalAmountMap.put("3562000040528715",new BigDecimal(53979));
		totalAmountMap.put("3562000040533247",new BigDecimal(53958));
		totalAmountMap.put("3562000040543755",new BigDecimal(53854));
		totalAmountMap.put("3562000043056745",new BigDecimal(53828));
		totalAmountMap.put("3562000040551165",new BigDecimal(53826));
		totalAmountMap.put("3562000040519336",new BigDecimal(53752));
		totalAmountMap.put("3562000045090715",new BigDecimal(53745));
		totalAmountMap.put("3562000042009076",new BigDecimal(53515));
		totalAmountMap.put("3562000044972326",new BigDecimal(53500));
		totalAmountMap.put("3562000045123575",new BigDecimal(53470));
		totalAmountMap.put("3562000045132347",new BigDecimal(53370));
		totalAmountMap.put("3562000041738365",new BigDecimal(53266));
		totalAmountMap.put("3562000044930027",new BigDecimal(53257));
		totalAmountMap.put("3562000040552974",new BigDecimal(53188));
		totalAmountMap.put("3562000041610076",new BigDecimal(53156));
		totalAmountMap.put("3562000040526136",new BigDecimal(53127));
		totalAmountMap.put("3562000045100066",new BigDecimal(53000));
		totalAmountMap.put("3562000045101286",new BigDecimal(53000));
		totalAmountMap.put("3562000044994805",new BigDecimal(53000));
		totalAmountMap.put("3562000042503396",new BigDecimal(52900));
		totalAmountMap.put("3562000041572505",new BigDecimal(52792));
		totalAmountMap.put("3562000045119645",new BigDecimal(52726));
		totalAmountMap.put("3562000041607535",new BigDecimal(52700));
		totalAmountMap.put("3562000040524376",new BigDecimal(52695));
		totalAmountMap.put("3562000043924117",new BigDecimal(52690));
		totalAmountMap.put("3562000040527326",new BigDecimal(52516));
		totalAmountMap.put("3562000040530946",new BigDecimal(52397));
		totalAmountMap.put("3562000040533926",new BigDecimal(52369));
		totalAmountMap.put("3562000045089045",new BigDecimal(52000));
		totalAmountMap.put("3562000045086154",new BigDecimal(52000));
		totalAmountMap.put("3562000040530555",new BigDecimal(51939));
		totalAmountMap.put("3562000040547106",new BigDecimal(51918));
		totalAmountMap.put("3562000040518365",new BigDecimal(51847));
		totalAmountMap.put("3562000040533237",new BigDecimal(51712));
		totalAmountMap.put("3562000040534127",new BigDecimal(51656));
		totalAmountMap.put("3562000040520327",new BigDecimal(51593));
		totalAmountMap.put("3562000045168624",new BigDecimal(51515));
		totalAmountMap.put("3562000040537416",new BigDecimal(51349));
		totalAmountMap.put("3562000041080685",new BigDecimal(51261));
		totalAmountMap.put("3562000041425117",new BigDecimal(51250));
		totalAmountMap.put("3562000041045056",new BigDecimal(51137));
		totalAmountMap.put("3562000040546594",new BigDecimal(51124));
		totalAmountMap.put("3562000045177105",new BigDecimal(51065));
		totalAmountMap.put("3562000045102456",new BigDecimal(51000));
		totalAmountMap.put("3562000040548395",new BigDecimal(50917));
		totalAmountMap.put("3562000040536605",new BigDecimal(50827));
		totalAmountMap.put("3562000040539664",new BigDecimal(50813));
		totalAmountMap.put("3562000040550416",new BigDecimal(50727));
		totalAmountMap.put("3562000045178634",new BigDecimal(50700));
		totalAmountMap.put("3562000040553346",new BigDecimal(50697));
		totalAmountMap.put("3562000036349365",new BigDecimal(50695));
		totalAmountMap.put("3562000040522247",new BigDecimal(50650));
		totalAmountMap.put("3562000040548146",new BigDecimal(50613));
		totalAmountMap.put("3562000041229736",new BigDecimal(50611));
		totalAmountMap.put("3562000036902065",new BigDecimal(50500));
		totalAmountMap.put("3562000045086315",new BigDecimal(50500));
		totalAmountMap.put("3562000040542327",new BigDecimal(50470));
		totalAmountMap.put("3562000045138306",new BigDecimal(50400));
		totalAmountMap.put("3562000040538935",new BigDecimal(50398));
		totalAmountMap.put("3562000044981864",new BigDecimal(50393));
		totalAmountMap.put("3562000037183935",new BigDecimal(50392));
		totalAmountMap.put("3562000040533207",new BigDecimal(50385));
		totalAmountMap.put("3562000045148195",new BigDecimal(50330));
		totalAmountMap.put("3562000040545764",new BigDecimal(50256));
		totalAmountMap.put("3562000044988494",new BigDecimal(50201));
		totalAmountMap.put("3562000045169054",new BigDecimal(50200));
		totalAmountMap.put("3562000041392407",new BigDecimal(50200));
		totalAmountMap.put("3562000041114886",new BigDecimal(50163));
		totalAmountMap.put("3562000040536426",new BigDecimal(50090));
		totalAmountMap.put("3562000041420097",new BigDecimal(50038));
		totalAmountMap.put("3562000041429706",new BigDecimal(50009));
		totalAmountMap.put("3562000040537625",new BigDecimal(50005));
		totalAmountMap.put("3562000037272705",new BigDecimal(49930));
		totalAmountMap.put("3562000040519355",new BigDecimal(49830));
		totalAmountMap.put("3562000042161337",new BigDecimal(49762));
		totalAmountMap.put("3562000040520496",new BigDecimal(49667));
		totalAmountMap.put("3562000043860026",new BigDecimal(49647));
		totalAmountMap.put("3562000040551026",new BigDecimal(49500));
		totalAmountMap.put("3562000036113855",new BigDecimal(49486));
		totalAmountMap.put("3562000045158744",new BigDecimal(49400));
		totalAmountMap.put("3562000040529426",new BigDecimal(49385));
		totalAmountMap.put("3562000043404177",new BigDecimal(49351));
		totalAmountMap.put("3562000042498664",new BigDecimal(49281));
		totalAmountMap.put("3562000040523266",new BigDecimal(49252));
		totalAmountMap.put("3562000040549436",new BigDecimal(49110));
		totalAmountMap.put("3562000042727285",new BigDecimal(49072));
		totalAmountMap.put("3562000040539584",new BigDecimal(49044));
		totalAmountMap.put("3562000040541555",new BigDecimal(48975));
		totalAmountMap.put("3562000040524646",new BigDecimal(48955));
		totalAmountMap.put("3562000043828554",new BigDecimal(48920));
		totalAmountMap.put("3562000045120555",new BigDecimal(48900));
		totalAmountMap.put("3562000040528754",new BigDecimal(48765));
		totalAmountMap.put("3562000042134637",new BigDecimal(48712));
		totalAmountMap.put("3562000040519894",new BigDecimal(48706));
		totalAmountMap.put("3562000042124907",new BigDecimal(48551));
		totalAmountMap.put("3562000040545525",new BigDecimal(48464));
		totalAmountMap.put("3562000041058515",new BigDecimal(48455));
		totalAmountMap.put("3562000036113916",new BigDecimal(48380));
		totalAmountMap.put("3562000041755374",new BigDecimal(48180));
		totalAmountMap.put("3562000040524516",new BigDecimal(48058));
		totalAmountMap.put("3562000036461475",new BigDecimal(48036));
		totalAmountMap.put("3562000045091406",new BigDecimal(48000));
		totalAmountMap.put("3562000045079205",new BigDecimal(48000));
		totalAmountMap.put("3562000045088205",new BigDecimal(48000));
		totalAmountMap.put("3562000045100266",new BigDecimal(48000));
		totalAmountMap.put("3562000042505475",new BigDecimal(48000));
		totalAmountMap.put("3562000038278015",new BigDecimal(47878));
		totalAmountMap.put("3562000040527984",new BigDecimal(47629));
		totalAmountMap.put("3562000042162486",new BigDecimal(47628));
		totalAmountMap.put("3562000039471336",new BigDecimal(47625));
		totalAmountMap.put("3562000042365475",new BigDecimal(47500));
		totalAmountMap.put("3562000041355216",new BigDecimal(47481));
		totalAmountMap.put("3562000040520726",new BigDecimal(47403));
		totalAmountMap.put("3562000040554505",new BigDecimal(47384));
		totalAmountMap.put("3562000040528365",new BigDecimal(47264));
		totalAmountMap.put("3562000045087883",new BigDecimal(47233));
		totalAmountMap.put("3562000045189215",new BigDecimal(47200));
		totalAmountMap.put("3562000040534555",new BigDecimal(47170));
		totalAmountMap.put("3562000043404157",new BigDecimal(47164));
		totalAmountMap.put("3562000040522256",new BigDecimal(47072));
		totalAmountMap.put("3562000040523836",new BigDecimal(47048));
		totalAmountMap.put("3562000045102466",new BigDecimal(47000));
		totalAmountMap.put("3562000040544047",new BigDecimal(46989));
		totalAmountMap.put("3562000040548725",new BigDecimal(46955));
		totalAmountMap.put("3562000040518815",new BigDecimal(46922));
		totalAmountMap.put("3562000040545535",new BigDecimal(46880));
		totalAmountMap.put("3562000045169863",new BigDecimal(46791));
		totalAmountMap.put("3562000040521366",new BigDecimal(46704));
		totalAmountMap.put("3562000040531147",new BigDecimal(46655));
		totalAmountMap.put("3562000040522895",new BigDecimal(46610));
		totalAmountMap.put("3562000039079105",new BigDecimal(46602));
		totalAmountMap.put("3562000040521555",new BigDecimal(46546));
		totalAmountMap.put("3562000038661074",new BigDecimal(46385));
		totalAmountMap.put("3562000040535426",new BigDecimal(46384));
		totalAmountMap.put("3562000041495535",new BigDecimal(46335));
		totalAmountMap.put("3562000041727574",new BigDecimal(46255));
		totalAmountMap.put("3562000040545945",new BigDecimal(46232));
		totalAmountMap.put("3562000040525426",new BigDecimal(46211));
		totalAmountMap.put("3562000040537954",new BigDecimal(46175));
		totalAmountMap.put("3562000042130907",new BigDecimal(46107));
		totalAmountMap.put("3562000040520256",new BigDecimal(46072));
		totalAmountMap.put("3562000037960793",new BigDecimal(46060));
		totalAmountMap.put("3562000040519095",new BigDecimal(46040));
		totalAmountMap.put("3562000040545175",new BigDecimal(46036));
		totalAmountMap.put("3562000045102585",new BigDecimal(46000));
		totalAmountMap.put("3562000045144736",new BigDecimal(45994));
		totalAmountMap.put("3562000041492646",new BigDecimal(45993));
		totalAmountMap.put("3562000045040775",new BigDecimal(45990));
		totalAmountMap.put("3562000042420537",new BigDecimal(45936));
		totalAmountMap.put("3562000040548095",new BigDecimal(45888));
		totalAmountMap.put("3562000045175464",new BigDecimal(45800));
		totalAmountMap.put("3562000040549525",new BigDecimal(45728));
		totalAmountMap.put("3562000038914206",new BigDecimal(45651));
		totalAmountMap.put("3562000040518026",new BigDecimal(45593));
		totalAmountMap.put("3562000040542795",new BigDecimal(45488));
		totalAmountMap.put("3562000041421238",new BigDecimal(45487));
		totalAmountMap.put("3562000040539075",new BigDecimal(45477));
		totalAmountMap.put("3562000043683784",new BigDecimal(45415));
		totalAmountMap.put("3562000042852835",new BigDecimal(45413));
		totalAmountMap.put("3562000040548894",new BigDecimal(45381));
		totalAmountMap.put("3562000040905426",new BigDecimal(45370));
		totalAmountMap.put("3562000041697325",new BigDecimal(45290));
		totalAmountMap.put("3562000040527395",new BigDecimal(45288));
		totalAmountMap.put("3562000040533017",new BigDecimal(45277));
		totalAmountMap.put("3562000040526645",new BigDecimal(45179));
		totalAmountMap.put("3562000036115635",new BigDecimal(45100));
		totalAmountMap.put("3562000040538894",new BigDecimal(45055));
		totalAmountMap.put("3562000041531096",new BigDecimal(45005));
		totalAmountMap.put("3562000040522147",new BigDecimal(44833));
		totalAmountMap.put("3562000045136436",new BigDecimal(44774));
		totalAmountMap.put("3562000040539705",new BigDecimal(44543));
		totalAmountMap.put("3562000040544936",new BigDecimal(44415));
		totalAmountMap.put("3562000040537216",new BigDecimal(44405));
		totalAmountMap.put("3562000040524056",new BigDecimal(44387));
		totalAmountMap.put("3562000040527554",new BigDecimal(44352));
		totalAmountMap.put("3562000040532585",new BigDecimal(44130));
		totalAmountMap.put("3562000041412717",new BigDecimal(44118));
		totalAmountMap.put("3562000045213007",new BigDecimal(44100));
		totalAmountMap.put("3562000040548265",new BigDecimal(44049));
		totalAmountMap.put("3562000045171915",new BigDecimal(44008));
		totalAmountMap.put("3562000041464386",new BigDecimal(43953));
		totalAmountMap.put("3562000045130816",new BigDecimal(43906));
		totalAmountMap.put("3562000040534616",new BigDecimal(43844));
		totalAmountMap.put("3562000041729255",new BigDecimal(43649));
		totalAmountMap.put("3562000040528085",new BigDecimal(43627));
		totalAmountMap.put("3562000045095644",new BigDecimal(43600));
		totalAmountMap.put("3562000041143208",new BigDecimal(43572));
		totalAmountMap.put("3562000040534685",new BigDecimal(43414));
		totalAmountMap.put("3562000045115674",new BigDecimal(43377));
		totalAmountMap.put("3562000045170554",new BigDecimal(43341));
		totalAmountMap.put("3562000040533865",new BigDecimal(43333));
		totalAmountMap.put("3562000040549185",new BigDecimal(43210));
		totalAmountMap.put("3562000040521675",new BigDecimal(43179));
		totalAmountMap.put("3562000041057095",new BigDecimal(43073));
		totalAmountMap.put("3562000037280964",new BigDecimal(43070));
		totalAmountMap.put("3562000037570435",new BigDecimal(43030));
		totalAmountMap.put("3562000040528525",new BigDecimal(43004));
		totalAmountMap.put("3562000045094365",new BigDecimal(43000));
		totalAmountMap.put("3562000040528935",new BigDecimal(42980));
		totalAmountMap.put("3562000040520127",new BigDecimal(42914));
		totalAmountMap.put("3562000036255405",new BigDecimal(42826));
		totalAmountMap.put("3562000040546465",new BigDecimal(42701));
		totalAmountMap.put("3562000042156964",new BigDecimal(42690));
		totalAmountMap.put("3562000040549554",new BigDecimal(42650));
		totalAmountMap.put("3562000043949954",new BigDecimal(42563));
		totalAmountMap.put("3562000040555164",new BigDecimal(42559));
		totalAmountMap.put("3562000045102565",new BigDecimal(42500));
		totalAmountMap.put("3562000045001706",new BigDecimal(42492));
		totalAmountMap.put("3562000040519426",new BigDecimal(42442));
		totalAmountMap.put("3562000040527925",new BigDecimal(42413));
		totalAmountMap.put("3562000040539275",new BigDecimal(42355));
		totalAmountMap.put("3562000036115275",new BigDecimal(42330));
		totalAmountMap.put("3562000040555593",new BigDecimal(42217));
		totalAmountMap.put("3562000045189563",new BigDecimal(42180));
		totalAmountMap.put("3562000045178025",new BigDecimal(42117));
		totalAmountMap.put("3562000040518475",new BigDecimal(42070));
		totalAmountMap.put("3562000045102595",new BigDecimal(42060));
		totalAmountMap.put("3562000040519764",new BigDecimal(42058));
		totalAmountMap.put("3562000040539825",new BigDecimal(42035));
		totalAmountMap.put("3562000039627354",new BigDecimal(42018));
		totalAmountMap.put("3562000040548554",new BigDecimal(41971));
		totalAmountMap.put("3562000045170226",new BigDecimal(41926));
		totalAmountMap.put("3562000040980375",new BigDecimal(41810));
		totalAmountMap.put("3562000043795374",new BigDecimal(41720));
		totalAmountMap.put("3562000044693845",new BigDecimal(41602));
		totalAmountMap.put("3562000040542456",new BigDecimal(41587));
		totalAmountMap.put("3562000041589824",new BigDecimal(41571));
		totalAmountMap.put("3562000040532706",new BigDecimal(41566));
		totalAmountMap.put("3562000043653085",new BigDecimal(41540));
		totalAmountMap.put("3562000045097673",new BigDecimal(41500));
		totalAmountMap.put("3562000040534665",new BigDecimal(41498));
		totalAmountMap.put("3562000040532785",new BigDecimal(41343));
		totalAmountMap.put("3562000040549735",new BigDecimal(41333));
		totalAmountMap.put("3562000040550185",new BigDecimal(41300));
		totalAmountMap.put("3562000041471636",new BigDecimal(41279));
		totalAmountMap.put("3562000040544086",new BigDecimal(41262));
		totalAmountMap.put("3562000040544347",new BigDecimal(41256));
		totalAmountMap.put("3562000040535774",new BigDecimal(41207));
		totalAmountMap.put("3562000045009574",new BigDecimal(41184));
		totalAmountMap.put("3562000041681165",new BigDecimal(41170));
		totalAmountMap.put("3562000041676863",new BigDecimal(41135));
		totalAmountMap.put("3562000040859583",new BigDecimal(41070));
		totalAmountMap.put("3562000040544526",new BigDecimal(41053));
		totalAmountMap.put("3562000040528945",new BigDecimal(40962));
		totalAmountMap.put("3562000037663744",new BigDecimal(40951));
		totalAmountMap.put("3562000045189573",new BigDecimal(40871));
		totalAmountMap.put("3562000041755264",new BigDecimal(40833));
		totalAmountMap.put("3562000044994594",new BigDecimal(40831));
		totalAmountMap.put("3562000041213287",new BigDecimal(40800));
		totalAmountMap.put("3562000040550055",new BigDecimal(40737));
		totalAmountMap.put("3562000041421676",new BigDecimal(40662));
		totalAmountMap.put("3562000043480555",new BigDecimal(40622));
		totalAmountMap.put("3562000040520785",new BigDecimal(40489));
		totalAmountMap.put("3562000040519825",new BigDecimal(40476));
		totalAmountMap.put("3562000040535745",new BigDecimal(40465));
		totalAmountMap.put("3562000040532427",new BigDecimal(40379));
		totalAmountMap.put("3562000040530865",new BigDecimal(40369));
		totalAmountMap.put("3562000040536475",new BigDecimal(40293));
		totalAmountMap.put("3562000040539216",new BigDecimal(40174));
		totalAmountMap.put("3562000040525674",new BigDecimal(40143));
		totalAmountMap.put("3562000045194495",new BigDecimal(40100));
		totalAmountMap.put("3562000040550584",new BigDecimal(39978));
		totalAmountMap.put("3562000040520885",new BigDecimal(39943));
		totalAmountMap.put("3562000043692835",new BigDecimal(39910));
		totalAmountMap.put("3562000041384885",new BigDecimal(39823));
		totalAmountMap.put("3562000040525784",new BigDecimal(39801));
		totalAmountMap.put("3562000040520266",new BigDecimal(39794));
		totalAmountMap.put("3562000040519835",new BigDecimal(39785));
		totalAmountMap.put("3562000045170915",new BigDecimal(39757));
		totalAmountMap.put("3562000045001695",new BigDecimal(39724));
		totalAmountMap.put("3562000037224555",new BigDecimal(39580));
		totalAmountMap.put("3562000045178194",new BigDecimal(39558));
		totalAmountMap.put("3562000040554226",new BigDecimal(39525));
		totalAmountMap.put("3562000041072865",new BigDecimal(39513));
		totalAmountMap.put("3562000041144428",new BigDecimal(39508));
		totalAmountMap.put("3562000041152655",new BigDecimal(39474));
		totalAmountMap.put("3562000045114716",new BigDecimal(39471));
		totalAmountMap.put("3562000040542437",new BigDecimal(39443));
		totalAmountMap.put("3562000040553495",new BigDecimal(39396));
		totalAmountMap.put("3562000040534755",new BigDecimal(39331));
		totalAmountMap.put("3562000040548285",new BigDecimal(39324));
		totalAmountMap.put("3562000040525485",new BigDecimal(39284));
		totalAmountMap.put("3562000045175704",new BigDecimal(39262));
		totalAmountMap.put("3562000040541985",new BigDecimal(39256));
		totalAmountMap.put("3562000040544616",new BigDecimal(39151));
		totalAmountMap.put("3562000040543855",new BigDecimal(39108));
		totalAmountMap.put("3562000037385763",new BigDecimal(39050));
		totalAmountMap.put("3562000040538694",new BigDecimal(39044));
		totalAmountMap.put("3562000045122156",new BigDecimal(39000));
		totalAmountMap.put("3562000040553694",new BigDecimal(38993));
		totalAmountMap.put("3562000042900017",new BigDecimal(38922));
		totalAmountMap.put("3562000045120775",new BigDecimal(38900));
		totalAmountMap.put("3562000045077394",new BigDecimal(38900));
		totalAmountMap.put("3562000040541546",new BigDecimal(38741));
		totalAmountMap.put("3562000043452895",new BigDecimal(38679));
		totalAmountMap.put("3562000040545316",new BigDecimal(38603));
		totalAmountMap.put("3562000040528784",new BigDecimal(38582));
		totalAmountMap.put("3562000040537584",new BigDecimal(38577));
		totalAmountMap.put("3562000041357774",new BigDecimal(38570));
		totalAmountMap.put("3562000040519925",new BigDecimal(38557));
		totalAmountMap.put("3562000040550026",new BigDecimal(38440));
		totalAmountMap.put("3562000045170006",new BigDecimal(38439));
		totalAmountMap.put("3562000040522327",new BigDecimal(38409));
		totalAmountMap.put("3562000040942875",new BigDecimal(38404));
		totalAmountMap.put("3562000045014196",new BigDecimal(38369));
		totalAmountMap.put("3562000042136096",new BigDecimal(38313));
		totalAmountMap.put("3562000045175663",new BigDecimal(38276));
		totalAmountMap.put("3562000040517715",new BigDecimal(38218));
		totalAmountMap.put("3562000041191366",new BigDecimal(38205));
		totalAmountMap.put("3562000040535794",new BigDecimal(38190));
		totalAmountMap.put("3562000041952326",new BigDecimal(38140));
		totalAmountMap.put("3562000040542366",new BigDecimal(38130));
		totalAmountMap.put("3562000040552605",new BigDecimal(38112));
		totalAmountMap.put("3562000040678105",new BigDecimal(38069));
		totalAmountMap.put("3562000038110496",new BigDecimal(38042));
		totalAmountMap.put("3562000040541096",new BigDecimal(38017));
		totalAmountMap.put("3562000040543386",new BigDecimal(37926));
		totalAmountMap.put("3562000036113765",new BigDecimal(37859));
		totalAmountMap.put("3562000041422208",new BigDecimal(37830));
		totalAmountMap.put("3562000040540955",new BigDecimal(37827));
		totalAmountMap.put("3562000040533546",new BigDecimal(37738));
		totalAmountMap.put("3562000040540575",new BigDecimal(37735));
		totalAmountMap.put("3562000036527844",new BigDecimal(37582));
		totalAmountMap.put("3562000045188763",new BigDecimal(37500));
		totalAmountMap.put("3562000040536285",new BigDecimal(37494));
		totalAmountMap.put("3562000045170694",new BigDecimal(37484));
		totalAmountMap.put("3562000041744616",new BigDecimal(37469));
		totalAmountMap.put("3562000045110437",new BigDecimal(37358));
		totalAmountMap.put("3562000040538964",new BigDecimal(37286));
		totalAmountMap.put("3562000040545195",new BigDecimal(37236));
		totalAmountMap.put("3562000036115295",new BigDecimal(37230));
		totalAmountMap.put("3562000040546945",new BigDecimal(37205));
		totalAmountMap.put("3562000040548275",new BigDecimal(37144));
		totalAmountMap.put("3562000045007674",new BigDecimal(37108));
		totalAmountMap.put("3562000040534276",new BigDecimal(37084));
		totalAmountMap.put("3562000037990154",new BigDecimal(37070));
		totalAmountMap.put("3562000040528735",new BigDecimal(37065));
		totalAmountMap.put("3562000040543975",new BigDecimal(37057));
		totalAmountMap.put("3562000040555445",new BigDecimal(37032));
		totalAmountMap.put("3562000041454337",new BigDecimal(37005));
		totalAmountMap.put("3562000037852753",new BigDecimal(37003));
		totalAmountMap.put("3562000040522466",new BigDecimal(36897));
		totalAmountMap.put("3562000041612916",new BigDecimal(36894));
		totalAmountMap.put("3562000040550954",new BigDecimal(36870));
		totalAmountMap.put("3562000040532616",new BigDecimal(36858));
		totalAmountMap.put("3562000045120565",new BigDecimal(36800));
		totalAmountMap.put("3562000041603726",new BigDecimal(36715));
		totalAmountMap.put("3562000041595963",new BigDecimal(36705));
		totalAmountMap.put("3562000036884663",new BigDecimal(36670));
		totalAmountMap.put("3562000040541427",new BigDecimal(36561));
		totalAmountMap.put("3562000041610836",new BigDecimal(36531));
		totalAmountMap.put("3562000045094106",new BigDecimal(36508));
		totalAmountMap.put("3562000040984336",new BigDecimal(36500));
		totalAmountMap.put("3562000045120736",new BigDecimal(36500));
		totalAmountMap.put("3562000043403727",new BigDecimal(36276));
		totalAmountMap.put("3562000041286535",new BigDecimal(36231));
		totalAmountMap.put("3562000040529815",new BigDecimal(36201));
		totalAmountMap.put("3562000041123837",new BigDecimal(36193));
		totalAmountMap.put("3562000041545495",new BigDecimal(36180));
		totalAmountMap.put("3562000040553395",new BigDecimal(36145));
		totalAmountMap.put("3562000040517994",new BigDecimal(36047));
		totalAmountMap.put("3562000045087274",new BigDecimal(36039));
		totalAmountMap.put("3562000040532526",new BigDecimal(36025));
		totalAmountMap.put("3562000045166194",new BigDecimal(36000));
		totalAmountMap.put("3562000040441067",new BigDecimal(36000));
		totalAmountMap.put("3562000041724946",new BigDecimal(35991));
		totalAmountMap.put("3562000041009107",new BigDecimal(35922));
		totalAmountMap.put("3562000036115385",new BigDecimal(35888));
		totalAmountMap.put("3562000041123927",new BigDecimal(35855));
		totalAmountMap.put("3562000040535155",new BigDecimal(35821));
		totalAmountMap.put("3562000041716485",new BigDecimal(35800));
		totalAmountMap.put("3562000042680036",new BigDecimal(35695));
		totalAmountMap.put("3562000040523466",new BigDecimal(35669));
		totalAmountMap.put("3562000040542755",new BigDecimal(35531));
		totalAmountMap.put("3562000040541936",new BigDecimal(35510));
		totalAmountMap.put("3562000043398964",new BigDecimal(35483));
		totalAmountMap.put("3562000040554026",new BigDecimal(35476));
		totalAmountMap.put("3562000040520156",new BigDecimal(35369));
		totalAmountMap.put("3562000039298005",new BigDecimal(35272));
		totalAmountMap.put("3562000040909485",new BigDecimal(35189));
		totalAmountMap.put("3562000036113755",new BigDecimal(35120));
		totalAmountMap.put("3562000040876025",new BigDecimal(35108));
		totalAmountMap.put("3562000045140376",new BigDecimal(35080));
		totalAmountMap.put("3562000041658405",new BigDecimal(35057));
		totalAmountMap.put("3562000040519654",new BigDecimal(35043));
		totalAmountMap.put("3562000040543056",new BigDecimal(35022));
		totalAmountMap.put("3562000045173046",new BigDecimal(35021));
		totalAmountMap.put("3562000040546615",new BigDecimal(34981));
		totalAmountMap.put("3562000042965573",new BigDecimal(34963));
		totalAmountMap.put("3562000040538754",new BigDecimal(34955));
		totalAmountMap.put("3562000045010417",new BigDecimal(34873));
		totalAmountMap.put("3562000040544386",new BigDecimal(34848));
		totalAmountMap.put("3562000040531916",new BigDecimal(34825));
		totalAmountMap.put("3562000040553365",new BigDecimal(34792));
		totalAmountMap.put("3562000041214028",new BigDecimal(34747));
		totalAmountMap.put("3562000037959493",new BigDecimal(34735));
		totalAmountMap.put("3562000041008147",new BigDecimal(34636));
		totalAmountMap.put("3562000040902685",new BigDecimal(34565));
		totalAmountMap.put("3562000044991326",new BigDecimal(34550));
		totalAmountMap.put("3562000042169864",new BigDecimal(34549));
		totalAmountMap.put("3562000040534066",new BigDecimal(34479));
		totalAmountMap.put("3562000040729226",new BigDecimal(34456));
		totalAmountMap.put("3562000038182426",new BigDecimal(34332));
		totalAmountMap.put("3562000040522366",new BigDecimal(34318));
		totalAmountMap.put("3562000040544626",new BigDecimal(34304));
		totalAmountMap.put("3562000041609815",new BigDecimal(34296));
		totalAmountMap.put("3562000041405836",new BigDecimal(34248));
		totalAmountMap.put("3562000040522137",new BigDecimal(34216));
		totalAmountMap.put("3562000040555225",new BigDecimal(34204));
		totalAmountMap.put("3562000040522496",new BigDecimal(34186));
		totalAmountMap.put("3562000041039855",new BigDecimal(34095));
		totalAmountMap.put("3562000045177335",new BigDecimal(34076));
		totalAmountMap.put("3562000041403348",new BigDecimal(34067));
		totalAmountMap.put("3562000040528615",new BigDecimal(34061));
		totalAmountMap.put("3562000040524565",new BigDecimal(34026));
		totalAmountMap.put("3562000040550764",new BigDecimal(33963));
		totalAmountMap.put("3562000040527465",new BigDecimal(33948));
		totalAmountMap.put("3562000045213356",new BigDecimal(33933));
		totalAmountMap.put("3562000040521985",new BigDecimal(33906));
		totalAmountMap.put("3562000040524417",new BigDecimal(33891));
		totalAmountMap.put("3562000044973226",new BigDecimal(33805));
		totalAmountMap.put("3562000040539395",new BigDecimal(33790));
		totalAmountMap.put("3562000040552754",new BigDecimal(33746));
		totalAmountMap.put("3562000040535475",new BigDecimal(33721));
		totalAmountMap.put("3562000037670064",new BigDecimal(33704));
		totalAmountMap.put("3562000040555235",new BigDecimal(33673));
		totalAmountMap.put("3562000041416317",new BigDecimal(33636));
		totalAmountMap.put("3562000040535455",new BigDecimal(33557));
		totalAmountMap.put("3562000045027845",new BigDecimal(33557));
		totalAmountMap.put("3562000045190006",new BigDecimal(33501));
		totalAmountMap.put("3562000045161146",new BigDecimal(33460));
		totalAmountMap.put("3562000045181515",new BigDecimal(33416));
		totalAmountMap.put("3562000040543156",new BigDecimal(33383));
		totalAmountMap.put("3562000043418276",new BigDecimal(33283));
		totalAmountMap.put("3562000036116006",new BigDecimal(33200));
		totalAmountMap.put("3562000040552654",new BigDecimal(33190));
		totalAmountMap.put("3562000040520746",new BigDecimal(33160));
		totalAmountMap.put("3562000041751016",new BigDecimal(33090));
		totalAmountMap.put("3562000040519774",new BigDecimal(33085));
		totalAmountMap.put("3562000044969804",new BigDecimal(33075));
		totalAmountMap.put("3562000038041056",new BigDecimal(33050));
		totalAmountMap.put("3562000045186783",new BigDecimal(33000));
		totalAmountMap.put("3562000045101266",new BigDecimal(33000));
		totalAmountMap.put("3562000040992285",new BigDecimal(32983));
		totalAmountMap.put("3562000040526316",new BigDecimal(32977));
		totalAmountMap.put("3562000045056364",new BigDecimal(32956));
		totalAmountMap.put("3562000040541875",new BigDecimal(32917));
		totalAmountMap.put("3562000040531995",new BigDecimal(32900));
		totalAmountMap.put("3562000045120595",new BigDecimal(32801));
		totalAmountMap.put("3562000036359573",new BigDecimal(32745));
		totalAmountMap.put("3562000040550295",new BigDecimal(32679));
		totalAmountMap.put("3562000040523447",new BigDecimal(32647));
		totalAmountMap.put("3562000040541476",new BigDecimal(32623));
		totalAmountMap.put("3562000038110417",new BigDecimal(32580));
		totalAmountMap.put("3562000040541736",new BigDecimal(32471));
		totalAmountMap.put("3562000045145815",new BigDecimal(32460));
		totalAmountMap.put("3562000043936065",new BigDecimal(32459));
		totalAmountMap.put("3562000044972126",new BigDecimal(32406));
		totalAmountMap.put("3562000040542376",new BigDecimal(32369));
		totalAmountMap.put("3562000040527625",new BigDecimal(32331));
		totalAmountMap.put("3562000040556015",new BigDecimal(32288));
		totalAmountMap.put("3562000040535805",new BigDecimal(32274));
		totalAmountMap.put("3562000040535046",new BigDecimal(32264));
		totalAmountMap.put("3562000045171216",new BigDecimal(32261));
		totalAmountMap.put("3562000040526385",new BigDecimal(32225));
		totalAmountMap.put("3562000040533855",new BigDecimal(32222));
		totalAmountMap.put("3562000040526095",new BigDecimal(32207));
		totalAmountMap.put("3562000040542616",new BigDecimal(32149));
		totalAmountMap.put("3562000041143676",new BigDecimal(32145));
		totalAmountMap.put("3562000041370755",new BigDecimal(32123));
		totalAmountMap.put("3562000040546635",new BigDecimal(32107));
		totalAmountMap.put("3562000039094784",new BigDecimal(32086));
		totalAmountMap.put("3562000040549745",new BigDecimal(32064));
		totalAmountMap.put("3562000039147845",new BigDecimal(32040));
		totalAmountMap.put("3562000045098384",new BigDecimal(32000));
		totalAmountMap.put("3562000040525554",new BigDecimal(31970));
		totalAmountMap.put("3562000041115356",new BigDecimal(31950));
		totalAmountMap.put("3562000040551416",new BigDecimal(31925));
		totalAmountMap.put("3562000040553416",new BigDecimal(31913));
		totalAmountMap.put("3562000045182106",new BigDecimal(31900));
		totalAmountMap.put("3562000042127885",new BigDecimal(31882));
		totalAmountMap.put("3562000041146496",new BigDecimal(31843));
		totalAmountMap.put("3562000041426975",new BigDecimal(31840));
		totalAmountMap.put("3562000040539784",new BigDecimal(31769));
		totalAmountMap.put("3562000041599814",new BigDecimal(31744));
		totalAmountMap.put("3562000040532746",new BigDecimal(31739));
		totalAmountMap.put("3562000045095634",new BigDecimal(31735));
		totalAmountMap.put("3562000040532366",new BigDecimal(31713));
		totalAmountMap.put("3562000040549894",new BigDecimal(31697));
		totalAmountMap.put("3562000040551116",new BigDecimal(31603));
		totalAmountMap.put("3562000040521086",new BigDecimal(31576));
		totalAmountMap.put("3562000040539226",new BigDecimal(31565));
		totalAmountMap.put("3562000041510137",new BigDecimal(31565));
		totalAmountMap.put("3562000040545475",new BigDecimal(31547));
		totalAmountMap.put("3562000040523276",new BigDecimal(31539));
		totalAmountMap.put("3562000040530716",new BigDecimal(31501));
		totalAmountMap.put("3562000045124806",new BigDecimal(31486));
		totalAmountMap.put("3562000041710447",new BigDecimal(31426));
		totalAmountMap.put("3562000040546046",new BigDecimal(31321));
		totalAmountMap.put("3562000036936454",new BigDecimal(31315));
		totalAmountMap.put("3562000040538136",new BigDecimal(31250));
		totalAmountMap.put("3562000040545984",new BigDecimal(31150));
		totalAmountMap.put("3562000045134356",new BigDecimal(31141));
		totalAmountMap.put("3562000041416995",new BigDecimal(31135));
		totalAmountMap.put("3562000040518605",new BigDecimal(31050));
		totalAmountMap.put("3562000041293017",new BigDecimal(31028));
		totalAmountMap.put("3562000044982894",new BigDecimal(31000));
		totalAmountMap.put("3562000040519794",new BigDecimal(30935));
		totalAmountMap.put("3562000040535416",new BigDecimal(30910));
		totalAmountMap.put("3562000040530486",new BigDecimal(30893));
		totalAmountMap.put("3562000040518065",new BigDecimal(30892));
		totalAmountMap.put("3562000041525974",new BigDecimal(30752));
		totalAmountMap.put("3562000040518954",new BigDecimal(30723));
		totalAmountMap.put("3562000040528185",new BigDecimal(30722));
		totalAmountMap.put("3562000040531196",new BigDecimal(30702));
		totalAmountMap.put("3562000040543726",new BigDecimal(30678));
		totalAmountMap.put("3562000040526894",new BigDecimal(30667));
		totalAmountMap.put("3562000041419327",new BigDecimal(30659));
		totalAmountMap.put("3562000037997843",new BigDecimal(30610));
		totalAmountMap.put("3562000040538825",new BigDecimal(30597));
		totalAmountMap.put("3562000040517735",new BigDecimal(30570));
		totalAmountMap.put("3562000040543307",new BigDecimal(30522));
		totalAmountMap.put("3562000041279525",new BigDecimal(30515));
		totalAmountMap.put("3562000040519185",new BigDecimal(30502));
		totalAmountMap.put("3562000041329585",new BigDecimal(30477));
		totalAmountMap.put("3562000041584046",new BigDecimal(30442));
		totalAmountMap.put("3562000045164185",new BigDecimal(30400));
		totalAmountMap.put("3562000045122017",new BigDecimal(30400));
		totalAmountMap.put("3562000040523317",new BigDecimal(30390));
		totalAmountMap.put("3562000040519645",new BigDecimal(30378));
		totalAmountMap.put("3562000037680345",new BigDecimal(30340));
		totalAmountMap.put("3562000045189015",new BigDecimal(30320));
		totalAmountMap.put("3562000041922217",new BigDecimal(30279));
		totalAmountMap.put("3562000041077275",new BigDecimal(30267));
		totalAmountMap.put("3562000040406047",new BigDecimal(30200));
		totalAmountMap.put("3562000041639864",new BigDecimal(30190));
		totalAmountMap.put("3562000040531736",new BigDecimal(30140));
		totalAmountMap.put("3562000045153246",new BigDecimal(30140));
		totalAmountMap.put("3562000041600595",new BigDecimal(30110));
		totalAmountMap.put("3562000037803874",new BigDecimal(30100));
		totalAmountMap.put("3562000043851055",new BigDecimal(30080));
		totalAmountMap.put("3562000040518945",new BigDecimal(30046));
		totalAmountMap.put("3562000043446337",new BigDecimal(30038));
		totalAmountMap.put("3562000037665733",new BigDecimal(30019));
		totalAmountMap.put("3562000036037874",new BigDecimal(30000));
		totalAmountMap.put("3562000042136795",new BigDecimal(29985));
		totalAmountMap.put("3562000042882155",new BigDecimal(29976));
		totalAmountMap.put("3562000040526006",new BigDecimal(29875));
		totalAmountMap.put("3562000040533117",new BigDecimal(29668));
		totalAmountMap.put("3562000040519465",new BigDecimal(29665));
		totalAmountMap.put("3562000040549925",new BigDecimal(29664));
		totalAmountMap.put("3562000041427337",new BigDecimal(29641));
		totalAmountMap.put("3562000040520137",new BigDecimal(29583));
		totalAmountMap.put("3562000042728825",new BigDecimal(29579));
		totalAmountMap.put("3562000040523616",new BigDecimal(29540));
		totalAmountMap.put("3562000040542555",new BigDecimal(29531));
		totalAmountMap.put("3562000040549705",new BigDecimal(29522));
		totalAmountMap.put("3562000040544975",new BigDecimal(29440));
		totalAmountMap.put("3562000045173674",new BigDecimal(29390));
		totalAmountMap.put("3562000041467694",new BigDecimal(29352));
		totalAmountMap.put("3562000040519735",new BigDecimal(29269));
		totalAmountMap.put("3562000041630906",new BigDecimal(29220));
		totalAmountMap.put("3562000045075953",new BigDecimal(29209));
		totalAmountMap.put("3562000040518874",new BigDecimal(29209));
		totalAmountMap.put("3562000040956215",new BigDecimal(29199));
		totalAmountMap.put("3562000040777325",new BigDecimal(29196));
		totalAmountMap.put("3562000040539085",new BigDecimal(29183));
		totalAmountMap.put("3562000041000627",new BigDecimal(29142));
		totalAmountMap.put("3562000044977753",new BigDecimal(29140));
		totalAmountMap.put("3562000040541506",new BigDecimal(29092));
		totalAmountMap.put("3562000040549336",new BigDecimal(29066));
		totalAmountMap.put("3562000043981874",new BigDecimal(29017));
		totalAmountMap.put("3562000041606155",new BigDecimal(29017));
		totalAmountMap.put("3562000045188734",new BigDecimal(29000));
		totalAmountMap.put("3562000045176435",new BigDecimal(28989));
		totalAmountMap.put("3562000040540466",new BigDecimal(28887));
		totalAmountMap.put("3562000040545894",new BigDecimal(28877));
		totalAmountMap.put("3562000040529295",new BigDecimal(28869));
		totalAmountMap.put("3562000041739106",new BigDecimal(28862));
		totalAmountMap.put("3562000042070407",new BigDecimal(28850));
		totalAmountMap.put("3562000040555384",new BigDecimal(28847));
		totalAmountMap.put("3562000041952574",new BigDecimal(28764));
		totalAmountMap.put("3562000041324737",new BigDecimal(28735));
		totalAmountMap.put("3562000041412197",new BigDecimal(28733));
		totalAmountMap.put("3562000040786384",new BigDecimal(28642));
		totalAmountMap.put("3562000045189225",new BigDecimal(28600));
		totalAmountMap.put("3562000040526365",new BigDecimal(28581));
		totalAmountMap.put("3562000040532186",new BigDecimal(28576));
		totalAmountMap.put("3562000040537794",new BigDecimal(28556));
		totalAmountMap.put("3562000040525984",new BigDecimal(28535));
		totalAmountMap.put("3562000040542207",new BigDecimal(28521));
		totalAmountMap.put("3562000041610466",new BigDecimal(28514));
		totalAmountMap.put("3562000045146845",new BigDecimal(28500));
		totalAmountMap.put("3562000040540795",new BigDecimal(28500));
		totalAmountMap.put("3562000043404596",new BigDecimal(28435));
		totalAmountMap.put("3562000043010556",new BigDecimal(28426));
		totalAmountMap.put("3562000041234807",new BigDecimal(28318));
		totalAmountMap.put("3562000040526426",new BigDecimal(28314));
		totalAmountMap.put("3562000040551216",new BigDecimal(28306));
		totalAmountMap.put("3562000040646465",new BigDecimal(28291));
		totalAmountMap.put("3562000040534885",new BigDecimal(28290));
		totalAmountMap.put("3562000040518355",new BigDecimal(28279));
		totalAmountMap.put("3562000040549754",new BigDecimal(28277));
		totalAmountMap.put("3562000041631366",new BigDecimal(28274));
		totalAmountMap.put("3562000040555094",new BigDecimal(28254));
		totalAmountMap.put("3562000037805644",new BigDecimal(28237));
		totalAmountMap.put("3562000040542655",new BigDecimal(28189));
		totalAmountMap.put("3562000040546884",new BigDecimal(28124));
		totalAmountMap.put("3562000040526584",new BigDecimal(28114));
		totalAmountMap.put("3562000041564426",new BigDecimal(28033));
		totalAmountMap.put("3562000040526236",new BigDecimal(28018));
		totalAmountMap.put("3562000045120765",new BigDecimal(28000));
		totalAmountMap.put("3562000036119236",new BigDecimal(27970));
		totalAmountMap.put("3562000045190085",new BigDecimal(27920));
		totalAmountMap.put("3562000040533665",new BigDecimal(27907));
		totalAmountMap.put("3562000042983175",new BigDecimal(27900));
		totalAmountMap.put("3562000040527436",new BigDecimal(27874));
		totalAmountMap.put("3562000041639275",new BigDecimal(27873));
		totalAmountMap.put("3562000040535006",new BigDecimal(27871));
		totalAmountMap.put("3562000040541806",new BigDecimal(27845));
		totalAmountMap.put("3562000042632636",new BigDecimal(27840));
		totalAmountMap.put("3562000040537815",new BigDecimal(27838));
		totalAmountMap.put("3562000038054046",new BigDecimal(27800));
		totalAmountMap.put("3562000035963105",new BigDecimal(27791));
		totalAmountMap.put("3562000040531247",new BigDecimal(27708));
		totalAmountMap.put("3562000037607045",new BigDecimal(27690));
		totalAmountMap.put("3562000045042765",new BigDecimal(27685));
		totalAmountMap.put("3562000045185484",new BigDecimal(27642));
		totalAmountMap.put("3562000044985873",new BigDecimal(27600));
		totalAmountMap.put("3562000041264855",new BigDecimal(27533));
		totalAmountMap.put("3562000041415117",new BigDecimal(27475));
		totalAmountMap.put("3562000043835136",new BigDecimal(27394));
		totalAmountMap.put("3562000040544456",new BigDecimal(27353));
		totalAmountMap.put("3562000040521056",new BigDecimal(27350));
		totalAmountMap.put("3562000040548884",new BigDecimal(27290));
		totalAmountMap.put("3562000043833296",new BigDecimal(27210));
		totalAmountMap.put("3562000045195345",new BigDecimal(27197));
		totalAmountMap.put("3562000040532665",new BigDecimal(27196));
		totalAmountMap.put("3562000040517705",new BigDecimal(27174));
		totalAmountMap.put("3562000040547935",new BigDecimal(27031));
		totalAmountMap.put("3562000040815905",new BigDecimal(27020));
		totalAmountMap.put("3562000041657524",new BigDecimal(27017));
		totalAmountMap.put("3562000045124196",new BigDecimal(27000));
		totalAmountMap.put("3562000045186435",new BigDecimal(26964));
		totalAmountMap.put("3562000037110995",new BigDecimal(26947));
		totalAmountMap.put("3562000040553046",new BigDecimal(26938));
		totalAmountMap.put("3562000040528594",new BigDecimal(26935));
		totalAmountMap.put("3562000041423487",new BigDecimal(26933));
		totalAmountMap.put("3562000040517805",new BigDecimal(26926));
		totalAmountMap.put("3562000040518016",new BigDecimal(26913));
		totalAmountMap.put("3562000040526075",new BigDecimal(26850));
		totalAmountMap.put("3562000040539255",new BigDecimal(26849));
		totalAmountMap.put("3562000040519874",new BigDecimal(26825));
		totalAmountMap.put("3562000040529954",new BigDecimal(26720));
		totalAmountMap.put("3562000040545515",new BigDecimal(26685));
		totalAmountMap.put("3562000040534076",new BigDecimal(26680));
		totalAmountMap.put("3562000040543785",new BigDecimal(26660));
		totalAmountMap.put("3562000041735605",new BigDecimal(26660));
		totalAmountMap.put("3562000045173085",new BigDecimal(26600));
		totalAmountMap.put("3562000036237195",new BigDecimal(26520));
		totalAmountMap.put("3562000040547945",new BigDecimal(26512));
		totalAmountMap.put("3562000040522675",new BigDecimal(26500));
		totalAmountMap.put("3562000040525684",new BigDecimal(26496));
		totalAmountMap.put("3562000043925046",new BigDecimal(26489));
		totalAmountMap.put("3562000041650326",new BigDecimal(26446));
		totalAmountMap.put("3562000041425865",new BigDecimal(26369));
		totalAmountMap.put("3562000041613846",new BigDecimal(26361));
		totalAmountMap.put("3562000041950406",new BigDecimal(26352));
		totalAmountMap.put("3562000040536385",new BigDecimal(26343));
		totalAmountMap.put("3562000040748994",new BigDecimal(26302));
		totalAmountMap.put("3562000045198264",new BigDecimal(26296));
		totalAmountMap.put("3562000045198704",new BigDecimal(26241));
		totalAmountMap.put("3562000040517964",new BigDecimal(26237));
		totalAmountMap.put("3562000040539764",new BigDecimal(26191));
		totalAmountMap.put("3562000040530066",new BigDecimal(26178));
		totalAmountMap.put("3562000041081307",new BigDecimal(26166));
		totalAmountMap.put("3562000040521037",new BigDecimal(26120));
		totalAmountMap.put("3562000040536554",new BigDecimal(26107));
		totalAmountMap.put("3562000040522665",new BigDecimal(26100));
		totalAmountMap.put("3562000040529255",new BigDecimal(26072));
		totalAmountMap.put("3562000041432817",new BigDecimal(26034));
		totalAmountMap.put("3562000044055195",new BigDecimal(26000));
		totalAmountMap.put("3562000045104147",new BigDecimal(26000));
		totalAmountMap.put("3562000045096415",new BigDecimal(25979));
		totalAmountMap.put("3562000038499924",new BigDecimal(25932));
		totalAmountMap.put("3562000041113228",new BigDecimal(25932));
		totalAmountMap.put("3562000036114137",new BigDecimal(25930));
		totalAmountMap.put("3562000043404297",new BigDecimal(25846));
		totalAmountMap.put("3562000038347735",new BigDecimal(25800));
		totalAmountMap.put("3562000040650894",new BigDecimal(25800));
		totalAmountMap.put("3562000040553065",new BigDecimal(25790));
		totalAmountMap.put("3562000041062486",new BigDecimal(25742));
		totalAmountMap.put("3562000040533086",new BigDecimal(25724));
		totalAmountMap.put("3562000040554206",new BigDecimal(25691));
		totalAmountMap.put("3562000040520875",new BigDecimal(25675));
		totalAmountMap.put("3562000040547994",new BigDecimal(25668));
		totalAmountMap.put("3562000040531565",new BigDecimal(25666));
		totalAmountMap.put("3562000045172265",new BigDecimal(25656));
		totalAmountMap.put("3562000040553455",new BigDecimal(25632));
		totalAmountMap.put("3562000045087804",new BigDecimal(25609));
		totalAmountMap.put("3562000045173915",new BigDecimal(25570));
		totalAmountMap.put("3562000040538426",new BigDecimal(25516));
		totalAmountMap.put("3562000040552246",new BigDecimal(25501));
		totalAmountMap.put("3562000040522456",new BigDecimal(25500));
		totalAmountMap.put("3562000040552365",new BigDecimal(25478));
		totalAmountMap.put("3562000042809175",new BigDecimal(25416));
		totalAmountMap.put("3562000040555274",new BigDecimal(25411));
		totalAmountMap.put("3562000045164835",new BigDecimal(25404));
		totalAmountMap.put("3562000040842107",new BigDecimal(25370));
		totalAmountMap.put("3562000041682605",new BigDecimal(25353));
		totalAmountMap.put("3562000041304627",new BigDecimal(25330));
		totalAmountMap.put("3562000045015146",new BigDecimal(25325));
		totalAmountMap.put("3562000045131616",new BigDecimal(25315));
		totalAmountMap.put("3562000041550365",new BigDecimal(25300));
		totalAmountMap.put("3562000041604955",new BigDecimal(25299));
		totalAmountMap.put("3562000041631307",new BigDecimal(25282));
		totalAmountMap.put("3562000045013486",new BigDecimal(25252));
		totalAmountMap.put("3562000045169025",new BigDecimal(25200));
		totalAmountMap.put("3562000045186773",new BigDecimal(25200));
		totalAmountMap.put("3562000040521736",new BigDecimal(25180));
		totalAmountMap.put("3562000041533066",new BigDecimal(25178));
		totalAmountMap.put("3562000041554326",new BigDecimal(25132));
		totalAmountMap.put("3562000040544695",new BigDecimal(25087));
		totalAmountMap.put("3562000038395074",new BigDecimal(25040));
		totalAmountMap.put("3562000040538195",new BigDecimal(25037));
		totalAmountMap.put("3562000037192905",new BigDecimal(25030));
		totalAmountMap.put("3562000044996673",new BigDecimal(25000));
		totalAmountMap.put("3562000041667435",new BigDecimal(24993));
		totalAmountMap.put("3562000040533775",new BigDecimal(24963));
		totalAmountMap.put("3562000045199824",new BigDecimal(24948));
		totalAmountMap.put("3562000041537165",new BigDecimal(24934));
		totalAmountMap.put("3562000041072356",new BigDecimal(24914));
		totalAmountMap.put("3562000045194505",new BigDecimal(24900));
		totalAmountMap.put("3562000045139195",new BigDecimal(24900));
		totalAmountMap.put("3562000040526126",new BigDecimal(24869));
		totalAmountMap.put("3562000040554684",new BigDecimal(24848));
		totalAmountMap.put("3562000040538395",new BigDecimal(24830));
		totalAmountMap.put("3562000040544726",new BigDecimal(24829));
		totalAmountMap.put("3562000045104247",new BigDecimal(24820));
		totalAmountMap.put("3562000038038236",new BigDecimal(24776));
		totalAmountMap.put("3562000040525106",new BigDecimal(24700));
		totalAmountMap.put("3562000040535246",new BigDecimal(24670));
		totalAmountMap.put("3562000040528554",new BigDecimal(24657));
		totalAmountMap.put("3562000040536075",new BigDecimal(24643));
		totalAmountMap.put("3562000040521456",new BigDecimal(24625));
		totalAmountMap.put("3562000040519625",new BigDecimal(24617));
		totalAmountMap.put("3562000041590545",new BigDecimal(24610));
		totalAmountMap.put("3562000040553465",new BigDecimal(24591));
		totalAmountMap.put("3562000040520665",new BigDecimal(24550));
		totalAmountMap.put("3562000041707735",new BigDecimal(24521));
		totalAmountMap.put("3562000045109874",new BigDecimal(24500));
		totalAmountMap.put("3562000040530695",new BigDecimal(24488));
		totalAmountMap.put("3562000040528246",new BigDecimal(24470));
		totalAmountMap.put("3562000040535316",new BigDecimal(24448));
		totalAmountMap.put("3562000040533595",new BigDecimal(24417));
		totalAmountMap.put("3562000040520836",new BigDecimal(24413));
		totalAmountMap.put("3562000045168035",new BigDecimal(24400));
		totalAmountMap.put("3562000044396206",new BigDecimal(24400));
		totalAmountMap.put("3562000045198064",new BigDecimal(24326));
		totalAmountMap.put("3562000040527265",new BigDecimal(24304));
		totalAmountMap.put("3562000040549206",new BigDecimal(24301));
		totalAmountMap.put("3562000045007584",new BigDecimal(24237));
		totalAmountMap.put("3562000040537905",new BigDecimal(24176));
		totalAmountMap.put("3562000036670235",new BigDecimal(24163));
		totalAmountMap.put("3562000039209774",new BigDecimal(24136));
		totalAmountMap.put("3562000040522047",new BigDecimal(24119));
		totalAmountMap.put("3562000040551375",new BigDecimal(24118));
		totalAmountMap.put("3562000040528426",new BigDecimal(24108));
		totalAmountMap.put("3562000040532846",new BigDecimal(24099));
		totalAmountMap.put("3562000040548574",new BigDecimal(24091));
		totalAmountMap.put("3562000040527216",new BigDecimal(24040));
		totalAmountMap.put("3562000040518406",new BigDecimal(24032));
		totalAmountMap.put("3562000036445664",new BigDecimal(24022));
		totalAmountMap.put("3562000045028684",new BigDecimal(24019));
		totalAmountMap.put("3562000037954624",new BigDecimal(23988));
		totalAmountMap.put("3562000040548845",new BigDecimal(23966));
		totalAmountMap.put("3562000041844137",new BigDecimal(23951));
		totalAmountMap.put("3562000045170864",new BigDecimal(23927));
		totalAmountMap.put("3562000040539564",new BigDecimal(23926));
		totalAmountMap.put("3562000040544895",new BigDecimal(23920));
		totalAmountMap.put("3562000040540685",new BigDecimal(23919));
		totalAmountMap.put("3562000041488605",new BigDecimal(23899));
		totalAmountMap.put("3562000045096744",new BigDecimal(23877));
		totalAmountMap.put("3562000041608306",new BigDecimal(23858));
		totalAmountMap.put("3562000042132837",new BigDecimal(23839));
		totalAmountMap.put("3562000044976354",new BigDecimal(23825));
		totalAmountMap.put("3562000040526416",new BigDecimal(23815));
		totalAmountMap.put("3562000038788572",new BigDecimal(23792));
		totalAmountMap.put("3562000041413907",new BigDecimal(23782));
		totalAmountMap.put("3562000040541995",new BigDecimal(23740));
		totalAmountMap.put("3562000040535265",new BigDecimal(23737));
		totalAmountMap.put("3562000040527006",new BigDecimal(23731));
		totalAmountMap.put("3562000042641946",new BigDecimal(23711));
		totalAmountMap.put("3562000040552615",new BigDecimal(23685));
		totalAmountMap.put("3562000045195374",new BigDecimal(23600));
		totalAmountMap.put("3562000040542975",new BigDecimal(23581));
		totalAmountMap.put("3562000040543936",new BigDecimal(23581));
		totalAmountMap.put("3562000040548754",new BigDecimal(23574));
		totalAmountMap.put("3562000040532217",new BigDecimal(23565));
		totalAmountMap.put("3562000038023885",new BigDecimal(23522));
		totalAmountMap.put("3562000036114865",new BigDecimal(23488));
		totalAmountMap.put("3562000040525974",new BigDecimal(23431));
		totalAmountMap.put("3562000041500166",new BigDecimal(23418));
		totalAmountMap.put("3562000045177154",new BigDecimal(23416));
		totalAmountMap.put("3562000040531217",new BigDecimal(23334));
		totalAmountMap.put("3562000041749426",new BigDecimal(23316));
		totalAmountMap.put("3562000040520076",new BigDecimal(23311));
		totalAmountMap.put("3562000040523196",new BigDecimal(23305));
		totalAmountMap.put("3562000040540816",new BigDecimal(23304));
		totalAmountMap.put("3562000045177015",new BigDecimal(23300));
		totalAmountMap.put("3562000040534366",new BigDecimal(23292));
		totalAmountMap.put("3562000041605485",new BigDecimal(23244));
		totalAmountMap.put("3562000045084116",new BigDecimal(23243));
		totalAmountMap.put("3562000040526615",new BigDecimal(23226));
		totalAmountMap.put("3562000040554664",new BigDecimal(23221));
		totalAmountMap.put("3562000040541276",new BigDecimal(23208));
		totalAmountMap.put("3562000040533066",new BigDecimal(23164));
		totalAmountMap.put("3562000040530256",new BigDecimal(23149));
		totalAmountMap.put("3562000040531716",new BigDecimal(23125));
		totalAmountMap.put("3562000040536006",new BigDecimal(23125));
		totalAmountMap.put("3562000040524746",new BigDecimal(23124));
		totalAmountMap.put("3562000040534906",new BigDecimal(23097));
		totalAmountMap.put("3562000040534626",new BigDecimal(23083));
		totalAmountMap.put("3562000040788205",new BigDecimal(23067));
		totalAmountMap.put("3562000040541356",new BigDecimal(23067));
		totalAmountMap.put("3562000040532417",new BigDecimal(23064));
		totalAmountMap.put("3562000045018745",new BigDecimal(23047));
		totalAmountMap.put("3562000039827394",new BigDecimal(23043));
		totalAmountMap.put("3562000045157844",new BigDecimal(23040));
		totalAmountMap.put("3562000040548255",new BigDecimal(23038));
		totalAmountMap.put("3562000040535525",new BigDecimal(23023));
		totalAmountMap.put("3562000040524196",new BigDecimal(23013));
		totalAmountMap.put("3562000045102575",new BigDecimal(23000));
		totalAmountMap.put("3562000040547635",new BigDecimal(22972));
		totalAmountMap.put("3562000037280925",new BigDecimal(22937));
		totalAmountMap.put("3562000040541536",new BigDecimal(22924));
		totalAmountMap.put("3562000040527285",new BigDecimal(22908));
		totalAmountMap.put("3562000045049964",new BigDecimal(22876));
		totalAmountMap.put("3562000041023937",new BigDecimal(22837));
		totalAmountMap.put("3562000045135815",new BigDecimal(22830));
		totalAmountMap.put("3562000041234218",new BigDecimal(22801));
		totalAmountMap.put("3562000040544327",new BigDecimal(22800));
		totalAmountMap.put("3562000037288963",new BigDecimal(22797));
		totalAmountMap.put("3562000045117146",new BigDecimal(22796));
		totalAmountMap.put("3562000040875215",new BigDecimal(22791));
		totalAmountMap.put("3562000040637715",new BigDecimal(22786));
		totalAmountMap.put("3562000041522765",new BigDecimal(22785));
		totalAmountMap.put("3562000040527894",new BigDecimal(22766));
		totalAmountMap.put("3562000040528925",new BigDecimal(22759));
		totalAmountMap.put("3562000040551764",new BigDecimal(22631));
		totalAmountMap.put("3562000042806446",new BigDecimal(22617));
		totalAmountMap.put("3562000040535864",new BigDecimal(22603));
		totalAmountMap.put("3562000040553845",new BigDecimal(22600));
		totalAmountMap.put("3562000041346396",new BigDecimal(22546));
		totalAmountMap.put("3562000041290506",new BigDecimal(22540));
		totalAmountMap.put("3562000040530227",new BigDecimal(22510));
		totalAmountMap.put("3562000044292785",new BigDecimal(22509));
		totalAmountMap.put("3562000040868663",new BigDecimal(22500));
		totalAmountMap.put("3562000040544746",new BigDecimal(22496));
		totalAmountMap.put("3562000045064395",new BigDecimal(22482));
		totalAmountMap.put("3562000040532496",new BigDecimal(22454));
		totalAmountMap.put("3562000041754336",new BigDecimal(22440));
		totalAmountMap.put("3562000040553326",new BigDecimal(22408));
		totalAmountMap.put("3562000041336775",new BigDecimal(22405));
		totalAmountMap.put("3562000045171436",new BigDecimal(22400));
		totalAmountMap.put("3562000037659823",new BigDecimal(22366));
		totalAmountMap.put("3562000040544946",new BigDecimal(22363));
		totalAmountMap.put("3562000043836475",new BigDecimal(22362));
		totalAmountMap.put("3562000040528226",new BigDecimal(22353));
		totalAmountMap.put("3562000041275295",new BigDecimal(22324));
		totalAmountMap.put("3562000041352466",new BigDecimal(22318));
		totalAmountMap.put("3562000040519545",new BigDecimal(22307));
		totalAmountMap.put("3562000045194465",new BigDecimal(22300));
		totalAmountMap.put("3562000038139954",new BigDecimal(22300));
		totalAmountMap.put("3562000040554406",new BigDecimal(22300));
		totalAmountMap.put("3562000040549784",new BigDecimal(22286));
		totalAmountMap.put("3562000040547645",new BigDecimal(22282));
		totalAmountMap.put("3562000040539905",new BigDecimal(22196));
		totalAmountMap.put("3562000041491047",new BigDecimal(22127));
		totalAmountMap.put("3562000043653395",new BigDecimal(22120));
		totalAmountMap.put("3562000040538436",new BigDecimal(22106));
		totalAmountMap.put("3562000040549236",new BigDecimal(22064));
		totalAmountMap.put("3562000040526905",new BigDecimal(22016));
		totalAmountMap.put("3562000040523636",new BigDecimal(22004));
		totalAmountMap.put("3562000045193126",new BigDecimal(22000));
		totalAmountMap.put("3562000045107815",new BigDecimal(22000));
		totalAmountMap.put("3562000041587944",new BigDecimal(22000));
		totalAmountMap.put("3562000041604137",new BigDecimal(21954));
		totalAmountMap.put("3562000040522836",new BigDecimal(21925));
		totalAmountMap.put("3562000037866473",new BigDecimal(21909));
		totalAmountMap.put("3562000045120785",new BigDecimal(21900));
		totalAmountMap.put("3562000040551246",new BigDecimal(21899));
		totalAmountMap.put("3562000040555973",new BigDecimal(21870));
		totalAmountMap.put("3562000045081925",new BigDecimal(21859));
		totalAmountMap.put("3562000041986484",new BigDecimal(21848));
		totalAmountMap.put("3562000040532017",new BigDecimal(21847));
		totalAmountMap.put("3562000040535126",new BigDecimal(21847));
		totalAmountMap.put("3562000040517654",new BigDecimal(21830));
		totalAmountMap.put("3562000041232937",new BigDecimal(21813));
		totalAmountMap.put("3562000045154065",new BigDecimal(21782));
		totalAmountMap.put("3562000040539974",new BigDecimal(21766));
		totalAmountMap.put("3562000042728884",new BigDecimal(21758));
		totalAmountMap.put("3562000040542086",new BigDecimal(21727));
		totalAmountMap.put("3562000040533785",new BigDecimal(21700));
		totalAmountMap.put("3562000040555354",new BigDecimal(21691));
		totalAmountMap.put("3562000042957914",new BigDecimal(21676));
		totalAmountMap.put("3562000041073546",new BigDecimal(21675));
		totalAmountMap.put("3562000040546935",new BigDecimal(21580));
		totalAmountMap.put("3562000036113885",new BigDecimal(21577));
		totalAmountMap.put("3562000040546745",new BigDecimal(21567));
		totalAmountMap.put("3562000040551725",new BigDecimal(21539));
		totalAmountMap.put("3562000040145186",new BigDecimal(21513));
		totalAmountMap.put("3562000040537385",new BigDecimal(21510));
		totalAmountMap.put("3562000042066735",new BigDecimal(21478));
		totalAmountMap.put("3562000036545534",new BigDecimal(21466));
		totalAmountMap.put("3562000045157145",new BigDecimal(21400));
		totalAmountMap.put("3562000042069185",new BigDecimal(21338));
		totalAmountMap.put("3562000041640207",new BigDecimal(21317));
		totalAmountMap.put("3562000040525915",new BigDecimal(21302));
		totalAmountMap.put("3562000042170317",new BigDecimal(21280));
		totalAmountMap.put("3562000040534486",new BigDecimal(21274));
		totalAmountMap.put("3562000040547674",new BigDecimal(21268));
		totalAmountMap.put("3562000041706165",new BigDecimal(21237));
		totalAmountMap.put("3562000045087683",new BigDecimal(21199));
		totalAmountMap.put("3562000041505495",new BigDecimal(21197));
		totalAmountMap.put("3562000041629835",new BigDecimal(21178));
		totalAmountMap.put("3562000038123166",new BigDecimal(21169));
		totalAmountMap.put("3562000040525475",new BigDecimal(21155));
		totalAmountMap.put("3562000040523985",new BigDecimal(21152));
		totalAmountMap.put("3562000040552065",new BigDecimal(21135));
		totalAmountMap.put("3562000036127994",new BigDecimal(21110));
		totalAmountMap.put("3562000040543626",new BigDecimal(21104));
		totalAmountMap.put("3562000044773326",new BigDecimal(21102));
		totalAmountMap.put("3562000041544775",new BigDecimal(21053));
		totalAmountMap.put("3562000037669592",new BigDecimal(21031));
		totalAmountMap.put("3562000041122807",new BigDecimal(21002));
		totalAmountMap.put("3562000040852974",new BigDecimal(21000));
		totalAmountMap.put("3562000045120716",new BigDecimal(21000));
		totalAmountMap.put("3562000041120896",new BigDecimal(21000));
		totalAmountMap.put("3562000039735934",new BigDecimal(21000));
		totalAmountMap.put("3562000045093694",new BigDecimal(21000));
		totalAmountMap.put("3562000040524955",new BigDecimal(20986));
		totalAmountMap.put("3562000038105854",new BigDecimal(20984));
		totalAmountMap.put("3562000040555853",new BigDecimal(20927));
		totalAmountMap.put("3562000036765253",new BigDecimal(20912));
		totalAmountMap.put("3562000040521147",new BigDecimal(20872));
		totalAmountMap.put("3562000045054505",new BigDecimal(20843));
		totalAmountMap.put("3562000041277046",new BigDecimal(20832));
		totalAmountMap.put("3562000040542575",new BigDecimal(20823));
		totalAmountMap.put("3562000045139216",new BigDecimal(20810));
		totalAmountMap.put("3562000045061505",new BigDecimal(20768));
		totalAmountMap.put("3562000042160616",new BigDecimal(20747));
		totalAmountMap.put("3562000040532127",new BigDecimal(20741));
		totalAmountMap.put("3562000040530806",new BigDecimal(20730));
		totalAmountMap.put("3562000040539854",new BigDecimal(20727));
		totalAmountMap.put("3562000040543585",new BigDecimal(20727));
		totalAmountMap.put("3562000040538564",new BigDecimal(20726));
		totalAmountMap.put("3562000040528326",new BigDecimal(20713));
		totalAmountMap.put("3562000040531795",new BigDecimal(20704));
		totalAmountMap.put("3562000040554545",new BigDecimal(20700));
		totalAmountMap.put("3562000040552964",new BigDecimal(20649));
		totalAmountMap.put("3562000045179235",new BigDecimal(20636));
		totalAmountMap.put("3562000045169115",new BigDecimal(20600));
		totalAmountMap.put("3562000040524366",new BigDecimal(20595));
		totalAmountMap.put("3562000040549954",new BigDecimal(20578));
		totalAmountMap.put("3562000041691075",new BigDecimal(20574));
		totalAmountMap.put("3562000040520855",new BigDecimal(20570));
		totalAmountMap.put("3562000041409227",new BigDecimal(20554));
		totalAmountMap.put("3562000045175914",new BigDecimal(20538));
		totalAmountMap.put("3562000040552426",new BigDecimal(20533));
		totalAmountMap.put("3562000040531486",new BigDecimal(20500));
		totalAmountMap.put("3562000040541327",new BigDecimal(20491));
		totalAmountMap.put("3562000040529645",new BigDecimal(20482));
		totalAmountMap.put("3562000040546954",new BigDecimal(20475));
		totalAmountMap.put("3562000040550336",new BigDecimal(20469));
		totalAmountMap.put("3562000041322008",new BigDecimal(20437));
		totalAmountMap.put("3562000040517854",new BigDecimal(20420));
		totalAmountMap.put("3562000045086524",new BigDecimal(20400));
		totalAmountMap.put("3562000045156524",new BigDecimal(20400));
		totalAmountMap.put("3562000036119216",new BigDecimal(20322));
		totalAmountMap.put("3562000040531027",new BigDecimal(20299));
		totalAmountMap.put("3562000037803525",new BigDecimal(20277));
		totalAmountMap.put("3562000043402756",new BigDecimal(20221));
		totalAmountMap.put("3562000040545295",new BigDecimal(20205));
		totalAmountMap.put("3562000040523117",new BigDecimal(20186));
		totalAmountMap.put("3562000041204896",new BigDecimal(20160));
		totalAmountMap.put("3562000040524575",new BigDecimal(20158));
		totalAmountMap.put("3562000041410497",new BigDecimal(20143));
		totalAmountMap.put("3562000040524706",new BigDecimal(20135));
		totalAmountMap.put("3562000040526625",new BigDecimal(20113));
		totalAmountMap.put("3562000041496715",new BigDecimal(20100));
		totalAmountMap.put("3562000040528046",new BigDecimal(20080));
		totalAmountMap.put("3562000041754894",new BigDecimal(20074));
		totalAmountMap.put("3562000041461565",new BigDecimal(20072));
		totalAmountMap.put("3562000040551574",new BigDecimal(20003));
		totalAmountMap.put("3562000041664994",new BigDecimal(20000));
		totalAmountMap.put("3562000043003087",new BigDecimal(20000));
		totalAmountMap.put("3562000041140487",new BigDecimal(20000));
		totalAmountMap.put("3562000045124066",new BigDecimal(19990));
		totalAmountMap.put("3562000040555663",new BigDecimal(19891));
		totalAmountMap.put("3562000044980535",new BigDecimal(19850));
		totalAmountMap.put("3562000040533147",new BigDecimal(19838));
		totalAmountMap.put("3562000045172236",new BigDecimal(19838));
		totalAmountMap.put("3562000040519664",new BigDecimal(19781));
		totalAmountMap.put("3562000040543926",new BigDecimal(19756));
		totalAmountMap.put("3562000038254854",new BigDecimal(19727));
		totalAmountMap.put("3562000040525954",new BigDecimal(19716));
		totalAmountMap.put("3562000040519026",new BigDecimal(19703));
		totalAmountMap.put("3562000042008746",new BigDecimal(19700));
		totalAmountMap.put("3562000041610726",new BigDecimal(19686));
		totalAmountMap.put("3562000041585225",new BigDecimal(19683));
		totalAmountMap.put("3562000036808683",new BigDecimal(19650));
		totalAmountMap.put("3562000040527635",new BigDecimal(19643));
		totalAmountMap.put("3562000040526326",new BigDecimal(19617));
		totalAmountMap.put("3562000040545036",new BigDecimal(19590));
		totalAmountMap.put("3562000041456745",new BigDecimal(19556));
		totalAmountMap.put("3562000041418027",new BigDecimal(19536));
		totalAmountMap.put("3562000040548175",new BigDecimal(19509));
		totalAmountMap.put("3562000040835395",new BigDecimal(19500));
		totalAmountMap.put("3562000045117465",new BigDecimal(19488));
		totalAmountMap.put("3562000040548106",new BigDecimal(19478));
		totalAmountMap.put("3562000040904447",new BigDecimal(19478));
		totalAmountMap.put("3562000045136146",new BigDecimal(19450));
		totalAmountMap.put("3562000041443666",new BigDecimal(19431));
		totalAmountMap.put("3562000038031227",new BigDecimal(19429));
		totalAmountMap.put("3562000036775523",new BigDecimal(19418));
		totalAmountMap.put("3562000040521027",new BigDecimal(19411));
		totalAmountMap.put("3562000040555653",new BigDecimal(19400));
		totalAmountMap.put("3562000040532795",new BigDecimal(19400));
		totalAmountMap.put("3562000045212536",new BigDecimal(19400));
		totalAmountMap.put("3562000037358494",new BigDecimal(19370));
		totalAmountMap.put("3562000040531186",new BigDecimal(19355));
		totalAmountMap.put("3562000045186335",new BigDecimal(19346));
		totalAmountMap.put("3562000039074974",new BigDecimal(19340));
		totalAmountMap.put("3562000040521546",new BigDecimal(19336));
		totalAmountMap.put("3562000042168475",new BigDecimal(19287));
		totalAmountMap.put("3562000040526605",new BigDecimal(19284));
		totalAmountMap.put("3562000040522396",new BigDecimal(19283));
		totalAmountMap.put("3562000037288583",new BigDecimal(19270));
		totalAmountMap.put("3562000037980793",new BigDecimal(19259));
		totalAmountMap.put("3562000040543356",new BigDecimal(19257));
		totalAmountMap.put("3562000040527455",new BigDecimal(19254));
		totalAmountMap.put("3562000040551845",new BigDecimal(19204));
		totalAmountMap.put("3562000037670145",new BigDecimal(19200));
		totalAmountMap.put("3562000036607563",new BigDecimal(19152));
		totalAmountMap.put("3562000040552136",new BigDecimal(19148));
		totalAmountMap.put("3562000041423467",new BigDecimal(19144));
		totalAmountMap.put("3562000042008546",new BigDecimal(19143));
		totalAmountMap.put("3562000038978952",new BigDecimal(19100));
		totalAmountMap.put("3562000045177853",new BigDecimal(19100));
		totalAmountMap.put("3562000041680216",new BigDecimal(19097));
		totalAmountMap.put("3562000040540096",new BigDecimal(19072));
		totalAmountMap.put("3562000040520356",new BigDecimal(19069));
		totalAmountMap.put("3562000040545495",new BigDecimal(19048));
		totalAmountMap.put("3562000040539475",new BigDecimal(19048));
		totalAmountMap.put("3562000040530017",new BigDecimal(19025));
		totalAmountMap.put("3562000040546185",new BigDecimal(19024));
		totalAmountMap.put("3562000045104156",new BigDecimal(19000));
		totalAmountMap.put("3562000040536095",new BigDecimal(18981));
		totalAmountMap.put("3562000041495165",new BigDecimal(18929));
		totalAmountMap.put("3562000040536815",new BigDecimal(18883));
		totalAmountMap.put("3562000040518136",new BigDecimal(18875));
		totalAmountMap.put("3562000040554126",new BigDecimal(18869));
		totalAmountMap.put("3562000040525745",new BigDecimal(18836));
		totalAmountMap.put("3562000042155306",new BigDecimal(18819));
		totalAmountMap.put("3562000042498635",new BigDecimal(18781));
		totalAmountMap.put("3562000041430087",new BigDecimal(18774));
		totalAmountMap.put("3562000040531516",new BigDecimal(18770));
		totalAmountMap.put("3562000041750974",new BigDecimal(18769));
		totalAmountMap.put("3562000045115426",new BigDecimal(18765));
		totalAmountMap.put("3562000043802127",new BigDecimal(18735));
		totalAmountMap.put("3562000042322477",new BigDecimal(18733));
		totalAmountMap.put("3562000040520196",new BigDecimal(18733));
		totalAmountMap.put("3562000042642137",new BigDecimal(18725));
		totalAmountMap.put("3562000040539055",new BigDecimal(18713));
		totalAmountMap.put("3562000040552905",new BigDecimal(18709));
		totalAmountMap.put("3562000042854664",new BigDecimal(18681));
		totalAmountMap.put("3562000041416746",new BigDecimal(18674));
		totalAmountMap.put("3562000041512307",new BigDecimal(18669));
		totalAmountMap.put("3562000037655672",new BigDecimal(18635));
		totalAmountMap.put("3562000040527605",new BigDecimal(18633));
		totalAmountMap.put("3562000036048805",new BigDecimal(18614));
		totalAmountMap.put("3562000042161916",new BigDecimal(18591));
		totalAmountMap.put("3562000040527116",new BigDecimal(18586));
		totalAmountMap.put("3562000040518835",new BigDecimal(18563));
		totalAmountMap.put("3562000040542076",new BigDecimal(18561));
		totalAmountMap.put("3562000038267105",new BigDecimal(18557));
		totalAmountMap.put("3562000040538805",new BigDecimal(18550));
		totalAmountMap.put("3562000045193365",new BigDecimal(18504));
		totalAmountMap.put("3562000040540555",new BigDecimal(18504));
		totalAmountMap.put("3562000041419096",new BigDecimal(18486));
		totalAmountMap.put("3562000040545016",new BigDecimal(18450));
		totalAmountMap.put("3562000041617265",new BigDecimal(18449));
		totalAmountMap.put("3562000040543396",new BigDecimal(18448));
		totalAmountMap.put("3562000045004785",new BigDecimal(18440));
		totalAmountMap.put("3562000041066525",new BigDecimal(18435));
		totalAmountMap.put("3562000037933674",new BigDecimal(18426));
		totalAmountMap.put("3562000040531975",new BigDecimal(18405));
		totalAmountMap.put("3562000040523086",new BigDecimal(18396));
		totalAmountMap.put("3562000040523356",new BigDecimal(18381));
		totalAmountMap.put("3562000042955614",new BigDecimal(18367));
		totalAmountMap.put("3562000041727584",new BigDecimal(18360));
		totalAmountMap.put("3562000041288126",new BigDecimal(18344));
		totalAmountMap.put("3562000044978174",new BigDecimal(18310));
		totalAmountMap.put("3562000043916316",new BigDecimal(18296));
		totalAmountMap.put("3562000041715905",new BigDecimal(18261));
		totalAmountMap.put("3562000040552584",new BigDecimal(18243));
		totalAmountMap.put("3562000045189614",new BigDecimal(18232));
		totalAmountMap.put("3562000042463875",new BigDecimal(18225));
		totalAmountMap.put("3562000040528275",new BigDecimal(18222));
		totalAmountMap.put("3562000043552935",new BigDecimal(18137));
		totalAmountMap.put("3562000045155025",new BigDecimal(18136));
		totalAmountMap.put("3562000041752735",new BigDecimal(18125));
		totalAmountMap.put("3562000045167673",new BigDecimal(18112));
		totalAmountMap.put("3562000045154395",new BigDecimal(18110));
		totalAmountMap.put("3562000040553165",new BigDecimal(18102));
		totalAmountMap.put("3562000045195824",new BigDecimal(18100));
		totalAmountMap.put("3562000040542347",new BigDecimal(18099));
		totalAmountMap.put("3562000040552835",new BigDecimal(18087));
		totalAmountMap.put("3562000040245247",new BigDecimal(18075));
		totalAmountMap.put("3562000045117925",new BigDecimal(18060));
		totalAmountMap.put("3562000038266624",new BigDecimal(18053));
		totalAmountMap.put("3562000040524017",new BigDecimal(18051));
		totalAmountMap.put("3562000045108754",new BigDecimal(18000));
		totalAmountMap.put("3562000045036136",new BigDecimal(18000));
		totalAmountMap.put("3562000045104356",new BigDecimal(18000));
		totalAmountMap.put("3562000045109845",new BigDecimal(18000));
		totalAmountMap.put("3562000040530307",new BigDecimal(17967));
		totalAmountMap.put("3562000041025466",new BigDecimal(17964));
		totalAmountMap.put("3562000044971794",new BigDecimal(17959));
		totalAmountMap.put("3562000040526564",new BigDecimal(17944));
		totalAmountMap.put("3562000040523965",new BigDecimal(17927));
		totalAmountMap.put("3562000042155815",new BigDecimal(17909));
		totalAmountMap.put("3562000045106446",new BigDecimal(17905));
		totalAmountMap.put("3562000040526954",new BigDecimal(17893));
		totalAmountMap.put("3562000040533007",new BigDecimal(17849));
		totalAmountMap.put("3562000041636645",new BigDecimal(17841));
		totalAmountMap.put("3562000041547275",new BigDecimal(17823));
		totalAmountMap.put("3562000040543337",new BigDecimal(17821));
		totalAmountMap.put("3562000040523186",new BigDecimal(17800));
		totalAmountMap.put("3562000040535784",new BigDecimal(17770));
		totalAmountMap.put("3562000040528095",new BigDecimal(17752));
		totalAmountMap.put("3562000040528016",new BigDecimal(17736));
		totalAmountMap.put("3562000041416407",new BigDecimal(17692));
		totalAmountMap.put("3562000040532575",new BigDecimal(17677));
		totalAmountMap.put("3562000040546725",new BigDecimal(17672));
		totalAmountMap.put("3562000040526195",new BigDecimal(17663));
		totalAmountMap.put("3562000040524736",new BigDecimal(17640));
		totalAmountMap.put("3562000042843546",new BigDecimal(17627));
		totalAmountMap.put("3562000040523555",new BigDecimal(17614));
		totalAmountMap.put("3562000040552794",new BigDecimal(17604));
		totalAmountMap.put("3562000041574036",new BigDecimal(17566));
		totalAmountMap.put("3562000045191554",new BigDecimal(17553));
		totalAmountMap.put("3562000041428356",new BigDecimal(17529));
		totalAmountMap.put("3562000041398705",new BigDecimal(17514));
		totalAmountMap.put("3562000040552945",new BigDecimal(17510));
		totalAmountMap.put("3562000040519446",new BigDecimal(17504));
		totalAmountMap.put("3562000043939165",new BigDecimal(17482));
		totalAmountMap.put("3562000045095653",new BigDecimal(17475));
		totalAmountMap.put("3562000037407406",new BigDecimal(17452));
		totalAmountMap.put("3562000040550745",new BigDecimal(17435));
		totalAmountMap.put("3562000040519935",new BigDecimal(17433));
		totalAmountMap.put("3562000040537754",new BigDecimal(17422));
		totalAmountMap.put("3562000045190226",new BigDecimal(17388));
		totalAmountMap.put("3562000040529085",new BigDecimal(17364));
		totalAmountMap.put("3562000045189074",new BigDecimal(17360));
		totalAmountMap.put("3562000040520765",new BigDecimal(17360));
		totalAmountMap.put("3562000040520506",new BigDecimal(17335));
		totalAmountMap.put("3562000041094846",new BigDecimal(17335));
		totalAmountMap.put("3562000040529485",new BigDecimal(17330));
		totalAmountMap.put("3562000041692845",new BigDecimal(17322));
		totalAmountMap.put("3562000041117307",new BigDecimal(17304));
		totalAmountMap.put("3562000042532706",new BigDecimal(17261));
		totalAmountMap.put("3562000040537835",new BigDecimal(17234));
		totalAmountMap.put("3562000045016584",new BigDecimal(17200));
		totalAmountMap.put("3562000040551475",new BigDecimal(17194));
		totalAmountMap.put("3562000045161635",new BigDecimal(17190));
		totalAmountMap.put("3562000041535984",new BigDecimal(17134));
		totalAmountMap.put("3562000040524166",new BigDecimal(17107));
		totalAmountMap.put("3562000041390606",new BigDecimal(17055));
		totalAmountMap.put("3562000037901554",new BigDecimal(17049));
		totalAmountMap.put("3562000040556094",new BigDecimal(17038));
		totalAmountMap.put("3562000040886873",new BigDecimal(17018));
		totalAmountMap.put("3562000040528605",new BigDecimal(17013));
		totalAmountMap.put("3562000040544256",new BigDecimal(17004));
		totalAmountMap.put("3562000045137864",new BigDecimal(17000));
		totalAmountMap.put("3562000045118664",new BigDecimal(17000));
		totalAmountMap.put("3562000042380217",new BigDecimal(17000));
		totalAmountMap.put("3562000042270276",new BigDecimal(16980));
		totalAmountMap.put("3562000040522936",new BigDecimal(16974));
		totalAmountMap.put("3562000039614346",new BigDecimal(16961));
		totalAmountMap.put("3562000040748106",new BigDecimal(16957));
		totalAmountMap.put("3562000040518385",new BigDecimal(16957));
		totalAmountMap.put("3562000041669494",new BigDecimal(16956));
		totalAmountMap.put("3562000040546326",new BigDecimal(16947));
		totalAmountMap.put("3562000040551085",new BigDecimal(16905));
		totalAmountMap.put("3562000036113895",new BigDecimal(16900));
		totalAmountMap.put("3562000045152485",new BigDecimal(16879));
		totalAmountMap.put("3562000040533516",new BigDecimal(16874));
		totalAmountMap.put("3562000043441177",new BigDecimal(16873));
		totalAmountMap.put("3562000040540327",new BigDecimal(16871));
		totalAmountMap.put("3562000040543086",new BigDecimal(16836));
		totalAmountMap.put("3562000040784845",new BigDecimal(16800));
		totalAmountMap.put("3562000041543775",new BigDecimal(16800));
		totalAmountMap.put("3562000040541975",new BigDecimal(16794));
		totalAmountMap.put("3562000040540765",new BigDecimal(16747));
		totalAmountMap.put("3562000040534816",new BigDecimal(16738));
		totalAmountMap.put("3562000040531806",new BigDecimal(16728));
		totalAmountMap.put("3562000042398055",new BigDecimal(16710));
		totalAmountMap.put("3562000040553825",new BigDecimal(16696));
		totalAmountMap.put("3562000036113675",new BigDecimal(16688));
		totalAmountMap.put("3562000041011208",new BigDecimal(16679));
		totalAmountMap.put("3562000040541785",new BigDecimal(16668));
		totalAmountMap.put("3562000040529265",new BigDecimal(16664));
		totalAmountMap.put("3562000045036705",new BigDecimal(16661));
		totalAmountMap.put("3562000040548165",new BigDecimal(16657));
		totalAmountMap.put("3562000040536715",new BigDecimal(16656));
		totalAmountMap.put("3562000040523895",new BigDecimal(16621));
		totalAmountMap.put("3562000040547426",new BigDecimal(16606));
		totalAmountMap.put("3562000041573226",new BigDecimal(16598));
		totalAmountMap.put("3562000040547155",new BigDecimal(16578));
		totalAmountMap.put("3562000040540595",new BigDecimal(16578));
		totalAmountMap.put("3562000038415336",new BigDecimal(16577));
		totalAmountMap.put("3562000045197125",new BigDecimal(16556));
		totalAmountMap.put("3562000045025574",new BigDecimal(16538));
		totalAmountMap.put("3562000040536085",new BigDecimal(16536));
		totalAmountMap.put("3562000045125275",new BigDecimal(16529));
		totalAmountMap.put("3562000037194085",new BigDecimal(16520));
		totalAmountMap.put("3562000040542137",new BigDecimal(16472));
		totalAmountMap.put("3562000040521946",new BigDecimal(16466));
		totalAmountMap.put("3562000041536864",new BigDecimal(16439));
		totalAmountMap.put("3562000040529725",new BigDecimal(16435));
		totalAmountMap.put("3562000040530916",new BigDecimal(16418));
		totalAmountMap.put("3562000040526375",new BigDecimal(16410));
		totalAmountMap.put("3562000045191395",new BigDecimal(16405));
		totalAmountMap.put("3562000045225554",new BigDecimal(16400));
		totalAmountMap.put("3562000040548525",new BigDecimal(16382));
		totalAmountMap.put("3562000040526226",new BigDecimal(16362));
		totalAmountMap.put("3562000040527046",new BigDecimal(16354));
		totalAmountMap.put("3562000045152155",new BigDecimal(16351));
		totalAmountMap.put("3562000040520526",new BigDecimal(16350));
		totalAmountMap.put("3562000041168155",new BigDecimal(16344));
		totalAmountMap.put("3562000045184346",new BigDecimal(16317));
		totalAmountMap.put("3562000040839485",new BigDecimal(16300));
		totalAmountMap.put("3562000041317417",new BigDecimal(16292));
		totalAmountMap.put("3562000045074945",new BigDecimal(16288));
		totalAmountMap.put("3562000040528006",new BigDecimal(16266));
		totalAmountMap.put("3562000040549355",new BigDecimal(16241));
		totalAmountMap.put("3562000036119255",new BigDecimal(16234));
		totalAmountMap.put("3562000045179504",new BigDecimal(16225));
		totalAmountMap.put("3562000040549146",new BigDecimal(16223));
		totalAmountMap.put("3562000045169583",new BigDecimal(16200));
		totalAmountMap.put("3562000040550495",new BigDecimal(16200));
		totalAmountMap.put("3562000045180355",new BigDecimal(16200));
		totalAmountMap.put("3562000045177634",new BigDecimal(16200));
		totalAmountMap.put("3562000041687005",new BigDecimal(16138));
		totalAmountMap.put("3562000041564564",new BigDecimal(16134));
		totalAmountMap.put("3562000040544795",new BigDecimal(16099));
		totalAmountMap.put("3562000045188415",new BigDecimal(16066));
		totalAmountMap.put("3562000040518645",new BigDecimal(16062));
		totalAmountMap.put("3562000040531846",new BigDecimal(16059));
		totalAmountMap.put("3562000042010257",new BigDecimal(16058));
		totalAmountMap.put("3562000045009764",new BigDecimal(16034));
		totalAmountMap.put("3562000040521307",new BigDecimal(16010));
		totalAmountMap.put("3562000045104127",new BigDecimal(16000));
		totalAmountMap.put("3562000040550994",new BigDecimal(15980));
		totalAmountMap.put("3562000040529155",new BigDecimal(15966));
		totalAmountMap.put("3562000041439726",new BigDecimal(15952));
		totalAmountMap.put("3562000037419715",new BigDecimal(15933));
		totalAmountMap.put("3562000040526055",new BigDecimal(15930));
		totalAmountMap.put("3562000040861026",new BigDecimal(15925));
		totalAmountMap.put("3562000038295405",new BigDecimal(15925));
		totalAmountMap.put("3562000040518594",new BigDecimal(15866));
		totalAmountMap.put("3562000043670754",new BigDecimal(15862));
		totalAmountMap.put("3562000044769035",new BigDecimal(15800));
		totalAmountMap.put("3562000038682544",new BigDecimal(15800));
		totalAmountMap.put("3562000040770006",new BigDecimal(15791));
		totalAmountMap.put("3562000040539236",new BigDecimal(15781));
		totalAmountMap.put("3562000040524546",new BigDecimal(15776));
		totalAmountMap.put("3562000040548465",new BigDecimal(15769));
		totalAmountMap.put("3562000040532765",new BigDecimal(15766));
		totalAmountMap.put("3562000043950594",new BigDecimal(15765));
		totalAmountMap.put("3562000041487155",new BigDecimal(15740));
		totalAmountMap.put("3562000042503366",new BigDecimal(15725));
		totalAmountMap.put("3562000041480616",new BigDecimal(15712));
		totalAmountMap.put("3562000040534516",new BigDecimal(15685));
		totalAmountMap.put("3562000041280056",new BigDecimal(15670));
		totalAmountMap.put("3562000040541456",new BigDecimal(15657));
		totalAmountMap.put("3562000041491127",new BigDecimal(15654));
		totalAmountMap.put("3562000037971445",new BigDecimal(15635));
		totalAmountMap.put("3562000036115645",new BigDecimal(15625));
		totalAmountMap.put("3562000045005126",new BigDecimal(15610));
		totalAmountMap.put("3562000040536055",new BigDecimal(15608));
		totalAmountMap.put("3562000045010186",new BigDecimal(15600));
		totalAmountMap.put("3562000041744417",new BigDecimal(15600));
		totalAmountMap.put("3562000041365155",new BigDecimal(15590));
		totalAmountMap.put("3562000045171255",new BigDecimal(15588));
		totalAmountMap.put("3562000042157046",new BigDecimal(15565));
		totalAmountMap.put("3562000040535535",new BigDecimal(15534));
		totalAmountMap.put("3562000040541855",new BigDecimal(15514));
		totalAmountMap.put("3562000041710327",new BigDecimal(15513));
		totalAmountMap.put("3562000040530496",new BigDecimal(15506));
		totalAmountMap.put("3562000040536165",new BigDecimal(15501));
		totalAmountMap.put("3562000041261655",new BigDecimal(15501));
		totalAmountMap.put("3562000040529615",new BigDecimal(15500));
		totalAmountMap.put("3562000040730327",new BigDecimal(15500));
		totalAmountMap.put("3562000045036226",new BigDecimal(15500));
		totalAmountMap.put("3562000045126594",new BigDecimal(15500));
		totalAmountMap.put("3562000041258385",new BigDecimal(15500));
		totalAmountMap.put("3562000041235755",new BigDecimal(15491));
		totalAmountMap.put("3562000042137755",new BigDecimal(15484));
		totalAmountMap.put("3562000041253007",new BigDecimal(15479));
		totalAmountMap.put("3562000040545605",new BigDecimal(15470));
		totalAmountMap.put("3562000041429056",new BigDecimal(15469));
		totalAmountMap.put("3562000045136016",new BigDecimal(15456));
		totalAmountMap.put("3562000042955235",new BigDecimal(15424));
		totalAmountMap.put("3562000038512794",new BigDecimal(15408));
		totalAmountMap.put("3562000045171416",new BigDecimal(15400));
		totalAmountMap.put("3562000043917874",new BigDecimal(15400));
		totalAmountMap.put("3562000041324686",new BigDecimal(15352));
		totalAmountMap.put("3562000040801955",new BigDecimal(15322));
		totalAmountMap.put("3562000040544516",new BigDecimal(15307));
		totalAmountMap.put("3562000040520466",new BigDecimal(15300));
		totalAmountMap.put("3562000040534795",new BigDecimal(15292));
		totalAmountMap.put("3562000040527495",new BigDecimal(15271));
		totalAmountMap.put("3562000040528854",new BigDecimal(15259));
		totalAmountMap.put("3562000040550715",new BigDecimal(15244));
		totalAmountMap.put("3562000040543037",new BigDecimal(15234));
		totalAmountMap.put("3562000040551584",new BigDecimal(15217));
		totalAmountMap.put("3562000045190046",new BigDecimal(15215));
		totalAmountMap.put("3562000045082574",new BigDecimal(15213));
		totalAmountMap.put("3562000041556064",new BigDecimal(15200));
		totalAmountMap.put("3562000040550446",new BigDecimal(15198));
		totalAmountMap.put("3562000040553945",new BigDecimal(15190));
		totalAmountMap.put("3562000042136476",new BigDecimal(15170));
		totalAmountMap.put("3562000040548375",new BigDecimal(15155));
		totalAmountMap.put("3562000040539355",new BigDecimal(15152));
		totalAmountMap.put("3562000042978154",new BigDecimal(15120));
		totalAmountMap.put("3562000041536075",new BigDecimal(15096));
		totalAmountMap.put("3562000041604256",new BigDecimal(15083));
		totalAmountMap.put("3562000041401008",new BigDecimal(15080));
		totalAmountMap.put("3562000041564285",new BigDecimal(15074));
		totalAmountMap.put("3562000041414827",new BigDecimal(15074));
		totalAmountMap.put("3562000041426636",new BigDecimal(15073));
		totalAmountMap.put("3562000041416755",new BigDecimal(15073));
		totalAmountMap.put("3562000042737455",new BigDecimal(15048));
		totalAmountMap.put("3562000040529446",new BigDecimal(15047));
		totalAmountMap.put("3562000040539265",new BigDecimal(15044));
		totalAmountMap.put("3562000040529715",new BigDecimal(15043));
		totalAmountMap.put("3562000041607854",new BigDecimal(15023));
		totalAmountMap.put("3562000040526935",new BigDecimal(15020));
		totalAmountMap.put("3562000040521995",new BigDecimal(14987));
		totalAmountMap.put("3562000045053265",new BigDecimal(14979));
		totalAmountMap.put("3562000040517794",new BigDecimal(14974));
		totalAmountMap.put("3562000040535216",new BigDecimal(14937));
		totalAmountMap.put("3562000040530985",new BigDecimal(14937));
		totalAmountMap.put("3562000040547355",new BigDecimal(14927));
		totalAmountMap.put("3562000039370505",new BigDecimal(14919));
		totalAmountMap.put("3562000040520296",new BigDecimal(14917));
		totalAmountMap.put("3562000040545255",new BigDecimal(14916));
		totalAmountMap.put("3562000040518725",new BigDecimal(14913));
		totalAmountMap.put("3562000041139695",new BigDecimal(14895));
		totalAmountMap.put("3562000045124726",new BigDecimal(14880));
		totalAmountMap.put("3562000038363406",new BigDecimal(14818));
		totalAmountMap.put("3562000040546126",new BigDecimal(14812));
		totalAmountMap.put("3562000042530117",new BigDecimal(14812));
		totalAmountMap.put("3562000045156563",new BigDecimal(14800));
		totalAmountMap.put("3562000043936346",new BigDecimal(14800));
		totalAmountMap.put("3562000040533675",new BigDecimal(14800));
		totalAmountMap.put("3562000040526455",new BigDecimal(14793));
		totalAmountMap.put("3562000040524356",new BigDecimal(14774));
		totalAmountMap.put("3562000045138255",new BigDecimal(14770));
		totalAmountMap.put("3562000041501846",new BigDecimal(14765));
		totalAmountMap.put("3562000041516316",new BigDecimal(14757));
		totalAmountMap.put("3562000037975752",new BigDecimal(14748));
		totalAmountMap.put("3562000040522865",new BigDecimal(14747));
		totalAmountMap.put("3562000038860425",new BigDecimal(14736));
		totalAmountMap.put("3562000045175673",new BigDecimal(14726));
		totalAmountMap.put("3562000041531755",new BigDecimal(14714));
		totalAmountMap.put("3562000038818484",new BigDecimal(14707));
		totalAmountMap.put("3562000040554195",new BigDecimal(14705));
		totalAmountMap.put("3562000036282436",new BigDecimal(14701));
		totalAmountMap.put("3562000041576394",new BigDecimal(14697));
		totalAmountMap.put("3562000040545564",new BigDecimal(14682));
		totalAmountMap.put("3562000040520237",new BigDecimal(14661));
		totalAmountMap.put("3562000041085065",new BigDecimal(14654));
		totalAmountMap.put("3562000040552206",new BigDecimal(14639));
		totalAmountMap.put("3562000042948515",new BigDecimal(14623));
		totalAmountMap.put("3562000040554705",new BigDecimal(14606));
		totalAmountMap.put("3562000041611037",new BigDecimal(14601));
		totalAmountMap.put("3562000040535945",new BigDecimal(14601));
		totalAmountMap.put("3562000045156544",new BigDecimal(14600));
		totalAmountMap.put("3562000040522826",new BigDecimal(14600));
		totalAmountMap.put("3562000040540186",new BigDecimal(14581));
		totalAmountMap.put("3562000041513785",new BigDecimal(14576));
		totalAmountMap.put("3562000040740675",new BigDecimal(14568));
		totalAmountMap.put("3562000043671485",new BigDecimal(14563));
		totalAmountMap.put("3562000040546994",new BigDecimal(14562));
		totalAmountMap.put("3562000041665573",new BigDecimal(14557));
		totalAmountMap.put("3562000042456945",new BigDecimal(14556));
		totalAmountMap.put("3562000040530166",new BigDecimal(14534));
		totalAmountMap.put("3562000040525075",new BigDecimal(14514));
		totalAmountMap.put("3562000040533476",new BigDecimal(14504));
		totalAmountMap.put("3562000042613946",new BigDecimal(14504));
		totalAmountMap.put("3562000045043266",new BigDecimal(14500));
		totalAmountMap.put("3562000040524327",new BigDecimal(14496));
		totalAmountMap.put("3562000040533955",new BigDecimal(14484));
		totalAmountMap.put("3562000040531626",new BigDecimal(14401));
		totalAmountMap.put("3562000045173116",new BigDecimal(14400));
		totalAmountMap.put("3562000043672485",new BigDecimal(14391));
		totalAmountMap.put("3562000040535654",new BigDecimal(14383));
		totalAmountMap.put("3562000040538095",new BigDecimal(14376));
		totalAmountMap.put("3562000037043975",new BigDecimal(14375));
		totalAmountMap.put("3562000045150175",new BigDecimal(14357));
		totalAmountMap.put("3562000041222367",new BigDecimal(14355));
		totalAmountMap.put("3562000044244927",new BigDecimal(14342));
		totalAmountMap.put("3562000039659703",new BigDecimal(14340));
		totalAmountMap.put("3562000040876274",new BigDecimal(14337));
		totalAmountMap.put("3562000042802685",new BigDecimal(14311));
		totalAmountMap.put("3562000041101428",new BigDecimal(14300));
		totalAmountMap.put("3562000041571416",new BigDecimal(14299));
		totalAmountMap.put("3562000040532037",new BigDecimal(14289));
		totalAmountMap.put("3562000041446926",new BigDecimal(14276));
		totalAmountMap.put("3562000045188534",new BigDecimal(14260));
		totalAmountMap.put("3562000040965893",new BigDecimal(14237));
		totalAmountMap.put("3562000040529974",new BigDecimal(14231));
		totalAmountMap.put("3562000041276905",new BigDecimal(14212));
		totalAmountMap.put("3562000040528155",new BigDecimal(14200));
		totalAmountMap.put("3562000040527075",new BigDecimal(14182));
		totalAmountMap.put("3562000040536964",new BigDecimal(14179));
		totalAmountMap.put("3562000040532237",new BigDecimal(14174));
		totalAmountMap.put("3562000040525925",new BigDecimal(14136));
		totalAmountMap.put("3562000040546584",new BigDecimal(14133));
		totalAmountMap.put("3562000041101996",new BigDecimal(14122));
		totalAmountMap.put("3562000045188374",new BigDecimal(14112));
		totalAmountMap.put("3562000045169294",new BigDecimal(14100));
		totalAmountMap.put("3562000042870915",new BigDecimal(14093));
		totalAmountMap.put("3562000042956384",new BigDecimal(14082));
		totalAmountMap.put("3562000040521526",new BigDecimal(14074));
		totalAmountMap.put("3562000041257385",new BigDecimal(14052));
		totalAmountMap.put("3562000045138705",new BigDecimal(14034));
		totalAmountMap.put("3562000041654694",new BigDecimal(14017));
		totalAmountMap.put("3562000041540337",new BigDecimal(14015));
		totalAmountMap.put("3562000041585514",new BigDecimal(14013));
		totalAmountMap.put("3562000045181694",new BigDecimal(14006));
		totalAmountMap.put("3562000037655782",new BigDecimal(14002));
		totalAmountMap.put("3562000040538664",new BigDecimal(14001));
		totalAmountMap.put("3562000045120056",new BigDecimal(14000));
		totalAmountMap.put("3562000045035206",new BigDecimal(14000));
		totalAmountMap.put("3562000045104137",new BigDecimal(14000));
		totalAmountMap.put("3562000040739964",new BigDecimal(13998));
		totalAmountMap.put("3562000040547146",new BigDecimal(13990));
		totalAmountMap.put("3562000045150475",new BigDecimal(13982));
		totalAmountMap.put("3562000041694535",new BigDecimal(13977));
		totalAmountMap.put("3562000040535854",new BigDecimal(13956));
		totalAmountMap.put("3562000040552285",new BigDecimal(13951));
		totalAmountMap.put("3562000040534386",new BigDecimal(13936));
		totalAmountMap.put("3562000037669513",new BigDecimal(13914));
		totalAmountMap.put("3562000039559762",new BigDecimal(13910));
		totalAmountMap.put("3562000040546065",new BigDecimal(13900));
		totalAmountMap.put("3562000045169604",new BigDecimal(13900));
		totalAmountMap.put("3562000040531655",new BigDecimal(13874));
		totalAmountMap.put("3562000040538265",new BigDecimal(13843));
		totalAmountMap.put("3562000042067106",new BigDecimal(13796));
		totalAmountMap.put("3562000044333467",new BigDecimal(13794));
		totalAmountMap.put("3562000041010856",new BigDecimal(13787));
		totalAmountMap.put("3562000044178515",new BigDecimal(13785));
		totalAmountMap.put("3562000041568064",new BigDecimal(13761));
		totalAmountMap.put("3562000036115316",new BigDecimal(13755));
		totalAmountMap.put("3562000041442287",new BigDecimal(13743));
		totalAmountMap.put("3562000041704366",new BigDecimal(13728));
		totalAmountMap.put("3562000040674854",new BigDecimal(13700));
		totalAmountMap.put("3562000041679553",new BigDecimal(13700));
		totalAmountMap.put("3562000040167745",new BigDecimal(13694));
		totalAmountMap.put("3562000040539206",new BigDecimal(13640));
		totalAmountMap.put("3562000043515715",new BigDecimal(13610));
		totalAmountMap.put("3562000040536745",new BigDecimal(13600));
		totalAmountMap.put("3562000045172925",new BigDecimal(13597));
		totalAmountMap.put("3562000041715825",new BigDecimal(13571));
		totalAmountMap.put("3562000043893016",new BigDecimal(13567));
		totalAmountMap.put("3562000040547554",new BigDecimal(13556));
		totalAmountMap.put("3562000040529754",new BigDecimal(13550));
		totalAmountMap.put("3562000040550255",new BigDecimal(13549));
		totalAmountMap.put("3562000042136456",new BigDecimal(13547));
		totalAmountMap.put("3562000040555573",new BigDecimal(13546));
		totalAmountMap.put("3562000040554605",new BigDecimal(13538));
		totalAmountMap.put("3562000041729495",new BigDecimal(13534));
		totalAmountMap.put("3562000040553246",new BigDecimal(13533));
		totalAmountMap.put("3562000040903247",new BigDecimal(13531));
		totalAmountMap.put("3562000037228285",new BigDecimal(13512));
		totalAmountMap.put("3562000040544985",new BigDecimal(13509));
		totalAmountMap.put("3562000040540337",new BigDecimal(13508));
		totalAmountMap.put("3562000042137595",new BigDecimal(13505));
		totalAmountMap.put("3562000040550905",new BigDecimal(13500));
		totalAmountMap.put("3562000043640826",new BigDecimal(13500));
		totalAmountMap.put("3562000042341177",new BigDecimal(13500));
		totalAmountMap.put("3562000041429217",new BigDecimal(13500));
		totalAmountMap.put("3562000040551545",new BigDecimal(13500));
		totalAmountMap.put("3562000041735674",new BigDecimal(13486));
		totalAmountMap.put("3562000040530516",new BigDecimal(13481));
		totalAmountMap.put("3562000040549416",new BigDecimal(13481));
		totalAmountMap.put("3562000040543437",new BigDecimal(13462));
		totalAmountMap.put("3562000041614086",new BigDecimal(13447));
		totalAmountMap.put("3562000040528694",new BigDecimal(13439));
		totalAmountMap.put("3562000040546545",new BigDecimal(13435));
		totalAmountMap.put("3562000040549385",new BigDecimal(13433));
		totalAmountMap.put("3562000041684645",new BigDecimal(13430));
		totalAmountMap.put("3562000040523826",new BigDecimal(13417));
		totalAmountMap.put("3562000042151156",new BigDecimal(13381));
		totalAmountMap.put("3562000041072965",new BigDecimal(13362));
		totalAmountMap.put("3562000040551715",new BigDecimal(13358));
		totalAmountMap.put("3562000039733375",new BigDecimal(13346));
		totalAmountMap.put("3562000041758345",new BigDecimal(13345));
		totalAmountMap.put("3562000041533186",new BigDecimal(13327));
		totalAmountMap.put("3562000041648905",new BigDecimal(13310));
		totalAmountMap.put("3562000043800447",new BigDecimal(13302));
		totalAmountMap.put("3562000040549126",new BigDecimal(13300));
		totalAmountMap.put("3562000040533836",new BigDecimal(13293));
		totalAmountMap.put("3562000041243986",new BigDecimal(13286));
		totalAmountMap.put("3562000045192455",new BigDecimal(13272));
		totalAmountMap.put("3562000043454407",new BigDecimal(13261));
		totalAmountMap.put("3562000040548994",new BigDecimal(13243));
		totalAmountMap.put("3562000040841695",new BigDecimal(13243));
		totalAmountMap.put("3562000040530546",new BigDecimal(13243));
		totalAmountMap.put("3562000040521936",new BigDecimal(13235));
		totalAmountMap.put("3562000040551406",new BigDecimal(13222));
		totalAmountMap.put("3562000040534017",new BigDecimal(13219));
		totalAmountMap.put("3562000040538475",new BigDecimal(13213));
		totalAmountMap.put("3562000040549036",new BigDecimal(13208));
		totalAmountMap.put("3562000040526984",new BigDecimal(13207));
		totalAmountMap.put("3562000037569444",new BigDecimal(13200));
		totalAmountMap.put("3562000040518095",new BigDecimal(13200));
		totalAmountMap.put("3562000045005815",new BigDecimal(13200));
		totalAmountMap.put("3562000040526915",new BigDecimal(13196));
		totalAmountMap.put("3562000040538206",new BigDecimal(13186));
		totalAmountMap.put("3562000040549805",new BigDecimal(13175));
		totalAmountMap.put("3562000041312527",new BigDecimal(13170));
		totalAmountMap.put("3562000043982535",new BigDecimal(13166));
		totalAmountMap.put("3562000040702975",new BigDecimal(13150));
		totalAmountMap.put("3562000041630317",new BigDecimal(13139));
		totalAmountMap.put("3562000041139276",new BigDecimal(13129));
		totalAmountMap.put("3562000045213407",new BigDecimal(13129));
		totalAmountMap.put("3562000037959453",new BigDecimal(13111));
		totalAmountMap.put("3562000040987934",new BigDecimal(13100));
		totalAmountMap.put("3562000045179484",new BigDecimal(13094));
		totalAmountMap.put("3562000040526255",new BigDecimal(13092));
		totalAmountMap.put("3562000040540127",new BigDecimal(13087));
		totalAmountMap.put("3562000040526535",new BigDecimal(13086));
		totalAmountMap.put("3562000040549574",new BigDecimal(13086));
		totalAmountMap.put("3562000037669913",new BigDecimal(13081));
		totalAmountMap.put("3562000043417555",new BigDecimal(13081));
		totalAmountMap.put("3562000038156494",new BigDecimal(13071));
		totalAmountMap.put("3562000040531695",new BigDecimal(13061));
		totalAmountMap.put("3562000038116854",new BigDecimal(13045));
		totalAmountMap.put("3562000041306396",new BigDecimal(13044));
		totalAmountMap.put("3562000040538236",new BigDecimal(13035));
		totalAmountMap.put("3562000045121995",new BigDecimal(13027));
		totalAmountMap.put("3562000040547116",new BigDecimal(13022));
		totalAmountMap.put("3562000041593395",new BigDecimal(13021));
		totalAmountMap.put("3562000040536216",new BigDecimal(13017));
		totalAmountMap.put("3562000040525436",new BigDecimal(13008));
		totalAmountMap.put("3562000045098374",new BigDecimal(13000));
		totalAmountMap.put("3562000045188853",new BigDecimal(13000));
		totalAmountMap.put("3562000040520806",new BigDecimal(12996));
		totalAmountMap.put("3562000040549545",new BigDecimal(12986));
		totalAmountMap.put("3562000040549085",new BigDecimal(12979));
		totalAmountMap.put("3562000040537485",new BigDecimal(12967));
		totalAmountMap.put("3562000036115075",new BigDecimal(12958));
		totalAmountMap.put("3562000041538915",new BigDecimal(12957));
		totalAmountMap.put("3562000040525375",new BigDecimal(12956));
		totalAmountMap.put("3562000040530217",new BigDecimal(12943));
		totalAmountMap.put("3562000037659803",new BigDecimal(12930));
		totalAmountMap.put("3562000041669534",new BigDecimal(12922));
		totalAmountMap.put("3562000040551735",new BigDecimal(12917));
		totalAmountMap.put("3562000040518116",new BigDecimal(12914));
		totalAmountMap.put("3562000040538106",new BigDecimal(12912));
		totalAmountMap.put("3562000041432187",new BigDecimal(12912));
		totalAmountMap.put("3562000040550075",new BigDecimal(12902));
		totalAmountMap.put("3562000037938534",new BigDecimal(12900));
		totalAmountMap.put("3562000045120147",new BigDecimal(12900));
		totalAmountMap.put("3562000041574784",new BigDecimal(12896));
		totalAmountMap.put("3562000041249885",new BigDecimal(12887));
		totalAmountMap.put("3562000036878253",new BigDecimal(12880));
		totalAmountMap.put("3562000040888494",new BigDecimal(12878));
		totalAmountMap.put("3562000038240955",new BigDecimal(12872));
		totalAmountMap.put("3562000040536735",new BigDecimal(12870));
		totalAmountMap.put("3562000045153845",new BigDecimal(12859));
		totalAmountMap.put("3562000040543695",new BigDecimal(12853));
		totalAmountMap.put("3562000044288664",new BigDecimal(12848));
		totalAmountMap.put("3562000040523337",new BigDecimal(12834));
		totalAmountMap.put("3562000040537935",new BigDecimal(12825));
		totalAmountMap.put("3562000045183784",new BigDecimal(12806));
		totalAmountMap.put("3562000036862863",new BigDecimal(12800));
		totalAmountMap.put("3562000045188593",new BigDecimal(12800));
		totalAmountMap.put("3562000040543885",new BigDecimal(12786));
		totalAmountMap.put("3562000040540386",new BigDecimal(12778));
		totalAmountMap.put("3562000045014117",new BigDecimal(12726));
		totalAmountMap.put("3562000041222408",new BigDecimal(12708));
		totalAmountMap.put("3562000040719416",new BigDecimal(12698));
		totalAmountMap.put("3562000045152475",new BigDecimal(12690));
		totalAmountMap.put("3562000040532076",new BigDecimal(12678));
		totalAmountMap.put("3562000043404776",new BigDecimal(12673));
		totalAmountMap.put("3562000040530585",new BigDecimal(12644));
		totalAmountMap.put("3562000041030428",new BigDecimal(12639));
		totalAmountMap.put("3562000042147665",new BigDecimal(12637));
		totalAmountMap.put("3562000040520985",new BigDecimal(12617));
		totalAmountMap.put("3562000040537515",new BigDecimal(12610));
		totalAmountMap.put("3562000040527905",new BigDecimal(12600));
		totalAmountMap.put("3562000043350526",new BigDecimal(12593));
		totalAmountMap.put("3562000045195354",new BigDecimal(12576));
		totalAmountMap.put("3562000041216276",new BigDecimal(12570));
		totalAmountMap.put("3562000040519884",new BigDecimal(12564));
		totalAmountMap.put("3562000041389326",new BigDecimal(12549));
		totalAmountMap.put("3562000040545874",new BigDecimal(12537));
		totalAmountMap.put("3562000036114846",new BigDecimal(12534));
		totalAmountMap.put("3562000040532266",new BigDecimal(12513));
		totalAmountMap.put("3562000038088094",new BigDecimal(12500));
		totalAmountMap.put("3562000041381816",new BigDecimal(12500));
		totalAmountMap.put("3562000040809925",new BigDecimal(12494));
		totalAmountMap.put("3562000042952615",new BigDecimal(12487));
		totalAmountMap.put("3562000041228366",new BigDecimal(12477));
		totalAmountMap.put("3562000041601256",new BigDecimal(12473));
		totalAmountMap.put("3562000041651845",new BigDecimal(12473));
		totalAmountMap.put("3562000045132946",new BigDecimal(12459));
		totalAmountMap.put("3562000040532806",new BigDecimal(12453));
		totalAmountMap.put("3562000040546026",new BigDecimal(12446));
		totalAmountMap.put("3562000040542386",new BigDecimal(12435));
		totalAmountMap.put("3562000040531037",new BigDecimal(12430));
		totalAmountMap.put("3562000037663953",new BigDecimal(12420));
		totalAmountMap.put("3562000045190684",new BigDecimal(12400));
		totalAmountMap.put("3562000045094475",new BigDecimal(12400));
		totalAmountMap.put("3562000040549974",new BigDecimal(12355));
		totalAmountMap.put("3562000044059046",new BigDecimal(12339));
		totalAmountMap.put("3562000040534975",new BigDecimal(12322));
		totalAmountMap.put("3562000037423546",new BigDecimal(12300));
		totalAmountMap.put("3562000043404218",new BigDecimal(12292));
		totalAmountMap.put("3562000045155074",new BigDecimal(12292));
		totalAmountMap.put("3562000040555415",new BigDecimal(12290));
		totalAmountMap.put("3562000040521466",new BigDecimal(12288));
		totalAmountMap.put("3562000045198025",new BigDecimal(12286));
		totalAmountMap.put("3562000045176893",new BigDecimal(12275));
		totalAmountMap.put("3562000045152055",new BigDecimal(12264));
		totalAmountMap.put("3562000041020228",new BigDecimal(12224));
		totalAmountMap.put("3562000044977025",new BigDecimal(12223));
		totalAmountMap.put("3562000037674683",new BigDecimal(12213));
		totalAmountMap.put("3562000040526705",new BigDecimal(12209));
		totalAmountMap.put("3562000040547915",new BigDecimal(12200));
		totalAmountMap.put("3562000040544595",new BigDecimal(12200));
		totalAmountMap.put("3562000037678163",new BigDecimal(12200));
		totalAmountMap.put("3562000040522555",new BigDecimal(12187));
		totalAmountMap.put("3562000045173475",new BigDecimal(12183));
		totalAmountMap.put("3562000042703496",new BigDecimal(12178));
		
		totalAmountMap.putAll(Work.map);
		totalAmountMap.putAll(Spring.spring);
		
		/*totalAmountMap.put("3293000000510367",new BigDecimal(1661375));
		totalAmountMap.put("3293000000062556",new BigDecimal(1579));
		totalAmountMap.put("3293000002908186",new BigDecimal(707));
		totalAmountMap.put("3293000000067247",new BigDecimal(559));
		totalAmountMap.put("3293000000037057",new BigDecimal(519));
		totalAmountMap.put("3293000000054807",new BigDecimal(271));
		totalAmountMap.put("3293000000073348",new BigDecimal(82290698));
		totalAmountMap.put("3293000000129737",new BigDecimal(148000000));
		totalAmountMap.put("3293000000058906",new BigDecimal(71869365));
		totalAmountMap.put("3293000002427547",new BigDecimal(51410140));
		totalAmountMap.put("3293000000067386",new BigDecimal(40000000));
		totalAmountMap.put("3293000000087027",new BigDecimal(40000000));
		totalAmountMap.put("3293000001232577",new BigDecimal(37000000));
		totalAmountMap.put("3293000002423139",new BigDecimal(36000000));
		totalAmountMap.put("3293000001154586",new BigDecimal(35000000));
		totalAmountMap.put("3293000000111068",new BigDecimal(33670191));
		totalAmountMap.put("3293000000102488",new BigDecimal(29906600));
		totalAmountMap.put("3293000000093576",new BigDecimal(27000000));
		totalAmountMap.put("3293000000048408",new BigDecimal(26567808));
		totalAmountMap.put("3293000001747646",new BigDecimal(26000000));
		totalAmountMap.put("3293000000088516",new BigDecimal(23461169));
		totalAmountMap.put("3293000002919207",new BigDecimal(22000000));
		totalAmountMap.put("3293000000022198",new BigDecimal(21000000));
		totalAmountMap.put("3293000000425547",new BigDecimal(20408387));
		totalAmountMap.put("3293000000047996",new BigDecimal(17709166));
		totalAmountMap.put("3293000003498286",new BigDecimal(17529845));
		totalAmountMap.put("3293000003221498",new BigDecimal(17000000));
		totalAmountMap.put("3293000003083927",new BigDecimal(15000000));
		totalAmountMap.put("3293000003369616",new BigDecimal(13000000));
		totalAmountMap.put("3293000002931238",new BigDecimal(12000000));
		totalAmountMap.put("3293000000187826",new BigDecimal(11297211));
		totalAmountMap.put("3293000000101787",new BigDecimal(11163313));
		totalAmountMap.put("3293000003260438",new BigDecimal(11000000));
		totalAmountMap.put("3293000002018228",new BigDecimal(10000000));
		totalAmountMap.put("3293000000113239",new BigDecimal(9612500));
		totalAmountMap.put("3293000001119397",new BigDecimal(9300000));
		totalAmountMap.put("3293000000742747",new BigDecimal(9000000));
		totalAmountMap.put("3293000000021918",new BigDecimal(9000000));
		totalAmountMap.put("3293000002445277",new BigDecimal(8560000));
		totalAmountMap.put("3293000002875316",new BigDecimal(8067726));
		totalAmountMap.put("3293000003057227",new BigDecimal(8000000));
		totalAmountMap.put("3293000003112887",new BigDecimal(8000000));
		totalAmountMap.put("3293000000080786",new BigDecimal(6600000));
		totalAmountMap.put("3293000000041409",new BigDecimal(6500000));
		totalAmountMap.put("3293000002942507",new BigDecimal(6477398));
		totalAmountMap.put("3293000003006497",new BigDecimal(6400000));
		totalAmountMap.put("3293000001402628",new BigDecimal(6300000));
		totalAmountMap.put("3293000000724676",new BigDecimal(6000000));
		totalAmountMap.put("3293000000102948",new BigDecimal(5640040));
		totalAmountMap.put("3293000002534297",new BigDecimal(5388900));
		totalAmountMap.put("3293000003414009",new BigDecimal(5300000));
		totalAmountMap.put("3293000000253457",new BigDecimal(5100000));
		totalAmountMap.put("3293000002781675",new BigDecimal(4958550));
		totalAmountMap.put("3293000003513418",new BigDecimal(4900000));
		totalAmountMap.put("3293000000102848",new BigDecimal(4700594));
		totalAmountMap.put("3293000002555994",new BigDecimal(4400000));
		totalAmountMap.put("3293000003454448",new BigDecimal(4200000));
		totalAmountMap.put("3293000002110887",new BigDecimal(4032567));
		totalAmountMap.put("3293000002637926",new BigDecimal(4000000));
		totalAmountMap.put("3293000003197565",new BigDecimal(4000000));
		totalAmountMap.put("3293000003528726",new BigDecimal(3933925));
		totalAmountMap.put("3293000002179726",new BigDecimal(3500000));
		totalAmountMap.put("3293000003163876",new BigDecimal(3400000));
		totalAmountMap.put("3293000000868106",new BigDecimal(3360504));
		totalAmountMap.put("3293000003352856",new BigDecimal(3360000));
		totalAmountMap.put("3293000000496985",new BigDecimal(3300000));
		totalAmountMap.put("3293000002291477",new BigDecimal(3216280));
		totalAmountMap.put("3293000000064167",new BigDecimal(3097499));
		totalAmountMap.put("3293000000021648",new BigDecimal(3000000));
		totalAmountMap.put("3293000000884237",new BigDecimal(3000000));
		totalAmountMap.put("3293000003429377",new BigDecimal(3000000));
		totalAmountMap.put("3293000003453976",new BigDecimal(3000000));
		totalAmountMap.put("3293000000179946",new BigDecimal(2961781));
		totalAmountMap.put("3293000003340298",new BigDecimal(2900000));
		totalAmountMap.put("3293000003471837",new BigDecimal(2889998));
		totalAmountMap.put("3293000000068746",new BigDecimal(2854247));
		totalAmountMap.put("3293000000047717",new BigDecimal(2822979));
		totalAmountMap.put("3293000000022309",new BigDecimal(2800000));
		totalAmountMap.put("3293000000061118",new BigDecimal(2800000));
		totalAmountMap.put("3293000000884895",new BigDecimal(2680000));
		totalAmountMap.put("3293000003499107",new BigDecimal(2319412));
		totalAmountMap.put("3293000002482796",new BigDecimal(2265671));
		totalAmountMap.put("3293000001062218",new BigDecimal(2204600));
		totalAmountMap.put("3293000002458327",new BigDecimal(2200000));
		totalAmountMap.put("3293000003526096",new BigDecimal(2110000));
		totalAmountMap.put("3293000003059166",new BigDecimal(2100000));
		totalAmountMap.put("3293000000084586",new BigDecimal(2026098));
		totalAmountMap.put("3293000000059396",new BigDecimal(2000000));
		totalAmountMap.put("3293000002105428",new BigDecimal(2000000));
		totalAmountMap.put("3293000002979794",new BigDecimal(2000000));
		totalAmountMap.put("3293000003044319",new BigDecimal(2000000));
		totalAmountMap.put("3293000002671606",new BigDecimal(2000000));
		totalAmountMap.put("3293000003423429",new BigDecimal(2000000));
		totalAmountMap.put("3293000001992056",new BigDecimal(1961084));
		totalAmountMap.put("3293000000128077",new BigDecimal(1935963));
		totalAmountMap.put("3293000002181827",new BigDecimal(1800000));
		totalAmountMap.put("3293000000062637",new BigDecimal(1798556));
		totalAmountMap.put("3293000000135497",new BigDecimal(1789500));
		totalAmountMap.put("3293000000044818",new BigDecimal(1736100));
		totalAmountMap.put("3293000000455546",new BigDecimal(1730000));
		totalAmountMap.put("3293000000067636",new BigDecimal(1701533));
		totalAmountMap.put("3293000000097317",new BigDecimal(1700000));
		totalAmountMap.put("3293000002924737",new BigDecimal(1699300));
		totalAmountMap.put("3293000002752536",new BigDecimal(1680191));
		totalAmountMap.put("3293000000358575",new BigDecimal(1500000));
		totalAmountMap.put("3293000002058417",new BigDecimal(1483000));
		totalAmountMap.put("3293000001200548",new BigDecimal(1400000));
		totalAmountMap.put("3293000003125448",new BigDecimal(1400000));
		totalAmountMap.put("3293000000069675",new BigDecimal(1385100));
		totalAmountMap.put("3293000000314029",new BigDecimal(1373316));
		totalAmountMap.put("3293000002980327",new BigDecimal(1320152));
		totalAmountMap.put("3293000001029038",new BigDecimal(1320000));
		totalAmountMap.put("3293000002334219",new BigDecimal(1300000));
		totalAmountMap.put("3293000003433449",new BigDecimal(1294656));
		totalAmountMap.put("3293000000925775",new BigDecimal(1258810));
		totalAmountMap.put("3293000000065985",new BigDecimal(1214862));
		totalAmountMap.put("3293000003026666",new BigDecimal(1200000));
		totalAmountMap.put("3293000003516276",new BigDecimal(1200000));
		totalAmountMap.put("3293000001036267",new BigDecimal(1150000));
		totalAmountMap.put("3293000003283627",new BigDecimal(1139823));
		totalAmountMap.put("3293000003498895",new BigDecimal(1100000));
		totalAmountMap.put("3293000000026747",new BigDecimal(1050000));
		totalAmountMap.put("3293000000067665",new BigDecimal(1000000));
		totalAmountMap.put("3293000000116547",new BigDecimal(1000000));
		totalAmountMap.put("3293000002388536",new BigDecimal(1000000));
		totalAmountMap.put("3293000001267186",new BigDecimal(1000000));
		totalAmountMap.put("3293000003182696",new BigDecimal(1000000));
		totalAmountMap.put("3293000002957845",new BigDecimal(1000000));
		totalAmountMap.put("3293000003197636",new BigDecimal(1000000));
		totalAmountMap.put("3293000003414339",new BigDecimal(1000000));
		totalAmountMap.put("3293000003505086",new BigDecimal(1000000));
		totalAmountMap.put("3293000000022967",new BigDecimal(975284));
		totalAmountMap.put("3293000001000498",new BigDecimal(967888));
		totalAmountMap.put("3293000000042409",new BigDecimal(945800));
		totalAmountMap.put("3293000003358726",new BigDecimal(942180));
		totalAmountMap.put("3293000002679754",new BigDecimal(850230));
		totalAmountMap.put("3293000000026627",new BigDecimal(845166));
		totalAmountMap.put("3293000000082377",new BigDecimal(823200));
		totalAmountMap.put("3293000000320149",new BigDecimal(800000));
		totalAmountMap.put("3293000002484686",new BigDecimal(793352));
		totalAmountMap.put("3293000003414697",new BigDecimal(788262));
		totalAmountMap.put("3293000002019467",new BigDecimal(748715));
		totalAmountMap.put("3293000000086086",new BigDecimal(712635));
		totalAmountMap.put("3293000000062907",new BigDecimal(700000));
		totalAmountMap.put("3293000000069217",new BigDecimal(700000));
		totalAmountMap.put("3293000002933008",new BigDecimal(700000));
		totalAmountMap.put("3293000001143638",new BigDecimal(688490));
		totalAmountMap.put("3293000000361976",new BigDecimal(660000));
		totalAmountMap.put("3293000003026956",new BigDecimal(632600));
		totalAmountMap.put("3293000000028067",new BigDecimal(631951));
		totalAmountMap.put("3293000000537595",new BigDecimal(565326));
		totalAmountMap.put("3293000000024029",new BigDecimal(563835));
		totalAmountMap.put("3293000000278137",new BigDecimal(559530));
		totalAmountMap.put("3293000000890366",new BigDecimal(528650));
		totalAmountMap.put("3293000002095606",new BigDecimal(525180));
		totalAmountMap.put("3293000003462566",new BigDecimal(518375));
		totalAmountMap.put("3293000000096836",new BigDecimal(515877));
		totalAmountMap.put("3293000000134948",new BigDecimal(511860));
		totalAmountMap.put("3293000000041268",new BigDecimal(508540));
		totalAmountMap.put("3293000000047167",new BigDecimal(500396));
		totalAmountMap.put("3293000000080057",new BigDecimal(500000));
		totalAmountMap.put("3293000002360717",new BigDecimal(496000));
		totalAmountMap.put("3293000000028048",new BigDecimal(481982));
		totalAmountMap.put("3293000002888515",new BigDecimal(448360));
		totalAmountMap.put("3293000003460038",new BigDecimal(445088));
		totalAmountMap.put("3293000001685974",new BigDecimal(435646));
		totalAmountMap.put("3293000000042229",new BigDecimal(430000));
		totalAmountMap.put("3293000002043797",new BigDecimal(400000));
		totalAmountMap.put("3293000001924018",new BigDecimal(380460));
		totalAmountMap.put("3293000003153167",new BigDecimal(369795));
		totalAmountMap.put("3293000003016297",new BigDecimal(360000));
		totalAmountMap.put("3293000002904487",new BigDecimal(354690));
		totalAmountMap.put("3293000000096765",new BigDecimal(349021));
		totalAmountMap.put("3293000000128457",new BigDecimal(334079));
		totalAmountMap.put("3293000001462218",new BigDecimal(323500));
		totalAmountMap.put("3293000003535356",new BigDecimal(310200));
		totalAmountMap.put("3293000000094537",new BigDecimal(285778));
		totalAmountMap.put("3293000003260218",new BigDecimal(273006));
		totalAmountMap.put("3293000002423597",new BigDecimal(264875));
		totalAmountMap.put("3293000001446238",new BigDecimal(261202));
		totalAmountMap.put("3293000002467207",new BigDecimal(241754));
		totalAmountMap.put("3293000000086096",new BigDecimal(230000));
		totalAmountMap.put("3293000002030268",new BigDecimal(217210));
		totalAmountMap.put("3293000003553296",new BigDecimal(207622));
		totalAmountMap.put("3293000003551047",new BigDecimal(204000));
		totalAmountMap.put("3293000000022419",new BigDecimal(200000));
		totalAmountMap.put("3293000000065875",new BigDecimal(200000));
		totalAmountMap.put("3293000000107438",new BigDecimal(200000));
		totalAmountMap.put("3293000003260707",new BigDecimal(190000));
		totalAmountMap.put("3293000003456806",new BigDecimal(190000));
		totalAmountMap.put("3293000002928327",new BigDecimal(188640));
		totalAmountMap.put("3293000002068506",new BigDecimal(187247));
		totalAmountMap.put("3293000002520676",new BigDecimal(180000));
		totalAmountMap.put("3293000002009576",new BigDecimal(169729));
		totalAmountMap.put("3293000000088875",new BigDecimal(157211));
		totalAmountMap.put("3293000003504856",new BigDecimal(154800));
		totalAmountMap.put("3293000003415077",new BigDecimal(144720));
		totalAmountMap.put("3293000000833886",new BigDecimal(140000));
		totalAmountMap.put("3293000000996165",new BigDecimal(140000));
		totalAmountMap.put("3293000003494566",new BigDecimal(130000));
		totalAmountMap.put("3293000000046817",new BigDecimal(123900));
		totalAmountMap.put("3293000001774695",new BigDecimal(121581));
		totalAmountMap.put("3293000003511087",new BigDecimal(120244));
		totalAmountMap.put("3293000001081986",new BigDecimal(117806));
		totalAmountMap.put("3293000002834318",new BigDecimal(106960));
		totalAmountMap.put("3293000000116297",new BigDecimal(97450));
		totalAmountMap.put("3293000003491776",new BigDecimal(88988));
		totalAmountMap.put("3293000003545017",new BigDecimal(86000));
		totalAmountMap.put("3293000000043058",new BigDecimal(80000));
		totalAmountMap.put("3293000002186685",new BigDecimal(79900));
		totalAmountMap.put("3293000000156376",new BigDecimal(74070));
		totalAmountMap.put("3293000002801547",new BigDecimal(71120));
		totalAmountMap.put("3293000002673716",new BigDecimal(64900));
		totalAmountMap.put("3293000000867694",new BigDecimal(60000));
		totalAmountMap.put("3293000002086516",new BigDecimal(55880));
		totalAmountMap.put("3293000002759026",new BigDecimal(50750));
		totalAmountMap.put("3293000003206118",new BigDecimal(45960));
		totalAmountMap.put("3293000003276836",new BigDecimal(39950));
		totalAmountMap.put("3293000001294197",new BigDecimal(20110));
		totalAmountMap.put("3293000003547307",new BigDecimal(10000));
		totalAmountMap.put("3293000003402918",new BigDecimal(6300));
		totalAmountMap.put("3293000002187546",new BigDecimal(71));*/
	}
	
	/**
	static {
		totalAmountMap.put("3293000000067706",new BigDecimal(9365710));
		totalAmountMap.put("3293000000921907",new BigDecimal(525836));
		totalAmountMap.put("3293000002845826",new BigDecimal(26226225));
		totalAmountMap.put("3293000000037067",new BigDecimal(3814314));
		totalAmountMap.put("3293000000854107",new BigDecimal(823823));
		totalAmountMap.put("3293000000080586",new BigDecimal(696752));
		totalAmountMap.put("3293000000079196",new BigDecimal(377900));
		totalAmountMap.put("3293000003409637",new BigDecimal(25750));
		totalAmountMap.put("3293000000134718",new BigDecimal(217467));
		totalAmountMap.put("3293000003512497",new BigDecimal(430414));
		totalAmountMap.put("3293000000974855",new BigDecimal(322427));
		totalAmountMap.put("3293000000113188",new BigDecimal(600824));
		totalAmountMap.put("3293000003044557",new BigDecimal(5702493));
		totalAmountMap.put("3293000003000948",new BigDecimal(1059400));
		totalAmountMap.put("3293000003537636",new BigDecimal(1810904));
		totalAmountMap.put("3293000002571995",new BigDecimal(986600));
		totalAmountMap.put("3293000001221567",new BigDecimal(4204444));
		totalAmountMap.put("3293000000061827",new BigDecimal(629360));
		totalAmountMap.put("3293000002463896",new BigDecimal(2603699));
		totalAmountMap.put("3293000002202319",new BigDecimal(468560));
		totalAmountMap.put("3293000003536096",new BigDecimal(11281318));
		totalAmountMap.put("3293000000067247",new BigDecimal(296376160));
		totalAmountMap.put("3293000000127467",new BigDecimal(4209097));
		totalAmountMap.put("3293000002030697",new BigDecimal(652219));
		totalAmountMap.put("3293000003099456",new BigDecimal(3864761));
		totalAmountMap.put("3293000003375646",new BigDecimal(141500));
		totalAmountMap.put("3293000000111409",new BigDecimal(346720));
		totalAmountMap.put("3293000002135786",new BigDecimal(3945220));
		totalAmountMap.put("3293000000146976",new BigDecimal(111735));
		totalAmountMap.put("3293000002110358",new BigDecimal(8516544));
		totalAmountMap.put("3293000002476037",new BigDecimal(420324));
		totalAmountMap.put("3293000003430757",new BigDecimal(35238363));
		totalAmountMap.put("3293000000041339",new BigDecimal(715488));
		totalAmountMap.put("3293000001772327",new BigDecimal(622127));
		totalAmountMap.put("3293000003411258",new BigDecimal(184584));
		totalAmountMap.put("3293000000062067",new BigDecimal(348674));
		totalAmountMap.put("3293000002954695",new BigDecimal(9450));
		totalAmountMap.put("3293000002591916",new BigDecimal(2651824));
		totalAmountMap.put("3293000000111748",new BigDecimal(488068));
		totalAmountMap.put("3293000000073438",new BigDecimal(72092));
		totalAmountMap.put("3293000003211019",new BigDecimal(2261941));
		totalAmountMap.put("3293000003198616",new BigDecimal(2295062));
		totalAmountMap.put("3293000000763317",new BigDecimal(63415));
		totalAmountMap.put("3293000000071177",new BigDecimal(909742));
		totalAmountMap.put("3293000000087536",new BigDecimal(51300));
		totalAmountMap.put("3293000002546056",new BigDecimal(21062844));
		totalAmountMap.put("3293000002005448",new BigDecimal(7383731));
		totalAmountMap.put("3293000003359585",new BigDecimal(289466));
		totalAmountMap.put("3293000003369506",new BigDecimal(76720));
		totalAmountMap.put("3293000000048497",new BigDecimal(4053370));
		totalAmountMap.put("3293000000867574",new BigDecimal(6713327));
		totalAmountMap.put("3293000000067276",new BigDecimal(29390936));
		totalAmountMap.put("3293000002814947",new BigDecimal(348714));
		totalAmountMap.put("3293000003536127",new BigDecimal(7428929));
		totalAmountMap.put("3293000003426627",new BigDecimal(404087));
		totalAmountMap.put("3293000000101288",new BigDecimal(1093468));
		totalAmountMap.put("3293000001462218",new BigDecimal(323500));
		totalAmountMap.put("3293000001261596",new BigDecimal(15652));
		totalAmountMap.put("3293000000033867",new BigDecimal(1941122));
		totalAmountMap.put("3293000000056995",new BigDecimal(745264));
		totalAmountMap.put("3293000001486137",new BigDecimal(1271120));
		totalAmountMap.put("3293000003387417",new BigDecimal(10789476));
		totalAmountMap.put("3293000003264917",new BigDecimal(529148));
		totalAmountMap.put("3293000002786725",new BigDecimal(3101194));
		totalAmountMap.put("3293000002673056",new BigDecimal(504562));
		totalAmountMap.put("3293000003025527",new BigDecimal(1614100));
		totalAmountMap.put("3293000000056955",new BigDecimal(7125470));
		totalAmountMap.put("3293000000059665",new BigDecimal(391385));
		totalAmountMap.put("3293000003465746",new BigDecimal(142714));
		totalAmountMap.put("3293000001445157",new BigDecimal(661333));
		totalAmountMap.put("3293000002718746",new BigDecimal(121390));
		totalAmountMap.put("3293000002275337",new BigDecimal(1931017));
		totalAmountMap.put("3293000000062556",new BigDecimal(1704263));
		totalAmountMap.put("3293000000292377",new BigDecimal(1205612));
		totalAmountMap.put("3293000000091118",new BigDecimal(1304550));
		totalAmountMap.put("3293000002085096",new BigDecimal(704166));
		totalAmountMap.put("3293000000053866",new BigDecimal(3634056));
		totalAmountMap.put("3293000003427676",new BigDecimal(253800));
		totalAmountMap.put("3293000001411309",new BigDecimal(5240776));
		totalAmountMap.put("3293000001845217",new BigDecimal(454744));
		totalAmountMap.put("3293000003537227",new BigDecimal(4023066));
		totalAmountMap.put("3293000000022728",new BigDecimal(45060));
		totalAmountMap.put("3293000001428796",new BigDecimal(3806158));
		totalAmountMap.put("3293000000093238",new BigDecimal(3128998));
		totalAmountMap.put("3293000000613607",new BigDecimal(539851));
		totalAmountMap.put("3293000000882347",new BigDecimal(182301));
		totalAmountMap.put("3293000000042757",new BigDecimal(677142));
		totalAmountMap.put("3293000002330368",new BigDecimal(132862));
		totalAmountMap.put("3293000002218576",new BigDecimal(292528));
		totalAmountMap.put("3293000000098985",new BigDecimal(244104));
		totalAmountMap.put("3293000003397296",new BigDecimal(41897615));
		totalAmountMap.put("3293000002119886",new BigDecimal(12862554));
		totalAmountMap.put("3293000003131268",new BigDecimal(30592));
		totalAmountMap.put("3293000000051467",new BigDecimal(276342));
		totalAmountMap.put("3293000000064408",new BigDecimal(58936));
		totalAmountMap.put("3293000003331358",new BigDecimal(525422));
		totalAmountMap.put("3293000000079906",new BigDecimal(5532389));
		totalAmountMap.put("3293000001025727",new BigDecimal(222710));
		totalAmountMap.put("3293000000614827",new BigDecimal(53522));
		totalAmountMap.put("3293000000079775",new BigDecimal(5640203));
		totalAmountMap.put("3293000002982337",new BigDecimal(4757317));
		totalAmountMap.put("3293000003351876",new BigDecimal(2280023));
		totalAmountMap.put("3293000000463627",new BigDecimal(816834));
		totalAmountMap.put("3293000000314697",new BigDecimal(22950));
		totalAmountMap.put("3293000002366516",new BigDecimal(909263));
		totalAmountMap.put("3293000000112597",new BigDecimal(5196255));
		totalAmountMap.put("3293000000097595",new BigDecimal(192693));
		totalAmountMap.put("3293000000315717",new BigDecimal(4234932));
		totalAmountMap.put("3293000003067086",new BigDecimal(2600));
		totalAmountMap.put("3293000000096955",new BigDecimal(880360));
		totalAmountMap.put("3293000002187546",new BigDecimal(71));
		totalAmountMap.put("3293000002061448",new BigDecimal(10576258));
		totalAmountMap.put("3293000002096695",new BigDecimal(321635));
		totalAmountMap.put("3293000003354157",new BigDecimal(165064));
		totalAmountMap.put("3293000001140838",new BigDecimal(5912218));
		totalAmountMap.put("3293000002743527",new BigDecimal(3038818));
		totalAmountMap.put("3293000000083907",new BigDecimal(105132));
		totalAmountMap.put("3293000000085476",new BigDecimal(1058087));
		totalAmountMap.put("3293000002085716",new BigDecimal(931656));
		totalAmountMap.put("3293000000510228",new BigDecimal(327401));
		totalAmountMap.put("3293000003411419",new BigDecimal(584062));
		totalAmountMap.put("3293000000530038",new BigDecimal(90998));
		totalAmountMap.put("3293000000092937",new BigDecimal(858384));
		totalAmountMap.put("3293000000048297",new BigDecimal(169856));
		totalAmountMap.put("3293000000056496",new BigDecimal(1344196));
		totalAmountMap.put("3293000000084786",new BigDecimal(96083));
		totalAmountMap.put("3293000003411967",new BigDecimal(22416));
		totalAmountMap.put("3293000000375995",new BigDecimal(1359553));
		totalAmountMap.put("3293000003121657",new BigDecimal(210160));
		totalAmountMap.put("3293000002922367",new BigDecimal(1856682));
		totalAmountMap.put("3293000003399995",new BigDecimal(2143088));
		totalAmountMap.put("3293000000084448",new BigDecimal(83108216));
		totalAmountMap.put("3293000003289276",new BigDecimal(27800));
		totalAmountMap.put("3293000002725007",new BigDecimal(576990));
		totalAmountMap.put("3293000003043777",new BigDecimal(2542198));
		totalAmountMap.put("3293000000024198",new BigDecimal(426490));
		totalAmountMap.put("3293000001205128",new BigDecimal(24160));
		totalAmountMap.put("3293000002667984",new BigDecimal(2967806));
		totalAmountMap.put("3293000000116576",new BigDecimal(1083560));
		totalAmountMap.put("3293000000096337",new BigDecimal(608119));
		totalAmountMap.put("3293000002788416",new BigDecimal(1303754));
		totalAmountMap.put("3293000000037018",new BigDecimal(7925516));
		totalAmountMap.put("3293000001534318",new BigDecimal(817780));
		totalAmountMap.put("3293000003527317",new BigDecimal(620366));
		totalAmountMap.put("3293000000859725",new BigDecimal(198922));
		totalAmountMap.put("3293000002884127",new BigDecimal(138092));
		totalAmountMap.put("3293000000745407",new BigDecimal(475058));
		totalAmountMap.put("3293000002488636",new BigDecimal(447376));
		totalAmountMap.put("3293000000067007",new BigDecimal(152280));
		totalAmountMap.put("3293000000053477",new BigDecimal(578264));
		totalAmountMap.put("3293000000048676",new BigDecimal(1143700));
		totalAmountMap.put("3293000002027637",new BigDecimal(2122039));
		totalAmountMap.put("3293000002151956",new BigDecimal(161424));
		totalAmountMap.put("3293000003543248",new BigDecimal(64500));
		totalAmountMap.put("3293000002382876",new BigDecimal(1252170));
		totalAmountMap.put("3293000002029248",new BigDecimal(284040));
		totalAmountMap.put("3293000000064647",new BigDecimal(982390));
		totalAmountMap.put("3293000000096926",new BigDecimal(2493761));
		totalAmountMap.put("3293000000664855",new BigDecimal(1099872));
		totalAmountMap.put("3293000000054807",new BigDecimal(738916));
		totalAmountMap.put("3293000003365307",new BigDecimal(1197197));
		totalAmountMap.put("3293000000358347",new BigDecimal(949350));
		totalAmountMap.put("3293000000033339",new BigDecimal(4572225));
		totalAmountMap.put("3293000000072996",new BigDecimal(6961604));
		totalAmountMap.put("3293000000504866",new BigDecimal(191118));
		totalAmountMap.put("3293000003391357",new BigDecimal(48960));
		totalAmountMap.put("3293000000106248",new BigDecimal(641537));
		totalAmountMap.put("3293000001806546",new BigDecimal(1616534));
		totalAmountMap.put("3293000000065347",new BigDecimal(5136790));
		totalAmountMap.put("3293000000682466",new BigDecimal(764603));
		totalAmountMap.put("3293000003430368",new BigDecimal(186094));
		totalAmountMap.put("3293000000092807",new BigDecimal(27865544));
		totalAmountMap.put("3293000000061228",new BigDecimal(266900));
		totalAmountMap.put("3293000000135297",new BigDecimal(2088526));
		totalAmountMap.put("3293000002905955",new BigDecimal(1026219));
		totalAmountMap.put("3293000002633357",new BigDecimal(752304));
		totalAmountMap.put("3293000002369616",new BigDecimal(86744));
		totalAmountMap.put("3293000003333329",new BigDecimal(176300));
		totalAmountMap.put("3293000000051167",new BigDecimal(11967799));
		totalAmountMap.put("3293000003275427",new BigDecimal(64632));
		totalAmountMap.put("3293000003392786",new BigDecimal(26450));
		totalAmountMap.put("3293000002030129",new BigDecimal(90578016));
		totalAmountMap.put("3293000001628407",new BigDecimal(4251606));
		totalAmountMap.put("3293000002910487",new BigDecimal(4231977));
		totalAmountMap.put("3293000003453647",new BigDecimal(244110));
		totalAmountMap.put("3293000000036837",new BigDecimal(1882662));
		totalAmountMap.put("3293000000054028",new BigDecimal(15153));
		totalAmountMap.put("3293000000753066",new BigDecimal(740936));
		totalAmountMap.put("3293000001074586",new BigDecimal(3096352));
		totalAmountMap.put("3293000000133697",new BigDecimal(1142446));
		totalAmountMap.put("3293000000999784",new BigDecimal(55934761));
		totalAmountMap.put("3293000000053617",new BigDecimal(690434));
		totalAmountMap.put("3293000003304498",new BigDecimal(353344));
		totalAmountMap.put("3293000000058975",new BigDecimal(567854));
		totalAmountMap.put("3293000000277196",new BigDecimal(308488));
		totalAmountMap.put("3293000002265736",new BigDecimal(452694));
		totalAmountMap.put("3293000001344787",new BigDecimal(4119201));
		totalAmountMap.put("3293000000072448",new BigDecimal(45235));
		totalAmountMap.put("3293000002533847",new BigDecimal(217160));
		totalAmountMap.put("3293000003371348",new BigDecimal(125439));
		totalAmountMap.put("3293000000022239",new BigDecimal(24929642));
		totalAmountMap.put("3293000003057655",new BigDecimal(182038823));
		totalAmountMap.put("3293000000135927",new BigDecimal(17934849));
		totalAmountMap.put("3293000003112478",new BigDecimal(16066188));
		totalAmountMap.put("3293000003455027",new BigDecimal(212610));
		totalAmountMap.put("3293000003114528",new BigDecimal(2504444));
		totalAmountMap.put("3293000003505107",new BigDecimal(327075));
		totalAmountMap.put("3293000000055256",new BigDecimal(105700));
		totalAmountMap.put("3293000002102508",new BigDecimal(164500));
		totalAmountMap.put("3293000000072267",new BigDecimal(8295647));
		totalAmountMap.put("3293000002942586",new BigDecimal(303326));
		totalAmountMap.put("3293000000023109",new BigDecimal(268606));
		totalAmountMap.put("3293000001949865",new BigDecimal(3889670));
		totalAmountMap.put("3293000000037527",new BigDecimal(349781));
		totalAmountMap.put("3293000000023908",new BigDecimal(149690));
		totalAmountMap.put("3293000002476785",new BigDecimal(182576));
		totalAmountMap.put("3293000002718107",new BigDecimal(522795));
		totalAmountMap.put("3293000003237287",new BigDecimal(25100));
		totalAmountMap.put("3293000000128876",new BigDecimal(7547));
		totalAmountMap.put("3293000003451308",new BigDecimal(1234788));
		totalAmountMap.put("3293000002542847",new BigDecimal(616075));
		totalAmountMap.put("3293000000133158",new BigDecimal(376574));
		totalAmountMap.put("3293000003539276",new BigDecimal(943811));
		totalAmountMap.put("3293000000067447",new BigDecimal(1619798));
		totalAmountMap.put("3293000003125197",new BigDecimal(95810));
		totalAmountMap.put("3293000001062656",new BigDecimal(10951353));
		totalAmountMap.put("3293000003507626",new BigDecimal(12114217));
		totalAmountMap.put("3293000000046448",new BigDecimal(1827184));
		totalAmountMap.put("3293000001805237",new BigDecimal(22612438));
		totalAmountMap.put("3293000000336397",new BigDecimal(6669738));
		totalAmountMap.put("3293000001428776",new BigDecimal(3828065));
		totalAmountMap.put("3293000000063786",new BigDecimal(223794));
		totalAmountMap.put("3293000003497926",new BigDecimal(185186));
		totalAmountMap.put("3293000000042029",new BigDecimal(712130));
		totalAmountMap.put("3293000000016057",new BigDecimal(1685381));
		totalAmountMap.put("3293000002768165",new BigDecimal(28120575));
		totalAmountMap.put("3293000002904267",new BigDecimal(1961479));
		totalAmountMap.put("3293000001035727",new BigDecimal(3379968));
		totalAmountMap.put("3293000000116187",new BigDecimal(1139738));
		totalAmountMap.put("3293000003316956",new BigDecimal(203688));
		totalAmountMap.put("3293000002033358",new BigDecimal(52469));
		totalAmountMap.put("3293000000079117",new BigDecimal(584254));
		totalAmountMap.put("3293000000097496",new BigDecimal(3199024));
		totalAmountMap.put("3293000003549027",new BigDecimal(24434630));
		totalAmountMap.put("3293000003540747",new BigDecimal(183912));
		totalAmountMap.put("3293000000056407",new BigDecimal(16079353));
		totalAmountMap.put("3293000000037827",new BigDecimal(1290232));
		totalAmountMap.put("3293000002204188",new BigDecimal(170468));
		totalAmountMap.put("3293000002726237",new BigDecimal(4554104));
		totalAmountMap.put("3293000003355995",new BigDecimal(349340));
		totalAmountMap.put("3293000000555385",new BigDecimal(146869));
		totalAmountMap.put("3293000000044697",new BigDecimal(1508140));
		totalAmountMap.put("3293000001384876",new BigDecimal(2040620));
		totalAmountMap.put("3293000002981865",new BigDecimal(287310));
		totalAmountMap.put("3293000002854875",new BigDecimal(263846));
		totalAmountMap.put("3293000000016947",new BigDecimal(701580));
		totalAmountMap.put("3293000000077595",new BigDecimal(259961));
		totalAmountMap.put("3293000001144168",new BigDecimal(842722));
		totalAmountMap.put("3293000000041808",new BigDecimal(103864));
		totalAmountMap.put("3293000001901986",new BigDecimal(465355));
		totalAmountMap.put("3293000003318866",new BigDecimal(571132));
		totalAmountMap.put("3293000000102648",new BigDecimal(635965));
		totalAmountMap.put("3293000000054367",new BigDecimal(12075409));
		totalAmountMap.put("3293000003299486",new BigDecimal(4356886));
		totalAmountMap.put("3293000003352297",new BigDecimal(754114));
		totalAmountMap.put("3293000002922596",new BigDecimal(1947764));
		totalAmountMap.put("3293000003389276",new BigDecimal(1991403));
		totalAmountMap.put("3293000001438338",new BigDecimal(179324));
		totalAmountMap.put("3293000000092157",new BigDecimal(303400));
		totalAmountMap.put("3293000000027248",new BigDecimal(7648614));
		totalAmountMap.put("3293000000741747",new BigDecimal(341156));
		totalAmountMap.put("3293000003043188",new BigDecimal(845590));
		totalAmountMap.put("3293000000101419",new BigDecimal(5887691));
		totalAmountMap.put("3293000000063907",new BigDecimal(2200497));
		totalAmountMap.put("3293000001165865",new BigDecimal(217992));
		totalAmountMap.put("3293000000083187",new BigDecimal(624405));
		totalAmountMap.put("3293000001223648",new BigDecimal(1761436));
		totalAmountMap.put("3293000002908186",new BigDecimal(544408));
		totalAmountMap.put("3293000000042608",new BigDecimal(477640));
		totalAmountMap.put("3293000000103518",new BigDecimal(877734));
		totalAmountMap.put("3293000001519795",new BigDecimal(1731157));
		totalAmountMap.put("3293000000093937",new BigDecimal(803915));
		totalAmountMap.put("3293000002419387",new BigDecimal(541440));
		totalAmountMap.put("3293000000088127",new BigDecimal(56454));
		totalAmountMap.put("3293000003245786",new BigDecimal(1721594));
		totalAmountMap.put("3293000002044757",new BigDecimal(1004596));
		totalAmountMap.put("3293000000113158",new BigDecimal(3842553));
		totalAmountMap.put("3293000000027517",new BigDecimal(570424));
		totalAmountMap.put("3293000000762186",new BigDecimal(7154656));
		totalAmountMap.put("3293000000980317",new BigDecimal(104770));
		totalAmountMap.put("3293000002428866",new BigDecimal(104680));
		totalAmountMap.put("3293000003534786",new BigDecimal(1678856));
		totalAmountMap.put("3293000000751626",new BigDecimal(2892112));
		totalAmountMap.put("3293000000184727",new BigDecimal(1210300));
		totalAmountMap.put("3293000002798465",new BigDecimal(983693));
		totalAmountMap.put("3293000000059147",new BigDecimal(1709231));
		totalAmountMap.put("3293000000028038",new BigDecimal(1926645));
		totalAmountMap.put("3293000002109467",new BigDecimal(71082));
		totalAmountMap.put("3293000003195555",new BigDecimal(239500));
		totalAmountMap.put("3293000002021398",new BigDecimal(186300));
		totalAmountMap.put("3293000002394947",new BigDecimal(1495214));
		totalAmountMap.put("3293000003153566",new BigDecimal(35391785));
		totalAmountMap.put("3293000002358926",new BigDecimal(4467698));
		totalAmountMap.put("3293000003333767",new BigDecimal(800496));
		totalAmountMap.put("3293000003255317",new BigDecimal(52230));
		totalAmountMap.put("3293000000289616",new BigDecimal(46700));
		totalAmountMap.put("3293000002401928",new BigDecimal(1595437));
		totalAmountMap.put("3293000000957645",new BigDecimal(142903));
		totalAmountMap.put("3293000002058706",new BigDecimal(544995));
		totalAmountMap.put("3293000000048018",new BigDecimal(1198632));
		totalAmountMap.put("3293000000087296",new BigDecimal(400100));
		totalAmountMap.put("3293000003323078",new BigDecimal(12297));
		totalAmountMap.put("3293000000057217",new BigDecimal(484995));
		totalAmountMap.put("3293000002867745",new BigDecimal(209740));
		totalAmountMap.put("3293000000217837",new BigDecimal(391002));
		totalAmountMap.put("3293000002359616",new BigDecimal(21824));
		totalAmountMap.put("3293000000073277",new BigDecimal(87827));
		totalAmountMap.put("3293000002445866",new BigDecimal(293170));
		totalAmountMap.put("3293000003497237",new BigDecimal(19465));
		totalAmountMap.put("3293000001542238",new BigDecimal(75260));
		totalAmountMap.put("3293000000930927",new BigDecimal(99116610));
		totalAmountMap.put("3293000001911607",new BigDecimal(412441));
		totalAmountMap.put("3293000003484438",new BigDecimal(107476));
		totalAmountMap.put("3293000000097476",new BigDecimal(230265));
		totalAmountMap.put("3293000002718207",new BigDecimal(1469400));
		totalAmountMap.put("3293000000075186",new BigDecimal(8270662));
		totalAmountMap.put("3293000002573076",new BigDecimal(367204));
		totalAmountMap.put("3293000002287755",new BigDecimal(2061372));
		totalAmountMap.put("3293000003282856",new BigDecimal(4479));
		totalAmountMap.put("3293000000315866",new BigDecimal(1332531));
		totalAmountMap.put("3293000000036637",new BigDecimal(149800));
		totalAmountMap.put("3293000003112897",new BigDecimal(26668));
		totalAmountMap.put("3293000003400808",new BigDecimal(11522));
		totalAmountMap.put("3293000000588236",new BigDecimal(396631));
		totalAmountMap.put("3293000000063077",new BigDecimal(20949));
		totalAmountMap.put("3293000002228038",new BigDecimal(151844));
		totalAmountMap.put("3293000002958684",new BigDecimal(678184));
		totalAmountMap.put("3293000003545555",new BigDecimal(264075));
		totalAmountMap.put("3293000000083428",new BigDecimal(9972017));
		totalAmountMap.put("3293000000086047",new BigDecimal(112130));
		totalAmountMap.put("3293000000083318",new BigDecimal(1171398));
		totalAmountMap.put("3293000000170766",new BigDecimal(973522));
		totalAmountMap.put("3293000000510367",new BigDecimal(26569086));
		totalAmountMap.put("3293000003405408",new BigDecimal(523566));
		totalAmountMap.put("3293000003523976",new BigDecimal(268152));
		totalAmountMap.put("3293000003390438",new BigDecimal(661550));
		totalAmountMap.put("3293000003365286",new BigDecimal(55));
		totalAmountMap.put("3293000002085456",new BigDecimal(337104));
		totalAmountMap.put("3293000002200677",new BigDecimal(469430));
		totalAmountMap.put("3293000003145348",new BigDecimal(14976415));
		totalAmountMap.put("3293000002403478",new BigDecimal(974532));
		totalAmountMap.put("3293000001215197",new BigDecimal(1486794));
		totalAmountMap.put("3293000003476417",new BigDecimal(442604));
		totalAmountMap.put("3293000000064576",new BigDecimal(438738));
		totalAmountMap.put("3293000000079066",new BigDecimal(214155));
		totalAmountMap.put("3293000002915127",new BigDecimal(1030574));
		totalAmountMap.put("3293000003464896",new BigDecimal(584202));
		totalAmountMap.put("3293000002066926",new BigDecimal(453966));
		totalAmountMap.put("3293000002448018",new BigDecimal(3777048));
		totalAmountMap.put("3293000000034828",new BigDecimal(1908290));
		totalAmountMap.put("3293000002757085",new BigDecimal(538328));
		totalAmountMap.put("3293000000065386",new BigDecimal(32617));
		totalAmountMap.put("3293000003528616",new BigDecimal(280803));
		totalAmountMap.put("3293000001321368",new BigDecimal(2452863));
		totalAmountMap.put("3293000000763456",new BigDecimal(1649474));
		totalAmountMap.put("3293000000111288",new BigDecimal(31991));
		totalAmountMap.put("3293000002766884",new BigDecimal(498790));
		totalAmountMap.put("3293000000021897",new BigDecimal(6910970));
		totalAmountMap.put("3293000000757515",new BigDecimal(7800));
		totalAmountMap.put("3293000000032508",new BigDecimal(55336));
		totalAmountMap.put("3293000000061248",new BigDecimal(1138704));
		totalAmountMap.put("3293000000278137",new BigDecimal(559530));
		totalAmountMap.put("3293000003352228",new BigDecimal(277482));
		totalAmountMap.put("3293000000058846",new BigDecimal(1117654));
		totalAmountMap.put("3293000000126197",new BigDecimal(433900));
		totalAmountMap.put("3293000000032468",new BigDecimal(5773269));
		totalAmountMap.put("3293000000095516",new BigDecimal(612844));
		totalAmountMap.put("3293000001681826",new BigDecimal(5327972));
		totalAmountMap.put("3293000003554646",new BigDecimal(1605370));
		totalAmountMap.put("3293000000021468",new BigDecimal(102330));
		totalAmountMap.put("3293000003428028",new BigDecimal(360058));
		totalAmountMap.put("3293000000295417",new BigDecimal(1947414));
		totalAmountMap.put("3293000003351566",new BigDecimal(648666));
		totalAmountMap.put("3293000002087496",new BigDecimal(1775830));
		totalAmountMap.put("3293000002969984",new BigDecimal(207820));
		totalAmountMap.put("3293000003402198",new BigDecimal(567850));
		totalAmountMap.put("3293000002071956",new BigDecimal(1705083));
		totalAmountMap.put("3293000003248228",new BigDecimal(91000));
		totalAmountMap.put("3293000002259955",new BigDecimal(1736316));
		totalAmountMap.put("3293000000084028",new BigDecimal(10090));
		totalAmountMap.put("3293000003542177",new BigDecimal(1658740));
		totalAmountMap.put("3293000003539266",new BigDecimal(140800));
		totalAmountMap.put("3293000002234528",new BigDecimal(302414));
		totalAmountMap.put("3293000000122088",new BigDecimal(268183));
		totalAmountMap.put("3293000003080008",new BigDecimal(231414));
		totalAmountMap.put("3293000000312429",new BigDecimal(1096167));
		totalAmountMap.put("3293000002086227",new BigDecimal(8803805));
		totalAmountMap.put("3293000000037057",new BigDecimal(192556));
		totalAmountMap.put("3293000001980736",new BigDecimal(820426));
		totalAmountMap.put("3293000001358826",new BigDecimal(399560));
		totalAmountMap.put("3293000001150328",new BigDecimal(0));
		totalAmountMap.put("3293000002527706",new BigDecimal(2305376));
		totalAmountMap.put("3293000001004419",new BigDecimal(1281614));
		totalAmountMap.put("3293000001575774",new BigDecimal(192045));
		totalAmountMap.put("3293000002605356",new BigDecimal(3725241));
		totalAmountMap.put("3293000003556645",new BigDecimal(210673364));
		totalAmountMap.put("3293000002297317",new BigDecimal(179280));
		totalAmountMap.put("3293000002837307",new BigDecimal(2510649));
		totalAmountMap.put("3293000000128087",new BigDecimal(120700));
		totalAmountMap.put("3293000003114628",new BigDecimal(512800));
		totalAmountMap.put("3293000003533487",new BigDecimal(701408));
	}
	**/
	public List<FallbackDto> getFallbackList() {
		log.info("解析文件开始");
		List<FallbackDto> dtoList = new ArrayList<>();
		try {
			List<String> fileList = FileUtil.readLines(new File("D:\\work\\SAAS店主余额v0613(1).csv"), "utf-8");
			for(String file: fileList) {
				FallbackDto dto =  new FallbackDto();
				List<String> split = Arrays.asList(file.split("[|]"));
				dto.setThirdCustid(split.get(0));
				dto.setCustAcctid(split.get(1));
				dto.setThirdHtId(split.get(2));
				dto.setTranAmount(split.get(3));
				dto.setCustomerType(CustomerType.NM.getName());
				dtoList.add(dto);
			}
		}catch (Exception e) {
			log.error("解析文件失败",e);
		} 
		log.info("解析文件完成,size:{}",dtoList.size());
		return dtoList;
	}
	
	public List<FallbackDto> pinganAmountFallBack(List<FallbackDto> dtoList) {
		log.info("处理回退开始,处理:{}",dtoList.size());
		List<FallbackDto> failList = new ArrayList<>();
		if(CollectionUtils.isEmpty(dtoList)) {
			log.info("回退的size为0退出");
			return failList;
		}
			for(int i = 0;i<dtoList.size();i++) {
				if(i==0) {
					continue;
				}
				FallbackDto dto = dtoList.get(i);
				Resp sp = new Resp();
			   try {
				    BigDecimal tranAmount = new BigDecimal(dto.getTranAmount());
				    BigDecimal totalAmount = totalAmountMap.get(dto.getCustAcctid());
				    if(totalAmount==null) {
				    	dto.setErrorCode("11111111");
						dto.setErrorName("");
						dto.setErrorMessage("不在回退的范围类退出");
						failList.add(dto);
						continue;
				    }
				    BigDecimal currentAmount = currentAmountMap.get(dto.getCustAcctid());
				    if(Objects.isNull(currentAmount)) {
				    	currentAmount = new BigDecimal(0);
				    	currentAmountMap.put(dto.getCustAcctid(), currentAmount);
				    }
				    
			    	if(currentAmount.compareTo(totalAmount)<0) {
			    		if(totalAmount.subtract(currentAmount).compareTo(tranAmount)<0) {
			    			dto.setTranAmount(String.valueOf(totalAmount.subtract(currentAmount).toPlainString()));
			    		}
			    	}else {
			    		dto.setErrorCode("11111111");
						dto.setErrorName("");
						dto.setErrorMessage("金额已够退出");
						failList.add(dto);
						continue;
			    	}
			    	currentAmount =currentAmount.add(new BigDecimal(dto.getTranAmount()));
			    	
					String resp =  HttpClient.doPost("http://zuul.infra.aikucun.com/pingan-web/api/pingan/assets/revokecredit", JSON.toJSONString(dto));
			    	sp = JSONObject.parseObject(resp, Resp.class);
					if(!sp.isSuccess()) {
						dto.setErrorCode(String.valueOf(sp.getErrorCode()));
						dto.setErrorName(sp.getErrorName());
						dto.setErrorMessage(sp.getErrorMessage());
						failList.add(dto);
						continue;
					}else {
						total = total.add(new BigDecimal(1));
						currentAmountMap.put(dto.getCustAcctid(), currentAmount);
					}
				}catch (Exception e) {
					dto.setErrorCode(sp.getErrorCode()+"");
					dto.setErrorName(sp.getErrorName());
					dto.setErrorMessage(sp.getErrorMessage());
					failList.add(dto);
					log.error("处理回退失败",e);
				}
			}
			log.info("处理回退完成,失败:{}",failList.size());
			return failList;
	}
	
	public void writeFail(List<FallbackDto> failList) {
		log.info("输出失败的回退明细开始");
		List<String> appendStr = new ArrayList<>();
		if(CollectionUtils.isEmpty(failList)) {
			return ;
		}
		File file = new File("D:\\work\\fail.txt");
		FileUtil.del(file);
		if(!file.exists()) {
			try {
				file.createNewFile();
			} catch (IOException e) {
				log.error("创建回退文件失败",e);
				
			}
		}
		StringBuilder builder = new StringBuilder();
        builder.append("third_custid");
        builder.append("|");
        builder.append("cust_acctid");
        builder.append("|");
        builder.append("third_htid");
        builder.append("|");
        builder.append("tran_amount");
        builder.append("|");
        builder.append("errorCode");
        builder.append("|");
        builder.append("errorName");
        builder.append("|");
        builder.append("errorMessage");
        
        appendStr.add(builder.toString());
		for(int i = 0;i<failList.size();i++) {
			FallbackDto fallbackDto = failList.get(i);
			builder = new StringBuilder();
            builder.append(fallbackDto.getThirdCustid());
            builder.append("|");
            builder.append(fallbackDto.getCustAcctid());
            builder.append("|");
            builder.append(fallbackDto.getThirdHtId());
            builder.append("|");
            builder.append(fallbackDto.getTranAmount());
            builder.append("|");
            builder.append(fallbackDto.getErrorCode());
            builder.append("|");
            builder.append(fallbackDto.getErrorName());
            builder.append("|");
            builder.append(fallbackDto.getErrorMessage());
            appendStr.add(builder.toString());
		}
		if(CollectionUtils.isNotEmpty(appendStr)) {
			FileUtil.appendLines(appendStr, file, "utf-8");
		}
		log.info("输出失败的回退明细完成");
	}
	
	public void writeDealFall() {
		log.info("输出失败的回退明细开始");
		List<String> appendStr = new ArrayList<>();
		File file = new File("D:\\work\\deal.txt");
		FileUtil.del(file);
		if(!FileUtil.exist(file)) {
			try {
				file.createNewFile();
			} catch (IOException e) {
				log.error("创建回退文件失败",e);
				
			}
		}
		StringBuilder builder = new StringBuilder();
        builder.append("平安商户号");
        builder.append("|");
        builder.append("回退总金额");
        appendStr.add(builder.toString());
        Set<String> keySet = currentAmountMap.keySet();
        for(String key:keySet) {
        	builder = new StringBuilder();
        	builder.append(key);
        	builder.append("|");
        	builder.append(String.valueOf(currentAmountMap.get(key).setScale(2, BigDecimal.ROUND_DOWN).toPlainString()));
        	appendStr.add(builder.toString());
        }
		if(CollectionUtils.isNotEmpty(appendStr)) {
			FileUtil.appendLines(appendStr, file, "utf-8");
		}
		log.info("输出失败的回退明细完成");
	}
	
	public static void main(String[] args) {
		FallbackHelp fallbackHelp = new FallbackHelp();
		List<FallbackDto> list = fallbackHelp.getFallbackList();
		List<FallbackDto>  failList = fallbackHelp.pinganAmountFallBack(list);
		fallbackHelp.writeFail(failList);
		fallbackHelp.writeDealFall();
		log.info("total:{}",total.longValue());
	}
	

}
