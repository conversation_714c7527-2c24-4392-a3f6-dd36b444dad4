package com.akucun.account.proxy.common.utils;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
public class HttpClientPoolConfig {

    @Value("${http-pool.connection-request-timeout:5000}")
    private int connectionRequestTimeout;

    // 连接超时
    @Value("${http-pool.connection-timeout:5000}")
    private int connectionTimeout;

    // 数据读取超时时间
    @Value("${http-pool.socket-timeout:5000}")
    private int socketTimeout;

    // 同路由并发数
    @Value("${http-pool.max-per-route:100}")
    private int maxPerRoute;

    // 池最大数
    @Value("${http-pool.max-total:1000}")
    private int maxTotal;

    // 重试次数
    @Value("${http-pool.retry-times:0}")
    private int retryTimes;

    public int getRetryTimes() {
        return retryTimes;
    }

    public int getConnectionRequestTimeout() {
        return connectionRequestTimeout;
    }

    public int getConnectionTimeout() {
        return connectionTimeout;
    }

    public int getSocketTimeout() {
        return socketTimeout;
    }

    public int getMaxPerRoute() {
        return maxPerRoute;
    }

    public int getMaxTotal() {
        return maxTotal;
    }
}