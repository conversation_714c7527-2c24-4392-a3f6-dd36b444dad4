package com.akucun.account.proxy.common.enums;

import com.akucun.fps.common.constants.MEnum;

/**
 * @Author: silei
 * @Date: 2020/12/8
 * @desc: 异步任务状态
 */
public class PostActionBizStatus extends MEnum<PostActionBizStatus> {

    public static final PostActionBizStatus DEFAULT = (PostActionBizStatus)create("DEFAULT", 0, "未处理");
    public static final PostActionBizStatus SUCCESS = (PostActionBizStatus)create("SUCCESS", 1, "成功");
    public static final PostActionBizStatus RETRY = (PostActionBizStatus)create("RETRY", 2, "失败需要重试");
    public static final PostActionBizStatus ERROR = (PostActionBizStatus)create("ERROR", 3, "失败不需要重试");

    private static final long serialVersionUID = -70748216461843821L;
}
