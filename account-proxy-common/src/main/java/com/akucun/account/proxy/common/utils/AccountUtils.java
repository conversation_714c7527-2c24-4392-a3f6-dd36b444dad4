package com.akucun.account.proxy.common.utils;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.common.constant.CustomerType;
import com.akucun.account.proxy.common.enums.ResponseEnum;
import com.akucun.account.proxy.common.enums.ResultStatus;
import com.akucun.cloud.security.CodeUtils;
import com.mengxiang.base.common.log.Logger;
import org.apache.commons.lang3.StringUtils;

/**
 * @Author: silei
 * @Date: 2020/10/21
 * @desc: 账户工具类
 */
public class AccountUtils {

    /**
     * 获取店主customerCode
     * @param customerCode
     * @param customerType
     * @return
     */
    public static String getSellerCode(String customerCode, String customerType) {
        //拼接店主编码
        if (CustomerType.NM.getName().equals(customerType)) {
            return customerType + customerCode;
        } else {
            return customerCode;
        }
    }

    public static String getCustomerCode(String customerCode, String customerType) {
        if (CustomerType.NM.getName().equals(customerType)) {
            return customerCode.substring(2);
        } else {
            return customerCode;
        }
    }

    public static boolean isFinalStatus(String status) {
        return ResultStatus.S.getCode().equals(status) || ResultStatus.F.getCode().equals(status);
    }

    /**
     * 处理店主的customerCode
     * @param customerCode
     * @param customerType
     * @return
     */
    public static String convertCustomerCode(String customerCode, String customerType){
        //拼接店主编码
        if (CustomerType.NM.getName().equals(customerType) && StringUtils.isNumeric(customerCode)) {
            return CustomerType.NM.getName() + customerCode;
        }
        return customerCode;
    }


    /**
     * 字符串加密
     *
     * @param str
     * @return
     */
    public static Result<String> encrypt(String str) {
        try {
            com.akucun.common.Result<String> encrypt = CodeUtils.encrypt(str);
            if (!encrypt.isSuccess() || StringUtils.isEmpty(encrypt.getData())) {
                Logger.error("AccountUtils encrypt fail :{}", encrypt.getMessage());
                return Results.error(ResponseEnum.ACCORE_101505);
            } else {
                return Result.success(encrypt.getData());
            }
        } catch (Exception e) {
            Logger.error("AccountUtils encrypt exception:", e);
            return Results.error(ResponseEnum.ACCORE_101504);
        }
    }



}
