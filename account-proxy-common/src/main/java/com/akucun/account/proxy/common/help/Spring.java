package com.akucun.account.proxy.common.help;

import java.math.BigDecimal;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class Spring {
	
	public static Map<String,BigDecimal> spring = new ConcurrentHashMap<>();

	
	static {
		spring.put("****************",new BigDecimal(12170));
		spring.put("****************",new BigDecimal(12167));
		spring.put("****************",new BigDecimal(12165));
		spring.put("****************",new BigDecimal(12148));
		spring.put("****************",new BigDecimal(12136));
		spring.put("****************",new BigDecimal(12122));
		spring.put("****************",new BigDecimal(12107));
		spring.put("****************",new BigDecimal(12090));
		spring.put("****************",new BigDecimal(12082));
		spring.put("****************",new BigDecimal(12078));
		spring.put("****************",new BigDecimal(12077));
		spring.put("****************",new BigDecimal(12065));
		spring.put("****************",new BigDecimal(12061));
		spring.put("****************",new BigDecimal(12044));
		spring.put("3562000042380207",new BigDecimal(12043));
		spring.put("3562000045198225",new BigDecimal(12043));
		spring.put("3562000043404747",new BigDecimal(12038));
		spring.put("3562000040964475",new BigDecimal(12016));
		spring.put("3562000040538745",new BigDecimal(12004));
		spring.put("3562000041383685",new BigDecimal(12000));
		spring.put("3562000045108735",new BigDecimal(12000));
		spring.put("3562000045104366",new BigDecimal(12000));
		spring.put("3562000040522646",new BigDecimal(12000));
		spring.put("3562000041612307",new BigDecimal(11988));
		spring.put("3562000036113726",new BigDecimal(11975));
		spring.put("3562000045122207",new BigDecimal(11959));
		spring.put("3562000041490286",new BigDecimal(11956));
		spring.put("3562000045082835",new BigDecimal(11946));
		spring.put("3562000045020646",new BigDecimal(11941));
		spring.put("3562000040535584",new BigDecimal(11930));
		spring.put("3562000040527725",new BigDecimal(11922));
		spring.put("3562000044076545",new BigDecimal(11904));
		spring.put("3562000045181725",new BigDecimal(11900));
		spring.put("3562000040541816",new BigDecimal(11900));
		spring.put("3562000040548615",new BigDecimal(11900));
		spring.put("3562000045011706",new BigDecimal(11835));
		spring.put("3562000044439476",new BigDecimal(11830));
		spring.put("3562000041384066",new BigDecimal(11816));
		spring.put("3562000039144127",new BigDecimal(11800));
		spring.put("3562000040548954",new BigDecimal(11796));
		spring.put("3562000040518905",new BigDecimal(11795));
		spring.put("3562000040517974",new BigDecimal(11778));
		spring.put("3562000041707915",new BigDecimal(11768));
		spring.put("3562000040543806",new BigDecimal(11753));
		spring.put("3562000041710027",new BigDecimal(11751));
		spring.put("3562000038440816",new BigDecimal(11750));
		spring.put("3562000037359425",new BigDecimal(11749));
		spring.put("3562000040524117",new BigDecimal(11740));
		spring.put("3562000041060665",new BigDecimal(11725));
		spring.put("3562000040528285",new BigDecimal(11722));
		spring.put("3562000037801954",new BigDecimal(11722));
		spring.put("3562000040820396",new BigDecimal(11700));
		spring.put("3562000043964984",new BigDecimal(11684));
		spring.put("3562000044598464",new BigDecimal(11680));
		spring.put("3562000040703675",new BigDecimal(11678));
		spring.put("3562000041067285",new BigDecimal(11675));
		spring.put("3562000040520626",new BigDecimal(11656));
		spring.put("3562000040523865",new BigDecimal(11638));
		spring.put("3562000040918365",new BigDecimal(11626));
		spring.put("3562000039079983",new BigDecimal(11617));
		spring.put("3562000040525116",new BigDecimal(11617));
		spring.put("3562000040544806",new BigDecimal(11606));
		spring.put("3562000040542785",new BigDecimal(11595));
		spring.put("3562000041257694",new BigDecimal(11591));
		spring.put("3562000040535275",new BigDecimal(11583));
		spring.put("3562000036727425",new BigDecimal(11580));
		spring.put("3562000041421367",new BigDecimal(11566));
		spring.put("3562000041314008",new BigDecimal(11558));
		spring.put("3562000041127716",new BigDecimal(11551));
		spring.put("3562000044985305",new BigDecimal(11545));
		spring.put("3562000041461875",new BigDecimal(11541));
		spring.put("3562000036351146",new BigDecimal(11530));
		spring.put("3562000045144506",new BigDecimal(11520));
		spring.put("3562000040539615",new BigDecimal(11513));
		spring.put("3562000043823655",new BigDecimal(11506));
		spring.put("3562000040552226",new BigDecimal(11504));
		spring.put("3562000041512496",new BigDecimal(11500));
		spring.put("3562000045108764",new BigDecimal(11500));
		spring.put("3562000045086335",new BigDecimal(11500));
		spring.put("3562000040520936",new BigDecimal(11500));
		spring.put("3562000041267815",new BigDecimal(11500));
		spring.put("3562000045189604",new BigDecimal(11476));
		spring.put("3562000043404656",new BigDecimal(11474));
		spring.put("3562000037674245",new BigDecimal(11424));
		spring.put("3562000040542396",new BigDecimal(11420));
		spring.put("3562000041291307",new BigDecimal(11403));
		spring.put("3562000038050265",new BigDecimal(11400));
		spring.put("3562000040531595",new BigDecimal(11400));
		spring.put("3562000040554436",new BigDecimal(11399));
		spring.put("3562000040537185",new BigDecimal(11396));
		spring.put("3562000042141297",new BigDecimal(11396));
		spring.put("3562000036424546",new BigDecimal(11384));
		spring.put("3562000040519165",new BigDecimal(11382));
		spring.put("3562000036401447",new BigDecimal(11381));
		spring.put("3562000042637715",new BigDecimal(11370));
		spring.put("3562000040553605",new BigDecimal(11365));
		spring.put("3562000045163854",new BigDecimal(11350));
		spring.put("3562000036810236",new BigDecimal(11342));
		spring.put("3562000041140387",new BigDecimal(11340));
		spring.put("3562000041707554",new BigDecimal(11339));
		spring.put("3562000045212486",new BigDecimal(11300));
		spring.put("3562000039721185",new BigDecimal(11300));
		spring.put("3562000040922086",new BigDecimal(11300));
		spring.put("3562000038275573",new BigDecimal(11300));
		spring.put("3562000037978592",new BigDecimal(11274));
		spring.put("3562000040540626",new BigDecimal(11270));
		spring.put("3562000040526436",new BigDecimal(11268));
		spring.put("3562000037953904",new BigDecimal(11261));
		spring.put("3562000036113946",new BigDecimal(11260));
		spring.put("3562000045034706",new BigDecimal(11256));
		spring.put("3562000041470636",new BigDecimal(11239));
		spring.put("3562000041030537",new BigDecimal(11239));
		spring.put("3562000040540546",new BigDecimal(11234));
		spring.put("3562000040520706",new BigDecimal(11223));
		spring.put("3562000045139584",new BigDecimal(11207));
		spring.put("3562000040519265",new BigDecimal(11207));
		spring.put("3562000045036805",new BigDecimal(11205));
		spring.put("3562000042981574",new BigDecimal(11200));
		spring.put("3562000040531437",new BigDecimal(11200));
		spring.put("3562000041711855",new BigDecimal(11176));
		spring.put("3562000040545825",new BigDecimal(11144));
		spring.put("3562000040533826",new BigDecimal(11136));
		spring.put("3562000037655344",new BigDecimal(11131));
		spring.put("3562000040537055",new BigDecimal(11125));
		spring.put("3562000041562505",new BigDecimal(11122));
		spring.put("3562000040547165",new BigDecimal(11105));
		spring.put("3562000041184746",new BigDecimal(11100));
		spring.put("3562000040545964",new BigDecimal(11100));
		spring.put("3562000040542276",new BigDecimal(11100));
		spring.put("3562000044654316",new BigDecimal(11095));
		spring.put("3562000041038736",new BigDecimal(11091));
		spring.put("3562000045155084",new BigDecimal(11090));
		spring.put("3562000037978404",new BigDecimal(11087));
		spring.put("3562000045008085",new BigDecimal(11085));
		spring.put("3562000041598035",new BigDecimal(11070));
		spring.put("3562000037968493",new BigDecimal(11069));
		spring.put("3562000041527674",new BigDecimal(11069));
		spring.put("3562000040545126",new BigDecimal(11052));
		spring.put("3562000040539495",new BigDecimal(11048));
		spring.put("3562000040552475",new BigDecimal(11042));
		spring.put("3562000038156345",new BigDecimal(11040));
		spring.put("3562000040538645",new BigDecimal(11039));
		spring.put("3562000045084625",new BigDecimal(11038));
		spring.put("3562000037494935",new BigDecimal(11023));
		spring.put("3562000040797883",new BigDecimal(11009));
		spring.put("3562000040518465",new BigDecimal(11007));
		spring.put("3562000040551554",new BigDecimal(11003));
		spring.put("3562000040518155",new BigDecimal(11003));
		spring.put("3562000045191236",new BigDecimal(11000));
		spring.put("3562000045109884",new BigDecimal(11000));
		spring.put("3562000045094316",new BigDecimal(11000));
		spring.put("3562000043836185",new BigDecimal(11000));
		spring.put("3562000041663564",new BigDecimal(11000));
		spring.put("3562000040545645",new BigDecimal(10983));
		spring.put("3562000041109985",new BigDecimal(10977));
		spring.put("3562000040552725",new BigDecimal(10965));
		spring.put("3562000041661825",new BigDecimal(10934));
		spring.put("3562000044972136",new BigDecimal(10923));
		spring.put("3562000041403248",new BigDecimal(10922));
		spring.put("3562000040534176",new BigDecimal(10913));
		spring.put("3562000041352207",new BigDecimal(10900));
		spring.put("3562000041586593",new BigDecimal(10896));
		spring.put("3562000040552825",new BigDecimal(10891));
		spring.put("3562000043032666",new BigDecimal(10891));
		spring.put("3562000040525884",new BigDecimal(10891));
		spring.put("3562000042670645",new BigDecimal(10890));
		spring.put("3562000041594085",new BigDecimal(10887));
		spring.put("3562000038239715",new BigDecimal(10874));
		spring.put("3562000040545554",new BigDecimal(10868));
		spring.put("3562000042786054",new BigDecimal(10867));
		spring.put("3562000040554535",new BigDecimal(10862));
		spring.put("3562000045152845",new BigDecimal(10861));
		spring.put("3562000040549825",new BigDecimal(10861));
		spring.put("3562000043912356",new BigDecimal(10848));
		spring.put("3562000040553554",new BigDecimal(10840));
		spring.put("3562000040544546",new BigDecimal(10831));
		spring.put("3562000040524256",new BigDecimal(10815));
		spring.put("3562000044718515",new BigDecimal(10814));
		spring.put("3562000041295495",new BigDecimal(10808));
		spring.put("3562000045154475",new BigDecimal(10802));
		spring.put("3562000037803884",new BigDecimal(10800));
		spring.put("3562000040531096",new BigDecimal(10800));
		spring.put("3562000045146705",new BigDecimal(10800));
		spring.put("3562000039123836",new BigDecimal(10797));
		spring.put("3562000040542626",new BigDecimal(10774));
		spring.put("3562000045134047",new BigDecimal(10766));
		spring.put("3562000045177814",new BigDecimal(10743));
		spring.put("3562000045015375",new BigDecimal(10734));
		spring.put("3562000040528495",new BigDecimal(10723));
		spring.put("3562000045101386",new BigDecimal(10720));
		spring.put("3562000040526805",new BigDecimal(10717));
		spring.put("3562000043950584",new BigDecimal(10704));
		spring.put("3562000040535146",new BigDecimal(10700));
		spring.put("3562000040537146",new BigDecimal(10698));
		spring.put("3562000045161275",new BigDecimal(10690));
		spring.put("3562000041027816",new BigDecimal(10676));
		spring.put("3562000040546654",new BigDecimal(10670));
		spring.put("3562000039588513",new BigDecimal(10666));
		spring.put("3562000040979135",new BigDecimal(10660));
		spring.put("3562000045150905",new BigDecimal(10648));
		spring.put("3562000040550754",new BigDecimal(10630));
		spring.put("3562000040631447",new BigDecimal(10630));
		spring.put("3562000042132297",new BigDecimal(10617));
		spring.put("3562000040544496",new BigDecimal(10616));
		spring.put("3562000045102946",new BigDecimal(10614));
		spring.put("3562000041489226",new BigDecimal(10609));
		spring.put("3562000045186254",new BigDecimal(10609));
		spring.put("3562000041610555",new BigDecimal(10600));
		spring.put("3562000042011218",new BigDecimal(10600));
		spring.put("3562000040976644",new BigDecimal(10600));
		spring.put("3562000040843575",new BigDecimal(10600));
		spring.put("3562000045094915",new BigDecimal(10582));
		spring.put("3562000045002266",new BigDecimal(10576));
		spring.put("3562000040538026",new BigDecimal(10576));
		spring.put("3562000037449664",new BigDecimal(10573));
		spring.put("3562000040518984",new BigDecimal(10565));
		spring.put("3562000040519615",new BigDecimal(10551));
		spring.put("3562000040539465",new BigDecimal(10543));
		spring.put("3562000037243716",new BigDecimal(10540));
		spring.put("3562000041333817",new BigDecimal(10530));
		spring.put("3562000040529075",new BigDecimal(10513));
		spring.put("3562000040551436",new BigDecimal(10508));
		spring.put("3562000042850046",new BigDecimal(10507));
		spring.put("3562000042421766",new BigDecimal(10505));
		spring.put("3562000038266904",new BigDecimal(10496));
		spring.put("3562000045173774",new BigDecimal(10492));
		spring.put("3562000045141906",new BigDecimal(10478));
		spring.put("3562000041986274",new BigDecimal(10470));
		spring.put("3562000040540695",new BigDecimal(10444));
		spring.put("3562000041468654",new BigDecimal(10426));
		spring.put("3562000040545026",new BigDecimal(10400));
		spring.put("3562000045156873",new BigDecimal(10400));
		spring.put("3562000045200047",new BigDecimal(10395));
		spring.put("3562000041516306",new BigDecimal(10389));
		spring.put("3562000045168125",new BigDecimal(10387));
		spring.put("3562000043671365",new BigDecimal(10386));
		spring.put("3562000040549845",new BigDecimal(10361));
		spring.put("3562000037296245",new BigDecimal(10355));
		spring.put("3562000038274525",new BigDecimal(10346));
		spring.put("3562000037938015",new BigDecimal(10340));
		spring.put("3562000040555025",new BigDecimal(10335));
		spring.put("3562000040522536",new BigDecimal(10330));
		spring.put("3562000043832926",new BigDecimal(10323));
		spring.put("3562000042955284",new BigDecimal(10319));
		spring.put("3562000042856254",new BigDecimal(10317));
		spring.put("3562000040523256",new BigDecimal(10314));
		spring.put("3562000040553085",new BigDecimal(10300));
		spring.put("3562000040552146",new BigDecimal(10290));
		spring.put("3562000040951725",new BigDecimal(10269));
		spring.put("3562000042063706",new BigDecimal(10258));
		spring.put("3562000045138475",new BigDecimal(10238));
		spring.put("3562000040533685",new BigDecimal(10236));
		spring.put("3562000037945544",new BigDecimal(10203));
		spring.put("3562000040550065",new BigDecimal(10201));
		spring.put("3562000041472156",new BigDecimal(10200));
		spring.put("3562000045157804",new BigDecimal(10200));
		spring.put("3562000040553964",new BigDecimal(10200));
		spring.put("3562000044056815",new BigDecimal(10199));
		spring.put("3562000043863794",new BigDecimal(10186));
		spring.put("3562000041481327",new BigDecimal(10183));
		spring.put("3562000040527864",new BigDecimal(10174));
		spring.put("3562000040544207",new BigDecimal(10166));
		spring.put("3562000037981225",new BigDecimal(10143));
		spring.put("3562000040554236",new BigDecimal(10132));
		spring.put("3562000040545774",new BigDecimal(10131));
		spring.put("3562000040942555",new BigDecimal(10126));
		spring.put("3562000040517845",new BigDecimal(10120));
		spring.put("3562000041339865",new BigDecimal(10100));
		spring.put("3562000036054136",new BigDecimal(10100));
		spring.put("3562000038595843",new BigDecimal(10090));
		spring.put("3562000045185345",new BigDecimal(10080));
		spring.put("3562000045194055",new BigDecimal(10080));
		spring.put("3562000045161255",new BigDecimal(10076));
		spring.put("3562000044985454",new BigDecimal(10061));
		spring.put("3562000040546835",new BigDecimal(10037));
		spring.put("3562000045031775",new BigDecimal(10033));
		spring.put("3562000040535116",new BigDecimal(10031));
		spring.put("3562000042144947",new BigDecimal(10010));
		spring.put("3562000045174346",new BigDecimal(10004));
		spring.put("3562000045090375",new BigDecimal(10000));
		spring.put("3562000045105146",new BigDecimal(10000));
		spring.put("3562000040523585",new BigDecimal(10000));
		spring.put("3562000045036085",new BigDecimal(10000));
		spring.put("3562000039092475",new BigDecimal(9979));
		spring.put("3562000041565294",new BigDecimal(9964));
		spring.put("3562000041002018",new BigDecimal(9963));
		spring.put("3562000036114456",new BigDecimal(9952));
		spring.put("3562000041544926",new BigDecimal(9944));
		spring.put("3562000041025386",new BigDecimal(9943));
		spring.put("3562000040519525",new BigDecimal(9937));
		spring.put("3562000044517416",new BigDecimal(9918));
		spring.put("3562000041428396",new BigDecimal(9901));
		spring.put("3562000040547326",new BigDecimal(9899));
		spring.put("3562000040555054",new BigDecimal(9895));
		spring.put("3562000045172326",new BigDecimal(9895));
		spring.put("3562000038390615",new BigDecimal(9891));
		spring.put("3562000040873615",new BigDecimal(9883));
		spring.put("3562000041159725",new BigDecimal(9878));
		spring.put("3562000041548994",new BigDecimal(9850));
		spring.put("3562000036115195",new BigDecimal(9840));
		spring.put("3562000045137085",new BigDecimal(9840));
		spring.put("3562000042844526",new BigDecimal(9829));
		spring.put("3562000040692255",new BigDecimal(9828));
		spring.put("3562000040549495",new BigDecimal(9822));
		spring.put("3562000045189983",new BigDecimal(9814));
		spring.put("3562000041590735",new BigDecimal(9814));
		spring.put("3562000045170535",new BigDecimal(9813));
		spring.put("3562000040535106",new BigDecimal(9810));
		spring.put("3562000040519195",new BigDecimal(9808));
		spring.put("3562000040523147",new BigDecimal(9800));
		spring.put("3562000040538446",new BigDecimal(9800));
		spring.put("3562000041379664",new BigDecimal(9796));
		spring.put("3562000040543995",new BigDecimal(9795));
		spring.put("3562000040527854",new BigDecimal(9785));
		spring.put("3562000041558924",new BigDecimal(9782));
		spring.put("3562000038204755",new BigDecimal(9762));
		spring.put("3562000040522027",new BigDecimal(9751));
		spring.put("3562000040545754",new BigDecimal(9750));
		spring.put("3562000038564335",new BigDecimal(9745));
		spring.put("3562000043670664",new BigDecimal(9745));
		spring.put("3562000045145426",new BigDecimal(9744));
		spring.put("3562000040542606",new BigDecimal(9738));
		spring.put("3562000040940396",new BigDecimal(9730));
		spring.put("3562000045001196",new BigDecimal(9727));
		spring.put("3562000040520207",new BigDecimal(9722));
		spring.put("3562000040857015",new BigDecimal(9720));
		spring.put("3562000041407286",new BigDecimal(9714));
		spring.put("3562000040521506",new BigDecimal(9712));
		spring.put("3562000041685763",new BigDecimal(9700));
		spring.put("3562000043724575",new BigDecimal(9700));
		spring.put("3562000045194515",new BigDecimal(9700));
		spring.put("3562000041759553",new BigDecimal(9700));
		spring.put("3562000045152925",new BigDecimal(9690));
		spring.put("3562000040983385",new BigDecimal(9687));
		spring.put("3562000040531965",new BigDecimal(9686));
		spring.put("3562000040545574",new BigDecimal(9679));
		spring.put("3562000042146296",new BigDecimal(9664));
		spring.put("3562000040524846",new BigDecimal(9660));
		spring.put("3562000040523156",new BigDecimal(9648));
		spring.put("3562000042411507",new BigDecimal(9627));
		spring.put("3562000040527505",new BigDecimal(9618));
		spring.put("3562000040551684",new BigDecimal(9610));
		spring.put("3562000040862974",new BigDecimal(9605));
		spring.put("3562000040534655",new BigDecimal(9602));
		spring.put("3562000040543137",new BigDecimal(9601));
		spring.put("3562000041113167",new BigDecimal(9600));
		spring.put("3562000040528106",new BigDecimal(9590));
		spring.put("3562000040521417",new BigDecimal(9583));
		spring.put("3562000038101166",new BigDecimal(9581));
		spring.put("3562000040527406",new BigDecimal(9571));
		spring.put("3562000041410907",new BigDecimal(9570));
		spring.put("3562000040538175",new BigDecimal(9569));
		spring.put("3562000041436506",new BigDecimal(9564));
		spring.put("3562000045171295",new BigDecimal(9557));
		spring.put("3562000042136417",new BigDecimal(9554));
		spring.put("3562000045046725",new BigDecimal(9552));
		spring.put("3562000041446437",new BigDecimal(9547));
		spring.put("3562000045109974",new BigDecimal(9540));
		spring.put("3562000041579814",new BigDecimal(9540));
		spring.put("3562000040535436",new BigDecimal(9529));
		spring.put("3562000041294726",new BigDecimal(9525));
		spring.put("3562000040522347",new BigDecimal(9518));
		spring.put("3562000040536594",new BigDecimal(9513));
		spring.put("3562000041606195",new BigDecimal(9512));
		spring.put("3562000045036285",new BigDecimal(9500));
		spring.put("3562000040518654",new BigDecimal(9500));
		spring.put("3562000040552645",new BigDecimal(9473));
		spring.put("3562000041299674",new BigDecimal(9469));
		spring.put("3562000040550146",new BigDecimal(9461));
		spring.put("3562000042412218",new BigDecimal(9458));
		spring.put("3562000040524755",new BigDecimal(9455));
		spring.put("3562000041664905",new BigDecimal(9448));
		spring.put("3562000041144547",new BigDecimal(9444));
		spring.put("3562000040541655",new BigDecimal(9439));
		spring.put("3562000040524176",new BigDecimal(9432));
		spring.put("3562000040518574",new BigDecimal(9418));
		spring.put("3562000040552275",new BigDecimal(9400));
		spring.put("3562000041631237",new BigDecimal(9392));
		spring.put("3562000043670894",new BigDecimal(9386));
		spring.put("3562000040539674",new BigDecimal(9344));
		spring.put("3562000045080815",new BigDecimal(9330));
		spring.put("3562000040534985",new BigDecimal(9322));
		spring.put("3562000041507006",new BigDecimal(9309));
		spring.put("3562000040531636",new BigDecimal(9300));
		spring.put("3562000039064326",new BigDecimal(9300));
		spring.put("3562000040535185",new BigDecimal(9300));
		spring.put("3562000040555893",new BigDecimal(9300));
		spring.put("3562000040555883",new BigDecimal(9288));
		spring.put("3562000040518764",new BigDecimal(9287));
		spring.put("3562000037971804",new BigDecimal(9274));
		spring.put("3562000045175325",new BigDecimal(9261));
		spring.put("3562000040527954",new BigDecimal(9254));
		spring.put("3562000040547385",new BigDecimal(9250));
		spring.put("3562000039690714",new BigDecimal(9242));
		spring.put("3562000040522117",new BigDecimal(9239));
		spring.put("3562000041043786",new BigDecimal(9237));
		spring.put("3562000040535954",new BigDecimal(9233));
		spring.put("3562000039216784",new BigDecimal(9219));
		spring.put("3562000040526525",new BigDecimal(9218));
		spring.put("3562000040552026",new BigDecimal(9205));
		spring.put("3562000040553954",new BigDecimal(9203));
		spring.put("3562000041310048",new BigDecimal(9200));
		spring.put("3562000040531356",new BigDecimal(9200));
		spring.put("3562000041598394",new BigDecimal(9200));
		spring.put("3562000040530526",new BigDecimal(9199));
		spring.put("3562000036114946",new BigDecimal(9190));
		spring.put("3562000040553116",new BigDecimal(9171));
		spring.put("3562000042136196",new BigDecimal(9142));
		spring.put("3562000040540086",new BigDecimal(9140));
		spring.put("3562000045199724",new BigDecimal(9138));
		spring.put("3562000040517684",new BigDecimal(9112));
		spring.put("3562000037748245",new BigDecimal(9110));
		spring.put("3562000042802706",new BigDecimal(9109));
		spring.put("3562000040542546",new BigDecimal(9106));
		spring.put("3562000045198683",new BigDecimal(9105));
		spring.put("3562000040526275",new BigDecimal(9100));
		spring.put("3562000040530706",new BigDecimal(9100));
		spring.put("3562000045179115",new BigDecimal(9100));
		spring.put("3562000041649715",new BigDecimal(9097));
		spring.put("3562000040546735",new BigDecimal(9075));
		spring.put("3562000041510017",new BigDecimal(9071));
		spring.put("3562000040529175",new BigDecimal(9069));
		spring.put("3562000040518106",new BigDecimal(9062));
		spring.put("3562000040534266",new BigDecimal(9053));
		spring.put("3562000045160684",new BigDecimal(9051));
		spring.put("3562000041313627",new BigDecimal(9049));
		spring.put("3562000040551136",new BigDecimal(9048));
		spring.put("3562000042613955",new BigDecimal(9040));
		spring.put("3562000040551265",new BigDecimal(9012));
		spring.put("3562000041127166",new BigDecimal(9007));
		spring.put("3562000038614385",new BigDecimal(9000));
		spring.put("3562000036284046",new BigDecimal(9000));
		spring.put("3562000040528564",new BigDecimal(9000));
		spring.put("3562000045086305",new BigDecimal(9000));
		spring.put("3562000045156573",new BigDecimal(9000));
		spring.put("3562000040541636",new BigDecimal(8995));
		spring.put("3562000045118925",new BigDecimal(8990));
		spring.put("3562000040531076",new BigDecimal(8988));
		spring.put("3562000041688693",new BigDecimal(8986));
		spring.put("3562000045169105",new BigDecimal(8973));
		spring.put("3562000043917545",new BigDecimal(8956));
		spring.put("3562000041604595",new BigDecimal(8951));
		spring.put("3562000040527745",new BigDecimal(8942));
		spring.put("3562000040539446",new BigDecimal(8941));
		spring.put("3562000039654814",new BigDecimal(8940));
		spring.put("3562000040545545",new BigDecimal(8924));
		spring.put("3562000040522066",new BigDecimal(8922));
		spring.put("3562000040197745",new BigDecimal(8917));
		spring.put("3562000041225086",new BigDecimal(8914));
		spring.put("3562000041354946",new BigDecimal(8914));
		spring.put("3562000045185644",new BigDecimal(8911));
		spring.put("3562000045103985",new BigDecimal(8900));
		spring.put("3562000040554365",new BigDecimal(8900));
		spring.put("3562000045076824",new BigDecimal(8900));
		spring.put("3562000036727464",new BigDecimal(8890));
		spring.put("3562000040540107",new BigDecimal(8879));
		spring.put("3562000041499745",new BigDecimal(8874));
		spring.put("3562000045196534",new BigDecimal(8873));
		spring.put("3562000045153884",new BigDecimal(8848));
		spring.put("3562000037392784",new BigDecimal(8848));
		spring.put("3562000040539126",new BigDecimal(8834));
		spring.put("3562000040542846",new BigDecimal(8831));
		spring.put("3562000044993864",new BigDecimal(8820));
		spring.put("3562000040529285",new BigDecimal(8810));
		spring.put("3562000041252816",new BigDecimal(8806));
		spring.put("3562000045167435",new BigDecimal(8800));
		spring.put("3562000040520037",new BigDecimal(8800));
		spring.put("3562000041504096",new BigDecimal(8790));
		spring.put("3562000041579863",new BigDecimal(8778));
		spring.put("3562000040537684",new BigDecimal(8771));
		spring.put("3562000040534286",new BigDecimal(8770));
		spring.put("3562000040527694",new BigDecimal(8770));
		spring.put("3562000042641706",new BigDecimal(8770));
		spring.put("3562000045172385",new BigDecimal(8770));
		spring.put("3562000040549905",new BigDecimal(8768));
		spring.put("3562000040553864",new BigDecimal(8761));
		spring.put("3562000040555534",new BigDecimal(8760));
		spring.put("3562000040548016",new BigDecimal(8757));
		spring.put("3562000041069446",new BigDecimal(8757));
		spring.put("3562000040544926",new BigDecimal(8757));
		spring.put("3562000044899683",new BigDecimal(8751));
		spring.put("3562000040547316",new BigDecimal(8750));
		spring.put("3562000045085394",new BigDecimal(8742));
		spring.put("3562000044376954",new BigDecimal(8727));
		spring.put("3562000039298215",new BigDecimal(8722));
		spring.put("3562000041586335",new BigDecimal(8716));
		spring.put("3562000041612906",new BigDecimal(8709));
		spring.put("3562000041110907",new BigDecimal(8705));
		spring.put("3562000040532137",new BigDecimal(8701));
		spring.put("3562000042854874",new BigDecimal(8700));
		spring.put("3562000041158805",new BigDecimal(8700));
		spring.put("3562000038288553",new BigDecimal(8700));
		spring.put("3562000043404617",new BigDecimal(8689));
		spring.put("3562000040525854",new BigDecimal(8680));
		spring.put("3562000036885672",new BigDecimal(8678));
		spring.put("3562000041127685",new BigDecimal(8676));
		spring.put("3562000037663773",new BigDecimal(8670));
		spring.put("3562000040531166",new BigDecimal(8669));
		spring.put("3562000043650426",new BigDecimal(8663));
		spring.put("3562000038271246",new BigDecimal(8655));
		spring.put("3562000042854625",new BigDecimal(8652));
		spring.put("3562000045087445",new BigDecimal(8649));
		spring.put("3562000045173864",new BigDecimal(8647));
		spring.put("3562000040551654",new BigDecimal(8640));
		spring.put("3562000040524526",new BigDecimal(8633));
		spring.put("3562000040538505",new BigDecimal(8626));
		spring.put("3562000041556573",new BigDecimal(8621));
		spring.put("3562000042122927",new BigDecimal(8620));
		spring.put("3562000040797005",new BigDecimal(8617));
		spring.put("3562000042907625",new BigDecimal(8616));
		spring.put("3562000043922965",new BigDecimal(8615));
		spring.put("3562000040520655",new BigDecimal(8614));
		spring.put("3562000045151355",new BigDecimal(8611));
		spring.put("3562000041428027",new BigDecimal(8603));
		spring.put("3562000041012907",new BigDecimal(8602));
		spring.put("3562000045138974",new BigDecimal(8600));
		spring.put("3562000043385326",new BigDecimal(8600));
		spring.put("3562000045213865",new BigDecimal(8600));
		spring.put("3562000042170327",new BigDecimal(8591));
		spring.put("3562000040288615",new BigDecimal(8587));
		spring.put("3562000040544076",new BigDecimal(8574));
		spring.put("3562000040533166",new BigDecimal(8570));
		spring.put("3562000040622307",new BigDecimal(8565));
		spring.put("3562000040524916",new BigDecimal(8548));
		spring.put("3562000040551485",new BigDecimal(8543));
		spring.put("3562000041469505",new BigDecimal(8543));
		spring.put("3562000040543565",new BigDecimal(8538));
		spring.put("3562000040539994",new BigDecimal(8538));
		spring.put("3562000043438575",new BigDecimal(8534));
		spring.put("3562000041344018",new BigDecimal(8531));
		spring.put("3562000041418646",new BigDecimal(8527));
		spring.put("3562000040524816",new BigDecimal(8525));
		spring.put("3562000041504056",new BigDecimal(8525));
		spring.put("3562000040521486",new BigDecimal(8513));
		spring.put("3562000041280396",new BigDecimal(8504));
		spring.put("3562000038692364",new BigDecimal(8500));
		spring.put("3562000040528654",new BigDecimal(8500));
		spring.put("3562000040544017",new BigDecimal(8500));
		spring.put("3562000040520217",new BigDecimal(8500));
		spring.put("3562000037436854",new BigDecimal(8482));
		spring.put("3562000038323417",new BigDecimal(8474));
		spring.put("3562000040554095",new BigDecimal(8470));
		spring.put("3562000043462196",new BigDecimal(8461));
		spring.put("3562000045020746",new BigDecimal(8455));
		spring.put("3562000043732496",new BigDecimal(8453));
		spring.put("3562000045172465",new BigDecimal(8440));
		spring.put("3562000040540496",new BigDecimal(8439));
		spring.put("3562000045090485",new BigDecimal(8434));
		spring.put("3562000040541675",new BigDecimal(8433));
		spring.put("3562000040539535",new BigDecimal(8428));
		spring.put("3562000041250386",new BigDecimal(8420));
		spring.put("3562000038270905",new BigDecimal(8417));
		spring.put("3562000041419276",new BigDecimal(8412));
		spring.put("3562000040533565",new BigDecimal(8400));
		spring.put("3562000040523366",new BigDecimal(8400));
		spring.put("3562000041159894",new BigDecimal(8400));
		spring.put("3562000038284925",new BigDecimal(8400));
		spring.put("3562000042931585",new BigDecimal(8395));
		spring.put("3562000040894426",new BigDecimal(8393));
		spring.put("3562000040516605",new BigDecimal(8393));
		spring.put("3562000041102537",new BigDecimal(8387));
		spring.put("3562000038277963",new BigDecimal(8386));
		spring.put("3562000040525385",new BigDecimal(8383));
		spring.put("3562000041529106",new BigDecimal(8373));
		spring.put("3562000041320747",new BigDecimal(8369));
		spring.put("3562000045139884",new BigDecimal(8352));
		spring.put("3562000042957904",new BigDecimal(8351));
		spring.put("3562000038270925",new BigDecimal(8340));
		spring.put("3562000043670994",new BigDecimal(8340));
		spring.put("3562000037297544",new BigDecimal(8338));
		spring.put("3562000040554136",new BigDecimal(8332));
		spring.put("3562000040519635",new BigDecimal(8330));
		spring.put("3562000041317826",new BigDecimal(8329));
		spring.put("3562000040552416",new BigDecimal(8326));
		spring.put("3562000040533137",new BigDecimal(8325));
		spring.put("3562000045009605",new BigDecimal(8325));
		spring.put("3562000037972274",new BigDecimal(8324));
		spring.put("3562000040540646",new BigDecimal(8320));
		spring.put("3562000045173306",new BigDecimal(8317));
		spring.put("3562000045190055",new BigDecimal(8316));
		spring.put("3562000036114746",new BigDecimal(8311));
		spring.put("3562000041418675",new BigDecimal(8304));
		spring.put("3562000044654106",new BigDecimal(8300));
		spring.put("3562000045131916",new BigDecimal(8300));
		spring.put("3562000040537406",new BigDecimal(8297));
		spring.put("3562000045189634",new BigDecimal(8295));
		spring.put("3562000040523675",new BigDecimal(8291));
		spring.put("3562000044991745",new BigDecimal(8282));
		spring.put("3562000041377046",new BigDecimal(8279));
		spring.put("3562000041391086",new BigDecimal(8269));
		spring.put("3562000040533795",new BigDecimal(8256));
		spring.put("3562000037524635",new BigDecimal(8255));
		spring.put("3562000045043066",new BigDecimal(8254));
		spring.put("3562000041595924",new BigDecimal(8252));
		spring.put("3562000041719994",new BigDecimal(8235));
		spring.put("3562000041561065",new BigDecimal(8226));
		spring.put("3562000040707684",new BigDecimal(8205));
		spring.put("3562000040544585",new BigDecimal(8203));
		spring.put("3562000036113736",new BigDecimal(8200));
		spring.put("3562000045175154",new BigDecimal(8200));
		spring.put("3562000041106256",new BigDecimal(8200));
		spring.put("3562000040536635",new BigDecimal(8196));
		spring.put("3562000045161016",new BigDecimal(8190));
		spring.put("3562000043422277",new BigDecimal(8186));
		spring.put("3562000045192306",new BigDecimal(8184));
		spring.put("3562000038128046",new BigDecimal(8178));
		spring.put("3562000040519674",new BigDecimal(8163));
		spring.put("3562000041062706",new BigDecimal(8160));
		spring.put("3562000045125664",new BigDecimal(8146));
		spring.put("3562000040528216",new BigDecimal(8144));
		spring.put("3562000045185335",new BigDecimal(8142));
		spring.put("3562000037358445",new BigDecimal(8140));
		spring.put("3562000041330397",new BigDecimal(8139));
		spring.put("3562000041299136",new BigDecimal(8134));
		spring.put("3562000043404666",new BigDecimal(8121));
		spring.put("3562000040539155",new BigDecimal(8109));
		spring.put("3562000040525994",new BigDecimal(8102));
		spring.put("3562000042157925",new BigDecimal(8100));
		spring.put("3562000045173126",new BigDecimal(8100));
		spring.put("3562000037385454",new BigDecimal(8100));
		spring.put("3562000039765562",new BigDecimal(8100));
		spring.put("3562000040521296",new BigDecimal(8100));
		spring.put("3562000040550046",new BigDecimal(8099));
		spring.put("3562000040527735",new BigDecimal(8096));
		spring.put("3562000040550925",new BigDecimal(8095));
		spring.put("3562000040528625",new BigDecimal(8088));
		spring.put("3562000040553674",new BigDecimal(8088));
		spring.put("3562000040532555",new BigDecimal(8087));
		spring.put("3562000040551206",new BigDecimal(8082));
		spring.put("3562000040548784",new BigDecimal(8077));
		spring.put("3562000040544417",new BigDecimal(8074));
		spring.put("3562000043404586",new BigDecimal(8070));
		spring.put("3562000040547874",new BigDecimal(8064));
		spring.put("3562000041379825",new BigDecimal(8063));
		spring.put("3562000040518036",new BigDecimal(8053));
		spring.put("3562000045146884",new BigDecimal(8050));
		spring.put("3562000040530117",new BigDecimal(8048));
		spring.put("3562000038277924",new BigDecimal(8040));
		spring.put("3562000040540965",new BigDecimal(8040));
		spring.put("3562000038270554",new BigDecimal(8040));
		spring.put("3562000040533706",new BigDecimal(8040));
		spring.put("3562000040529735",new BigDecimal(8036));
		spring.put("3562000038716484",new BigDecimal(8030));
		spring.put("3562000041647055",new BigDecimal(8026));
		spring.put("3562000042907815",new BigDecimal(8025));
		spring.put("3562000044071985",new BigDecimal(8000));
		spring.put("3562000041359455",new BigDecimal(8000));
		spring.put("3562000040527026",new BigDecimal(8000));
		spring.put("3562000041513985",new BigDecimal(8000));
		spring.put("3562000043400866",new BigDecimal(8000));
		spring.put("3562000038128545",new BigDecimal(8000));
		spring.put("3562000037436574",new BigDecimal(8000));
		spring.put("3562000040543427",new BigDecimal(8000));
		spring.put("3562000044187465",new BigDecimal(8000));
		spring.put("3562000040551306",new BigDecimal(7986));
		spring.put("3562000040536754",new BigDecimal(7984));
		spring.put("3562000041597315",new BigDecimal(7983));
		spring.put("3562000037673405",new BigDecimal(7976));
		spring.put("3562000041329047",new BigDecimal(7970));
		spring.put("3562000041502496",new BigDecimal(7966));
		spring.put("3562000040526554",new BigDecimal(7960));
		spring.put("3562000045069973",new BigDecimal(7956));
		spring.put("3562000037436864",new BigDecimal(7956));
		spring.put("3562000040537525",new BigDecimal(7950));
		spring.put("3562000041534117",new BigDecimal(7940));
		spring.put("3562000040530327",new BigDecimal(7940));
		spring.put("3562000040545845",new BigDecimal(7930));
		spring.put("3562000041523975",new BigDecimal(7927));
		spring.put("3562000040521636",new BigDecimal(7922));
		spring.put("3562000042948436",new BigDecimal(7919));
		spring.put("3562000045084265",new BigDecimal(7918));
		spring.put("3562000038121506",new BigDecimal(7915));
		spring.put("3562000040527485",new BigDecimal(7905));
		spring.put("3562000040536645",new BigDecimal(7904));
		spring.put("3562000040533726",new BigDecimal(7904));
		spring.put("3562000040544955",new BigDecimal(7900));
		spring.put("3562000040540826",new BigDecimal(7900));
		spring.put("3562000041237636",new BigDecimal(7900));
		spring.put("3562000045194475",new BigDecimal(7900));
		spring.put("3562000045157005",new BigDecimal(7900));
		spring.put("3562000040529505",new BigDecimal(7883));
		spring.put("3562000044262536",new BigDecimal(7877));
		spring.put("3562000043904017",new BigDecimal(7876));
		spring.put("3562000041750206",new BigDecimal(7874));
		spring.put("3562000040523946",new BigDecimal(7873));
		spring.put("3562000043456026",new BigDecimal(7871));
		spring.put("3562000040531985",new BigDecimal(7857));
		spring.put("3562000040549165",new BigDecimal(7852));
		spring.put("3562000041664874",new BigDecimal(7844));
		spring.put("3562000040543795",new BigDecimal(7839));
		spring.put("3562000037751354",new BigDecimal(7820));
		spring.put("3562000040553994",new BigDecimal(7801));
		spring.put("3562000044616725",new BigDecimal(7800));
		spring.put("3562000041446256",new BigDecimal(7800));
		spring.put("3562000041370417",new BigDecimal(7800));
		spring.put("3562000040549016",new BigDecimal(7800));
		spring.put("3562000038400176",new BigDecimal(7800));
		spring.put("3562000040533746",new BigDecimal(7800));
		spring.put("3562000041699753",new BigDecimal(7800));
		spring.put("3562000040555824",new BigDecimal(7800));
		spring.put("3562000041656514",new BigDecimal(7799));
		spring.put("3562000041251636",new BigDecimal(7792));
		spring.put("3562000037669733",new BigDecimal(7780));
		spring.put("3562000042853835",new BigDecimal(7771));
		spring.put("3562000041030937",new BigDecimal(7765));
		spring.put("3562000040531207",new BigDecimal(7765));
		spring.put("3562000041743746",new BigDecimal(7760));
		spring.put("3562000042156764",new BigDecimal(7757));
		spring.put("3562000041251816",new BigDecimal(7756));
	}
	
}
