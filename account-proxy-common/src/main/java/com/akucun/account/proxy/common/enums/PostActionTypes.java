package com.akucun.account.proxy.common.enums;

import com.akucun.fps.common.constants.MEnum;

/**
 * @Author: silei
 * @Date: 2020/12/8
 * @desc:
 */
public class PostActionTypes extends MEnum<PostActionTypes> {

    private static final long serialVersionUID = 6416426707646992152L;

    public static final PostActionTypes DELAY_COMMISSION = (PostActionTypes)create("DELAY_COMMISSION", 0, "异步分佣任务");
    public static final PostActionTypes WITHDRAWTAX_MEMBERTRADE = (PostActionTypes)create("WITHDRAWTAX_MEMBERTRADE", 1, "提现扣税会员交易任务");
    public static final PostActionTypes WITHDRAWTAX_FAILAMOUNT = (PostActionTypes)create("WITHDRAWTAX_FAILAMOUNT", 2, "提现扣税失败金额更新任务");
    public static final PostActionTypes PINGAN_DELAY_WITHDRAW = (PostActionTypes)create("PINGAN_DELAY_WITHDRAW", 3, "平安提现异步任务");
    public static final PostActionTypes PINGAN_DELAY_REVOKE = (PostActionTypes)create("PINGAN_DELAY_REVOKE", 4, "撤销异步任务");
    public static final PostActionTypes PINGAN_REFUND = (PostActionTypes)create("PINGAN_REFUND", 5, "平安退款异步任务");
    public static final PostActionTypes ACCOUNT_CENTER_COMPENSATE = (PostActionTypes)create("ACCOUNT_CENTER_COMPENSATE", 6, "资产补偿异步任务");
    public static final PostActionTypes WECHAT_WITHDRAW_FAIL_AMOUNT = (PostActionTypes)create("WECHAT_WITHDRAW_FAIL_AMOUNT", 7, "微信提现失败累计金额退回任务");
    public static final PostActionTypes WITHDRAWSERVICEFEE_FAILAMOUNT = (PostActionTypes)create("WITHDRAWSERVICEFEE_FAILAMOUNT", 8, "提现扣手续费失败金额更新任务");
    public static final PostActionTypes MERCHANT_WECHAT_WITHDRAW_QUERY = (PostActionTypes)create("MERCHANT_WECHAT_WITHDRAW_QUERY", 10, "商户微信提现结果查询");
    public static final PostActionTypes WECHAT_DELAY_WITHDRAW = (PostActionTypes)create("WECHAT_DELAY_WITHDRAW", 9, "微信异步提现任务");
    public static final PostActionTypes WECHAT_WITHDRAW_QUERY = (PostActionTypes)create("WECHAT_WITHDRAW_QUERY", 11, "微信提现结果查询");
    public static final PostActionTypes PINGAN_DELAY_WITHDRAW_QUERY = (PostActionTypes)create("PINGAN_DELAY_WITHDRAW_QUERY", 12, "平安提现结果查询");
    public static final PostActionTypes PINGAN_REVOKE_CREDIT = (PostActionTypes)create("PINGAN_REVOKE_CREDIT", 13, "平安分账撤销");

    public static final PostActionTypes PINGAN_WITHDRAW = (PostActionTypes)create("PINGAN_WITHDRAW", 14, "平安提现任务");

    public static final PostActionTypes ACCOUNT_MONITOR = (PostActionTypes)create("ACCOUNT_MONITOR", 15, "账户监控");

    public static final PostActionTypes MARKET_FULL_RETURN_CALLBACK_NOTIFY = (PostActionTypes)create("MARKET_FULL_RETURN_CALLBACK_NOTIFY", 16, "营销活动异步回调任务");

    public static final PostActionTypes OA_WORKFLOW_STATUS_POLLING = (PostActionTypes)create("OA_WORKFLOW_STATUS_POLLING", 17, "OA工作流状态轮询任务");

    public static final PostActionTypes PROMO_REWARD_START = (PostActionTypes)create("PROMO_REWARD_START", 18, "营销-奖励下发-执行");

    public static final PostActionTypes PROMO_REWARD_NOTIFY = (PostActionTypes)create("PROMO_REWARD_NOTIFY", 19, "营销-奖励下发-结果回调");

    public static final PostActionTypes PROMO_PAY = (PostActionTypes)create("PROMO_PAY", 20, "营销-支付奖金");

    public static final PostActionTypes WITHDRAW_RECEIPT_DOWNLOAD = (PostActionTypes)create("WITHDRAW_RECEIPT_DOWNLOAD", 21, "提现回单下载任务");
}
