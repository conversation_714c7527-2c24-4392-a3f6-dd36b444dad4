package com.akucun.account.proxy.common.utils;

import com.akucun.account.proxy.common.constant.Constant;
import com.akucun.fps.common.constants.MEnum;
import com.mengxiang.base.common.sequence.spring.SequenceGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */

@Component
public class AccountSequenceManager {

    @Autowired
    private SequenceGenerator sequenceGenerator;

    public String generateTransferNo() {
        return Constant.ZZ + sequenceGenerator.getSequence();
    }

    public String generateWithdrawNo() {
        return Constant.TX + sequenceGenerator.getSequence();
    }

    public String generateVerifyNo(MEnum<?> orderType) {
        return Constant.HX + sequenceGenerator.getSequence();
    }

    public String generateRecNo(MEnum<?> orderEnum) {
        return Constant.SK + sequenceGenerator.getSequence();
    }

    public String generateWithdrawBatchNo() {
        return Constant.BH + sequenceGenerator.getSequence();
    }

    public String generateInitNo() {
        return "AUTOTX" + sequenceGenerator.getSequence();
    }

    public String generateNo() {
        return sequenceGenerator.getSequence();
    }

}
