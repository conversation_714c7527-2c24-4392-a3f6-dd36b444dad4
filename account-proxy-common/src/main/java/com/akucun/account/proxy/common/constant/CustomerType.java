package com.akucun.account.proxy.common.constant;

import com.akucun.fps.common.constants.MEnum;


/**
 * @program: fps-account
 * @description: 客户类型枚举
 * @author: wangliangliang
 * @create: 2018-05-21 16:31
 **/
public class CustomerType extends MEnum<CustomerType> {

    private static final long serialVersionUID = -2709355274804514681L;
    /** 商家 **/
    public static final CustomerType SH = (CustomerType)create("SH", 0, "商户");
    /** 代购 **/
    public static final CustomerType DG = (CustomerType)create("DG", 1, "代购");
    /** NM **/
    public static final CustomerType NM = (CustomerType)create("NM", 2, "H5");
    /** NP **/
    public static final CustomerType NP = (CustomerType)create("NP", 3, "新平台");
    /** openApi **/
    public static final CustomerType OP = (CustomerType)create("OP", 4, "openApi");
    /** CC **/
    public static final CustomerType CC = (CustomerType)create("CC", 5, "公司账户");
    /** NMDL **/
    public static final CustomerType NMDL = (CustomerType)create("NMDL", 6, "H5代理");
    /** DCC **/
    public static final CustomerType DCC = (CustomerType)create("DCC", 7, "今日断码");
    /** 商家微信 **/
    public static final CustomerType SH_WC = (CustomerType)create("SH_WC", 8, "微信商户");
    /** 租户 ***/
    public static final CustomerType AT = (CustomerType)create("AT", 8, "租户");
}

