package com.akucun.account.proxy.common.constant;

/**
 * @Author: silei
 * @Date: 2020/9/1
 * @desc:
 */
public interface CommonConstants {

    //成功Code
    String SUCC_CODE = "0";
    //失败Code
    String FAIL_CODE = "9999";

    int GENERAL_CODE = 999;


    /**
     * 扩展参数
     */

    String BANK_CARD_CODE ="bankCardCode";//银行卡号
    String UPDATE_STATUS ="updateStatus";//账户更新状态
    String PINGAN_CUST_ID ="pinganCustId";//平安账户号
    String UPGRADE_TYPE ="upgradeType";//升级类型
    String TRADE_CHANNEL_NAME = "channelName";//渠道名称
    String TRADE_USERID= "userId";//用户id
    String BIZ_REFUND_NO = "bizRefundNo";//第三方业务退款单号
    String USER_ROLE = "userRole";//用户角色，2店主/3店长
    String TENANT_ID = "tenantId";//租户ID

    /**
     * 老会员系统
     */
    String BRANCH_MEMBER = "MEMBER";
    /**
     * 新账户中心
     */
    String BRANCH_ACCOUNT_CENTER = "ACCOUNT_CENTER";


    /**
     * 新账户余额key
     */
    String NEW_BALANCE_CUSTOMER_KEY = "2721679B9C013EC7FC5B31C673494413";
    /**
     * 新账户余额支付type
     */
    String NEW_PAY_BALANCE_CUSTOMER_TRADE_TYPE_008 = "TRADE_TYPE_008";
    /**
     * 新账户余额type
     */
    String NEW_REFUND_BALANCE_CUSTOMER_TYPE = "TRADE_TYPE_001";
    /**
     * 提现账户余额key
     */
    String CASH_WITHDRAWAL_BALANCE_CUSTOMER_KEY = "D310397BA71383DCD8208A5DD49F25C1";
    /**
     * 提现账户余额type
     */
    String CASH_WITHDRAWAL_BALANCE_CUSTOMER_TRADE_TYPE_008 = "TRADE_TYPE_180";
    /**
     * 提现账户余额type
     */
    String CASH_WITHDRAWAL_BALANCE_CUSTOMER_TRADE_TYPE_181 = "TRADE_TYPE_181";
    /**
     * OpenApi普通用户余额账户配置
     */
    String OPENAPI_BALANCE_CUSTOMER_KEY = "AEC6DAA921F51796F05CC3AE025A04EC";
    /**
     * OpenApi普通用户余额账户,支付type
     */
    String OPENAPI_PAY_BALANCE_CUSTOMER_TRADE_TYPE_120 = "TRADE_TYPE_120";
    /**
     * openapi账户余额type
     */
    String OPENAPI_NEW_REFUND_BALANCE_CUSTOMER_TYPE = "TRADE_TYPE_121";
    /**
     * OpenApi信用账户配置
     */
   String OPENAPI_CREDIT_CUSTOMER_KEY = "AEC6DAA921F51796F05CC3AE025A04EC";
    /**
     * OpenApi信用账户,支付type
     */
    String OPENAPI_PAY_CREDIT_CUSTOMER_TRADE_TYPE_128 = "TRADE_TYPE_128";
    /**
     * OpenApi信用账户,支付type
     */
    String OPENAPI_REFUND_CREDIT_CUSTOMER_TRADE_TYPE_129 = "TRADE_TYPE_129";
    /**
     * 奖励金支付交易类型
     */
    String BONUS_ACCOUNT_PAY_TRADE_TYPE = "bonusAccountPay";
    /**
     * 钱包余额支付交易类型
     */
    String AKC_ACCOUNT_PAY_TRADE_TYPE = "akcAccountPay";
    /**
     * 开放平台支付交易类型
     */
    String OPENAPI_ACCOUNT_PAY_TRADE_TYPE = "openApiAccountPay";
    /**
     * 饷店余额支付交易类型
     */
    String XD_ACCOUNT_PAY_TRADE_TYPE = "xdAccountPay";
    /**
     * 饷店余额退款交易类型
     */
    String XD_ACCOUNT_REFUND_TRADE_TYPE = "xdAccountRefund";
    /**
     * C积分支付交易类型
     */
    String POINT_ACCOUNT_PAY_TRADE_TYPE = "pointAccountPay";
    /**
     * 奖励金支付交易类型
     */
    String BONUS_ACCOUNT_REFUND_TRADE_TYPE = "bonusAccountRefund";
    /**
     * 钱包余额支付交易类型
     */
    String AKC_ACCOUNT_REFUND_TRADE_TYPE = "akcAccountRefund";
    /**
     * 开放平台支付交易类型
     */
    String OPENAPI_ACCOUNT_REFUND_TRADE_TYPE = "openApiAccountRefund";
    /**
     * vip会员卡AES加解密key
     */
    String VIP_CARD_AES_KEY = "e6qxkcsp";



    /**
     * 企业付款到零钱单号前缀
     */
    String TRANSFER_SOURCE_NO_PREFIX = "TX";
    /**
     * 企业付款到零钱redis锁key
     */
    String PAYMENT_TRANSFER_LOCK_PREFIX = "account:proxy:payment:transfer:%s";

    /**
     * 提现到微信
     */
    String PAYMENT_WITHDRAW_LOCK_PREFIX = "account:proxy:payment:withdraw:%s";
    /**
     * 企业付款到零钱商户redis key
     */
    String TRANSFER_MERCHANT_INFO_PREFIX = "account:proxy:transfer:%s";
    /**
     * 企业付款到零钱商户redis key
     */
    String TRANSFER_MERCHANT_INFO_PREFIX_REDIS = "account:proxy:transfer:";

    /**
     * 菜菜小程序渠道编号
     */
    String ORDER_3RD_MP_CHANNEL_CODE = "3RD_MP";

    // 用户身份-店长
    String USER_ROLE_3 = "3";
    // 用户身份-店主
    String USER_ROLE_2 = "2";

    /**
     * 商家营销满返操作锁前缀
     */
    String MERCHANT_FULL_RETURN_MARKET_LOCK_PREFIX = "account:proxy:merchantFullReturnMarket:";

    /**
     * 商家营销满返操作锁前缀
     */
    String PROMO_OAPAY__LOCK_PREFIX = "account:proxy:promo:oapay:";
}
