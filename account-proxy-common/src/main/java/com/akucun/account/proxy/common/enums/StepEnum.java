package com.akucun.account.proxy.common.enums;


public class StepEnum {

    public enum IsRetryAble {
        Y("Y","可重试"),
        N("N", "不可重试");

        private String code;

        private String desc;

        IsRetryAble(String code,String desc){
            this.code = code;
            this.desc = desc;
        }

        public String getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }

    public enum IsHead {
        Y("Y","是第一步"),
        N("N", "不是第一步");

        private String code;

        private String desc;

        IsHead(String code,String desc){
            this.code = code;
            this.desc = desc;
        }

        public String getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }

    public enum StepStatus {
        S("S","成功"),
        F("F", "失败");

        private String code;

        private String desc;

        StepStatus(String code,String desc){
            this.code = code;
            this.desc = desc;
        }

        public String getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }
}
