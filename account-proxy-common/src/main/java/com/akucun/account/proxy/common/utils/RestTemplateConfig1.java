package com.akucun.account.proxy.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

@Slf4j
@Configuration
public class RestTemplateConfig1 {

    @Autowired
    private HttpClientPoolConfig poolConfig;

    @Bean(name = "customRestTemplate")
    public RestTemplate customRestTemplate(@Qualifier("customClientHttpRequestFactory") ClientHttpRequestFactory factory) {
        return new RestTemplate(factory);
    }

    @Bean(name = "customClientHttpRequestFactory")
    public ClientHttpRequestFactory httpRequestFactory() {
        CloseableHttpClient httpClient = getCloseableHttpClient();

        HttpComponentsClientHttpRequestFactory httpComponentsClientHttpRequestFactory = new HttpComponentsClientHttpRequestFactory();
        httpComponentsClientHttpRequestFactory.setHttpClient(httpClient);
        httpComponentsClientHttpRequestFactory.setConnectionRequestTimeout(poolConfig.getConnectionRequestTimeout()); // 不宜过长
        httpComponentsClientHttpRequestFactory.setConnectTimeout(poolConfig.getConnectionTimeout());
        httpComponentsClientHttpRequestFactory.setReadTimeout(poolConfig.getSocketTimeout());

        return httpComponentsClientHttpRequestFactory;
    }

    private CloseableHttpClient getCloseableHttpClient() {
        Registry<ConnectionSocketFactory> registry = RegistryBuilder.<ConnectionSocketFactory>create()
                .register("http", PlainConnectionSocketFactory.getSocketFactory())
                .build();
        PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager(registry);

        // 连接池最大连接数
        connectionManager.setMaxTotal(poolConfig.getMaxTotal());

        // 同路由并发数
        connectionManager.setDefaultMaxPerRoute(poolConfig.getMaxPerRoute());

        CloseableHttpClient httpClient = HttpClients.custom()
                .setConnectionManager(connectionManager)
                .build();
        return httpClient;
    }

}
