package com.akucun.account.proxy.common.utils;

import com.akucun.account.proxy.common.enums.AccountPropertyType;
import com.akucun.account.proxy.facade.stub.enums.CustomerType;

public class CustomerUtils {

    public static String convertCustomerCode(String customerCode, String customerType, String accountType) {
        if(CustomerType.NM.name().equals(customerType)) {
            if("WALLET".equals(accountType)) {
                return customerCode;
            } else {
                return customerType + customerCode;
            }
        } else if(CustomerType.OP.name().equals(customerType)) {
            return customerType + customerCode;
        } else {
            return customerCode;
        }
    }

    public static String convertPinganCustomerType(String customerType, String accountType) {
        if(CustomerType.NM.name().equals(customerType)) {
            if("WALLET".equals(accountType)) {
                return CustomerType.DG.name();
            } else {
                return customerType;
            }
        } else {
            return customerType;
        }
    }

    public static AccountPropertyType getCustProperty(String customerType) {
        if(CustomerType.AT.name().equals(customerType) || CustomerType.OP.name().equals(customerType)
                || CustomerType.QDS.name().equals(customerType) || CustomerType.DXQDS.name().equals(customerType)) {
            return AccountPropertyType.GENERAL;
        } else {
            return AccountPropertyType.MERCHANTS;
        }
    }

}
