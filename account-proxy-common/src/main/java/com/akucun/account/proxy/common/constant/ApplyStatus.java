package com.akucun.account.proxy.common.constant;

import com.akucun.fps.common.constants.MEnum;

/**
 * @Author: silei
 * @Date: 2020/11/12
 * @desc: 对账单结清状态
 */
public class ApplyStatus extends MEnum<ApplyStatus> {

	public static final ApplyStatus INIT = (ApplyStatus)create("INIT", 0, "初始化");
	 
    public static final ApplyStatus UNAUDIT = (ApplyStatus)create("UNAUDIT", 1, "未审核");
    public static final ApplyStatus AGREE = (ApplyStatus)create("AGREE", 2, "审核通过");
    public static final ApplyStatus REFUSE = (ApplyStatus)create("REFUSE", 3, "审核拒绝");
    public static final ApplyStatus FAIL = (ApplyStatus)create("FAIL", 4, "提现失败");
    public static final ApplyStatus SUCC = (ApplyStatus)create("SUCC", 5, "提现成功");
    public static final ApplyStatus DOING = (ApplyStatus)create("DOING", 6, "提现中");

}
