package com.akucun.account.proxy.common.enums;


import lombok.Getter;

public class VipCardEnum {

    @Getter
    public enum VipCardStatus {
        STATUS_10(10,"待激活"),
        STATUS_20(20,"充值中/已激活"),
        STATUS_30(30,"已核销"),
        STATUS_40(40,"核销失败"),
        ;

        private Integer code;

        private String desc;

        VipCardStatus(Integer code,String desc){
            this.code = code;
            this.desc = desc;
        }
    }

    @Getter
    public enum VipCardType {
        TYPE_BONUS("BONUS","待激活"),
        ;

        private String code;

        private String desc;

        VipCardType(String code,String desc){
            this.code = code;
            this.desc = desc;
        }
    }

}
