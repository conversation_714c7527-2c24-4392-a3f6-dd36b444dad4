package com.akucun.account.proxy.common.utils;

import com.aikucun.common2.log.Logger;
import com.akucun.cloud.security.CodeUtils;
import com.akucun.common.Result;
import org.apache.commons.lang3.StringUtils;

public class EncryptUtils {

    public static String decrypt(String text) {
        try {
            Result<String> result = CodeUtils.decrypt(text);
            if(result.isSuccess() && StringUtils.isNotBlank(result.getData())) {
                return result.getData();
            } else {
                Logger.info("解密失败：{}", text);
                return null;
            }
        } catch (Exception e) {
            Logger.info("解密异常：{}", text);
            return null;
        }
    }

}
