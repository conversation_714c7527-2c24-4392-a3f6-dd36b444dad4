package com.akucun.account.proxy.common.enums;

public enum AccountTypeEnum {
	
	XD_NM("XD_NM","饷店店主:customerCode=NM+爱豆编号"),
	XD_NMDL("XD_NMDL", "饷店店长:customerCode=distributorId"),
	AT("AT", "租户"),
    ;

    private String value;
    private String desc;

    AccountTypeEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return  this.value;
    }

    public String getDesc() {
        return this.desc;
    }

}
