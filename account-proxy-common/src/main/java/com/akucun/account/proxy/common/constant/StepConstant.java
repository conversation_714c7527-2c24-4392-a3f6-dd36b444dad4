package com.akucun.account.proxy.common.constant;

/**
 * @Author: silei
 * @Date: 2020/8/26
 * @desc:
 */
public interface StepConstant {

    String ACTION_QUERY = "query";

    String ACTION_EXEC = "execute";

    //流程是否可重试
    String IS_RETRYABLE = "isRetryAble";
    //最大重试次数
    String MAX_RETRY_TIMES = "maxRetryTimes";
    //流程结束
    String STEP_END = "END";

    /**
     * 交易类型
     */
    String ACCOUNT_UPDATE = "accountUpdate";//账户升级

}
