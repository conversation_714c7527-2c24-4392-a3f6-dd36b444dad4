package com.akucun.account.proxy.common.enums;

import org.springframework.util.StringUtils;

/**
 * 会员等级
 *
 * <AUTHOR>
 * @version [版本号, 2020年9月29日]
 */
public enum MemberGrade {

    /*会员等级*/
    SHORT_VERIFY(-2,"SHORT_VERIFY","临时税务登记"),
    SHOP_PLEDGE(-1,"SHOP_PLEDGE","个人店铺申明"),
    PERSON(0, "PERSON", "个人"),
    LESHUI_AUTH(1, "LESHUI_AUTH", "个体认证（乐税）"),
    PERSON_AUTH(2, "PERSON_AUTH", "个体认证（自有）"),
    ENTERPRISE_AUTH(3, "ENTERPRISE_AUTH", "企业认证");

    private Integer code;

    private String name;

    private String desc;

    MemberGrade(Integer code, String name, String desc) {
        this.code = code;
        this.name = name;
        this.desc = desc;
    }

    public static MemberGrade getByCode(Integer code) {
        MemberGrade[] grades = MemberGrade.values();
        if (null != code) {
            for (MemberGrade grade : grades) {
                if (grade.getCode().equals(code)) {
                    return grade;
                }
            }
        }
        return null;
    }

    public static String getAuthName(Integer gradeCode) {
        if(StringUtils.isEmpty(gradeCode)){
            return null;
        }
        if(PERSON_AUTH.getCode().intValue() == gradeCode.intValue() || ENTERPRISE_AUTH.getCode().intValue() == gradeCode.intValue()){
            return "enterprise";
        }else if(SHORT_VERIFY.getCode().intValue() == gradeCode.intValue() || LESHUI_AUTH.getCode().intValue() == gradeCode.intValue()){
            return "leshui";
        }else if(SHOP_PLEDGE.getCode().intValue() == gradeCode.intValue() || PERSON.getCode().intValue() == gradeCode.intValue()){
            return "person";
        }

        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getDesc() {
        return desc;
    }

}
