package com.akucun.account.proxy.common.utils;

import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;


@Component
public class RedisUtil {

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    /**
     * 批量存入
     *
     * @param keyValueMap 键值对
     * @param expire      过期时间（秒）
     * @return List
     */
    public List<Object> bulkCacheData(Map<String, String> keyValueMap, long expire, String prefix) {
        return redisTemplate.executePipelined(new RedisCallback<Object>() {
            @Override
            public Object doInRedis(RedisConnection redisConnection) throws DataAccessException {
                for (Map.Entry<String, String> entry : keyValueMap.entrySet()) {
                    String key = prefix + entry.getKey();
                    String value = entry.getValue();
                    byte[] rawKey = key.getBytes();
                    byte[] rawValue = value.getBytes();
                    redisConnection.setEx(rawKey, expire, rawValue);
                }
                return null;
            }
        });
    }

    /**
     * 查找对应值
     *
     * @param key 键
     * @return String
     */
    public String get(String key) {
        return redisTemplate.opsForValue().get(key);
    }

    /**
     * 设置 String 类型 key-value 并添加过期时间 (毫秒单位)
     * @param key
     * @param value
     * @param time 过期时间,分钟单位
     */
    public  void setForTimeMS(String key, String value,long time){
        redisTemplate.opsForValue().set(key, value, time, TimeUnit.MILLISECONDS);
    }

    /**
     *
     *往Hash中存入数据
     * @param key Redis键
     * @param hKey
     * @param value
     */
    public void hPut(String key, String hKey, Object value){
        redisTemplate.opsForHash().put(key, hKey, value);
    }

    /**
     * 获取Hash中的数据
     *
     * @param key Redis键
     * @param hKey Hash键
     * @return Hash中的对象
     */
    public  Object hGet(String key, String hKey) {
        return redisTemplate.opsForHash().get(key, hKey);
    }

    /**
     * 删除 hkey
     * @param key
     * @param hKey
     * @return
     */
    public long hDel(String key,String hKey){
        return redisTemplate.opsForHash().delete(key,hKey);
    }

    /**
     *
     * @param key
     * @param value
     */
    public void set(String key,String value,long time){
        redisTemplate.opsForValue().set(key,value,time,TimeUnit.SECONDS);
    }

    public void del(String key){
        redisTemplate.delete(key);
    }
}
