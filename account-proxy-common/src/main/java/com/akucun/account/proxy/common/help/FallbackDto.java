package com.akucun.account.proxy.common.help;

import java.io.Serializable;

import lombok.Data;


@Data
public class FallbackDto implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 6184535969067789442L;
	
	private String custAcctid;
	
	private String thirdCustid;
	
	/**
	 * 第三方单号
	 */
	private String thirdHtId;
	
	/**
	 * 交易金额
	 */
	private String tranAmount;
	
	/**
	 * 交易类型 SH
	 */
	private String customerType;
	
	private String errorCode;
	private String errorName;
	private String errorMessage;
	
	public FallbackDto() {
		
	}
	
	public FallbackDto(String thirdHtId,String tranAmount) {
		this.thirdHtId = thirdHtId;
		this.tranAmount =tranAmount;
		this.customerType = "SH";
	}
}
