package com.akucun.account.proxy.common.enums;

public enum TenantTypeEnum {

    //头部用户
	tenantType1(1,"三方小程序"),
	tenantType2(2, "企业饷店"),
    //阿基米德
	tenantType3(3, "SASS标准类型"),
    tenantType4(4, "团长大营与啊呀团"),
    //会员的枚举中有这个值，有用处时可以打开
    //DOU_UNION(5, "豆联盟"),
    OPEN_SUPPLY_DISTRIBUTION(6, "供应链开放-经销"),
    OPEN_SUPPLY_CONSIGNMENT_WITHHOLD(7, "供应链开放-代销帐扣"),
    OPEN_SUPPLY_CONSIGNMENT_COLLECTING(8, "供应链开放-代销代收"),
    OPEN_SUPPLY_CONSIGNMENT_SHARING(9, "供应链开放-代销分帐"),;

    private Integer value;
    private String desc;

    TenantTypeEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return  this.value;
    }

    public String getDesc() {
        return this.desc;
    }

}
