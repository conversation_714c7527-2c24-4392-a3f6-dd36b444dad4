package com.akucun.account.proxy.common.utils;

import io.micrometer.core.instrument.util.StringUtils;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Create on : 2025/1/20 10:23
 **/
@Component
public class RestTemplateUtils {
    
    @Resource(name = "customRestTemplate")
    private RestTemplate restTemplate;
    
    public <T,R> R doPost(String url, T param, ParameterizedTypeReference<R> clazz, String envHeaderValue) {
        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        if (StringUtils.isNotBlank(envHeaderValue)) {
            headers.set("X-Hades-Env-Identity", envHeaderValue);
        }
        // 创建 HttpEntity 包含请求头和请求体
        HttpEntity<T> entity = new HttpEntity<>(param, headers);
        R result = restTemplate.exchange(url,
                HttpMethod.POST,
                entity,
                clazz
                ).getBody();
        return result;
    }

}
