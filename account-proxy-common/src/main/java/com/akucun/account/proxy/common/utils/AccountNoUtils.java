package com.akucun.account.proxy.common.utils;

import com.akucun.cloud.security.CodeUtils;
import com.akucun.fps.common.constants.MEnum;

/**
 * 流水号升级
 *
 * <AUTHOR>
 * */
public class AccountNoUtils {

    public static final AccountSequenceManager accountSequenceManager = (AccountSequenceManager) SpringBeanUtil.getBean("accountSequenceManager");

    public static String generateRecNo(MEnum<?> orderEnum) {
        return accountSequenceManager.generateRecNo(orderEnum);
    }

    public static String generateRecPayNo(MEnum<?> orderEnum) {
        return accountSequenceManager.generateRecNo(orderEnum);
    }

    public static String generateInitNo() {
        return accountSequenceManager.generateInitNo();
    }


    public static String generateWithdrawBatchNo() {
        return accountSequenceManager.generateWithdrawBatchNo();
    }

    public static String generateVerifyNo(MEnum<?> orderEnum) {
        return accountSequenceManager.generateVerifyNo(orderEnum);
    }

    public static String generateWithdrawNo() {
        return accountSequenceManager.generateWithdrawNo();
    }

    public static String generateTransferNo() {
        return accountSequenceManager.generateWithdrawNo();
    }


    public static String generateNo() {
        return accountSequenceManager.generateNo();
    }
    
}
