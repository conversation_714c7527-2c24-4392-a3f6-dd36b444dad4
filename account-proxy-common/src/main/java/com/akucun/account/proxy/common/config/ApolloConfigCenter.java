package com.akucun.account.proxy.common.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
@Getter
@Setter
public class ApolloConfigCenter {

	@Value("#{${account.monitor.map:{\"3E4D85885F77F389CC6645109977C7B4_TRADE_TYPE_176\":\"true\"}}}")
	public Map<String, Boolean> accountMonitorMap;

	@Value("#{${balance.not.enough.code.map:{\"100600\": \"true\",\"100900\": \"true\",\"100700\": \"true\",\"100800\": \"true\"}}}")
	public Map<String, Boolean> balanceNotEnoughCodeMap;

	@Value("${test.env.group:stable}")
	public String testEnvGroup;
}
