package com.akucun.account.proxy.common.utils;

import com.akucun.account.proxy.common.exception.AccountProxyException;

import java.math.BigDecimal;

/**
 * @Author: silei
 * @Date: 2020/7/17
 * @desc:
 */
public class AmountUtils {

    /**
     * 金额为分的格式
     */
    public static final String CURRENCY_FEN_REGEX = "-?[0-9]+";
    public static final String CURRENCY_YUAN_REGEX = "^(([0-9]|([1-9][0-9]{0,9}))((.[0-9]{1,2})?))$";

    /**
     * 将分为单位的转换为元 （除100）
     * @param amount
     * @return
     * @throws Exception
     */
    public static String fen2yuan(String amount) {
        if (!amount.matches(CURRENCY_FEN_REGEX)) {
            throw new AccountProxyException("金额格式错误|" + amount);
        }
        return BigDecimal.valueOf(Long.parseLong(amount)).divide(new BigDecimal(100)).toString();
    }

    /**
     * 将元为单位的参数转换为分 , 只对小数点前2位支持
     * @param amount
     * @return
     * @throws Exception
     */
    public static Long yuan2fen(String amount) {
        if (!amount.matches(CURRENCY_YUAN_REGEX)) {
            throw new AccountProxyException("金额格式错误|" + amount);
        }
        BigDecimal fenBd = new BigDecimal(amount).multiply(new BigDecimal(100));
        return fenBd.longValue();
    }

}
