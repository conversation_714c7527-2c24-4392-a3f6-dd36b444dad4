package com.akucun.account.proxy.common.help;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.UUID;

import org.apache.log4j.Logger;


public class StringHelper {
	private static Logger logger = Logger.getLogger(StringHelper.class);
	public static String null2String(Object strIn) {
		return strIn == null ? "" : strIn.toString();
	}
	
	public static boolean isEmpty(Object str) {
		return str == null || str.toString().trim().length()==0;
	}
	
	public static boolean getBoolean(Object obj) {
		return obj==null?false:parseBoolean(obj.toString());
	}
	
	public static boolean parseBoolean(String param) {
		if (isEmpty(param)) {
			return false;
		}
		switch (param.charAt(0)) {
		case '1':
		case 'y':
		case 'Y':
		case 't':
		case 'T':
			return true;
		}
		return false;
	}

	public static boolean isNumber(Object o){
		boolean ret=false;
		if(o==null) return ret;
		return o.toString().trim().matches("^-?\\d+[\\.\\d]*$");
	}
	
	public static double obj2double(Object val){
		return obj2double(val, 0D);
	}

	public static double obj2double(Object val,double defaultVal){
		double d1=defaultVal;
		if(val==null) return d1;
		if(val instanceof Number){//需要确定是否number不能直接用double判断，否则int,short,float会转换异常
			return ((Number)val).doubleValue();
		}
		try{
			if(!isNumber(val)) return d1;
			d1=Double.parseDouble(val.toString().trim());
		}catch(Exception e){logger.warn("转换{"+val.toString()+"}为double时异常!");d1=defaultVal;}
		return d1;
	}
	
	public static long obj2long(Object obj){
		return obj2long(obj, 0L);
	}
	
	public static long obj2long(Object obj,long defval){
		long ret=defval;
		if(isEmpty(obj)) return ret;
		if(obj instanceof Number){
			ret=Long.parseLong(obj.toString());
		}else{
			try{
				if(isNumber(obj)) ret=(long)obj2double(obj);
				else ret=Long.parseLong(obj.toString().trim());
			}catch(Exception e){
				String errMsg= "object2long("+obj+")转换为long型异常!";
				if(logger.isDebugEnabled()) logger.warn(errMsg,e);
				else logger.warn(errMsg,e);
				ret=defval;
			}
		}
		return ret;
	}
	
	/**
	 * jstr中的JS关键字符转义，以便直接输出为js脚本内容。
	 * @param jstr as String
	 * @return String //a="a\\bc";转换后 a=\"a\\\\bc\"
 	 */
	public static String filterJString(String jstr){
		jstr=jstr.replaceAll("\\\\","\\\\\\\\");
		jstr=jstr.replaceAll("\"","\\\\\"");
		return jstr;
	}
	
	public static String filterJString2(String jstr){
		if(jstr==null)
			return "";
		jstr=jstr.replaceAll("\\\\","/");
		jstr=jstr.replaceAll("'", "");
		jstr=jstr.replaceAll("\"", "");
		return jstr;
	}
	
	/**
	 * 如果date1为null,今天的一年后的日期
	 * @param date1 as java.util.Date 
	 * @return String //yyyy-MM-dd
	 */
	public static String getNextYear(Date date1){
		Date date=new   Date();//取时间
		System.out.println(date.toString());
		    Calendar   calendar   =   new   GregorianCalendar(); 
		    calendar.setTime(date); 
		    calendar.add(calendar.YEAR, 1);//把日期往后增加一年.整数往后推,负数往前移动
		    date=calendar.getTime();
		SimpleDateFormat format=new SimpleDateFormat("yyyy-MM-dd");
		//}catch(ParseException pe){System.out.println("DateHelper.getShortDate解析异常:"+pe.toString());}
		return format.format(date);
	}
	/**
	 * 如果date1为null,今天的一年后的日期
	 * @param date1 as java.util.Date 
	 * @return String //yyyy-MM-dd
	 */
	public static String getNextYear(String formatStr){
		Date date=new   Date();//取时间
		System.out.println(date.toString());
		Calendar   calendar   =   new   GregorianCalendar(); 
		calendar.setTime(date); 
		calendar.add(calendar.YEAR, 1);//把日期往后增加一年.整数往后推,负数往前移动
		date=calendar.getTime();
		SimpleDateFormat format=new SimpleDateFormat(formatStr);
		//}catch(ParseException pe){System.out.println("DateHelper.getShortDate解析异常:"+pe.toString());}
		return format.format(date);
	}
    /**
     * 如果date1为null,返回今天的日期
     * @param date1 as java.util.Date 
     * @return String //yyyy-MM-dd
     */
    public static String getShortDate(Date date1){
    	String strDate=null;
    	if(date1==null) date1=Calendar.getInstance().getTime();
		//try{
			SimpleDateFormat format=new SimpleDateFormat("yyyy-MM-dd");
			strDate=format.format(date1);
		//}catch(ParseException pe){System.out.println("DateHelper.getShortDate解析异常:"+pe.toString());}
		return strDate;
    }
    /** 
     * 获取任意时间的上一个月 
     * 描述:<描述函数实现的功能>. 时间格式为yyyy-MM
     * @param repeatDate 
     * @return 
     */  
    public static String getLastMonth(String repeatDate) {  
        String lastMonth = "";  
        Calendar cal = Calendar.getInstance();  
        SimpleDateFormat dft = new SimpleDateFormat("yyyy-MM");  
        int year = Integer.parseInt(repeatDate.substring(0, 4));  
        String monthsString = repeatDate.substring(5, 7);  
        int month;  
        if ("0".equals(monthsString.substring(0, 1))) {  
            month = Integer.parseInt(monthsString.substring(1, 2));  
        } else {  
            month = Integer.parseInt(monthsString.substring(0, 2));  
        }  
        cal.set(year,month-2,Calendar.DATE);  
        lastMonth = dft.format(cal.getTime());  
        return lastMonth;  
    }  
    /**
     * 如果date1为null,返回当月最后一天日期
     * @param date1 as java.util.Date 
     * @return String //yyyy-MM-dd
     */
    public static String getLastDate(){
    	 Calendar calendar = Calendar.getInstance();
    	 calendar.setTime(new Date());
    	 calendar.set(Calendar.DATE, calendar.getActualMaximum(Calendar.DATE));
    	 DateFormat format = new SimpleDateFormat("yyyy-MM-dd");
		return format.format(calendar.getTime());
    	
    }
    /**
     * 如果date1为null,返回今天的日期
     * @param date1 as java.util.Date 
     * @return String //yyyy-MM-dd HH:mm:ss
     */
    public static String getLongDate(Date date1){
    	String strDate=null;
    	if(date1==null) date1=Calendar.getInstance().getTime();
		//try{
			SimpleDateFormat format=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			strDate=format.format(date1);
		//}catch(ParseException pe){System.out.println("DateHelper.getShortDate解析异常:"+pe.toString());}
		return strDate;
    }
    
    /**
     * 如果date1为null,返回今天的日期
     * @param  String //yyyy-MM-dd HH:mm:ss
     * @return date1 as java.util.Date
     */
    public static Date parseLongDate(String strDate){
    	Date date1 = null;
		try{
			SimpleDateFormat format=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			date1 = format.parse(strDate);
		}catch(ParseException pe){System.out.println("parseLongDate解析异常:"+pe.toString());}
		return date1;
    }
    
    /**
     * 如果date1为null,返回今天的日期
     * @param  String //yyyy-MM-dd 
     * @return  date1 as java.util.Date
     */
    public static Date parseShortDate(String strDate){
    	Date date1 = null;
		try{
			SimpleDateFormat format=new SimpleDateFormat("yyyy-MM-dd");
			date1 = format.parse(strDate);
		}catch(ParseException pe){System.out.println("parseShortDate解析异常:"+pe.toString());}
		return date1;
    }
    
	public static boolean isID(Object id) {
		if(id==null) return false;
		String tmp=id.toString().trim();
		return (tmp.length()==32 && tmp.matches("[a-z0-9]{32}"));
	}
	
	public static String getNowDate() {
		SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd");
		Date d=new Date();
		return sdf.format(d);
	}
	public static String getyearMonth() {
		SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM");
		Date d=new Date();
		return sdf.format(d);
	}

	public static String getNowTime() {
		SimpleDateFormat sdf=new SimpleDateFormat("HH:mm:ss");
		Date d=new Date();
		return sdf.format(d);
	}
	public static String getDate() {
		SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Date d=new Date();
		return sdf.format(d);
	}
	public static int stringToInt(String s) {
		return Integer.parseInt(s);
	}
	public static String uuid() {
		return UUID.randomUUID().toString().replace("-", "");
	}
	
	public static void main(String[] args) {
		System.out.println(uuid());
	}
}
