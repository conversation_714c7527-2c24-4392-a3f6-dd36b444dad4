package com.akucun.account.proxy.common.enums;

public enum BizTypeEnum {
	
	ACCOUNT_CENTER_REGISTER("ACCOUNT_CENTER_REGISTER","开账户中户"),
	REGISTER_BINDCARD("REGISTER_BINDCARD","开平安账户"),
    ;

    private String value;
    private String desc;

    BizTypeEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return this.value;
    }

    public String getDesc() {
        return this.desc;
    }

}
