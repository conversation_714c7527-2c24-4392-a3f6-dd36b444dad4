package com.akucun.account.proxy.common.enums;

import com.akucun.fps.common.entity.Result;
import com.akucun.fps.common.entity.ResultList;

public enum ErrorCodeConstants {

    /**
     * AccountSequenceManager Begin
     **/
    ACCORE_0(0, "AccountSequenceManager.init", "machineCode不能为空"),
    ACCORE_1(1, "AccountSequenceManager.init", "machineCode为两位"),
    ACCORE_2(2, "AccountSequenceManager.init", "machineCode转换异常"),
    /** AccountSequenceManager End **/

    /**
     * RecPaySchduleProcess Begin
     **/
    ACCORE_100000(100000, "RecPaySchduleProcess.createRecPay", "账户信息不存在"),
    ACCORE_100001(100001, "RecPaySchduleProcess.createRecPay", "支付方式错误"),
    ACCORE_100002(100002, "RecPaySchduleProcess.createRecPay", "收付款单生成异常"),
    ACCORE_100003(100003, "RecPaySchduleProcess.createRecPay", "收付款单，出纳，异常表处理异常"),
    ACCORE_100004(100004, "RecPaySchduleProcess.createRecPay", "生成收付款单异常"),
    ACCORE_100005(100005, "RecPaySchduleProcess.createRecPay", "收付款重复请求"),
    ACCORE_100006(100006, "RecPaySchduleProcess.createRecPay", "生成出纳账户表异常"),
    ACCORE_100007(100007, "RecPaySchduleProcess.createRecPay", "更新出纳账户金额异常"),
    ACCORE_100008(100008, "RecPaySchduleProcess.createRecPay", "插入出纳账户明细表异常"),
    ACCORE_100009(100009, "RecPaySchduleProcess.createRecPay", "插入核销单表异常"),
    ACCORE_100010(100010, "RecPaySchduleProcess.createRecPay", "插入异常表异常"),
    ACCORE_100011(100011, "RecPaySchduleProcess.redoWarnVerify", "插入核销单表异常"),
    ACCORE_100012(100012, "RecPaySchduleProcess.redoWarnVerify", "插入出纳账户明细表异常"),
    ACCORE_100013(100013, "RecPaySchduleProcess.redoWarnVerify", "记录表删除失败"),
    ACCORE_100014(100014, "RecPaySchduleProcess.redoWarnVerify", "记录表核销重试异常"),
    ACCORE_100015(100015, "RecPaySchduleProcess.redoWarnVerify", "更新告警信息失败"),
    /** RecPaySchduleProcess End **/


    /**
     * VerifySchduleProcess Begin
     **/
    ACCORE_100100(100100, "VerifySchduleProcess.createVerify", "查询流水信息异常"),
    ACCORE_100101(100101, "VerifySchduleProcess.createVerify", "核销单插入异常"),
    ACCORE_100102(100102, "VerifySchduleProcess.createVerify", "调用同步核销接口异常"),
    ACCORE_100103(100103, "VerifySchduleProcess.createVerify", "批量核销事物执行异常"),
    ACCORE_100104(100104, "VerifySchduleProcess.createVerify", "批量核销务异常"),
    ACCORE_100105(100105, "VerifySchduleProcess.createVerify", "批量核销流水子类型未设置值"),
    /** VerifySchduleProcess End **/

    /**
     * InsertRecPaySchduleAction Begin
     **/
    ACCORE_100200(100200, "InsertRecPaySchduleAction", "收付款单处理逻辑异常"),
    ACCORE_100201(100201, "WarnSchduleAction", "记录表处理异常"),
    ACCORE_100202(100202, "FanVerifySchduleAction", "反写流水核销金额异常"),
    ACCORE_100203(100203, "WarnSchduleAction", "任务查询收款单不存在"),
    ACCORE_100204(100204, "WarnSchduleAction", "任务查询付款单不存在"),
    ACCORE_100205(100205, "WithdrawSchduleAction", "异步调用提现任异常"),
    ACCORE_100206(100206, "AssetBillSchedule", "平安自动转账任务异常"),
    /** InsertRecPaySchduleAction End **/

    /**
     * InsertVerifySchduleAction Begin
     **/
    ACCORE_100300(100300, "InsertVerifySchduleAction", "核销插入任务执行异常"),
    /** InsertVerifySchduleAction End **/


    /**
     * RemoveDuplicateSchduleAction Begin
     **/
    ACCORE_100400(100400, "RemoveDuplicateSchduleAction", "清理去重表任务执行异常"),
    /** RemoveDuplicateSchduleAction End **/

    /**
     * ReceiveShareVerifyConsumer Begin
     **/
    ACCORE_100500(100500, "ReceiveShareVerifyConsumer", "MQ处理核销异常"),
    /** ReceiveShareVerifyConsumer End **/


    /**
     * AccountDetailServiceImpl Begin
     **/
    ACCORE_100600(100600, "AccountDetailServiceImpl.select", "查询账单明细异常"),
    ACCORE_100601(100601, "AccountDetailServiceImpl.insert", "新增账单明细参数不可为空"),
    ACCORE_100602(100602, "AccountDetailServiceImpl.insert", "新增账户账本异常"),
    ACCORE_100603(100603, "AccountDetailServiceImpl.select", "查询余额明细参数缺失"),
    ACCORE_100604(100604, "AccountDetailServiceImpl.select", "查询余额明细未获取账号信息"),
    /** AccountDetailServiceImpl End **/

    /**
     * AccountSchduleClientImpl Begin
     **/
    ACCORE_100700(100700, "AccountSchduleClientImpl.sendVerifyMessage", "对账请求参数不可为空"),
    ACCORE_100701(100701, "AccountSchduleClientImpl.sendVerifyMessage", "核销任务请求插入异常"),
    ACCORE_100702(100702, "AccountSchduleClientImpl.sendRecPayMessage", "收付款单请求参数不可为空"),
    ACCORE_100703(100703, "AccountSchduleClientImpl.sendRecPayMessage", "收付款单任务请求插入异常"),
    ACCORE_100704(100704, "AccountSchduleClientImpl.selectPage", "分页任务查询异常"),
    ACCORE_100705(100705, "AccountSchduleClientImpl.updateSchduleBySelected", "更新选择任务异常"),
    ACCORE_100706(100706, "AccountSchduleClientImpl.delaySchduleBySelected", "迁移选择任务异常"),
    ACCORE_100707(100707, "AccountSchduleClientImpl.sendRecPayMessage", "收付款插入异步任务事物执行异常"),
    ACCORE_100708(100708, "AccountSchduleClientImpl.sendRecPayMessage", "收付款插入异步任务出现并发"),
    /** AccountSchduleClientImpl End **/

    /**
     * AccountServiceImpl Begin
     **/
    ACCORE_100800(100800, "AccountServiceImpl.select", "查询参数不能为空"),
    ACCORE_100801(100801, "AccountServiceImpl.select", "查询账户账本异常"),
    ACCORE_100802(100802, "AccountServiceImpl.updateAmountByCode", "更新余额参数不能为空"),
    ACCORE_100803(100803, "AccountServiceImpl.updateAmountByCode", "执行更新余额结果异常"),
    ACCORE_100804(100804, "AccountServiceImpl.updateStatusByCode", "新增账本参数不可为空"),
    ACCORE_100805(100805, "AccountServiceImpl.updateStatusByCode", "新增账户账本异常"),
    ACCORE_100806(100806, "AccountServiceImpl.insert", "客户编码不可为空"),
    ACCORE_100807(100807, "AccountServiceImpl.insert", "查询客户信息异常"),
    ACCORE_100808(100808, "AccountServiceImpl.selectByCustomerCode", "查询账本编码或账户类型不可为空"),
    ACCORE_100809(100809, "AccountServiceImpl.selectByCustomerCode", "查询用户信息异常"),
    ACCORE_100810(100810, "AccountServiceImpl.changeAmount", "余额变更参数缺失，不可进行交易"),
    ACCORE_100811(100811, "AccountServiceImpl.changeAmount", "用户账户不存在，无法使用余额进行支付"),
    ACCORE_100812(100812, "AccountServiceImpl.changeAmount", "余额不足,请充值或使用其他方式支付"),
    ACCORE_100813(100813, "AccountServiceImpl.changeAmount", "余额不足,无法提现"),
    ACCORE_100814(100814, "AccountServiceImpl.changeAmount", "类型错误，不可进行账本操作"),
    ACCORE_100815(100815, "AccountServiceImpl.changeAmount", "余额操作异常"),
    ACCORE_100816(100816, "AccountServiceImpl.sendAccountMessage", "新增账本参数缺少，请核实后重试"),
    ACCORE_100817(100817, "AccountServiceImpl.sendAccountMessage", "新增账本客户类型错误，请核实后重试"),
    ACCORE_100818(100818, "AccountServiceImpl.sendAccountMessage", "新增账本异常"),
    ACCORE_100819(100819, "AccountServiceImpl.changeAmount", "账本操作请求重复"),
    ACCORE_100820(100820, "AccountServiceImpl.sendAccountMessage", "新增账本事物执行异常"),
    ACCORE_100821(100821, "AccountServiceImpl.sendAccountMessage", "批量新增账号失败"),
    ACCORE_100822(100822, "AccountServiceImpl.changeAccountStatus", "账号状态更改参数缺失"),
    ACCORE_100824(100824, "AccountServiceImpl.changeAccountStatus", "账号状态操作异常"),
    ACCORE_100825(100825, "AccountServiceImpl.changeAccountStatus", "状态操作参数错误请核实"),
    ACCORE_100826(100826, "AccountServiceImpl.changeAccountStatus", "更新账号状态失败"),
    ACCORE_100827(100827, "AccountServiceImpl.changeAccountStatus", "更新账号状态事物执行异常"),
    ACCORE_100828(100828, "AccountServiceImpl.changeAmount", "金额不足，不允许操作"),
    ACCORE_100829(100829, "AccountServiceImpl.changeAmount", "清分增加付款单失败"),
    ACCORE_100830(100830, "AccountServiceImpl.withdrawAmount", "提现参数缺失请核实"),
    ACCORE_100831(100831, "AccountServiceImpl.withdrawAmount", "未查询到相应客户账户信息"),
    ACCORE_100832(100832, "AccountServiceImpl.withdrawAmount", "提现失败，冻结金额不足"),
    ACCORE_100833(100833, "AccountServiceImpl.withdrawAmount", "同一提现单据不可多次审核"),
    ACCORE_100834(100834, "AccountServiceImpl.withdrawAmount", "提现操作余额账户失败"),
    ACCORE_100835(100835, "AccountServiceImpl.withdrawAmount", "提现操作事物提交异常"),
    ACCORE_100836(100836, "AccountServiceImpl.notifyWithdrawAmount", "提现结果通知参数缺失"),
    ACCORE_100837(100837, "AccountServiceImpl.notifyWithdrawAmount", "提现通知未查询到账户信息"),
    ACCORE_100838(100838, "AccountServiceImpl.notifyWithdrawAmount", "提现通知出现并发"),
    ACCORE_100839(100839, "AccountServiceImpl.notifyWithdrawAmount", "提现通知账户操作异常"),
    ACCORE_100840(100840, "AccountServiceImpl.notifyWithdrawAmount", "提现通知事物提交异常"),
    ACCORE_100841(100841, "AccountServiceImpl.notifyWithdrawAmount", "提现通知异常"),
    ACCORE_100842(100842, "AccountServiceImpl.withdrawAmount", "提现操作异常"),
    ACCORE_100843(100843, "AccountServiceImpl.withdrawAmount", "审核通过更改审核状态异常"),
    ACCORE_100844(100844, "AccountServiceImpl.refuseWithdrawAmount", "审核拒绝冻结余额不足"),
    ACCORE_100845(100845, "AccountServiceImpl.refuseWithdrawAmount", "同一提现单据不可多次拒绝"),
    ACCORE_100846(100846, "AccountServiceImpl.refuseWithdrawAmount", "审核拒绝更新失败"),
    ACCORE_100847(100847, "AccountServiceImpl.refuseWithdrawAmount", "审核拒绝更新余额失败"),
    ACCORE_100848(100848, "AccountServiceImpl.refuseWithdrawAmount", "审核拒绝事物执行异常"),
    ACCORE_100849(100849, "AccountServiceImpl.changeDrawAndAmount", "自动分账参数缺失，不可进行交易"),
    ACCORE_100850(100850, "AccountServiceImpl.changeDrawAndAmount", "自动分账参数缺失，未查询到账户信息"),
    ACCORE_100851(100851, "AccountServiceImpl.changeDrawAndAmount", "非转账类型不可交易"),
    ACCORE_100852(100852, "AccountServiceImpl.changeDrawAndAmount", "余额不足"),
    ACCORE_100853(100853, "AccountServiceImpl.changeDrawAndAmount", "不可重复请求"),
    ACCORE_100854(100854, "AccountServiceImpl.changeDrawAndAmount", "更改账户失败"),
    ACCORE_100855(100855, "AccountServiceImpl.changeDrawAndAmount", "付款单添加失败"),
    ACCORE_100856(100856, "AccountServiceImpl.changeDrawAndAmount", "事物执行异常"),
    ACCORE_100857(100857, "AccountServiceImpl.changeDrawAndAmount", "自动分账异常"),
    ACCORE_100858(100858, "AccountServiceImpl.changeDrawAndAmount", "预付款抵扣账户操作异常"),
    ACCORE_100859(100859, "AccountServiceImpl.changeDrawAndAmount", "预付款抵扣账户明细操作异常"),
    ACCORE_100860(100860, "AccountServiceImpl.notifyWithdrawAmount", "更改提现结果异常"),
    ACCORE_100861(100861, "AccountServiceImpl.notifyWithdrawAmount", "微信提现通知账户操作异常"),
    ACCORE_100862(100862, "AccountServiceImpl.notifyWithdrawAmount", "微信提现通知出现并发"),
    ACCORE_100863(100863, "AccountServiceImpl.notifyWithdrawAmount", "微信提现更改提现结果异常"),
    ACCORE_100864(100864, "AccountServiceImpl.notifyWithdrawAmount", "微信提现通知事物提交异常"),
    ACCORE_100865(100865, "AccountServiceImpl.notifyWithdrawAmount", "微信提现通知异常"),
    /** AccountServiceImpl End **/

    /**
     * DuplicateRemovalServiceImpl Begin
     **/
    ACCORE_100900(100900, "DuplicateRemovalServiceImpl.checkDuplicate", "插入去重表异常"),
    ACCORE_100901(100901, "DuplicateRemovalServiceImpl.addSchedule", "添加删除去重任务异常"),
    ACCORE_100902(100902, "DuplicateRemovalServiceImpl.deleteDuplicate", "删除去重表数据异常"),
    ACCORE_100903(100903, "DuplicateRemovalServiceImpl.checkDuplicate", "插入去重表出现并发"),
    /** DuplicateRemovalServiceImpl End **/


    /**
     * RecPayServiceImpl Begin
     **/
    ACCORE_101000(101000, "RecPayServiceImpl.insert", "新增收付款参数不可为空"),
    ACCORE_101001(101001, "RecPayServiceImpl.insert", "新增收付款单异常"),
    ACCORE_101002(101002, "RecPayServiceImpl.selectRecPage", "收款单查询参数不能为空"),
    ACCORE_101003(101003, "RecPayServiceImpl.selectRecPage", "收款单查询异常"),
    ACCORE_101004(101004, "RecPayServiceImpl.selectPayPage", "付款单查询参数不能为空"),
    ACCORE_101005(101005, "RecPayServiceImpl.selectPayPage", "付款单查询异常"),
    ACCORE_101006(101006, "RecPayServiceImpl.insert", "新增收付款单类型不存在"),
    /** RecPayServiceImpl End **/


    /**
     * VerifyServiceImpl Begin
     **/
    ACCORE_101100(101100, "VerifyServiceImpl.insert", "核销单新增参数不可为空"),
    ACCORE_101101(101101, "VerifyServiceImpl.insert", "核销单新增异常"),
    ACCORE_101102(101102, "VerifyServiceImpl.selectPage", "分页查询核销单异常"),
    ACCORE_101103(101103, "VerifyServiceImpl.updateStatusByCode", "修改核销单参数不能为空"),
    ACCORE_101104(101104, "VerifyServiceImpl.updateStatusByCode", "更新核销单异常"),
    /** VerifyServiceImpl End **/

    /**
     * VerifyMqProcess Begin
     **/
    ACCORE_101200(101200, "VerifyMqProcess.createVerify", "MQ消费未查询到流水信息"),
    ACCORE_101201(101201, "VerifyMqProcess.createVerify", "MQ消费生成核销单事物执行异常"),
    ACCORE_101202(101202, "VerifyMqProcess.createVerify", "MQ消费生成核销单异常"),
    ACCORE_101203(101203, "VerifyMqProcess.createVerify", "MQ查询流水数据异常"),
    /** VerifyMqProcess End **/



    /**
     * CashierServiceImpl Begin
     **/
    ACCORE_101300(101300, "CashierServiceImpl.selectCashier", "查询出纳账户表信息异常"),
    ACCORE_101301(101301, "CashierServiceImpl.insertCashier", "出纳表插入参数为空"),
    ACCORE_101302(101302, "CashierServiceImpl.insertCashier", "出纳表插入失败"),
    ACCORE_101303(101303, "CashierServiceImpl.insertCashier", "出纳表插入异常"),
    ACCORE_101304(101304, "CashierServiceImpl.insertCashierDetail", "出纳表明细插入异常"),
    ACCORE_101305(101305, "CashierServiceImpl.updateCashierAmount", "更新出纳表金额异常"),
    ACCORE_101306(101306, "CashierServiceImpl.select", "分页查询出纳表异常"),
    ACCORE_101307(101307, "CashierServiceImpl.selectCashierDetail", "分页查询出纳明细表异常"),
    /** CashierServiceImpl End **/


    /**
     * WarnServiceImpl Begin
     **/
    ACCORE_101400(101400, "WarnServiceImpl.selectPage", "查询预警表信息异常"),
    ACCORE_101401(101401, "WarnServiceImpl.manualBlend", "手动勾兑核销异常"),
    ACCORE_101402(101402, "WarnServiceImpl.manualBlend", "手动勾兑核销收付款单号为空"),
    ACCORE_101403(101403, "WarnServiceImpl.clearWarn", "清理参数不可为空"),
    ACCORE_101404(101404, "WarnServiceImpl.clearWarn", "清理异常"),
    ACCORE_101405(101405, "WarnServiceImpl.clearWarn", "清理失败"),
    ACCORE_101406(101406, "WarnServiceImpl.clearWarn", "清理事物提交异常"),
    /** WarnServiceImpl End **/


    /**
     * WithdrawServiceImpl Begin
     **/
    ACCORE_101500(101500, "AccountServiceImpl.applyWithdraw", "申请提现参数缺失"),
    ACCORE_101501(101501, "AccountServiceImpl.applyWithdraw", "申请提现异常"),
    ACCORE_101502(101502, "AccountServiceImpl.applyWithdraw", "提现账户不存在，请稍后重试"),
    ACCORE_101503(101503, "AccountServiceImpl.applyWithdraw", "账户余额不足，请核实"),
    ACCORE_101504(101504, "AccountServiceImpl.applyWithdraw", "调用加密服务异常"),
    ACCORE_101505(101505, "AccountServiceImpl.applyWithdraw", "调用加密服务返回状态失败"),
    ACCORE_101506(101506, "AccountServiceImpl.applyWithdraw", "提现申请事物执行异常"),
    ACCORE_101507(101506, "AccountServiceImpl.applyWithdraw", "插入提现申请表失败"),
    ACCORE_101508(101506, "AccountServiceImpl.applyWithdraw", "提现申请更改账户余额失败"),
    ACCORE_101509(101509, "AccountServiceImpl.selectPage", "查询提现记录参数缺失"),
    ACCORE_101510(101510, "AccountServiceImpl.selectPage", "查询提现记录异常"),
    ACCORE_101511(101511, "AccountServiceImpl.checkWithdraw", "提现审核参数为空"),
    ACCORE_101512(101512, "AccountServiceImpl.checkWithdraw", "未查询到相关提现记录信息"),
    ACCORE_101513(101513, "AccountServiceImpl.checkWithdraw", "提现审核未查询到相关账户信息"),
    ACCORE_101514(101514, "AccountServiceImpl.selectPage", "账号解密异常"),
    ACCORE_101515(101515, "AccountServiceImpl.checkWithdraw", "提现审核账号解密异常"),
    ACCORE_101516(101516, "AccountServiceImpl.checkWithdraw", "当前状态不允许操作"),
    ACCORE_101517(101517, "AccountServiceImpl.checkWithdraw", "提现审核操作异常"),
    ACCORE_101518(101518, "AccountServiceImpl.applyWithdraw", "提现金额需大于0"),
    ACCORE_101519(101519, "AccountServiceImpl.applyWithdraw", "您的银行卡还未绑定，请确认绑卡后再进行提现"),
    ACCORE_101520(101520, "AccountServiceImpl.wechatApplyWithdraw", "微信申请提现参数缺失"),
    ACCORE_101521(101521, "AccountServiceImpl.wechatApplyWithdraw", "微信提现申请入表失败"),
    ACCORE_101522(101522, "AccountServiceImpl.wechatApplyWithdraw", "微信提现申请更改账户余额失败"),
    ACCORE_101523(101523, "AccountServiceImpl.wechatApplyWithdraw", "微信提现申请事物执行异常"),

    ACCORE_101524(101524, "AccountServiceImpl.applyWithdraw", "本月累计提现达到阈值需升级到临时税务登记"),
    ACCORE_101525(101525, "AccountServiceImpl.applyWithdraw", "本月累计提现达到阈值需升级到个体工商户"),
    ACCORE_101526(101526, "AccountServiceImpl.applyWithdraw", "本月累计提现达到阈值需升级到企业"),
    /** WithdrawServiceImpl End **/


    /**
     * CustomerCircleServiceImpl Begin
     **/
    ACCORE_101600(101600, "CustomerCircleServiceImpl.addCustomerCircle", "新增绑定关系参数缺失"),
    ACCORE_101601(101601, "CustomerCircleServiceImpl.addCustomerCircle", "不可对自身进行绑定"),
    ACCORE_101602(101602, "CustomerCircleServiceImpl.addCustomerCircle", "请先解绑后绑定"),
    ACCORE_101603(101603, "CustomerCircleServiceImpl.addCustomerCircle", "绑定插入数据失败"),
    ACCORE_101604(101604, "CustomerCircleServiceImpl.addCustomerCircle", "绑定客户关系异常"),
    ACCORE_101605(101605, "CustomerCircleServiceImpl.addCustomerCircle", "分页查询绑定信息异常"),
    ACCORE_101606(101606, "CustomerCircleServiceImpl.cancelCustomerCircle", "取消绑定关系参数为空"),
    ACCORE_101607(101607, "CustomerCircleServiceImpl.cancelCustomerCircle", "取消绑定关系失败"),
    ACCORE_101608(101608, "CustomerCircleServiceImpl.cancelCustomerCircle", "取消绑定关系事务执行异常"),
    ACCORE_101609(101609, "CustomerCircleServiceImpl.cancelCustomerCircle", "取消绑定关系异常"),
    /** CustomerCircleServiceImpl End **/


    /**
     * AdvanceServiceImpl Begin
     **/
    ACCORE_101700(101700, "AdvanceServiceImpl.changeAmount", "预付款参数缺失"),
    ACCORE_101701(101701, "AdvanceServiceImpl.changeAmount", "未查询到银行账号信息,请核实结算账号是否存在"),
    ACCORE_101702(101702, "AdvanceServiceImpl.changeAmount", "预付款账号加密异常"),
    ACCORE_101703(101703, "AdvanceServiceImpl.changeAmount", "预付款账户操作失败"),
    ACCORE_101704(101704, "AdvanceServiceImpl.changeAmount", "预付款账户明细插入失败"),
    ACCORE_101705(101705, "AdvanceServiceImpl.changeAmount", "预付款事物执行异常"),
    ACCORE_101706(101706, "AdvanceServiceImpl.changeAmount", "预付款处理异常"),
    ACCORE_101707(101707, "AdvanceServiceImpl.selectAdvanceList", "预付款信息查询参数缺失"),
    ACCORE_101708(101708, "AdvanceServiceImpl.selectAdvanceList", "预付款查询异常"),
    ACCORE_101709(101709, "AdvanceServiceImpl.selectAdvanceDetailList", "预付款明细查询参数缺失"),
    ACCORE_101710(101710, "AdvanceServiceImpl.selectAdvanceDetailList", "预付款明细查询异常"),
    /** AdvanceServiceImpl End **/

    /**
     * AccountFillAmountServiceImpl Begin zhongshumin
     **/
    ACCORE_101800(101800, "AccountFillAmountServiceImpl.PaymentRegistrationByFillAmount", "不可重复请求"),
    ACCORE_101801(101801, "AccountFillAmountServiceImpl.PaymentRegistrationByFillAmount", "客户账户不存在"),
      /** AccountFillAmountServiceImpl End **/

    AUTHAPPLY_101900(101900, "", "税库银三方协议审核中，请审核完成后进行其他操作"),
    BINDCARD_101901(101901, "UPGRADE.PROCESSING", "账户变更处理中，请稍后重试"),

    /**
     * WithdrawTaxService Begin
     */
    ACCORE_101900(101900, "WithdrawTaxServiceImpl.sumWithdrawAndTaxFeeByDay", "参数不合法"),

    SYSTEMMAINTAIN_900000(900000, "SYSTEM.MAINTAIN", "系统维护中，请稍后重试"),

    SYSTEMERROR_999999(999999, "SYSTEM.ERROR", "系统错误"),
	
	ACCORE_101802(101802, "101802", "查询租户信息失败");
    /**
     * 错误code
     */
    private int errorCode;
    /**
     * 错误名称
     */
    private String errorName;
    /**
     * 错误信息
     */
    private String errorMessage;

    ErrorCodeConstants(int errorCode, String errorName, String errorMessage) {
        //错误编码
        this.errorCode = errorCode;
        //错误名称
        this.errorName = errorName;
        //错误详细信息
        this.errorMessage = errorMessage;
    }

    /**
     * 错误编码
     *
     * @return the errorCode
     */
    public int getErrorCode() {
        return errorCode;
    }

    /**
     * 错误编码
     *
     * @param errorCode the errorCode to set
     */
    public void setErrorCode(int errorCode) {
        this.errorCode = errorCode;
    }

    /**
     * 错误名称
     *
     * @return the errorName
     */
    public String getErrorName() {
        return errorName;
    }

    /**
     * 错误名称
     *
     * @param errorName the errorName to set
     */
    public void setErrorName(String errorName) {
        this.errorName = errorName;
    }

    /**
     * 错误详细信息
     *
     * @return the errorMessage
     */
    public String getErrorMessage() {
        return errorMessage;
    }

    /**
     * 错误详细信息
     *
     * @param errorMessage the errorMessage to set
     */
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    /**
     * 错误详细信息
     * getErrorMessage
     *
     * @param args
     * @return
     */
    public String getErrorMessage(Object... args) {
        return String.format(errorMessage, args);
    }

    /**
     * generateError
     *
     * @param result
     * @return
     */
    public void generateError(Result result) {
        result.setSuccess(Boolean.FALSE);
        result.setErrorCode(getErrorCode());
        result.setErrorName(getErrorName());
        result.setErrorMessage(getErrorMessage());
    }
    /**
     * generateListError
     *
     * @param resultList
     * @return
     */
    public void generateListError(ResultList resultList){
        resultList.setSuccess(Boolean.FALSE);
        resultList.setErrorCode(getErrorCode());
        resultList.setErrorName(getErrorName());
        resultList.setErrorMessage(getErrorMessage());
    }
}
