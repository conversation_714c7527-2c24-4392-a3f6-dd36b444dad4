package com.akucun.account.proxy.common.utils;

/**
 * 老奖励金账户交易类型转换
 */
public class BonusTradeTypeUtils {

    /**
     * 老奖励金交易类型转tradeType
     * @param reason
     * @return
     */
    public static String reason2Type(Integer reason) {
        switch (reason) {
            case 3:
                return "TRADE_TYPE_001";// 退货退款
            case 4:
                return "TRADE_TYPE_002";// 次品协商退款
            case 5:
                return "TRADE_TYPE_003";//运费返还
            case 10:
                return "TRADE_TYPE_004";//用户充值
            case 12:
                return "TRADE_TYPE_005";//奖励发放
            case 15:
                return "TRADE_TYPE_006";//补偿代购费
            case 100:
                return "TRADE_TYPE_008";//订单支付
            case 17:
                return "TRADE_TYPE_010";//金额冲正增加
            case 18:
                return "TRADE_TYPE_011";//金额冲正减少
            case 16:
                return "TRADE_TYPE_012";//用户注销
            case 20:
                return "TRADE_TYPE_015";//订单补偿
            case 21:
                return "TRADE_TYPE_016";// 售后津贴
            case 22:
                return "TRADE_TYPE_017";// 售后津贴充值
            case 23:
                return "TRADE_TYPE_018";// 屏蔽扣款
            case 24:
                return "TRADE_TYPE_019";// 屏蔽返还
            default:
                return null;
        }
    }

    /**
     * tradeType转老奖励金交易类型
     * @param tradeType
     * @return
     */
    public static Integer type2Reason(String tradeType) {
        switch (tradeType) {
            case "TRADE_TYPE_001":
                return 3;
            case "TRADE_TYPE_002":
                return 4;
            case "TRADE_TYPE_003":
                return 5;
            case "TRADE_TYPE_004":
                return 10;
            case "TRADE_TYPE_005":
                return 12;
            case "TRADE_TYPE_006":
                return 15;
            case "TRADE_TYPE_008":
                return 100;
            case "TRADE_TYPE_010":
                return 17;
            case "TRADE_TYPE_011":
                return 18;
            case "TRADE_TYPE_012":
                return 16;
            case "TRADE_TYPE_015":
                return 20;
            case "TRADE_TYPE_016":
                return 21;
            case "TRADE_TYPE_017":
                return 22;
            case "TRADE_TYPE_018":
                return 23;
            case "TRADE_TYPE_019":
                return 24;
            default:
                return 0;
        }
    }

}
