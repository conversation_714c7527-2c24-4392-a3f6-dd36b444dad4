package com.akucun.account.proxy.common.enums;

public enum MemberRegisterType {
	
	type1(1,"普通会员注册消息"),
	type2(2, "店主注册消息"),
	type3(3, "店长注册消息"),
    ;

    private Integer value;
    private String desc;

    MemberRegisterType(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return  this.value;
    }

    public String getDesc() {
        return this.desc;
    }

}
