package com.akucun.account.proxy.common.utils;

import com.akucun.account.proxy.facade.stub.enums.IdentifyEnum;

public class IdentifyUtils {

    public static IdentifyEnum convertIdTypeMember2Pingan(int memberIdType) {
        //会员身份证类型，0-身份证 1-港澳居民通行证 2-中国护照 3-企业的社会统一信用代码  4-外国护照 5-台湾居民通行证
        switch (memberIdType) {
            case 0:
                return IdentifyEnum.IDCARD;
            case 1:
            case 5:
                return IdentifyEnum.HONGKONG_MACAO_TAI_TRAVEL_PERMIT;
            case 2:
                return IdentifyEnum.CHINESE_PASSPORT;
            case 3:
                return IdentifyEnum.CREDIT_CODE;
            case 4:
                return IdentifyEnum.FOREIGN_PASSPORT;
            default:
                return null;
        }
    }

}
