package com.akucun.account.proxy.common.utils;

import org.apache.commons.lang3.StringUtils;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.Random;

/**
 * 审核工单编码生成工具类
 */
public class AuditNoUtils {

    private static final long DATA_CENTER_ID = 1l;
    private static final long SEQUENCE = 1l;
    private static final IdWorker idWorker;

    static {
        String workId = null;
        InetAddress ia;
        try {
            ia = InetAddress.getLocalHost();
            String ip = ia.getHostAddress();
            if(StringUtils.isNotBlank(ip)) {
                workId = ip.substring(ip.length() -1, ip.length());
            }
        } catch (UnknownHostException e) {
            e.printStackTrace();
        }
        if(StringUtils.isBlank(workId)) {
            Random r = new Random();
            workId = String.valueOf(r.nextInt(32));
        }
        idWorker = new IdWorker(Long.valueOf(workId), DATA_CENTER_ID, SEQUENCE);
    }

    public static String getNextId() {
        return Long.toString(idWorker.nextId());
    }
}
