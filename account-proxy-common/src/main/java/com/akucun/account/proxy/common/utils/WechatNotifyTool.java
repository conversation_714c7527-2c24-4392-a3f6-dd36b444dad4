package com.akucun.account.proxy.common.utils;

import com.alibaba.fastjson.JSONObject;
import com.mengxiang.base.common.log.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.EnvironmentAware;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

/**
 * @Author: silei
 * @Date: 2021/4/26
 * @desc:
 */

@Component
public class WechatNotifyTool implements EnvironmentAware {

    @Value("${wechat.notify.url:https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=2e7872a2-2475-4ef5-82e1-834cb513f3ed}")
    private String notifyUrl;

    @Autowired
    private ThreadPoolTaskExecutor executor;

    private Environment environment;

    /**
     * 发送企业微信通知信息
     *
     * @param message
     */
    public void sendNotifyMsg(String message) {
        try {
            String env = environment.getActiveProfiles()[0];
            JSONObject text = new JSONObject();
            text.put("content", "环境：" + env + ", 消息： " + message);
            JSONObject entity = new JSONObject();
            entity.put("msgtype", "text");
            entity.put("text", text);
            executor.execute(() -> HttpClient.doPost(notifyUrl, entity.toJSONString()));
        } catch (Exception e) {
            Logger.warn("WechatNotifyTool send msg exception:", e);
        }
    }

    public static void main(String[] args) {
        String url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=2e7872a2-2475-4ef5-82e1-834cb513f3ed";

        JSONObject text = new JSONObject();
        text.put("content", "消息测试111");
        JSONObject entity = new JSONObject();
        entity.put("msgtype", "text");
        entity.put("text", text);
        System.out.println(entity.toJSONString());
        HttpClient.doPost(url, entity.toJSONString());
    }


    @Override
    public void setEnvironment(Environment environment) {
        this.environment = environment;
    }
}
