package com.akucun.account.proxy.common.utils;

import com.akucun.account.proxy.facade.stub.enums.CustomerType;

/**
 * @Author: silei
 * @Date: 2021/1/13
 * @desc: 账户类型key转账户类型
 */
public class CustomerTypeUtils {


    public static String accountKey2Type(String accountKey){
        switch (accountKey){
            case "8D256656F0A9E0A959024F16A8C910B3":
                return CustomerType.NM.getName();
            case "6A55B6EA4B16E697ED8F30DE17AFBA34":
                return CustomerType.NMDL.getName();
            case "D310397BA71383DCD8208A5DD49F25C1":
                return CustomerType.DG.getName();
            case "AEC6DAA921F51796F05CC3AE025A04EC":
                return CustomerType.OP.getName();
            case "1B0224AA3F894DA4687F82D231D7F0CE":
                return CustomerType.SH.getName();
            case "CD1CB5580F8C4307E986FD77ED98DC1E":
                return CustomerType.AT.getName();
            default:
                return null;

        }

    }

}
