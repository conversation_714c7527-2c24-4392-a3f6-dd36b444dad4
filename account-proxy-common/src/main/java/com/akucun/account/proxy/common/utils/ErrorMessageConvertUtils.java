package com.akucun.account.proxy.common.utils;

import java.util.HashMap;
import java.util.Map;

/**
 * 平安提现错误信息替换
 */
public class ErrorMessageConvertUtils {

    private static final Map<String, String> map = new HashMap<>(500);

    static {
        map.put("响应数据为空", "连接银行异常，请咨询银行客服");
        map.put("接收清算行状态无效暂不能做业务！", "连接银行异常，请咨询银行客服");
        map.put(":签名验签失败,源数据和签名数据验证失败！", "签名验证失败，请重试");
        map.put("MONEY_LIMIT_已达到该商户单日付款金额上限.", "已达到该商户单日付款金额上限，请明日重试");
        map.put("[CE2416]输入的转出账户户名[平安银行电子商务交易资金待清算专户（丝友）]不正确", "输入的转出账户户名不正确，请重新输入");
        map.put("[ME4029][对私负债账户]-[1600195230692100RMB]存在[未上传身份影像]状态，不允许进行", "未上传身份影像，请先上传");
        map.put("[ME4029][对私负债账户]-[1600187359555100RMB]存在[未上传身份影像]状态，不允许进行", "未上传身份影像，请先上传");
        map.put("[ME4096]您的账户因证件到期60天或未留存已被中止业务，您可在口袋银行APP‘首页-更多", "您的账户因证件到期60天或未留存已被中止业务，您可在口袋银行APP‘首页-更多中查看");
        map.put("[ME4080]您的账户因证件到期60天或未留存已被中止业务，您可在口袋银行APP‘首页-更多", "您的账户因证件到期60天或未留存已被中止业务，您可在口袋银行APP‘首页-更多中查看");
        map.put("[ME4099]您的账户因开户半年无交易已被暂停非柜面业务,您可在口袋银行APP‘首页-更多-", "您的账户因开户半年无交易已被暂停非柜面业务,您可在口袋银行APP‘首页-更多-中查看");
        map.put("[RE1342]输入的卡号[6230580000171032811]不存在或者已经销卡", "输入的卡号不存在或者已经销卡，请咨询银行客服");
        map.put("[RE1693]未面核状态不允许非绑定转入", "未面核状态不允许非绑定转入，请先面核或咨询银行客服");
        map.put("中国光大银行系统已关闭，暂停中国光大银行业务办理", "中国光大银行系统已关闭，暂停中国光大银行业务办理，请咨询银行客服");
        map.put("中国农业银行股份有限公司系统已关闭，暂停中国农业银行股份有限公司业务办理", "中国农业银行股份有限公司系统已关闭，暂停中国农业银行股份有限公司业务办理，请咨询银行客服");
        map.put("中国建设银行股份有限公司总行系统已关闭，暂停中国建设银行股份有限公司总行业务办理", "中国建设银行股份有限公司总行系统已关闭，暂停中国建设银行股份有限公司总行业务办理，请咨询银行客服");
        map.put("中国银行总行系统已关闭，暂停中国银行总行业务办理", "中国银行总行系统已关闭，暂停中国银行总行业务办理");
        map.put("九江银行股份有限公司系统已关闭，暂停九江银行股份有限公司业务办理", "九江银行股份有限公司系统已关闭，暂停九江银行股份有限公司业务办理，请咨询银行客服");
        map.put("提现失败，已达到连接上限40，无法接收新的连接请求!", "操作失败，系统繁忙请稍后重试");
        map.put("提现失败，已达到连接上限[40]，无法接收新的连接请求!", "操作失败，系统繁忙请稍后重试");
        map.put("提现失败，行内处理失败,失败原因:交易失败，系统繁忙请稍后重试", "操作失败，系统繁忙请稍后重试");
        map.put("提现失败，行内处理失败,失败原因:后端324079系统超过了流量控制的最大值", "操作失败，系统繁忙请稍后重试");
        map.put("提现失败，锁等待失败[原因:该账号交易过程中，正发生其他交易，请稍后重试]", "操作失败，系统繁忙请稍后重试");
        map.put("提现失败，交易失败，系统繁忙请稍后重试", "操作失败，系统繁忙请稍后重试");
        map.put("提现失败，交易超时，请您稍后重试[1020405]", "操作失败，系统繁忙请稍后重试");
        map.put("提现失败，对方返回：主机通讯超时", "操作失败，系统繁忙请稍后重试");
        map.put("提现失败，对方返回：与主机通讯超时", "操作失败，系统繁忙请稍后重试");
        map.put("提现失败，对方返回：系统异常，请稍后再试!", "操作失败，系统繁忙请稍后重试");
        map.put("提现失败，对方返回：通讯失败", "操作失败，系统繁忙请稍后重试");
        map.put("提现失败，对方返回：交易失败：[系统繁忙，请稍候再试！]", "操作失败，系统繁忙请稍后重试");
        map.put("提现失败，对方返回：对方返回：The system of receiver is busy now. Please try again later.", "操作失败，系统繁忙请稍后重试");
        map.put("提现失败，对方返回：T24 JCA Protocol error , beacause :Failed to receive message - Server disconnected", "操作失败，系统繁忙请稍后重试");
        map.put("交易失败，系统繁忙请稍后重试", "操作失败，系统繁忙请稍后重试");
        map.put("交易拒绝：查询先到、支付指令未到", "操作失败，系统繁忙请稍后重试");
        map.put("交易超时，且已回滚成功", "操作失败，系统繁忙请稍后重试");
        map.put("后端【324079】系统超过了流量控制的最大值", "操作失败，系统繁忙请稍后重试");
        map.put("bib_iqps系统通讯超时", "操作失败，系统繁忙请稍后重试");
        map.put("bib_iqps系统http通讯异常", "操作失败，系统繁忙请稍后重试");
        map.put("交通银行系统已关闭，暂停交通银行业务办理", "交通银行系统已关闭，暂停交通银行业务办理，请咨询银行客服");
        map.put("可用余额不足", "可用余额不足！");
        map.put("可提现余额不足", "可用余额不足！");
        map.put("减少可用余额失败，可用余额不足！", "可用余额不足！");
        map.put("大额支付系统已关闭,不允许发起业务!", "大额支付系统已关闭,不允许发起业务!请咨询银行客服");
        map.put("微信企业付款到零钱异常，请联系管理员！", "微信企业付款到零钱异常，请联系管理员！");
        map.put("提现失败，Ⅱ、Ⅲ类户年累计交易限额超限[1021261]", "提现失败，Ⅱ、Ⅲ类户年累计交易限额超限，请咨询银行客服");
        map.put("支付系统非日间！", "操作失败，请咨询银行客服");
        map.put("支付系统当前为人行签退状态,不能做交易", "操作失败，请咨询银行客服");
        map.put("银联提现异常", "操作失败，请咨询银行客服");
        map.put("系统异常导致的交易失败", "操作失败，请咨询银行客服");
        map.put("提现失败，支付系统非日间", "操作失败，请咨询银行客服");
        map.put("提现失败，支付系统查询无记录", "操作失败，请咨询银行客服");
        map.put("提现失败，行内处理失败,失败原因:服务控制拒绝", "操作失败，请咨询银行客服");
        map.put("提现失败，系统异常", "操作失败，请咨询银行客服");
        map.put("提现失败，无满足条件记录", "操作失败，请咨询银行客服");
        map.put("提现失败，外呼@@A0182S421@@不成功，错误码：@@XCMP4001A004@@，错误描述", "操作失败，请咨询银行客服");
        map.put("提现失败，日间交易不明确，日终对账调整为交易失败", "操作失败，请咨询银行客服");
        map.put("提现失败，日间撤销", "操作失败，请咨询银行客服");
        map.put("提现失败，请求失败", "操作失败，请咨询银行客服");
        map.put("提现失败，接收行状态无效暂不能做业务！", "操作失败，请咨询银行客服");
        map.put("提现失败，接出CA请求失败[上送主机[SH_CN_SVC]服务失败[TPENOENT-noent", "操作失败，请咨询银行客服");
        map.put("提现失败，交易失败[01271001004]", "操作失败，请咨询银行客服");
        map.put("提现失败，交易失败，详情请咨询您的发卡行[1020091]", "操作失败，请咨询银行客服");
        map.put("提现失败，交易失败，详情请咨询您的发卡行[1020062]", "操作失败，请咨询银行客服");
        map.put("提现失败，交易失败，详情请咨询您的发卡行[1020057]", "操作失败，请咨询银行客服");
        map.put("提现失败，交易失败，详情请咨询您的发卡行[1000096]", "操作失败，请咨询银行客服");
        map.put("提现失败，交易失败，详情请咨询您的发卡行[1000091]", "操作失败，请咨询银行客服");
        map.put("提现失败，交易失败，详情请咨询您的发卡行[1000090]", "操作失败，请咨询银行客服");
        map.put("提现失败，交易失败，详情请咨询您的发卡行[1000062]", "操作失败，请咨询银行客服");
        map.put("提现失败，交易失败，详情请咨询您的发卡行[1000057]", "操作失败，请咨询银行客服");
        map.put("提现失败，交易失败，详情请咨询您的发卡行[1000012]", "操作失败，请咨询银行客服");
        map.put("提现失败，交易失败，详情请咨询您的发卡行[1000001]", "操作失败，请咨询银行客服");
        map.put("提现失败，交易失败", "操作失败，请咨询银行客服");
        map.put("提现失败，交易拒绝：查询先到、支付指令未到。", "操作失败，请咨询银行客服");
        map.put("提现失败，交易成功", "操作失败，请咨询银行客服");
        map.put("提现失败，交易超时，且已回滚成功", "操作失败，请咨询银行客服");
        map.put("提现失败，后端【324079】系统超过了流量控制的最大值", "操作失败，请咨询银行客服");
        map.put("提现失败，服务控制拒绝", "操作失败，请咨询银行客服");
        map.put("提现失败，发卡行交易权限受限，详情请咨询您的发卡行[1020861]", "操作失败，请咨询银行客服");
        map.put("提现失败，发卡行交易权限受限，详情请咨询您的发卡行[1020061]", "操作失败，请咨询银行客服");
        map.put("提现失败，发卡行交易权限受限，详情请咨询您的发卡行[1000061]", "操作失败，请咨询银行客服");
        map.put("提现失败，发卡方无此主账号[619X1020114]", "操作失败，请咨询银行客服");
        map.put("提现失败，对方返回：已纳入控制名单，不允许办理", "操作失败，请咨询银行客服");
        map.put("提现失败，对方返回：业务拒绝码非法! -- tag[G00][RJ3F]", "操作失败，请咨询银行客服");
        map.put("提现失败，对方返回：业务拒绝码非法! -- tag[G00][]", "操作失败，请咨询银行客服");
        map.put("提现失败，对方返回：业务检查错", "操作失败，请咨询银行客服");
        map.put("提现失败，对方返回：系统错", "操作失败，请咨询银行客服");
        map.put("提现失败，对方返回：收款清算行状态非法! -- 参与机构:[403100000004]已离线!", "操作失败，请咨询银行客服");
        map.put("提现失败，对方返回：收款清算行状态非法! -- 参与机构:[104100000004]已离线!", "操作失败，请咨询银行客服");
        map.put("提现失败，对方返回：入账检查错", "操作失败，请咨询银行客服");
        map.put("提现失败，对方返回：日终自动退回", "操作失败，请咨询银行客服");
        map.put("提现失败，对方返回：日间撤销", "操作失败，请咨询银行客服");
        map.put("提现失败，对方返回：清分失败", "操作失败，请咨询银行客服");
        map.put("提现失败，对方返回：其他差错", "操作失败，请咨询银行客服");
        map.put("提现失败，对方返回：其他", "操作失败，请咨询银行客服");
        map.put("提现失败，对方返回：连接主机失败，请联系技术人员", "操作失败，请咨询银行客服");
        map.put("提现失败，对方返回：涓嶆敮鎸佹", "操作失败，请咨询银行客服");
        map.put("提现失败，对方返回：接收参与机构检查错拒绝", "操作失败，请咨询银行客服");
        map.put("提现失败，对方返回：核数字签名错 bocomm errcode:[IS0005]", "操作失败，请咨询银行客服");
        map.put("提现失败，对方返回：付款或收款账号解析失败", "操作失败，请咨询银行客服");
        map.put("提现失败，对方返回：调用核心IPP错", "操作失败，请咨询银行客服");
        map.put("提现失败，对方返回：当前IBPS系统状态非法! -- 网银系统维护", "操作失败，请咨询银行客服");
        map.put("提现失败，对方返回：iret=[-1].[ib1691]失败[esb与核心通讯异常]", "操作失败，请咨询银行客服");
        map.put("提现失败，对方返回：iret=[-1].[ib1691]失败[[Aplt.E0353]程序出现空指针异常]", "操作失败，请咨询银行客服");
        map.put("提现失败，对方返回：FB交易检查失败-休眠户", "操作失败，请咨询银行客服");
        map.put("提现失败，对方返回：bocomm errcode:[ID0296]", "操作失败，请咨询银行客服");
        map.put("提现失败，对方返回：bocomm errcode:[GE6001]", "操作失败，请咨询银行客服");
        map.put("提现失败，对方返回：bocomm errcode:[EBLN0000]", "操作失败，请咨询银行客服");
        map.put("提现失败，对方返回：bocomm errcode:[01]", "操作失败，请咨询银行客服");
        map.put("提现失败，对方返回：[DC2319]主账户暂禁（银行内部发起）AFAI2020042600000000000011265491", "操作失败，请咨询银行客服");
        map.put("提现失败，对方返回：[DC2319]主账户暂禁（银行内部发起）AFAI2020042600000000000011232752", "操作失败，请咨询银行客服");
        map.put("提现失败，对方返回：[DC2319]主账户暂禁（银行内部发起）AFAI2020042600000000000011140373", "操作失败，请咨询银行客服");
        map.put("提现失败，对方返回：[0423]MDT1009账号，已经睡眠！", "操作失败，请咨询银行客服");
        map.put("提现失败，当前系统状态不允许受理报文！ -- 当前系统状态[20]不允许受理[hvps.111.001.01]报文", "操作失败，请咨询银行客服");
        map.put("提现失败，单位返回信息:银联风险受限[2020023]", "操作失败，请咨询银行客服");
        map.put("提现失败，冲账成功", "操作失败，请咨询银行客服");
        map.put("提现失败，不允许此卡交易[639X1020057]", "操作失败，请咨询银行客服");
        map.put("提现失败，不允许此卡交易[639X1000057]", "操作失败，请咨询银行客服");
        map.put("提现失败，报文格式错误[1000030]", "操作失败，请咨询银行客服");
        map.put("提现流水号请求重复", "操作失败，请咨询银行客服");
        map.put("请求方流水号重复", "操作失败，请咨询银行客服");
        map.put("该应用系统处于暂停状态,不能做交易", "操作失败，请咨询银行客服");
        map.put("服务控制拒绝", "操作失败，请咨询银行客服");
        map.put(":通讯异常-连接后台服务异常", "操作失败，请咨询银行客服");
        map.put(":通讯异常-接收后台服务响应", "操作失败，请咨询银行客服");
        map.put(":服务方响应错误", "操作失败，请咨询银行客服");
        map.put(":创建连接异常[连银行]", "操作失败，请咨询银行客服");
        map.put("提现失败，交易失败，日累计交易金额超限[1020261]", "提现失败，日累计交易金额超限，请明日重试");
        map.put("提现失败，交易失败，转账交易金额超限[1020461]", "提现失败，转账交易金额超限，请降低金额");
        map.put("提现失败，密码输入次数超限[1000075]", "提现失败，密码输入次数超限，请稍后重试");
        map.put("提现失败，对方返回：330067账户已暂停非柜面交易", "提现失败，账户已暂停非柜面交易，请咨询银行客服");
        map.put("提现失败，对方返回：600117副卡不允许办理此交易", "提现失败，副卡不允许办理此交易，请咨询银行客服");
        map.put("提现失败，对方返回：600121该账户已销户或撤销", "提现失败，该账户已销户或撤销，请咨询银行客服");
        map.put("提现失败，对方返回：700012转入账号不存在，请重新输入", "提现失败，转入账号不存在，请重新输入");
        map.put("提现失败，对方返回：700099日期错误", "提现失败，日期错误，请重新输入");
        map.put("提现失败，对方返回：711023II/III类账户日累计存款限额超限", "提现失败，II/III类账户日累计存款限额超限，请咨询银行客服");
        map.put("提现失败，对方返回：980021该账户资金只能由绑定一类账户转入", "提现失败，该账户资金只能由绑定一类账户转入，可咨询银行客服");
        map.put("提现失败，对方返回：A000000收款账户和户名不匹配!", "提现失败，收款账户和户名不匹配，请重新输入");
        map.put("提现失败，对方返回：II,III类账户超过日限额", "提现失败，II,III类账户超过日限额，可咨询银行客服");
        map.put("提现失败，对方返回：UPAFIL输入客户名错", "提现失败，输入客户名错误，请重新输入");
        map.put("提现失败，对方返回：[0423]账户为限制账户，请携带本人/单位有效证件至恒丰银行任一网点解除限制。", "提现失败，账户为限制账户，请携带本人/单位有效证件至恒丰银行任一网点解除限制。");
        map.put("提现失败，对方返回：[2]类账户[单日转入/存入]额度超限", "提现失败，[2]类账户[单日转入/存入]额度超限，请咨询银行客服");
        map.put("提现失败，对方返回：业务累计金额/笔数超过规定上限", "提现失败，业务累计金额/笔数超过规定上限，可咨询银行客服");
        map.put("提现失败，对方返回：二类账户转入金额超限!", "提现失败，二类账户转入金额超限，可咨询银行客服");
        map.put("提现失败，对方返回：其它:账户不存在", "提现失败，账户不存在，请重新输入");
        map.put("提现失败，对方返回：卡性质限制", "提现失败，卡性质限制，可咨询银行客服");
        map.put("提现失败，您输入的卡号已注销，详询发卡行[1020414]", "提现失败，卡片无效，可咨询银行客服");
        map.put("提现失败，您输入的卡号无效，详询发卡行[1020114]", "提现失败，卡片无效，可咨询银行客服");
        map.put("提现失败，该卡长期未使用，暂停非柜面交易[1020621]", "提现失败，卡片无效，可咨询银行客服");
        map.put("提现失败，该卡未初始化或睡眠卡[609X1000021]", "提现失败，卡片无效，可咨询银行客服");
        map.put("提现失败，对方返回：卡状态为销卡 bocomm errcode:[IE0406]", "提现失败，卡片无效，可咨询银行客服");
        map.put("提现失败，对方返回：卡片已销卡AFAI2020041800000000000043368397", "提现失败，卡片无效，可咨询银行客服");
        map.put("提现失败，对方返回：卡片已销卡AFAI2019110800000000000004393665", "提现失败，卡片无效，可咨询银行客服");
        map.put("提现失败，对方返回：卡片已销卡AFAI2019110700000000000002183987", "提现失败，卡片无效，可咨询银行客服");
        map.put("提现失败，对方返回：卡片已销卡AFAI2019110700000000000000349767", "提现失败，卡片无效，可咨询银行客服");
        map.put("提现失败，对方返回：卡片已销卡AFAI2019110400000000000039468195", "提现失败，卡片无效，可咨询银行客服");
        map.put("提现失败，对方返回：卡片已销卡AFAI2019102900000000000011329502", "提现失败，卡片无效，可咨询银行客服");
        map.put("提现失败，对方返回：客户账号已作废", "提现失败，账号已作废，可咨询银行客服");
        map.put("提现失败，对方返回：尊敬的客户，因收款方证件已过期，交易失败，请在收款方更新证件后重新发起交易。AFAI2019100700", "提现失败，尊敬的客户，因收款方证件已过期，交易失败，请在收款方更新证件后重新发起交易");
        map.put("提现失败，对方返回：尊敬的客户，因收款方证件已过期，交易失败，请在收款方更新证件后重新发起交易。AFAI2019092900", "提现失败，尊敬的客户，因收款方证件已过期，交易失败，请在收款方更新证件后重新发起交易");
        map.put("提现失败，对方返回：尊敬的客户，因收款方证件已过期，交易失败，请在收款方更新证件后重新发起", "提现失败，尊敬的客户，因收款方证件已过期，交易失败，请在收款方更新证件后重新发起交易");
        map.put("提现失败，对方返回：当日业务累计金额超过规定金额", "提现失败，当日业务累计金额超过规定金额，可咨询银行客服");
        map.put("提现失败，对方返回：拒绝业务必须填写拒绝原因! -- 业务状态[PR09]tag[H06][]！", "提现失败，必须填写拒绝原因!");
        map.put("提现失败，收款账号状态不能入账", "提现失败，收款人账号异常，可咨询银行客服");
        map.put("提现失败，对方返回：收款账号状态异常", "提现失败，收款人账号异常，可咨询银行客服");
        map.put("提现失败，对方返回：收款人账号异常", "提现失败，收款人账号异常，可咨询银行客服");
        map.put("提现失败，对方返回：收款方客户在我行预留身份证件已过期，需更新身份资料后办理业务", "提现失败，收款方客户在我行预留身份证件已过期，需更新身份资料后办理业务");
        map.put("提现失败，对方返回：账户状态异常不允许入账", "提现失败，账户状态异常，可咨询银行客服");
        map.put("提现失败，对方返回：账户状态异常(F3C1112)", "提现失败，账户状态异常，可咨询银行客服");
        map.put("提现失败，对方返回：账户状态异常(CSAB016)", "提现失败，账户状态异常，可咨询银行客服");
        map.put("提现失败，对方返回：账户状态异常", "提现失败，账户状态异常，可咨询银行客服");
        map.put("提现失败，对方返回：账户状态[10000000]异常不允许此项操作", "提现失败，账户状态异常，可咨询银行客服");
        map.put("提现失败，对方返回：账户异常(NPC0089)", "提现失败，账户状态异常，可咨询银行客服");
        map.put("提现失败，对方返回：账户[黎汶红]名单类型[六个月未活动账户灰名单]", "提现失败，账户状态异常，可咨询银行客服");
        map.put("提现失败，对方返回：账号解析失败或账号状态异常", "提现失败，账户状态异常，可咨询银行客服");
        map.put("提现失败，对方返回：收款行处理失败,账户状态异常", "提现失败，账户状态异常，可咨询银行客服");
        map.put("提现失败，对方返回：账号、户名不符(PIB4105)", "提现失败，账号、户名不符，请重新输入");
        map.put("提现失败，对方返回：账号、户名不符(NPC0057)", "提现失败，账号、户名不符，请重新输入");
        map.put("提现失败，对方返回：账号、户名不符", "提现失败，账号、户名不符，请重新输入");
        map.put("提现失败，对方返回：账号、户名不符 bocomm errcode:[IE0321]", "提现失败，账号、户名不符，请重新输入");
        map.put("提现失败，对方返回：收款账号、户名不符", "提现失败，账号、户名不符，请重新输入");
        map.put("提现失败，对方返回：查询账户信息失败或异常", "提现失败，查询账户信息失败或异常，请稍后重试");
        map.put("提现失败，对方返回：校验客户身份失败", "提现失败，校验客户身份失败，可咨询银行客服");
        map.put("提现失败，对方返回：系统维护，交易失败", "提现失败，系统维护，请稍后重试");
        map.put("提现失败，对方返回：该卡限制网银渠道交易", "提现失败，该卡限制网银渠道交易，请联系银行客服");
        map.put("提现失败，对方返回：该账号／卡号6223160027210491无效！", "提现失败，该账号／卡号无效，请重新输入");
        map.put("提现失败，对方返回：该账户已被暂停非柜面渠道业务功能", "提现失败，该账户已被暂停非柜面渠道业务功能，可咨询银行客服");
        map.put("提现失败，对方返回：账号不合法", "提现失败，账号不合法，请重新输入");
        map.put("提现失败，对方返回：账号不合法 bocomm errcode:[IE0327]", "提现失败，账号不合法，请重新输入");
        map.put("提现失败，账号不存在", "提现失败，账号不存在，请重新输入");
        map.put("提现失败，对方返回：账户不存在", "提现失败，账号不存在，请重新输入");
        map.put("提现失败，对方返回：账号不存在或其他错误", "提现失败，账号不存在，请重新输入");
        map.put("提现失败，对方返回：账户类型非法", "提现失败，账号类型非法，请重新输入");
        map.put("提现失败，对方返回：账号错误", "提现失败，账户错误，请重新输入");
        map.put("提现失败，对方返回：账户/卡不允许入账", "提现失败，账户/卡不允许入账，请咨询银行客服");
        map.put("提现失败，对方返回：账户为不动户", "提现失败，账户为不动户，请重新输入");
        map.put("提现失败，对方返回：账户信息查询失败", "提现失败，账户信息查询失败，请咨询银行客服");
        map.put("提现失败，对方返回：账户处理异常", "提现失败，账户处理异常，请咨询银行客服");
        map.put("提现失败，对方返回：非面对面验证的II、III类户不允许非绑定账户资金转入", "提现失败，非面对面验证的II、III类户不允许非绑定账户资金转入，请咨询银行客服");
        map.put("提现失败，对方返回：面签标识验证不通过", "提现失败，面签标识验证不通过，可咨询银行客服");
        map.put("提现失败，您输入的身份验证信息有误，请确认后重试[1020305]", "提现失败，您输入的身份验证信息有误，请确认后重试");
        map.put("提现失败，户名不符", "提现失败，户名不符，请重新输入");
        map.put("提现失败，持卡人认证失败[669X1000005]", "提现失败，持卡人认证失败，可咨询银行客服");
        map.put("提现失败，持卡人身份信息、手机号或CVN2输入不正确，验证失败[1020005]", "提现失败，持卡人身份信息、手机号或CVN2输入不正确，验证失败，请重新输入");
        map.put("提现失败，持卡人身份信息、手机号或CVN2输入不正确，验证失败[1000005]", "提现失败，持卡人身份信息、手机号或CVN2输入不正确，验证失败，请重新输入");
        map.put("提现失败，缺少必要的验证信息，详询您的发卡行[1021205]", "提现失败，缺少必要的验证信息，可咨询银行客服");
        map.put("提现失败，证件有效期过期[639X1021757]", "提现失败，证件有效期过期");
        map.put("提现失败，证件有效期过期[1021757]", "提现失败，证件有效期过期");
        map.put("提现失败，输入的卡号无效，请确认后输入[1020914]", "提现失败，输入的卡号无效，请确认后输入");
        map.put("提现失败，输入的卡号无效，请确认后输入[1020014]", "提现失败，输入的卡号无效，请确认后输入");
        map.put("提现失败，输入的卡号无效，请确认后输入[1000014]", "提现失败，输入的卡号无效，请确认后输入");
        map.put("提现失败，银行卡未开通认证支付[1020057]", "提现失败，银行卡未开通认证支付，可咨询银行客服");
        map.put("提现失败，银行卡未开通认证支付[1000057]", "提现失败，银行卡未开通认证支付，可咨询银行客服");
        map.put("提现渠道路由异常（非平安银行账户须提供他行行号信息，大额提现须提供联行号）", "提现渠道路由异常（非平安银行账户须提供他行行号信息，大额提现须提供联行号）");
        map.put("提现账户未绑定", "提现账户未绑定，请先绑定");
        map.put("无此委托单位签约协议!", "无此委托单位签约协议，可咨询银行客服");
        map.put("无符合条件记录", "无符合条件记录，可咨询银行客服");
        map.put("没有配置托管类型，请检查账户配置！", "没有配置托管类型，请检查账户配置！");
        map.put("账户不允许进行非柜面交易操作", "账户不允许进行非柜面交易操作，可咨询银行客服");
        map.put("账户已锁定不能交易！", "账户已锁定不能交易！可咨询银行客服");
        map.put("账户银行卡异常", "账户银行卡异常，请咨询银行客服");

    }

    public static String errorDescConvert(String errorDesc) {
        return map.get(errorDesc);
    }

}
