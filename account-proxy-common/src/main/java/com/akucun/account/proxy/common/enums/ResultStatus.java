package com.akucun.account.proxy.common.enums;


public enum ResultStatus {
    I("I", "初始化"),
    P("P", "处理中"),
    S("S", "处理成功"),
    F("F", "处理失败");


    String code;
    String desc;

    ResultStatus(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }

    public static ResultStatus getEnumByCode(String code) {
        for (ResultStatus result : ResultStatus.values()) {
            if (code.equals(result.getCode())) {
                return result;
            }
        }
        return null;
    }
}
