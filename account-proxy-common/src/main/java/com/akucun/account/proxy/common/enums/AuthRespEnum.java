package com.akucun.account.proxy.common.enums;

/**
 * @Author: silei
 * @Date: 2020/9/30
 * @desc: 小额鉴权状态枚举
 */
public enum AuthRespEnum {

    NONE(0,"未鉴权或鉴权已结束"),
    SUCCESS(1, "成功"),
    FAIL(2, "失败"),
    PROCESSING(3, "处理中"),;

    private Integer value;
    private String desc;

    AuthRespEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return  this.value;
    }

    public String getDesc() {
        return this.desc;
    }
}
