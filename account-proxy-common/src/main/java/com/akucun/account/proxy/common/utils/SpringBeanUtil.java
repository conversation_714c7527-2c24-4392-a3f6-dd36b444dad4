package com.akucun.account.proxy.common.utils;

import com.akucun.account.proxy.common.enums.ResponseEnum;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * @desc: : SpringBean
 **/
@Component
public class SpringBeanUtil implements ApplicationContextAware {
    private ApplicationContext context; //Spring应用上下文环境

    private static SpringBeanUtil singletonSpringUtil;

    /**
     * 获取静态对象
     *
     * @return
     */
    public static SpringBeanUtil getSingletonSpringContextUtil() {
        if (singletonSpringUtil == null) {
            throw new IllegalStateException(ResponseEnum.SYSTEM_EXCEPTION.getMessage());
        }
        return singletonSpringUtil;
    }

    /**
     * @return the context
     */
    public ApplicationContext getContext() {
        return context;
    }

    /**
     * @param context the context to set
     */
    public void setContext(ApplicationContext context) {
        this.context = context;
    }

    /**
     * 实现ApplicationContextAware接口的回调方法，设置上下文环境
     *
     * @param applicationContext
     * @throws BeansException
     */
    @Override
    public synchronized void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        if (SpringBeanUtil.singletonSpringUtil == null) {
            SpringBeanUtil.singletonSpringUtil = new SpringBeanUtil();
            singletonSpringUtil.setContext(applicationContext);
        }
    }

    /**
     * @return ApplicationContext
     */
    public static ApplicationContext getApplicationContext() {
        return getSingletonSpringContextUtil().getContext();
    }

    /**
     * 获取对象
     *
     * @param name
     * @return Object 一个以所给名字注册的bean的实例
     * @throws BeansException
     */
    public static Object getBean(String name) throws BeansException {
        return getSingletonSpringContextUtil().getContext().getBean(name);
    }

    /**
     * 获取类型为requiredType的对象
     * 如果bean不能被类型转换，相应的异常将会被抛出（BeanNotOfRequiredTypeException）
     *
     * @param name         bean注册名
     * @param requiredType 返回对象类型
     * @return Object 返回requiredType类型对象
     * @throws BeansException
     */
    public static <T> T getBean(String name, Class<T> requiredType) throws BeansException {
        return (T) getSingletonSpringContextUtil().getContext().getBean(name, requiredType);
    }

    /**
     * 如果BeanFactory包含一个与所给名称匹配的bean定义，则返回true
     *
     * @param name
     * @return boolean
     */
    public static boolean containsBean(String name) {
        return getSingletonSpringContextUtil().getContext().containsBean(name);
    }

    /**
     * 判断以给定名字注册的bean定义是一个singleton还是一个prototype。
     * 如果与给定名字相应的bean定义没有被找到，将会抛出一个异常（NoSuchBeanDefinitionException）
     *
     * @param name
     * @return boolean
     * @throws BeansException
     */
    public static boolean isSingleton(String name) throws BeansException {
        return getSingletonSpringContextUtil().getContext().isSingleton(name);
    }

    /**
     * @param name
     * @return Class 注册对象的类型
     * @throws BeansException
     */
    public static Class<?> getType(String name) throws BeansException {
        return getSingletonSpringContextUtil().getContext().getType(name);
    }

    /**
     * 如果给定的bean名字在bean定义中有别名，则返回这些别名
     *
     * @param name
     * @return
     * @throws BeansException
     */
    public static String[] getAliases(String name) throws BeansException {
        return getSingletonSpringContextUtil().getContext().getAliases(name);
    }
}


