package com.akucun.account.proxy.common.constant;

import com.akucun.fps.common.constants.MEnum;

/**
 * @program: fps-account
 * @description: 明细类型枚举
 * @author: wangliangliang
 * @create: 2018-05-18 11:11
 **/
public class DetailTypeConstants extends MEnum<DetailTypeConstants> {

    public static final DetailTypeConstants FIRST_PAY = (DetailTypeConstants) create("FIRST_PAY", 0, "首款划账");
    public static final DetailTypeConstants END_PAY = (DetailTypeConstants) create("END_PAY", 1, "尾款划账");
    public static final DetailTypeConstants FILL = (DetailTypeConstants) create("FILL", 26, "补款");
    public static final DetailTypeConstants BALANCE_PAY = (DetailTypeConstants) create("BALANCE_PAY", 2, " 余额支付");
    public static final DetailTypeConstants BALANCE_REFUND = (DetailTypeConstants) create("BALANCE_REFUND", 3, "余额退款");
    public static final DetailTypeConstants BALANCE_CH = (DetailTypeConstants) create("BALANCE_CH", 4, "余额充值");
    public static final DetailTypeConstants WITHDRAW = (DetailTypeConstants) create("WITHDRAW", 5, "余额提现");
    public static final DetailTypeConstants COMPENSATE = (DetailTypeConstants) create("COMPENSATE", 6, "退单补偿");
    public static final DetailTypeConstants RED_REWARD = (DetailTypeConstants) create("RED_REWARD", 7, "红包奖励");
    public static final DetailTypeConstants OTHER_REWARD = (DetailTypeConstants) create("OTHER_REWARD", 8, "其他奖励");
    public static final DetailTypeConstants CORRECTION = (DetailTypeConstants) create("CORRECTION", 9, "冲正");
    public static final DetailTypeConstants UN_WITHDRAW = (DetailTypeConstants) create("UN_WITHDRAW", 10, "余额提现失败返还");
    public static final DetailTypeConstants WITHDRAW_AG = (DetailTypeConstants) create("WITHDRAW_AG", 11, "提现审核通过");
    public static final DetailTypeConstants WITHDRAW_RF = (DetailTypeConstants) create("WITHDRAW_RF", 12, "提现审核拒绝");
    public static final DetailTypeConstants INIT_AMOUNT = (DetailTypeConstants) create("INIT_AMOUNT", 13, "账户金额初始化");
    public static final DetailTypeConstants PAY_BILL = (DetailTypeConstants) create("PAY_BILL", 61, "账单结算");

    //h5
    public static final DetailTypeConstants TRADE_TYPE_043 = (DetailTypeConstants) create("TRADE_TYPE_043", 14, "佣金分润");
    public static final DetailTypeConstants TRADE_TYPE_044 = (DetailTypeConstants) create("TRADE_TYPE_044", 15, "提现");
    public static final DetailTypeConstants TRADE_TYPE_045 = (DetailTypeConstants) create("TRADE_TYPE_045", 16, "提现失败");
    public static final DetailTypeConstants TRADE_TYPE_046 = (DetailTypeConstants) create("TRADE_TYPE_046", 28, "补差价、质量问题、假货");
    public static final DetailTypeConstants TRADE_TYPE_047 = (DetailTypeConstants) create("TRADE_TYPE_047", 29, "开店佣金");
    public static final DetailTypeConstants TRADE_TYPE_040 = (DetailTypeConstants) create("TRADE_TYPE_040", 30, "支付");
    public static final DetailTypeConstants TRADE_TYPE_041 = (DetailTypeConstants) create("TRADE_TYPE_041", 31, "退款");
    public static final DetailTypeConstants TRADE_TYPE_042 = (DetailTypeConstants) create("TRADE_TYPE_042", 32, "充值");
    public static final DetailTypeConstants TRADE_TYPE_048 = (DetailTypeConstants) create("TRADE_TYPE_048", 33, "打赏");
    public static final DetailTypeConstants TRADE_TYPE_049 = (DetailTypeConstants) create("TRADE_TYPE_049", 34, "打赏失败返还");
    public static final DetailTypeConstants TRADE_TYPE_050 = (DetailTypeConstants) create("TRADE_TYPE_050", 36, "补偿运费");
    public static final DetailTypeConstants TRADE_TYPE_051 = (DetailTypeConstants) create("TRADE_TYPE_051", 42, "奖励发放");
    public static final DetailTypeConstants TRADE_TYPE_052 = (DetailTypeConstants) create("TRADE_TYPE_052", 46, "运费险理赔");
    public static final DetailTypeConstants TRADE_TYPE_053 = (DetailTypeConstants) create("TRADE_TYPE_053", 49, "资产费");
    public static final DetailTypeConstants TRADE_TYPE_054 = (DetailTypeConstants) create("TRADE_TYPE_054", 50, "服务费");
    public static final DetailTypeConstants TRADE_TYPE_056 = (DetailTypeConstants) create("TRADE_TYPE_056", 56, "商品服务收入");
    public static final DetailTypeConstants TRADE_TYPE_057 = (DetailTypeConstants) create("TRADE_TYPE_057", 57, "加码资产收入");
    public static final DetailTypeConstants TRADE_TYPE_058 = (DetailTypeConstants) create("TRADE_TYPE_058", 58, "加码商品服务收入");
    public static final DetailTypeConstants TRADE_TYPE_059 = (DetailTypeConstants) create("TRADE_TYPE_059", 59, "加码服务收入");

    //新平台
    public static final DetailTypeConstants TRADE_TYPE_024 = (DetailTypeConstants) create("TRADE_TYPE_024", 17, "结算");
    public static final DetailTypeConstants TRADE_TYPE_022 = (DetailTypeConstants) create("TRADE_TYPE_022", 18, "提现");
    public static final DetailTypeConstants TRADE_TYPE_026 = (DetailTypeConstants) create("TRADE_TYPE_026", 19, "提现失败");
    public static final DetailTypeConstants TRADE_TYPE_033 = (DetailTypeConstants) create("TRADE_TYPE_033", 41, "邀请入群结算");

    //app代购
    public static final DetailTypeConstants TRADE_TYPE_184 = (DetailTypeConstants) create("TRADE_TYPE_184", 21, "提现失败");
    public static final DetailTypeConstants TRADE_TYPE_187 = (DetailTypeConstants) create("TRADE_TYPE_187", 22, "返点分润");
    public static final DetailTypeConstants TRADE_TYPE_189 = (DetailTypeConstants) create("TRADE_TYPE_189", 47, "运费险理赔");




    public static final DetailTypeConstants REVOKE_PAY = (DetailTypeConstants)create("REVOKE_PAY", 27, "撤销扣款");
    public static final DetailTypeConstants ADVANCE_RETURN = (DetailTypeConstants) create("ADVANCE_RETURN", 20, "撤销返还");


    //h5商城代理
    public static final DetailTypeConstants TRADE_TYPE_260 = (DetailTypeConstants) create("TRADE_TYPE_260", 23, "分佣");
    public static final DetailTypeConstants TRADE_TYPE_261 = (DetailTypeConstants) create("TRADE_TYPE_261", 24, "提现");
    public static final DetailTypeConstants TRADE_TYPE_262 = (DetailTypeConstants) create("TRADE_TYPE_262", 25, "提现失败");
    public static final DetailTypeConstants TRADE_TYPE_263 = (DetailTypeConstants) create("TRADE_TYPE_263", 35, "打赏收入");
    public static final DetailTypeConstants TRADE_TYPE_264 = (DetailTypeConstants) create("TRADE_TYPE_264", 40, "红包收入");
    public static final DetailTypeConstants TRADE_TYPE_265 = (DetailTypeConstants) create("TRADE_TYPE_265", 48, "服务费");
    public static final DetailTypeConstants TRADE_TYPE_266 = (DetailTypeConstants) create("TRADE_TYPE_266", 70, "加码服务收入");
    public static final DetailTypeConstants TRADE_TYPE_267 = (DetailTypeConstants) create("TRADE_TYPE_267", 267, "支付");
    public static final DetailTypeConstants TRADE_TYPE_268 = (DetailTypeConstants) create("TRADE_TYPE_268", 268, "退款");

    //openApi交易枚举
    public static final DetailTypeConstants TRADE_TYPE_120 = (DetailTypeConstants)create("TRADE_TYPE_120", 37, "支付");
    public static final DetailTypeConstants TRADE_TYPE_121 = (DetailTypeConstants)create("TRADE_TYPE_121", 38, "退款");
    public static final DetailTypeConstants TRADE_TYPE_124 = (DetailTypeConstants)create("TRADE_TYPE_124", 39, "充值");
    public static final DetailTypeConstants TRADE_TYPE_133 = (DetailTypeConstants)create("TRADE_TYPE_133", 60, "提现");
    public static final DetailTypeConstants TRADE_TYPE_134 = (DetailTypeConstants)create("TRADE_TYPE_134", 61, "提现失败");


    //今日断码交易枚举
    public static final DetailTypeConstants TRADE_TYPE_360 = (DetailTypeConstants)create("TRADE_TYPE_360", 45, "分润");
    public static final DetailTypeConstants TRADE_TYPE_361 = (DetailTypeConstants)create("TRADE_TYPE_361", 43, "提现");
    public static final DetailTypeConstants TRADE_TYPE_362 = (DetailTypeConstants)create("TRADE_TYPE_362", 44, "提现失败");

    //微信账户交易类型
    public static final DetailTypeConstants TRADE_TYPE_400 = (DetailTypeConstants)create("TRADE_TYPE_400", 51, "分账");
    public static final DetailTypeConstants TRADE_TYPE_401 = (DetailTypeConstants)create("TRADE_TYPE_401", 52, "提现");
    public static final DetailTypeConstants TRADE_TYPE_402 = (DetailTypeConstants)create("TRADE_TYPE_402", 53, "提现失败");
}
