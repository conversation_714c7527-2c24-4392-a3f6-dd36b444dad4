package com.akucun.account.proxy.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : 账户中心交心汇总的业务处理类型
 * @Create on : 2023/11/20 14:23
 **/
public enum BusiTypeEnum {

    AYTUAN("ayatuan", "阿呀团"),
    SASS("sass", "企业饷店"),
    ;
    @Getter
    private String code;
    @Getter
    private String desc;

    BusiTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
