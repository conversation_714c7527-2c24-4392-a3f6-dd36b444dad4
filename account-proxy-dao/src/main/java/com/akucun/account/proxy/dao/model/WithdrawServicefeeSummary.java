package com.akucun.account.proxy.dao.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("withdraw_servicefee_summary")
public class WithdrawServicefeeSummary {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 提现月份
     */
    @TableField("month")
    private String month;
    /**
     * 身份证
     */
    @TableField("identify_no")
    private String identifyNo;
    /**
     * 名称
     */
    @TableField("identify_name")
    private String identifyName;
    /**
     * 身份类别
     */
    @TableField("identify_category")
    private String identifyCategory;

    /**
     * 手续费合计金额
     */
    @TableField("service_fee")
    private BigDecimal serviceFee;
    /**
     * 累计提现成功次数
     */
    @TableField("withdraw_num")
    private Integer withdrawNum;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 是否删除，0:未删除，1删除
     */
    @TableField("is_delete")
    private Boolean isDelete;
}
