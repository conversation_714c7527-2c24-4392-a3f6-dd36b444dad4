package com.akucun.account.proxy.dao.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: silei
 * @Date: 2020/11/30
 * @desc: 账户支付记录
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("account_customer_route")
public class AccountCustomerRoute {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 交易订单号
     */
    @TableField("trade_no")
    private String tradeNo;

    /**
     * 客户编码
     */
    @TableField("customer_code")
    private String customerCode;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", update = "now()")
    private Date updateTime;

    /**
     * 是否删除 0:未删除
     */
    @TableField("is_delete")
    private Integer isDelete;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;
}
