/*
 * @Author: <PERSON>
 * @Date: 2025-04-16 15:24:14
 * @Description: Do not edit
 * @License: Copyright (c) 2025, Lee
 */
package com.akucun.account.proxy.dao.model;

import java.math.BigDecimal;
import java.util.Date;

import lombok.Data;

@Data
public class OfflineAdjustAccount {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 客户编码
     */
    private String customerCode;

    /**
     * 客户类型
     */
    private String customerType;

    /**
     * 账户类型Key
     */
    private String accountTypeKey;

    /**
     * 交易单号
     */
    private String tradeNo;

    /**
     * 来源单号
     */
    private String sourceBillNo;

    /**
     * 交易类型
     */
    private String tradeType;

    /**
     * 交易金额
     */
    private BigDecimal amount;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 扩展字段，用于存储额外信息
     */
    private String ext;

    /**
     * 审核人
     */
    private String auditBy;

    /**
     * 审核状态,0:待审核,1:审核通过,2:审核拒绝
     */
    private Integer auditStatus;

    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * 是否删除（0:未删除, 1:已删除）
     */
    private byte isDelete;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建人
     */
    private String createBy;
    
    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 审核备注
     */
    private String auditRemark;

    /** 
     * 调整状态
     */
    private Integer adjustStatus;

    /**
     * 失败原因
     */
    private String failReason;

}