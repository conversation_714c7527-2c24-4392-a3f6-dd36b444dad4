package com.akucun.account.proxy.dao.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Create on : 2024/11/25 21:10
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
@TableName("compensation_pay_apply")
public class CompensationPayApply {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 唯一业务单号
     */
    @TableField("source_business_no")
    private String sourceBusinessNo;

    /**
     * 商家编码
     */
    @TableField("merchant_code")
    private String merchantCode;

    /**
     * 商家名称
     */
    @TableField("merchant_name")
    private String merchantName;

    /**
     * 渠道商ID/租户ID
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * SKU单号
     */
    @TableField("order_sku_no")
    private String orderSkuNo;

    @TableField("amount")
    private BigDecimal amount;

    /**
     * 业务发生时间
     */
    @TableField("business_date")
    private Date businessDate;

    /**
     * 渠道:INSURANCE、AFTER_SALE
     */
    @TableField("channel")
    private String channel;

    /**
     * 补款类型:运费补偿：SHIPPING_FEE_COMPENSATION，补货款：GOODS_FEE_COMPENSATION，运费险理赔：SHIPPING_INSURANCE_CLAIM
     */
    @TableField("type")
    private String type;

    /**
     * 订单渠道
     */
    @TableField("order_channel")
    private String orderChannel;

    /**
     * 二级单号
     */
    @TableField("second_order_no")
    private String secondOrderNo;

    /**
     * 三级单号
     */
    @TableField("third_order_no")
    private String thirdOrderNo;

    /**
     * 三级ID
     */
    @TableField("third_order_id")
    private String thirdOrderId;

    /**
     * 店铺code
     */
    @TableField("shop_code")
    private String shopCode;

    /**
     * 标记字段
     */
    @TableField("busi_option")
    private Long busiOption;

    /**
     * 店铺code
     */
    @TableField("exts")
    private String exts;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", update = "now()")
    private Date updateTime;

}
