package com.akucun.account.proxy.dao.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("reward_apply")
public class RewardApply {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("request_no")
    private String requestNo;

    @TableField("busi_type")
    private String busiType;

    @TableField("trans_bill_date")
    private String transBillDate;

    @TableField("trade_time")
    private Date tradeTime;

    @TableField("trade_amt")
    private BigDecimal tradeAmt;

    @TableField("tax_amt")
    private BigDecimal taxAmt;

    @TableField("user_code")
    private String userCode;

    @TableField("user_type")
    private String userType;

    @TableField("tenant_id")
    private String tenantId;

    @TableField("user_grade")
    private Integer userGrade;

    @TableField("grade_channel")
    private Integer gradeChannel;

    @TableField("source_biz_no")
    private String sourceBizNo;

    @TableField("batch_no")
    private String batchNo;

    @TableField("activity_no")
    private String activityNo;

    @TableField("attachment_paths")
    private String attachmentPaths;

    @TableField("apply_user_id")
    private String applyUserId;

    @TableField("resp_no")
    private String respNo;

    @TableField("remark")
    private String remark;

    @TableField("busi_num")
    private Long busiNum;

    @TableField("invoice_url")
    private String invoiceUrl;

    @TableField("source_sys_id")
    private String sourceSysId;

    @TableField("ext1")
    private String ext1;

    @TableField("ext2")
    private String ext2;

    @TableField("status")
    private String status;

    @TableField("create_time")
    private Date createTime;

    @TableField(value = "update_time", update = "now()")
    private Date updateTime;

}