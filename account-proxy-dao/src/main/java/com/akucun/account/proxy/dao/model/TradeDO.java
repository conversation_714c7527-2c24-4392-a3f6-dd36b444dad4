package com.akucun.account.proxy.dao.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@TableName("trade")
public class TradeDO {

    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 交易流水号
     */
    @TableField("trade_no")
    private String tradeNo;

    /**
     * 业务单号
     */
    @TableField("biz_no")
    private String bizNo;

    /**
     * 产品线
     */
    @TableField("product_category")
    private String productCategory;

    /**
     * 业务类型
     */
    @TableField("biz_type")
    private String bizType;

    /**
     * 请求平台
     */
    @TableField("request_platform")
    private String requestPlatform;

    /**
     * 业务参数
     */
    @TableField("biz_info")
    private String bizInfo;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 交易状态
     */
    @TableField("status")
    private String status;

    /**
     * 协议ID
     */
    @TableField("agreement_id")
    private String agreementId;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time", update = "now()")
    private Date updateTime;

}
