package com.akucun.account.proxy.dao.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: silei
 * @Date: 2021/4/12
 * @desc:
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tenant_withdraw_apply")
public class TenantWithdrawApply {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 提现编号
     */
    @TableField("withdraw_no")
    private String withdrawNo;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;
    /**
     * 客户编码
     */
    @TableField("customer_code")
    private String customerCode;
    /**
     * 客户名称
     */
    @TableField("customer_name")
    private String customerName;
    /**
     * 客户类型
     */
    @TableField("customer_type")
    private String customerType;
    /**
     * 来源单号
     */
    @TableField("source_bill_no")
    private String sourceBillNo;
    /**
     * 银行名称
     */
    @TableField("bank_name")
    private String bankName;
    /**
     * 银行卡号
     */
    @TableField("bank_no")
    private String bankNo;
    /**
     * 提现金额
     */
    @TableField("amount")
    private BigDecimal amount;
    /**
     * 服务费
     */
    @TableField("service_amount")
    private BigDecimal serviceAmount;
    /**
     * 申请状态
     */
    @TableField("apply_status")
    private String applyStatus;
    /**
     * 提现失败原因
     */
    @TableField("fail_reason")
    private String failReason;
    /**
     * 提现渠道
     */
    @TableField("withdraw_channel")
    private String withdrawChannel;
    /**
     * 店铺id
     */
    @TableField("shop_id")
    private String shopId;


    /**
     * 店铺id
     */
    @TableField("identify_no")
    private String identifyNo;
    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 是否删除，0:未删除，1删除
     */
    @TableField("is_delete")
    private Boolean isDelete;

    /**
     * 提现月份
     */
    @TableField("month")
    private String month;

    /**
     * 客户等级
     */
    @TableField("customer_grade")
    private String customerGrade;

    /**
     * 回执单地址
     */
    @TableField("receipt_url")
    private String receiptUrl;
}
