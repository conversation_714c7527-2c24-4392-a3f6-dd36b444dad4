package com.akucun.account.proxy.dao.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: silei
 * @Date: 2021/3/15
 * @desc: 账户汇总金额信息（收入、提现）
 */

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("account_total_amount")
public class AccountTotalAmount {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 客户编码
     */
    @TableField("customer_code")
    private String customerCode;
    /**
     * 客户名称
     */
    @TableField("customer_name")
    private String customerName;
    /**
     * 客户类型
     */
    @TableField("customer_type")
    private String customerType;

    /**
     * 提现金额
     */
    @TableField("cash_amount")
    private BigDecimal cashAmount;

    /**
     * 收入金额
     */
    @TableField("income_amount")
    private BigDecimal incomeAmount;


    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

}
