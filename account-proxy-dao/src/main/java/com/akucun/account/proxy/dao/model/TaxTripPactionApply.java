package com.akucun.account.proxy.dao.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.util.Date;

/**
 * <p>
 * 税库银三方协议审核申请记录表
 * </p>
 * <AUTHOR>
 * @since 2021-02-24
 */
@Data
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tax_trip_paction_apply")
public class TaxTripPactionApply {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户名
     */
    @TableField("user_name")
    private String userName;

    /**
     * 客户编码
     */
    @TableField("customer_code")
    private String customerCode;

    /**
     * 客户类型
     */
    @TableField("customer_type")
    private String customerType;

    /**
     * 银行名称
     */
    @TableField("bank_name")
    private String bankName;

    /**
     * 银行编码
     */
    @TableField("bank_code")
    private String bankCode;

    /**
     * 银行卡号
     */
    @TableField("bank_card_no")
    private String bankCardNo;

    /**
     * 协议图片路径
     */
    @TableField("paction_img_url")
    private String pactionImgUrl;

    /**
     * 状态 0：审核中，1：审核通过，2：审核未通过
     */
    @TableField("status")
    private int status;

    /**
     * 驳回原因
     */
    @TableField("reject_reason")
    private String rejectReason;

    /**
     * 客服侧审核工单编号
     */
    @TableField("audit_no")
    private String auditNo;

    /**
     * 社会信用代码
     */
    @TableField("social_credit_code")
    private String socialCreditCode;

    /**
     * 商家名称
     */
    @TableField("merchant_name")
    private String merchantName;

    /**
     * 法人姓名
     */
    @TableField("legal_person_name")
    private String legalPersonName;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;


}
