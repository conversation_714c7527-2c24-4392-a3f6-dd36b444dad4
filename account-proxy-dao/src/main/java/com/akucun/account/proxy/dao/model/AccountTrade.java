package com.akucun.account.proxy.dao.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: silei
 * @Date: 2020/11/30
 * @desc: 账户支付记录
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("account_trade")
public class AccountTrade {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 订单来源系统编码
     */
    @TableField("source_code")
    private String sourceCode;

    /**
     * 来源订单号
     */
    @TableField("source_no")
    private String sourceNo;

    /**
     * 交易订单号
     */
    @TableField("trade_no")
    private String tradeNo;

    /**
     * 业务交易类型(账户中心交易类型)
     */
    @TableField("biz_trade_type")
    private String bizTradeType;

    /**
     * 交易类型
     */
    @TableField("trade_type")
    private String tradeType;

    /**
     * 客户编码
     */
    @TableField("customer_code")
    private String customerCode;

    /**
     * 客户名称
     */
    @TableField("customer_name")
    private String customerName;

    /**
     * 客户类型
     */
    @TableField("customer_type")
    private String customerType;

    /**
     * 客户类型
     */
    @TableField("amount")
    private BigDecimal amount;

    /**
     * 交易状态(I初始化，S成功，F失败，P处理中)
     */
    @TableField("status")
    private String status;

    /**
     * 返回码
     */
    @TableField("reply_code")
    private String replyCode;

    /**
     * 返回消息
     */
    @TableField("reply_msg")
    private String replyMsg;

    /**
     * 扩展域（json格式）
     */
    @TableField("ext")
    private String ext;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", update = "now()")
    private Date updateTime;

    /**
     * 是否删除 0:未删除
     */
    @TableField("is_delete")
    private Integer isDelete;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;
}
