package com.akucun.account.proxy.dao.mapper;

import com.akucun.account.proxy.dao.model.CallLog;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface CallLogMapper {

    int deleteByPrimaryKey(Long id);

    int insert(CallLog record);

    int insertSelective(CallLog record);

    CallLog selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(CallLog record);

    int updateByPrimaryKey(CallLog record);

    List<CallLog> selectByBizIdAndType(CallLog record);

    int delete(@Param("expireDate") Date expireDate,
               @Param("types") List<String> types, @Param("deleteSize") int deleteSize);

}