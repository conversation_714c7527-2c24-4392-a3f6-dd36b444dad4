package com.akucun.account.proxy.dao.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @Author: silei
 * @Date: 2020/12/11
 * @desc: 代理白名单店主账户
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("agent_white_list_account")
public class AgentWhiteListAccount {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 店主客户编码
     */
    @TableField("customer_code")
    private String customerCode;

    /**
     * 店铺id
     */
    @TableField("shop_id")
    private String shopId;

    /**
     * 状态：1有效 、2无效
     */
    @TableField("status")
    private Integer status;

    /**
     * 创建日期
     */
    @TableField("create_date")
    private Date createDate;

    /**
     * 更新日期
     */
    @TableField("update_date")
    private Date updateDate;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;
}
