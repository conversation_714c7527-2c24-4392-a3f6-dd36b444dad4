package com.akucun.account.proxy.dao.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.util.Date;

/**
 * @Author: silei
 * @Date: 2020/9/2
 * @desc: 资金归集表
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName("account_op_transfer_amount")
public class AccountOpTransferAmount {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 主键
     */
    @TableId(value = "account_trade_id")
    private Long accountTradeId;

    /**
     * 流程明细orderNo
     */
    @TableField("detail_order_no")
    private String detailOrderNo;

    /**
     * 客户编码
     */
    @TableField("customer_code")
    private String customerCode;

    /**
     * 客户名称
     */
    @TableField("customer_name")
    private String customerName;

    /**
     * 客户类型
     */
    @TableField("customer_type")
    private String customerType;

    /**
     * 转账金额（分）
     */
    @TableField("amount")
    private Long amount;

    /**
     * 资金归集状态
     */
    @TableField("collect_status")
    private String collectStatus;

    /**
     * 资金划归状态
     */
    @TableField("allocate_status")
    private String allocateStatus;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", update = "now()")
    private Date updateTime;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 修改人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 是否删除 0:未删除
     */
    @TableField("is_delete")
    private Integer isDelete;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;
}
