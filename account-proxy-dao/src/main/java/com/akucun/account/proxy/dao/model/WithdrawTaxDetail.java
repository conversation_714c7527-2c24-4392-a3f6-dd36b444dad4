package com.akucun.account.proxy.dao.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: silei
 * @Date: 2021/3/9
 * @desc: 提现扣税明细记录
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("withdraw_tax_detail")
public class WithdrawTaxDetail {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 提现月份
     */
    @TableField("month")
    private String month;
    /**
     * 身份证
     */
    @TableField("identify_no")
    private String identifyNo;
    /**
     * 客户编码
     */
    @TableField("customer_code")
    private String customerCode;
    /**
     * 客户类型
     */
    @TableField("customer_type")
    private String customerType;
    /**
     * 客户名称
     */
    @TableField("customer_name")
    private String customerName;
    /**
     * 会员ID
     */
    @TableField("user_id")
    private String userId;
    /**
     * 店主身份用户编号
     */
    @TableField("seller_customer_code")
    private String sellerCustomerCode;
    /**
     * 身份类别
     */
    @TableField("identify_category")
    private String identifyCategory;
    /**
     * 申税注册ID
     */
    @TableField("tax_id")
    private String taxId;
    /**
     * 统一社会信用代码
     */
    @TableField("credit_code")
    private String creditCode;
    /**
     * 公司名称
     */
    @TableField("corp_name")
    private String corpName;
    /**
     * 法定代表人姓名
     */
    @TableField("corp_rep_name")
    private String corpRepName;
    /**
     * 提现申请编号
     */
    @TableField("withdraw_no")
    private String withdrawNo;
    /**
     * 申请金额
     */
    @TableField("amount")
    private BigDecimal amount;
    /**
     * 到账金额
     */
    @TableField("withdraw")
    private BigDecimal withdraw;
    /**
     * 税费金额
     */
    @TableField("tax_fee")
    private BigDecimal taxFee;
    /**
     * 状态:INIT,DOING,SUCC,FAIL
     */
    @TableField("status")
    private String status;
    /**
     * 当月累计提现金额(包含本次)
     */
    @TableField("curr_month_summary")
    private BigDecimal currMonthSummary;
    /**
     * 税率
     */
    @TableField("fee_rate")
    private String feeRate;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 是否删除，0:未删除，1删除
     */
    @TableField("is_delete")
    private Boolean isDelete;
    /**
     * 手续费金额
     */
    @TableField("service_fee")
    private BigDecimal serviceFee;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;
}
