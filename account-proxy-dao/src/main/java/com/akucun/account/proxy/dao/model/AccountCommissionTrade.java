package com.akucun.account.proxy.dao.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: silei
 * @Date: 2020/12/7
 * @desc: 分佣交易表
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
@TableName("account_commission_trade")
public class AccountCommissionTrade {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 客户编码
     */
    @TableField("customer_code")
    private String customerCode;

    /**
     * 客户类型
     */
    @TableField("customer_type")
    private String customerType;

    /**
     * 来源流水号
     */
    @TableField("source_bill_no")
    private String sourceBillNo;

    /**
     * 请求流水号
     */
    @TableField("trade_no")
    private String tradeNo;

    /**
     * 交易类型
     */
    @TableField("trade_type")
    private String tradeType;

    /**
     *
     */
    @TableField("amount")
    private BigDecimal amount;

    /**
     * 交易状态: I初始化，S成功，F失败，P处理中
     */
    @TableField("status")
    private String status;

    /**
     * 店铺编码
     */
    @TableField("shop_id")
    private String shopId;

    /**
     * 扩展字段
     */
    @TableField("ext")
    private String ext;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", update = "now()")
    private Date updateTime;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;
}
