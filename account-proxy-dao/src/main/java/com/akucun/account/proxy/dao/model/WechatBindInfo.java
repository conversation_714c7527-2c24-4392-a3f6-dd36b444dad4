package com.akucun.account.proxy.dao.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.NotBlank;

import java.util.Date;

/**
 * @Author: silei
 * @Date: 2021/5/2
 * @desc:
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("wechat_bind_info")
public class WechatBindInfo {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 客户编码
     */
    @TableField("customer_code")
    private String customerCode;
    /**
     * 客户名称
     */
    @TableField("customer_name")
    private String customerName;
    /**
     * 客户类型
     */
    @TableField("customer_type")
    private String customerType;
    /**
     * 微信openId
     */
    @TableField("open_id")
    private String openId;
    /**
     * 微信appId
     */
    @TableField("app_id")
    private String appId;

    /**
     * 渠道代码
     */
    @TableField("channel_code")
    private String channelCode;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;
    /**
     * 状态，0有效，1无效
     */
    @TableField("status")
    private Integer status;
    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

}
