package com.akucun.account.proxy.dao.mapper;

import com.akucun.account.proxy.dao.model.TradePhaseDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TradePhaseMapper extends BaseMapper<TradePhaseDO> {

    int updateResult(TradePhaseDO phaseDO);

    List<TradePhaseDO> selectByTradeId(@Param("tradeId") Long tradeId);

}
