package com.akucun.account.proxy.dao.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.util.Date;

/**
 * @Author: silei
 * @Date: 2020/8/26
 * @desc: 流程明细信息
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName("account_step_info")
public class AccountStepInfo {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 账户交易类型
     */
    @TableField("trade_type")
    private String tradeType;

    /**
     * 账户交易子类型
     */
    @TableField("sub_trade_type")
    private String subTradeType;

    /**
     * 步骤具体类名
     */
    @TableField("step_ref_class")
    private String stepRefClass;

    /**
     * 执行具体类名
     */
    @TableField("exec_ref_class")
    private String execRefClass;

    /**
     * 查询具体类名
     */
    @TableField("query_ref_class")
    private String queryRefClass;

    /**
     * 扩展字段
     */
    @TableField("ext_map")
    private String extMap;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", update = "now()")
    private Date updateTime;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 修改人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 是否删除 0:未删除
     */
    @TableField("is_delete")
    private Integer isDelete;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;
}
