package com.akucun.account.proxy.dao.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 付款渠道网关关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("transfer_channel_gateway")
public class TransferChannelGateway implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 网关代码
     */
    @TableField("gateway_code")
    private String gatewayCode;

    /**
     * 渠道代码：H5页面，XDAPPLET小程序
     */
    @TableField("channel_code")
    private String channelCode;

    /**
     * 应用ID
     */
    @TableField("app_id")
    private String appId;

    /**
     * 状态:启用0 禁用1
     */
    @TableField("status")
    private Long status;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 是否删除，0:未删除，1删除
     */
    @TableField("is_delete")
    private Boolean isDelete;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;
}
