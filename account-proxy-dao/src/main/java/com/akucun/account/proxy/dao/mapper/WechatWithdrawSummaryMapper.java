package com.akucun.account.proxy.dao.mapper;

import com.akucun.account.proxy.dao.model.WechatWithdrawSummary;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;

/**
 * @Author: silei
 * @Date: 2021/8/28
 * @desc:
 */
public interface WechatWithdrawSummaryMapper extends BaseMapper<WechatWithdrawSummary> {

    int addAmount(@Param("id") Long id, @Param("withdrawAmount") BigDecimal withdrawAmount);

    int subtractAmount(@Param("id") Long id, @Param("withdrawAmount") BigDecimal withdrawAmount);
}
