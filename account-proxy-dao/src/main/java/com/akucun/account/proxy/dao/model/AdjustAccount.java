package com.akucun.account.proxy.dao.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 账户调账操作表
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
@TableName("adjust_account")
public class AdjustAccount {

    /**
     * id主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 请求号
     */
    @TableField("request_no")
    private String requestNo;

    /**
     *调账类型：1：分账，2：转账，3：罚扣，4：账户中心调账
     */
    @TableField("adjustment_type")
    private String adjustmentType;

    /**
     *用户编码
     */
    @TableField("customer_code")
    private String customerCode;

    /**
     *用户类型
     */
    @TableField("customer_type")
    private String customerType;

    /**
     *状态：0：处理中，1：成功，2：失败，3：异常
     */
    @TableField("status")
    private String status;

    /**
     *金额
     */
    @TableField("amount")
    private BigDecimal amount;

    /**
     *备注
     */
    @TableField("remark")
    private String remark;

    /**
     *账户类型-key
     */
    @TableField("account_type_key")
    private String accountTypeKey;

    /**
     *操作人
     */
    @TableField("operator")
    private String operator;

    /**
     *创建时间
     */
    @TableField("create_Time")
    private Date createTime;

    /**
     *更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 来源单号
     */
    @TableField("source_bill_no")
    private String sourceBillNo;

    /**
     * 交易流水号
     */
    @TableField("trade_no")
    private  String tradeNo;

}
