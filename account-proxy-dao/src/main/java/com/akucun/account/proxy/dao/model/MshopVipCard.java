package com.akucun.account.proxy.dao.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 金饷VIP卡记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("mshop_vip_card")
public class MshopVipCard implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 卡号(邀请卡密)
     */
    @TableField("card_no")
    private String cardNo;

    @TableField("invite_code")
    private String inviteCode;

    /**
     * 卡类型：奖励金发放卡-BONUS
     */
    @TableField("card_type")
    private String cardType;

    /**
     * 金额(分)
     */
    @TableField("amount")
    private Long amount;

    /**
     * 爱豆编码
     */
    @TableField("customer_code")
    private String customerCode;

    /**
     * 状态：10-待激活;20-充值中/已激活;30-已核销;40-核销失败
     */
    @TableField("status")
    private Integer status;

    /**
     * 有效期开始时间
     */
    @TableField("begin_time")
    private Date beginTime;

    /**
     * 有效期结束时间
     */
    @TableField("end_time")
    private Date endTime;

    /**
     * 激活时间
     */
    @TableField("activation_time")
    private Date activationTime;

    /**
     * 核销时间
     */
    @TableField("verification_time")
    private Date verificationTime;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 是否删除，0:未删除，1删除
     */
    @TableField("is_delete")
    private Boolean isDelete;


}
