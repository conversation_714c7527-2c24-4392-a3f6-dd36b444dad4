package com.akucun.account.proxy.dao.mapper;

import com.akucun.account.proxy.dao.model.MentorInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MentorInfoMapper {
    int deleteByPrimaryKey(Long id);

    int insert(MentorInfo record);

    int insertSelective(MentorInfo record);

    MentorInfo selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(MentorInfo record);

    int updateByPrimaryKey(MentorInfo record);

    List<MentorInfo> loadAll();

    List<MentorInfo> loadByUserCode(@Param("userCode") String userCode);
}