package com.akucun.account.proxy.dao.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * @Author: silei
 * @Date: 2020/12/8
 * @desc: 延时（异步）处理任务
 */
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("post_action_item")
public class PostActionItem {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 任务名称
     */
    @TableId(value = "action_type")
    private String actionType;

    /**
     * 业务ID
     */
    @TableField("biz_id")
    private String bizId;

    /**
     * 业务参数
     */
    @TableId(value = "param")
    private String param;

    /**
     * 最近一次错误日志
     */
    @TableField("error_log")
    private String errorLog;


    /**
     * 备注信息
     */
    @TableId(value = "remark")
    private String remark;

    /**
     * 客户编码
     */
    @TableField("retry_nums")
    private Integer retryNums;

    /**
     * 下次重试时间unix数字
     */
    @TableField("next_retry_time")
    private LocalDateTime nextRetryTime;

    /**
     * 状态：1.成功；2.失败需要重试；3.失败不需要重试
     */
    @TableField("biz_status")
    private Integer bizStatus;

    /**
     * 是否可执行 1可执行 0 不可执行
     */
    @TableField("status")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", update = "now()")
    private Date updateTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getActionType() {
        return actionType;
    }

    public void setActionType(String actionType) {
        this.actionType = actionType;
    }

    public String getBizId() {
        return bizId;
    }

    public void setBizId(String bizId) {
        this.bizId = bizId;
    }

    public String getParam() {
        return param;
    }

    public void setParam(String param) {
        this.param = param;
    }

    public String getErrorLog() {
        return errorLog;
    }

    public void setErrorLog(String errorLog) {
        this.errorLog = errorLog;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getRetryNums() {
        return retryNums;
    }

    public void setRetryNums(Integer retryNums) {
        this.retryNums = retryNums;
    }

    public LocalDateTime getNextRetryTime() {
        return nextRetryTime;
    }

    public void setNextRetryTime(LocalDateTime nextRetryTime) {
        this.nextRetryTime = nextRetryTime;
    }

    public Integer getBizStatus() {
        return bizStatus;
    }

    public void setBizStatus(Integer bizStatus) {
        this.bizStatus = bizStatus;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
