package com.akucun.account.proxy.dao.mapper;

import com.akucun.account.proxy.dao.model.WithdrawApplyRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Author: silei
 * @Date: 2021/3/9
 * @desc:
 */
public interface WithdrawApplyRecordMapper extends BaseMapper<WithdrawApplyRecord> {

    /**
     * 更新提现记录状态
     *
     * @param withdrawNo
     * @param applyStatus
     * @param failReason
     * @return
     */
    int updateWithdrawRecordStatus(@Param("withdrawNo") String withdrawNo, @Param("applyStatus") String applyStatus, @Param("failReason") String failReason);

    /**
     * 查询提现汇总金额
     * @param identifyNo
     * @param month
     * @return
     */
    Long selectWithdrawSummary(@Param("identifyNo") String identifyNo, @Param("month") String month, @Param("summaryTaxRecord") Boolean summaryTaxRecord);
}
