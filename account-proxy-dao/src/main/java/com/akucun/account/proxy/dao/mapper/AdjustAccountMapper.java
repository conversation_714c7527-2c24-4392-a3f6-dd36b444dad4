package com.akucun.account.proxy.dao.mapper;

import com.akucun.account.proxy.dao.model.AdjustAccount;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

public interface AdjustAccountMapper extends BaseMapper<AdjustAccount> {

    Boolean updateStatusByRequestNo(@Param("status")String status, @Param("requestNo")String requestNo);

    Boolean updateValuesByRequestNo(@Param("status")String status,@Param("sourceBillNo")String sourceBillNo,@Param("tradeNo")String tradeNo, @Param("requestNo")String requestNo);
}
