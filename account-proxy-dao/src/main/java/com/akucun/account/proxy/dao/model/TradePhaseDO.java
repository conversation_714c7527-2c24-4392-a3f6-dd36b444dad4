package com.akucun.account.proxy.dao.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@TableName("trade_phase")
public class TradePhaseDO {

    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 交易ID
     */
    @TableField("trade_id")
    private Long tradeId;

    /**
     * 阶段编码
     */
    @TableField("code")
    private String code;

    /**
     * 业务参数
     */
    @TableField("biz_info")
    private String bizInfo;

    /**
     * 阶段状态
     */
    @TableField("status")
    private String status;

    /**
     * 错误码
     */
    @TableField("error_code")
    private String errorCode;

    /**
     * 错误信息
     */
    @TableField("error_message")
    private String errorMessage;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time", update = "now()")
    private Date updateTime;

}
