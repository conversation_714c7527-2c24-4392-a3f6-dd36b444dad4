package com.akucun.account.proxy.dao.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 付款网关表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("transfer_gateway")
public class TransferGateway implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 通道网关编号
     */
    @TableField("gateway_code")
    private String gatewayCode;

    /**
     * 商户网关类型（ALIPAY,WECHAT）
     */
    @TableField("gateway_type")
    private String gatewayType;

    /**
     * 商户号
     */
    @TableField("mch_code")
    private String mchCode;

    /**
     * 公司主体
     */
    @TableField("company_name")
    private String companyName;

    /**
     * 网关信息
     */
    @TableField("gateway_info")
    private String gatewayInfo;

    /**
     * 证书1
     */
    @TableField("certificate1")
    private String certificate1;

    /**
     * 证书2
     */
    @TableField("certificate2")
    private String certificate2;

    /**
     * 证书3
     */
    @TableField("certificate3")
    private String certificate3;

    /**
     * 证书4
     */
    @TableField("certificate4")
    private String certificate4;

    /**
     * 证书5
     */
    @TableField("certificate5")
    private String certificate5;

    /**
     * 证书过期时间
     */
    @TableField("cert_expiretime")
    private LocalDateTime certExpiretime;

    /**
     * 状态:启用0 禁用1
     */
    @TableField("status")
    private Long status;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 是否删除，0:未删除，1删除
     */
    @TableField("is_delete")
    private Boolean isDelete;


}
