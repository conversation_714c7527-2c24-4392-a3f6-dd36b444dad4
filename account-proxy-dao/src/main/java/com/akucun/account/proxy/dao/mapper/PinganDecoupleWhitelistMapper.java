package com.akucun.account.proxy.dao.mapper;

import com.akucun.account.proxy.dao.model.PinganDecoupleWhitelist;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: silei
 * @Date: 2021/12/20
 * @desc:
 */
public interface PinganDecoupleWhitelistMapper extends BaseMapper<PinganDecoupleWhitelist> {

    List<PinganDecoupleWhitelist> queryAntiClearingAccount(@Param("beginIndex") int beginIndex, @Param("endIndex") int endIndex, @Param("limit") int limit);

    List<PinganDecoupleWhitelist> queryWhitelistAgentAccount(@Param("beginIndex") int beginIndex, @Param("endIndex") int endIndex, @Param("limit") int limit);
}
