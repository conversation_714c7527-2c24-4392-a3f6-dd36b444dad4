/*
 * @Author: <PERSON>
 * @Date: 2025-04-16 15:14:10
 * @Description: Do not edit
 * @License: Copyright (c) 2025, Lee
 */
package com.akucun.account.proxy.dao.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.akucun.account.proxy.dao.model.OfflineAdjustAccount;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

public interface OfflineAdjustAccountMapper extends BaseMapper<OfflineAdjustAccount> {

    // 批量删除
    int batchDelete(@Param("ids") List<Long> ids, @Param("updateBy") String updateBy, @Param("deleteFlag") String deleteFlag);

    // 根据唯一约束查询
    OfflineAdjustAccount selectByUniqueKey(@Param("customerCode") String customerCode, @Param("accountTypeKey") String accountTypeKey, @Param("tradeNo") String tradeNo, @Param("tradeType") String tradeType);

}