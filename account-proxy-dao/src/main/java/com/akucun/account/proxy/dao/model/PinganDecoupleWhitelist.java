package com.akucun.account.proxy.dao.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.util.Date;

/**
 * @Author: silei
 * @Date: 2021/12/20
 * @desc:
 */
@TableName("pingan_decouple_whitelist")
public class PinganDecoupleWhitelist {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 客户编码
     */
    @TableField("customer_code")
    private String customerCode;

    /**
     * 客户类型
     */
    @TableField("customer_type")
    private String customerType;

    /**
     * 平安账户反清分状态：0 未反清分，1 已反清分
     */
    @TableField("anti_clearing_status")
    private Integer antiClearingStatus;

    /**
     * 是否白名单店长：0 否，1 是
     */
    @TableField("whitelist_shop_agent_flag")
    private Integer whitelistShopAgentFlag;

    /**
     * 白名单店长处理状态：0 未处理，1 已处理
     */
    @TableField("whitelist_status")
    private Integer whitelistStatus;

    /**
     * 状态，0有效，1无效
     */
    @TableField("status")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", update = "now()")
    private Date updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getCustomerType() {
        return customerType;
    }

    public void setCustomerType(String customerType) {
        this.customerType = customerType;
    }

    public Integer getAntiClearingStatus() {
        return antiClearingStatus;
    }

    public void setAntiClearingStatus(Integer antiClearingStatus) {
        this.antiClearingStatus = antiClearingStatus;
    }

    public Integer getWhitelistShopAgentFlag() {
        return whitelistShopAgentFlag;
    }

    public void setWhitelistShopAgentFlag(Integer whitelistShopAgentFlag) {
        this.whitelistShopAgentFlag = whitelistShopAgentFlag;
    }

    public Integer getWhitelistStatus() {
        return whitelistStatus;
    }

    public void setWhitelistStatus(Integer whitelistStatus) {
        this.whitelistStatus = whitelistStatus;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "PinganDecoupleWhitelist{" +
                "id=" + id +
                ", customerCode='" + customerCode + '\'' +
                ", customerType='" + customerType + '\'' +
                ", antiClearingStatus=" + antiClearingStatus +
                ", whitelistShopAgentFlag=" + whitelistShopAgentFlag +
                ", whitelistStatus=" + whitelistStatus +
                ", status=" + status +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}
