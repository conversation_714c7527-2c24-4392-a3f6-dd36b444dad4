package com.akucun.account.proxy.dao.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @Author: silei
 * @Date: 2021/3/11
 * @desc:
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("account_duplicate_check")
public class DuplicateCheck {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("unique_id")
    private String uniqueId;

    @TableField("create_time")
    private Date createTime;

}
