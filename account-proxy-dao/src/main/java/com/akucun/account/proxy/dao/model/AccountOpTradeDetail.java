package com.akucun.account.proxy.dao.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.util.Date;

/**
 * @Author: silei
 * @Date: 2020/8/26
 * @desc: 账户并更交易明细
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName("account_op_trade_detail")
public class AccountOpTradeDetail {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * account_op_trade表id
     */
    @TableId(value = "account_trade_id")
    private Long accountTradeId;

    /**
     * 明细请求流水号
     */
    @TableField("detail_order_no")
    private String detailOrderNo;

    /**
     * 交易类型
     */
    @TableField("trade_type")
    private String tradeType;

    /**
     * 交易子类型
     */
    @TableField("sub_trade_type")
    private String subTradeType;

    /**
     * 保留域
     */
    @TableField("reserve")
    private String reserve;

    /**
     * 交易状态
     */
    @TableField("status")
    private String status;

    /**
     * 返回码
     */
    @TableField("reply_code")
    private String replyCode;

    /**
     * 返回消息
     */
    @TableField("reply_msg")
    private String replyMsg;

    /**
     * 重试次数
     */
    @TableField("retry_times")
    private Integer retryTimes;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", update = "now()")
    private Date updateTime;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 修改人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 是否删除 0:未删除
     */
    @TableField("is_delete")
    private Integer isDelete;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;
}
