package com.akucun.account.proxy.dao.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 企业付款到零钱
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("payment_transfer")
public class PaymentTransfer implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 来源编号
     */
    @TableField("source_code")
    private String sourceCode;

    /**
     * 请求流水号
     */
    @TableField("source_no")
    private String sourceNo;

    /**
     * 付款交易流水号
     */
    @TableField("trade_no")
    private String tradeNo;

    /**
     * 用户openid
     */
    @TableField("open_id")
    private String openId;

    /**
     * 客户编码
     */
    @TableField("customer_code")
    private String customerCode;

    /**
     * 客户类型
     */
    @TableField("customer_type")
    private String customerType;

    /**
     * 客户名称
     */
    @TableField("customer_name")
    private String customerName;

    /**
     * 付款金额：分
     */
    @TableField("amount")
    private Long amount;

    /**
     * 订单渠道编号：H5页面，XDAPPLET小程序
     */
    @TableField("channel_code")
    private String channelCode;

    /**
     * 交易状态: I初始化，S成功，F失败，P处理中
     */
    @TableField("status")
    private String status;

    /**
     * 网关交易流水
     */
    @TableField("gateway_transaction_id")
    private String gatewayTransactionId;

    /**
     * 终端IP
     */
    @TableField("terminal_ip")
    private String terminalIp;

    /**
     * 付款完成时间
     */
    @TableField("transfer_success_time")
    private Date transferSuccessTime;

    /**
     * NO_CHECK:不校验真实姓名FORCE_CHECK:强校验真实姓名
     */
    @TableField("check_name")
    private String checkName;

    /**
     * 付款类型：WECHAT 微信，ALIPAY支付宝
     */
    @TableField("transfer_type")
    private String transferType;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 网关返回信息
     */
    @TableField("gateway_msg")
    private String gatewayMsg;

    /**
     * 付款商户号
     */
    @TableField("mch_code")
    private String mchCode;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", update = "now()")
    private Date updateTime;

    /**
     * 是否删除，0:未删除，1删除
     */
    @TableField("is_delete")
    private Boolean isDelete;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;
}
