package com.akucun.account.proxy.dao.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 付款请求日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("transfer_call_log")
public class TransferCallLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 付款交易流水号
     */
    @TableField("trade_no")
    private String tradeNo;

    /**
     * 请求类型
     */
    @TableField("request_type")
    private String requestType;

    /**
     * 请求原始报文信息
     */
    @TableField("req_message")
    private String reqMessage;

    /**
     * 返回原始报文信息
     */
    @TableField("res_message")
    private String resMessage;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 是否删除，0:未删除，1删除
     */
    @TableField("is_delete")
    private Boolean isDelete;


}
