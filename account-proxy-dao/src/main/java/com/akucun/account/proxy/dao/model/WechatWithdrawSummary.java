package com.akucun.account.proxy.dao.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

import java.math.BigDecimal;
import java.util.Date;


/**
 * @Author: silei
 * @Date: 2021/8/28
 * @desc:
 */


@TableName("wechat_withdraw_summary")
public class WechatWithdrawSummary {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 身份证
     */
    @TableField("identify_no")
    private String identifyNo;
    /**
     * 提现年份
     */
    @TableField("year")
    private String year;
    /**
     * 累计提现金额
     */
    @TableField("sum_withdraw_amt")
    private BigDecimal sumWithdrawAmt;

    /**
     * 累计提现成功次数
     */
    @TableField("withdraw_num")
    private Integer withdrawNum;

    /**
     * 是否删除，0:未删除，1删除
     */
    @TableField("is_delete")
    private Boolean isDelete;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getIdentifyNo() {
        return identifyNo;
    }

    public void setIdentifyNo(String identifyNo) {
        this.identifyNo = identifyNo;
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public BigDecimal getSumWithdrawAmt() {
        return sumWithdrawAmt;
    }

    public void setSumWithdrawAmt(BigDecimal sumWithdrawAmt) {
        this.sumWithdrawAmt = sumWithdrawAmt;
    }

    public Boolean getDelete() {
        return isDelete;
    }

    public void setDelete(Boolean delete) {
        isDelete = delete;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getWithdrawNum() {
        return withdrawNum;
    }

    public void setWithdrawNum(Integer withdrawNum) {
        this.withdrawNum = withdrawNum;
    }

    @Override
    public String toString() {
        return "WechatWithdrawSummary{" +
                "id=" + id +
                ", identifyNo='" + identifyNo + '\'' +
                ", year='" + year + '\'' +
                ", sumWithdrawAmt=" + sumWithdrawAmt +
                ", withdrawNum=" + withdrawNum +
                ", isDelete=" + isDelete +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}
