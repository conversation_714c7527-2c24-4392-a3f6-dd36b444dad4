package com.akucun.account.proxy.dao.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.util.Date;

/**
 * @Author: silei
 * @Date: 2020/8/26
 * @desc: 流程控制信息表
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName("account_step_control")
public class AccountStepControl {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 账户交易类型
     */
    @TableField("trade_type")
    private String tradeType;

    /**
     * 账户交易子类型
     */
    @TableField("sub_trade_type")
    private String subTradeType;

    /**
     * 是否是流程第一步：N 不是，Y 是
     */
    @TableField("is_head")
    private String isHead;

    /**
     * 后续流程配置信息
     */
    @TableField("next_step_map")
    private String nextStepMap;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", update = "now()")
    private Date updateTime;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 修改人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 是否删除 0:未删除
     */
    @TableField("is_delete")
    private Integer isDelete;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

}
