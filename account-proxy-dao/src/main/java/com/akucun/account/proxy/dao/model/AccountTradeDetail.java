package com.akucun.account.proxy.dao.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @Author: silei
 * @Date: 2020/11/30
 * @desc: 账户支付明细记录
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("account_trade_detail")
public class AccountTradeDetail {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 交易流水号
     */
    @TableField("trade_no")
    private String tradeNo;

    /**
     * 客户编码
     */
    @TableField("customer_code")
    private String customerCode;

    /**
     * 明细请求流水号
     */
    @TableField("detail_pay_no")
    private String detailPayNo;

    /**
     * 交易类型
     */
    @TableField("trade_type")
    private String tradeType;

    /**
     * 交易子类型
     */
    @TableField("sub_trade_type")
    private String subTradeType;

    /**
     * 交易状态
     */
    @TableField("status")
    private String status;

    /**
     * 返回码
     */
    @TableField("reply_code")
    private String replyCode;

    /**
     * 返回消息
     */
    @TableField("reply_msg")
    private String replyMsg;

    /**
     * 重试次数
     */
    @TableField("retry_times")
    private Integer retryTimes;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", update = "now()")
    private Date updateTime;

    /**
     * 是否删除 0:未删除
     */
    @TableField("is_delete")
    private Integer isDelete;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;
}
