package com.akucun.account.proxy.dao.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.util.Date;

/**
 * @Author: silei
 * @Date: 2020/8/26
 * @desc: 账户变更交易处理
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("account_op_trade")
public class AccountOpTrade {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 请求流水号
     */
    @TableField("order_no")
    private String orderNo;

    /**
     * 交易类型
     */
    @TableField("trade_type")
    private String tradeType;

    /**
     * 客户编码
     */
    @TableField("customer_code")
    private String customerCode;

    /**
     * 客户名称
     */
    @TableField("customer_name")
    private String customerName;

    /**
     * 昵称
     */
    @TableField("nick_name")
    private String nickName;

    /**
     * 手机号
     */
    @TableField("mobile")
    private String mobile;

    /**
     * 客户类型
     */
    @TableField("customer_type")
    private String customerType;

    /**
     * 账户属性
     */
    @TableField("account_property")
    private String accountProperty;

    /**
     * 保留域
     */
    @TableField("reserve")
    private String reserve;

    /**
     * 交易状态
     */
    @TableField("status")
    private String status;

    /**
     * 返回码
     */
    @TableField("reply_code")
    private String replyCode;

    /**
     * 返回消息
     */
    @TableField("reply_msg")
    private String replyMsg;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", update = "now()")
    private Date updateTime;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 修改人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 是否删除 0:未删除
     */
    @TableField("is_delete")
    private Integer isDelete;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

}
