<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.baomidou.ant.account.mapper.TransferCallLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.akucun.account.proxy.dao.model.TransferCallLog">
        <id column="id" property="id" />
        <result column="trade_no" property="tradeNo" />
        <result column="request_type" property="requestType" />
        <result column="req_message" property="reqMessage" />
        <result column="res_message" property="resMessage" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="is_delete" property="isDelete" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, trade_no, request_type, req_message, res_message, remark, create_time, update_time, is_delete
    </sql>

</mapper>
