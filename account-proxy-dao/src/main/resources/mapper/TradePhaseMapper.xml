<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.akucun.account.proxy.dao.mapper.TradePhaseMapper">
    <resultMap id="BaseResultMap" type="com.akucun.account.proxy.dao.model.TradePhaseDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="trade_id" property="tradeId" jdbcType="BIGINT"/>
        <result column="code" property="code" jdbcType="VARCHAR"/>
        <result column="biz_info" property="bizInfo" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="error_code" property="errorCode" jdbcType="VARCHAR"/>
        <result column="error_message" property="errorMessage" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 表字段 -->
    <sql id="Base_Column_List">
        id,trade_id,code,biz_info,`status`,
        error_code,error_message,create_time,update_time
    </sql>

    <insert id="insert" parameterType="com.akucun.account.proxy.dao.model.TradePhaseDO"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into trade_phase (
            trade_id,code,biz_info,`status`,
            error_code,error_message
        )
        values (
            #{tradeId,jdbcType=BIGINT},#{code,jdbcType=VARCHAR},#{bizInfo,jdbcType=VARCHAR},
            #{status,jdbcType=VARCHAR},#{errorCode,jdbcType=VARCHAR},#{errorMessage,jdbcType=VARCHAR}
        )
    </insert>

    <update id="updateResult" parameterType="com.akucun.account.proxy.dao.model.TradePhaseDO">
        update trade_phase set
        `status` = #{status,jdbcType=VARCHAR},
        error_code = #{errorCode,jdbcType=VARCHAR},
        error_message = #{errorMessage,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectByTradeId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from trade_phase
        where trade_id = #{tradeId,jdbcType=BIGINT}
    </select>

</mapper>