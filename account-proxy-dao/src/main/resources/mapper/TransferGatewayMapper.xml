<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.baomidou.ant.account.mapper.TransferGatewayMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.akucun.account.proxy.dao.model.TransferGateway">
        <id column="id" property="id" />
        <result column="gateway_code" property="gatewayCode" />
        <result column="gateway_type" property="gatewayType" />
        <result column="mch_code" property="mchCode" />
        <result column="company_name" property="companyName" />
        <result column="gateway_info" property="gatewayInfo" />
        <result column="certificate1" property="certificate1" />
        <result column="certificate2" property="certificate2" />
        <result column="certificate3" property="certificate3" />
        <result column="certificate4" property="certificate4" />
        <result column="certificate5" property="certificate5" />
        <result column="cert_expiretime" property="certExpiretime" />
        <result column="status" property="status" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="is_delete" property="isDelete" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, gateway_code, gateway_type, mch_code, company_name, gateway_info, certificate1, certificate2, certificate3, certificate4, certificate5, cert_expiretime, status, remark, create_time, update_time, is_delete
    </sql>

</mapper>
