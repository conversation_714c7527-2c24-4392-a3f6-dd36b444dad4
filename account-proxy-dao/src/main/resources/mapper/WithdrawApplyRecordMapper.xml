<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akucun.account.proxy.dao.mapper.WithdrawApplyRecordMapper">



    <!--根据提现结果更改提现状态-->
    <update id="updateWithdrawRecordStatus" parameterType="string">
        update withdraw_apply_record
        set apply_status =#{applyStatus},
            fail_reason=#{failReason}
        where withdraw_no = #{withdrawNo}
    </update>


    <select id="selectWithdrawSummary" resultType="java.lang.Long">
        select sum(amount) * 100 as amount
        from withdraw_apply_record
        where identify_no=#{identifyNo}
          and month = #{month}
          and apply_status in ('DOING','SUCC')
          <!-- 是否只统计需要累计扣税的提现记录, yes表示无需累计扣税, 非yes表示需要累计扣税 -->
          <if test="summaryTaxRecord != null and summaryTaxRecord">
            and (ext is null or ext NOT LIKE '%"noTaxFlag":"yes"%')
          </if>
    </select>
</mapper>
