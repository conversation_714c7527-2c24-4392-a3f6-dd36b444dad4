<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.baomidou.ant.acc.mapper.MshopVipCardMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.akucun.account.proxy.dao.model.MshopVipCard">
        <id column="id" property="id" />
        <result column="card_no" property="cardNo" />
        <result column="invite_code" property="inviteCode" />
        <result column="card_type" property="cardType" />
        <result column="amount" property="amount" />
        <result column="customer_code" property="customerCode" />
        <result column="status" property="status" />
        <result column="begin_time" property="beginTime" />
        <result column="end_time" property="endTime" />
        <result column="activation_time" property="activationTime" />
        <result column="verification_time" property="verificationTime" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="is_delete" property="isDelete" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, card_no, invite_code, card_type, amount, customer_code, status, begin_time, end_time, activation_time, verification_time, remark, create_time, update_time, is_delete
    </sql>

</mapper>
