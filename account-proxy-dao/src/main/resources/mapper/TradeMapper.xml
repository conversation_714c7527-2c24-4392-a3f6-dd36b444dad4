<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.akucun.account.proxy.dao.mapper.TradeMapper">
    <resultMap id="BaseResultMap" type="com.akucun.account.proxy.dao.model.TradeDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="trade_no" property="tradeNo" jdbcType="VARCHAR"/>
        <result column="biz_no" property="bizNo" jdbcType="VARCHAR"/>
        <result column="product_category" property="productCategory" jdbcType="VARCHAR"/>
        <result column="biz_type" property="bizType" jdbcType="VARCHAR"/>
        <result column="request_platform" property="requestPlatform" jdbcType="VARCHAR"/>
        <result column="biz_info" property="bizInfo" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="agreement_id" property="agreementId" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 表字段 -->
    <sql id="Base_Column_List">
        id,trade_no,biz_no,product_category,
        biz_type,request_platform,biz_info,remark,
        `status`,agreement_id,create_time,update_time
    </sql>

    <insert id="insert" parameterType="com.akucun.account.proxy.dao.model.TradeDO"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into trade (
            trade_no,biz_no,product_category,
            biz_type,request_platform,biz_info,
            remark,`status`,agreement_id
        )
        values (
            #{tradeNo,jdbcType=VARCHAR},#{bizNo,jdbcType=VARCHAR},#{productCategory,jdbcType=VARCHAR},
            #{bizType,jdbcType=VARCHAR},#{requestPlatform,jdbcType=VARCHAR},#{bizInfo,jdbcType=VARCHAR},
            #{remark,jdbcType=VARCHAR},#{status,jdbcType=VARCHAR},#{agreementId,jdbcType=VARCHAR}
        )
    </insert>

    <update id="updateResult" parameterType="com.akucun.account.proxy.dao.model.TradeDO">
        update trade set
        `status` = #{status,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectByTradeNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from trade
        where trade_no = #{tradeNo,jdbcType=VARCHAR}
    </select>

    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from trade
        where id = #{id,jdbcType=BIGINT}
    </select>

</mapper>