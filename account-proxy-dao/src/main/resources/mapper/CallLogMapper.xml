<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.akucun.account.proxy.dao.mapper.CallLogMapper" >
  <resultMap id="BaseResultMap" type="com.akucun.account.proxy.dao.model.CallLog" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="biz_id" property="bizId" jdbcType="VARCHAR" />
    <result column="type" property="type" jdbcType="VARCHAR" />
    <result column="request_message" property="requestMessage" jdbcType="LONGVARCHAR" />
    <result column="response_message" property="responseMessage" jdbcType="LONGVARCHAR" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="is_delete" property="isDelete" jdbcType="BIT" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, biz_id, `type`, request_message, response_message, remark, create_time, update_time, is_delete
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from call_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from call_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.akucun.account.proxy.dao.model.CallLog" >
    insert into call_log (id, biz_id, `type`,
      remark, create_time, update_time, 
      is_delete, request_message, response_message
      )
    values (#{id,jdbcType=BIGINT}, #{bizId,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{isDelete,jdbcType=BIT}, #{requestMessage,jdbcType=LONGVARCHAR}, #{responseMessage,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.akucun.account.proxy.dao.model.CallLog" >
    insert into call_log
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="bizId != null" >
        biz_id,
      </if>
      <if test="type != null" >
        `type`,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="isDelete != null" >
        is_delete,
      </if>
      <if test="requestMessage != null" >
        request_message,
      </if>
      <if test="responseMessage != null" >
        response_message,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="bizId != null" >
        #{bizId,jdbcType=VARCHAR},
      </if>
      <if test="type != null" >
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null" >
        #{isDelete,jdbcType=BIT},
      </if>
      <if test="requestMessage != null" >
        #{requestMessage,jdbcType=LONGVARCHAR},
      </if>
      <if test="responseMessage != null" >
        #{responseMessage,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.akucun.account.proxy.dao.model.CallLog" >
    update call_log
    <set >
      <if test="bizId != null" >
        biz_id = #{bizId,jdbcType=VARCHAR},
      </if>
      <if test="type != null" >
        `type` = #{type,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null" >
        is_delete = #{isDelete,jdbcType=BIT},
      </if>
      <if test="requestMessage != null" >
        request_message = #{requestMessage,jdbcType=LONGVARCHAR},
      </if>
      <if test="responseMessage != null" >
        response_message = #{responseMessage,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.akucun.account.proxy.dao.model.CallLog" >
    update call_log
    set biz_id = #{bizId,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      is_delete = #{isDelete,jdbcType=BIT},
      request_message = #{requestMessage,jdbcType=LONGVARCHAR},
      response_message = #{responseMessage,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByBizIdAndType" resultMap="BaseResultMap" >
    select
    <include refid="Base_Column_List" />
    from call_log
    where biz_id = #{bizId,jdbcType=VARCHAR}
    <if test="type != null" >
      and `type` = #{type,jdbcType=VARCHAR}
    </if>
  </select>

  <delete id="delete">
    <![CDATA[
        delete from call_log where update_time <= #{expireDate} and update_time  >= date_sub(#{expireDate}, interval 1 day)
		]]>
    <if test="types != null">
      and `type` in (
      <foreach collection="types" item="item" open="" separator=",">
        #{item}
      </foreach>
      )
    </if>
    limit #{deleteSize}
  </delete>
</mapper>