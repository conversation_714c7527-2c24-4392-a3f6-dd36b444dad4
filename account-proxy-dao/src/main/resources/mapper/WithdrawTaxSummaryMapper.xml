<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akucun.account.proxy.dao.mapper.WithdrawTaxSummaryMapper">



    <update id="addAmount" parameterType="com.akucun.account.proxy.dao.model.WithdrawTaxSummary">
        update withdraw_tax_summary
        set amount = amount + #{amount},
            withdraw = withdraw + #{withdraw},
            tax_fee = tax_fee + #{taxFee},
            identify_category = #{identifyCategory},
            service_fee = service_fee + #{serviceFee}
        where id = #{id}
    </update>

    <update id="subtractAmount" parameterType="com.akucun.account.proxy.dao.model.WithdrawTaxSummary">
        update withdraw_tax_summary
        set amount = amount - #{amount},
            withdraw = withdraw - #{withdraw},
            tax_fee = tax_fee - #{taxFee},
            service_fee = service_fee - #{serviceFee}
        where id = #{id}
    </update>
</mapper>
