<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.akucun.account.proxy.dao.mapper.MentorInfoMapper" >
  <resultMap id="BaseResultMap" type="com.akucun.account.proxy.dao.model.MentorInfo" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="user_code" property="userCode" jdbcType="VARCHAR" />
    <result column="user_type" property="userType" jdbcType="VARCHAR" />
    <result column="user_nick_name" property="userNickName" jdbcType="VARCHAR" />
    <result column="payee_name" property="payeeName" jdbcType="VARCHAR" />
    <result column="payee_bank_address" property="payeeBankAddress" jdbcType="VARCHAR" />
    <result column="payee_bank_no" property="payeeBankNo" jdbcType="VARCHAR" />
    <result column="merchant_id" property="merchantId" jdbcType="VARCHAR" />
    <result column="merchant_code" property="merchantCode" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, user_code, user_type, user_nick_name, payee_name, payee_bank_address, payee_bank_no, 
    merchant_id, merchant_code, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from mentor_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from mentor_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.akucun.account.proxy.dao.model.MentorInfo" >
    insert into mentor_info (id, user_code, user_type, 
      user_nick_name, payee_name, payee_bank_address, 
      payee_bank_no, merchant_id, merchant_code, 
      create_time, update_time)
    values (#{id,jdbcType=BIGINT}, #{userCode,jdbcType=VARCHAR}, #{userType,jdbcType=VARCHAR}, 
      #{userNickName,jdbcType=VARCHAR}, #{payeeName,jdbcType=VARCHAR}, #{payeeBankAddress,jdbcType=VARCHAR}, 
      #{payeeBankNo,jdbcType=VARCHAR}, #{merchantId,jdbcType=VARCHAR}, #{merchantCode,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.akucun.account.proxy.dao.model.MentorInfo" >
    insert into mentor_info
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="userCode != null" >
        user_code,
      </if>
      <if test="userType != null" >
        user_type,
      </if>
      <if test="userNickName != null" >
        user_nick_name,
      </if>
      <if test="payeeName != null" >
        payee_name,
      </if>
      <if test="payeeBankAddress != null" >
        payee_bank_address,
      </if>
      <if test="payeeBankNo != null" >
        payee_bank_no,
      </if>
      <if test="merchantId != null" >
        merchant_id,
      </if>
      <if test="merchantCode != null" >
        merchant_code,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="userCode != null" >
        #{userCode,jdbcType=VARCHAR},
      </if>
      <if test="userType != null" >
        #{userType,jdbcType=VARCHAR},
      </if>
      <if test="userNickName != null" >
        #{userNickName,jdbcType=VARCHAR},
      </if>
      <if test="payeeName != null" >
        #{payeeName,jdbcType=VARCHAR},
      </if>
      <if test="payeeBankAddress != null" >
        #{payeeBankAddress,jdbcType=VARCHAR},
      </if>
      <if test="payeeBankNo != null" >
        #{payeeBankNo,jdbcType=VARCHAR},
      </if>
      <if test="merchantId != null" >
        #{merchantId,jdbcType=VARCHAR},
      </if>
      <if test="merchantCode != null" >
        #{merchantCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.akucun.account.proxy.dao.model.MentorInfo" >
    update mentor_info
    <set >
      <if test="userCode != null" >
        user_code = #{userCode,jdbcType=VARCHAR},
      </if>
      <if test="userType != null" >
        user_type = #{userType,jdbcType=VARCHAR},
      </if>
      <if test="userNickName != null" >
        user_nick_name = #{userNickName,jdbcType=VARCHAR},
      </if>
      <if test="payeeName != null" >
        payee_name = #{payeeName,jdbcType=VARCHAR},
      </if>
      <if test="payeeBankAddress != null" >
        payee_bank_address = #{payeeBankAddress,jdbcType=VARCHAR},
      </if>
      <if test="payeeBankNo != null" >
        payee_bank_no = #{payeeBankNo,jdbcType=VARCHAR},
      </if>
      <if test="merchantId != null" >
        merchant_id = #{merchantId,jdbcType=VARCHAR},
      </if>
      <if test="merchantCode != null" >
        merchant_code = #{merchantCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.akucun.account.proxy.dao.model.MentorInfo" >
    update mentor_info
    set user_code = #{userCode,jdbcType=VARCHAR},
      user_type = #{userType,jdbcType=VARCHAR},
      user_nick_name = #{userNickName,jdbcType=VARCHAR},
      payee_name = #{payeeName,jdbcType=VARCHAR},
      payee_bank_address = #{payeeBankAddress,jdbcType=VARCHAR},
      payee_bank_no = #{payeeBankNo,jdbcType=VARCHAR},
      merchant_id = #{merchantId,jdbcType=VARCHAR},
      merchant_code = #{merchantCode,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="loadAll" resultMap="BaseResultMap" >
    select
    <include refid="Base_Column_List" />
    from mentor_info
  </select>

  <select id="loadByUserCode" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select
    <include refid="Base_Column_List" />
    from mentor_info
    where user_code = #{userCode,jdbcType=VARCHAR}
  </select>
</mapper>