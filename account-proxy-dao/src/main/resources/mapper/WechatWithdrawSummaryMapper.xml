<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akucun.account.proxy.dao.mapper.WechatWithdrawSummaryMapper">

    <update id="addAmount">
        update wechat_withdraw_summary
        set
            sum_withdraw_amt = sum_withdraw_amt + #{withdrawAmount},
            withdraw_num = withdraw_num + 1
        where id = #{id}

    </update>
    <update id="subtractAmount">
        update wechat_withdraw_summary
        set
            sum_withdraw_amt = sum_withdraw_amt - #{withdrawAmount},
            withdraw_num = withdraw_num - 1
        where id = #{id}

    </update>
</mapper>
