<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akucun.account.proxy.dao.mapper.AdjustAccountMapper">

    <update id="updateValuesByRequestNo" parameterType="string">
        update adjust_account set
        status =#{status},
        source_bill_no=#{sourceBillNo},
        trade_no=#{tradeNo}
        where request_no= #{requestNo}
    </update>

    <update id="updateStatusByRequestNo" parameterType="string">
        update adjust_account set
        status =#{status}
        where request_no= #{requestNo}
    </update>

</mapper>
