<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.baomidou.ant.account.mapper.TransferChannelGatewayMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.akucun.account.proxy.dao.model.TransferChannelGateway">
        <id column="id" property="id" />
        <result column="gateway_code" property="gatewayCode" />
        <result column="channel_code" property="channelCode" />
        <result column="app_id" property="appId" />
        <result column="status" property="status" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="is_delete" property="isDelete" />
        <result column="tenant_id" property="tenantId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, gateway_code, channel_code, app_id, status, remark, create_time, update_time, is_delete, tenant_id
    </sql>

</mapper>
