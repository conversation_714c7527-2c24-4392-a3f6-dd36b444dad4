<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.baomidou.ant.account.mapper.PaymentTransferMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.akucun.account.proxy.dao.model.PaymentTransfer">
        <id column="id" property="id" />
        <result column="source_code" property="sourceCode" />
        <result column="source_no" property="sourceNo" />
        <result column="trade_no" property="tradeNo" />
        <result column="open_id" property="openId" />
        <result column="customer_code" property="customerCode" />
        <result column="customer_type" property="customerType" />
        <result column="customer_name" property="customerName" />
        <result column="amount" property="amount" />
        <result column="channel_code" property="channelCode" />
        <result column="status" property="status" />
        <result column="gateway_transaction_id" property="gatewayTransactionId" />
        <result column="terminal_ip" property="terminalIp" />
        <result column="transfer_success_time" property="transferSuccessTime" />
        <result column="check_name" property="checkName" />
        <result column="transfer_type" property="transferType" />
        <result column="remark" property="remark" />
        <result column="mch_code" property="mchCode" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="is_delete" property="isDelete" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, source_code, source_no, trade_no, open_id, customer_code, customer_type, customer_name, amount, channel_code, status, gateway_transaction_id, terminal_ip, transfer_success_time, check_name, transfer_type, remark, mch_code, create_time, update_time, is_delete
    </sql>

</mapper>
