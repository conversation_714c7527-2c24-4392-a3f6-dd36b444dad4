<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akucun.account.proxy.dao.mapper.WithdrawServicefeeSummaryMapper">



    <update id="addAmount" parameterType="com.akucun.account.proxy.dao.model.WithdrawServicefeeSummary">
        update withdraw_servicefee_summary
        set
        service_fee = service_fee + #{serviceFee},
        withdraw_num = withdraw_num + 1
        where id = #{id}
    </update>

    <update id="subtractAmount" parameterType="com.akucun.account.proxy.dao.model.WithdrawTaxSummary">
        update withdraw_servicefee_summary
        set
        service_fee = service_fee - #{serviceFee},
        withdraw_num = withdraw_num - 1
        where id = #{id}
    </update>
</mapper>
