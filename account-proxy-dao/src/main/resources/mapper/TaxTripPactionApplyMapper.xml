<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akucun.account.proxy.dao.mapper.TaxTripPactionApplyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.akucun.account.proxy.dao.model.TaxTripPactionApply">
        <id column="id" property="id" />
        <result column="user_name" property="userName" />
        <result column="customer_code" property="customerCode" />
        <result column="customer_type" property="customerType" />
        <result column="bank_name" property="bankName" />
        <result column="bank_code" property="bankCode" />
        <result column="bank_card_no" property="bankCardNo" />
        <result column="paction_img_url" property="pactionImgUrl" />
        <result column="status" property="status" />
        <result column="reject_reason" property="rejectReason" />
        <result column="audit_no" property="auditNo" />
        <result column="social_credit_code" property="socialCreditCode" />
        <result column="merchant_name" property="merchantName" />
        <result column="legal_person_name" property="legalPersonName" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_name, customer_code, customer_type, bank_name, bank_card_no,
        paction_img_url, status, reject_reason, audit_no, social_credit_code, merchant_name, legal_person_name,
        create_time, update_time
    </sql>

</mapper>
