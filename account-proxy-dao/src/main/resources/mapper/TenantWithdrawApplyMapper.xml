<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akucun.account.proxy.dao.mapper.TenantWithdrawApplyMapper">

    <!--根据提现结果更改提现状态-->
    <update id="updateWithdrawRecordStatus" parameterType="string">
        update tenant_withdraw_apply
        set apply_status =#{applyStatus},
            fail_reason=#{failReason}
        where withdraw_no = #{withdrawNo}
    </update>

    <select id="selectWithdrawSummary" resultType="java.lang.Long">
        select sum(amount) * 100 as amount
        from tenant_withdraw_apply
        where identify_no=#{identifyNo}
        and month = #{month}
        and apply_status in ('DOING','SUCC')
    </select>
</mapper>
