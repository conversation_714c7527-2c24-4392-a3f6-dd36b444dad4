<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akucun.account.proxy.dao.mapper.OfflineAdjustAccountMapper">
    <!-- 新增 resultMap 映射 -->
    <resultMap id="BaseResultMap" type="com.akucun.account.proxy.dao.model.OfflineAdjustAccount">
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="customer_code" property="customerCode" jdbcType="VARCHAR" />
        <result column="customer_type" property="customerType" jdbcType="VARCHAR" />
        <result column="account_type_key" property="accountTypeKey" jdbcType="VARCHAR" />
        <result column="trade_no" property="tradeNo" jdbcType="VARCHAR" />
        <result column="source_bill_no" property="sourceBillNo" jdbcType="VARCHAR" />
        <result column="trade_type" property="tradeType" jdbcType="VARCHAR" />
        <result column="amount" property="amount" jdbcType="DECIMAL" />
        <result column="remark" property="remark" jdbcType="VARCHAR" />
        <result column="ext" property="ext" jdbcType="VARCHAR" />
        <result column="audit_by" property="auditBy" jdbcType="VARCHAR" />
        <result column="audit_status" property="auditStatus" jdbcType="INTEGER" />
        <result column="audit_time" property="auditTime" jdbcType="TIMESTAMP" />
        <result column="is_delete" property="isDelete" jdbcType="TINYINT" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="create_by" property="createBy" jdbcType="VARCHAR" />
        <result column="update_by" property="updateBy" jdbcType="VARCHAR" />
        <result column="audit_remark" property="auditRemark" jdbcType="VARCHAR" />
        <result column="adjust_status" property="adjustStatus" jdbcType="INTEGER" />
        <result column="fail_reason" property="failReason" jdbcType="VARCHAR" />
    </resultMap>

    <!-- 修改 batchDelete 中的 is_delete 处理逻辑 -->
    <update id="batchDelete">
        UPDATE offline_adjust_account
        SET is_delete = 1, update_by = #{updateBy}, trade_no = concat(trade_no, '_d_', #{deleteFlag})
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND is_delete = 0
        <!-- 只能删除待审核的数据 -->
        AND audit_status = 0
    </update>

    <!-- 根据唯一约束查询 -->
    <select id="selectByUniqueKey" resultType="com.akucun.account.proxy.dao.model.OfflineAdjustAccount">
        SELECT * FROM offline_adjust_account
        WHERE customer_code = #{customerCode}
        AND account_type_key = #{accountTypeKey}
        AND trade_no = #{tradeNo}
        AND trade_type = #{tradeType}
    </select>
</mapper>