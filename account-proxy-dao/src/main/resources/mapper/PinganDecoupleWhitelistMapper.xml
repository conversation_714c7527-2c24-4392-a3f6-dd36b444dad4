<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akucun.account.proxy.dao.mapper.PinganDecoupleWhitelistMapper">

    <sql id="Base_Column_List">
        id, customer_code, customer_type, anti_clearing_status,whitelist_shop_agent_flag,status, create_time, update_time
    </sql>

    <select id="queryAntiClearingAccount" resultType="com.akucun.account.proxy.dao.model.PinganDecoupleWhitelist">
        select
        <include refid="Base_Column_List"/>
        from pingan_decouple_whitelist
        where id > #{beginIndex}
        and id &lt;= #{endIndex}
        and anti_clearing_status = 0
        and status = 0
        order by id
        limit #{limit}

    </select>
    <select id="queryWhitelistAgentAccount" resultType="com.akucun.account.proxy.dao.model.PinganDecoupleWhitelist">
        select
        <include refid="Base_Column_List"/>
        from pingan_decouple_whitelist
        where id > #{beginIndex}
        and id &lt;= #{endIndex}
        and whitelist_shop_agent_flag = 1
        order by id
        limit #{limit}
    </select>
</mapper>
