<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.akucun.account.proxy.dao.mapper.RewardApplyMapper" >

    <update id="updatesStatusById" parameterType="com.akucun.account.proxy.dao.model.RewardApply">
        update reward_apply
            set status = #{status},
            <if test="remark != null and remark != ''">
                remark =#{remark},
            </if>
            <if test="batchNo != null and batchNo != ''">
                batch_no = #{batchNo},
            </if>
            <if test="attachmentPaths != null and attachmentPaths != ''">
                attachment_paths=#{attachmentPaths},
            </if>
            <if test="ext2 != null and ext2 != ''">
                ext2=#{ext2},
            </if>
            <if test="applyUserId != null and applyUserId != ''">
                apply_user_id=#{applyUserId},
            </if>
            update_time=now()
        where id = #{id}
    </update>
</mapper>