package com.akucun.account.proxy.facade.controller.account;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.facade.stub.account.WechatAuthFacade;
import com.akucun.account.proxy.facade.stub.others.account.vo.WechatInfoBindVO;
import com.akucun.account.proxy.service.acct.WechatAuthService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @Author: silei
 * @Date: 2021/5/2
 * @desc:
 */
@RestController
@Api(value = "微信鉴权相关", tags = {"微信鉴权相关"})
@RequestMapping("/api/account/proxy/wechat/auth")
public class WechatAuthController implements WechatAuthFacade {

    @Autowired
    private WechatAuthService wechatAuthService;

    @Override
    @ApiOperation(value = "微信信息绑定", notes = "微信信息绑定", httpMethod = "POST")
    @PostMapping("/bindInfo")
    public Result<Void> bindInfo(@RequestBody WechatInfoBindVO vo) {
        return wechatAuthService.bindInfo(vo);
    }

    @Override
    @ApiOperation(value = "微信信息解绑", notes = "微信信息解绑", httpMethod = "POST")
    @PostMapping("/unbind")
    public Result<Void> unbind(@RequestParam("customerCode") String customerCode, @RequestParam("customerType") String customerType) {
        return wechatAuthService.unbind(customerCode, customerType);
    }

    @Override
    @ApiOperation(value = "微信绑定信息查询", notes = "微信绑定信息查询", httpMethod = "POST")
    @GetMapping("/queryBindInfo")
    public Result<WechatInfoBindVO> queryBindInfo(@RequestParam("customerCode") String customerCode, @RequestParam("customerType") String customerType) {
        return wechatAuthService.queryBindInfo(customerCode, customerType);
    }
}
