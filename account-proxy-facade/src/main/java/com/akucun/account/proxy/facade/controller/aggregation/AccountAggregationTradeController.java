package com.akucun.account.proxy.facade.controller.aggregation;

import com.aikucun.common2.base.Result;
import com.aikucun.common2.utils.datamasking.DataMask;
import com.akucun.account.proxy.common.enums.ErrorCodeConstants;
import com.akucun.account.proxy.common.enums.ResponseEnum;
import com.akucun.account.proxy.common.utils.Results;
import com.akucun.account.proxy.dao.model.TenantWithdrawApply;
import com.akucun.account.proxy.facade.stub.aggregation.AccountAggregationTradeFacade;
import com.akucun.account.proxy.facade.stub.others.account.req.WithdrawQueryReq;
import com.akucun.account.proxy.facade.stub.others.account.vo.AccountWithdrawVO;
import com.akucun.account.proxy.facade.stub.others.aggregation.req.TenantWithdrawQueryReq;
import com.akucun.account.proxy.facade.stub.others.aggregation.req.WithdrawTaxCalcReq;
import com.akucun.account.proxy.facade.stub.others.aggregation.res.WithdrawServiceFeeSwitchResp;
import com.akucun.account.proxy.facade.stub.others.aggregation.res.WithdrawTaxCalcResp;
import com.akucun.account.proxy.facade.stub.others.aggregation.res.WithdrawTaxSwitchResp;
import com.akucun.account.proxy.facade.stub.others.aggregation.vo.TenantWithdrawApplyVO;
import com.akucun.account.proxy.service.acct.AccountWithdrawService;
import com.akucun.account.proxy.service.acct.TenantWithdrawService;
import com.akucun.account.proxy.service.acct.WithdrawTaxService;
import com.akucun.fps.account.client.api.H5Service;
import com.akucun.fps.account.client.model.WithdrawAuditDO;
import com.akucun.fps.account.client.model.query.WithdrawQueryDO;
import com.akucun.fps.account.client.model.vo.WithdrawTaxSwitchVO;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.mengxiang.base.common.log.Logger;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

/**
 * @Author: silei
 * @Date: 2020/11/15
 * @desc:
 */
@Api(value = "聚合层账户交易相关", tags = {"聚合层账户交易相关"})
@RestController
@RequestMapping("/api/account/aggregation/trade")
public class AccountAggregationTradeController implements AccountAggregationTradeFacade {

    @Reference(check = false)
    private H5Service h5Service;
    @Autowired
    private AccountWithdrawService accountWithdrawService;
    @Autowired
    private WithdrawTaxService withdrawTaxService;
    @Autowired
    private TenantWithdrawService tenantWithdrawService;

    @Value("${pingan.maintain.switch:false}")
    private boolean pinganMaintainSwitch;
    @Value("${pingan.maintain.tips:系统维护中，请稍后重试}")
    private String pinganMaintainTips;

    @Override
    @PostMapping(value = {"/queryWithdrawDetail"})
    @ApiOperation(value = "查询提现明细", notes = "查询提现明细", httpMethod = "POST")
    public Result<AccountWithdrawVO> queryWithdrawDetail(@RequestBody WithdrawQueryReq req) {
        Logger.info("AccountTradeController queryWithdrawDetail req:{}", DataMask.toJSONString(req));
        try {
            Result<AccountWithdrawVO> detailRes = accountWithdrawService.queryWithdrawDetail(req);
            if (detailRes.getSuccess() && detailRes.getData() != null) {
                return Result.success(detailRes.getData());
            }
            WithdrawQueryDO withdrawQueryDO = new WithdrawQueryDO();
            BeanUtils.copyProperties(req, withdrawQueryDO);
            com.akucun.fps.common.entity.Result<WithdrawAuditDO> result = h5Service.selectWithdrawDetail(withdrawQueryDO);
            if (Results.checkFpsResult(result)) {
                AccountWithdrawVO accountWithdrawVO = new AccountWithdrawVO();
                BeanUtils.copyProperties(result.getData(), accountWithdrawVO);
                return Result.success(accountWithdrawVO);
            } else {
                return Result.error(result.getErrorCode(), result.getErrorMessage());
            }
        } catch (Exception e) {
            Logger.error("AccountTradeController queryWithdrawDetail error:", e);
        }
        return null;
    }

//    @Override
//    @PostMapping(value = {"/agentApplyWithDraw"})
//    @ApiOperation(value = "店长提现申请", notes = "店长提现申请", httpMethod = "POST")
//    public Result<String> agentApplyWithDraw(@RequestBody AccountWithdrawVO accountWithdrawVO) {
//        Logger.info("AccountTradeController agentApplyWithDraw req:{}", DataMask.toJSONString(accountWithdrawVO));
//        try {
//            return accountWithdrawService.accountWithdraw(accountWithdrawVO);
//        } catch (Exception e) {
//            Logger.error("AccountTradeController agentApplyWithDraw error:", e);
//            return null;
//        }
//    }

    /**
     * 来源：饷店小程序-店主/长提现
     * @param accountWithdrawVO
     * @return
     */
    @Override
    @PostMapping(value = {"/applyBankWithDraw"})
    @ApiOperation(value = "店主提现申请", notes = "店主提现申请", httpMethod = "POST")
    public Result<String> applyBankWithDraw(@RequestBody AccountWithdrawVO accountWithdrawVO) {
        Logger.info("AccountTradeController sellerApplyWithDraw req:{}", DataMask.toJSONString(accountWithdrawVO));
        try {
            return accountWithdrawService.accountWithdraw(accountWithdrawVO);
        } catch (Exception e) {
            Logger.error("AccountTradeController sellerApplyWithDraw error:", e);
            return null;
        }
    }

    @Override
    @PostMapping(value = {"/taxClac"})
    @ApiOperation(value = "提现税费试算", notes = "提现税费试算", httpMethod = "POST")
    public Result<WithdrawTaxCalcResp> taxClac(@RequestBody WithdrawTaxCalcReq req) {
        Logger.info("AccountTradeController taxClac req:{}", DataMask.toJSONString(req));
        try {
            Result<WithdrawTaxCalcResp> result = withdrawTaxService.taxCalc(req);
            Logger.info("AccountTradeController taxClac res:{}", DataMask.toJSONString(result));
            if (result == null) {
                return Result.error(ResponseEnum.TAX_CALC_ERROR.getCode(), ResponseEnum.TAX_CALC_ERROR.getMessage());
            }
            return result;
        } catch (Exception e) {
            Logger.error("AccountTradeController taxClac error:", e);
        }
        return null;
    }

    @Override
    @GetMapping(value = {"/taxSwitch"})
    @ApiOperation(value = "提现扣税开关：on-开", notes = "提现扣税开关：on-开", httpMethod = "GET")
    public Result<WithdrawTaxSwitchVO> taxSwitch(@RequestParam("customerCode") String customerCode) {
        try {
            Result<WithdrawTaxSwitchResp> result = withdrawTaxService.taxSwitch(customerCode);
            if (result.getSuccess()) {
                WithdrawTaxSwitchVO vo = new WithdrawTaxSwitchVO();
                BeanUtils.copyProperties(result.getData(), vo);
                return Result.success(vo);
            }
            return Result.error(result.getCode(), result.getMessage());
        } catch (Exception e) {
            Logger.error("AccountTradeController taxSwitch error:", e);
        }
        return null;
    }

    @Override
    @GetMapping(value = {"/serviceFeeSwitch"}, produces = {"application/json;charset=utf-8"})
    public Result<WithdrawServiceFeeSwitchResp> serviceFeeSwitch(@RequestParam("identifyNo") String identifyNo) {
         return withdrawTaxService.serviceFeeSwitch(identifyNo);
    }


    @Override
    @ApiOperation(value = "查询是否存在处理中的提现", notes = "查询是否存在处理中的提现", httpMethod = "POST")
    @GetMapping(value = {"/queryProcessingWithdraw"}, produces = {"application/json;charset=utf-8"})
    public Result<Boolean> queryProcessingWithdraw(@RequestParam("customerCode")String customerCode, @RequestParam("customerType")String customerType) {
        return accountWithdrawService.processingWithdrawExist(customerCode, customerType);
    }

    @Override
    @ApiOperation(value = "提现到微信余额请求", notes = "提现到微信余额请求", httpMethod = "POST")
    @PostMapping(value = {"/applyWechatWithDraw"}, produces = {"application/json;charset=utf-8"})
    public Result<String> applyWechatWithDraw(@RequestBody AccountWithdrawVO accountWithdrawVO) {
        return accountWithdrawService.accountWechatWithdraw(accountWithdrawVO);
    }

    /**
     * 企业饷店-店主提现
     * @param vo
     * @return
     */
    @Override
    @PostMapping(value = {"/tenantApplyWithDraw"})
    @ApiOperation(value = "租户提现请求", notes = "租户提现请求", httpMethod = "POST")
    public Result<String> tenantApplyWithDraw(@RequestBody TenantWithdrawApplyVO vo) {
        Logger.info("AccountTradeController tenantApplyWithDraw req:{}", DataMask.toJSONString(vo));
        if(pinganMaintainSwitch){
            return Result.error(ErrorCodeConstants.SYSTEMMAINTAIN_900000.getErrorCode(), pinganMaintainTips);
        }
        TenantWithdrawApply apply = new TenantWithdrawApply();
        BeanUtils.copyProperties(vo, apply);
        return tenantWithdrawService.tenantWithdraw(apply);
    }

    @Override
    @PostMapping(value = {"/queryTenantWithdrawDetail"})
    @ApiOperation(value = "租户店主店长查询提现明细", notes = "租户店主店长查询提现明细", httpMethod = "POST")
    public Result<TenantWithdrawApplyVO> queryTenantWithdrawDetail(@RequestBody TenantWithdrawQueryReq req) {
        return tenantWithdrawService.queryTenantWithdrawDetail(req);
    }

}
