package com.akucun.account.proxy.facade.healthcheck;

import com.akucun.account.proxy.facade.stub.healthcheck.HealthCheckFacade;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Api(tags = "SLB健康检测入口")
public class HealthCheckFacadeImpl implements HealthCheckFacade {

    @ApiOperation(value = "SLB健康检测", notes = "只做健康检查,无任何作用")
    @GetMapping(value = "/slb/health")
    @Override
    public void health() {
        //do nothing
    }
}
