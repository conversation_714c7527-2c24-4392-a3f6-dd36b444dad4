package com.akucun.account.proxy.facade.controller.oa;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.facade.stub.oa.OAWorkflowBusinessFacade;
import com.akucun.account.proxy.facade.stub.others.oa.req.business.MultiSupplierCorporatePaymentRequest;
import com.akucun.account.proxy.service.oa.OAWorkflowBusinessService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Create on : 2025/1/14 17:31
 **/
@Api(tags = {"OA工作流业务接口"}, value = "OA工作流业务接口")
@RestController
public class OAWorkflowBusinessFacadeController implements OAWorkflowBusinessFacade {

    @Autowired
    private OAWorkflowBusinessService oaWorkflowBusinessService;

    @ApiOperation("多供应商对公付款")
    @Override
    public Result<String> multiSupplierCorporatePayment(@RequestBody @Validated MultiSupplierCorporatePaymentRequest request) {
        return oaWorkflowBusinessService.multiSupplierCorporatePayment(request);
    }
}
