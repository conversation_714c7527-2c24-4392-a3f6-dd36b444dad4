package com.akucun.account.proxy.facade.controller.compensation;

import com.aikucun.common2.base.Result;
import com.aikucun.common2.utils.datamasking.DataMask;
import com.akucun.account.proxy.common.enums.ResponseEnum;
import com.akucun.account.proxy.facade.stub.compensation.IntegrationApplyFacade;
import com.akucun.account.proxy.facade.stub.enums.CompensationFillPayTypeEnum;
import com.akucun.account.proxy.facade.stub.others.dto.req.CompensationFillPayApplyReq;
import com.akucun.account.proxy.service.compensation.CompensationPayApplyService;
import com.alibaba.fastjson.JSON;
import com.mengxiang.base.common.log.Logger;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : 供应链开放业务接口
 * @Create on : 2024/11/26 09:51
 **/
@RestController
@Api(value = "供应链开放业务接口")
public class IntegrationApplyController implements IntegrationApplyFacade {
    @Autowired
    private CompensationPayApplyService compensationPayApplyService;
    /**
     * 统一补款申请
     * @param req
     * @return
     */
    @Override
    @ApiOperation(value = "统一补款申请", notes = "统一补款申请", httpMethod = "POST")
    public Result<Boolean> fillapply(@RequestBody @Validated CompensationFillPayApplyReq req) {
        Logger.info("IntegrationApplyController fillapply req:{}", JSON.toJSONString(req));
        CompensationFillPayTypeEnum payTypeEnum = CompensationFillPayTypeEnum.getByCode(req.getType());
        if(null == payTypeEnum){
            Result.error(ResponseEnum.PARAM_EXCEPTION.getCode(), "补款类型不支持");
        }

        Pair<Boolean,String> result = compensationPayApplyService.deal(req);
        if(!ObjectUtils.isEmpty(result) && result.getLeft()){
            return Result.success(true);
        }else{
            return Result.error(ResponseEnum.PARAM_EXCEPTION.getCode(), !ObjectUtils.isEmpty(result)?result.getRight():"系统异常");
        }
    }
}
