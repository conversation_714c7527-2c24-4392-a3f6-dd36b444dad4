package com.akucun.account.proxy.facade.controller.test;

import com.aikucun.common2.base.Result;
import com.aikucun.common2.base.exception.BusinessException;
import com.akucun.account.proxy.common.utils.SpringBeanUtil;
import com.akucun.account.proxy.dao.mapper.AccountTenantCustomerMapper;
import com.akucun.account.proxy.dao.mapper.PostActionItemMapper;
import com.akucun.account.proxy.dao.model.PostActionItem;
import com.akucun.account.proxy.service.help.TenantCoreHelper;
import com.mengxiang.base.common.log.Logger;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

@RestController
@Api(value = "测试", tags = {"测试"})
@RequestMapping("/dore/api")
public class GuleTestTwoController {
    @GetMapping(value = "/clean")
    @ApiOperation(value = "清理后门")
    public void test(){
        Logger.info("into xxljob");
        List<Long> ids= Arrays.asList(
                236L
        );
        AccountTenantCustomerMapper accountTenantCustomerMapper = SpringBeanUtil.getBean("accountTenantCustomerMapper", AccountTenantCustomerMapper.class);
        for (Long id : ids) {
            accountTenantCustomerMapper.deleteById(id);
        }


        PostActionItemMapper postActionItemMapper = SpringBeanUtil.getBean("postActionItemMapper", PostActionItemMapper.class);
        PostActionItem postActionItem = postActionItemMapper.selectById(*********);
        postActionItem.setNextRetryTime(LocalDateTime.now());
        postActionItem.setRetryNums(1);
        postActionItemMapper.updateById(postActionItem);


    }


    @Autowired
    private TenantCoreHelper tenantCoreHelper;
    @GetMapping("/isCompanyShop")
    @ApiOperation("是否是企业饷店租户判断")
    public Result<Boolean> isCompanyShop(@RequestParam("tenantId") Long tenantId) throws BusinessException {
        Pair<Boolean, Integer> pair = tenantCoreHelper.isCompanyShop(tenantId);
        return Result.success(pair.getLeft());
    }
}



//    AccountOpTradeDetailMapper accountOpTradeDetailMapper=SpringBeanUtil.getBean("accountOpTradeDetailMapper", AccountOpTradeDetailMapper.class);
//        accountOpTradeDetailMapper.deleteById(632220);
      	/*AccountTenantCustomerMapper accountTenantCustomerMapper = SpringBeanUtil.getBean("accountTenantCustomerMapper", AccountTenantCustomerMapper.class);
        AccountTenantCustomer accountTenantCustomer=new AccountTenantCustomer();
        accountTenantCustomer.setTenantId("2199580135002404037");
        accountTenantCustomer.setCustomerCode("1100138622384645015");
        accountTenantCustomer.setStatus(0);
        accountTenantCustomer.setCustomerType("NMDL");
        accountTenantCustomer.setCreateTime(new Date());
        accountTenantCustomer.setUpdateTime(new Date());
        accountTenantCustomerMapper.insert(accountTenantCustomer);
        */
//AccountTenantCustomerMapper accountTenantCustomerMapper = SpringBeanUtil.getBean("accountTenantCustomerMapper", AccountTenantCustomerMapper.class);
//List<Long> ids= Arrays.asList(500023L,500422L);
//int i = accountTenantCustomerMapper.deleteBatchIds(ids);
		/*
      	WithdrawApplyRecordMapper withdrawApplyRecordMapper = SpringBeanUtil.getBean("withdrawApplyRecordMapper", WithdrawApplyRecordMapper.class);
        AccountWithdrawServiceImpl accountWithdrawService=SpringBeanUtil.getBean("accountWithdrawServiceImpl", AccountWithdrawServiceImpl.class);
        List<Long> ids=Arrays.asList(7887676L,7877763L);
        List<WithdrawApplyRecord> withdrawApplyRecords = withdrawApplyRecordMapper.selectBatchIds(ids);
        if(withdrawApplyRecords!=null && withdrawApplyRecords.size()>0){
            int i=0;
            for (WithdrawApplyRecord withdrawApplyRecord : withdrawApplyRecords) {
                NotifyWithdrawVO notifyWithdrawVO=new NotifyWithdrawVO();
                notifyWithdrawVO.setCustomerCode(withdrawApplyRecord.getCustomerType()+withdrawApplyRecord.getCustomerCode());
                notifyWithdrawVO.setAmount(withdrawApplyRecord.getAmount());
                notifyWithdrawVO.setResult(Boolean.TRUE);
                notifyWithdrawVO.setCustomerType(withdrawApplyRecord.getCustomerType());
                notifyWithdrawVO.setSourceBillNo(withdrawApplyRecord.getWithdrawNo());
                XxlJobLogger.log("提现成功-回调通知请求开始：request={}", JSON.toJSONString(notifyWithdrawVO));
                Result<Void> voidResult = accountWithdrawService.notifyWithdrawResult(notifyWithdrawVO);
                XxlJobLogger.log("提现成功-回调通知请求结束：result={}", JSON.toJSONString(voidResult));
              	if(voidResult.getSuccess()){
                    i++;
                }
            }
          	XxlJobLogger.log("成功记录数：count="+i);
        }
        */