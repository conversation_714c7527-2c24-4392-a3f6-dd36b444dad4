package com.akucun.account.proxy.facade.producer;

import cn.hutool.json.JSONUtil;
import com.aikucun.common2.log.Logger;
import com.aikucun.common2.utils.datamasking.DataMask;
import com.maihaoche.starter.mq.MQException;
import com.maihaoche.starter.mq.annotation.MQProducer;
import com.maihaoche.starter.mq.base.AbstractMQProducer;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.MessageQueueSelector;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.selector.SelectMessageQueueByHash;
import org.apache.rocketmq.common.message.Message;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Author: silei
 * @Date: 2020/8/10
 * @desc: MQ producer
 */
@MQProducer
public class BizMqProducer extends AbstractMQProducer {

    private static final MessageQueueSelector MESSAGE_QUEUE_SELECTOR = new SelectMessageQueueByHash();
    @Autowired
    private DefaultMQProducer producer;


    public void asyncSendMessage(final String topic, final String tag, String message, String key) throws MQException {
        Logger.info("send mq message, topic:{}, body：{}", topic, message);
        if (StringUtils.isBlank(topic)) {
            Logger.error("send message topic is blank, topic:{}", topic);
        } else if (StringUtils.isBlank(message)) {
            Logger.error("send message body is blank, topic:{}, tag:{}", topic, tag);
        } else {
            Message msg = new Message(topic, tag, message.getBytes());
            if (StringUtils.isNotEmpty(key)) {
                msg.setKeys(key);
            }
            try {
                super.asyncSend(msg, new SendCallback() {
                    @Override
                    public void onSuccess(SendResult sendResult) {
                        Logger.info("send message success, topic:{}, tag:{}, sendResult:{}:", topic, tag, DataMask.toJSONString(sendResult));
                    }

                    @Override
                    public void onException(Throwable throwable) {
                        Logger.error("send message fail, topic:{}, tag:{}, e:{}", topic, tag, throwable);
                    }
                });
            } catch (Exception var7) {
                Logger.error("send message error, topic:{}, tag:{}, e:{}", var7);
            }

        }
    }

    public void sendMessageOrderly(String topic, String tag, String message, String key) throws MQException {
        Logger.info("send mq message, topic:{}, body:{}", topic, message);
        if (StringUtils.isBlank(topic)) {
            Logger.error("send message topic is blank, topic:{}", topic);
        } else if (StringUtils.isBlank(message)) {
            Logger.error("send message body is blank, topic:{}, tag:{}", topic, tag);
        } else {
            Message msg = new Message(topic, tag, message.getBytes());
            if (StringUtils.isNotEmpty(key)) {
                msg.setKeys(key);
            }

            try {
                this.producer.send(msg, MESSAGE_QUEUE_SELECTOR, key);
            } catch (Exception var7) {
                Logger.error("send message error, e:", var7);
            }

        }
    }
}
