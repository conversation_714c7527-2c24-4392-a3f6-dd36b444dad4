package com.akucun.account.proxy.facade.config.sharding;

import com.akucun.account.center.common.constants.MagicNumber;
import com.akucun.account.center.common.util.StringCommonUtils;
import io.shardingsphere.api.algorithm.sharding.PreciseShardingValue;
import io.shardingsphere.api.algorithm.sharding.standard.PreciseShardingAlgorithm;

import java.util.Collection;

/**
 * @Author: silei
 * @Date: 2020/12/2
 * @desc:
 */
public final class TablePreciseShardingAlgorithm implements PreciseShardingAlgorithm<String> {

    @Override
    public String doSharding(final Collection<String> availableTargetNames, final PreciseShardingValue<String> shardingValue) {
        for (String each : availableTargetNames) {
            if (each.endsWith("_" + StringCommonUtils.tail2((Long.parseLong(StringCommonUtils.tail4(shardingValue.getValue())) % MagicNumber.int_64) + ""))) {
                return each;
            }
        }
        throw new UnsupportedOperationException();
    }
}