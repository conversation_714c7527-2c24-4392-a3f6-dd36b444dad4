package com.akucun.account.proxy.facade.controller.pingan;

import com.akucun.account.proxy.facade.stub.pingan.AcquireJointLineNumberFacade;
import com.akucun.fps.common.entity.Result;
import com.akucun.fps.common.entity.ResultList;
import com.akucun.fps.pingan.client.model.*;
import com.akucun.fps.pingan.client.model.po.BankNode;
import com.akucun.fps.pingan.feign.api.acquirejointlinenum.AcquireJointLineNumbeServiceApi;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@RestController
@Api(value = "对外提供获取大小银行联行号服务", tags = {"对外提供获取大小银行联行号服务"})
@RequestMapping("/api/account/proxy/pingan")
public class AcquireJointLineNumberController implements AcquireJointLineNumberFacade {

    @Resource
    private AcquireJointLineNumbeServiceApi acquireJointLineNumbeServiceApi;

    /**
     * 根据输入名模糊查询银行（总行名）列表
     *
     * @param bankName 银行名称
     * @return
     */
    @Override
    @PostMapping(value = "/selectByBankName")
    @ApiOperation(value = "根据输入名模糊查询银行（总行名）列表", notes = "根据输入名模糊查询银行（总行名）列表", httpMethod = "POST")
    public ResultList<BankInfoManageResp> selectByBankName(@RequestParam("bankName") String bankName) {
        return acquireJointLineNumbeServiceApi.selectByBankName(bankName);
    }

    /**
     * 模糊查询银行（总行名）列表
     *
     * @param req
     * @return
     */
    @Override
    @PostMapping(value = "/selectBankInfoManage")
    @ApiOperation(value = "模糊查询银行（总行名）列表", notes = "模糊查询银行（总行名）列表", httpMethod = "POST")
    public ResultList<BankInfoManageResp> selectBankInfoManage(@RequestBody BankInfoManageReq req) {
        return acquireJointLineNumbeServiceApi.selectBankInfoManage(req);
    }

    /**
     * 根据银行名称精确查询银行信息
     *
     * @param bankName
     * @return
     */
    @Override
    @PostMapping(value = "/selectByName")
    @ApiOperation(value = "根据银行名称精确查询银行信息", notes = "根据银行名称精确查询银行信息", httpMethod = "POST")
    public Result<BankInfoManageResp> selectByName(@RequestParam("bankName")String bankName) {
        return acquireJointLineNumbeServiceApi.selectByName(bankName);
    }

    /**
     * 根据bankCode获取银行信息
     *
     * @param bankCode
     * @return
     */
    @Override
    @PostMapping(value = "/selectByBankCode")
    @ApiOperation(value = "根据bankCode获取银行信息", notes = "根据bankCode获取银行信息", httpMethod = "POST")
    public Result<BankInfoManageResp> selectByBankCode(@RequestParam("bankCode") String bankCode) {
        return acquireJointLineNumbeServiceApi.selectByBankCode(bankCode);
    }

    /**
     * 获取省列表
     *
     * @return
     */
    @Override
    @PostMapping(value = "/selectProvince")
    @ApiOperation(value = "获取省列表", notes = "获取省列表", httpMethod = "POST")
    public ResultList<BankNode> selectProvince() {
        return acquireJointLineNumbeServiceApi.selectProvince();
    }

    /**
     * 获取市级信息
     *
     * @param code ：省编号
     * @return
     */
    @Override
    @PostMapping(value = "/selectBankCity")
    @ApiOperation(value = "获取市级信息", notes = "获取市级信息", httpMethod = "POST")
    public ResultList<BankCityInfo> selectBankCity(@RequestParam("code") String code) {
        return acquireJointLineNumbeServiceApi.selectBankCity(code);
    }

    /**
     * 模糊查询支行信息
     *
     * @param request
     * @return
     */
    @Override
    @PostMapping(value = "/selectSubBankCityPage")
    @ApiOperation(value = "模糊查询支行信息", notes = "模糊查询支行信息", httpMethod = "POST")
    public ResultList<SubBankInfoDO> selectSubBankCityPage(@RequestBody SubBankInfoReq request) {
        return acquireJointLineNumbeServiceApi.selectSubBankCityPage(request);
    }

    /**
     * 模糊查询农商行、合作社支行信息
     *
     * @param request
     * @return
     */
    @Override
    @PostMapping(value = "/selectSpecialSubBankCityPage")
    @ApiOperation(value = "模糊查询农商行、合作社支行信息", notes = "模糊查询农商行、合作社支行信息", httpMethod = "POST")
    public ResultList<SubBankInfoDO> selectSpecialSubBankCityPage(@RequestBody SubBankInfoReq request) {
        return acquireJointLineNumbeServiceApi.selectSpecialSubBankCityPage(request);
    }

    /**
     * 查询银行（总行名）列表
     *
     * @param channel
     * @return
     */
    @Override
    @PostMapping(value = "/selectBankInfoManageByChannel")
    @ApiOperation(value = "查询银行（总行名）列表", notes = "查询银行（总行名）列表", httpMethod = "POST")
    public ResultList<BankInfoManageResp> selectBankInfoManageByChannel(@RequestParam("channel") String channel) {
        return acquireJointLineNumbeServiceApi.selectBankInfoManageByChannel(channel);
    }
}
