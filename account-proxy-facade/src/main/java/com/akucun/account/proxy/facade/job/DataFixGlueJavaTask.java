package com.akucun.account.proxy.facade.job;

import com.akucun.account.proxy.client.center.AccountCenterClient;
import com.akucun.account.proxy.common.constant.ApplyStatus;
import com.akucun.account.proxy.common.enums.WithdrawChannelConstants;
import com.akucun.account.proxy.common.utils.EncryptUtils;
import com.akucun.account.proxy.dao.mapper.AccountTenantCustomerMapper;
import com.akucun.account.proxy.dao.mapper.WithdrawApplyRecordMapper;
import com.akucun.account.proxy.dao.model.AccountTenantCustomer;
import com.akucun.account.proxy.dao.model.WithdrawApplyRecord;
import com.akucun.account.proxy.facade.stub.enums.AccountKeyConstants;
import com.akucun.account.proxy.facade.stub.others.account.req.AccountOperateInfoReq;
import com.akucun.account.proxy.service.acct.TenantService;
import com.akucun.account.proxy.service.acct.WithdrawTaxService;
import com.akucun.account.proxy.service.common.SpringContextHolder;
import com.akucun.account.proxy.service.common.TaxService;
import com.akucun.account.proxy.service.postaction.task.WithdrawReceiptDownloadTask;
import com.akucun.common.Result;
import com.akucun.member.audit.model.dto.auth.QueryAuthStatusRespDTO;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.mengxiang.base.common.log.Logger;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Create on : 2025/4/8 20:43
 **/
public class DataFixGlueJavaTask  extends IJobHandler {
    String LOG_PREFIX = "数据修复DataFixGlueJavaTask-";

    @Resource
    private TaxService taxService;
    @Resource
    private AccountTenantCustomerMapper accountTenantCustomerMapper;
    @Resource
    private WithdrawApplyRecordMapper withdrawApplyRecordMapper;
    @Autowired
    private AccountCenterClient accountCenterClient;
    @Resource
    private WithdrawTaxService withdrawTaxService;
    @Override
    public ReturnT<String> execute(String params) throws Exception {
       /* if (StringUtils.isEmpty(params)) {
            Logger.error(LOG_PREFIX + "参数为空！");
            return ReturnT.FAIL;
        }

        String[] paramAray = params.split(",");
        if(paramAray.length == 2){
            Result<QueryAuthStatusRespDTO> resp = taxService.queryUserHighestAuth(paramAray[0],paramAray[1]);
            Logger.info(LOG_PREFIX + "resp:{}", JSON.toJSONString(resp));
        }*/
        /*AccountTenantCustomer record = new AccountTenantCustomer();
        record.setCustomerType("NMDL");
        record.setCustomerCode("1100141203018422113");
        record.setTenantId("2199580135002404037");
        record.setStatus(0);
        accountTenantCustomerMapper.insert(record);*/

        /*AccountOperateInfoReq req = new AccountOperateInfoReq();
        req.setCustomerCode("2691592859896123347");
        req.setCustomerName("贝比袋鼠好物甄选");
        req.setAccountTypeKey(AccountKeyConstants.AT.getName());
        req.setOperationType("CREATE");
        Result<Void> result = accountCenterClient.accountOperate(req);*/

        /*TradeInfo debitTradeInfo = new TradeInfo();
        String tradeid = "20250419003010004041750801A";
        debitTradeInfo.setAccountTypeKey("8D256656F0A9E0A959024F16A8C910B3");
        debitTradeInfo.setTradeType("TRADE_TYPE_050");
        debitTradeInfo.setCustomerCode("**********");
        debitTradeInfo.setAmount(new BigDecimal(11.50));
        debitTradeInfo.setRemark("运费补款(BAKC2505080642852500701000)");
        debitTradeInfo.setTradeNo(tradeid);
        debitTradeInfo.setSourceBillNo(tradeid);
        Logger.info(LOG_PREFIX + "账户中心操作,req:{}", JSON.toJSONString(debitTradeInfo));
        Result<Void> debitRslt = accountCenterController.dealTrade(debitTradeInfo);
        Logger.info(LOG_PREFIX + "账户中心操作,resp:{}", JSON.toJSONString(debitRslt));*/

        //饷店店主店长提现，直接置为失败
        /*String failWithdrawNos = "1,2,3";
        String[] failWithdrawNoArray = failWithdrawNos.split(",");
        for(String withdrawNoTmp : failWithdrawNoArray) {
            WithdrawApplyRecord record = withdrawApplyRecordMapper.selectOne(new QueryWrapper<WithdrawApplyRecord>().eq("withdraw_no", withdrawNoTmp));
            Logger.info(LOG_PREFIX + "提现记录:{}", JSON.toJSONString(record));
            if(!ObjectUtils.isEmpty(record)) {
                withdrawApplyRecordMapper.updateWithdrawRecordStatus(withdrawNoTmp, ApplyStatus.FAIL.name(), "算费落地数据,获取分布式锁失败");
            }
        }*/

        //饷店店主店长提现，通知提现成功
        String sussWithdrawNos = "************************,************************,********************6000,TX2502073288916700606000,TX2502073289271600606000,TX2502283319148500606000,TX2502283319237000606000,TX2504043383174600606000,TX2504303427648000606000,TX2505013430451000606000,TX2505133451745900606000,TX2505313482124800606000,TX2506213516759700606000,TX2506273525125300606000";
        String[] failWithdrawNoArray = sussWithdrawNos.split(",");
        for(String withdrawNoTmp : failWithdrawNoArray) {
            WithdrawApplyRecord record = withdrawApplyRecordMapper.selectOne(new QueryWrapper<WithdrawApplyRecord>().eq("withdraw_no", withdrawNoTmp));
            Logger.info(LOG_PREFIX + "提现记录:{}", JSON.toJSONString(record));
            if(!ObjectUtils.isEmpty(record)) {
                withdrawApplyRecordMapper.updateWithdrawRecordStatus(withdrawNoTmp, ApplyStatus.SUCC.name(), "");
                Logger.info(LOG_PREFIX + "更新提现记录为成功:{}", withdrawNoTmp);

                //提现成功扣税
                withdrawTaxService.withdrawSuccForTax(record);
                Logger.info(LOG_PREFIX + "创建提现扣税任务:{}", withdrawNoTmp);

                //新增拉取回执单地址定时任务
                SpringContextHolder.getBean(WithdrawReceiptDownloadTask.class).init(WithdrawChannelConstants.PINGAN.getName(),
                        withdrawNoTmp, null, null, false, record.getCustomerType());
                Logger.info(LOG_PREFIX + "新增拉取回执单地址定时任务:{}", withdrawNoTmp);
            }
        }

        String dd = "10jV8yAmSKMKU9Zv42kSApvY55rqx9dEXPasCjq8stswo0hJxPEqR2G0i4+GPemjVLiDDi1jZuvX4eGZ/TI1Ze6ccWtOLumb4BAyfYNLFoy2ekz0NNVJVk4QavfRUJMtSgLoWjzX9izID7M+H2nZhyD4klxUmY4hz1Q7UtFGoJtQg=";
        Logger.info(LOG_PREFIX + "解密字符串:{}", EncryptUtils.decrypt(dd));

        return ReturnT.SUCCESS;
    }



}
