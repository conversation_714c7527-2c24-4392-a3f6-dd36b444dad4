package com.akucun.account.proxy.facade.controller.account;

import com.aikucun.common2.utils.datamasking.DataMask;
import com.akucun.account.proxy.facade.stub.account.WithdrawServiceClientFacade;
import com.akucun.fps.account.client.api.WithdrawServiceClient;
import com.akucun.fps.account.client.model.WithdrawAuditDO;
import com.akucun.fps.account.client.model.query.WithdrawQueryDO;
import com.akucun.fps.common.entity.Query;
import com.akucun.fps.common.entity.Result;
import com.akucun.fps.common.entity.ResultList;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.mengxiang.base.common.log.Logger;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@Api(value = "提现服务接口", tags = {"提现服务接口"})
@RequestMapping("/api/account/proxy/accountWeb")
public class WithdrawServiceClientController implements WithdrawServiceClientFacade {
    @Reference(check = false)
    WithdrawServiceClient withdrawServiceClient;

    /**
     * 提现申请接口
     *
     * @param withdrawAuditDO
     * @return
     */
    @Override
    @ApiOperation(value = "提现申请接口", notes = "提现申请接口", httpMethod = "POST")
    @PostMapping(value = "/applyWithdraw", produces = "application/json;charset=utf-8")
    public Result<Void> applyWithdraw(@RequestBody WithdrawAuditDO withdrawAuditDO) {
        Logger.info("WithdrawServiceClient applyWithdraw request:{}", DataMask.toJSONString(withdrawAuditDO));
        Result<Void> result = withdrawServiceClient.applyWithdraw(withdrawAuditDO);
        Logger.info("WithdrawServiceClient applyWithdraw response:{}", DataMask.toJSONString(result));
        return result;
    }


    @Override
    @ApiOperation(value = "查询提现记录", notes = "查询提现记录", httpMethod = "POST")
    @PostMapping(value = "/selectPage", produces = "application/json;charset=utf-8")
    public ResultList<WithdrawAuditDO> selectPage(@RequestBody Query<WithdrawQueryDO> query) {
        Logger.info("WithdrawServiceClient selectPage req:{}", DataMask.toJSONString(query));
        ResultList<WithdrawAuditDO> resultList = withdrawServiceClient.selectPage(query);
        Logger.info("WithdrawServiceClient selectPage resultList:{}", DataMask.toJSONString(resultList));
        return resultList;
    }
}
