package com.akucun.account.proxy.facade.controller.account;

import com.aikucun.common2.base.Pagination;
import com.aikucun.common2.base.Result;
import com.aikucun.common2.log.Logger;
import com.aikucun.common2.utils.DateUtils;
import com.aikucun.common2.utils.datamasking.DataMask;
import com.akucun.account.proxy.common.enums.AdjustAccountStatusEnum;
import com.akucun.account.proxy.common.utils.RedisLock;
import com.akucun.account.proxy.domain.model.AccountAdjust;
import com.akucun.account.proxy.domain.model.PinganAdjust;
import com.akucun.account.proxy.facade.stub.account.AccountAdjustFacade;
import com.akucun.account.proxy.facade.stub.others.dto.req.AccountAdjustReq;
import com.akucun.account.proxy.facade.stub.others.dto.req.AdjustAccountQueryReq;
import com.akucun.account.proxy.facade.stub.others.dto.req.PinganAdjustReq;
import com.akucun.account.proxy.facade.stub.others.dto.res.AdjustAccountQueryRes;
import com.akucun.account.proxy.service.acct.AdjustAccountService;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.Objects;

@RestController
@Api(value = "账户调账相关", tags = {"账户调账相关"})
@RequestMapping("/api/account/proxy/adjust/account")
public class AccountAdjustController implements AccountAdjustFacade {

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private AdjustAccountService adjustAccountService;

    //开启后停止调账
    @Value("${adjustAccount.switch}")
    private Boolean adjustAccountSwitch;
    //单位为分
    @Value("${adjustAccount.limittime}")
    private Integer adjustAccountLimitTime;
    @Value("${accountProxy.accountAdjust.maxAccountAdjustLimit:500}")
    private String maxAccountAdjustLimit;
    @Value("${accountProxy.PinganAdjust.PinganTransfer.maxPinganTransferLimit:500}")
    private String maxPinganTransferLimit;
    @Value("${accountProxy.PinganAdjust.PinganTransfer.maxPinganClearingLimit:500}")
    private String maxPinganClearingLimit;


    @Override
    @ApiOperation(value = "账户调账查询")
    @PostMapping(value = "/query", produces = "application/json;charset=utf-8")
    public Pagination<AdjustAccountQueryRes> adjustAccountQuery(@RequestBody AdjustAccountQueryReq adjustAccountQueryReq) {
        Logger.info("调账查询参数adjustAccountQueryReq:{}",DataMask.toJSONString(adjustAccountQueryReq));
        return adjustAccountService.adjustAccountQuery(adjustAccountQueryReq);
    }

    @Override
    @ApiOperation(value = "平安账户调账")
    @PostMapping(value = "/pingan/adjust", produces = "application/json;charset=utf-8")
    public Result<Void> pinganAdjust(@RequestBody PinganAdjustReq pinganAdjustReq) {
        Logger.info("平安调账参数pinganAdjustReq:{}", JSON.toJSONString(pinganAdjustReq));
        //校验参数
        if(ObjectUtils.isEmpty(pinganAdjustReq)||pinganAdjustReq.getRequestNo().isEmpty()||pinganAdjustReq.getOperator().isEmpty()||
                pinganAdjustReq.getCustomerCode().isEmpty()||pinganAdjustReq.getCustomerType().isEmpty()||pinganAdjustReq.getAdjustmentType().isEmpty()||
                pinganAdjustReq.getRemark().isEmpty()||pinganAdjustReq.getAmount()==null
        ){
            return Result.error(9,"服务异常请重试");
        }

        //调账开关关闭则可调账
        if(adjustAccountSwitch){
            return Result.error(6,"未开启调账配置");
        }

        //加锁：同一用户在同一个交易类型上同一时间只允许一个人操作
        String lockKey=pinganAdjustReq.getCustomerCode()+"_"+pinganAdjustReq.getAdjustmentType();
        RedisLock lock = new RedisLock(redisTemplate,lockKey);
        if (!lock.tryLock()) {
            //设特殊编码
            return Result.error(8,"该用户被同时操作");
        }

        // 查询同一个操作人过去分钟内(apollo配置)对同一个customercode与同一个类型操作次数只能为0
        Date date=new Date();
        Date limitTime=DateUtils.addMinutes(date,adjustAccountLimitTime);
        if(!(adjustAccountService.selectOperation(pinganAdjustReq.getOperator(),pinganAdjustReq.getCustomerCode(),limitTime))){
            return Result.error(7,adjustAccountLimitTime+"分钟内同一操作人只能对同一个用户编码执行一次");
        }

        try {
            // 记录保存
            Pair<Integer,String> insertResult=adjustAccountService.insertPinganAdjudt(pinganAdjustReq);

            if(insertResult.getLeft() != 0){
                return Result.error(insertResult.getLeft(), insertResult.getRight());
            }

            // 构建模型
            PinganAdjust pinganAdjust = PinganAdjust.build(pinganAdjustReq);
            pinganAdjust.setMaxPinganClearingLimit(maxPinganClearingLimit);
            pinganAdjust.setMaxPinganTransferLimit(maxPinganTransferLimit);

            // 执行
            Pair<Integer,String> result = pinganAdjust.done();

            // 更新状态，构建响应
            if (Objects.equals(result.getLeft(), 0)) {
                if(adjustAccountService.updateStatusByRequestNo(AdjustAccountStatusEnum.SUCCESS.getValue(),pinganAdjustReq.getRequestNo())){
                    return Result.success();
                }else{
                    return Result.error(2,"调账成功，数据更新异常，请联系技术人员解决更新问题");
                }

            } else {
                if(!adjustAccountService.updateStatusByRequestNo(AdjustAccountStatusEnum.FAIL.getValue(),pinganAdjustReq.getRequestNo())){
                    return Result.error(result.getLeft(),"调账失败，"+result.getRight()+"；数据更新异常，请联系技术人员解决失败及更新问题");
                }
                return Result.error(result.getLeft(),"调账失败，"+result.getRight());
            }

        } catch (NumberFormatException e) {
            Logger.error("AccountAdjustController pinganAdjust error:{}",e);
            return Result.error(5,"服务异常请联系技术人员");
        } finally {
            lock.unlock();
        }
    }

    @Override
    @ApiOperation(value = "账户中心调账")
    @PostMapping(value = "/account/adjust", produces = "application/json;charset=utf-8")
    public Result<Void> accountAdjust( @RequestBody AccountAdjustReq accountAdjustReq) {
        Logger.info("账户中心调账参数：accountAdjustReq:{}", JSON.toJSONString(accountAdjustReq));
        //校验参数
        if(ObjectUtils.isEmpty(accountAdjustReq)||accountAdjustReq.getRequestNo().isEmpty()||accountAdjustReq.getOperator().isEmpty()||
                accountAdjustReq.getCustomerCode().isEmpty()||accountAdjustReq.getAccountTypeKey().isEmpty()||
                accountAdjustReq.getAdjustmentType().isEmpty()||accountAdjustReq.getRemark().isEmpty()||
                accountAdjustReq.getAmount()==null
        ){
            return Result.error(9,"服务异常请重试");
        }
        if ((ObjectUtils.isEmpty(accountAdjustReq.getSourceBillNo())&&!ObjectUtils.isEmpty(accountAdjustReq.getTradeNo()))||
                (!ObjectUtils.isEmpty(accountAdjustReq.getSourceBillNo())&&ObjectUtils.isEmpty(accountAdjustReq.getTradeNo()))
        ) {
            return Result.error(22,"来源单号与交易流水号需同时具备");
        }

        //调账开关关闭则可调账
        if(adjustAccountSwitch){
            return Result.error(6,"未开启调账配置，请联系技术人员开启");
        }

        //加锁：同一用户在同一个交易类型上同一时间只允许一个人操作
        String lockKey=accountAdjustReq.getCustomerCode()+"_"+accountAdjustReq.getAdjustmentType();
        RedisLock lock = new RedisLock(redisTemplate,lockKey);
        if (!lock.tryLock()) {
            return Result.error(8,"该用户被同时操作，请稍后再试");
        }
        // 查询同一个操作人过去分钟内(apollo配置)对同一个customercode与同一个类型操作次数只能为0
        Date date=new Date();
        Date limitTime=DateUtils.addMinutes(date,adjustAccountLimitTime);
        if(!(adjustAccountService.selectOperation(accountAdjustReq.getOperator(),accountAdjustReq.getCustomerCode(),limitTime))){
            return Result.error(7,adjustAccountLimitTime+"分钟内同一操作人只能对同一个用户编码执行一次");
        }

        try {
            // 记录保存
            Pair<Integer,String> insertResult=adjustAccountService.insertAccountAdjust(accountAdjustReq);
            if(insertResult.getLeft() != 0){
                return Result.error(Integer.valueOf(insertResult.getLeft()), insertResult.getRight());
            }

             // 构建模型
            AccountAdjust accountAdjust = AccountAdjust.build(accountAdjustReq);
            accountAdjust.setMaxAccountAdjustLimit(maxAccountAdjustLimit);

            // 执行
            Pair<Integer, String> result = accountAdjust.done();

            // 更新状态、来源单号、交易流水号，构建响应
            if (Objects.equals(result.getLeft(), 0)) {
            // 保存操作记录-成功
                if(adjustAccountService.updateValuesByRequestNo(AdjustAccountStatusEnum.SUCCESS.getValue(),accountAdjust.getSourceBillNo(),accountAdjust.getTradeNo(),accountAdjustReq.getRequestNo())){
                    return Result.success();
                }else{
                    return Result.error(2,"调账成功，数据更新异常，请联系技术人员解决更新问题");
                }

            } else {
                // 保存操作记录-失败
                if(!adjustAccountService.updateValuesByRequestNo(AdjustAccountStatusEnum.FAIL.getValue(),accountAdjust.getSourceBillNo(),accountAdjust.getTradeNo(),accountAdjustReq.getRequestNo())){
                    return Result.error(result.getLeft(),"调账失败，"+result.getRight()+"；数据更新异常，请联系技术人员解决失败及更新问题");
                }
                return Result.error(result.getLeft(),"调账失败，"+result.getRight());
            }

        } catch (Exception e) {
            Logger.error("AccountAdjustController accountAdjust error:{}",e);
            adjustAccountService.updateStatusByRequestNo(AdjustAccountStatusEnum.EXECPTION.getValue(),accountAdjustReq.getRequestNo());
            return Result.error(5,"服务异常请联系技术人员");
        } finally {
            lock.unlock();
        }
    }

}
