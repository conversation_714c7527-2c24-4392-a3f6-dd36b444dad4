package com.akucun.account.proxy.facade.controller.test;

import com.aikucun.common2.log.Logger;
import com.akucun.account.proxy.client.bonus.AccountMemberClient;
import com.akucun.account.proxy.client.customer.MerchantApplyApi;
import com.akucun.account.proxy.client.risk.RiskService;
import com.akucun.account.proxy.common.constant.CommonConstants;
import com.akucun.account.proxy.dao.mapper.TenantWithdrawApplyMapper;
import com.akucun.account.proxy.dao.model.RewardApply;
import com.akucun.account.proxy.dao.model.TenantWithdrawApply;
import com.akucun.account.proxy.facade.stub.enums.RewardTypeEnum;
import com.akucun.account.proxy.facade.stub.others.account.req.Notify;
import com.akucun.account.proxy.facade.stub.others.account.req.NotifyReq;
import com.akucun.account.proxy.service.acct.PromoTradeFactory;
import com.akucun.account.proxy.service.acct.PromoTradeHandler;
import com.akucun.account.proxy.service.acct.RewardApplyService;
import com.akucun.account.proxy.service.acct.WithdrawTaxService;
import com.akucun.account.proxy.service.consumer.SellerRegisterConsumer;
import com.akucun.account.proxy.service.help.TenantCoreHelper;
import com.akucun.account.proxy.service.postaction.task.AccountMonitorTask;
import com.akucun.account.proxy.servicea.account.bo.MemberRegisterBo;
import com.akucun.common.Result;
import com.akucun.member.api.vo.AccountRecordVO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;


@RestController
@Api(value = "门", tags = {"门"})
@RequestMapping("/api/door")
public class DoorController {
    @Autowired
    private AccountMonitorTask accountMonitorTask;

    @Autowired
    private SellerRegisterConsumer sellerRegisterConsumer;
    @Autowired
    private MerchantApplyApi merchantApplyApi;
    @Autowired
    private PromoTradeFactory factory;
    @Autowired
    private RewardApplyService rewardApplyService;
    @Autowired
    private RiskService riskService;
    @Autowired
    private TenantCoreHelper tenantCoreHelper;
    @Autowired
    private AccountMemberClient accountMemberClient;

    @GetMapping(value = "/postaction")
    @ApiOperation(value = "执行任务")
    public void postaction(){
        accountMonitorTask.execute("");
    }


    @ApiOperation(value = "账户注册")
    @PostMapping(value = "/account/register", produces = "application/json;charset=utf-8")
    public Result<String> accountRegister(@RequestBody MemberRegisterBo message) {
        if (!message.check()) {
            Logger.info("检查未通过, 账户开通请求：{}", JSON.toJSONString(message));
            return Result.error("检查未通过！");
        }
        boolean r = sellerRegisterConsumer.process(message, null);
        Logger.info("账户开通请求：{}, 账户开通结果：{}", JSON.toJSONString(message), Boolean.toString(r));
        return Result.success(Boolean.toString(r));
    }

    @ApiOperation(value = "解密")
    @GetMapping(value = "/decrypt")
    public com.akucun.common.Result<String> decrypt(@RequestParam(value = "data") String data) {
        com.aikucun.common2.base.Result<String> decryptRst= merchantApplyApi.dataencrypt(data, "decrypt");

        //Logger.info("decrypt:{}",JSON.toJSONString(decryptRst));
        if(!ObjectUtils.isEmpty(decryptRst) && decryptRst.getSuccess() && !StringUtils.isEmpty(decryptRst.getData())){
            System.out.println(data+"->"+decryptRst.getData());
            return com.akucun.common.Result.success(decryptRst.getData());
        }
        System.out.println(data+"->");
        return com.akucun.common.Result.error(decryptRst.getMessage());
    }


    @ApiOperation(value = "月勤奖通知上游营销")
    @GetMapping(value = "/notifyPromo")
    public Result<Void> notifyPromo(@RequestParam(value = "applyId") long applyId, @RequestParam(value = "status") String status,
            @RequestParam(value = "errorMsg") String errorMsg) {
        RewardApply record =rewardApplyService.load(applyId);
        RewardTypeEnum busiTypeEnum = RewardTypeEnum.getByCode(record.getBusiType());
        Result<Void> notifyRslt = factory.getHandler(busiTypeEnum).syncNotify(record.getRequestNo(), record.getBusiType(), record.getTransBillDate(), status, errorMsg);
        return notifyRslt;
    }

    @ApiOperation(value = "月勤奖通知上游营销")
    @GetMapping(value = "/risk")
    public Result<String> risk(@RequestParam(value = "nmCode") String nmCode,@RequestParam(value = "dt") String dt){
        Pair<Boolean, String> riskRslt =  riskService.checkName(nmCode,dt);
        if(riskRslt.getLeft()){
            return Result.success(riskRslt.getRight());
        }else{
            return Result.error(riskRslt.getRight());
        }
    }

    @ApiOperation(value = "检查是否为平台企业饷店租户")
    @GetMapping(value = "/checkPlatformSaasTenant")
    public Result<Boolean> checkPlatformTenant(@RequestParam(value = "tenantId") Long tenantId){
        Boolean checkRst =  tenantCoreHelper.isPlatformSaasTenant(tenantId);
        return Result.success(checkRst);
    }

    /*@ApiOperation(value = "检查奖励金账户记录是否存在")
    @GetMapping(value = "/checkAwardRecordByTradeNo")
    public Result<List<AccountRecordVO>> checkAwardRecordByTradeNo(@RequestParam(value = "customerCode") String customerCode,@RequestParam(value = "tradeNo") String tradeNo){
        Result<List<AccountRecordVO>> result =  accountMemberClient.queryOldBonusTrade(customerCode,tradeNo);
        return result;
    }*/

}
