package com.akucun.account.proxy.facade.account;

import com.aikucun.common2.base.Result;
import com.aikucun.common2.utils.datamasking.DataMask;
import com.akucun.account.proxy.dao.model.TransferChannelGateway;
import com.akucun.account.proxy.facade.stub.account.PayTransferFacade;
import com.akucun.account.proxy.facade.stub.others.dto.req.PaymentTransferReq;
import com.akucun.account.proxy.facade.stub.others.dto.res.PaymentTransferResp;
import com.akucun.account.proxy.service.transfer.PayTransferService;
import com.akucun.account.proxy.service.transfer.TransferChannelGatewayService;
import com.alibaba.fastjson.JSON;
import com.mengxiang.base.common.log.Logger;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


@RestController
@Api(value = "付款接口", tags = {"付款接口"})
@RequestMapping("/api/account/proxy")
public class PayTransferController implements PayTransferFacade {

    @Autowired
    private PayTransferService payTransferService;
    @Autowired
    private TransferChannelGatewayService gatewayService;

    @Override
    @PostMapping(value = "/payTransfer")
    public Result<PaymentTransferResp> payTransfer(@RequestBody @Validated PaymentTransferReq request) {
        Logger.info("付款接口请求参数：{}", DataMask.toJSONString(request));
        Result<PaymentTransferResp> result = payTransferService.transfer(request);
        Logger.info("付款接口请求参数：{}，返回参数：{}", request.getSourceNo(), DataMask.toJSONString(result));
        return result;
    }

    @PostMapping(value = "/transfer/removeGatewayCache")
    public Result<Void> removeGatewayCache(@RequestParam("mchCode") String mchCode) {
        gatewayService.removeRedisCache(mchCode);
        return Result.success();
    }

    @PostMapping(value = "/updateTransferChannelGateway")
    public Result<Void> removeGatewayCache(@RequestBody TransferChannelGateway transferChannelGateway) {
        gatewayService.saveOrUpdate(transferChannelGateway);
        return Result.success();
    }
}
