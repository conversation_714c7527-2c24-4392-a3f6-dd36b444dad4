package com.akucun.account.proxy.facade.consumer;

import cn.hutool.json.JSONUtil;
import com.aikucun.common2.base.Result;
import com.aikucun.common2.utils.datamasking.DataMask;
import com.akucun.account.proxy.facade.stub.account.AccountTradeFacade;
import com.akucun.account.proxy.facade.stub.enums.TradeType;
import com.akucun.account.proxy.facade.stub.others.dto.res.AccountTradeResponse;
import com.akucun.account.proxy.service.mq.CommonProducer;
import com.akucun.pay.base.common.constant.CommonConstant;
import com.akucun.pay.base.common.enums.ChannelCode;
import com.akucun.pay.base.common.enums.CommonErrorCode;
import com.akucun.pay.base.common.enums.PayStatus;
import com.akucun.pay.base.common.enums.PayType;
import com.akucun.pay.base.common.model.PayQueryBO;
import com.akucun.pay.base.common.model.PayResultBO;
import com.maihaoche.starter.mq.annotation.MQConsumer;
import com.maihaoche.starter.mq.base.AbstractMQPushConsumer;
import com.maihaoche.starter.mq.base.MessageBuilder;
import com.mengxiang.base.common.log.Logger;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@MQConsumer(instances = {
        @MQConsumer.Instance(
                instanceName = "DEFAULT", // 指定配置中的instance name
                topic = "AKC_NOTIFY_PAY_QUERY_TOPIC",
                tag = "AKC_ACCOUNT_V1",
                consumerGroup = "AKC_PAY_QUERY_AKC_ACCOUNT_V1_CONSUMER_GROUP")})
public class PayQueryConsumer extends AbstractMQPushConsumer<String> {
    @Autowired
    private AccountTradeFacade accountTradeFacade;
    @Autowired
    private CommonProducer commonProducer;

    @Override
    public boolean process(String message, Map<String, Object> extMap) {

        Logger.info("消费AKC_NOTIFY_PAY_QUERY_TOPIC消息,message:{},extMap:{}", message, DataMask.toJSONString(extMap));
        PayQueryBO payQueryBO = JSONUtil.toBean(message, PayQueryBO.class);
        String payType = payQueryBO.getPayType();
        String tradeNo = payQueryBO.getTradeNo();
        Result<AccountTradeResponse> ownResult = null;
        if(PayType.BALANCE_BONUS.getType().equals(payType)) {
            ownResult = accountTradeFacade.bonusAccountQuery(tradeNo, TradeType.PAY.getCode());
        } else if(PayType.BALANCE_AKC.getType().equals(payType)) {
            ownResult = accountTradeFacade.akcAccountQuery(tradeNo, TradeType.PAY.getCode());
        } else if(PayType.BALANCE_OPENAPI.getType().equals(payType)) {
            ownResult = accountTradeFacade.openApiAccountQuery(tradeNo, TradeType.PAY.getCode());
        }else if(PayType.BALANCE_XD.getType().equals(payType)) {
            ownResult = accountTradeFacade.xdAccountQuery(tradeNo, TradeType.PAY.getCode());
        }else if(PayType.ACCOUNT_POINT.getType().equals(payType)) {
            ownResult = accountTradeFacade.pointAccountQuery(tradeNo, TradeType.PAY.getCode());
        }

        PayResultBO payResultBO = null;
        if (!ownResult.getSuccess() && CommonErrorCode.PAY_FAILE.getCode().equals(ownResult.getCode())) {
            payResultBO = PayResultBO.builder()
                    .payType(payQueryBO.getPayType())
                    .bankType("")
                    .channelCode(ChannelCode.AKC_ACCOUNT.getCode())
                    .channelMchCode(payQueryBO.getMerchantCode())
                    .channelTransactionId("")
                    .paidAmount(new BigDecimal(0))
                    .status(PayStatus.C.getStatus())
                    .subOrders(null)
                    .successTime("")
                    .tradeNo(payQueryBO.getTradeNo())
                    .responseCode(ownResult.getCode()+"")
                    .responseMessage(ownResult.getMessage())
                    .build();
            // 发送mq
//            String msg = JSONUtil.toJsonStr(payResultBO);
            commonProducer.syncSend(MessageBuilder.of(payResultBO).topic(CommonConstant.AKC_PAY_RESULT_TOPIC).build());
            return true;
        } else if (ownResult.getSuccess() && Objects.nonNull(ownResult.getData())) {
            AccountTradeResponse resPayQueryDTO = ownResult.getData();
            if (PayStatus.S.getStatus().equals(resPayQueryDTO.getStatus()) || PayStatus.C.getStatus().equals(resPayQueryDTO.getStatus())) {
                payResultBO = PayResultBO.builder()
                        .payType(payQueryBO.getPayType())
                        .bankType("")
                        .channelCode(ChannelCode.AKC_ACCOUNT.getCode())
                        .channelMchCode(payQueryBO.getMerchantCode())
                        .channelTransactionId("")
                        .paidAmount(resPayQueryDTO.getAmount())
                        .status(resPayQueryDTO.getStatus())
                        .successTime(resPayQueryDTO.getSuccessTime())
                        .tradeNo(resPayQueryDTO.getTradeNo())
                        .responseCode(ownResult.getCode()+"")
                        .responseMessage(ownResult.getMessage())
                        .responseMsg("")
                        .requestMsg("")
                        .build();
                // 发送mq
//                String msg = JSONUtil.toJsonStr(payResultBO);
                commonProducer.syncSend(MessageBuilder.of(payResultBO).topic(CommonConstant.AKC_PAY_RESULT_TOPIC).build());
                return true;
            }
        }
        return true;
    }

}
