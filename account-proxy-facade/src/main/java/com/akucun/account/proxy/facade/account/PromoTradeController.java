package com.akucun.account.proxy.facade.account;

import com.akucun.account.proxy.dao.model.MentorInfo;
import com.akucun.account.proxy.facade.stub.account.PromoTradeFacade;
import com.akucun.account.proxy.facade.stub.enums.RewardTypeEnum;
import com.akucun.account.proxy.facade.stub.others.dto.req.*;
import com.akucun.account.proxy.facade.stub.others.dto.res.BonusPayInfoResp;
import com.akucun.account.proxy.facade.stub.others.dto.res.BonusPayResp;
import com.akucun.account.proxy.facade.stub.others.dto.res.OANotifyDTO;
import com.akucun.account.proxy.facade.stub.others.dto.res.TaxQueryResp;
import com.akucun.account.proxy.facade.stub.others.oa.req.OAWorkflowStatusNotifyRequest;
import com.akucun.account.proxy.service.acct.MentorInfoService;
import com.akucun.account.proxy.service.acct.PromoTradeFactory;
import com.akucun.account.proxy.service.acct.PromoTradeService;
import com.akucun.account.proxy.service.common.TaxService;
import com.akucun.common.Result;
import com.alibaba.fastjson.JSON;
import com.mengxiang.base.common.log.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : 营销-活动奖励发放-接口
 * @Create on : 2025/1/15 11:20
 **/
@RestController
public class PromoTradeController implements PromoTradeFacade {
    @Autowired
    private PromoTradeFactory factory;
    @Autowired
    private PromoTradeService promoTradeService;
    @Autowired
    private TaxService taxService;
    @Autowired
    private MentorInfoService mentorInfoService;
    @Value("${promo.oa.mock.switch:true}")
    private Boolean promoOAMock;
    @Value("${promo.notify.def.url:http://zuul.infra.akcstable.com/promo-activity-center/api/promo/achievement/userAwardDistributionCallback}")
    private String DEF_PRPMO_NOTIFY_URL;

    /**
     * 处理交易
     *
     * @param bonusPayReq
     * @return
     */
    @Override
    public Result<BonusPayResp> dealTrade(@RequestBody @Validated BonusPayReq bonusPayReq) {
        Logger.info("PromoTradeFacade 处理交易入参：{}", JSON.toJSONString(bonusPayReq));

        //01-业务参数校验
        RewardTypeEnum busiTypeEnum = RewardTypeEnum.getByCode(bonusPayReq.getBizType());
        if (ObjectUtils.isEmpty(busiTypeEnum)) {
            return Result.error("无法识别的业务类型");
        }
        if(bonusPayReq.getAmount().compareTo(BigDecimal.ZERO)<=0){
            return Result.error("下发金额必须大于0");
        }
        if(StringUtils.isEmpty(bonusPayReq.getNotifyUrl()) &&
                (busiTypeEnum == RewardTypeEnum.EMPOWERMENT_CAMP || busiTypeEnum == RewardTypeEnum.MONTHLY_BONUS)){
            bonusPayReq.setNotifyUrl(DEF_PRPMO_NOTIFY_URL);
        }

        //02-业务处理
        Result<BonusPayResp> resp = factory.getHandler(busiTypeEnum).dealTrade(bonusPayReq);
        Logger.info("PromoTradeFacade 处理交易响应：{}", JSON.toJSONString(resp));

        return resp;
    }

    /**
     * 批量处理交易
     *
     * @param batchBonusPayReq
     * @return
     */
    @Override
    public Result<BonusPayResp> batchDealTrade(@RequestBody @Validated BatchBonusPayReq batchBonusPayReq) {
        //01-业务参数校验
        if (CollectionUtils.isEmpty(batchBonusPayReq.getDetails())) {
            return Result.error("明细列表不能为空");
        }
        RewardTypeEnum busiTypeEnum = RewardTypeEnum.getByCode(batchBonusPayReq.getBizType());
        if (ObjectUtils.isEmpty(busiTypeEnum)) {
            return Result.error("无法识别的业务类型");
        }

        //02-业务处理
        Result<BonusPayResp> resp = factory.getHandler(busiTypeEnum).batchDeal(batchBonusPayReq);

        return resp;
    }

    //======================== 任务提交接口 ================================
    @Override
    public Result<BonusPayInfoResp> queryTradeInfo(@RequestBody @Validated BonusPayQueryReq bonusPayQueryReq) {
        Logger.info("任务提交-查询交易信息入参：{}", JSON.toJSONString(bonusPayQueryReq));
        RewardTypeEnum busiTypeEnum = RewardTypeEnum.getByCode(bonusPayQueryReq.getBizType());
        if (ObjectUtils.isEmpty(busiTypeEnum)) {
            return Result.error("无法识别的业务类型");
        }

        BonusPayInfoResp resp = promoTradeService.queryTradeInfo(bonusPayQueryReq);
        Logger.info("任务提交-查询交易信息结果：{}", JSON.toJSONString(resp));
        if (ObjectUtils.isEmpty(resp)) {
            return Result.error("未查询到交易信息");
        }

        return Result.success(resp);
    }

    @Override
    public Result<BonusPayResp> submit(@RequestBody @Validated BonusPaySubmitReq bonusPaySubmitReq) {
        Logger.info("任务提交-待确认交易信息入参：{}", JSON.toJSONString(bonusPaySubmitReq));
        RewardTypeEnum busiTypeEnum = RewardTypeEnum.getByCode(bonusPaySubmitReq.getBizType());
        if (ObjectUtils.isEmpty(busiTypeEnum)) {
            return Result.error("无法识别的业务类型");
        }
        if (busiTypeEnum == RewardTypeEnum.MENTOR_BONUS && CollectionUtils.isEmpty(bonusPaySubmitReq.getAllFjs())) {
            return Result.error("导师激励业务，附件不能为空");
        }
        if (StringUtils.isEmpty(bonusPaySubmitReq.getApplyUserNo()) || StringUtils.isEmpty(bonusPaySubmitReq.getApplyUserName())) {
            return Result.error("请人员工编号和名称不能为空");
        }
        //OA的测试环境使用了生产数据，因此提交人必须使用【李乐】。发布生产环境的时候关闭
        if(promoOAMock){
            bonusPaySubmitReq.setApplyUserNo("AK006152");
            bonusPaySubmitReq.setApplyUserName("李乐");
        }

        Result<BonusPayResp> resp = factory.getHandler(busiTypeEnum).submit(bonusPaySubmitReq);
        Logger.info("任务提交-交易信息确认结果：{}", JSON.toJSONString(bonusPaySubmitReq));

        return resp;
    }

    //================ 其他接口 ===========================

    /**
     * 税率查询
     *
     * @param taxQueryReq
     * @return
     */
    @Override
    public Result<TaxQueryResp> calc(@RequestBody TaxReq taxQueryReq) {
        return taxService.calc(taxQueryReq);
    }

    @Override
    public Result<List<TaxQueryResp>> batchCalc(@RequestBody BatchTaxReq batchTaxReq) {
        if (CollectionUtils.isEmpty(batchTaxReq.getTaxList())) {
            return Result.error("批量查询参数不能为空");
        }
        if (batchTaxReq.getTaxList().size() > 100) {
            return Result.error("批量查询参数不能超过100个");
        }
        return taxService.batchCalc(batchTaxReq);
    }

    //====================== OA相关接口=====================
    @Override
    public Result<Boolean> oaWorkflowNotify(@RequestBody OAWorkflowStatusNotifyRequest notifyMsg) {
        Logger.info("OA工作流状态通知：{}", JSON.toJSONString(notifyMsg));
        return Result.success(promoTradeService.notifyFromOA(notifyMsg));
    }

    @Override
    public Result<OANotifyDTO> queryStatus(@RequestBody @Validated BonusPayQueryReq queryReq) {
        Logger.info("营销-OA出款任务查询：{}", JSON.toJSONString(queryReq));
        RewardTypeEnum busiTypeEnum = RewardTypeEnum.getByCode(queryReq.getBizType());
        if (ObjectUtils.isEmpty(busiTypeEnum)) {
            return Result.error("无法识别的业务类型");
        }

        OANotifyDTO resp = promoTradeService.queryStatus(queryReq);
        if (ObjectUtils.isEmpty(resp)) {
            return Result.error("未查询到OA出款申请记录");
        }
        return Result.success(resp);
    }

    @Override
    public Result<Integer> saveOrUpdateMentorInfo(@RequestBody MentorInfoDTO mentorInfo) {
        MentorInfo mentorInfoRecord = new MentorInfo();
        BeanUtils.copyProperties(mentorInfo,mentorInfoRecord);
        return Result.success(mentorInfoService.saveOrUpdate(mentorInfoRecord));
    }
}
