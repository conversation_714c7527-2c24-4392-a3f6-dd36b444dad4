package com.akucun.account.proxy.facade.job;

import com.aikucun.common2.utils.DateUtils;
import com.akucun.account.center.client.model.TradeInfo;
import com.akucun.account.proxy.facade.controller.account.AccountCenterController;
import com.akucun.common.Result;
import com.alibaba.fastjson.JSON;
import com.mengxiang.base.common.log.Logger;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.io.*;
import java.math.BigDecimal;
import java.net.URL;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : 年终奖下发
 * @Create on : 2025/1/20 14:44
 **/
public class YearBonusGlueJavaTask extends IJobHandler {
    String LOG_PREFIX = "年终奖下发-";
    @Autowired
    private AccountCenterController accountCenterController;

    //任务入口
    @Override
    public ReturnT<String> execute(String params) throws Exception {
        if (StringUtils.isEmpty(params)) {
            Logger.error(LOG_PREFIX + "参数为空！");
            return ReturnT.FAIL;
        }

        String[] paramsArr = params.split(",");
        if (paramsArr.length <= 3) {
            String fileOssPath = paramsArr[0];
            long beginRowNum = 0l ;
            long endRowNum =  5000;
            if(paramsArr.length>=2){
                beginRowNum =  Long.valueOf(paramsArr[1]);
                if(paramsArr.length==3){
                    endRowNum =  Long.valueOf(paramsArr[2]);
                }
            }
            Logger.info(LOG_PREFIX + "解析url并且发放年终奖开始:url={},beginRowNum={},endRowNum={}", params,beginRowNum,endRowNum);
            sendYearBonusByUrl(fileOssPath,beginRowNum,endRowNum);
        } else {
            Logger.info(LOG_PREFIX + "手工下发放年终奖开始:参数={}", params);
            pay(params, 1l);
        }

        return ReturnT.SUCCESS;
    }

    private void sendYearBonusByUrl(String filePath, Long beginRowNum, Long endRowNum) {
        long startTime = System.currentTimeMillis();

        try {
            //01-下载文件到本地
            String localFilePath = getLocalFilePath(filePath);
            Logger.info(LOG_PREFIX + "下载文件到本地成功:{}", localFilePath);

            //02-业务处理
            List<String> dataList = readCsvAndSend(localFilePath,beginRowNum,endRowNum);
            Logger.info(LOG_PREFIX + "下发完成(存在异常记录{}条):{}", dataList.size(), JSON.toJSONString(dataList));

            //03-写入结果到文件
            if (!CollectionUtils.isEmpty(dataList)) {
                String csvFilePath = "output" + DateUtils.format(new Date(), "yyyyMMddHHmmssSSS") + ".csv";
                File outputFile = new File(csvFilePath);
                if (!outputFile.exists()) {
                    outputFile.createNewFile();
                }
                writeListToCsv(dataList, csvFilePath);
                Logger.info(LOG_PREFIX + "下发异常文件路径:{}", csvFilePath);
            }

        } catch (Exception e) {
            Logger.error(LOG_PREFIX + "下发失败", e);
        } finally {
            Logger.info(LOG_PREFIX + "下发结束,耗时{}分钟", (System.currentTimeMillis() - startTime) / 1000 / 60);
        }
    }

    private List<String> readCsvAndSend(String localFilePath, Long beginRowNum, Long endRowNum) {
        long beginTime = System.currentTimeMillis();
        List<String> failData = new ArrayList<>();
        long rowNum = 0l;
        long totalNum =0l;

        try {
            BufferedReader reader = new BufferedReader(new FileReader(localFilePath));
            String line;
            while ((line = reader.readLine()) != null) {
                //控制读取CSV文件的行数
                if (rowNum < beginRowNum) {
                    rowNum++;
                    continue;
                }
                if (rowNum > endRowNum) {
                    break;
                }

                //业务处理
                Pair<Boolean, String> restTmp = pay(line, totalNum);
                if (!restTmp.getLeft()) {
                    failData.add(line);
                }

                rowNum++;
                totalNum++;
            }

        } catch (Exception e) {
            Logger.error(LOG_PREFIX + "解析CSV文件并下发年终奖失败", e);
        } finally {
            Logger.info(LOG_PREFIX + "解析CSV文件并下发年终奖结束(共{}条),耗时{}分钟", totalNum, (System.currentTimeMillis() - beginTime) / 1000 / 60);
        }

        return failData;
    }


    private Pair<Boolean, String> pay(String dealingData, Long lineNum) {
        try {
            String[] values = dealingData.split(",");
            if (values.length < 6) {
                return Pair.of(false, "核心数据缺失");
            }

            TradeInfo tradeInfo = new TradeInfo();
            tradeInfo.setTradeNo(values[0]);
            tradeInfo.setCustomerCode(values[1]);
            tradeInfo.setAccountTypeKey(values[2]);
            tradeInfo.setTradeType(values[3]);
            tradeInfo.setAmount(new BigDecimal(values[4]));

            tradeInfo.setSourceBillNo(values[0]);
            tradeInfo.setRemark(values[5]);

            Result<Void> dealTradeRlt = accountCenterController.dealTrade(tradeInfo);
            Logger.info(LOG_PREFIX + "调用接口发放年终奖({}):req={},resp={}", lineNum, JSON.toJSONString(tradeInfo), JSON.toJSONString(dealTradeRlt));
            if (ObjectUtils.isEmpty(dealTradeRlt) || !dealTradeRlt.isSuccess()) {
                return Pair.of(false, ObjectUtils.isEmpty(dealTradeRlt) ? "发放失败" : dealTradeRlt.getMessage());
            }
        } catch (Exception e) {
            Logger.info(LOG_PREFIX + "调用接口发放年终奖失败({})", lineNum, e);
            return Pair.of(false, e.getMessage());
        }

        return Pair.of(true, "suss");
    }


    //======================基础方法：http文件下载===============================
    //01-下载文件到本地
    public String getLocalFilePath(String httpFileUrl) throws IOException {
        String localFilePath = UUID.randomUUID().toString() + ".csv";

        try {
            //重新构建本地临时文件
            File file = new File(localFilePath);
            if (file.exists()) {
                file.delete();
            }
            file.createNewFile();

            //拷贝文件
            downloadCSV(httpFileUrl, localFilePath);
            Logger.info(LOG_PREFIX + "CSV文件下载成功！");
        } catch (Exception e) {
            Logger.error(LOG_PREFIX + "下载CSV文件时发生错误: ", e);
        }

        return localFilePath;
    }

    public void downloadCSV(String fileUrl, String localFilePath) throws IOException {
        URL url = new URL(fileUrl);
        int bufferSize = 50 * 1024; // 50KB

        try {
            BufferedInputStream buffin = new BufferedInputStream(url.openStream(), bufferSize);
            BufferedOutputStream buffout = new BufferedOutputStream(new FileOutputStream(localFilePath), bufferSize);
            byte[] buffer = new byte[bufferSize];
            int bytesRead;
            while ((bytesRead = buffin.read(buffer)) != -1) {
                buffout.write(buffer, 0, bytesRead);
            }
            buffout.flush();
            buffout.close();
            buffin.close();
        } catch (Exception e) {
            Logger.error(LOG_PREFIX + "下载CSV文件时发生错误: ", e);
        }
    }

    //02-写入结果到文件
    public static void writeListToCsv(List<String> dataList, String filePath) throws IOException {
        BufferedWriter writer = new BufferedWriter(new FileWriter(filePath));
        try {
            for (String item : dataList) {
                writer.write(item);
                writer.newLine(); // 写入换行符
            }
            writer.flush();
        } catch (IOException e) {
            Logger.warn("文件写入失败", e);
        } finally {
            writer.close();
        }
    }
}
