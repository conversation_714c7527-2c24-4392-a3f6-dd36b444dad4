package com.akucun.account.proxy.facade.account;

import com.aikucun.common2.base.Result;
import com.aikucun.common2.utils.datamasking.DataMask;
import com.akucun.account.proxy.common.constant.CommonConstants;
import com.akucun.account.proxy.common.enums.ResponseEnum;
import com.akucun.account.proxy.common.enums.ResultStatus;
import com.akucun.account.proxy.common.exception.AccountProxyException;
import com.akucun.account.proxy.facade.stub.account.AccountTradeFacade;
import com.akucun.account.proxy.facade.stub.enums.TradeType;
import com.akucun.account.proxy.facade.stub.others.dto.req.AccountTradeRequest;
import com.akucun.account.proxy.facade.stub.others.dto.res.AccountTradeResponse;
import com.akucun.account.proxy.service.trade.AccountTradeService;
import com.akucun.account.proxy.service.trade.bo.AccountTradeResp;
import com.alibaba.fastjson.JSON;
import com.mengxiang.base.common.log.Logger;
import io.swagger.annotations.Api;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


@RestController
@Api(value = "账户交易相关", tags = {"账户交易相关"})
@RequestMapping("/api/account/proxy")
public class AccountTradeController implements AccountTradeFacade {

    @Autowired
    private AccountTradeService accountTradeService;

    @Override
    public Result<Boolean> bonusAccountTrade(@RequestBody AccountTradeRequest request) {
        try {
            Logger.info("AccountTradeController bonusAccountTrade req：{}", DataMask.toJSONString(request));
            AccountTradeResp response = accountTradeService.accountTrade(request, CommonConstants.BONUS_ACCOUNT_PAY_TRADE_TYPE);
            Result<Boolean> result = buildTradeResult(response);
            Logger.info("AccountTradeController bonusAccountTrade res：{}", DataMask.toJSONString(result));
            return result;
        } catch (AccountProxyException e) {
            Logger.warn("AccountTradeController bonusAccountTrade warn：{}", e);
            return Result.error(e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            Logger.error("AccountTradeController bonusAccountTrade error：{}", e);
            return Result.error(ResponseEnum.SYSTEM_EXCEPTION.getCode(), ResponseEnum.SYSTEM_EXCEPTION.getMessage());
        }
    }


    @Override
    public Result<AccountTradeResponse> bonusAccountQuery(@RequestParam(name = "tradeNo") String tradeNo, @RequestParam(name = "tradeType") Integer tradeType) {
        if (StringUtils.isEmpty(tradeNo) || tradeType == null) {
            return Result.error(ResponseEnum.PARAM_EXCEPTION.getCode(), ResponseEnum.PARAM_EXCEPTION.getMessage());
        }
        try {
            Logger.info("AccountTradeController bonusAccountQuery req：{}-{}", tradeNo, tradeType);
            String acctTradeType = null;
            if (tradeType.equals(TradeType.PAY.getCode())) {
                acctTradeType = CommonConstants.BONUS_ACCOUNT_PAY_TRADE_TYPE;
            } else {
                acctTradeType = CommonConstants.BONUS_ACCOUNT_REFUND_TRADE_TYPE;
            }
            AccountTradeResponse response = accountTradeService.bonusAccountQuery(tradeNo, acctTradeType);
            Result<AccountTradeResponse> result = Result.success(response);
            Logger.info("AccountTradeController bonusAccountQuery req：{}-{}，res：{}", tradeNo, acctTradeType, DataMask.toJSONString(result));
            return result;
        } catch (AccountProxyException e) {
            Logger.warn("AccountTradeController bonusAccountQuery warn：{}", e);
            return Result.error(e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            Logger.error("AccountTradeController bonusAccountQuery error：{}", e);
            return Result.error(ResponseEnum.SYSTEM_EXCEPTION.getCode(), ResponseEnum.SYSTEM_EXCEPTION.getMessage());
        }
    }

    @Override
    public Result<Boolean> bonusAccountRefund(@RequestBody AccountTradeRequest request) {
        try {
            Logger.info("AccountTradeController bonusAccountRefund req：{}", DataMask.toJSONString(request));
            AccountTradeResp response = accountTradeService.accountTrade(request, CommonConstants.BONUS_ACCOUNT_REFUND_TRADE_TYPE);
            Result<Boolean> result = buildTradeResult(response);
            Logger.info("AccountTradeController bonusAccountRefund res：{}", DataMask.toJSONString(result));
            return result;
        } catch (AccountProxyException e) {
            Logger.warn("AccountTradeController bonusAccountRefund warn：{}", e);
            return Result.error(e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            Logger.error("AccountTradeController bonusAccountRefund error：{}", e);
            return Result.error(ResponseEnum.SYSTEM_EXCEPTION.getCode(), ResponseEnum.SYSTEM_EXCEPTION.getMessage());
        }
    }


    @Override
    public Result<Boolean> akcAccountTrade(@RequestBody AccountTradeRequest request) {
        try {
            Logger.info("AccountTradeController akcAccountTrade req：{}", DataMask.toJSONString(request));
            AccountTradeResp response = accountTradeService.accountTrade(request, CommonConstants.AKC_ACCOUNT_PAY_TRADE_TYPE);
            Result<Boolean> result = buildTradeResult(response);
            Logger.info("AccountTradeController akcAccountTrade res：{}", DataMask.toJSONString(result));
            return result;
        } catch (AccountProxyException e) {
            Logger.warn("AccountTradeController akcAccountTrade warn：{}", e);
            return Result.error(e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            Logger.error("AccountTradeController akcAccountTrade error：{}", e);
            return Result.error(ResponseEnum.SYSTEM_EXCEPTION.getCode(), ResponseEnum.SYSTEM_EXCEPTION.getMessage());
        }
    }

    @Override
    public Result<AccountTradeResponse> akcAccountQuery(@RequestParam(name = "tradeNo") String tradeNo, @RequestParam(name = "tradeType") Integer tradeType) {
        if (StringUtils.isEmpty(tradeNo) || tradeType == null) {
            return Result.error(ResponseEnum.PARAM_EXCEPTION.getCode(), ResponseEnum.PARAM_EXCEPTION.getMessage());
        }
        try {
            Logger.info("AccountTradeController akcAccountQuery req：{}-{}", tradeNo, tradeType);
            String acctTradeType = null;
            if (tradeType.equals(TradeType.PAY.getCode())) {
                acctTradeType = CommonConstants.AKC_ACCOUNT_PAY_TRADE_TYPE;
            } else {
                acctTradeType = CommonConstants.AKC_ACCOUNT_REFUND_TRADE_TYPE;
            }
            AccountTradeResponse response = accountTradeService.akcAccountQuery(tradeNo, acctTradeType);
            Result<AccountTradeResponse> result = Result.success(response);
            Logger.info("AccountTradeController akcAccountQuery req：{}-{}，res：{}", tradeNo, acctTradeType, DataMask.toJSONString(result));
            return result;
        } catch (AccountProxyException e) {
            Logger.warn("AccountTradeController akcAccountQuery warn：{}", e);
            return Result.error(e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            Logger.error("AccountTradeController akcAccountQuery error：{}", e);
            return Result.error(ResponseEnum.SYSTEM_EXCEPTION.getCode(), ResponseEnum.SYSTEM_EXCEPTION.getMessage());
        }
    }

    @Override
    public Result<Boolean> akcAccountRefund(@RequestBody AccountTradeRequest request) {
        try {
            Logger.info("AccountTradeController akcAccountRefund req：{}", DataMask.toJSONString(request));
            AccountTradeResp response = accountTradeService.accountTrade(request, CommonConstants.AKC_ACCOUNT_REFUND_TRADE_TYPE);
            Result<Boolean> result = buildTradeResult(response);
            Logger.info("AccountTradeController akcAccountRefund res：{}", DataMask.toJSONString(result));
            return result;
        } catch (AccountProxyException e) {
            Logger.warn("AccountTradeController akcAccountRefund warn：{}", e);
            return Result.error(e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            Logger.error("AccountTradeController akcAccountRefund error：{}", e);
            return Result.error(ResponseEnum.SYSTEM_EXCEPTION.getCode(), ResponseEnum.SYSTEM_EXCEPTION.getMessage());
        }
    }

    @Override
    public Result<Boolean> openApiAccountTrade(@RequestBody AccountTradeRequest request) {
        try {
            Logger.info("AccountTradeController openApiAccountTrade req：{}", DataMask.toJSONString(request));
            AccountTradeResp response = accountTradeService.accountTrade(request, CommonConstants.OPENAPI_ACCOUNT_PAY_TRADE_TYPE);
            Result<Boolean> result = buildTradeResult(response);
            Logger.info("AccountTradeController openApiAccountTrade res：{}", DataMask.toJSONString(result));
            return result;
        } catch (AccountProxyException e) {
            Logger.warn("AccountTradeController openApiAccountTrade warn：{}", e);
            return Result.error(e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            Logger.error("AccountTradeController openApiAccountTrade error：{}", e);
            return Result.error(ResponseEnum.SYSTEM_EXCEPTION.getCode(), ResponseEnum.SYSTEM_EXCEPTION.getMessage());
        }
    }

    @Override
    public Result<AccountTradeResponse> openApiAccountQuery(@RequestParam(name = "tradeNo") String tradeNo, @RequestParam(name = "tradeType") Integer tradeType) {
        if (StringUtils.isEmpty(tradeNo) || tradeType == null) {
            return Result.error(ResponseEnum.PARAM_EXCEPTION.getCode(), ResponseEnum.PARAM_EXCEPTION.getMessage());
        }
        try {
            Logger.info("AccountTradeController openApiAccountQuery req：{}-{}", tradeNo, tradeType);
            String acctTradeType = null;
            if (tradeType.equals(TradeType.PAY.getCode())) {
                acctTradeType = CommonConstants.OPENAPI_ACCOUNT_PAY_TRADE_TYPE;
            } else {
                acctTradeType = CommonConstants.OPENAPI_ACCOUNT_REFUND_TRADE_TYPE;
            }
            AccountTradeResponse response = accountTradeService.openApiAccountQuery(tradeNo, acctTradeType);
            Result<AccountTradeResponse> result = Result.success(response);
            Logger.info("AccountTradeController openApiAccountQuery req：{}-{}，res：{}", tradeNo, acctTradeType, DataMask.toJSONString(result));
            return result;
        } catch (AccountProxyException e) {
            Logger.warn("AccountTradeController openApiAccountQuery warn：{}", e);
            return Result.error(e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            Logger.error("AccountTradeController openApiAccountQuery error：{}", e);
            return Result.error(ResponseEnum.SYSTEM_EXCEPTION.getCode(), ResponseEnum.SYSTEM_EXCEPTION.getMessage());
        }
    }

    @Override
    public Result<Boolean> openApiAccountRefund(@RequestBody AccountTradeRequest request) {
        try {
            Logger.info("AccountTradeController openApiAccountRefund req：{}", DataMask.toJSONString(request));
            AccountTradeResp response = accountTradeService.accountTrade(request, CommonConstants.OPENAPI_ACCOUNT_REFUND_TRADE_TYPE);
            Result<Boolean> result = buildTradeResult(response);
            Logger.info("AccountTradeController openApiAccountRefund res：{}", DataMask.toJSONString(result));
            return result;
        } catch (AccountProxyException e) {
            Logger.warn("AccountTradeController openApiAccountRefund warn：{}", e);
            return Result.error(e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            Logger.error("AccountTradeController openApiAccountRefund error：{}", e);
            return Result.error(ResponseEnum.SYSTEM_EXCEPTION.getCode(), ResponseEnum.SYSTEM_EXCEPTION.getMessage());
        }
    }

    @Override
    public Result<Boolean> xdAccountTrade(@RequestBody AccountTradeRequest request) {
        try {
            Logger.info("AccountTradeController xdAccountTrade req：{}", DataMask.toJSONString(request));
            AccountTradeResp response = accountTradeService.accountTrade(request, CommonConstants.XD_ACCOUNT_PAY_TRADE_TYPE);
            Result<Boolean> result = buildTradeResult(response);
            Logger.info("AccountTradeController xdAccountTrade res：{}", DataMask.toJSONString(result));
            return result;
        } catch (AccountProxyException e) {
            Logger.warn("AccountTradeController xdAccountTrade warn：{}", e);
            return Result.error(e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            Logger.error("AccountTradeController xdAccountTrade error：{}", e);
            return Result.error(ResponseEnum.SYSTEM_EXCEPTION.getCode(), ResponseEnum.SYSTEM_EXCEPTION.getMessage());
        }
    }


    @Override
    public Result<AccountTradeResponse> xdAccountQuery(@RequestParam(name = "tradeNo") String tradeNo, @RequestParam(name = "tradeType") Integer tradeType) {
        if (StringUtils.isEmpty(tradeNo) || tradeType == null) {
            return Result.error(ResponseEnum.PARAM_EXCEPTION.getCode(), ResponseEnum.PARAM_EXCEPTION.getMessage());
        }
        try {
            Logger.info("AccountTradeController xdAccountQuery req：{}-{}", tradeNo, tradeType);
            String acctTradeType = null;
            if (tradeType.equals(TradeType.PAY.getCode())) {
                acctTradeType = CommonConstants.XD_ACCOUNT_PAY_TRADE_TYPE;
            }else {
                acctTradeType = CommonConstants.XD_ACCOUNT_REFUND_TRADE_TYPE;
            }
            AccountTradeResponse response = accountTradeService.xdAccountQuery(tradeNo, acctTradeType);
            Result<AccountTradeResponse> result = Result.success(response);
            Logger.info("AccountTradeController xdAccountQuery req：{}-{}，res：{}", tradeNo, acctTradeType, DataMask.toJSONString(result));
            return result;
        } catch (AccountProxyException e) {
            Logger.warn("AccountTradeController xdAccountQuery warn：{}", e);
            return Result.error(e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            Logger.error("AccountTradeController xdAccountQuery error：{}", e);
            return Result.error(ResponseEnum.SYSTEM_EXCEPTION.getCode(), ResponseEnum.SYSTEM_EXCEPTION.getMessage());
        }
    }

    @Override
    public Result<Boolean> pointAccountTrade(@RequestBody AccountTradeRequest request) {
        try {
            Logger.info("AccountTradeController pointAccountTrade req：{}", DataMask.toJSONString(request));
            AccountTradeResp response = accountTradeService.accountTrade(request, CommonConstants.POINT_ACCOUNT_PAY_TRADE_TYPE);
            Result<Boolean> result = buildTradeResult(response);
            Logger.info("AccountTradeController pointAccountTrade res：{}", DataMask.toJSONString(result));
            return result;
        } catch (AccountProxyException e) {
            Logger.warn("AccountTradeController pointAccountTrade warn：{}", e);
            return Result.error(e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            Logger.error("AccountTradeController pointAccountTrade error：{}", e);
            return Result.error(ResponseEnum.SYSTEM_EXCEPTION.getCode(), ResponseEnum.SYSTEM_EXCEPTION.getMessage());
        }
    }


    @Override
    public Result<AccountTradeResponse> pointAccountQuery(@RequestParam(name = "tradeNo") String tradeNo, @RequestParam(name = "tradeType") Integer tradeType) {
        if (StringUtils.isEmpty(tradeNo) || tradeType == null) {
            return Result.error(ResponseEnum.PARAM_EXCEPTION.getCode(), ResponseEnum.PARAM_EXCEPTION.getMessage());
        }
        try {
            Logger.info("AccountTradeController pointAccountQuery req：{}-{}", tradeNo, tradeType);
            String acctTradeType = null;
            if (tradeType.equals(TradeType.PAY.getCode())) {
                acctTradeType = CommonConstants.POINT_ACCOUNT_PAY_TRADE_TYPE;
            }
            AccountTradeResponse response = accountTradeService.pointAccountQuery(tradeNo, acctTradeType);
            Result<AccountTradeResponse> result = Result.success(response);
            Logger.info("AccountTradeController pointAccountQuery req：{}-{}，res：{}", tradeNo, acctTradeType, DataMask.toJSONString(result));
            return result;
        } catch (AccountProxyException e) {
            Logger.warn("AccountTradeController pointAccountQuery warn：{}", e);
            return Result.error(e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            Logger.error("AccountTradeController xdAccountQuery error：{}", e);
            return Result.error(ResponseEnum.SYSTEM_EXCEPTION.getCode(), ResponseEnum.SYSTEM_EXCEPTION.getMessage());
        }
    }

    @Override
    public Result<Boolean> xdAccountRefund(@RequestBody AccountTradeRequest request) {
        try {
            Logger.info("AccountTradeController xdAccountRefund req：{}", DataMask.toJSONString(request));
            AccountTradeResp response = accountTradeService.accountTrade(request, CommonConstants.XD_ACCOUNT_REFUND_TRADE_TYPE);
            Result<Boolean> result = buildTradeResult(response);
            Logger.info("AccountTradeController xdAccountRefund res：{}", DataMask.toJSONString(result));
            return result;
        } catch (AccountProxyException e) {
            Logger.warn("AccountTradeController xdAccountRefund warn：{}", e);
            return Result.error(e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            Logger.error("AccountTradeController xdAccountRefund error：{}", e);
            return Result.error(ResponseEnum.SYSTEM_EXCEPTION.getCode(), ResponseEnum.SYSTEM_EXCEPTION.getMessage());
        }
    }

    private Result<Boolean> buildTradeResult(AccountTradeResp response) {
        Result<Boolean> result = Result.success(null);
        if (ResultStatus.S.getCode().equals(response.getStatus())) {
            result.setSuccess(true);
        } else {
            result.setSuccess(false);
            if(StringUtils.isNotEmpty(response.getReplyCode())) {
                result.setCode(Integer.parseInt(response.getReplyCode()));
            } else {
                result.setCode(ResponseEnum.SYSTEM_EXCEPTION.getCode());
            }

            if(StringUtils.isNotEmpty(response.getReplyMsg())) {
                result.setMessage(response.getReplyMsg());
            } else {
                result.setMessage(ResponseEnum.SYSTEM_EXCEPTION.getMessage());
            }
        }
        return result;
    }
    public static void main(String[] args) {
		System.out.println(DataMask.toJSONString(null));
	}

}
