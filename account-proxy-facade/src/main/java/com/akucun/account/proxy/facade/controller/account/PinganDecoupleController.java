package com.akucun.account.proxy.facade.controller.account;

import com.aikucun.common2.base.Result;
import com.aikucun.common2.utils.datamasking.DataMask;
import com.akucun.account.proxy.facade.stub.account.PinganDecoupleFacade;
import com.akucun.account.proxy.facade.stub.others.account.vo.PinganDecoupleAccountVO;
import com.akucun.account.proxy.service.acct.PinganDecoupleService;
import com.alibaba.fastjson.JSON;
import com.mengxiang.base.common.log.Logger;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * @Author: silei
 * @Date: 2021/12/20
 * @desc:
 */
@RestController
@Api(value = "平安解耦相关", tags = {"平安解耦相关"})
@RequestMapping("/api/account/proxy/pinganDecouple")
public class PinganDecoupleController implements PinganDecoupleFacade {


    @Autowired
    private PinganDecoupleService pinganDecoupleService;

    @Override
    @PostMapping(value = "/saveOrUpdate")
    public Result<Void> saveOrUpdate(@RequestBody @Validated PinganDecoupleAccountVO pinganDecoupleAccountVO) {
        return pinganDecoupleService.saveOrUpdateData(pinganDecoupleAccountVO);
    }

    @Override
    @PostMapping(value = "/checkWhiteList")
    public Result<Boolean> checkGrayWhitelist(@RequestBody @Validated PinganDecoupleAccountVO vo) {
        Logger.info("PinganDecoupleController vo :{}", DataMask.toJSONString(vo));
        boolean isWhitelist = pinganDecoupleService.checkGrayWhitelist(vo.getCustomerCode(), vo.getCustomerType());
        if (isWhitelist) {
            return Result.success(Boolean.TRUE);
        } else {
            return Result.success(Boolean.FALSE);
        }
    }

    @Override
    @PostMapping(value = "/accountAntiClearing")
    public Result<Void> accountAntiClearing(@RequestParam("beginIndex") int beginIndex, @RequestParam("endIndex") int endIndex) {
        pinganDecoupleService.accountAntiClearing(beginIndex, endIndex);
        return Result.success();
    }

    @Override
    @PostMapping(value = "/shopAgentWhitelistProcess")
    public Result<Void> shopAgentWhitelistProcess(@RequestParam("beginIndex") int beginIndex, @RequestParam("endIndex") int endIndex) {
        pinganDecoupleService.shopAgentWhitelistProcess(beginIndex, endIndex);
        return Result.success();
    }
    
    @PostMapping(value = "/tenantGray")
    public Result<Boolean> tenantGray(@RequestBody @Validated PinganDecoupleAccountVO vo) {
        Logger.info("PinganDecoupleController vo :{}", JSON.toJSONString(vo));
        boolean isWhitelist = pinganDecoupleService.tenantGray(vo.getCustomerCode(), vo.getCustomerType());
        if (isWhitelist) {
            return Result.success(Boolean.TRUE);
        } else {
            return Result.success(Boolean.FALSE);
        }
    }

}
