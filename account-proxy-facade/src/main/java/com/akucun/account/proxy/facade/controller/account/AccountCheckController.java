package com.akucun.account.proxy.facade.controller.account;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.facade.stub.account.AccountCheckFacade;
import com.akucun.account.proxy.facade.stub.others.dto.req.IsTenantCustomerReq;
import com.akucun.account.proxy.service.acct.AccountCheckService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: mapenghui
 * @Date: 2022/3/24
 * @desc:
 */
@RestController
@Api(value = "账户检查相关", tags = {"账户检查相关"})
@RequestMapping("/api/account/proxy/accountCheck")
public class AccountCheckController implements AccountCheckFacade {

    @Autowired
    private AccountCheckService accountCheckService;

    @Override
    @ApiOperation(value = "是否租户店主店长", notes = "是否租户店主店长", httpMethod = "POST")
    @PostMapping(value = "/isTenantCustomer", produces = "application/json;charset=utf-8")
    public Result<Boolean> isTenantCustomer(@RequestBody IsTenantCustomerReq req) {
        return accountCheckService.isTenantCustomer(req);
    }

}
