package com.akucun.account.proxy.facade.consumer;

import com.akucun.account.proxy.common.enums.PostActionExecStatus;
import com.akucun.account.proxy.common.enums.PostActionTypes;
import com.akucun.account.proxy.dao.model.RewardApply;
import com.akucun.account.proxy.facade.stub.enums.RewardTypeEnum;
import com.akucun.account.proxy.service.acct.PromoTradeFactory;
import com.akucun.account.proxy.service.acct.RewardApplyService;
import com.akucun.account.proxy.service.enums.PromoTradeStatusEnum;
import com.akucun.account.proxy.service.postaction.PostActionService;
import com.akucun.account.proxy.service.postaction.bo.PostActionItemBO;
import com.akucun.common.Result;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.maihaoche.starter.mq.annotation.MQConsumer;
import com.maihaoche.starter.mq.base.AbstractMQPushConsumer;
import com.mengxiang.base.common.log.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : 发票变更消息-消费
 * @Create on : 2025/2/15 13:40
 **/
@MQConsumer(instances = {
        @MQConsumer.Instance(
                instanceName = "DEFAULT", // 指定配置中的instance name
                topic = "AKC_INVOICE_CENTER_TOPIC",
                tag = "invoice_status",
                consumerGroup = "AKC_INVOICE_CHANGE_INFO_GROUP")})
public class InvoiceChangeConsumer extends AbstractMQPushConsumer<Object> {
    @Autowired
    private RewardApplyService rewardApplyService;
    @Autowired
    private PromoTradeFactory factory;
    @Autowired
    private PostActionService postActionService;

    final static Integer BLUE_UNDO = 10;// 待开票
    final static Integer BLUE_SUCCESS = 30;//开票成功

    @Override
    public boolean process(Object obj, Map<String, Object> map) {
        Logger.info("消费 AKC_INVOICE_CENTER_TOPIC_invoice_status_TAG obj :{}", obj);

        try {
            JSONObject jsonObj = null;
            if(obj instanceof String) {
                jsonObj = JSON.parseObject(obj.toString());
            }else {
                jsonObj = JSON.parseObject(JSON.toJSONString(obj));
            }

            //01-只处理参数审核的发票状态
            if(!jsonObj.containsKey("audit")){
                Logger.info("消费AKC_INVOICE_CENTER_TOPIC_invoice_status_TAG消息暂不处理(非离线上传待审核记录) :{}",obj);
                return true;
            }
            String applyNo = jsonObj.getString("applyNo");
            Integer status = jsonObj.getInteger("status");
            String statusDesc = jsonObj.getString("statusDesc");
            String exts = jsonObj.getString("exts");
            JSONObject extJSONObj = null;
            if(!StringUtils.isEmpty(exts)) {
                extJSONObj = JSON.parseObject(exts);
            }

            //02-确认是营销的店主开票
            List<RewardApply> existRecords = rewardApplyService.queryByBatchNo(applyNo);
            if(CollectionUtils.isEmpty(existRecords)){
                Logger.info("消费AKC_INVOICE_CENTER_TOPIC_invoice_status_TAG消息暂不处理(非营销店主开票) :{}",obj);
                return true;
            }
            RewardApply existRecord = existRecords.get(0);
            String newStatus = PromoTradeStatusEnum.INVOICE_WAIT_AUDIT.getCode();
            String errorMsg ="";
            //上传成功和审核通过，发票状态都是成功
            if(status.intValue() == BLUE_SUCCESS.intValue()){
                //审核通过，则更新状态为待付款
                if (extJSONObj != null && extJSONObj.containsKey("auditFlag") && extJSONObj.getInteger("auditFlag").intValue() == 1) {
                    newStatus = PromoTradeStatusEnum.SUBMIT.getCode();
                }
            }else if(status.intValue() == BLUE_UNDO.intValue()){
                newStatus = PromoTradeStatusEnum.INVOICE_REJECT.getCode();
                if (extJSONObj != null && extJSONObj.containsKey("auditReson")) {
                    errorMsg = extJSONObj.getString("auditReson");
                }
            } else {
                Logger.info("消费AKC_INVOICE_CENTER_TOPIC_invoice_status_TAG消息暂不处理(该状态不处理) :{}",obj);
                return true;
            }

            //03- 更新状态并异步通知
            if(newStatus.equalsIgnoreCase(PromoTradeStatusEnum.SUBMIT.getCode())){
                //03-1 创建异步出款任务
                PostActionItemBO postActionItemBO = PostActionItemBO.builder()
                        .actionType(PostActionTypes.PROMO_PAY.getName())
                        .bizId(existRecord.getRequestNo())
                        .paramObject(existRecord)
                        .remark(existRecord.getBusiType()+"-启动出款任务")
                        .status(PostActionExecStatus.EXECUTE.value())
                        .build();
                postActionService.addAction(postActionItemBO);
                Logger.info("消费AKC_INVOICE_CENTER_TOPIC_invoice_status_TAG(审核通过落地异步出款任务) :{}",JSON.toJSONString(postActionItemBO));
            }
            RewardTypeEnum busiTypeEnum = RewardTypeEnum.getByCode(existRecord.getBusiType());
            Logger.info("消费AKC_INVOICE_CENTER_TOPIC_invoice_status_TAG消息-更新申请状态并通知上游开始:obj={},newStatus={}",obj,newStatus);
            Result<Void> resp = factory.getHandler(busiTypeEnum).syncNotify(existRecord.getRequestNo(), existRecord.getBusiType(), existRecord.getTransBillDate(), newStatus, errorMsg);
            Logger.info("消费AKC_INVOICE_CENTER_TOPIC_invoice_status_TAG消息-更新申请状态并通知上游完成:obj={},newStatus={},resp={}",obj,newStatus,JSON.toJSONString(resp));
            if(ObjectUtils.isEmpty(resp) || !resp.isSuccess()){
                Logger.error("消费AKC_INVOICE_CENTER_TOPIC_invoice_status_TAG消息失败-更新申请状态并通知上游失败:obj={},resp={}",obj, JSON.toJSONString(resp));
                return false;
            }
        }catch (Exception e){
            Logger.error("消费AKC_INVOICE_CENTER_TOPIC_invoice_status_TAG消息失败-更新申请状态并通知上游失败:{}",obj,e);
            return false;
        }

        return true;
    }
}
