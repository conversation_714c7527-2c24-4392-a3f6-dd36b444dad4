package com.akucun.account.proxy.facade.controller.commission;

import com.aikucun.common2.base.Result;
import com.aikucun.common2.utils.datamasking.DataMask;
import com.akucun.account.proxy.facade.stub.commission.AccountCommissionFacade;
import com.akucun.account.proxy.facade.stub.others.trade.req.AccountCommissionReq;
import com.akucun.account.proxy.service.commission.AccountCommissionService;
import com.akucun.account.proxy.service.commission.bo.AccountCommissionBO;
import com.alibaba.fastjson.JSON;
import com.mengxiang.base.common.log.Logger;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: silei
 * @Date: 2020/12/7
 * @desc: 账户分佣相关
 */
@RestController
@Api(value = "账户分佣相关", tags = {"账户分佣相关"})
@RequestMapping("/api/account")
public class AccountCommissionController implements AccountCommissionFacade {

    @Autowired
    private AccountCommissionService commissionService;

    @Override
    @PostMapping(value = "/allocateCommission")
    @ApiOperation(value = "账户分佣", notes = "账户分佣", httpMethod = "POST")
    public Result<Void> allocateCommission(@RequestBody @Validated AccountCommissionReq req) {
        Logger.info("allocateCommission req:{}", DataMask.toJSONString(req));
        AccountCommissionBO bo = new AccountCommissionBO();
        BeanUtils.copyProperties(req, bo);
        return commissionService.allocate(bo);
    }
}
