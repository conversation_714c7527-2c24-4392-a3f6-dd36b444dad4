package com.akucun.account.proxy.facade.job;

import com.aikucun.common2.utils.DateUtils;
import com.akucun.account.center.client.model.TradeInfo;
import com.akucun.account.center.client.model.account.AccountBookDO;
import com.akucun.account.center.client.model.query.AccountQuery;
import com.akucun.account.proxy.facade.controller.account.AccountCenterController;
import com.akucun.common.Result;
import com.alibaba.fastjson.JSON;
import com.mengxiang.base.common.log.Logger;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.io.*;
import java.math.BigDecimal;
import java.net.URL;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : 账户操作
 * -- 提供OBS的CSV文件地址
 * @Create on : 2025/3/17 15:50
 **/
public class AccountOperateGlueJavaTask extends IJobHandler {
    String LOG_PREFIX = "饷联盟KA租户大土豆店主余额提现-";

    @Autowired
    private AccountCenterController accountCenterController;

    @Override
    public ReturnT<String> execute(String params) throws Exception {
        if (StringUtils.isEmpty(params)) {
            Logger.error(LOG_PREFIX + "参数为空！");
            return ReturnT.FAIL;
        }

        if(!params.endsWith(".csv")){
            //操作单个店主
            dealAcct(params);
        }else {
            //操作CSV文件
            long startTime = System.currentTimeMillis();
            try {
                //01-下载文件到本地
                String localFilePath = getLocalFilePath(params);
                Logger.info(LOG_PREFIX + "下载文件到本地成功:{}", localFilePath);

                //02-业务处理
                List<String> dataList = readCsvAndOperate(localFilePath);
                Logger.info(LOG_PREFIX + "操作完成(共{}条记录):{}", dataList.size());

                //03-写入结果到文件
                if (!CollectionUtils.isEmpty(dataList)) {
                    String csvFilePath = "output" + DateUtils.format(new Date(), "yyyyMMddHHmmssSSS") + ".csv";
                    File outputFile = new File(csvFilePath);
                    if (!outputFile.exists()) {
                        outputFile.createNewFile();
                    }
                    writeListToCsv(dataList, csvFilePath);
                    Logger.info(LOG_PREFIX + "操作账户结果文件路径:{}", csvFilePath);
                }

            } catch (Exception e) {
                Logger.error(LOG_PREFIX + "操作账户失败", e);
            } finally {
                Logger.info(LOG_PREFIX + "操作账户结束,耗时{}分钟", (System.currentTimeMillis() - startTime) / 1000 / 60);
            }

        }

        return ReturnT.SUCCESS;
    }

    private List<String> readCsvAndOperate(String localFilePath) {
        long beginTime = System.currentTimeMillis();
        List<String> sussData = new ArrayList<>();
        long rowNum = 0l;

        try {
            BufferedReader reader = new BufferedReader(new FileReader(localFilePath));
            String line;
            while ((line = reader.readLine()) != null) {
                Logger.info(LOG_PREFIX + "解析第{}行数据,准备开始处理:{}", rowNum,line);
                String dealMsg = dealAcct(line);
                if(!StringUtils.isEmpty(dealMsg)){
                    sussData.add(dealMsg);
                }
                rowNum ++;
            }

        } catch (Exception e) {
            Logger.error(LOG_PREFIX + "解析CSV文件并操作账户失败", e);
        } finally {
            Logger.info(LOG_PREFIX + "解析CSV文件并操作账户结束(共{}条),耗时{}分钟", sussData.size(), (System.currentTimeMillis() - beginTime) / 1000 / 60);
        }

        return sussData;
    }

    private String dealAcct(String customerCode){
        //01-查询店主饷店账户余额'
        String sourceAcctTK = "8D256656F0A9E0A959024F16A8C910B3";
        AccountQuery accountQuery = new AccountQuery();
        accountQuery.setAccountTypeKey(sourceAcctTK);
        accountQuery.setCustomerCode(customerCode);
        Logger.info(LOG_PREFIX + "查询店主余额,req:{}", JSON.toJSONString(accountQuery));
        Result<AccountBookDO> accountBookResult = accountCenterController.queryAccount(accountQuery);
        Logger.info(LOG_PREFIX + "查询店主余额,resp:{}", JSON.toJSONString(accountBookResult));
        if(!ObjectUtils.isEmpty(accountBookResult) && accountBookResult.isSuccess() && !ObjectUtils.isEmpty(accountBookResult.getData())){
            AccountBookDO accountBookDOTmp = accountBookResult.getData();
            //可用余额大于0，才执行余额的转移
            if(!ObjectUtils.isEmpty(accountBookDOTmp.getBalance()) && accountBookDOTmp.getBalance().compareTo(BigDecimal.ZERO)>0){
                BigDecimal operateAmount = accountBookDOTmp.getBalance();

                //02-店主饷店账户余额扣减
                TradeInfo debitTradeInfo = new TradeInfo();
                String tradeid = String.format("%s_044_%s",customerCode,DateUtils.format(new Date(), "yyyyMMddHHmm"));
                debitTradeInfo.setAccountTypeKey(sourceAcctTK);
                debitTradeInfo.setTradeType("TRADE_TYPE_044");
                debitTradeInfo.setCustomerCode(customerCode);
                debitTradeInfo.setAmount(operateAmount);
                debitTradeInfo.setRemark("提现到租户账户");
                debitTradeInfo.setTradeNo(tradeid);
                debitTradeInfo.setSourceBillNo(tradeid);
                Logger.info(LOG_PREFIX + "店主余额扣减,req:{}", JSON.toJSONString(debitTradeInfo));
                Result<Void> debitRslt =accountCenterController.dealTrade(debitTradeInfo);
                Logger.info(LOG_PREFIX + "店主余额扣减,req:{}", JSON.toJSONString(debitRslt));

                //03-扣减的店主余额增加到租户账户
                TradeInfo addTradeInfo = new TradeInfo();
                String addtradeid = String.format("%s_428_%s",customerCode,DateUtils.format(new Date(), "yyyyMMddHHmm"));
                addTradeInfo.setAccountTypeKey("CD1CB5580F8C4307E986FD77ED98DC1E");
                addTradeInfo.setTradeType("TRADE_TYPE_420");
                addTradeInfo.setCustomerCode("2236610602700345756");
                addTradeInfo.setAmount(operateAmount);
                addTradeInfo.setRemark("店主转移资金:"+customerCode);
                addTradeInfo.setTradeNo(addtradeid);
                addTradeInfo.setSourceBillNo(addtradeid);
                Logger.info(LOG_PREFIX + "租户余额增加,req:{}", JSON.toJSONString(addTradeInfo));
                Result<Void> addRslt =accountCenterController.dealTrade(addTradeInfo);
                Logger.info(LOG_PREFIX + "租户余额增加,req:{}", JSON.toJSONString(addRslt));

                return String.format("%s,%s,%s,%s,%s,%s",customerCode,operateAmount,debitRslt.getCode(),debitRslt.getMessage(),addRslt.getCode(),addRslt.getMessage());
            }else{
                Logger.info(LOG_PREFIX + "当前用户余额为0不处理,customerCode:{}", customerCode);
                return null;
            }
        }else{
            Logger.info(LOG_PREFIX + "当前用户未开户不处理,customerCode:{}", customerCode);
            return null;
        }
    }

    //======================基础方法：http文件下载===============================
    //01-下载文件到本地
    public String getLocalFilePath(String httpFileUrl) throws IOException {
        String localFilePath = UUID.randomUUID().toString() + ".csv";

        try {
            //重新构建本地临时文件
            File file = new File(localFilePath);
            if (file.exists()) {
                file.delete();
            }
            file.createNewFile();

            //拷贝文件
            downloadCSV(httpFileUrl, localFilePath);
            Logger.info(LOG_PREFIX + "CSV文件下载成功！");
        } catch (Exception e) {
            Logger.error(LOG_PREFIX + "下载CSV文件时发生错误: ", e);
        }

        return localFilePath;
    }

    public void downloadCSV(String fileUrl, String localFilePath) throws IOException {
        URL url = new URL(fileUrl);
        int bufferSize = 50 * 1024; // 50KB

        try {
            BufferedInputStream buffin = new BufferedInputStream(url.openStream(), bufferSize);
            BufferedOutputStream buffout = new BufferedOutputStream(new FileOutputStream(localFilePath), bufferSize);
            byte[] buffer = new byte[bufferSize];
            int bytesRead;
            while ((bytesRead = buffin.read(buffer)) != -1) {
                buffout.write(buffer, 0, bytesRead);
            }
            buffout.flush();
            buffout.close();
            buffin.close();
        } catch (Exception e) {
            Logger.error(LOG_PREFIX + "下载CSV文件时发生错误: ", e);
        }
    }

    //02-写入结果到文件
    public static void writeListToCsv(List<String> dataList, String filePath) throws IOException {
        BufferedWriter writer = new BufferedWriter(new FileWriter(filePath));
        try {
            for (String item : dataList) {
                writer.write(item);
                writer.newLine(); // 写入换行符
            }
            writer.flush();
        } catch (IOException e) {
            Logger.warn("文件写入失败", e);
        } finally {
            writer.close();
        }
    }
}
