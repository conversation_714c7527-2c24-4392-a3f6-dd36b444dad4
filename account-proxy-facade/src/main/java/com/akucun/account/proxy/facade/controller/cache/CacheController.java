package com.akucun.account.proxy.facade.controller.cache;

import com.aikucun.common2.log.Logger;
import com.akucun.account.proxy.common.constant.CommonConstants;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashSet;
import java.util.Set;

@RestController
@RequestMapping("/api/account/proxy/cache")
public class CacheController {
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @ApiOperation(value = "清空网关路由缓存")
    @GetMapping("/clearMerchantRuleRedis")
    public Boolean clearAllGwyRuleRedis(String type) {
        long startTime = System.currentTimeMillis();
        Logger.info("清空网关路由缓存开始，type:{}", type);
        try {
            Set<String> keys = new HashSet<>();
            String prefix = "";
            if (type.equalsIgnoreCase("all")) {
                prefix = CommonConstants.TRANSFER_MERCHANT_INFO_PREFIX_REDIS + "*";
                keys = redisTemplate.keys(prefix);
                redisTemplate.delete(keys);
            } else if (type.equals(CommonConstants.TRANSFER_MERCHANT_INFO_PREFIX_REDIS)) {
                prefix = CommonConstants.TRANSFER_MERCHANT_INFO_PREFIX_REDIS + "*";
                keys = redisTemplate.keys(prefix);
                redisTemplate.delete(keys);
            }else {
                Logger.info("清空网关路由缓存，不支持的类型");
                return Boolean.FALSE;
            }
            Logger.info("清空网关路由缓存结束，耗时:{}", System.currentTimeMillis() - startTime);
            return Boolean.TRUE;
        } catch (Exception e) {
            Logger.info("清空网关路由缓存异常:{}", e);
            return Boolean.FALSE;
        }
    }
}
