package com.akucun.account.proxy.facade.controller.pingan;

import com.akucun.account.proxy.common.enums.ErrorCodeConstants;
import com.akucun.account.proxy.common.utils.AccountUtils;
import com.akucun.account.proxy.facade.stub.pingan.UnionpayAuthFacade;
import com.akucun.account.proxy.service.acct.AccountUpgradeService;
import com.akucun.fps.common.entity.Result;
import com.akucun.fps.pingan.client.model.PingAnBindingRegisterAccountReqDO;
import com.akucun.fps.pingan.client.model.PingAnBindingRegisterAccountRespDO;
import com.akucun.fps.pingan.client.vo.PinganCardVO;
import com.akucun.fps.pingan.feign.api.unionpayauth.UnionpayAuthServiceApi;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@RestController
@Api(value = "银联鉴权服务", tags = {"银联鉴权服务"})
@RequestMapping("/api/account/proxy/pingan")
public class UnionpayAuthServiceController implements UnionpayAuthFacade {

    @Value("${pingan.maintain.switch:false}")
    private boolean pinganMaintainSwitch;
    @Value("${pingan.maintain.tips:系统维护中，请稍后重试}")
    private String pinganMaintainTips;

    @Resource
    private UnionpayAuthServiceApi unionpayAuthServiceApi;
    @Autowired
    private AccountUpgradeService accountUpgradeService;

    /**
     * 会员绑定提现账户(发送短信验证码)[6066]
     *
     * @param pinganCard
     */
    @Override
    @PostMapping(value = "/unionpayAuthApply")
    @ApiOperation(value = "会员绑定提现账户(发送短信验证码)", notes = "会员绑定提现账户(发送短信验证码)", httpMethod = "POST")
    public Result<String> unionpayAuthApply(@RequestBody PinganCardVO pinganCard) {
        if(pinganMaintainSwitch){
            Result<String> result = new Result<>();
            result.setError(ErrorCodeConstants.SYSTEMMAINTAIN_900000.getErrorCode(), ErrorCodeConstants.SYSTEMMAINTAIN_900000.getErrorName(), pinganMaintainTips);
            return result;
        }
        String convertCustomerCode = AccountUtils.getCustomerCode(pinganCard.getCustomerCode(), pinganCard.getCustomerType());
        if(accountUpgradeService.hasRecentUpgradeProcessing(convertCustomerCode, pinganCard.getCustomerType())) {
            Result<String> result = new Result<>();
            result.setError(ErrorCodeConstants.BINDCARD_101901.getErrorCode(), ErrorCodeConstants.BINDCARD_101901.getErrorName(), ErrorCodeConstants.BINDCARD_101901.getErrorMessage());
            return result;
        }
        return unionpayAuthServiceApi.unionpayAuthApply(pinganCard);
    }

    /**
     * 会员绑定提现账户(验证短信)[6067]
     *
     * @param account
     */
    @Override
    @PostMapping(value = "/unionpayAuthConfirm")
    @ApiOperation(value = "会员绑定提现账户(验证短信)", notes = "会员绑定提现账户(验证短信)", httpMethod = "POST")
    public Result<PingAnBindingRegisterAccountRespDO> unionpayAuthConfirm(@RequestBody PingAnBindingRegisterAccountReqDO account) {
        return unionpayAuthServiceApi.unionpayAuthConfirm(account);
    }
}
