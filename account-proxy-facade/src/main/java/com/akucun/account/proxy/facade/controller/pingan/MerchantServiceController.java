package com.akucun.account.proxy.facade.controller.pingan;

import com.akucun.account.proxy.facade.stub.pingan.MerchantFacade;
import com.akucun.fps.common.entity.Result;
import com.akucun.fps.pingan.client.model.po.PinganAccount;
import com.akucun.fps.pingan.client.vo.*;
import com.akucun.fps.pingan.feign.api.merchant.MerchantServiceApi;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@RestController
@Api(value = "对接商户系统", tags = {"对接商户系统"})
@RequestMapping("/api/account/proxy/pingan")
public class MerchantServiceController implements MerchantFacade {

    @Resource
    private MerchantServiceApi merchantServiceApi;

    /**
     * 账户注册
     *
     * @param
     */
    @Override
    @PostMapping(value = "/registerPinganAccount")
    @ApiOperation(value = "账户注册", notes = "账户注册", httpMethod = "POST")
    public Result<String> registerPinganAccount(@RequestBody MemBerRegisterVO memBerRegisterVO) {
        return merchantServiceApi.registerPinganAccount(memBerRegisterVO);
    }

    /**
     * 商户注册
     *
     * @param
     */
    @Override
    @PostMapping(value = "/register")
    @ApiOperation(value = "商户注册", notes = "商户注册", httpMethod = "POST")
    public Result<String> register(@RequestBody MerchantRegisterVO merchantRegisterVO) {
        return merchantServiceApi.register(merchantRegisterVO);
    }

    /**
     * 商家小额鉴权绑卡
     *
     * @param
     */
    @Override
    @PostMapping(value = "/bindCardForMerFir")
    @ApiOperation(value = "商家小额鉴权绑卡", notes = "商家小额鉴权绑卡", httpMethod = "POST")
    public Result<String> bindCardForMerFir(@RequestBody PinganCardVO pinganCardVO) {
        return merchantServiceApi.bindCardForMerFir(pinganCardVO);
    }

    /**
     * 小额鉴权验证
     *
     * @param
     * @return
     */
    @Override
    @PostMapping(value = "/bindCardForMerSec")
    @ApiOperation(value = "小额鉴权验证", notes = "小额鉴权验证", httpMethod = "POST")
    public Result<String> bindCardForMerSec(@RequestBody PinganCardVercVO pinganCardVercVO) {
        return merchantServiceApi.bindCardForMerSec(pinganCardVercVO);
    }

    /**
     * 会员解绑提现账户【6065】
     *
     * @param untieWithdrawVO
     * @return
     */
    @Override
    @PostMapping(value = "/untieWithdraw")
    @ApiOperation(value = "会员解绑提现账户", notes = "会员解绑提现账户", httpMethod = "POST")
    public Result<String> untieWithdraw(@RequestBody UntieWithdrawVO untieWithdrawVO) {
        return merchantServiceApi.untieWithdraw(untieWithdrawVO);
    }

    /**
     * 申请提现或支付短信动态码
     *
     * @param authCodeVO
     */
    @Override
    @PostMapping(value = "/getAuthCode")
    @ApiOperation(value = "申请提现或支付短信动态码", notes = "申请提现或支付短信动态码", httpMethod = "POST")
    public Result<AuthCodeResultVO> getAuthCode(@RequestBody AuthCodeVO authCodeVO) {
        return merchantServiceApi.getAuthCode(authCodeVO);
    }

    /**
     * 查询某笔小额鉴权转账状态
     *
     * @param authCodeVO@return
     * @desc:  状态：0：成功，1：失败，2：待确认
     */
    @Override
    @PostMapping(value = "/selectAuthStatusByCode")
    @ApiOperation(value = "查询某笔小额鉴权转账状态", notes = "查询某笔小额鉴权转账状态", httpMethod = "POST")
    public Result<AuthStatusResultVO> selectAuthStatusByCode(@RequestBody AuthCodeVO authCodeVO) {
        return merchantServiceApi.selectAuthStatusByCode(authCodeVO);
    }

    /**
     * 维护会员绑定提现账户联行号
     *
     * @param maintainWithdrawVO
     * @return
     */
    @Override
    @PostMapping(value = "/maintainWithdraw")
    @ApiOperation(value = "维护会员绑定提现账户联行号", notes = "维护会员绑定提现账户联行号", httpMethod = "POST")
    public Result<String> maintainWithdraw(@RequestBody MaintainWithdrawVO maintainWithdrawVO) {
        return merchantServiceApi.maintainWithdraw(maintainWithdrawVO);
    }

    /**
     * 查询平安注册账户信息
     *
     * @param accountRegisterVO
     * @return
     */
    @Override
    @PostMapping(value = "/selectPinganAccount")
    @ApiOperation(value = "查询平安注册账户信息", notes = "查询平安注册账户信息", httpMethod = "POST")
    public Result<PinganAccount> selectPinganAccount(@RequestBody AccountRegisterVO accountRegisterVO) {
        return merchantServiceApi.selectPinganAccount(accountRegisterVO);
    }

    /**
     * 对内解绑功能
     *
     * @param untieWithdrawVO
     * @return
     */
    @Override
    @PostMapping(value = "/untieWithdrawSelf")
    @ApiOperation(value = "对内解绑功能", notes = "对内解绑功能", httpMethod = "POST")
    public Result<String> untieWithdrawSelf(@RequestBody UntieWithdrawVO untieWithdrawVO) {
        return merchantServiceApi.untieWithdrawSelf(untieWithdrawVO);
    }

    /**
     * 注销平安账户
     *
     * @param memBerRegisterVO
     * @return
     */
    @Override
    @PostMapping(value = "/cancelPinganAccount")
    @ApiOperation(value = "注销平安账户", notes = "注销平安账户", httpMethod = "POST")
    public Result<Void> cancelPinganAccount(@RequestBody MemBerRegisterVO memBerRegisterVO) {
        return merchantServiceApi.cancelPinganAccount(memBerRegisterVO);
    }

    /**
     * 更新平安账户信息
     *
     * @param pinganAccount
     * @return
     */
    @Override
    @PostMapping(value = "/updatePinganAccount")
    @ApiOperation(value = "更新平安账户信息", notes = "更新平安账户信息", httpMethod = "POST")
    public Result<Void> updatePinganAccount(@RequestBody PinganAccount pinganAccount) {
        return merchantServiceApi.updatePinganAccount(pinganAccount);
    }

}
