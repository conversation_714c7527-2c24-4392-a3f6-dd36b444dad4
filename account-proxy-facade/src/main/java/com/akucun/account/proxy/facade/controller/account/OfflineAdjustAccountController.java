/*
 * @Author: Lee
 * @Date: 2025-04-16 15:14:10
 * @Description: 线下调账控制器
 * @License: Copyright (c) 2025, Lee
 */
package com.akucun.account.proxy.facade.controller.account;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.akucun.account.proxy.facade.stub.account.OfflineAdjustAccountFacade;
import com.akucun.account.proxy.facade.stub.enums.OfflineAdjustAccountAuditStatusEnum;
import com.akucun.account.proxy.facade.stub.others.account.req.OfflineAdjustAccountBatchRequest;
import com.akucun.account.proxy.facade.stub.others.account.req.OfflineAdjustAccountPageQueryReq;
import com.akucun.account.proxy.facade.stub.others.account.req.OfflineAdjustAccountSingleRequest;
import com.akucun.account.proxy.facade.stub.others.account.req.OfflineAdjustAccountSubmitReq;
import com.akucun.account.proxy.facade.stub.others.account.vo.OfflineAdjustAccountBatchStatisticVO;
import com.akucun.account.proxy.facade.stub.others.account.vo.OfflineAdjustAccountVO;
import com.akucun.account.proxy.service.acct.OfflineAdjustAccountService;
import com.alibaba.fastjson.JSON;
import com.mengxiang.base.common.log.Logger;
import com.mengxiang.base.common.model.result.Pagination;
import com.mengxiang.base.common.model.result.Result;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@RestController
@Api(value = "线下调账相关", tags = {"线下调账相关"})
@RequestMapping("/api/account/proxy/offlineAdjust")
public class OfflineAdjustAccountController implements OfflineAdjustAccountFacade {

    @Autowired
    private OfflineAdjustAccountService offlineAdjustAccountService;

    @Override
    @ApiOperation(value = "新增线下调账记录")
    public Result<Void> submit(@RequestBody @Validated OfflineAdjustAccountSubmitReq addVO) {
        return offlineAdjustAccountService.submit(addVO);
    }

    @Override
    @ApiOperation(value = "分页查询线下调账记录")
    public Result<Pagination<OfflineAdjustAccountVO>> pageQuery(@RequestBody OfflineAdjustAccountPageQueryReq queryReq) {
        return offlineAdjustAccountService.pageQuery(queryReq);
    }

    @Override
    @ApiOperation(value = "统计线下调账记录")
    public Result<OfflineAdjustAccountBatchStatisticVO> statistic(@RequestBody OfflineAdjustAccountPageQueryReq queryReq) {
        return offlineAdjustAccountService.statistic(queryReq);
    }

    @Override
    @ApiOperation(value = "审核通过线下调账记录")
    public Result<Void> auditPass(@RequestBody OfflineAdjustAccountSingleRequest request) {
        return offlineAdjustAccountService.auditPass(request.getId(), request.getOperator());
    }

    @Override
    @ApiOperation(value = "审核拒绝线下调账记录")
    public Result<Void> auditRefuse(@RequestBody OfflineAdjustAccountSingleRequest request) {
        return offlineAdjustAccountService.auditRefuse(request.getId(), request.getOperator());
    }

    @Override
    @ApiOperation(value = "批量删除线下调账记录")
    public Result<Integer> batchDelete(@RequestBody OfflineAdjustAccountBatchRequest request) {
        return offlineAdjustAccountService.batchDelete(request.getIds(), request.getOperator());
    }

    @Override
    @ApiOperation(value = "一键删除")
    public Result<Void> onKeyDelete(@RequestBody OfflineAdjustAccountPageQueryReq queryReq, @RequestParam("operator") String operator) {
        Logger.info("一键删除线下调账记录开始，req：{}，operator：{}", JSON.toJSONString(queryReq), operator);
        if (StringUtils.isAnyBlank(queryReq.getCreateTimeStart(), queryReq.getCreateTimeEnd())) {
            return Result.error("一键删除必须指定创建时间范围");
        }
        
        //结束时间必须小于当前时间, 使用localdatetime实现
        LocalDateTime endTime = LocalDateTime.parse(queryReq.getCreateTimeEnd(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        if (endTime.isAfter(LocalDateTime.now())) {
            return Result.error("结束时间必须小于当前时间");
        }
        
        List<Integer> supportAuditStatusList = Arrays.asList(OfflineAdjustAccountAuditStatusEnum.PENDING.getCode());
        if (ObjectUtils.isEmpty(queryReq.getAuditStatus()) || !supportAuditStatusList.contains(queryReq.getAuditStatus())) {
            return Result.error("一键删除只能操作待审核状态数据");
        }
        return offlineAdjustAccountService.onKeyDelete(queryReq, operator);
    }

    @Override
    @ApiOperation(value = "重试")
    public Result<Void> retry(@RequestBody OfflineAdjustAccountSingleRequest request) {
        return offlineAdjustAccountService.retry(request.getId(), request.getOperator());
    }
}