package com.akucun.account.proxy.facade;

import com.alibaba.dubbo.spring.boot.annotation.EnableDubboConfiguration;
import com.maihaoche.starter.mq.annotation.EnableMQConfiguration;
import com.mengxiang.base.common.rpc.support.annotation.MXEnableCloud;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.support.SpringBootServletInitializer;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.cloud.netflix.feign.EnableFeignClients;
import org.springframework.cloud.netflix.hystrix.EnableHystrix;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ComponentScans;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.ImportResource;

/**
 * <AUTHOR>
 */
@EnableConfigurationProperties
@EnableAspectJAutoProxy(exposeProxy = true)
@ComponentScans({
        @ComponentScan("com.akucun.*"),
        @ComponentScan("com.aikucun.common2")
})
@EnableEurekaClient
@MapperScan({"com.akucun.account.proxy.dao.mapper", "com.baidu.fsg.uid.worker.dao", "com.mengxiang.transaction.framework.mapper"})
@EnableDubboConfiguration
@EnableHystrix
@EnableMQConfiguration
@EnableFeignClients(basePackages = {"com.akucun.account.center", "com.akucun.member.center.api",
        "com.akucun.fps.pingan.feign","com.akucun.pay.gateway.wxv2.facade.stub"
        ,"com.akucun.member.audit",
        "com.mengxiang.member.service.facade.common.feign",
        "com.akucun.account.proxy.client","com.akucun.bcs.channel.facade.stub.api"
        ,"com.akucun.pay.gateway.wx.facade.stub","com.akucun.sms.remote","com.akucun.aftersale.mgt.facade.stub"
        })
@MXEnableCloud(basePackages = { "com.mengxiang.account.finpointcore.service.facade.common"
		,"com.mengxiang.fin.clearing.core.service.facade.common.feign",
        "com.mengxiang.member.service.facade.common",
        "com.mengxiang.saas.service.facade.common.feign.tenant",
        "com.akucun.bcs.bill.facade.stub.api","com.akucun.bond.feign",
        "com.akucun.risk.gateway.facade.stub.namelist","com.mengxiang.fileupload"})

@SpringBootApplication
public class Application extends SpringBootServletInitializer {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }

    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder builder) {
        return builder.sources(Application.class);
    }

}
