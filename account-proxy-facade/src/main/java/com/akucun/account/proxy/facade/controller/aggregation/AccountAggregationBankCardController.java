package com.akucun.account.proxy.facade.controller.aggregation;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.client.pingan.MerchantClient;
import com.akucun.account.proxy.common.enums.ErrorCodeConstants;
import com.akucun.account.proxy.common.utils.AccountUtils;
import com.akucun.account.proxy.common.utils.Results;
import com.akucun.account.proxy.facade.stub.aggregation.AccountAggregationBankCardFacade;
import com.akucun.account.proxy.facade.stub.others.aggregation.req.BindCardInfoDTO;
import com.akucun.account.proxy.facade.stub.others.aggregation.req.BindCardVerifyDTO;
import com.akucun.account.proxy.facade.stub.others.aggregation.res.BindAuthResp;
import com.akucun.account.proxy.service.acct.AccountService;
import com.akucun.account.proxy.service.acct.AccountUpgradeService;
import com.akucun.account.proxy.service.acct.TaxTripPactionApplyService;
import com.akucun.fps.common.entity.ResultList;
import com.akucun.fps.pingan.client.model.*;
import com.akucun.fps.pingan.client.model.po.BankNode;
import com.akucun.fps.pingan.client.model.query.BindCardQueryReq;
import com.akucun.fps.pingan.client.vo.PinganCardVO;
import com.akucun.fps.pingan.client.vo.UntieWithdrawVO;
import com.akucun.fps.pingan.feign.api.acquirejointlinenum.AcquireJointLineNumbeServiceApi;
import com.akucun.fps.pingan.feign.api.merchant.MerchantServiceApi;
import com.akucun.fps.pingan.feign.api.merchantquery.MerchantQueryServiceApi;
import com.akucun.fps.pingan.feign.api.unionpayauth.UnionpayAuthServiceApi;
import com.mengxiang.base.common.log.Logger;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * @Author: silei
 * @Date: 2020/11/15
 * @desc:
 */
@Api(value = "聚合层账户银行卡相关", tags = {"聚合层账户银行卡相关"})
@RestController
@RequestMapping("/api/account/aggregation/bankCard")
public class AccountAggregationBankCardController implements AccountAggregationBankCardFacade {

    @Resource
    private AcquireJointLineNumbeServiceApi acquireJointLineNumbeServiceApi;
    @Resource
    private UnionpayAuthServiceApi unionpayAuthServiceApi;
    @Resource
    private MerchantServiceApi merchantServiceApi;
    @Resource
    private MerchantQueryServiceApi merchantQueryServiceApi;
    @Autowired
    private MerchantClient merchantClient;
    @Autowired
    private AccountService accountService;
    @Autowired
    private TaxTripPactionApplyService taxTripPactionApplyService;
    @Autowired
    private AccountUpgradeService accountUpgradeService;

    @Value("${pingan.maintain.switch:false}")
    private boolean pinganMaintainSwitch;
    @Value("${pingan.maintain.tips:系统维护中，请稍后重试}")
    private String pinganMaintainTips;

    @Override
    @GetMapping(value = {"/queryBankInfoManageByChannel"})
    @ApiOperation(value = "查询银联银行列表", notes = "查询银联银行列表", httpMethod = "GET")
    public ResultList<BankInfoManageResp> queryBankInfoManageByChannel(@RequestParam("channel") String channel) {
        try {
            return acquireJointLineNumbeServiceApi.selectBankInfoManageByChannel(channel);
        } catch (Exception e) {
            Logger.error("AccountBankCardController queryBankInfoManageByChannel error:", e);
            return null;
        }

    }

    @Override
    @PostMapping(value = {"/unionPayAuthApply"})
    @ApiOperation(value = "银联鉴权申请接口", notes = "银联鉴权申请接口", httpMethod = "POST")
    public Result<String> unionPayAuthApply(@RequestBody PinganCardVO pinganCardVO) {
        if(pinganMaintainSwitch){
            return Result.error(ErrorCodeConstants.SYSTEMMAINTAIN_900000.getErrorCode(), pinganMaintainTips);
        }
        String convertCustomerCode = AccountUtils.getCustomerCode(pinganCardVO.getCustomerCode(), pinganCardVO.getCustomerType());
        if(accountUpgradeService.hasRecentUpgradeProcessing(convertCustomerCode, pinganCardVO.getCustomerType())) {
            return Result.error(ErrorCodeConstants.BINDCARD_101901.getErrorCode(), ErrorCodeConstants.BINDCARD_101901.getErrorMessage());
        }
        try {
            boolean hasAuditingApply = taxTripPactionApplyService.hasAuditingApply(pinganCardVO.getCustomerCode(), pinganCardVO.getCustomerType());
            if(hasAuditingApply){
                Logger.info("该用户的税库银三方协议申请记录状态为审核中,不允许进行绑卡操作");
                return Result.error(ErrorCodeConstants.AUTHAPPLY_101900.getErrorCode(), ErrorCodeConstants.AUTHAPPLY_101900.getErrorMessage());
            }
            com.akucun.fps.common.entity.Result<String> result = unionpayAuthServiceApi.unionpayAuthApply(pinganCardVO);
            if (Results.checkFpsResult(result)) {
                return Result.success(result.getData());
            } else {
                return Result.error(result.getErrorCode(), result.getErrorMessage());
            }
        } catch (Exception e) {
            Logger.error("AccountBankCardController unionPayAuthApply error:", e);
        }
        return null;
    }

    @Override
    @PostMapping(value = {"/unionPayAuthConfirm"})
    @ApiOperation(value = "银联鉴权确认接口", notes = "银联鉴权确认接口", httpMethod = "POST")
    public Result<PingAnBindingRegisterAccountRespDO> unionPayAuthConfirm(@RequestBody PingAnBindingRegisterAccountReqDO registerAccountReqDO) {
        try {
            com.akucun.fps.common.entity.Result<PingAnBindingRegisterAccountRespDO> result = unionpayAuthServiceApi.unionpayAuthConfirm(registerAccountReqDO);
            if (Results.checkFpsResult(result)) {
                return Result.success(result.getData());
            } else {
                return Result.error(result.getErrorCode(), result.getErrorMessage());
            }
        } catch (Exception e) {
            Logger.error("AccountBankCardController unionPayAuthConfirm error:", e);
        }
        return null;
    }

    @Override
    @PostMapping(value = {"/untieWithdraw"})
    @ApiOperation(value = "解绑银行卡", notes = "解绑银行卡", httpMethod = "POST")
    public Result<String> untieWithdraw(@RequestBody UntieWithdrawVO untieWithdrawVO) {
        if(pinganMaintainSwitch){
            return Result.error(ErrorCodeConstants.SYSTEMMAINTAIN_900000.getErrorCode(), pinganMaintainTips);
        }
        try {
            com.akucun.fps.common.entity.Result<String> result = merchantServiceApi.untieWithdraw(untieWithdrawVO);
            if (Results.checkFpsResult(result)) {
                return Result.success(result.getData());
            } else {
                return Result.error(result.getErrorCode(), result.getErrorMessage());
            }
        } catch (Exception e) {
            Logger.error("AccountBankCardController untieWithdraw error:", e);
        }
        return null;
    }

    @Override
    @PostMapping(value = {"/selectBindCardListByParams"})
    @ApiOperation(value = "查询账户绑卡信息", notes = "查询账户绑卡信息", httpMethod = "POST")
    public ResultList<PinganCardVO> selectBindCardListByParams(@RequestBody BindCardQueryReq bindCardQueryReq) {
        return merchantQueryServiceApi.selectBindCardListByParams(bindCardQueryReq);
    }

    @Override
    @PostMapping(value = {"/selectSubBankCityPage"})
    @ApiOperation(value = "根据分支行名称模糊查询具体分支行名称", notes = "根据分支行名称模糊查询具体分支行名称", httpMethod = "POST")
    public ResultList<SubBankInfoDO> selectSubBankCityPage(@RequestBody SubBankInfoReq subBankInfoReq) {
        return acquireJointLineNumbeServiceApi.selectSpecialSubBankCityPage(subBankInfoReq);
    }

    @Override
    @PostMapping(value = {"/selectProvince"})
    @ApiOperation(value = "查询省份", notes = "查询省份", httpMethod = "POST")
    public ResultList<BankNode> selectProvince() {
        return acquireJointLineNumbeServiceApi.selectProvince();
    }

    @Override
    @PostMapping(value = {"/selectByBankName"})
    @ApiOperation(value = "根据银行名称模糊查询具体银行", notes = "根据银行名称模糊查询具体银行", httpMethod = "POST")
    public ResultList<BankInfoManageResp> selectByBankName(@RequestParam("bankName") String bankName) {
        return acquireJointLineNumbeServiceApi.selectByBankName(bankName);
    }

    @Override
    @PostMapping(value = {"/selectBankCity"})
    @ApiOperation(value = "根据省份查询市", notes = "根据省份查询市", httpMethod = "POST")
    public ResultList<BankCityInfo> selectBankCity(@RequestParam("nodeCode") String nodeCode) {
        return acquireJointLineNumbeServiceApi.selectBankCity(nodeCode);
    }

    @Override
    @PostMapping(value = {"/bindCardAndCheck"})
    @ApiOperation(value = "绑卡发送小额鉴权申请", notes = "绑卡发送小额鉴权申请", httpMethod = "POST")
    public Result<Void> bindCardAndCheck(@RequestBody @Validated BindCardInfoDTO bindCardInfoDTO) {
        if(pinganMaintainSwitch){
            return Result.error(ErrorCodeConstants.SYSTEMMAINTAIN_900000.getErrorCode(), pinganMaintainTips);
        }
        String convertCustomerCode = AccountUtils.getCustomerCode(bindCardInfoDTO.getCustomerCode(), bindCardInfoDTO.getCustomerType());
        if(accountUpgradeService.hasRecentUpgradeProcessing(convertCustomerCode, bindCardInfoDTO.getCustomerType())) {
            return Result.error(ErrorCodeConstants.BINDCARD_101901.getErrorCode(), ErrorCodeConstants.BINDCARD_101901.getErrorMessage());
        }
        try {
            boolean hasAuditingApply = taxTripPactionApplyService.hasAuditingApply(bindCardInfoDTO.getCustomerCode(), bindCardInfoDTO.getCustomerType());
            if(hasAuditingApply){
                Logger.info("该用户的税库银三方协议申请记录状态为审核中,不允许进行绑卡操作");
                return Result.error(ErrorCodeConstants.AUTHAPPLY_101900.getErrorCode(), ErrorCodeConstants.AUTHAPPLY_101900.getErrorMessage());
            }
        }catch (Exception e){
            Logger.error("绑卡小额鉴权服务异常,请稍后重试", e);
            return Result.error(300002, "绑卡小额鉴权服务异常,请稍后重试");
        }

        return merchantClient.bindCardAndCheck(bindCardInfoDTO);
    }

    @Override
    @PostMapping(value = {"/bindCardForMerSec"})
    @ApiOperation(value = "小额鉴权鉴权金额回写验证", notes = "小额鉴权鉴权金额回写验证", httpMethod = "POST")
    public Result<Void> bindCardForMerSec(@RequestBody @Validated BindCardVerifyDTO bindCardVerifyDTO) {
        return merchantClient.bindCardForMerSec(bindCardVerifyDTO);
    }

    @Override
    @PostMapping(value = {"/authStatusCheck"})
    @ApiOperation(value = "查询小额鉴权状态", notes = "查询小额鉴权状态", httpMethod = "POST")
    public Result<BindAuthResp> authStatusCheck(@RequestParam("customerCode") String customerCode, @RequestParam("customerType") String customerType) {
        return merchantClient.authStatusCheck(customerCode, customerType);
    }

    @Override
    @PostMapping(value = {"/queryAccountStatus"})
    @ApiOperation(value = "查询账户升级状态", notes = "查询账户升级状态", httpMethod = "POST")
    public Result<Boolean> queryAccountStatus(@RequestParam("customerCode") String customerCode, @RequestParam("customerType") String customerType) {
        return Result.success(accountService.queryAccountLockStatus(customerCode, customerType));
    }
}
