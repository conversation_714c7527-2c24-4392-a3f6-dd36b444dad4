package com.akucun.account.proxy.facade.controller.account;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.facade.stub.account.AccountWithdrawFacade;
import com.akucun.account.proxy.facade.stub.others.account.req.ShopkeeperIncentiveAwardWithdrawCalcTaxReq;
import com.akucun.account.proxy.facade.stub.others.account.vo.AccountTotalVO;
import com.akucun.account.proxy.facade.stub.others.account.vo.AccountWithdrawVO;
import com.akucun.account.proxy.facade.stub.others.account.vo.NotifyWithdrawVO;
import com.akucun.account.proxy.facade.stub.others.account.vo.WithdrawTaxDetailVO;
import com.akucun.account.proxy.service.acct.AccountTotalAmountService;
import com.akucun.account.proxy.service.acct.AccountWithdrawService;
import com.akucun.account.proxy.service.acct.WithdrawTaxService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: silei
 * @Date: 2021/3/5
 * @desc:
 */
@RestController
@Api(value = "账户提现相关", tags = {"账户提现相关"})
@RequestMapping("/api/account/proxy/withdraw")
public class AccountWithdrawController implements AccountWithdrawFacade {

    @Autowired
    private AccountWithdrawService accountWithdrawService;

    @Autowired
    private AccountTotalAmountService accountTotalAmountService;

    @Autowired
    private WithdrawTaxService withdrawTaxService;

    @Override
    @ApiOperation(value = "账户提现", notes = "账户提现", httpMethod = "POST")
    @PostMapping(value = "/applyWithdraw", produces = "application/json;charset=utf-8")
    public Result<String> applyWithdraw(@RequestBody AccountWithdrawVO accountWithdrawVO) {
        return accountWithdrawService.accountWithdraw(accountWithdrawVO);
    }

    @Override
    @ApiOperation(value = "账户提现结果通知", notes = "账户提现结果通知", httpMethod = "POST")
    @PostMapping(value = "/notifyWithdrawResp", produces = "application/json;charset=utf-8")
    public Result<Void> notifyWithdrawResp(@RequestBody NotifyWithdrawVO notifyWithdrawVO) {
        return accountWithdrawService.notifyWithdrawResult(notifyWithdrawVO);
    }

    @Override
    @ApiOperation(value = "查询账户提现汇总（商户）", notes = "查询账户提现汇总（商户）", httpMethod = "POST")
    @PostMapping(value = "/selectAccountTotal", produces = "application/json;charset=utf-8")
    public Result<AccountTotalVO> selectAccountTotal(@RequestBody AccountTotalVO accountTotalVO) {
        return accountTotalAmountService.queryAccountTotalAmount(accountTotalVO);
    }

    @Override
    @ApiOperation(value = "账户提现到微信余额", notes = "账户提现到微信余额", httpMethod = "POST")
    @PostMapping(value = "/applyWechatWithdraw", produces = "application/json;charset=utf-8")
    public Result<String> applyWechatWithdraw(@RequestBody AccountWithdrawVO accountWithdrawVO) {
        return  accountWithdrawService.accountWechatWithdraw(accountWithdrawVO);
    }

    @Override
    @ApiOperation(value = "店主激励奖励自动提现计税", notes = "店主激励奖励自动提现计税", httpMethod = "POST")
    @PostMapping(value = "/shopkeeperIncentiveAwardWithdrawCalcTax", produces = "application/json;charset=utf-8")
    public Result<WithdrawTaxDetailVO> shopkeeperIncentiveAwardWithdrawCalcTax(@RequestBody ShopkeeperIncentiveAwardWithdrawCalcTaxReq req) {
        return withdrawTaxService.shopkeeperIncentiveAwardWithdrawCalcTax(req);
    }

    @Override
    @ApiOperation(value = "添加提现记录", notes = "添加提现记录", httpMethod = "POST")
    @PostMapping(value = "/addWithdrawRecord", produces = "application/json;charset=utf-8")
    public Result<String> addWithdrawRecord(@RequestBody AccountWithdrawVO accountWithdrawVO) {
        return accountWithdrawService.addWithdrawRecord(accountWithdrawVO);
    }

    @Override
    @ApiOperation(value = "获取提现回单下载地址")
    @GetMapping(value = "/getWithdrawReceiptDownloadUrl")
    public Result<String> getWithdrawReceiptDownloadUrl(@RequestParam("withdrawNo") String withdrawNo) {
        return accountWithdrawService.getWithdrawReceiptDownloadUrl(withdrawNo);
    }
}
