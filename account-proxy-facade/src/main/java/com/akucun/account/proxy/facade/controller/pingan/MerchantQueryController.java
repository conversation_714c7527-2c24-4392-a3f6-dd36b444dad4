package com.akucun.account.proxy.facade.controller.pingan;

import com.akucun.account.proxy.facade.stub.pingan.MerchantQueryFacade;
import com.akucun.fps.common.entity.Result;
import com.akucun.fps.common.entity.ResultList;
import com.akucun.fps.pingan.client.model.query.BindCardQueryReq;
import com.akucun.fps.pingan.client.vo.PinganCardVO;
import com.akucun.fps.pingan.feign.api.merchantquery.MerchantQueryServiceApi;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@RestController
@Api(value = "商户查询", tags = {"商户查询"})
@RequestMapping("/api/account/proxy/pingan")
public class MerchantQueryController implements MerchantQueryFacade {

    @Resource
    private MerchantQueryServiceApi merchantQueryServiceApi;

    @Override
    @PostMapping(value = "/selectBindCardByCodePage")
    @ApiOperation(value = "查询绑定银行卡信息", notes = "查询绑定银行卡信息", httpMethod = "POST")
    public ResultList<PinganCardVO> selectBindCardByCodePage(@RequestParam("customerCode") String customerCode) {
        return merchantQueryServiceApi.selectBindCardByCodePage(customerCode);
    }

    @Override
    @PostMapping(value = "/selectBindCardByStatus")
    @ApiOperation(value = "根据状态查询绑定银行卡信息", notes = "根据状态查询绑定银行卡信息", httpMethod = "POST")
    public ResultList<PinganCardVO> selectBindCardByStatus(@RequestBody BindCardQueryReq bindCardQueryReq) {
        return merchantQueryServiceApi.selectBindCardByStatus(bindCardQueryReq);
    }

    @Override
    @PostMapping(value = "/selectBindCardByCardNo")
    @ApiOperation(value = "根据卡号查询已绑定卡信息", notes = "根据卡号查询已绑定卡信息", httpMethod = "POST")
    public Result<PinganCardVO> selectBindCardByCardNo(@RequestBody BindCardQueryReq bindCardQueryReq) {
        return merchantQueryServiceApi.selectBindCardByCardNo(bindCardQueryReq);
    }

    @Override
    @PostMapping(value = "/selectBindCardListByParams")
    @ApiOperation(value = "根据条件查询绑卡信息", notes = "根据条件查询绑卡信息", httpMethod = "POST")
    public ResultList<PinganCardVO> selectBindCardListByParams(@RequestBody BindCardQueryReq bindCardQueryReq) {
        return merchantQueryServiceApi.selectBindCardListByParams(bindCardQueryReq);
    }

    @Override
    @PostMapping(value = "/isHasBindSuccRecord")
    @ApiOperation(value = "判断是否有绑卡成功记录")
    public Result<Integer> isHasBindSuccRecord(@RequestBody BindCardQueryReq bindCardQueryReq) {
        return merchantQueryServiceApi.isHasBindSuccRecord(bindCardQueryReq);
    }
}
