package com.akucun.account.proxy.facade.config.sharding;

import com.akucun.account.center.common.constants.MagicNumber;
import com.akucun.account.center.common.util.StringCommonUtils;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import io.shardingsphere.api.config.ShardingRuleConfiguration;
import io.shardingsphere.api.config.TableRuleConfiguration;
import io.shardingsphere.api.config.strategy.ShardingStrategyConfiguration;
import io.shardingsphere.api.config.strategy.StandardShardingStrategyConfiguration;
import io.shardingsphere.shardingjdbc.api.ShardingDataSourceFactory;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.sql.DataSource;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

/**
 * @Author: silei
 * @Date: 2020/12/2
 * @desc:
 */

@Configuration
@EnableConfigurationProperties(value = ShardingProperties.class)
@MapperScan(basePackages = {"com.akucun.account.proxy.dao"}, sqlSessionTemplateRef = "sqlSessionTemplate")
public class ShardingConfig {

    @Autowired
    private ShardingProperties shardingProperties;

    @Bean
    public DataSource dataSource() {
        HikariConfig hikariConfig = new HikariConfig();
        hikariConfig.setDriverClassName(com.mysql.jdbc.Driver.class.getName());
        hikariConfig.setUsername(shardingProperties.getUsername());
        hikariConfig.setJdbcUrl(shardingProperties.getUrl());
        hikariConfig.setPassword(shardingProperties.getPassword());
        hikariConfig.setMaximumPoolSize(60);
        hikariConfig.setMaxLifetime(1800000);
        hikariConfig.setConnectionTimeout(30000);
        hikariConfig.setIdleTimeout(600000);
        hikariConfig.setMinimumIdle(10);
        hikariConfig.setPoolName("account_proxy");
        return new HikariDataSource(hikariConfig);
    }

    @Primary
    @Bean("shardingDataSource")
    public DataSource shardingDataSource(DataSource dataSource) throws SQLException {
        ShardingRuleConfiguration shardingRuleConfig = new ShardingRuleConfiguration();
        shardingRuleConfig.getTableRuleConfigs().add(buildTableRule("account_trade", "account_proxy.account_trade_", new StandardShardingStrategyConfiguration("customer_code", new TablePreciseShardingAlgorithm(), new TableRangeShardingAlgorithm())));
        shardingRuleConfig.getTableRuleConfigs().add(buildTableRule("account_trade_detail", "account_proxy.account_trade_detail_", new StandardShardingStrategyConfiguration("customer_code", new TablePreciseShardingAlgorithm(), new TableRangeShardingAlgorithm())));
        shardingRuleConfig.getTableRuleConfigs().add(buildTableRule("account_commission_trade", "account_proxy.account_commission_trade_", new StandardShardingStrategyConfiguration("customer_code", new TablePreciseShardingAlgorithm(), new TableRangeShardingAlgorithm())));
        shardingRuleConfig.getTableRuleConfigs().add(buildTableRule("payment_transfer", "account_proxy.payment_transfer_", new StandardShardingStrategyConfiguration("source_no", new TablePreciseShardingAlgorithm(), new TableRangeShardingAlgorithm())));
        shardingRuleConfig.getBindingTableGroups().add("account_trade, account_trade_detail, account_commission_trade, payment_transfer");
        Map<String, DataSource> dataSourceMap = new HashMap<>();
        dataSourceMap.put("account_proxy", dataSource);
        Properties properties = new Properties();
        properties.setProperty("sql.show", Boolean.FALSE.toString());
        return ShardingDataSourceFactory.createDataSource(dataSourceMap, shardingRuleConfig, new HashMap<>(), properties);
    }

    private TableRuleConfiguration buildTableRule(String logicTable, String tableName, ShardingStrategyConfiguration tableShardingStrategyConfig) {
        TableRuleConfiguration orderTableRuleConfig = new TableRuleConfiguration();
        orderTableRuleConfig.setLogicTable(logicTable);
        orderTableRuleConfig.setActualDataNodes(buildTable(tableName));
        orderTableRuleConfig.setTableShardingStrategyConfig(tableShardingStrategyConfig);
        return orderTableRuleConfig;
    }

    private String buildTable(String tableName) {
        StringBuilder buffer = new StringBuilder();
        for (int i = 0; i < MagicNumber.int_64; i++) {
            buffer.append(tableName).append(StringCommonUtils.tail2(i + "")).append(",");
        }
        return buffer.substring(0, buffer.length() - 1);
    }

}