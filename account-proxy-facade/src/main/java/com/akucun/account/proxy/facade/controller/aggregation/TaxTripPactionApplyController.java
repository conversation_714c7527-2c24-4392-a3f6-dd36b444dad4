package com.akucun.account.proxy.facade.controller.aggregation;

import com.aikucun.common2.base.Result;
import com.aikucun.common2.utils.BeanUtils;
import com.aikucun.common2.utils.CollectionUtils;
import com.aikucun.common2.utils.StringUtils;
import com.aikucun.common2.utils.datamasking.DataMask;
import com.akucun.account.proxy.client.member.FeignIndividualAuthServiceClient;
import com.akucun.account.proxy.common.enums.ResponseEnum;
import com.akucun.account.proxy.common.enums.TaxTripPactionApplyStatusEnum;
import com.akucun.account.proxy.common.utils.AccountUtils;
import com.akucun.account.proxy.dao.model.TaxTripPactionApply;
import com.akucun.account.proxy.facade.stub.aggregation.TaxTripPactionApplyFacade;
import com.akucun.account.proxy.facade.stub.others.dto.req.TaxTripPactionApplyReq;
import com.akucun.account.proxy.facade.stub.others.dto.req.TaxTripPactionApplyUpdateReq;
import com.akucun.account.proxy.facade.stub.others.dto.res.TaxTripPactionApplyResp;
import com.akucun.account.proxy.service.acct.TaxTripPactionApplyService;
import com.akucun.account.proxy.service.acct.bo.TaxTripPactionApplyBO;
import com.akucun.member.audit.model.dto.auth.IndividualAuthInfoRespDTO;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.mengxiang.base.common.log.Logger;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;


@Slf4j
@Api(value = "税库银三方协议审核申请接口", tags = {"税库银三方协议审核申请接口"})
@RestController
@RequestMapping("/api/account/aggregation/trippaction")
public class TaxTripPactionApplyController implements TaxTripPactionApplyFacade {

    @Autowired
    private TaxTripPactionApplyService taxTripPactionApplyService;
    @Autowired
    private FeignIndividualAuthServiceClient feignIndividualAuthServiceClient;

    /**
     * 税库银三方协议审核申请接口
     * @param taxTripPactionApplyReq
     * @return
     */
    @Override
    @PostMapping(value = {"/apply"})
    @ApiOperation(value = "税库银三方协议审核申请接口", notes = "税库银三方协议审核申请接口", httpMethod = "POST")
    public Result<String> apply(@RequestBody TaxTripPactionApplyReq taxTripPactionApplyReq){
        Logger.info("税库银三方协议审核申请接口入参为:{}", DataMask.toJSONString(taxTripPactionApplyReq));
        if(StringUtils.isNullOrBlank(taxTripPactionApplyReq.getCustomerCode())
            || StringUtils.isNullOrBlank(taxTripPactionApplyReq.getCustomerType())){
            Logger.info("TaxTripPactionApplyController.apply接口customerCode或customerType不能为空");
            return Result.error(ResponseEnum.PARAM_EXCEPTION.getCode(), ResponseEnum.PARAM_EXCEPTION.getMessage());
        }
        try {
            TaxTripPactionApply taxTripPactionApply = taxTripPactionApplyService.selectValidApply(taxTripPactionApplyReq.getCustomerCode(), taxTripPactionApplyReq.getCustomerType());
            if(taxTripPactionApply != null){
                Logger.info("{}该用户存在有效的申请记录，状态为:{},请勿重复申请", taxTripPactionApply.getCustomerCode(), taxTripPactionApply.getStatus());
                return Result.error(ResponseEnum.PARAM_EXCEPTION.getCode(),
                        "存在有效的申请记录，状态为:"+ TaxTripPactionApplyStatusEnum.getDescByStatus(taxTripPactionApply.getStatus())+",请勿重复申请");
            }

            //调用会员侧查询个体工商户认证信息
            IndividualAuthInfoRespDTO individualAuthInfoRespDTO = feignIndividualAuthServiceClient.queryIndividualInfo(taxTripPactionApplyReq.getUserId(), taxTripPactionApplyReq.getCustomerType());
            if(individualAuthInfoRespDTO == null){
                Logger.info("未查询到个体工商户认证信息");
                return Result.error(ResponseEnum.PARAM_EXCEPTION.getCode(), "未查询到个体工商户认证信息");
            }

            TaxTripPactionApplyBO taxTripPactionApplyBO = BeanUtils.copy(taxTripPactionApplyReq, TaxTripPactionApplyBO.class);
            taxTripPactionApplyBO.setStatus(TaxTripPactionApplyStatusEnum.AUDITING.getStatus());
            taxTripPactionApplyBO.setLegalPersonName(individualAuthInfoRespDTO.getLegalPersonName());
            taxTripPactionApplyBO.setMerchantName(individualAuthInfoRespDTO.getMerchantName());
            taxTripPactionApplyBO.setSocialCreditCode(individualAuthInfoRespDTO.getCreditCode());
            boolean saveResult = taxTripPactionApplyService.saveApply(taxTripPactionApplyBO);
            if(saveResult){
                return Result.success("申请成功");
            }
        } catch (Exception e){
            Logger.error("税库银三方协议审核申请接口异常", e);
        }

        return Result.error(ResponseEnum.SYSTEM_ERROR.getCode(), ResponseEnum.SYSTEM_ERROR.getMessage());
    }

    /**
     * 查询所有申请记录
     * @param customerCode
     * @param customerType
     * @return
     */
    @Override
    @GetMapping(value = {"/findTaxTripPactionApply"})
    @ApiOperation(value = "查询所有申请记录", notes = "查询所有申请记录", httpMethod = "GET")
    public Result<List<TaxTripPactionApplyResp>> findTaxTripPactionApply(@RequestParam("customerCode") String customerCode,
                                                                        @RequestParam("customerType") String customerType){
        Logger.info("findTaxTripPactionApply接口入参customerCode:{}, customerType:{}", customerCode, customerType);
        if(StringUtils.isNullOrBlank(customerCode) || StringUtils.isNullOrBlank(customerType)){
            Logger.info("findTaxTripPactionApply接口入参不能为空");
            return Result.error(ResponseEnum.PARAM_EXCEPTION.getCode(), ResponseEnum.PARAM_EXCEPTION.getMessage());
        }
        try {
            //处理店主的customerCode,如果没有NM前缀，需要拼接
            customerCode = AccountUtils.convertCustomerCode(customerCode.trim(), customerType);
            List<TaxTripPactionApply> taxTripPactionApplies = taxTripPactionApplyService.selectAllApply(customerCode, customerType);
            if(CollectionUtils.isEmpty(taxTripPactionApplies)){
                Logger.info("根据入参customerCode:{},customerType:{},没有查询到申请记录", customerCode, customerType);
                return Result.success(Lists.newArrayList());
            }
            List<TaxTripPactionApplyResp> taxTripPactionApplyResps = new ArrayList<>();
            for(TaxTripPactionApply taxTripPactionApply : taxTripPactionApplies){
                TaxTripPactionApplyResp taxTripPactionApplyResp = BeanUtils.copy(taxTripPactionApply, TaxTripPactionApplyResp.class);
                taxTripPactionApplyResps.add(taxTripPactionApplyResp);
            }
            return Result.success(taxTripPactionApplyResps);
        }catch (Exception e){
            Logger.error("税库银三方协议审核记录查询接口异常", e);
            return Result.error(ResponseEnum.SYSTEM_ERROR.getCode(), ResponseEnum.SYSTEM_ERROR.getMessage());
        }

    }

    /**
     * 更新申请状态
     * @param taxTripPactionApplyUpdateReq
     * @return
     */
    @Override
    @PostMapping(value = {"/updateApplyStatus"})
    @ApiOperation(value = "更新申请状态", notes = "更新申请状态", httpMethod = "POST")
    public Result<Boolean> updateApplyStatus(@RequestBody TaxTripPactionApplyUpdateReq taxTripPactionApplyUpdateReq){

        Logger.info("TaxTripPactionApplyController.updateApplyStatus接口入参为:{}", DataMask.toJSONString(taxTripPactionApplyUpdateReq));

        try {
            TaxTripPactionApply taxTripPactionApply = taxTripPactionApplyService.selectByAuditNo(taxTripPactionApplyUpdateReq.getAuditNo());
            if(taxTripPactionApply == null){
                Logger.info("根据入参auditNo:{}未查询到申请记录，更新失败", taxTripPactionApplyUpdateReq.getAuditNo());
                return Result.error(ResponseEnum.PARAM_EXCEPTION.getCode(), "未查询到申请记录");
            }
            if(TaxTripPactionApplyStatusEnum.AUDIT_PASS.getStatus() == taxTripPactionApply.getStatus()){
                if(taxTripPactionApplyUpdateReq.getAuditResult()){
                    return Result.success(true);
                }
                return Result.error(ResponseEnum.PARAM_EXCEPTION.getCode(), "该条申请记录已通过审核，不允许修改为其他状态");
            }

            if(TaxTripPactionApplyStatusEnum.AUDIT_REJECT.getStatus() == taxTripPactionApply.getStatus()){
                if(!taxTripPactionApplyUpdateReq.getAuditResult()){
                    return Result.success(true);
                }
                Result result = Result.error(ResponseEnum.PARAM_EXCEPTION.getCode(), "该条申请记录已被驳回，不允许修改为其他状态");
                result.setData(false);
                return result;
            }

            TaxTripPactionApply needUpateApply = taxTripPactionApply;
            int status = TaxTripPactionApplyStatusEnum.AUDIT_PASS.getStatus();
            if(!taxTripPactionApplyUpdateReq.getAuditResult()){
                status = TaxTripPactionApplyStatusEnum.AUDIT_REJECT.getStatus();
                String rejectReason = taxTripPactionApplyService.convertRejectReason(taxTripPactionApplyUpdateReq.getRejectReason());
                needUpateApply.setRejectReason(rejectReason);
            }
            needUpateApply.setStatus(status);
            boolean upateResult = taxTripPactionApplyService.updateApply(needUpateApply);
            if(upateResult){
                return Result.success(true);
            }
        }catch (Exception e){
            Logger.error("更新申请记录时异常", e);
        }

        return Result.error(ResponseEnum.SYSTEM_ERROR.getCode(), ResponseEnum.SYSTEM_ERROR.getMessage());
    }
}
