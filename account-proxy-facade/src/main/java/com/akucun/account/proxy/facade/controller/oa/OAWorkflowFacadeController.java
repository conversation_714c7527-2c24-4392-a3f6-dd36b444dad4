package com.akucun.account.proxy.facade.controller.oa;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.facade.stub.oa.OAWorkflowFacade;
import com.akucun.account.proxy.facade.stub.others.oa.req.OAWorkflowCreateRequest;
import com.akucun.account.proxy.facade.stub.others.oa.req.OAWorkflowQueryRequest;
import com.akucun.account.proxy.facade.stub.others.oa.resp.OAWorkflowCreateResponse;
import com.akucun.account.proxy.facade.stub.others.oa.resp.OAWorkflowResponseInfo;
import com.akucun.account.proxy.service.oa.OAWorkflowBaseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Create on : 2025/1/14 17:31
 **/
@Api(tags = {"OA工作流基础接口"}, value = "OA工作流基础接口")
@RestController
public class OAWorkflowFacadeController implements OAWorkflowFacade {

    @Autowired
    private OAWorkflowBaseService oaWorkflowBaseService;

    @ApiOperation("创建OA工作流程")
    @Override
    public Result<OAWorkflowCreateResponse> create(@RequestBody @Validated OAWorkflowCreateRequest request) {
        return oaWorkflowBaseService.create(request);
    }

    @ApiOperation("查询OA工作流程")
    @Override
    public Result<OAWorkflowResponseInfo> queryWorkflowByRequestId(@RequestBody @Validated OAWorkflowQueryRequest request) {
        return oaWorkflowBaseService.queryWorkflowByRequestId(request);
    }

}
