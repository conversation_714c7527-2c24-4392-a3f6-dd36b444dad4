package com.akucun.account.proxy.facade.controller.trade.test;

import com.akucun.account.proxy.facade.stub.others.trade.req.TradeReq;
import com.akucun.account.proxy.facade.stub.others.trade.res.TradeRes;
import com.akucun.account.proxy.facade.stub.trade.TradeFacade;
import com.akucun.account.proxy.service.postaction.task.WithdrawServiceTask;
import com.akucun.account.proxy.service.tradeflow.TradeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Api(value = "测试", tags = {"测试"})
@RequestMapping("/api/test")
public class TestController {

    @Autowired
    private WithdrawServiceTask withdrawServiceTask;

    @ApiOperation(value = "交易")
    @PostMapping(value = "/withdraw", produces = "application/json;charset=utf-8")
    public void withdraw() {
         withdrawServiceTask.execute("");
    }

}
