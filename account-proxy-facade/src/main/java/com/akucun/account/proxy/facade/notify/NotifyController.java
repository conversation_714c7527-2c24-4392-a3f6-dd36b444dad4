package com.akucun.account.proxy.facade.notify;

import com.akucun.fps.account.client.api.WithdrawServiceClient;
import com.akucun.fps.account.client.model.NotifyDO;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.akucun.account.proxy.facade.stub.account.check.NotifyReqCheck;
import com.akucun.account.proxy.facade.stub.others.account.req.NotifyReq;
import com.akucun.account.proxy.service.notify.NotifyService;
import com.akucun.common.Result;
import com.mengxiang.base.common.log.Logger;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@RestController
@Api(value = "异步通知相关", tags = {"提现异步通知"})
@RequestMapping("/api/account/proxy/withdraw")
public class NotifyController {
	
	@Autowired
	private NotifyService notifyService;

    @Reference(check = false)
    private WithdrawServiceClient withdrawServiceClient;
	
    @ApiOperation(value = "提现异步通知）")
    @PostMapping(value = "/notify", produces = "application/json;charset=utf-8")
    public Result<Void> notify(@RequestBody NotifyReq notifyReq) {
        Logger.info("NotifyController notify req:{}", JSON.toJSONString(notifyReq));
        return notifyService.withdrawNotify(NotifyReqCheck.notifyReqCheck(notifyReq));
    }

    @ApiOperation(value = "豆联盟&钱包提现异步通知")
    @PostMapping(value = "/notifyV2", produces = "application/json;charset=utf-8")
    public Result<Void> notifyV2(@RequestBody NotifyDO notifyDO) {
        Logger.info("NotifyController notifyV2 req:{}", JSON.toJSONString(notifyDO));
        com.akucun.fps.common.entity.Result<Void> result = withdrawServiceClient.notify(notifyDO);
        Logger.info("NotifyController notifyV2 response:{}", JSON.toJSONString(result));
        Result<Void> voidResult = Result.error();
        voidResult.setSuccess(result.isSuccess());
        voidResult.setCode(result.getErrorCode());
        voidResult.setMessage(result.getErrorMessage());
        return voidResult;
    }
}
