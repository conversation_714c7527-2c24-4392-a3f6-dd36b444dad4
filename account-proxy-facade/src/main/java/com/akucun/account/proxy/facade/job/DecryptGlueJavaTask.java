package com.akucun.account.proxy.facade.job;

import com.aikucun.common2.base.Result;
import com.aikucun.common2.utils.DateUtils;
import com.akucun.account.proxy.client.customer.MerchantApplyApi;
import com.mengxiang.base.common.log.Logger;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.io.*;
import java.math.BigDecimal;
import java.net.URL;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Create on : 2025/1/20 22:06
 **/
public class DecryptGlueJavaTask extends IJobHandler {
    String LOG_PREFIX = "解密-";

    @Autowired
    private MerchantApplyApi merchantApplyApi;

    @Override
    public ReturnT<String> execute(String filePath) throws Exception {
        if (StringUtils.isEmpty(filePath)) {
            Logger.error(LOG_PREFIX + "参数为空！");
            return ReturnT.FAIL;
        }

        try {
            //01-下载文件到本地
            String localFilePath = getLocalFilePath(filePath);
            Logger.info(LOG_PREFIX + "下载文件到本地成功:{}", localFilePath);

            //02-读取文件内容
            long rowNum = 0l;
            List<String> decrptList = new ArrayList<>();
            try {
                BufferedReader reader = new BufferedReader(new FileReader(localFilePath));
                String line;
                while ((line = reader.readLine()) != null) {
                    String[] values = line.split(",");

                    Result<String> decryptRst = merchantApplyApi.dataencrypt(values[0], "decrypt");
                    merchantApplyApi.dataencrypt(values[0], "decrypt");
                    if (!ObjectUtils.isEmpty(decryptRst) && decryptRst.getSuccess() && !StringUtils.isEmpty(decryptRst.getData())) {
                        decrptList.add(decryptRst.getData());
                        continue;
                    }
                    decrptList.add("失败");

                    rowNum++;
                }
            } catch (Exception e) {
                Logger.error(LOG_PREFIX + "解析CSV文件并解密失败", e);
            }

            if (!CollectionUtils.isEmpty(decrptList)) {
                String csvFilePath = "output" + DateUtils.format(new Date(), "yyyyMMddHHmmssSSS") + ".csv";
                File outputFile = new File(csvFilePath);
                if (!outputFile.exists()) {
                    outputFile.createNewFile();
                }
                writeListToCsv(decrptList, csvFilePath);
                Logger.info(LOG_PREFIX + "解密文件路径:{}", csvFilePath);
            }
        } catch (Exception e) {
            Logger.error(LOG_PREFIX + "解密失败", e);
        }

        return null;
    }


    //======================基础方法：http文件下载===============================
    //01-下载文件到本地
    public String getLocalFilePath(String httpFileUrl) throws IOException {
        String localFilePath = UUID.randomUUID().toString() + ".csv";

        try {
            //重新构建本地临时文件
            File file = new File(localFilePath);
            if (file.exists()) {
                file.delete();
            }
            file.createNewFile();

            //拷贝文件
            downloadCSV(httpFileUrl, localFilePath);
            Logger.info(LOG_PREFIX + "CSV文件下载成功！");
        } catch (Exception e) {
            Logger.error(LOG_PREFIX + "下载CSV文件时发生错误: ", e);
        }

        return localFilePath;
    }

    public void downloadCSV(String fileUrl, String localFilePath) throws IOException {
        URL url = new URL(fileUrl);
        int bufferSize = 50 * 1024; // 50KB

        try {
            BufferedInputStream buffin = new BufferedInputStream(url.openStream(), bufferSize);
            BufferedOutputStream buffout = new BufferedOutputStream(new FileOutputStream(localFilePath), bufferSize);
            byte[] buffer = new byte[bufferSize];
            int bytesRead;
            while ((bytesRead = buffin.read(buffer)) != -1) {
                buffout.write(buffer, 0, bytesRead);
            }
            buffout.flush();
            buffout.close();
            buffin.close();
        } catch (Exception e) {
            Logger.error(LOG_PREFIX + "下载CSV文件时发生错误: ", e);
        }
    }

    //02-写入结果到文件
    public static void writeListToCsv(List<String> dataList, String filePath) throws IOException {
        BufferedWriter writer = new BufferedWriter(new FileWriter(filePath));
        try {
            for (String item : dataList) {
                writer.write(item);
                writer.newLine(); // 写入换行符
            }
            writer.flush();
        } catch (IOException e) {
            Logger.warn("文件写入失败", e);
        } finally {
            writer.close();
        }
    }
}
