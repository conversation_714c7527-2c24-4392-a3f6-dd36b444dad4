package com.akucun.account.proxy.facade.config;

import com.xxl.job.core.executor.impl.XxlJobSpringExecutor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class XXLScheduleConfig {
	@Value("${xxl.job.admin.addresses:http://xxl-job-new.akctest1.com}")
	private String adminAddresses;

	@Value("${xxl.job.executor.appname:account-proxy}")
	private String appName;

//	@Value("${xxl.job.executor.ip:}")
//	private String ip;
//
//	@Value("${xxl.job.executor.port}")
//	private String port;

//	@Value("${xxl.job.accessToken:}")
//	private String accessToken;

	@Value("${xxl.job.executor.logpath:/home/<USER>/logs/}")
	private String logPath;

	@Value("${xxl.job.executor.logretentiondays:5}")
	private int logRetentionDays;

	@Bean
	public XxlJobSpringExecutor xxlJobExecutor() {
		XxlJobSpringExecutor xxlJobSpringExecutor = new XxlJobSpringExecutor();
		xxlJobSpringExecutor.setAdminAddresses(adminAddresses);
		xxlJobSpringExecutor.setAppname(appName);
//		xxlJobSpringExecutor.setIp(ip);
//		xxlJobSpringExecutor.setPort(Integer.parseInt(port));
//		xxlJobSpringExecutor.setAccessToken(accessToken);
		xxlJobSpringExecutor.setLogPath(logPath);
		xxlJobSpringExecutor.setLogRetentionDays(logRetentionDays);
		return xxlJobSpringExecutor;
	}
}
