package com.akucun.account.proxy.facade.controller;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.client.file.FileUploadClient;
import com.akucun.account.proxy.client.wechat.PayGatewayWxClient;
import com.akucun.account.proxy.dao.model.PaymentTransfer;
import com.akucun.account.proxy.service.common.SpringContextHolder;
import com.akucun.account.proxy.service.postaction.task.WithdrawReceiptDownloadTask;
import com.akucun.account.proxy.service.transfer.PaymentTransferService;
import com.akucun.account.proxy.service.transfer.bo.TransferGatewayBO;
import com.akucun.account.proxy.service.transfer.impl.TransferChannelGatewayServiceImpl;
import com.mengxiang.fileupload.dto.FileInfo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@Api(value = "工具", tags = {"工具"})
@RequestMapping("/api/util")
public class UtilController {

    @Autowired
    private TransferChannelGatewayServiceImpl transferChannelGatewayService;

    @Autowired
    private PaymentTransferService paymentTransferService;

    @Autowired
    private PayGatewayWxClient payGatewayWxClient;

    @Autowired
    private FileUploadClient fileUploadClient;

    @ApiOperation(value = "付款到零钱路由")
    @GetMapping(value = "/queryGatewayByChannelCode")
    public TransferGatewayBO queryGatewayByChannelCode(@RequestParam("channelCode") String channelCode,
                                                       @RequestParam("tenantId") String tenantId,
                                                       @RequestParam("appId") String appId) {
        return transferChannelGatewayService.queryGatewayByChannelCode(channelCode, tenantId, appId);
    }


    @ApiOperation(value = "查询付款到零钱记录")
    @GetMapping(value = "/queryBySourceNo")
    public PaymentTransfer queryBySourceNo(@RequestParam("channelCode") String channelCode,
                                           @RequestParam("sourceNo") String sourceNo) {
        return paymentTransferService.queryBySourceNo(channelCode, sourceNo);

    }

    @ApiOperation(value = "查询微信提现回单")
    @GetMapping(value = "/queryWechatWithdrawReceipt")
    public Result<String> queryWechatWithdrawReceipt(@RequestParam("batchNo") String batchNo,
                                                    @RequestParam("withdrawNo") String withdrawNo,
                                                    @RequestParam("merchantCode") String merchantCode) {
        return payGatewayWxClient.downloadTransferReceipt(batchNo, withdrawNo, merchantCode, Boolean.FALSE);
    }

    @ApiOperation(value = "添加提现回单下载定时任务")
    @PostMapping(value = "/addWithdrawReceiptDownloadTask")
    public Result<Void> addWithdrawReceiptDownloadTask(@RequestParam("withdrawChannel") String withdrawChannel,
                                                      @RequestParam("withdrawNo") String withdrawNo,
                                                      @RequestParam(value = "batchNo", required = false) String batchNo,
                                                      @RequestParam(value = "merchantCode", required = false) String merchantCode,
                                                      @RequestParam("isTenantCustomer") Boolean isTenantCustomer,
                                                      @RequestParam("customerType") String customerType) {
        SpringContextHolder.getBean(WithdrawReceiptDownloadTask.class).init(withdrawChannel, withdrawNo, batchNo, merchantCode, isTenantCustomer, customerType);
        return Result.success();
    }

    @ApiOperation(value = "获取临时文件下载地址")
    @GetMapping(value = "/getTempFileDownloadUrl")
    public Result<FileInfo> getTempFileDownloadUrl(@RequestParam("fileName") String fileName,
                                                @RequestParam("parentDirectory") String parentDirectory,
                                                @RequestParam(value = "expires", required = false) Long expires) {
        return fileUploadClient.getTmpSign(fileName, expires, parentDirectory);
    }
}
