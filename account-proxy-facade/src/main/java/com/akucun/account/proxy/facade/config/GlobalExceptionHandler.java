package com.akucun.account.proxy.facade.config;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.common.enums.ResponseEnum;
import com.akucun.account.proxy.common.exception.AccountProxyException;
import com.mengxiang.base.common.log.Logger;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * 全局异常拦截器
 * @description GlobalExceptionHandler
 * <AUTHOR>
 * @date 2023/3/13 14:00
 * @version v1.0.0
 */
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 拦截业务异常
     * @param e
     * @return
     */
    @ExceptionHandler({AccountProxyException.class})
    public Result handleBcsBillException(AccountProxyException e) {
        Logger.warn("【业务异常】异常信息：{}，异常码：{} ", e.getMessage(), e.getErrorCode());
        return Result.error(e.getErrorCode(), e.getMessage());
    }

    /**
     * 拦截空指针异常
     *
     * @param e
     * @return
     */
    @ExceptionHandler(NullPointerException.class)
    public Result handleNullPointerException(NullPointerException e) {
        Logger.warn("【NP异常】异常信息：{}", e.getMessage(), e);
        return Result.error(ResponseEnum.SYSTEM_EXCEPTION.getCode(), e.getMessage());
    }

    /**
     * 拦截未知异常
     *
     * @param e
     * @return
     */
    @ExceptionHandler(Exception.class)
    public Result handleException(Exception e) {
        Logger.warn("【系统异常】异常信息：{}", e.getMessage(), e);
        return Result.error(ResponseEnum.SYSTEM_EXCEPTION.getCode(), e.getMessage());
    }

    /**
     * 请求方法不支持
     *
     * @param ex
     * @return
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public Result handleRequestMethodException(HttpRequestMethodNotSupportedException ex) {
        Logger.warn("【参数异常】请求方法不支持，异常信息：{}", ex.getMessage(), ex);
        return Result.error(ResponseEnum.SYSTEM_EXCEPTION.getCode(), ex.getMessage());
    }

    /**
     * 缺少参数
     *
     * @param ex
     * @return
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    public Result handleMissingParamMethodException(MissingServletRequestParameterException ex) {
        Logger.warn("【参数异常】缺少请求参数，异常信息：{}", ex.getMessage(), ex);
        return Result.error(ResponseEnum.PARAM_EXCEPTION.getCode(), "缺少请求参数");
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Result handleMethodArgumentNotValidException(MethodArgumentNotValidException ex) {
        Logger.warn("【参数异常】请求参数验证不通过，异常信息：{}", ex.getMessage(), ex);
        Result result = Result.error(ResponseEnum.PARAM_EXCEPTION.getCode(), "请求参数验证不通过");
        BindingResult bindingResult = ex.getBindingResult();
        if (bindingResult.hasErrors()) {
            bindingResult.getAllErrors().forEach(e -> {
                FieldError fieldError = (FieldError) e;
                result.setMessage(fieldError.getDefaultMessage());
            });
        }
        return result;
    }

}