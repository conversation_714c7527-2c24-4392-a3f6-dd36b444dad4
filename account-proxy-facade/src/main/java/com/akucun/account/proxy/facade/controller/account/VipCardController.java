package com.akucun.account.proxy.facade.controller.account;

import com.aikucun.common2.base.Result;
import com.aikucun.common2.utils.datamasking.DataMask;
import com.akucun.account.proxy.common.enums.ResponseEnum;
import com.akucun.account.proxy.common.exception.AccountProxyException;
import com.akucun.account.proxy.dao.model.MshopVipCard;
import com.akucun.account.proxy.facade.stub.account.VipCardFacade;
import com.akucun.account.proxy.facade.stub.others.dto.req.VipCardInfoReq;
import com.akucun.account.proxy.facade.stub.others.dto.req.VipCardWriteOffReq;
import com.akucun.account.proxy.facade.stub.others.dto.res.VipCardInfoResp;
import com.akucun.account.proxy.service.acct.VipCardService;
import com.alibaba.fastjson.JSON;
import com.mengxiang.base.common.log.Logger;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2021/01/03 13:48
 */
@RestController
@RequestMapping("/api/vipcard")
public class VipCardController implements VipCardFacade {

    @Autowired
    private VipCardService vipCardService;

    @Value("${vipcard.interface.flag:false}")
    private Boolean interfaceFlag;

    @Override
    @PostMapping(value = "/valid/info")
    public Result<VipCardInfoResp> vipCardValidInfo(@RequestBody @Validated VipCardInfoReq vipCardInfoReq) {
        Logger.info("VipCardController vipCardValidInfo request:{}", DataMask.toJSONString(vipCardInfoReq));
        try {
            VipCardInfoResp vipCardInfoResp = vipCardService.vipCardValidInfo(vipCardInfoReq);
            Result<VipCardInfoResp> result = Result.success(vipCardInfoResp);
            Logger.info("VipCardController vipCardValidInfo request:{},response:{}", DataMask.toJSONString(vipCardInfoReq), DataMask.toJSONString(result));
            return result;
        } catch (AccountProxyException e) {
            Logger.warn("VipCardController vipCardValidInfo warn：{}", e);
            return Result.error(e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            Logger.error("VipCardController vipCardValidInfo error：{}", e);
            return Result.error(ResponseEnum.SYSTEM_EXCEPTION.getCode(), ResponseEnum.SYSTEM_EXCEPTION.getMessage());
        }
    }

    @Override
    @PostMapping(value = "/writeOff")
    public Result<Boolean> vipCardWriteOff(@RequestBody @Validated VipCardWriteOffReq vipCardWriteOffReq) {
        Logger.info("VipCardController vipCardWriteOff request:{}", DataMask.toJSONString(vipCardWriteOffReq));
        try {
            Boolean writeOffResult = vipCardService.vipCardWriteOff(vipCardWriteOffReq);
            Result<Boolean> result = Result.success(writeOffResult);
            Logger.info("VipCardController vipCardWriteOff request:{},response:{}", DataMask.toJSONString(vipCardWriteOffReq), DataMask.toJSONString(result));
            return result;
        } catch (AccountProxyException e) {
            Logger.warn("VipCardController vipCardWriteOff warn：{}", e);
            return Result.error(e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            Logger.error("VipCardController vipCardWriteOff error：{}", e);
            return Result.error(ResponseEnum.SYSTEM_EXCEPTION.getCode(), ResponseEnum.SYSTEM_EXCEPTION.getMessage());
        }
    }

    @ApiOperation(value = "保存或更新VIP卡记录表")
    @PostMapping("/saveOrUpdateVipCard")
    public Result saveOrUpdateVipCard(@RequestBody MshopVipCard request) {
        Logger.info("保存或更新VIP卡记录表，入参：{}", DataMask.toJSONString(request));
        if(!interfaceFlag) {
            return Result.error(500, "不允许访问");
        }
        return Result.success(vipCardService.saveOrUpdate(request));
    }
}
