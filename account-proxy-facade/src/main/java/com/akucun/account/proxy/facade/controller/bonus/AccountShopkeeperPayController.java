package com.akucun.account.proxy.facade.controller.bonus;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.common.utils.Results;
import com.akucun.account.proxy.common.enums.ResponseEnum;
import com.akucun.account.proxy.facade.stub.account.AccountShopkeeperPayFacade;
import com.akucun.account.proxy.facade.stub.others.account.req.AccountShopkeeperPayReq;
import com.akucun.fps.account.client.api.AccountShopkeeperPayService;
import com.akucun.fps.account.client.model.AccountShopkeeperPayDO;
import com.alibaba.dubbo.config.annotation.Reference;
import com.mengxiang.base.common.log.Logger;
import com.mengxiang.base.common.utils.datamasking.DataMask;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 */
@RestController
@Api(value = "店主支付账户服务", tags = {"店主支付账户服务"})
@RequestMapping("/api/account/proxy")
public class AccountShopkeeperPayController implements AccountShopkeeperPayFacade {
    @Reference(check = false)
    AccountShopkeeperPayService accountShopkeeperPayService;

    /**
     * 新增店主二维码信息
     *
     * @param accountShopkeeperPayReq
     * @return
     */
    @Override
    @ApiOperation(value = "新增店主二维码信息", notes = "新增店主二维码信息", httpMethod = "POST")
    @PostMapping(value = "/addAccountShopkeeperPayList", produces = "application/json;charset=utf-8")
    public Result<Void> addAccountShopkeeperPayList(@RequestBody AccountShopkeeperPayReq accountShopkeeperPayReq) {
        Logger.info("AccountShopkeeperPayController addAccountShopkeeperPayList req:{}", DataMask.toJSONString(accountShopkeeperPayReq));
        AccountShopkeeperPayDO payDO = new AccountShopkeeperPayDO();
        BeanUtils.copyProperties(accountShopkeeperPayReq, payDO);
        com.akucun.fps.common.entity.Result result = accountShopkeeperPayService.addAccountShopkeeperPayList(payDO);
        if (result == null) {
            return Results.error(ResponseEnum.SERVER_RESP_ERROR);
        }
        if (result.isSuccess()) {
            return Result.success();
        }
        return Result.error(result.getErrorCode(), result.getErrorMessage());
    }
}
