package com.akucun.account.proxy.facade.controller.test;

import com.aikucun.common2.base.Result;
import com.aikucun.common2.base.exception.BusinessException;
import com.akucun.account.proxy.common.utils.SpringBeanUtil;
import com.akucun.account.proxy.dao.mapper.WithdrawApplyRecordMapper;
import com.akucun.account.proxy.dao.model.WithdrawApplyRecord;
import com.akucun.account.proxy.facade.stub.others.account.vo.NotifyWithdrawVO;
import com.akucun.account.proxy.service.acct.impl.AccountWithdrawServiceImpl;
import com.akucun.account.proxy.service.help.TenantCoreHelper;
import com.alibaba.fastjson.JSON;
import com.mengxiang.base.common.log.Logger;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;
@RestController
@Api(value = "测试", tags = {"测试"})
@RequestMapping("/api/test")
public class GuleTestController {
    @GetMapping(value = "/deleteBatchIds")
    @ApiOperation(value = "根据Ids删除租户信息")
    public void test(){
        //AccountTenantCustomerMapper accountTenantCustomerMapper = SpringBeanUtil.getBean("accountTenantCustomerMapper", AccountTenantCustomerMapper.class);
        //List<Long> ids= Arrays.asList(122L);
        //int i = accountTenantCustomerMapper.deleteBatchIds(ids);
        WithdrawApplyRecordMapper withdrawApplyRecordMapper = SpringBeanUtil.getBean("withdrawApplyRecordMapper", WithdrawApplyRecordMapper.class);
        AccountWithdrawServiceImpl accountWithdrawService=SpringBeanUtil.getBean("accountWithdrawServiceImpl", AccountWithdrawServiceImpl.class);
        List<Long> ids=Arrays.asList(7621940L,
                7616168L,
                7792724L,
                7310171L,
                7287770L,
                7412346L,
                7589146L,
                7623263L,
                7328901L,
                7367089L,
                7562506L,
                7590565L,
                7633135L,
                7752913L,
                7859960L,
                7777790L,
                7833043L,
                7870748L,
                7464732L,
                7509172L,
                7626689L,
                7666179L,
                7753810L,
                7815902L,
                7470202L,
                7530466L,
                7530472L,
                7664599L,
                7693985L,
                7693991L,
                7531448L,
                7823299L,
                7853340L,
                7542752L,
                7699755L,
                7347716L,
                7487029L,
                7784800L,
                7444921L,
                7821431L,
                7862647L,
                7736491L,
                7463121L,
                7754651L,
                7517148L);
        List<WithdrawApplyRecord> withdrawApplyRecords = withdrawApplyRecordMapper.selectBatchIds(ids);
        if(withdrawApplyRecords!=null && withdrawApplyRecords.size()>0){
            for (WithdrawApplyRecord withdrawApplyRecord : withdrawApplyRecords) {
                NotifyWithdrawVO notifyWithdrawVO=new NotifyWithdrawVO();
                notifyWithdrawVO.setCustomerCode(withdrawApplyRecord.getCustomerType()+withdrawApplyRecord.getCustomerCode());
                notifyWithdrawVO.setAmount(withdrawApplyRecord.getAmount());
                notifyWithdrawVO.setResult(Boolean.TRUE);
                notifyWithdrawVO.setCustomerType(withdrawApplyRecord.getCustomerType());
                notifyWithdrawVO.setSourceBillNo(withdrawApplyRecord.getWithdrawNo());
                Logger.info("提现成功-回调通知请求开始：request={}", JSON.toJSONString(notifyWithdrawVO));
                Result<Void> voidResult = accountWithdrawService.notifyWithdrawResult(notifyWithdrawVO);
                Logger.info("提现成功-回调通知请求结束：result={}", JSON.toJSONString(voidResult));
            }
        }
    }


    @Autowired
    private TenantCoreHelper tenantCoreHelper;
    @GetMapping("/isCompanyShop")
    @ApiOperation("是否是企业饷店租户判断")
    public Result<Boolean> isCompanyShop(@RequestParam("tenantId") Long tenantId) throws BusinessException {
        Pair<Boolean, Integer> pair = tenantCoreHelper.isCompanyShop(tenantId);
        return Result.success(pair.getLeft());
    }
}
