package com.akucun.account.proxy.facade.account;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.facade.producer.BizMqProducer;
import com.akucun.account.proxy.facade.stub.others.dto.req.TransferChannelGatewayReq;
import com.akucun.account.proxy.facade.stub.others.dto.req.TransferGatewayReq;
import com.akucun.account.proxy.service.transfer.TransferChannelGatewayService;
import com.akucun.account.proxy.service.transfer.TransferGatewayService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.mengxiang.base.common.log.Logger;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: silei
 * @Date: 2021/7/16
 * @desc:
 */


@RestController
@Api(value = "支付网关配置接口", tags = {"支付网关配置接口"})
@RequestMapping("/api/account/proxy/gateway")
public class PayTransferGatewayController {

    @Autowired
    private TransferGatewayService transferGatewayService;

    @Autowired
    private TransferChannelGatewayService transferChannelGatewayService;

    @PostMapping(value = "/handleTransferGateway")
    Result<Void> handleTransferGateway(@RequestBody TransferGatewayReq transferGatewayReq) {
        Logger.info("handleTransferGateway req:", transferGatewayReq);
        transferGatewayService.saveUpdate(transferGatewayReq);
        return Result.success();
    }

    @PostMapping(value = "/handleTransferChannelGateway")
    Result<Void> handleTransferChannelGateway(@RequestBody TransferChannelGatewayReq req) {
        Logger.info("handleTransferChannelGateway req:", req);
        transferChannelGatewayService.saveUpdate(req);
        return Result.success();
    }

}
