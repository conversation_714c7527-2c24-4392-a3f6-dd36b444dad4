package com.akucun.account.proxy.facade.controller.account;

import com.aikucun.common2.utils.CollectionUtils;
import com.aikucun.common2.utils.datamasking.DataMask;
import com.akucun.account.center.client.model.*;
import com.akucun.account.center.client.model.account.AccountBookDO;
import com.akucun.account.center.client.model.account.AccountBookDetailDO;
import com.akucun.account.center.client.model.account.AccountDetailTotalInfoDO;
import com.akucun.account.center.client.model.query.AccountDetailQuery;
import com.akucun.account.center.client.model.query.AccountQuery;
import com.akucun.account.center.client.model.rsp.QueryTradeSumRsp;
import com.akucun.account.center.common.entity.Query;
import com.akucun.account.center.common.entity.ResultList;
import com.akucun.account.center.feign.AccountCenterService;
import com.akucun.account.proxy.client.bonus.AccountMemberClient;
import com.akucun.account.proxy.client.center.AccountCenterClient;
import com.akucun.account.proxy.common.config.ApolloConfigCenter;
import com.akucun.account.proxy.common.constant.CustomerType;
import com.akucun.account.proxy.common.enums.AccountTypeEnum;
import com.akucun.account.proxy.common.enums.BusiTypeEnum;
import com.akucun.account.proxy.common.enums.ErrorCodeConstants;
import com.akucun.account.proxy.facade.stub.account.AccountCenterFacade;
import com.akucun.account.proxy.facade.stub.enums.AccountKeyConstants;
import com.akucun.account.proxy.facade.stub.others.account.req.*;
import com.akucun.account.proxy.service.acct.AccountService;
import com.akucun.account.proxy.service.help.FinClearingCoreFacadeHelp;
import com.akucun.common.Result;
import com.akucun.fps.pingan.client.model.query.PinganCardQueryDO;
import com.akucun.fps.pingan.client.vo.PinganCardVO;
import com.akucun.fps.pingan.feign.api.settlement.SettlementServiceApi;
import com.akucun.member.api.vo.AccountRecordVO;
import com.alibaba.fastjson.JSON;
import com.mengxiang.base.common.log.Logger;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * @Author: silei
 * @Date: 2020/12/6
 * @desc: 账户中心接口
 */
@RestController
@Api(value = "账户中心相关", tags = {"账户中心相关"})
@RequestMapping("/api/account/proxy/center")
public class AccountCenterController implements AccountCenterFacade {

    @Autowired
    private AccountCenterService accountCenterService;
    @Autowired
    private AccountMemberClient accountMemberClient;
    @Autowired
    private AccountCenterClient accountCenterClient;

    @Autowired
    private AccountService accountService;

    @Value("${account.bonus.switch:true}")
    private boolean accountBonusSwitch;

    @Autowired
    private ApolloConfigCenter apolloConfigCenter;

    @Override
    @ApiOperation(value = "账户操作（创建、注销、锁定、解锁等）")
    @PostMapping(value = "/dealAccount", produces = "application/json;charset=utf-8")
    public Result<Void> dealAccount(@RequestBody AccountOperateInfoReq req) {
        Logger.info("AccountCenterController dealAccount req:{}", DataMask.toJSONString(req));
        return accountCenterClient.accountOperate(req);
    }

    @Override
    @ApiOperation(value = "账户交易处理")
    @PostMapping(value = "/dealTrade", produces = "application/json;charset=utf-8")
    public Result<Void> dealTrade(@RequestBody TradeInfo tradeInfo) {
        Logger.info("AccountCenterController dealTrade tradeInfo:{}", DataMask.toJSONString(tradeInfo));
        return accountService.dealTrade(tradeInfo);
    }

    @Override
    @ApiOperation(value = "处理转账信息")
    @PostMapping(value = "/transfer", produces = "application/json;charset=utf-8")
    public Result<Void> transfer(@RequestBody TransferInfo transferInfo) {
        return accountCenterService.transfer(transferInfo);
    }

    @Override
    @ApiOperation(value = "查询账本信息")
    @PostMapping(value = "/queryAccount", produces = "application/json;charset=utf-8")
    public Result<AccountBookDO> queryAccount(@RequestBody AccountQuery accountQuery) {
        if (accountBonusSwitch && AccountKeyConstants.AWARD.getName().equals(accountQuery.getAccountTypeKey())) {
            return accountMemberClient.queryAccount(accountQuery);
        } else {
            return accountCenterService.queryAccount(accountQuery);
        }
    }

    @Override
    @ApiOperation(value = "查询账本明细信息，可分页查询")
    @PostMapping(value = "/queryAccountDetail", produces = "application/json;charset=utf-8")
    public ResultList<AccountBookDetailDO> queryAccountDetail(@RequestBody Query<AccountDetailQuery> query) {
        if (accountBonusSwitch && query.getData().getAccountTypeKey().equals(AccountKeyConstants.AWARD.getName())) {
            return accountMemberClient.queryAccountDetail(query);
        }
        return accountCenterService.queryAccountDetail(query);
    }

    @Override
    @ApiOperation(value = "查询账本月度汇总信息")
    @PostMapping(value = "/queryAccountDetailTotalInfo", produces = "application/json;charset=utf-8")
    public Result<AccountDetailTotalInfoDO> queryAccountDetailTotalInfo(@RequestBody Query<AccountDetailQuery> query) {
        try {
            Result<AccountDetailTotalInfoDO> result = accountCenterService.queryAccountDetailTotalInfo(query);
            return result;
        } catch (Exception e) {
            Logger.error("AccountCenterController queryAccountDetailTotalInfo error:", e);
            return null;
        }
    }

    @Override
    @ApiOperation(value = "查询账本明细信息金额")
    @PostMapping(value = "/queryAccountDetailAmount", produces = "application/json;charset=utf-8")
    public Result<AmountInfo> queryAccountDetailAmount(@RequestBody Query<AccountDetailQuery> query) {
        return accountCenterService.queryAccountDetailAmount(query);
    }

    @Override
    @ApiOperation(value = "查询主要账户")
    @RequestMapping(value = "/getAdminSystem", produces = "application/json;charset=utf-8")
    public Result<String> getAdminSystem(@RequestParam("customerCode") String customerCode) {
        return accountCenterService.getAdminSystem(customerCode);
    }

    @Override
    @ApiOperation(value = "H5积分转入爱库存奖励金账户")
    @PostMapping(value = "/h5point2Award", produces = "application/json;charset=utf-8")
    public Result<Void> h5point2Award(@RequestBody H5PointTransferInfo h5PointTransferInfo) {
        return accountCenterService.h5point2Award(h5PointTransferInfo);
    }

    @Override
    @ApiOperation(value = "保存提现标识")
    @RequestMapping(value = "/buildWithdrawFlag", produces = "application/json;charset=utf-8")
    public Result<Boolean> buildWithdrawFlag(@RequestParam("customerCode") String customerCode, @RequestParam("customerType") String customerType) {
        return accountCenterService.buildWithdrawFlag(customerCode, customerType);
    }

    @Override
    @ApiOperation(value = "查询提现标识")
    @RequestMapping(value = "/queryWithdrawFlag", produces = "application/json;charset=utf-8")
    public Result<Boolean> queryWithdrawFlag(@RequestParam("customerCode") String customerCode, @RequestParam("customerType") String customerType) {
        return accountCenterService.buildWithdrawFlag(customerCode, customerType);
    }

    @Override
    @ApiOperation(value = "商户账户金额解冻")
    @RequestMapping(value = "/unfreezeMerchantAccountAmount", produces = "application/json;charset=utf-8")
    public Result<Void> unfreezeMerchantAccountAmount(@RequestBody AccountTradeInfo accountTradeInfo) {
        return accountCenterService.unfreezeMerchantAccountAmount(accountTradeInfo);
    }

    @Override
    @ApiOperation(value = "查询奖励金总额")
    @RequestMapping(value = "/getAllBonus", produces = "application/json;charset=utf-8")
    public Result<Long> getAllBonus(@RequestParam("customerCode") String customerCode) {
        return accountMemberClient.getAllBonus(customerCode);
    }

    @Override
    @ApiOperation(value = "啊呀团团长奖励发放")
    @RequestMapping(value = "/aytAwardTransfer", produces = "application/json;charset=utf-8")
    public Result<Void> aytAwardTransfer(@RequestBody AYTTransferReq aytTransferReq) {
        return accountCenterClient.aytAwardTransfer(aytTransferReq);
    }

    @Override
    @ApiOperation(value = "批量交易处理")
    @PostMapping(value = "/batchDealTrade", produces = "application/json;charset=utf-8")
    public Result<Void> batchDealTrade(@RequestBody List<TradeInfo> tradeInfoList) {
        Result<Void> result = accountCenterService.batchDealTrade(tradeInfoList);

        for (TradeInfo tradeInfo : tradeInfoList) {
            if (null != apolloConfigCenter.getAccountMonitorMap()) {
                Boolean tmp = apolloConfigCenter.getAccountMonitorMap().get(tradeInfo.getAccountTypeKey() + "_" + tradeInfo.getTradeType());
                if(!ObjectUtils.isEmpty(tmp) && tmp) {
                    accountService.monitor(tradeInfo, result);
                    break; // 只触发一次监控；
                }
            }
        }

        return result;
    }

    @Override
    @ApiOperation(value = "查询啊呀团余额汇总信息")
    @PostMapping(value = "/queryTradeSumForAyaTuan", produces = "application/json;charset=utf-8")
    public Result<List<QueryTradeSumRsp>> queryTradeSumForAyaTuan(@RequestBody List<QueryTradeSumRequest> tradeSumRequests) {
        List<QueryTradeSumRsp> tradeSumRsps = accountCenterClient.queryTradeSumForAyaTuan(tradeSumRequests);
        return Result.success(tradeSumRsps);
    }


    @ApiOperation(value = "查询余额汇总信息")
    @PostMapping(value = "/queryTradeSum", produces = "application/json;charset=utf-8")
    @Override
    public Result<List<QueryTradeSumRsp>> queryTradeSum(@RequestBody List<TradeQueryRequest> tradeQueryRequestList) {
        Logger.info("账户中心交易记录汇总查询req:{}",JSON.toJSONString(tradeQueryRequestList));
        Result<List<QueryTradeSumRsp>> result = accountCenterClient.queryTradeSum(tradeQueryRequestList);
        Logger.info("账户中心交易记录汇总查询req:{},resp:{}",JSON.toJSONString(tradeQueryRequestList),JSON.toJSONString(result));

        return result;
    }

    @Override
    @ApiOperation(value = "查询会员奖励金账本信息（刷数用）")
    @GetMapping(value = "/queryMemberBonusAccount")
    public Result<AccountBookDO> queryMemberBonusAccount(@RequestParam("userId") String userId) {
        return accountMemberClient.queryMemberBonusAccount(userId);
    }

    @Override
    @ApiOperation(value = "会员奖励金账户交易处理")
    @PostMapping(value = "/memberBonusAccountDealTrade", produces = "application/json;charset=utf-8")
    public Result<Void> memberBonusAccountDealTrade(@RequestBody MemberBonusAccountTradeInfo tradeInfo) {
        Logger.info("AccountCenterController memberBonusAccountDealTrade tradeInfo:{}", DataMask.toJSONString(tradeInfo));
        accountMemberClient.bonusAccountTradeByUserId(tradeInfo);

        Result<Void> result = Result.success();
        //同步交易到账户中心
        if (BooleanUtils.isTrue(tradeInfo.getIsSyncAccount())) {
            result = accountCenterService.dealTrade(tradeInfo);
        }
        return result;
    }

    @ApiOperation(value = "检查奖励金账户记录是否存在")
    @GetMapping(value = "/checkAwardRecordByTradeNo")
    public Result<List<AccountRecordVO>> checkAwardRecordByTradeNo(@RequestParam(value = "customerCode") String customerCode,@RequestParam(value = "tradeNo") String tradeNo){
        Result<List<AccountRecordVO>> result =  accountMemberClient.queryOldBonusTrade(customerCode,tradeNo);
        return result;
    }

    @ApiOperation(value = "老奖励金交易查询")
    @PostMapping(value = "/queryOldBonusTrade", produces = "application/json;charset=utf-8")
    public Result<AccountRecordVO> queryOldBonusTrade(@RequestBody TradeInfo tradeInfo) {
        AccountRecordVO accountRecordVO = accountMemberClient.queryOldBonusTradeByTradeInfo(tradeInfo);
        return Result.success(accountRecordVO);
    }

}
