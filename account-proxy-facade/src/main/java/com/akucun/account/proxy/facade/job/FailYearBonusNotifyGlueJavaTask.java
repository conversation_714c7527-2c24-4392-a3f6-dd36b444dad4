package com.akucun.account.proxy.facade.job;

import com.aikucun.common2.base.Result;
import com.aikucun.common2.log.Logger;
import com.akucun.account.proxy.common.enums.ErrorCodeConstants;
import com.akucun.account.proxy.dao.model.RewardApply;
import com.akucun.account.proxy.facade.stub.enums.RewardTypeEnum;
import com.akucun.account.proxy.service.acct.PromoTradeFactory;
import com.akucun.account.proxy.service.acct.PromoTradeService;
import com.akucun.account.proxy.service.acct.RewardApplyService;
import com.akucun.account.proxy.service.enums.PromoTradeStatusEnum;
import com.alibaba.fastjson.JSON;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Create on : 2025/5/8 20:07
 **/
public class FailYearBonusNotifyGlueJavaTask extends IJobHandler {
    String LOG_PREFIX = "月勤奖/赋能营失败后重新下发结果的异步通知-";
    @Autowired
    private RewardApplyService rewardApplyService;
    @Autowired
    private PromoTradeService promoTradeService;
    @Autowired
    private PromoTradeFactory factory;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        if(StringUtils.isEmpty(param)){
            Logger.info(LOG_PREFIX+"参数为空");
            return ReturnT.FAIL;
        }
        //01-参数解析：tradeNo,payRslt(Boolean),errorMsg
        String[] params = param.split(",");
        if(params.length!=3){
            Logger.info(LOG_PREFIX+"参数异常：必须为3个参数切用,分割");
            return ReturnT.FAIL;
        }
        String tradeNo = params[0];
        Boolean payRslt = Boolean.valueOf(params[1]);
        String errorMsg = params[2];

        Logger.info(LOG_PREFIX+"PromoPayTask支付结果异步回调:{}-{}-失败原因:{}", tradeNo,payRslt?"成功":"失败",errorMsg);
        if(!StringUtils.isEmpty(tradeNo) && tradeNo.contains("#")){
            tradeNo = tradeNo.substring(0,tradeNo.indexOf("#"));
            Logger.info(LOG_PREFIX+"PromoPayTask支付结果异步回调2:{}-{}-失败原因:{}", tradeNo,payRslt?"成功":"失败",errorMsg);
        }

        //01-获取奖励发放记录
        List<RewardApply> existRecords = rewardApplyService.queryRewardApplyList(tradeNo, null, null, null);
        if(CollectionUtils.isEmpty(existRecords)){
            Logger.warn(LOG_PREFIX+"PromoPayTask支付结果回调-数据异常:记录不存在：{}",tradeNo);
            return new ReturnT(ErrorCodeConstants.ACCORE_101900.getErrorCode(),String.format("PromoPayTask支付结果回调-数据异常:记录不存在：%s",tradeNo));
        }
        RewardApply existRecord = existRecords.get(0);

        //02-状态变更判断
        String oldStatus = existRecord.getStatus();
        String newStatus = payRslt ? PromoTradeStatusEnum.SUSS.getCode() : PromoTradeStatusEnum.FAIL.getCode();
        if(oldStatus.equalsIgnoreCase(newStatus)){
            Logger.warn(LOG_PREFIX+"PromoPayTask支付结果回调[{}-{}-{}]-跳过更新-当前任务状态一致:{}", tradeNo,payRslt?"成功":"失败",errorMsg,oldStatus);
            return ReturnT.SUCCESS;
        }

        //03-更新状态并通知上游
        RewardTypeEnum busiTypeEnum = RewardTypeEnum.getByCode(existRecord.getBusiType());
        com.akucun.common.Result<Void> notifyRslt = factory.getHandler(busiTypeEnum).syncNotify(existRecord.getRequestNo(),existRecord.getBusiType(),existRecord.getTransBillDate(),newStatus,errorMsg);
        if(!ObjectUtils.isEmpty(notifyRslt) && !notifyRslt.isSuccess()){
            Logger.warn(LOG_PREFIX+"PromoPayTask支付结果-回调上游失败:{}",JSON.toJSONString(notifyRslt));
            return new ReturnT(ErrorCodeConstants.ACCORE_101900.getErrorCode(),String.format("PromoPayTask支付结果-回调上游失败：{}", JSON.toJSONString(existRecord)));
        }

        return ReturnT.SUCCESS;
    }
}
