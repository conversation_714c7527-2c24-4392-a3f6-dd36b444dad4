package com.akucun.account.proxy.facade.controller.account;

import com.aikucun.common2.utils.datamasking.DataMask;
import com.akucun.account.proxy.client.account.AccountTransferClient;
import com.akucun.account.proxy.facade.stub.account.AccountTransferFacade;
import com.akucun.fps.account.client.model.TransferAccountDO;
import com.akucun.fps.account.client.model.query.TransferAccountQueryDO;
import com.akucun.fps.common.entity.Query;
import com.akucun.fps.common.entity.Result;
import com.akucun.fps.common.entity.ResultList;
import com.alibaba.fastjson.JSON;
import com.mengxiang.base.common.log.Logger;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * @Author: silei
 * @Date: 2021/2/3
 * @desc:
 */
@RestController
@Api(value = "店主打赏", tags = {"店主打赏"})
@RequestMapping("/api/account/proxy/transfer")
public class AccountTransferController implements AccountTransferFacade {

    @Autowired
    private AccountTransferClient accountTransferClient;

    @Override
    @PostMapping(value = "/transferApply")
    @ApiOperation(value = "转账申请 当前仅适用H5", notes = "转账申请 当前仅适用H5", httpMethod = "POST")
    public Result<String> transferApply(@RequestBody TransferAccountDO transferAccountDO) {
        Logger.info("AccountTransferController transferApply req:{}", DataMask.toJSONString(transferAccountDO));
        return accountTransferClient.transferApply(transferAccountDO);
    }

    @Override
    @PostMapping(value = "/selectPage")
    @ApiOperation(value = "转账申请分页查询", notes = "转账申请分页查询", httpMethod = "POST")
    public ResultList<TransferAccountDO> selectPage(@RequestBody Query<TransferAccountQueryDO> query) {
        Logger.info("AccountTransferController selectPage query:{}", DataMask.toJSONString(query));
        return accountTransferClient.selectPage(query);
    }
}
