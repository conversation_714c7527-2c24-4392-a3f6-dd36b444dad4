package com.akucun.account.proxy.facade.controller.merchant.market;

import com.akucun.account.proxy.common.enums.ResponseEnum;
import com.akucun.account.proxy.common.exception.AccountProxyException;
import com.akucun.account.proxy.common.log.CallLogUtils;
import com.akucun.account.proxy.facade.stub.merchant.market.MerchantFullReturnMarketFundFacade;
import com.akucun.account.proxy.facade.stub.others.merchant.market.MerchantFullReturnMarketAddFreezeRequest;
import com.akucun.account.proxy.facade.stub.others.merchant.market.MerchantFullReturnMarketFreezeRequest;
import com.akucun.account.proxy.facade.stub.others.merchant.market.MerchantFullReturnMarketSettleRequest;
import com.akucun.account.proxy.facade.stub.others.merchant.market.MerchantFullReturnMarketUnFreezeRequest;
import com.akucun.account.proxy.service.merchant.market.MerchantFullReturnMarketFundService;
import com.akucun.common.Result;
import com.alibaba.fastjson.JSONObject;
import com.mengxiang.base.common.log.Logger;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Create on : 2024/5/6 11:08
 **/
@Api(tags = {"商家营销满返资金服务接口"}, value = "商家营销满返资金服务接口")
@RestController
public class MerchantFullReturnMarketFundController implements MerchantFullReturnMarketFundFacade {

    @Resource
    private MerchantFullReturnMarketFundService merchantFullReturnMarketFundService;

    @Override
    @ApiOperation("活动报名（冻结金额）")
    public Result<Void> freeze(@RequestBody @Validated MerchantFullReturnMarketFreezeRequest request) {
        Logger.info("商家营销满返活动报名冻结请求：{}", JSONObject.toJSONString(request));
        Result result = null;
        try {
            merchantFullReturnMarketFundService.freeze(request);
            result = Result.success();
        } catch (AccountProxyException e) {
            Logger.warn("商家营销满返活动报名冻结请求发生业务异常", e);
            result = Result.error(e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            Logger.error("商家营销满返活动报名冻结请求发生系统异常", e);
            result = Result.error(ResponseEnum.SYSTEM_EXCEPTION.getCode(), e.getMessage());
        } finally {
            CallLogUtils.saveLog(request.getBizNo(), CallLogUtils.CallLogType.FULL_RETURN_FREEZE.name(), JSONObject.toJSONString(request), JSONObject.toJSONString(result));
            Logger.info("商家营销满返活动报名冻结result：{}，request：{}", JSONObject.toJSONString(result), JSONObject.toJSONString(request));
        }
        return result;
    }

    @Override
    @ApiOperation("活动未开始取消（解冻金额）")
    public Result<Void> unFreeze(@RequestBody @Validated MerchantFullReturnMarketUnFreezeRequest request) {
        Logger.info("商家营销满返活动取消解冻请求：{}", JSONObject.toJSONString(request));
        Result result = null;
        try {
            merchantFullReturnMarketFundService.unFreeze(request);
            result = Result.success();
        } catch (AccountProxyException e) {
            Logger.warn("商家营销满返活动取消解冻请求发生业务异常", e);
            result = Result.error(e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            Logger.error("商家营销满返活动取消解冻请求发生系统异常", e);
            result = Result.error(ResponseEnum.SYSTEM_EXCEPTION.getCode(), e.getMessage());
        } finally {
            CallLogUtils.saveLog(request.getBizNo(), CallLogUtils.CallLogType.FULL_RETURN_UNFREEZE.name(), JSONObject.toJSONString(request), JSONObject.toJSONString(result));
            Logger.info("商家营销满返活动取消解冻result：{}，request：{}", JSONObject.toJSONString(result), JSONObject.toJSONString(request));
        }
        return result;
    }

    @Override
    @ApiOperation("活动结束 或 定期加冻（加冻）")
    public Result<Void> addFreeze(@RequestBody @Validated MerchantFullReturnMarketAddFreezeRequest request) {
        Logger.info("商家营销满返活动加冻请求：{}", JSONObject.toJSONString(request));
        Result result = null;
        try {
            merchantFullReturnMarketFundService.addFreeze(request);
            result = Result.success();
        } catch (AccountProxyException e) {
            Logger.warn("商家营销满返活动加冻请求发生业务异常", e);
            result = Result.error(e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            Logger.error("商家营销满返活动加冻请求发生系统异常", e);
            result = Result.error(ResponseEnum.SYSTEM_EXCEPTION.getCode(), e.getMessage());
        } finally {
            CallLogUtils.saveLog(request.getBizNo(), CallLogUtils.CallLogType.FULL_RETURN_ADD_FREEZE.name(), JSONObject.toJSONString(request), JSONObject.toJSONString(result));
            Logger.info("商家营销满返活动加冻result：{}，request：{}", JSONObject.toJSONString(result), JSONObject.toJSONString(request));
        }
        return result;
    }

    @Override
    @ApiOperation("活动售后期结束结算")
    public Result<Void> settle(@RequestBody @Validated MerchantFullReturnMarketSettleRequest request) {
        Logger.info("商家营销满返活动售后期结束结算请求：{}", JSONObject.toJSONString(request));
        Result result = null;
        try {
            merchantFullReturnMarketFundService.settle(request);
            result = Result.success();
        } catch (AccountProxyException e) {
            Logger.warn("商家营销满返活动售后期结束结算请求发生业务异常", e);
            result = Result.error(e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            Logger.error("商家营销满返活动售后期结束结算请求发生系统异常", e);
            result = Result.error(ResponseEnum.SYSTEM_EXCEPTION.getCode(), e.getMessage());
        } finally {
            CallLogUtils.saveLog(request.getBizNo(), CallLogUtils.CallLogType.FULL_RETURN_SETTLE.name(), JSONObject.toJSONString(request), JSONObject.toJSONString(result));
            Logger.info("商家营销满返活动售后期结束结算result：{}，request：{}", JSONObject.toJSONString(result), JSONObject.toJSONString(request));
        }
        return result;
    }

}
