//package com.akucun.account.proxy.facade.config;
//
//import com.mengxiang.base.common.sequence.spring.SequenceGenerator;
//import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
///**
// * @Author: silei
// * @Date: 2021/3/17
// * @desc:
// */
//@Configuration
//public class SequenceConfig {
//
//
//    @Bean
//    @ConditionalOnMissingBean(SequenceGenerator.class)
//    public SequenceGenerator sequenceGenerator() {
//        return new SequenceGenerator();
//    }
//}
