package com.akucun.account.proxy.facade.controller.trade;

import com.akucun.account.proxy.facade.stub.others.trade.req.TradeReq;
import com.akucun.account.proxy.facade.stub.others.trade.res.TradeRes;
import com.akucun.account.proxy.facade.stub.trade.TradeFacade;
import com.akucun.account.proxy.service.tradeflow.TradeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Api(value = "交易流程", tags = {"交易流程"})
@RequestMapping("/api/trade")
public class TradeController implements TradeFacade {

    @Autowired
    private TradeService tradeService;

    @Override
    @ApiOperation(value = "交易")
    @PostMapping(value = "", produces = "application/json;charset=utf-8")
    public TradeRes trade(@RequestBody TradeReq tradeReq) {
        return tradeService.trade(tradeReq);
    }

}
