package com.akucun.account.proxy.facade.job;

import com.aikucun.common2.base.Result;
import com.aikucun.common2.log.Logger;
import com.aikucun.common2.utils.DateUtils;
import com.akucun.account.proxy.client.risk.RiskService;
import com.akucun.account.proxy.common.enums.ErrorCodeConstants;
import com.akucun.account.proxy.common.enums.MemberGrade;
import com.akucun.account.proxy.common.enums.ResponseEnum;
import com.akucun.account.proxy.dao.model.RewardApply;
import com.akucun.account.proxy.facade.stub.enums.RewardTypeEnum;
import com.akucun.account.proxy.service.acct.PromoTradeFactory;
import com.akucun.account.proxy.service.acct.PromoTradeService;
import com.akucun.account.proxy.service.acct.RewardApplyService;
import com.akucun.account.proxy.service.enums.PromoTradeStatusEnum;
import com.akucun.fps.common.util.MD5Util;
import com.alibaba.fastjson.JSON;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Create on : 2025/5/8 17:05
 **/
public class FailRetryYearBonusGlueJavaTask extends IJobHandler {
    String LOG_PREFIX = "月勤奖/赋能营失败后重新下发-";
    @Autowired
    private RewardApplyService rewardApplyService;
    @Autowired
    private PromoTradeService promoTradeService;
    @Autowired
    private PromoTradeFactory factory;
    @Autowired
    private RiskService riskService;
    @Override
    public ReturnT<String> execute(String param) throws Exception {
        if(StringUtils.isEmpty(param)){
            Logger.info(LOG_PREFIX+"参数为空");
            return ReturnT.FAIL;
        }
        ReturnT<String> result = new ReturnT();

        try {
            //01-参数解析：RequestNo,BusiType,TransBillDate,UserCode,UserType eg:11319462_MONTHLY_INCENTIVE_2025-02,EMPOWERMENT_CAMP,202502,11319462,NM
            String[] params = param.split(",");
            if(params.length!=5){
                Logger.info(LOG_PREFIX+"参数异常：必须为5个参数切用,分割");
                return ReturnT.FAIL;
            }

            //02-状态的double check
            List<RewardApply> existRecords = rewardApplyService.queryRewardApplyRecords(params[0], params[1], params[2], params[3],params[4]);
            if(CollectionUtils.isEmpty(existRecords)){
                Logger.info(LOG_PREFIX+"参数异常：记录不存在：{}",param);
                return new ReturnT(ErrorCodeConstants.ACCORE_101900.getErrorCode(),String.format("PromoOAStartTask奖励下发-启动出款任务时，数据异常:记录不存在：{}",param));
            }
            RewardApply existRecord = existRecords.get(0);
            RewardTypeEnum busiTypeEnum = RewardTypeEnum.getByCode(existRecord.getBusiType());
            PromoTradeStatusEnum statusEnum = PromoTradeStatusEnum.getByCode(existRecord.getStatus());
            //--明确是失败状态的重试
            if(statusEnum != PromoTradeStatusEnum.FAIL){
                Logger.info(LOG_PREFIX+"参数异常：非失败状态无法重试-status={}",existRecord.getStatus());
                return new ReturnT(ErrorCodeConstants.SYSTEMERROR_999999.getErrorCode(),String.format("PromoOAStartTask奖励下发-只支持失败记录的重试：{}",param));
            }

            //03-风控:支付必须先走风控-风控被拦截则异常中断
            /*Pair<Boolean, String> riskResp = riskService.checkName(existRecord.getUserCode(),existRecord.getTransBillDate());
            Logger.info(LOG_PREFIX+"PromoOAStartTask奖励下发-启动出款任务时-风控处理结果:{}-{}-{}:{}",existRecord.getRequestNo(),existRecord.getUserCode(),existRecord.getTransBillDate(), ObjectUtils.isEmpty(riskResp)?"拦截":(riskResp.getLeft()+riskResp.getRight()));
            if(ObjectUtils.isEmpty(riskResp) || !riskResp.getLeft()){
                Logger.warn(LOG_PREFIX+"PromoOAStartTask奖励下发-启动出款任务时-被风控拦截:{}",JSON.toJSONString(existRecord));
                com.akucun.common.Result<Void> notifyRslt = factory.getHandler(busiTypeEnum).syncNotify(existRecord.getRequestNo(),existRecord.getBusiType(),existRecord.getTransBillDate()
                        ,PromoTradeStatusEnum.RISK_REJECT.getCode(), ObjectUtils.isEmpty(riskResp)?"风控异常":riskResp.getRight());
                if(!ObjectUtils.isEmpty(notifyRslt) && !notifyRslt.isSuccess()){
                    String notifyErrorMsg = String.format("PromoOAStartTask奖励下发-启动出款任务时-被风控拦截-异步通知失败:bizNo=%s,notifyRslt=%s",existRecord.getRequestNo(),JSON.toJSONString(notifyRslt));
                    Logger.error(notifyErrorMsg);
                    return new ReturnT(ErrorCodeConstants.ACCORE_101900.getErrorCode(),notifyErrorMsg);
                }
                return new ReturnT(ErrorCodeConstants.ACCORE_101900.getErrorCode(),"被风控拦截");
            }*/

            //04-付款申请
            String userAuthType = MemberGrade.getAuthName(existRecord.getUserGrade());
            String customerCode = existRecord.getUserCode();
            String customerType = StringUtils.isEmpty(existRecord.getUserType())?"NM":existRecord.getUserType();
            //String payBillDate = existRecord.getTransBillDate();
            RewardTypeEnum rewardTypeEnum = RewardTypeEnum.getByCode(existRecord.getBusiType());
            String remark = existRecord.getRemark();
            String accountType = "XD_BALANCE";
            //初始化待更新的状态
            String newStatus = PromoTradeStatusEnum.SUSS.getCode();
            String errorMsg = "suss";
            String payBizNo = existRecord.getRequestNo();
            if("person".equalsIgnoreCase(userAuthType)){
                //这里暂不支持重试-------因为payBizNo不修改-------
                //发放奖励金账户
                customerCode = customerCode.startsWith("NM") ? customerCode.substring(2) : customerCode;
                accountType = "BONUS";
                //上游上送的业务流水号过长，而老奖励金字段长度32，因此这里强制md5，弊端就是关联查询数据库很麻烦
                payBizNo = MD5Util.md5(existRecord.getRequestNo());
            }else{
                //余额提现账户
                customerCode = customerCode.startsWith("NM") ? customerCode : String.format("%s%s","NM",customerCode);
                accountType = "XD_BALANCE";
                //乐税级别，走发放饷店并提现，其他（这里有企业认证）只发放饷店余额同步就返回结果了
                if("leshui".equalsIgnoreCase(userAuthType)){
                    newStatus = PromoTradeStatusEnum.PAYING.getCode();
                }
                //这里强制修改了payBizNo ---------之所以能够重试，就是因为修改了这个payBizNo---------------
                payBizNo = String.format("%s#%s",existRecord.getRequestNo(), DateUtils.format(new Date(),"MMddHHmm"));
            }
            Logger.info("PromoPayTask{}req:customerCode={},customerType={},bizNo={},amount={},businessType={},accountType={}",remark,customerCode,customerType,payBizNo,existRecord.getTradeAmt(),rewardTypeEnum.getCode(),accountType);
            Result<Void> accountPayRslt = promoTradeService.monthlyDiligenceEmpowerAwardSettle(customerCode,customerType,payBizNo,existRecord.getTradeAmt(),remark,rewardTypeEnum.getCode(),accountType,existRecord.getUserGrade());
            Logger.info("PromoPayTask{}resp:{}", remark,JSON.toJSONString(accountPayRslt));

            //05-状态更新和异步通知
            if(ObjectUtils.isEmpty(accountPayRslt) || !accountPayRslt.getSuccess()){
                newStatus = PromoTradeStatusEnum.FAIL.getCode();
                errorMsg = ObjectUtils.isEmpty(accountPayRslt)?"出款异常，请联系管理员处理":accountPayRslt.getMessage();
                //未绑卡的特殊处理：已经预定义了该状态
                if(!ObjectUtils.isEmpty(accountPayRslt) && ResponseEnum.BIND_CARD_ERROR.getCode().intValue() == accountPayRslt.getCode().intValue()){
                    newStatus = PromoTradeStatusEnum.NO_CARD_BIND.getCode();
                }
                Logger.info(LOG_PREFIX+"出款异常(非成功)直接跳出-status={}",newStatus);
                return new ReturnT(ErrorCodeConstants.ACCORE_101900.getErrorCode(),"二次失败则直接跳出，不做二次失败通知");
            }
            //必须使用RequestNo异步通知上游-营销
            com.akucun.common.Result<Void> notifyRslt = factory.getHandler(busiTypeEnum).syncNotify(existRecord.getRequestNo(),existRecord.getBusiType(),existRecord.getTransBillDate(),newStatus,errorMsg);
            if(!ObjectUtils.isEmpty(notifyRslt) && !notifyRslt.isSuccess()){
                String notifyErrorMsg = String.format("%s异步通知失败:bizNo=%s,notifyRslt=%s",remark,existRecord.getRequestNo(),JSON.toJSONString(notifyRslt));
                Logger.error(notifyErrorMsg);
                return new ReturnT(ErrorCodeConstants.ACCORE_101900.getErrorCode(),notifyErrorMsg);
            }

            return ReturnT.SUCCESS;
        }catch (Exception e){
            Logger.error("营销月勤奖下发异常", e);
            return new ReturnT(ErrorCodeConstants.ACCORE_101900.getErrorCode(),e.getMessage());
        }
    }
}
