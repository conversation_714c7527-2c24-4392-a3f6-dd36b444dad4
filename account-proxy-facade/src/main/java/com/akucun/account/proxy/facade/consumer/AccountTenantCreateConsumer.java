package com.akucun.account.proxy.facade.consumer;

import com.aikucun.common2.base.exception.BusinessException;
import com.aikucun.common2.utils.datamasking.DataMask;
import com.akucun.account.proxy.client.center.AccountCenterClient;
import com.akucun.account.proxy.common.enums.TenantTypeEnum;
import com.akucun.account.proxy.dao.mapper.AccountTenantMerchantMapper;
import com.akucun.account.proxy.dao.model.AccountTenantMerchant;
import com.akucun.account.proxy.facade.stub.enums.AccountKeyConstants;
import com.akucun.account.proxy.facade.stub.others.account.req.AccountOperateInfoReq;
import com.akucun.account.proxy.service.help.TenantCoreHelper;
import com.akucun.common.Result;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.maihaoche.starter.mq.annotation.MQConsumer;
import com.maihaoche.starter.mq.base.AbstractMQPushConsumer;
import com.mengxiang.base.common.log.Logger;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: silei
 * @Date: 2021/3/15
 * @desc: 账户创建消息处理
 */
@MQConsumer(instances = {
        @MQConsumer.Instance(
                instanceName = "DEFAULT", // 指定配置中的instance name
                topic = "AKC_MEMBER_TENANT_CHANGE_TOPIC",
                tag = "AKC_MEMBER_TENANT_CHANGE_CREATE_TAG",
                consumerGroup = "AKC_ACCOUNT_CREATE_ACCOUNT_CONSUMER_GROUP")})
public class AccountTenantCreateConsumer extends AbstractMQPushConsumer<String> {

    @Autowired
    private AccountCenterClient accountCenterClient;
    @Resource
    private AccountTenantMerchantMapper accountTenantMerchantMapper;

    @Resource
    private TenantCoreHelper tenantCoreHelper;

    @Override
    public boolean process(String message, Map<String, Object> extMap) {
        Logger.info("消费 AKC_MEMBER_TENANT_CHANGE_CREATE_TAG message :{}", message);
        JSONObject json = JSONObject.parseObject(message);

        String tenantId = json.getString("tenantId");
        String tenantName = json.getString("tenantName");
        String tenantType = json.getString("tenantType");
        //参数校验
        if (StringUtils.isEmpty(tenantId) || StringUtils.isEmpty(tenantName)
                || StringUtils.isEmpty(tenantType)) {
            Logger.error("AccountCreateConsumer message 缺少必要开户参数!");
            return false;
        }

        LambdaQueryWrapper<AccountTenantMerchant> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AccountTenantMerchant::getTenantId, tenantId)
                .eq(AccountTenantMerchant::getTenantType, tenantType)
                .eq(AccountTenantMerchant::getStatus, 0);
        AccountTenantMerchant merchant = accountTenantMerchantMapper.selectOne(wrapper);
        if (merchant != null) {
            Logger.warn("AccountCreateConsumer merchant exist, merchant:{}", DataMask.toJSONString(merchant));
            return true;
        }
        merchant = new AccountTenantMerchant();
        //租户类型  tenant_type  0 爱库存， 1.三方小程序， 2.企业饷店，3.SASS标准类型
        merchant.setTenantId(tenantId);
        merchant.setTenantType(tenantType);
        merchant.setTenantName(tenantName);
        accountTenantMerchantMapper.insert(merchant);

        //如果时经销商入驻，则需要同步创建保证金账户
        if (TenantTypeEnum.OPEN_SUPPLY_DISTRIBUTION.getValue().equals(Integer.valueOf(tenantType))) {
            AccountOperateInfoReq req = new AccountOperateInfoReq();
            req.setCustomerCode(tenantId);
            req.setCustomerName(tenantName);
            req.setAccountTypeKey(AccountKeyConstants.QDS.getName());
            req.setOperationType("CREATE");
            accountCenterClient.accountOperate(req);
        }

        /**
         * tenantType = 6 -> SUPPLY  供应链-经销      -> 需要开保证金帐户
         * tentantType = 7 and enablePayApplets = 1 ->  SUPPLY_CONSIGN 供应链-代销代收  -> 需要开佣金帐户     变更为   tenantType = 8 -> OPEN_SUPPLY_CONSIGNMENT_COLLECTING 供应链-代销代收
         * tentantType = 7 and enablePayApplets = 0 -> SUPPLY_CONSIGN 供应链-代销账扣   -> 需要开保证金帐户    变更为   tenantType = 7 -> OPEN_SUPPLY_CONSIGNMENT_WITHHOLD 供应链-代销账扣
         * tenantType = 9 -> OPEN_SUPPLY_CONSIGNMENT_SHARING 供应链-代销分账 -> 需要开保证金帐户
         */
        if (TenantTypeEnum.OPEN_SUPPLY_CONSIGNMENT_WITHHOLD.getValue().equals(Integer.valueOf(tenantType))
                || TenantTypeEnum.OPEN_SUPPLY_CONSIGNMENT_SHARING.getValue().equals(Integer.valueOf(tenantType))) {
            AccountOperateInfoReq req = new AccountOperateInfoReq();
            req.setCustomerCode(tenantId);
            req.setCustomerName(tenantName);
            req.setAccountTypeKey(AccountKeyConstants.DXQDS.getName());
            req.setOperationType("CREATE");
            accountCenterClient.accountOperate(req);
        }
        return true;
    }

    @Override
    protected String parseMessage(MessageExt message) {
        if (message == null || message.getBody() == null) {
            return null;
        }
        final Type type = this.getMessageType();
        if (type instanceof Class) {
            try {
                return new String(message.getBody(), StandardCharsets.UTF_8);
            } catch (Exception e) {
                Logger.error("parse message json fail : {}", e.getMessage());
            }
        } else {
            Logger.warn("Parse msg error. {}", message);
        }
        return null;
    }
}
