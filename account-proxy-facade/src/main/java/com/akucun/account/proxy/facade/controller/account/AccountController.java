package com.akucun.account.proxy.facade.controller.account;

import com.aikucun.common2.base.Result;
import com.aikucun.common2.utils.datamasking.DataMask;
import com.akucun.account.proxy.common.enums.ResponseEnum;
import com.akucun.account.proxy.facade.stub.account.AccountFacade;
import com.akucun.account.proxy.facade.stub.others.dto.req.AccountInfoReq;
import com.akucun.account.proxy.facade.stub.others.dto.req.AccountUpgradePreCheckRequest;
import com.akucun.account.proxy.facade.stub.others.dto.req.AccountUpgradeRequest;
import com.akucun.account.proxy.facade.stub.others.dto.res.AccountInfoResp;
import com.akucun.account.proxy.facade.stub.others.dto.res.AccountUpgradeResp;
import com.akucun.account.proxy.service.acct.AccountService;
import com.akucun.account.proxy.service.acct.AccountUpgradeService;
import com.akucun.account.proxy.service.acct.bo.AccountUpgradeBO;
import com.alibaba.fastjson.JSON;
import com.mengxiang.base.common.log.Logger;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@Api(value = "账户升级相关", tags = {"账户升级相关"})
@RequestMapping("/api/account/proxy")
public class AccountController implements AccountFacade {

    @Autowired
    private AccountUpgradeService accountUpgradeService;

    @Autowired
    private AccountService accountService;

    @Override
    @PostMapping(value = "/upgradePreCheck")
    @ApiOperation(value = "账户变更升级预校验", notes = "账户变更升级预校验", httpMethod = "POST")
    public Result<Void> upgradePreCheck(@RequestBody AccountUpgradePreCheckRequest request) {
        return accountUpgradeService.upgradePreCheck(request);
    }

    @Override
    @PostMapping(value = "/upgrade")
    @ApiOperation(value = "账户变更升级", notes = "账户变更升级", httpMethod = "POST")
    public Result<Void> accountUpgrade(@ApiParam(name = "传入对象", value = "传入json格式", required = true) @RequestBody @Validated AccountUpgradeRequest accountUpgradeRequest) {
        Logger.info("AccountController accountUpgrade req:{}", DataMask.toJSONString(accountUpgradeRequest));
        AccountUpgradeBO accountTradePO = new AccountUpgradeBO();
        BeanUtils.copyProperties(accountUpgradeRequest, accountTradePO);
        return accountUpgradeService.accountUpgrade(accountTradePO);
    }

    @Override
    @PostMapping(value = "/queryUpgradeStatus")
    @ApiOperation(value = "账户升级状态查询", notes = "账户升级状态查询", httpMethod = "POST")
    public Result<List<AccountUpgradeResp>> queryUpgradeStatus(@RequestBody @Validated List<AccountUpgradeRequest> list) {
        try {
            return accountUpgradeService.queryUpgradeStatus(list);
        } catch (Exception e) {
            Logger.error("AccountController queryUpgradeStatus error:",e);
            return Result.error(ResponseEnum.SYSTEM_EXCEPTION.getCode(),ResponseEnum.SYSTEM_EXCEPTION.getMessage());
        }
    }

    @Override
    @PostMapping(value = "/queryAccountInfo")
    @ApiOperation(value = "账户信息（余额）查询", notes = "账户信息（余额）查询", httpMethod = "POST")
    public Result<List<AccountInfoResp>> queryAccountInfo(@RequestBody @Validated List<AccountInfoReq> list) {
        Logger.info("AccountController queryAccountInfo list:{}", DataMask.toJSONString(list));
        try {
            List<AccountInfoResp> respList = accountService.queryAccountInfo(list);
            return Result.success(respList);
        } catch (Exception e) {
            Logger.error("AccountController queryAccountInfo error:",e);
            return Result.error(ResponseEnum.SYSTEM_EXCEPTION.getCode(),ResponseEnum.SYSTEM_EXCEPTION.getMessage());
        }
    }



    @Override
    @PostMapping(value = "/queryAccountBindStatus")
    @ApiOperation(value = "账户实名变更后绑卡状态查询", notes = "账户实名变更后绑卡状态查询", httpMethod = "POST")
    public Result<Boolean> queryAccountBindStatus(@RequestParam("customerCode")String customerCode, @RequestParam("customerType")String customerType) {
        Logger.info("AccountController queryAccountBindStatus customerCode:{},customerType:{}",customerCode, customerType);
        try {
            Boolean isBind = accountService.queryAccountBindStatus(customerCode, customerType);
            return Result.success(isBind);
        } catch (Exception e) {
            Logger.error("AccountController queryAccountBindStatus error:",e);
            return Result.error(ResponseEnum.SYSTEM_EXCEPTION.getCode(),ResponseEnum.SYSTEM_EXCEPTION.getMessage());
        }
    }


}
