package com.akucun.account.proxy.facade;

import com.aikucun.common2.base.Result;
import com.akucun.account.center.client.model.TradeInfo;
import com.akucun.account.proxy.facade.stub.enums.AccountKeyConstants;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Stopwatch;
import lombok.Data;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.web.client.RestTemplate;

import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Create on : 2024/12/25 20:19
 **/
public class XDBalanceClearUtils {

    private static final Logger log = LoggerFactory.getLogger(XDBalanceClearUtils.class);

    private static final String prodGatewayUrl = "http://zuul.infra.aikucun.com";

    private static RestTemplate restTemplate;

    private static HttpHeaders headers;

    private static Map<String, String> accountTradeTypeMap;

    static {
        headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));

        restTemplate = new RestTemplate();

       accountTradeTypeMap = JSON.parseObject("{\"2721679B9C013EC7FC5B31C673494413\":\"TRADE_TYPE_018\",\"8D256656F0A9E0A959024F16A8C910B3\":\"TRADE_TYPE_450\",\"6A55B6EA4B16E697ED8F30DE17AFBA34\":\"TRADE_TYPE_251\"}", Map.class);
    }

    public static void main(String[] args) {
        List<String> errorList = new ArrayList<>();
        try {
            List<BalanceClearObject> records = new ArrayList<>();

            String testAccountStr = "";
            if (StringUtils.isNotBlank(testAccountStr)) {
                String[] split = testAccountStr.split(",");

                BalanceClearObject req = new BalanceClearObject();
                req.setCustomerCode(split[0].trim());
                req.setAmount(new BigDecimal(split[1].trim()));
                req.setCustomerType(split[2].trim());

                if (StringUtils.isAnyBlank(req.getCustomerCode(), req.getCustomerType())
                        || Objects.isNull(req.getAmount()) || req.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
                    throw new RuntimeException("数据异常");
                }
                records.add(req);
            } else {
                FileReader fileReader = new FileReader("/Users/<USER>/Desktop/饷店全量数据.csv");
                CSVParser csvRecords = new CSVParser(fileReader, CSVFormat.DEFAULT);
                for (CSVRecord csvRecord : csvRecords) {
                    if (csvRecord.getRecordNumber() == 1) {
                        continue;
                    }
                    if (csvRecord.getRecordNumber() < 10000) {
                    }
                    BalanceClearObject req = new BalanceClearObject();
                    req.setCustomerCode(csvRecord.get(0).trim());
                    req.setAmount(new BigDecimal(csvRecord.get(1).trim()));
                    req.setCustomerType(csvRecord.get(2).trim());

                    if (StringUtils.isAnyBlank(req.getCustomerCode(), req.getCustomerType())
                            || Objects.isNull(req.getAmount()) || req.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
                        throw new RuntimeException("数据异常");
                    }

                    records.add(req);
                }
            }


            ExecutorService executorService = Executors.newFixedThreadPool(10);

            List<CompletableFuture<Result<BalanceClearObject>>> completableFutures = records.stream().map((balanceClearObject -> CompletableFuture.supplyAsync(() ->
                    execute(balanceClearObject, restTemplate, headers), executorService))).collect(Collectors.toList());

            /*List<CompletableFuture<Pair<Result<Boolean>, String>>> completableFutures = Lists.partition(records, 200).stream().map(subList ->
                    CompletableFuture.supplyAsync(() -> execute(subList, restTemplate, headers), executorService)).collect(Collectors.toList());*/
            //CompletableFuture<Void> allOf = CompletableFuture.allOf(completableFutures.stream().toArray(CompletableFuture[]::new));

            CompletableFuture<Void> allOf = CompletableFuture.allOf(completableFutures.toArray(new CompletableFuture[completableFutures.size()]));

            allOf.get();

            AtomicInteger successAtomicInteger = new AtomicInteger(0);
            AtomicInteger failAtomicInteger = new AtomicInteger(0);
            completableFutures.stream().forEach(completableFuture -> {
                try {
                    Result<BalanceClearObject> result = completableFuture.join();

                    BalanceClearObject balanceClearObject = result.getData();
                    if (!result.getSuccess()) {
                        balanceClearObject.setErrorMsg(result.getMessage());
                        errorList.add(balanceClearObject.getCustomerCode() + "@" + balanceClearObject.getCustomerType() + "@" + balanceClearObject.getAmount().toString() + "@" + convertNullValue(balanceClearObject.getErrorMsg()));
                        failAtomicInteger.addAndGet(1);
                    } else {
                        successAtomicInteger.addAndGet(1);
                    }
                } catch (Exception e) {
                    failAtomicInteger.addAndGet(1);
                    log.error("获取账户清零执行结果异常", e);
                }
            });
            log.info("执行完成，总笔数:{}, 成功笔数:{}, 失败笔数:{}", records.size(), successAtomicInteger.get(), failAtomicInteger.get());
            exportCSV(new File("/Users/<USER>/Desktop/饷店全量数据-异常.csv"), errorList);
        } catch (Exception e) {
            log.error("获取账户清零全链路异常", e);
        }
    }

    private static String convertNullValue(String value) {
        if (Objects.isNull(value)) {
            return "";
        }
        return value;
    }

    public static Result<BalanceClearObject> execute(BalanceClearObject balanceClearObject, RestTemplate restTemplate, HttpHeaders headers) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        log.info("正在执行.........customerCode:{}, balanceClearObject:{}", balanceClearObject.getCustomerCode(), JSON.toJSONString(balanceClearObject));
        Result result = null;
        try {
            if (StringUtils.equals("NM", balanceClearObject.getCustomerType())) {
                com.akucun.common.Result<Void> deductResult = deductAccountBalance(balanceClearObject, AccountKeyConstants.NM.getName());
                if (deductResult.isSuccess()) {
                    //扣款成功
                    result = Result.success(balanceClearObject);
                } else {
                    result = Result.error(deductResult.getCode(), "扣款失败：" + (Objects.isNull(deductResult.getMessage()) ? "" : deductResult.getMessage()));
                }
            } else if (StringUtils.equals("NMDL", balanceClearObject.getCustomerType())) {
                com.akucun.common.Result<Void> deductResult = deductAccountBalance(balanceClearObject, AccountKeyConstants.NMDL.getName());
                if (deductResult.isSuccess()) {
                    //扣款成功
                    result = Result.success(balanceClearObject);
                } else {
                    result = Result.error(deductResult.getCode(), "扣款失败：" + (Objects.isNull(deductResult.getMessage()) ? "" : deductResult.getMessage()));
                }
            }
        } catch (Exception e) {
            log.error("账户清零异常：{}", JSON.toJSONString(balanceClearObject), e);
            result = Result.error(500, "账户清零执行异常");
        } finally {
            stopwatch.stop();
            //不管成功失败，都把原操作对象返回
            result.setData(balanceClearObject);
            log.info("执行完成.........customerCode:{}, result:{}, 耗时:{}s", balanceClearObject.getCustomerCode(), JSON.toJSONString(result), stopwatch.elapsed().getSeconds());
        }
        return result;
    }

    /**
     * {
     *     "accountTypeKey": "6A55B6EA4B16E697ED8F30DE17AFBA34",
     *     "amount": 5,
     *     "customerCode": "1270085755417990608",
     *     "remark": "屏蔽返还",
     *     "sourceBillNo": "***********",
     *     "tradeNo": "***********",
     *     "tradeType": "TRADE_TYPE_251"
     * }
     * @param balanceClearObject
     * @param accountTypeKey
     * @return
     */
    private static com.akucun.common.Result<Void> deductAccountBalance(BalanceClearObject balanceClearObject, String accountTypeKey) {
        try {
            TradeInfo tradeInfo = new TradeInfo();
            tradeInfo.setAccountTypeKey(accountTypeKey);
            tradeInfo.setAmount(balanceClearObject.getAmount());
            tradeInfo.setCustomerCode(balanceClearObject.getCustomerCode());
            tradeInfo.setRemark("屏蔽扣款");
            tradeInfo.setSourceBillNo("********");
            tradeInfo.setTradeNo("********");
            tradeInfo.setTradeType(accountTradeTypeMap.get(accountTypeKey));

            com.akucun.common.Result<Void> result = restTemplate.exchange(
                    prodGatewayUrl + "/account-proxy/api/account/proxy/center/dealTrade",
                    HttpMethod.POST,
                    new HttpEntity<>(JSON.toJSONString(tradeInfo), headers),
                    new ParameterizedTypeReference<com.akucun.common.Result<Void>>() {
                    }).getBody();
            return result;
        } catch (Exception e) {
            log.error("账户清零交易异常：{}", JSON.toJSONString(balanceClearObject), e);
            return com.akucun.common.Result.error("账户清零交易异常");
        }
    }

    public static boolean exportCSV(File file, List<String> dataList){
        boolean isSuccess = false;
        FileWriter fw = null;
        try {
            fw = new FileWriter(file);
            for (String data: dataList) {
                fw.write(data + "\r\n");
                fw.flush();
            }
            isSuccess = true;
        } catch (Exception e) {
            com.aikucun.common2.log.Logger.error("写入文件异常", e);
        }finally {
            try {
                if (fw != null) {
                    fw.close();
                }
            } catch (Exception e) {
                com.aikucun.common2.log.Logger.error("流关闭异常", e);
            }
        }
        return isSuccess;
    }

    @Data
    public static class BalanceClearObject {

        private String customerCode;

        private String customerType;

        private BigDecimal amount;

        private String success;

        private String errorMsg;
    }

}
