package com.akucun.account.proxy.facade.consumer;

import com.aikucun.common2.utils.datamasking.DataMask;
import com.akucun.account.proxy.client.center.AccountCenterClient;
import com.akucun.account.proxy.dao.mapper.AccountTenantMerchantMapper;
import com.akucun.account.proxy.dao.model.AccountTenantMerchant;
import com.akucun.account.proxy.facade.stub.others.dto.req.TransferChannelGatewayReq;
import com.akucun.account.proxy.service.transfer.TransferChannelGatewayService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.maihaoche.starter.mq.annotation.MQConsumer;
import com.maihaoche.starter.mq.base.AbstractMQPushConsumer;
import com.mengxiang.base.common.log.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.util.Map;

/**
 * @Author: silei
 * @Date: 2021/4/30
 * @desc:
 */
@MQConsumer(instances = {
        @MQConsumer.Instance(
                instanceName = "DEFAULT", // 指定配置中的instance name
                topic = "AKC_MEMBER_TENANT_CHANGE_TOPIC",
                tag = "AKC_SAAS_ENTERPRISE_BIND_APP_ID_TAG",
                consumerGroup = "AKC_ACCOUNT_UPDATE_ACCOUNT_INFO_GROUP")})
public class AccountTenantUpdateConsumer extends AbstractMQPushConsumer<Object> {

    @Resource
    private AccountTenantMerchantMapper accountTenantMerchantMapper;
    @Autowired
    private AccountCenterClient accountCenterClient;
    @Autowired
    private TransferChannelGatewayService transferChannelGatewayService;

    @Value("${saas.channel.code:EMP_SAAS}")
    private String channelCode;

    @Value("${saas.gateway.code:WECHAT_V1}")
    private String gatewayCode;

    @Override
    public boolean process(Object obj, Map<String, Object> extMap) {
        Logger.info("消费 AKC_SAAS_ENTERPRISE_BIND_APP_ID_TAG obj :{}", obj);
//        boolean flag;
        String message = obj.toString();
        JSONObject json = JSONObject.parseObject(message);
        String tenantId = json.getString("tenantId");
        String appId = json.getString("appId");
        LambdaQueryWrapper<AccountTenantMerchant> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AccountTenantMerchant::getTenantId, tenantId)
                .eq(AccountTenantMerchant::getStatus, 0);
        AccountTenantMerchant merchant = accountTenantMerchantMapper.selectOne(wrapper);
        if (merchant != null) {
            merchant.setAppId(appId);
            accountTenantMerchantMapper.updateById(merchant);
            Logger.info("AccountCreateConsumer merchant update success, merchant:{}", DataMask.toJSONString(merchant));
//            flag = true;
        } else {
            String tenantName = json.getString("tenantName");
            String tenantType = json.getString("tenantType");
            Logger.warn("AccountCreateConsumer merchant not exist, tenantId:{}", tenantId);
            merchant = new AccountTenantMerchant();
            //租户类型  tenant_type  0 爱库存， 1.三方小程序， 2.企业饷店，3.SASS标准类型
            merchant.setTenantId(tenantId);
            merchant.setAppId(appId);
            merchant.setTenantType(tenantType);
            merchant.setTenantName(tenantName);
            accountTenantMerchantMapper.insert(merchant);

//            AccountOperateInfoReq req = new AccountOperateInfoReq();
//            req.setCustomerCode(tenantId);
//            req.setCustomerName(tenantName);
//            req.setAccountTypeKey(AccountKeyConstants.AT.getName());
//            req.setOperationType("CREATE");
//            Result<Void> result = accountCenterClient.accountOperate(req);
//            flag = result.isSuccess();
        }
        TransferChannelGatewayReq req = new TransferChannelGatewayReq();
        req.setGatewayCode(gatewayCode);
        req.setChannelCode(channelCode);
        req.setTenantId(tenantId);
        req.setAppId(appId);
        transferChannelGatewayService.saveUpdate(req);
//        return flag;
        return true;
    }

}
