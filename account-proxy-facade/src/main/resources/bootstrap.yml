app:
  id: account-proxy
apollo:
  bootstrap:
    enabled: true
    namespaces: application

spring:
  profiles:
    active: stable
  application:
    name: account-proxy
  datasource:
    url: **********************************************************************************************************************************************************************************************************
    username: dev
    password: DvdKf8G192
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      pool-name: account-proxy-hikariCP

account:
  proxy:
    zk:
      address: zk1.akcdevdev.com:2181,zk2.akcdevdev.com:2181,zk3.akcdevdev.com:2181
    dubbo:
      version: 1.0.0

server:
  port: 8080

endpoints:
  web:
    exposure:
      include: prometheus
      exclude: "*"
  enabled: false
  prometheus:
    sensitive: false
    enabled: true

management:
  metrics:
    tags:
      application: account-proxy

eureka:
  user-name: merchant
  pass-word: 123456
  instance:
      prefer-ip-address: true
      instance-id: ${spring.cloud.client.ipAddress}:${server.port}
      status-page-url-path: swagger-ui.html

### mybatis
mybatis-plus:
  global-config:
    db-config:
      # 1 代表已删除，不配置默认是1，也可修改配置
      logic-delete-value: 1
      # 0 代表未删除，不配置默认是0，也可修改配置
  mapper-locations: classpath*:mapper/**/*.xml,classpath*:spring/sqlmap/*Mapper.xml
  type-aliases-package: com.mengxiang.transaction.framework.dao
  configuration:
    # 查询时,关闭关联对象及时加载以提高性能
    lazy-loading-enabled: false
    # 设置关联对象加载的形态,此处为按需加载字段(加载字段由SQL指定),不会加载关联表的所有字段,以提高性能
    aggressive-lazy-loading: false
    # 是否允许单一语句返回多结果集（需要兼容驱动）
    multiple-result-sets-enabled: true
    # 允许使用列标签代替列名
    use-column-label: true
    # 允许使用自定义的主键值(比如由程序生成的UUID 32位编码作为键值), 数据表的pk生成策略将被覆盖
    use-generated-keys: true
    # 给予被嵌套的resultMap以字段-属性的映射支持
    auto-mapping-behavior: partial
    # 给予被嵌套的resultMap以字段-属性的映射支持
    default-executor-type: reuse
    # 数据库超过10秒仍未响应则超时
    default-statement-timeout: 10
    #是否开启自动驼峰命名规则（camel case）映射，即从经典数据库列名 A_COLUMN 到经典 Java 属性名 aColumn 的类似映射
    map-underscore-to-camel-case: true
    #log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

## 开启一致性框架
com:
  mengxiang:
    transaction:
      task:
        enable: true
---
spring:
  profiles: dev
eureka:
  client:
    service-url:
      defaultZone: http://${eureka.user-name}:${eureka.pass-word}@eureka.akcdevdev.com:8080/eureka/
---
spring:
  profiles: test1
eureka:
  client:
    service-url:
      defaultZone: http://${eureka.user-name}:${eureka.pass-word}@eureka.akctest1.com:8080/eureka/
---
spring:
  profiles: test2
eureka:
  client:
    service-url:
      defaultZone: http://${eureka.user-name}:${eureka.pass-word}@eureka.akctest2.com:8080/eureka/
---
spring:
  profiles: release
eureka:
  client:
    service-url:
      defaultZone: http://${eureka.user-name}:${eureka.pass-word}@eureka.akcrelease.com:8080/eureka/

---
spring:
  profiles: pre
eureka:
  client:
    service-url:
      defaultZone: http://${eureka.user-name}:${eureka.pass-word}@eureka.akcpre.com:8080/eureka/
---
spring:
  profiles: prod
eureka:
  client:
    service-url:
      defaultZone: ${eureka}