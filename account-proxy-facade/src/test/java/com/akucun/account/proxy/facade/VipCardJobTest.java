package com.akucun.account.proxy.facade;

import com.akucun.account.proxy.common.constant.CommonConstants;
import com.akucun.account.proxy.common.utils.AESUtils;
import com.akucun.account.proxy.service.acct.job.VipCardBonusTradeJob;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2021/01/04 16:05
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class VipCardJobTest {
    @Autowired
    private VipCardBonusTradeJob vipCardBonusTradeJob;

    @Test
    public void vipCardBonusTradeJob() throws Exception {
        vipCardBonusTradeJob.execute("");
    }

    @Test
    public void generateVip() {
        List<String> codes = new ArrayList<>();
        codes.add("VIPTEST000000001,GYZYFJIEC1MQ8ORE");
        codes.add("VIPTEST000000002,8B5BP3FNRD67PJDL");
        codes.add("VIPTEST000000003,X79WQ9P3SEWV71MX");
        codes.add("VIPTEST000000004,GLWM69GWQT1RPRG8");
        codes.add("VIPTEST000000005,JSQ1TN7CBUMV7WFY");

        int amount = 100;
        for(String c : codes) {
            String cardNo = c.split(",")[0];
            String code = c.split(",")[1];
            String enCode = AESUtils.encrypt(code, CommonConstants.VIP_CARD_AES_KEY);
            String sql = "insert into mshop_vip_card(card_no, invite_code,amount,remark) values('%s','%s',%s,'%s');";
            String sqlFmt = String.format(sql, cardNo, enCode, amount, code);
            System.out.println(sqlFmt);
        }
    }
}
