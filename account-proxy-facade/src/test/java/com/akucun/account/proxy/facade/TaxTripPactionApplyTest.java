package com.akucun.account.proxy.facade;

import com.akucun.account.proxy.common.enums.TaxTripPactionApplyStatusEnum;
import com.akucun.account.proxy.dao.model.TaxTripPactionApply;
import com.akucun.account.proxy.service.acct.TaxTripPactionApplyService;
import com.akucun.account.proxy.service.acct.bo.TaxTripPactionApplyBO;
import com.alibaba.fastjson.JSON;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest
public class TaxTripPactionApplyTest {
    @Autowired
    private TaxTripPactionApplyService taxTripPactionApplyService;

    @Test
    public void testSaveApply(){
        TaxTripPactionApplyBO taxTripPactionApplyBO = new TaxTripPactionApplyBO();
        taxTripPactionApplyBO.setUserName("张三三");
        taxTripPactionApplyBO.setCustomerCode("********");
        taxTripPactionApplyBO.setCustomerType("20");
        taxTripPactionApplyBO.setBankName("招商银行南京东路支行");
        taxTripPactionApplyBO.setBankCardNo("****************");
        taxTripPactionApplyBO.setPactionImgUrl("http://www.baidu.com?adfafdadfadfdsfdfadfa.jpg");
        taxTripPactionApplyBO.setStatus(0);
        taxTripPactionApplyBO.setRejectReason("");
        taxTripPactionApplyBO.setSocialCreditCode("913101053511107705");
        taxTripPactionApplyBO.setMerchantName("阿吉加盟店");
        taxTripPactionApplyBO.setLegalPersonName("阿吉");
        taxTripPactionApplyService.saveApply(taxTripPactionApplyBO);
    }

    @Test
    public void testSelectValidApply(){
        TaxTripPactionApply taxTripPactionApply = taxTripPactionApplyService.selectValidApply("1234566", "20");
        System.out.println(JSON.toJSONString(taxTripPactionApply));
    }

    @Test
    public void testUpdateApply(){
        TaxTripPactionApply taxTripPactionApply = taxTripPactionApplyService.selectValidApply("1234566", "20");
        taxTripPactionApply.setStatus(TaxTripPactionApplyStatusEnum.AUDIT_PASS.getStatus());
        taxTripPactionApplyService.updateApply(taxTripPactionApply);
    }

    @Test
    public void testHasAuditingApply(){
        boolean has = taxTripPactionApplyService.hasAuditingApply("NM21981387", "NM");
        System.out.println(has);
    }
}
