package com.akucun.account.proxy.facade;

import com.aikucun.common2.base.exception.BusinessException;
import com.akucun.account.proxy.common.enums.TenantTypeEnum;
import com.akucun.account.proxy.facade.consumer.AccountTenantCreateConsumer;
import com.akucun.account.proxy.service.help.TenantCoreHelper;
import com.alibaba.fastjson.JSONObject;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.HashMap;

/**
 * @Author: silei
 * @Date: 2020/12/20
 * @desc:
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class AccountTenantCreateTest {

    @Resource
    private AccountTenantCreateConsumer accountTenantCreateConsumer;

    @MockBean
    private TenantCoreHelper tenantCoreHelper;

    @Test
    public void createConsignmentBondAccount() throws BusinessException {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("tenantId", "2542325254033126848");
        jsonObject.put("tenantName", "代销帐扣渠道商");
        jsonObject.put("tenantType", TenantTypeEnum.OPEN_SUPPLY_CONSIGNMENT_WITHHOLD.getValue().toString());

        Mockito.when(tenantCoreHelper.queryTenantPayComponentValue(Long.valueOf(jsonObject.getString("tenantId")))).thenReturn(0);

        accountTenantCreateConsumer.process(jsonObject.toJSONString(), new HashMap<>());
    }

}
