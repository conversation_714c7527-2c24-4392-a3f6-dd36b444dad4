package com.akucun.account.proxy.facade;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.dao.mapper.WithdrawApplyRecordMapper;
import com.akucun.account.proxy.facade.stub.enums.CustomerType;
import com.akucun.account.proxy.facade.stub.others.account.vo.AccountWithdrawVO;
import com.akucun.account.proxy.service.acct.AccountWithdrawService;
import com.akucun.account.proxy.service.acct.PromoTradeService;
import com.alibaba.fastjson.JSON;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * @Author: silei
 * @Date: 2021/12/9
 * @desc:
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class AccountWithdrawTest {

    @Resource
    private AccountWithdrawService accountWithdrawService;


    @Test
    public void shopkeeperBankWithdrawTest(){
        AccountWithdrawVO vo = generateShopkeeperWithdrawParam();

        Result<String> result = accountWithdrawService.accountWithdraw(vo);
        System.out.println(result);
    }

    private AccountWithdrawVO generateShopkeeperWithdrawParam() {
        AccountWithdrawVO vo = new AccountWithdrawVO();
        vo.setCustomerCode("**********");
        vo.setCustomerType(CustomerType.NM.getName());
        vo.setCustomerName("张菲");
        vo.setApplyUser("张菲");
        vo.setIdentifyNo("20ZH8LDMIlj6LGRI7ltAmIK5PtHfG/FFUH6fo+mKyH9lDK0YK6b5imwL8MMRVGA0vaxo1xGQTC2253d2669JQpHEfJJPYhMgbvA2Cg2ABC/FVcjSabi2RTtLtJEOqkeAV1MmMoaoLjIv0VklxqoeKNXvAQideNkF8EndHoMz93NJk=");
        vo.setAmount(new BigDecimal("100"));
        vo.setBankName("平安银行");
        vo.setBankNo("****************");
        vo.setShopId("851749370683419349");
        return vo;
    }

    @Test
    public void shopAgentBankWithdrawTest(){
        AccountWithdrawVO vo = generateShopAgentWithdrawParam();

        Result<String> result = accountWithdrawService.accountWithdraw(vo);
        System.out.println(result);
    }

    private AccountWithdrawVO generateShopAgentWithdrawParam() {
        AccountWithdrawVO vo = new AccountWithdrawVO();
        vo.setCustomerCode("855459652523276372");
        vo.setCustomerType(CustomerType.NMDL.getName());
        vo.setApplyUser("张菲");
        vo.setCustomerName("张菲");
        vo.setIdentifyNo("10Ih52+x2DZosSIZiOx6XBtH+aP4llGc5LT/0eHQAiusEgV4tmQIvZnZSm71UHfNsJMEcNd+pKz8/5MdqkwfKuzn1/yUrQE2oQhQHm2FZaF9QTU7aJFPKAwxYep+LP1GSfOZgNIfj5SEyCZI+PJ5dh6yOk05tzuAJZ6aKNpsjbNEs=");
        vo.setAmount(new BigDecimal("100"));
        vo.setBankName("平安银行");
        vo.setBankNo("****************");
        vo.setShopId("851749370683419349");
        return vo;
    }


    @Test
    public void shopkeeperWechatWithdrawTest(){
        AccountWithdrawVO vo = generateWechatWithdrawParam(CustomerType.NM.getName());
        Result<String> result = accountWithdrawService.accountWechatWithdraw(vo);
        System.out.println(result);
    }

    private AccountWithdrawVO generateWechatWithdrawParam(String customerType) {
        AccountWithdrawVO vo = new AccountWithdrawVO();
        if (CustomerType.NM.getName().equals(customerType)){
            vo.setCustomerCode("**********");
            vo.setCustomerType(CustomerType.NM.getName());
            vo.setChannelCode("WECHAT_XCX");
            vo.setOpenId("on7t85dfVenCpXkz-FqMuhj0Xi4w");
        } else {
            vo.setCustomerCode("855459652523276372");
            vo.setCustomerType(CustomerType.NMDL.getName());
            vo.setChannelCode("WECHAT_H5");
            vo.setOpenId("on7t85dne5HCCDGUCP6N4jcFsrXM");
        }

        vo.setTenantId("151738493257170900");
        vo.setApplyUser("张菲");
        vo.setCustomerName("张菲");
        vo.setIdentifyNo("10Ih52+x2DZosSIZiOx6XBtH+aP4llGc5LT/0eHQAiusEgV4tmQIvZnZSm71UHfNsJMEcNd+pKz8/5MdqkwfKuzn1/yUrQE2oQhQHm2FZaF9QTU7aJFPKAwxYep+LP1GSfOZgNIfj5SEyCZI+PJ5dh6yOk05tzuAJZ6aKNpsjbNEs=");
        vo.setAmount(new BigDecimal("100"));
        vo.setShopId("851749370683419349");

        return vo;

    }

    @Test
    public void shopAgentWechatWithdrawTest(){
        AccountWithdrawVO vo = generateWechatWithdrawParam(CustomerType.NMDL.getName());
        Result<String> result = accountWithdrawService.accountWechatWithdraw(vo);
        System.out.println(result);
    }

    @Resource
    private PromoTradeService promoTradeService;

    @Test
    public void testMonthlyDiligenceEmpowerAwardSettle(){
        Result<Void> result = promoTradeService.monthlyDiligenceEmpowerAwardSettle("**********", "NM", "TEST202502140000001", new BigDecimal("100"),
                "月勤奖/励新奖奖励发放测试", "月勤奖/励新奖", "XD_BALANCE",1);
        System.out.println(JSON.toJSONString(result));
    }

    @Resource
    private WithdrawApplyRecordMapper withdrawApplyRecordMapper;

    @Test
    public void testSelectWithdrawSummary(){
        Long summary = withdrawApplyRecordMapper.selectWithdrawSummary("883d90777cd845b040ff34ac13dbb00b", "202311", true);
        Assert.isTrue(summary.equals(new Long(1500)), "提现汇总金额不正确");
    }


}
