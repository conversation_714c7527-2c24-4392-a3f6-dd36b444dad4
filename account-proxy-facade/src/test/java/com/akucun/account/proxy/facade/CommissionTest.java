package com.akucun.account.proxy.facade;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.common.utils.WechatNotifyTool;
import com.akucun.account.proxy.facade.stub.commission.AccountCommissionFacade;
import com.akucun.account.proxy.facade.stub.enums.DetailTypeConstants;
import com.akucun.account.proxy.facade.stub.others.trade.req.AccountCommissionReq;
import com.alibaba.fastjson.JSON;
import com.mengxiang.base.common.log.Logger;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * @Author: silei
 * @Date: 2020/12/20
 * @desc:
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class CommissionTest {

    @Resource
    private AccountCommissionFacade commissionFacade;

    @Resource
    private WechatNotifyTool wechatNotifyTool;

    @Test
    public void notifyMsg(){
        wechatNotifyTool.sendNotifyMsg("account-proxy initAccountExecStepContext retry exceed max limit! subTradeType: test, customerCode: 123456" );
    }

    @Test
    public void commissionTest(){
        for (int i = 0; i < 100; i++) {
            Result<Void> result = commissionFacade.allocateCommission(generateParam(i));
            Logger.info("commissionTest result:{}", JSON.toJSONString(result));
        }
    }

    private AccountCommissionReq generateParam(int i){

        AccountCommissionReq req = new AccountCommissionReq();
        req.setCustomerCode("788007835346321408");
        req.setCustomerType("NMDL");
        req.setAmount(new BigDecimal("6.34"));
        req.setRemark(DetailTypeConstants.TRADE_TYPE_265.getCname());
        req.setTradeType(DetailTypeConstants.TRADE_TYPE_265.getName());
        String orderNo = LocalDateTime.now().format(DateTimeFormatter.BASIC_ISO_DATE)+i;
        req.setTradeNo(orderNo);
        req.setSourceBillNo(orderNo);
        req.setShopId("747840889024069632");

        AccountCommissionReq req1 = new AccountCommissionReq();
        req1.setCustomerCode("788007835346321408");
        req1.setCustomerType("NMDL");
        req1.setAmount(new BigDecimal("4.6"));
        req1.setRemark(DetailTypeConstants.TRADE_TYPE_266.getCname());
        req1.setTradeType(DetailTypeConstants.TRADE_TYPE_266.getName());
//        String orderNo = LocalDateTime.now().format(DateTimeFormatter.BASIC_ISO_DATE)+i;
        req1.setTradeNo(orderNo);
        req1.setSourceBillNo(orderNo);
        req1.setShopId("747840889024069632");
        Result<Void> result = commissionFacade.allocateCommission(req1);

        return req;

    }


}
