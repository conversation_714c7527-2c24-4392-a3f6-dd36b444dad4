<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>account-proxy</artifactId>
        <groupId>com.akucun.account.proxy</groupId>
        <version>1.0.6</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>account-proxy-facade</artifactId>
    <packaging>war</packaging>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-eureka-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-hystrix</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>io.micrometer</groupId>-->
<!--            <artifactId>micrometer-registry-prometheus</artifactId>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>io.micrometer</groupId>-->
<!--            <artifactId>micrometer-spring-legacy</artifactId>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.mengxiang.base</groupId>
            <artifactId>base-metrics-client</artifactId>
        </dependency>
        <!--Apollo -->
        <dependency>
            <groupId>com.aikucun.common2</groupId>
            <artifactId>common2-apollo-client</artifactId>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
        </dependency>

        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
        </dependency>
        <dependency>
            <groupId>com.akucun.account.proxy</groupId>
            <artifactId>account-proxy-facade-stub</artifactId>
        </dependency>
        <dependency>
            <groupId>com.akucun.account.proxy</groupId>
            <artifactId>account-proxy-service</artifactId>
        </dependency>
        <!-- xxl Schedule -->
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.spring.boot</groupId>
            <artifactId>dubbo-spring-boot-starter</artifactId>
        </dependency>

        <!-- zookeeper -->
        <dependency>
            <groupId>org.apache.zookeeper</groupId>
            <artifactId>zookeeper</artifactId>
        </dependency>

        <!-- zkclient -->
        <dependency>
            <groupId>com.101tec</groupId>
            <artifactId>zkclient</artifactId>
        </dependency>
        <dependency>
            <groupId>com.akucun.account.center</groupId>
            <artifactId>account-center-feign</artifactId>
        </dependency>
        <dependency>
            <groupId>io.shardingsphere</groupId>
            <artifactId>sharding-jdbc-core</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>groovy</artifactId>
                    <groupId>org.codehaus.groovy</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.akucun.pay.base</groupId>
            <artifactId>pay-base-common</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>io.micrometer</groupId>
                    <artifactId>micrometer-spring-legacy</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.mengxiang.base</groupId>
            <artifactId>common-log</artifactId>
        </dependency>
        <dependency>
            <groupId>com.akucun</groupId>
            <artifactId>member-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.akucun.fps</groupId>
            <artifactId>account-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.akucun.fps</groupId>
            <artifactId>pingan-feign</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mengxiang.base</groupId>
            <artifactId>common-rpc-scan</artifactId>
        </dependency>

        <dependency>
            <groupId>com.mengxiang.base</groupId>
            <artifactId>base-sentinel-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-csv</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>account-proxy</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
