<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>account-proxy</artifactId>
        <groupId>com.akucun.account.proxy</groupId>
        <version>1.0.6</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>account-proxy-service</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.akucun.account.proxy</groupId>
            <artifactId>account-proxy-dao</artifactId>
        </dependency>

        <dependency>
            <groupId>com.akucun.account.proxy</groupId>
            <artifactId>account-proxy-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.akucun.account.proxy</groupId>
            <artifactId>account-proxy-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.akucun.account.proxy</groupId>
            <artifactId>account-proxy-facade-stub</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.akucun.account.center</groupId>
            <artifactId>account-center-feign</artifactId>
        </dependency>
        <dependency>
            <groupId>com.akucun.fps</groupId>
            <artifactId>account-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.akucun</groupId>
            <artifactId>akucun-common-security</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mengxiang.base</groupId>
            <artifactId>common-log</artifactId>
        </dependency>
        <dependency>
            <groupId>com.akucun</groupId>
            <artifactId>member-api</artifactId>
        </dependency>
         <dependency>
            <groupId>com.mengxiang.fin.clearing.core</groupId>
            <artifactId>fin-clearing-core-service-facade-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.maihaoche</groupId>
            <artifactId>spring-boot-starter-rocketmq</artifactId>
        </dependency>
        <dependency>
            <groupId>com.akucun.member.center</groupId>
            <artifactId>member-center-stub</artifactId>
        </dependency>
        <dependency>
                <groupId>com.mengxiang.member</groupId>
                <artifactId>mshop-member-core-service-facade-common</artifactId>
        </dependency>
        <dependency>
            <artifactId>member-center-audit-facade-stub</artifactId>
            <groupId>com.akucun.member.audit</groupId>
        </dependency>
        <dependency>
            <groupId>com.akucun.bcs.channel</groupId>
            <artifactId>bcs-channel-facade-stub</artifactId>
        </dependency>
        <dependency>
            <groupId>com.akucun</groupId>
            <artifactId>akucun-basic-common</artifactId>
            <version>********.1</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.akucun.fps</groupId>
            <artifactId>bill-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.akucun</groupId>
            <artifactId>akucun-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mengxiang.account.finpointcore</groupId>
            <artifactId>fin-point-core-service-facade-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.akucun.pay.gateway.wx</groupId>
            <artifactId>pay-gateway-wx-facade-stub</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mengxiang.saas</groupId>
            <artifactId>tenant-core-service-facade-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.akucun.sms</groupId>
            <artifactId>akucun-sms-stub</artifactId>
        </dependency>

        <dependency>
            <groupId>com.akucun.bcs.bill</groupId>
            <artifactId>bcs-bill-facade-stub</artifactId>
        </dependency>

        <dependency>
            <groupId>com.akucun.aftersale</groupId>
            <artifactId>akucun-aftersale-facade-mgt-stub</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mengxiang.base</groupId>
            <artifactId>transaction-framework</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.9</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
