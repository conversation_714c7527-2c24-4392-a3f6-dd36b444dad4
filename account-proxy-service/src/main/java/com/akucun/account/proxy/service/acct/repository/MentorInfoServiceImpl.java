package com.akucun.account.proxy.service.acct.repository;

import com.akucun.account.proxy.dao.mapper.MentorInfoMapper;
import com.akucun.account.proxy.dao.model.MentorInfo;
import com.akucun.account.proxy.service.acct.MentorInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.ObjectUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Create on : 2025/1/18 16:45
 **/
@Repository
public class MentorInfoServiceImpl implements MentorInfoService {
    @Autowired
    MentorInfoMapper mentorInfoMapper;
    @Override
    public List<MentorInfo> loadAll() {
        return mentorInfoMapper.loadAll();
    }

    @Override
    public List<MentorInfo> loadByUserCode(String userCode) {
        return mentorInfoMapper.loadByUserCode(userCode);
    }

    @Override
    public Integer saveOrUpdate(MentorInfo mentorInfo) {
        if(ObjectUtils.isEmpty(mentorInfo.getId())){
            return mentorInfoMapper.insertSelective(mentorInfo);
        }else{
            return mentorInfoMapper.updateByPrimaryKeySelective(mentorInfo);
        }
    }
}
