package com.akucun.account.proxy.service.config;

import com.akucun.account.proxy.common.utils.HttpClient;
import com.akucun.account.proxy.dao.model.PinganDecoupleWhitelist;
import com.akucun.account.proxy.service.acct.PinganDecoupleService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.mengxiang.base.common.log.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.List;

/**
 * @Author: silei
 * @Date: 2021/12/14
 * @desc:
 */
@Component
public class DecouplingConfig {

    @Autowired
    private PinganDecoupleService pinganDecoupleService;

    /**
     * 平安反清分开关
     */
    @Value("${pingan.antiClearing.switch:true}")
    private boolean antiClearingFlag;

    /**
     * 解耦的账户类型
     */
    @Value("#{'${decouple.customer.types:NM,NMDL}'.split(',')}")
    private List<String> decoupleCustomerTypes;
    
    @Value("#{'${decouple.customer.tenant.types:NM,NMDL,AT,DXQDS}'.split(',')}")
    private List<String> decoupleTenantCustomerTypes;


    /**
     * 是否需要反清分开关
     * @return
     */
    public boolean antiClearingSwitch(String customerCode, String customerType){
        //平安解耦开关未开或用户未在白名单列表需要执行反清分
        return !pinganDecoupleService.checkGrayWhitelist(customerCode, customerType);

        //未执行线下反清分，微信提现时需要反清分
//        boolean antiClearingRes =  pinganDecoupleService.checkAntiClearing(customerCode, customerType);
//        return antiClearingFlag && !antiClearingRes;
    }

    /**
     * 解耦灰度判断
     * @param customerCode
     * @return
     */
    public boolean decouplingSwitch(String customerCode, String customerType){
        if (!decoupleCustomerTypes.contains(customerType)){
            return false;
        }
        return pinganDecoupleService.checkGrayWhitelist(customerCode, customerType);
    }
    
    /**
     * 租户解耦灰度判断
     * @param customerCode
     * @param customerType
     * @return
     */
    public boolean decoupleTenantSwitch(String customerCode, String customerType){
        if (!decoupleTenantCustomerTypes.contains(customerType)){
            return false;
        }
        return pinganDecoupleService.tenantGray(customerCode, customerType);
    }

    private static void decoupleWhitelist(String url){
        BufferedReader reader = null;
        String line;
        try {
            reader = new BufferedReader(new InputStreamReader(new FileInputStream("/Users/<USER>/AKC/需求/平安解耦/whitelist.txt"), "gbk"));
            while (null != (line = reader.readLine())) {
                String[] arr = line.split(",");
                if (arr.length != 2) {
                    Logger.warn("数据非法:" + line + ",length:" + arr.length);
                    continue;
                }
                PinganDecoupleWhitelist request = new PinganDecoupleWhitelist();
                request.setCustomerCode(arr[0]);
                request.setCustomerType(arr[1]);
                request.setAntiClearingStatus(0);
                //是否白名单
                request.setWhitelistShopAgentFlag(1);
                request.setWhitelistStatus(1);
                request.setStatus(0);
                String result = HttpClient.doPost(url + "/api/account/proxy/pinganDecouple/saveOrUpdate", JSON.toJSONString(request));
                Logger.info("decoupleWhitelist request:{}, result:{}", JSONObject.toJSONString(request), result);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (null != reader) {
                try {
                    reader.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public static void main(String[] args) {

        String local = "http://localhost:8080";
        String stable = "http://*************:8080";
        String release = "http://***********:8080";;
        String pro = "http://zuul.infra.aikucun.com/account-proxy";

        String url = stable;
        decoupleWhitelist(url);
    }
}
