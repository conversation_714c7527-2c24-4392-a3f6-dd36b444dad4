package com.akucun.account.proxy.service.oa.impl;

import com.aikucun.common2.base.Result;
import com.aikucun.common2.base.exception.IErrorCode;
import com.aikucun.common2.log.Logger;
import com.akucun.account.proxy.client.oa.WorkflowClient;
import com.akucun.account.proxy.common.enums.PostActionExecStatus;
import com.akucun.account.proxy.common.enums.PostActionTypes;
import com.akucun.account.proxy.common.exception.AccountProxyException;
import com.akucun.account.proxy.facade.stub.others.oa.req.OAWorkflowCreateRequest;
import com.akucun.account.proxy.facade.stub.others.oa.req.OAWorkflowQueryRequest;
import com.akucun.account.proxy.facade.stub.others.oa.resp.OAWorkflowCreateResponse;
import com.akucun.account.proxy.facade.stub.others.oa.resp.OAWorkflowResponseInfo;
import com.akucun.account.proxy.service.oa.OAWorkflowBaseService;
import com.akucun.account.proxy.service.oa.bo.OAWorkflowStatusPollingBo;
import com.akucun.account.proxy.service.postaction.PostActionService;
import com.akucun.account.proxy.service.postaction.bo.PostActionItemBO;
import com.alibaba.fastjson.JSON;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Create on : 2025/1/14 17:40
 **/
@Service
public class OAWorkflowBaseServiceImpl implements OAWorkflowBaseService {

    @Autowired
    private WorkflowClient workflowClient;

    @Autowired
    private PostActionService postActionService;

    @Override
    public Result<OAWorkflowCreateResponse> create(OAWorkflowCreateRequest request) {
        Result<OAWorkflowCreateResponse> result = null;
        Logger.info("创建OA工作流参数：{}", JSON.toJSONString(request));
        try {
            Set<ConstraintViolation<OAWorkflowCreateRequest>> validate = Validation.buildDefaultValidatorFactory().getValidator().validate(request);
            if (CollectionUtils.isNotEmpty(validate)) {
                String validMessage = validate.stream().map(ConstraintViolation::getMessage).collect(Collectors.joining(";"));
                result = Result.error(IErrorCode.ARGUMENT_ERROR, String.format("创建OA工作流参数错误：validMessage=%s",validMessage));
            } else {
                OAWorkflowCreateResponse workflowCreateResponse = workflowClient.createWorkflow(request);
                if (request.polling()) {
                    OAWorkflowStatusPollingBo pollingBo = OAWorkflowStatusPollingBo.builder()
                            .bizNo(request.getBizNo())
                            .requestId(Integer.valueOf(workflowCreateResponse.getRequestId()))
                            .businessType(request.getBusinessType().name())
                            .notifyUrl(request.getNotifyUrl())
                            .build();
                    this.submitPollingTask(pollingBo, request.getBizNo(), request.getWorkflowTitle());
                }
                result = Result.success(workflowCreateResponse);
            }
        } catch (AccountProxyException e) {
            Logger.warn("创建OA工作流异常", e);
            result = Result.error(e.getErrorCode(), e.getErrorMsg());
        } catch (Exception e) {
            Logger.error("创建OA工作流异常", e);
            result = Result.error(e);
        } finally {
            Logger.info("创建OA工作流result：{}，bizNo：{}", JSON.toJSONString(result), request.getBizNo());
        }
        return result;
    }

    @Override
    public Result<OAWorkflowResponseInfo> queryWorkflowByRequestId(OAWorkflowQueryRequest request) {
        Result<OAWorkflowResponseInfo> result = null;
        Logger.info("查询OA工作流参数：{}", JSON.toJSONString(request));
        try {
            OAWorkflowResponseInfo responseInfo = workflowClient.queryWorkflowByRequestId(request.getRequestId(), request.getUserId());
            result = Result.success(responseInfo);
        } catch (AccountProxyException e) {
            Logger.warn("查询OA工作流异常", e);
            result = Result.error(e.getErrorCode(), e.getErrorMsg());
        } catch (Exception e) {
            Logger.error("查询OA工作流异常", e);
            result = Result.error(e);
        } finally {
            Logger.info("查询OA工作流result：{}，request：{}", JSON.toJSONString(result), JSON.toJSONString(request));
        }
        return result;
    }

    @Override
    public void submitPollingTask(OAWorkflowStatusPollingBo pollingBo, String bizId, String remark) {
        if (pollingBo == null || pollingBo.getRequestId() == null || StringUtils.isBlank(pollingBo.getBusinessType())
                || StringUtils.isBlank(pollingBo.getBizNo()) || StringUtils.isBlank(pollingBo.getNotifyUrl())) {
            throw new AccountProxyException(IErrorCode.ARGUMENT_ERROR, "OA流程状态轮询任务必传参数缺失");
        }
        PostActionItemBO postActionItemBO = PostActionItemBO.builder()
                .actionType(PostActionTypes.OA_WORKFLOW_STATUS_POLLING.getName())
                .bizId(bizId)
                .paramObject(pollingBo)
                .remark(remark)
                .status(PostActionExecStatus.EXECUTE.value())
                .build();
        postActionService.addAction(postActionItemBO);
    }
}
