package com.akucun.account.proxy.service.merchant.market;

import com.akucun.account.proxy.facade.stub.others.merchant.market.MerchantFullReturnMarketAddFreezeRequest;
import com.akucun.account.proxy.facade.stub.others.merchant.market.MerchantFullReturnMarketFreezeRequest;
import com.akucun.account.proxy.facade.stub.others.merchant.market.MerchantFullReturnMarketSettleRequest;
import com.akucun.account.proxy.facade.stub.others.merchant.market.MerchantFullReturnMarketUnFreezeRequest;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Create on : 2024/5/7 10:35
 **/
public interface MerchantFullReturnMarketFundService {

    /**
     * 活动报名（冻结金额）
     * @param request
     */
    void freeze(MerchantFullReturnMarketFreezeRequest request);

    /**
     * 活动未开始取消（解冻金额）
     * @param request
     */
    void unFreeze(MerchantFullReturnMarketUnFreezeRequest request);

    /**
     * 活动结束 或 定期加冻（加冻）
     * @param request
     */
    void addFreeze(MerchantFullReturnMarketAddFreezeRequest request);

    /**
     * 活动售后期结束结算
     * @param request
     */
    void settle(MerchantFullReturnMarketSettleRequest request);

}
