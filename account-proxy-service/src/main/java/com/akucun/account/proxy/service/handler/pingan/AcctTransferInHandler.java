package com.akucun.account.proxy.service.handler.pingan;

import com.aikucun.common2.utils.datamasking.DataMask;
import com.akucun.account.proxy.common.constant.CommonConstants;
import com.akucun.account.proxy.common.enums.ResponseEnum;
import com.akucun.account.proxy.common.enums.ResultStatus;
import com.akucun.account.proxy.common.exception.AccountProxyException;
import com.akucun.account.proxy.common.utils.AccountUtils;
import com.akucun.account.proxy.common.utils.WechatNotifyTool;
import com.akucun.account.proxy.dao.model.AccountOpTransferAmount;
import com.akucun.account.proxy.service.acct.AccountOpTransferAmountService;
import com.akucun.account.proxy.service.common.bo.AccountExecStepContext;
import com.akucun.account.proxy.service.common.bo.AccountReq;
import com.akucun.account.proxy.service.common.bo.AccountResp;
import com.akucun.account.proxy.service.common.step.AbstractHandler;
import com.akucun.fps.common.entity.Result;
import com.akucun.fps.pingan.client.api.expose.AssetsService;
import com.akucun.fps.pingan.client.vo.CashCreditVO;
import com.akucun.fps.pingan.feign.api.assets.AssetsServiceApi;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mengxiang.base.common.log.Logger;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @Author: silei
 * @Date: 2020/9/3
 * @desc: 账户余额划归到个人账户
 */
@Component
public class AcctTransferInHandler extends AbstractHandler {

    @Resource
    private AssetsServiceApi assetsServiceApi;
    @Resource
    private AccountOpTransferAmountService accountTransferAmountService;
    @Autowired
    private WechatNotifyTool wechatNotifyTool;

    @Override
    protected boolean preCheck(AccountExecStepContext accountExecStepContext) {
        AccountResp resp = accountExecStepContext.getAccountResp();
        AccountReq req = accountExecStepContext.getAccountReq();
        Logger.info("AcctTransferInHandler preCheck req:{}", DataMask.toJSONString(req));
        //参数校验
        if (StringUtils.isBlank(req.getCustomerCode())) {
            resp.setStatus(ResultStatus.F.getCode());
            resp.setReplyMsg("客户编码为空");
        }
        if (StringUtils.isBlank(req.getCustomerType())) {
            resp.setStatus(ResultStatus.F.getCode());
            resp.setReplyMsg("客户类型为空");
        }
        if (Objects.isNull(req.getAccountTradeId())) {
            resp.setStatus(ResultStatus.F.getCode());
            resp.setReplyMsg("请求流水号为空");
        }
        return !ResultStatus.F.getCode().equals(resp.getStatus());
    }

    @Override
    protected void doSubmitBefore(AccountExecStepContext accountExecStepContext) {
        AccountReq req = accountExecStepContext.getAccountReq();
        //查询归集总金额
        Long amount = queryAccountTransferAmt(req);
        Logger.info("AcctTransferInHandler doSubmitBefore amount balance:{}", amount);
        //余额
        if (amount > 0) {
            accountExecStepContext.setReqMessage(amount);
        }
    }

    /**
     * 查询归集金额总数
     * @param req
     * @return
     */
    private Long queryAccountTransferAmt(AccountReq req) {
        long amount = 0L;
        LambdaQueryWrapper<AccountOpTransferAmount> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AccountOpTransferAmount::getAccountTradeId, req.getAccountTradeId())
                .eq(AccountOpTransferAmount::getCustomerCode, req.getCustomerCode())
                .eq(AccountOpTransferAmount::getCustomerType, req.getCustomerType())
                .eq(AccountOpTransferAmount::getCollectStatus, ResultStatus.S.getCode());
        List<AccountOpTransferAmount> list = accountTransferAmountService.getBaseMapper().selectList(wrapper);
        if (!CollectionUtils.isEmpty(list)) {
            amount = list.stream().mapToLong(AccountOpTransferAmount::getAmount).sum();
        }
        return amount;
    }

    @Override
    protected void doSubmit(AccountExecStepContext accountExecStepContext) {
        Long amount = (Long) accountExecStepContext.getReqMessage();
        //金额为空说明没有做过资金归集，余额为空
        if (Objects.nonNull(amount) && amount > 0) {
            AccountReq req = accountExecStepContext.getAccountReq();
            try {
                CashCreditVO vo = generateParam(req, amount);
                Logger.info("AcctTransferInHandler doSubmit CashCreditVO:{}", DataMask.toJSONString(vo));
                Result<String> result = assetsServiceApi.credit(vo);
                Logger.info("AcctTransferInHandler doSubmit result:{}", DataMask.toJSONString(result));
                if (Objects.isNull(result)) {
                    Logger.error("AcctTransferInHandler doSubmit accountUpdateError, transferAmount result is null! customerCode:{}, customerType:{}", req.getCustomerCode(), req.getCustomerType());
                    //返回参数异常
                    throw new AccountProxyException(ResponseEnum.PARAM_EXCEPTION);
                }
                accountExecStepContext.setRespMessage(result);
            } catch (AccountProxyException e) {
                Logger.error("AcctTransferInHandler doSubmit customerCode:{}, customerType:{},accountUpdateError:{}", req.getCustomerCode(), req.getCustomerType(), e);
                throw new AccountProxyException(ResponseEnum.SUBMIT_EXCEPTION);
            }
        }
    }

    @Override
    protected void doSubmitAfter(AccountExecStepContext accountExecStepContext) {
        Result<String> result = (Result<String>) accountExecStepContext.getRespMessage();
        AccountResp resp = accountExecStepContext.getAccountResp();
        AccountReq req = accountExecStepContext.getAccountReq();
        //结果为空，说明账户无余额，直接成功
        if (Objects.nonNull(result)) {
            if (result.isSuccess()) {
                //更新资金划归状态
                int i = accountTransferAmountService.getBaseMapper().update(AccountOpTransferAmount.builder().allocateStatus(ResultStatus.S.getCode()).build(),
                        new LambdaQueryWrapper<AccountOpTransferAmount>().eq(AccountOpTransferAmount::getAccountTradeId, req.getAccountTradeId())
                                .eq(AccountOpTransferAmount::getCollectStatus, ResultStatus.S.getCode()));
                if (i != 1) {
                    wechatNotifyTool.sendNotifyMsg("Account-Proxy AcctTransferInHandler doSubmitAfter transferAmount exception! customerCode:" + req.getCustomerCode() + ", customerType:" + req.getCustomerType());
                    Logger.error("AcctTransferInHandler doSubmitAfter accountUpdateError, transferAmount exception! customerCode:{}, customerType:{}", req.getCustomerCode(), req.getCustomerType());
                    throw new AccountProxyException(ResponseEnum.AMOUNT_ALLOCATE_EXCEPTION);
                }
            } else {
                wechatNotifyTool.sendNotifyMsg("Account-Proxy AcctTransferInHandler doSubmitAfter transferAmount processing! customerCode:" + req.getCustomerCode() + ", customerType:" + req.getCustomerType());
                //进入查询逻辑
                resp.setStatus(ResultStatus.P.getCode());
                resp.setReplyCode(result.getErrorCode() + "");
                resp.setReplyMsg(result.getErrorMessage());
                return;
            }
        }
        resp.setStatus(ResultStatus.S.getCode());
        resp.setReplyCode(CommonConstants.SUCC_CODE);
        resp.setReplyMsg(ResultStatus.S.getDesc());
    }

    private CashCreditVO generateParam(AccountReq req, long amount) {
        CashCreditVO vo = new CashCreditVO();
        //店主编码需要拼接
        vo.setThirdCustId(AccountUtils.getSellerCode(req.getCustomerCode(), req.getCustomerType()));
        vo.setCustomerType(req.getCustomerType());
        vo.setTranAmount((int) amount);
        vo.setBillType("AU");//账户升级account upgrade
        vo.setBillNo(req.getDetailOrderNo());
        vo.setRemark("账户升级");
        return vo;
    }
}
