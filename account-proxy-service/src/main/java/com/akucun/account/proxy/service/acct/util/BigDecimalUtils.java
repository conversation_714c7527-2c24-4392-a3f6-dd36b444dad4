package com.akucun.account.proxy.service.acct.util;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Create on : 2025/1/18 00:57
 **/
public class BigDecimalUtils {
    // 数字大写
    private static final char[] chineseNumerals = {'零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'};
    // 单位
    private static final String[] unit = {"", "拾", "佰", "仟"};
    // 分组单位
    private static final String[] groupUnit = {"", "万", "亿", "万亿"};

    /**
     * 数值转换为中文
     * @param obj
     * @return
     */
    public static String convertToChineseUppercase(Object obj) {
        Double amount = Double.valueOf(obj.toString());
        // 将金额转为字符串，保证保留两位小数
        String strAmount = String.format("%.2f", amount);
        // 拆分整数部分和小数部分
        String[] parts = strAmount.split("\\.");
        String strInteger = null;
        // 整数部分是否为0
        boolean integerIsZero = false;
        if (parts[0].equals("0")) {
            integerIsZero = true;
        }
        // 整数部分不等于0时才需要处理
        if (!integerIsZero) {
            strInteger = integerConvert(Long.valueOf(parts[0]));
        }
        String strDecimal = decimalConvert(parts[1], integerIsZero);
        // 如果都为0时
        if (strInteger == null && strDecimal.isEmpty()) {
            return "零元";
        }
        // 小数部分为0时
        if (strDecimal.isEmpty()) {
            return strInteger.concat("元整");        // 没有整数部分时
        } else if (strInteger == null) {
            return strDecimal;
        } else {
            return strInteger.concat("元").concat(strDecimal);
        }
    }

    /**
     * 小数部分处理
     */
    public static String decimalConvert(String decimalPart, boolean integerIsZero) {
        // 处理小数部分
        StringBuilder decimalResult = new StringBuilder();
        if (!decimalPart.equals("00")) {  // 如果小数部分不为00，则转换            //decimalResult.append("点");
            if (decimalPart.charAt(0) != '0') {
                decimalResult.append(chineseNumerals[decimalPart.charAt(0) - '0']).append("角");
            } else if (!integerIsZero) {
                decimalResult.append("零");
            }
            if (decimalPart.charAt(1) != '0') {
                decimalResult.append(chineseNumerals[decimalPart.charAt(1) - '0']).append("分");
            }
        }
        return decimalResult.toString();
    }

    /**
     * 整数部分转换
     */
    public static String integerConvert(long number) {
        StringBuilder result = new StringBuilder();
        String numStr = Long.toString(number);
        int length = numStr.length();
        // 分组处理每四位
        int groupCount = (length + 3) / 4;
        // 计算需要处理的组数
        boolean lastGroupZero = false; // 标记上一组是否全为零
        for (int i = 0; i < groupCount; i++) {
            int start = length - 4 * (i + 1); // 当前组开始位置
            int end = length - 4 * i; // 当前组结束位置
            if (start < 0) {
                start = 0;
            }
            String group = numStr.substring(start, end);
            String groupChinese = convertGroup(group);
            if (!"零".equals(groupChinese)) { // 当前组不全为零时添加单位
                result.insert(0, groupChinese + groupUnit[i]);
                lastGroupZero = false;
            } else if (!lastGroupZero && i > 0) { // 当前组全为零且不是第一组
                result.insert(0, "零");
                lastGroupZero = true;
            }
        }
        // 删除结果中多余的零
        while (result.indexOf("零零") != -1) {
            result = new StringBuilder(result.toString().replace("零零", "零"));
        }
        if (result.lastIndexOf("零") == result.length() - 1) {
            result.deleteCharAt(result.length() - 1);
        }
        if (result.indexOf("零") == 0) {
            result.deleteCharAt(0);
        }
        return result.toString();
    }

    /**
     * 对数据进行分组处理
     */
    private static String convertGroup(String group) {
        StringBuilder groupChinese = new StringBuilder();
        int len = group.length();
        boolean lastNumZero = false; // 记录上一个数字是否为零，用于控制零的输出
        for (int i = 0; i < len; i++) {
            int num = group.charAt(i) - '0';
            if (num != 0) {
                if (lastNumZero) {
                    groupChinese.append('零');
                }
                groupChinese.append(chineseNumerals[num]).append(unit[len - i - 1]);
                lastNumZero = false;
            } else {
                lastNumZero = true;
            }
        }
        return groupChinese.toString();
    }

    public static void main(String[] args) {
        System.out.println(convertToChineseUppercase(1234567890.12));  // 壹拾贰亿叁仟肆佰伍拾陆万柒仟捌佰玖拾元壹角贰分
        System.out.println(convertToChineseUppercase(0.50));           // 伍角
        System.out.println(convertToChineseUppercase(0.05));           // 伍分
        System.out.println(convertToChineseUppercase(1234.00));        // 壹仟贰佰叁拾肆元整
        System.out.println(convertToChineseUppercase(1008.00));        // 壹仟零捌元整
        System.out.println(convertToChineseUppercase(108.00));         // 壹佰零捌元整
        System.out.println(convertToChineseUppercase(18.00));          // 壹拾捌元整
        System.out.println(convertToChineseUppercase(180.00));         // 壹佰捌拾元整
        System.out.println(convertToChineseUppercase(1800.00));        // 壹仟捌佰元整
        System.out.println(convertToChineseUppercase(1080000.00));     // 壹佰零捌万元整
        System.out.println(convertToChineseUppercase(1880000.00));     // 壹佰捌拾捌万元整
        System.out.println(convertToChineseUppercase(1800000.00)); // 壹佰捌拾万元整
        System.out.println(convertToChineseUppercase(18800000.00)); // 壹仟捌佰捌拾万元整
        System.out.println(convertToChineseUppercase(18000000.00)); // 壹仟捌佰万元整
        System.out.println(convertToChineseUppercase(10000.00));    // 壹万元整
        System.out.println(convertToChineseUppercase(100001.00));   // 壹拾万零壹元整
        System.out.println(convertToChineseUppercase(100001.01));   // 壹拾万零壹元零壹分
        System.out.println(convertToChineseUppercase(10000000001.00)); // 壹佰亿万零壹元整
    }
}
