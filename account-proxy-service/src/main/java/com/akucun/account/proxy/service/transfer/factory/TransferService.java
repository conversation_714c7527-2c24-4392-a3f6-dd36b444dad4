package com.akucun.account.proxy.service.transfer.factory;

import com.akucun.account.proxy.dao.model.PaymentTransfer;
import com.akucun.account.proxy.facade.stub.others.dto.req.PaymentTransferReq;
import com.akucun.account.proxy.facade.stub.others.dto.res.PaymentTransferResp;
import com.akucun.account.proxy.service.transfer.bo.PaymentTransferBO;
import com.akucun.account.proxy.service.transfer.bo.TransferGatewayBO;

/**
 * <AUTHOR>
 * @date 2021/01/28 18:05
 */
public interface TransferService {

    PaymentTransferBO transfer(PaymentTransfer transfer, TransferGatewayBO transferGatewayBO);

    PaymentTransferBO transferQuery(String tradeNo, TransferGatewayBO transferGatewayBO);

    boolean isSupport(String transferType);
}
