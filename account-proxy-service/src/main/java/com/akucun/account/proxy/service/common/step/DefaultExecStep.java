package com.akucun.account.proxy.service.common.step;

import com.akucun.account.proxy.common.constant.StepConstant;
import com.akucun.account.proxy.common.enums.ResultStatus;
import com.akucun.account.proxy.service.common.bo.AccountExecContext;
import com.akucun.account.proxy.service.common.bo.AccountExecStepContext;
import com.akucun.account.proxy.service.acct.bo.AccountOpTradeDetailBO;
import com.akucun.account.proxy.service.common.bo.AccountResp;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * @Author: silei
 * @Date: 2020/8/26
 * @desc: 账户升级整体流程步骤
 */
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class DefaultExecStep extends Step {

    @Override
    protected boolean submitBefore(AccountExecContext accountExecContext) {
        AccountExecStepContext accountExecStepContext = accountExecContext.initAccountExecStepContext(this);
        return accountExecStepContext.isContinue();
    }

    @Override
    protected void submit(AccountExecContext accountExecContext) {
        AccountExecStepContext accountExecStepContext = accountExecContext.getLatestAccountStepExecContext();
        AccountResp accountResp = new AccountResp();
        accountExecStepContext.setAccountResp(accountResp);

        if (StepConstant.ACTION_EXEC.equals(accountExecStepContext.getAction())) {
            getStepAdapter().exec(accountExecStepContext);
        } else if (StepConstant.ACTION_QUERY.equals(accountExecStepContext.getAction())) {
            getStepAdapter().query(accountExecStepContext);
            accountExecStepContext.getAccountResp().setQuery(true);
        }
    }

    @Override
    protected void submitAfter(AccountExecContext accountExecContext) {
        //DB操作
        AccountExecStepContext accountExecStepContext = accountExecContext.getLatestAccountStepExecContext();
        AccountResp accountResp = accountExecStepContext.getAccountResp();
        updateAccountOpTradeDetail(accountExecStepContext.getAccountOpTradeDetailBO(), accountResp);
        if (!(accountResp.isQuery() && Objects.equals(ResultStatus.P.getCode(), accountResp.getStatus()))) {
            accountExecContext.updateAccountOpTradeDetail();
        }

    }

    private void updateAccountOpTradeDetail(AccountOpTradeDetailBO detail, AccountResp resp) {
        detail.setStatus(resp.getStatus());
        detail.setReplyCode(resp.getReplyCode());
        detail.setReplyMsg(resp.getReplyMsg());
    }
}
