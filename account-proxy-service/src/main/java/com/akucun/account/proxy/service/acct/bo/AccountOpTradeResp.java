package com.akucun.account.proxy.service.acct.bo;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: silei
 * @Date: 2020/8/26
 * @desc: 账户请求响应
 */
@Data
public class AccountOpTradeResp {


    /**
     * 请求流水号(取时间戳)
     */
    private String orderNo;
    /**
     * 交易状态
     */
    private String status;
    /**
     * 返回码
     */
    private String replyCode;
    /**
     * 返回消息
     */
    private String replyMsg;
    /**
     * 扩展信息
     */
    private Map<String, String> extField = new HashMap<>();

}
