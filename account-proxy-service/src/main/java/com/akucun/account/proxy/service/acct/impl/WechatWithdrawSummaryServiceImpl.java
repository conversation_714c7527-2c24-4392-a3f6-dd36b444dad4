package com.akucun.account.proxy.service.acct.impl;

import com.akucun.account.proxy.dao.mapper.WechatWithdrawSummaryMapper;
import com.akucun.account.proxy.dao.model.WechatWithdrawSummary;
import com.akucun.account.proxy.service.acct.WechatWithdrawSummaryService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * @Author: silei
 * @Date: 2021/9/4
 * @desc:
 */
@Service
public class WechatWithdrawSummaryServiceImpl extends ServiceImpl<WechatWithdrawSummaryMapper, WechatWithdrawSummary> implements WechatWithdrawSummaryService {

    @Resource
    private WechatWithdrawSummaryMapper wechatWithdrawSummaryMapper;

    @Override
    public void addAmount(Long id, BigDecimal withdrawAmount) {
        wechatWithdrawSummaryMapper.addAmount(id, withdrawAmount);
    }

    @Override
    public void subtractAmount(Long id, BigDecimal withdrawAmount) {
        wechatWithdrawSummaryMapper.subtractAmount(id, withdrawAmount);
    }
}
