package com.akucun.account.proxy.service.postaction.task;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.client.marketaccount.AwardEventCallbackClient;
import com.akucun.account.proxy.client.marketaccount.feign.entity.AssetAccountEventCallbackRequest;
import com.akucun.account.proxy.common.enums.PostActionTypes;
import com.akucun.account.proxy.dao.model.PostActionItem;
import com.akucun.account.proxy.service.postaction.common.AbsPostActionExecutor;
import com.akucun.fps.common.util.GsonUtils;
import com.mengxiang.base.common.log.Logger;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class MarketFullReturnCallbackNotifyTask extends AbsPostActionExecutor {

	@Autowired
	private AwardEventCallbackClient awardEventCallbackClient;

	@Override
	protected String getActionType() {
		return PostActionTypes.MARKET_FULL_RETURN_CALLBACK_NOTIFY.getName();
	}

	@XxlJob("marketFullReturnCallbackNotifyTask")
	public ReturnT<String> execute(String param) {
		return this.executeEntrance();
	}

	@Override
	public Result<Void> execute(PostActionItem item) {
		Result<Void> resp = Result.success();
		try {
			AssetAccountEventCallbackRequest callbackRequest = GsonUtils.getInstance().fromJson(item.getParam(), AssetAccountEventCallbackRequest.class);
			awardEventCallbackClient.awardEventCallback(callbackRequest);
		}catch (Exception e) {
			resp.setSuccess(false);
			resp.setMessage(e.getMessage());
            Logger.error("提交任务失败：", e);
		}
		 return resp;
	}
}
