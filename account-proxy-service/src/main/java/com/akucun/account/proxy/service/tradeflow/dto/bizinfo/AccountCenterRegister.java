package com.akucun.account.proxy.service.tradeflow.dto.bizinfo;

import java.io.Serializable;

import lombok.Data;

@Data
public class AccountCenterRegister implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 3320344702708630263L;
	
	/**
	 *  用户类型
	 */
	private String customerType;
	
	/**
	 * 用户编号
	 */
	private String customerCode;
	
	/**
	 * 账户类型
	 */
	private String accountType;
	
	/**
	 * 客户名称（个人-真实姓名，个体/企业-公司名称）
	 */
	private String customerName;
	
	/**
	 * 租户ID
	 */
	private String tenantId;
	
	/**
	 * 租户类型
	 */
	private Integer tenantType;

}
