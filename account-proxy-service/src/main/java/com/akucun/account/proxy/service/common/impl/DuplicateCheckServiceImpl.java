package com.akucun.account.proxy.service.common.impl;

import com.aikucun.common2.base.Result;
import com.aikucun.common2.log.Logger;
import com.akucun.account.proxy.common.utils.Results;
import com.akucun.account.proxy.common.enums.ResponseEnum;
import com.akucun.account.proxy.dao.mapper.DuplicateCheckMapper;
import com.akucun.account.proxy.dao.model.DuplicateCheck;
import com.akucun.account.proxy.service.common.DuplicateCheckService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

/**
 * @Author: silei
 * @Date: 2021/3/11
 * @desc:
 */
@Service
public class DuplicateCheckServiceImpl extends ServiceImpl<DuplicateCheckMapper, DuplicateCheck> implements DuplicateCheckService {

    @Override
    public Result<Void> checkDuplicate(String uniqueId, Boolean isRemoveDuplicate) {
        Result<Void> result = Result.success();
        try {
            DuplicateCheck duplicateCheck = new DuplicateCheck();
            duplicateCheck.setUniqueId(uniqueId);
            baseMapper.insert(duplicateCheck);
//            if(isRemoveDuplicate){
//                addSchedule(uniqueId);
//            }
        }catch (DuplicateKeyException e){
            return Results.error(ResponseEnum.ACCORE_100903);
        }catch (Exception e){
            Logger.error(result.getMessage(),e);
            return Results.error(ResponseEnum.ACCORE_100900);
        }
        return result;
    }
}
