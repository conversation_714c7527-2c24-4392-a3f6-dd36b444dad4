package com.akucun.account.proxy.service.acct.repository;

import com.akucun.account.proxy.dao.mapper.AdjustAccountMapper;
import com.akucun.account.proxy.dao.model.AdjustAccount;
import com.akucun.account.proxy.facade.stub.others.dto.req.AdjustAccountQueryReq;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mengxiang.base.common.log.Logger;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Objects;

@Repository
public class AdjustAccountRepository {

    @Resource
    private AdjustAccountMapper adjustAccountMapper;

    /**
     * 调账分页查询
     * @param adjustAccountQueryReq
     * @return
     */
    public Page<AdjustAccount> adjustAccountQuery(AdjustAccountQueryReq adjustAccountQueryReq){
        Logger.info("adjustAccountQueryReq:{}",adjustAccountQueryReq);

        Page<AdjustAccount> page =new Page<>(adjustAccountQueryReq.getPageNum(),adjustAccountQueryReq.getPageSize());
        LambdaQueryWrapper<AdjustAccount> wrapper = Wrappers.lambdaQuery();
        if(StringUtils.isNotBlank(adjustAccountQueryReq.getStatus())){
            wrapper.eq(AdjustAccount::getStatus,adjustAccountQueryReq.getStatus());
        }
        if(StringUtils.isNotBlank(adjustAccountQueryReq.getCustomerCode())){
            wrapper.eq(AdjustAccount::getCustomerCode,adjustAccountQueryReq.getCustomerCode());
        }
       if(Objects.nonNull(adjustAccountQueryReq.getStartTime())&&Objects.nonNull(adjustAccountQueryReq.getEndTime())){
           wrapper.between(AdjustAccount::getUpdateTime,adjustAccountQueryReq.getStartTime(),adjustAccountQueryReq.getEndTime());
       }
        wrapper.orderByDesc(AdjustAccount::getUpdateTime);

        Page<AdjustAccount> adjustAccountPage = adjustAccountMapper.selectPage(page, wrapper);
        return adjustAccountPage;
    }
}
