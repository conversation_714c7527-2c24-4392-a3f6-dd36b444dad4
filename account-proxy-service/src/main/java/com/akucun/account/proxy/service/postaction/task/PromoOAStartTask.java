package com.akucun.account.proxy.service.postaction.task;

import com.aikucun.common2.base.Result;
import com.aikucun.common2.log.Logger;
import com.akucun.account.proxy.common.constant.CommonConstants;
import com.akucun.account.proxy.common.enums.ErrorCodeConstants;
import com.akucun.account.proxy.common.enums.PostActionTypes;
import com.akucun.account.proxy.common.enums.ResponseEnum;
import com.akucun.account.proxy.common.utils.RedisLock;
import com.akucun.account.proxy.common.utils.Results;
import com.akucun.account.proxy.dao.model.MentorInfo;
import com.akucun.account.proxy.dao.model.PostActionItem;
import com.akucun.account.proxy.dao.model.RewardApply;
import com.akucun.account.proxy.facade.stub.others.oa.req.business.MultiSupplierCorporatePaymentRequest;
import com.akucun.account.proxy.service.acct.MentorInfoService;
import com.akucun.account.proxy.service.acct.RewardApplyService;
import com.akucun.account.proxy.service.enums.PromoTradeStatusEnum;
import com.akucun.account.proxy.service.oa.OAWorkflowBusinessService;
import com.akucun.account.proxy.service.postaction.common.AbsPostActionExecutor;
import com.akucun.fps.common.util.GsonUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : 营销-奖励发放-OA出款启动
 * @Create on : 2025/1/18 14:19
 **/
@Component
public class PromoOAStartTask extends AbsPostActionExecutor {
    @Autowired
    private RewardApplyService rewardApplyService;
    @Autowired
    private MentorInfoService mentorInfoService;
    @Autowired
    private OAWorkflowBusinessService oaWorkflowBusinessService;
    @Autowired
    private RedisTemplate redisTemplate;
    @Value("${promo.oa.biz.category:导师激励}")
    private String BIZ_CATEGORY;
    @Value("${promo.oa.inner.notifyUrl:http://zuul.infra.akcstable.com/account-proxy/api/account/proxy/promo/oa/workflow/notify}")
    private String innerNotifyUrl;

    @XxlJob("PromoOAStartTask")
    public ReturnT<String> execute(String param) {
        // 执行入口：模版方法提供的通用任务查询和执行
        return this.executeEntrance();
    }

    /**
     * 处理单个任务
     *
     * @param item
     * @return
     */
    @Override
    public Result<Void> execute(PostActionItem item) {
        Result<Void> result = Result.success();

        String lockKey = null;
        RedisLock lock = null;
        try {
            //01-序列化对象
            RewardApply record = GsonUtils.getInstance().fromJson(item.getParam(),RewardApply.class);

            //02-double check
            List<RewardApply> existRecords = rewardApplyService.queryRewardApplyList(record.getRequestNo(), record.getBusiType(), record.getTransBillDate(), record.getActivityNo());
            RewardApply rewardApplyRecord = existRecords.stream().filter(recordTmp-> !StringUtils.isEmpty(recordTmp.getExt1()) && !StringUtils.isEmpty(recordTmp.getExt2()) ).findFirst().orElse(null);
            if(rewardApplyRecord == null || rewardApplyRecord.getId().longValue()!=record.getId().longValue()){
                Logger.error("OA奖励金下发-启动出款任务时，数据异常：{}", GsonUtils.getInstance().toJson(record));
                return Result.error(ErrorCodeConstants.ACCORE_101900.getErrorCode(),String.format("OA奖励金下发-启动出款任务时，数据异常:%s VS %s",record.getId(),(rewardApplyRecord==null)?null:rewardApplyRecord.getId()));
            }

            //03-发起OA申请
            lockKey = String.format(CommonConstants.PROMO_OAPAY__LOCK_PREFIX, record.getActivityNo());
            lock = new RedisLock(redisTemplate, lockKey);
            if (!lock.tryLock()) {
                return Results.error(ResponseEnum.TRANSFER_TOO_OFTEN);
            }
            //03-01:构建申请数据
            List<MentorInfo> mentorInfos = mentorInfoService.loadAll();
            Map<String,MentorInfo> mentorInfoMap =mentorInfos.stream().collect(Collectors.toMap(MentorInfo::getUserCode, v->v, (v1, v2) -> v1));
            MultiSupplierCorporatePaymentRequest oaRequest = buildOAPaymentRequest(rewardApplyRecord,existRecords,mentorInfoMap);

            //03-02:发起OA申请
            Logger.info("OA奖励金下发-启动出款任务时，发起OA申请：{}", JSON.toJSONString(oaRequest));
            Result<String> oaStartRst = oaWorkflowBusinessService.multiSupplierCorporatePayment(oaRequest);
            Logger.info("OA奖励金下发-启动出款任务时，发起OA申请结果：{}", JSON.toJSONString(oaStartRst));
            if(ObjectUtils.isEmpty(oaStartRst) || !oaStartRst.getSuccess()){
                //Logger.error("OA奖励金下发-启动出款任务时，发起OA申请失败：{}", GsonUtils.getInstance().toJson(record));
                return Results.error(oaStartRst.getCode(),oaStartRst.getMessage());
            }

            //03-03:更新记录状态
            String oaRequestId = oaStartRst.getData();
            RewardApply updateRecord = new RewardApply();
            updateRecord.setId(record.getId());
            updateRecord.setStatus(PromoTradeStatusEnum.PAYING.getCode());
            HashMap<String,String> ext2 = JSON.parseObject(record.getExt2(), new TypeReference<HashMap<String, String>>() {});
            ext2.put("oaRequestId",oaRequestId);
            updateRecord.setExt2(JSON.toJSONString(ext2));
            rewardApplyService.updatesStatusById(updateRecord);

        } catch (Exception e) {
            result.setSuccess(false);
            result.setMessage(e.getMessage());
            Logger.error("提现扣手续费失败金额更新任务失败", e);
        } finally {
            // 释放锁
            if(null!=lock) {
                lock.unlock();
            }
        }

        return result;
    }

    private MultiSupplierCorporatePaymentRequest buildOAPaymentRequest(RewardApply mainRecord ,List<RewardApply> subDetails,Map<String,MentorInfo> mentorInfoMap) {
        MultiSupplierCorporatePaymentRequest req = new MultiSupplierCorporatePaymentRequest();
        req.setBizNo(mainRecord.getBatchNo());
        req.setBizCategory(BIZ_CATEGORY);
        req.setContent(mainRecord.getRemark());
        req.setApplyTime(mainRecord.getTradeTime());

        HashMap<String,String> ext2 = JSON.parseObject(mainRecord.getExt2(), new TypeReference<HashMap<String, String>>() {});
        req.setAmount(new BigDecimal(ext2.get("totalAmount")));
        req.setTaxAmount(new BigDecimal(ext2.get("totalTaxAmount")));
        req.setApplyUserNo(ext2.get("applyUserNo"));
        req.setApplyUserName(ext2.get("applyUserName"));

        req.setAttachment(JSON.parseObject(mainRecord.getAttachmentPaths(), new TypeReference<List<String>>() {}));

        req.setNotifyUrl(innerNotifyUrl);

        //明细
        req.setPaymentItems(subDetails.stream().map(record -> {
            MultiSupplierCorporatePaymentRequest.PaymentItem item = new MultiSupplierCorporatePaymentRequest.PaymentItem();
            item.setAmount(record.getTradeAmt());
            item.setDescription(String.format("导师%s下发奖励金%s元(含税%s元)",mentorInfoMap.get(record.getUserCode()).getUserNickName(),record.getTradeAmt(),record.getTaxAmt()));
            item.setSupplierId(mentorInfoMap.get(record.getUserCode()).getMerchantId());
            return item;
        }).collect(Collectors.toList()));

        return req;
    }

    /**
     * 当前job处理的任务类型
     *
     * @return
     */
    @Override
    protected String getActionType() {
        return PostActionTypes.PROMO_REWARD_START.getName();
    }
}
