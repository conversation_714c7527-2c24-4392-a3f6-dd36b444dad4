/*
package com.akucun.account.proxy.service.postaction.task;

import com.aikucun.common2.base.Result;
import com.aikucun.common2.log.Logger;
import com.akucun.account.proxy.common.enums.ErrorCodeConstants;
import com.akucun.account.proxy.common.enums.PostActionTypes;
import com.akucun.account.proxy.common.utils.HttpClient;
import com.akucun.account.proxy.common.utils.RedisLock;
import com.akucun.account.proxy.dao.model.PostActionItem;
import com.akucun.account.proxy.dao.model.RewardApply;
import com.akucun.account.proxy.facade.stub.others.dto.req.BonusPayQueryReq;
import com.akucun.account.proxy.facade.stub.others.dto.res.OANotifyDTO;
import com.akucun.account.proxy.service.acct.MentorInfoService;
import com.akucun.account.proxy.service.acct.PromoTradeService;
import com.akucun.account.proxy.service.acct.RewardApplyService;
import com.akucun.account.proxy.service.postaction.common.AbsPostActionExecutor;
import com.akucun.fps.common.util.GsonUtils;
import com.alibaba.fastjson.JSON;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;

*/
/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : 营销-奖励发放-OA最新状态通知
 * @Create on : 2025/1/18 01:44
 **//*

@Component
public class PromoOANotifyTask extends AbsPostActionExecutor {
    @Autowired
    private RewardApplyService rewardApplyService;
    @Autowired
    private MentorInfoService mentorInfoService;
    @Autowired
    private PromoTradeService promoTradeService;

    @Value("${promo.oa.outer.notifyUrl:http://zuul.infra.akcstable.com/promo-app-center/mentor/oa/callback}")
    private String innerNotifyUrl;

    @XxlJob("PromoOANotifyTask")
    public ReturnT<String> execute(String param) {
        // 执行入口：模版方法提供的通用任务查询和执行
        return this.executeEntrance();
    }

    */
/**
     * 处理单个任务
     *
     * @param item
     * @return
     *//*

    @Override
    public Result<Void> execute(PostActionItem item) {
        Result<Void> result = Result.success();

        try {
            //01-序列化对象
            RewardApply record = GsonUtils.getInstance().fromJson(item.getParam(), RewardApply.class);

            //02-double check
            List<RewardApply> existRecords = rewardApplyService.queryRewardApplyList(record.getRequestNo(), record.getBusiType(), record.getTransBillDate(), record.getActivityNo());
            RewardApply rewardApplyRecord = existRecords.stream().filter(recordTmp-> !StringUtils.isEmpty(recordTmp.getExt1()) && !StringUtils.isEmpty(recordTmp.getExt2()) ).findFirst().orElse(null);
            if(rewardApplyRecord == null || rewardApplyRecord.getId().longValue()!=record.getId().longValue()){
                Logger.error("营销-奖励发放-OA最新状态通知外部结果，数据异常：{}", GsonUtils.getInstance().toJson(record));
                return Result.error(ErrorCodeConstants.ACCORE_101900.getErrorCode(),String.format("营销-奖励发放-OA最新状态通知外部结果，数据异常:%s VS %s",record.getId(),(rewardApplyRecord==null)?null:rewardApplyRecord.getId()));
            }

            //03-异步通知
            OANotifyDTO notifyDTO = promoTradeService.buildOANotifyDTO(rewardApplyRecord);
            Logger.info("营销-奖励发放-OA最新状态通知外部开始：{}", JSON.toJSONString(notifyDTO));
            String notifyRslt = HttpClient.doPost(innerNotifyUrl, JSON.toJSONString(Arrays.asList(notifyDTO)));
            Logger.info("营销-奖励发放-OA最新状态通知外部结果：{}", JSON.toJSONString(notifyRslt));
        }catch (Exception e){
            result.setSuccess(false);
            result.setMessage(e.getMessage());
            Logger.error("营销-奖励发放-OA最新状态通知外部失败", e);
        }

        return result;
    }

    @Override
    protected String getActionType() {
        return PostActionTypes.PROMO_REWARD_NOTIFY.getName();
    }
}
*/
