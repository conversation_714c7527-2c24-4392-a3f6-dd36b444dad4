package com.akucun.account.proxy.service.postaction.task;

import com.aikucun.common2.base.Result;
import com.aikucun.common2.utils.datamasking.DataMask;
import com.akucun.account.proxy.common.enums.PostActionTypes;
import com.akucun.account.proxy.dao.model.PostActionItem;
import com.akucun.account.proxy.service.postaction.common.AbsPostActionExecutor;
import com.akucun.fps.common.util.GsonUtils;
import com.akucun.fps.pingan.client.vo.RevokeCreditVO;
import com.akucun.fps.pingan.feign.api.assets.AssetsServiceApi;
import com.alibaba.fastjson.JSON;
import com.mengxiang.base.common.log.Logger;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @Author: mapenghui
 * @Date: 2022/10/09
 * @desc: 平安分账撤销定时任务
 */
@Component
public class PinganRevokeCreditTask extends AbsPostActionExecutor {

    @Value("${pingan.revoke_credit.max.retry.times:30}")
    private int maxRetryTimes;

    @Resource
    private AssetsServiceApi assetsServiceApi;

    @XxlJob("pinganRevokeCreditTask")
    public ReturnT<String> execute(String param) {
        return this.executeEntrance();
    }

    @Override
    public int getMaxRetryTimes() {
        return maxRetryTimes;
    }

    @Override
    protected String getActionType() {
        return PostActionTypes.PINGAN_REVOKE_CREDIT.getName();
    }

    @Override
    public Result<Void> execute(PostActionItem item) {
        Result<Void> result = Result.success();
        RevokeCreditVO creditVO = null;
        try {
            creditVO = GsonUtils.getInstance().fromJson(item.getParam(), RevokeCreditVO.class);
            Logger.info("PinganRevokeCreditTask execute，请求接口 : {}", DataMask.toJSONString(creditVO));
            com.akucun.fps.common.entity.Result<String> revokeResult = assetsServiceApi.revokeCredit(creditVO);
            Logger.info("PinganRevokeCreditTask execute，请求接口 : {}，接口返回：{}", DataMask.toJSONString(creditVO), DataMask.toJSONString(revokeResult));
            result.setSuccess(revokeResult.isSuccess());
        } catch (Exception e) {
            Logger.error("PinganRevokeCreditTask execute异常，请求接口 : {}", DataMask.toJSONString(creditVO), e);
            return Result.error(e);
        }
        return result;
    }

}
