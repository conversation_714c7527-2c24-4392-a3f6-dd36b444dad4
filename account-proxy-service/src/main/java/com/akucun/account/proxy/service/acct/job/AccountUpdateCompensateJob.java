package com.akucun.account.proxy.service.acct.job;

import com.aikucun.common2.utils.datamasking.DataMask;
import com.akucun.account.proxy.common.utils.AccountUtils;
import com.akucun.account.proxy.dao.model.AccountOpTrade;
import com.akucun.account.proxy.dao.model.AccountOpTransferAmount;
import com.akucun.account.proxy.service.acct.AccountOpTradeService;
import com.akucun.account.proxy.service.acct.AccountOpTransferAmountService;
import com.akucun.account.proxy.service.acct.AccountService;
import com.akucun.account.proxy.service.acct.bo.AccountOpTradeBO;
import com.akucun.account.proxy.service.acct.bo.AccountOpTradeDetailBO;
import com.akucun.account.proxy.service.acct.bo.AccountOpTradeResp;
import com.akucun.account.proxy.service.acct.repository.AccountOpTradeRepository;
import com.akucun.cloud.security.CodeUtils;
import com.akucun.fps.pingan.client.api.expose.MerchantService;
import com.akucun.fps.pingan.client.model.po.PinganAccount;
import com.akucun.fps.pingan.client.vo.AccountRegisterVO;
import com.akucun.fps.pingan.feign.api.merchant.MerchantServiceApi;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mengxiang.base.common.log.Logger;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: silei
 * @Date: 2020/11/2
 * @desc: 人工补偿定时任务
 */
@Component
public class AccountUpdateCompensateJob {

    @Resource
    private AccountOpTradeRepository accountOpTradeRepository;
    @Resource
    private AccountOpTradeService accountOpTradeService;
    @Autowired
    private AccountService accountService;
    @Autowired
    private AccountOpTransferAmountService amountService;
    @Resource
    private MerchantServiceApi merchantServiceApi;

    @XxlJob("accountUpdateCompensateJob")
    public ReturnT<String> execute(String param) {
        Logger.info("AccountUpdateCompensateJob param:{}", param);
        //1,1,2,S,F,S
        //标识(1.推进，2.状态修改，3.解锁，4.归集金额),主交易ID,明细交易ID,交易状态,归集状态,划归状态
        if (StringUtils.isBlank(param)) {
            return ReturnT.FAIL;
        }
        String[] arr = param.split(",");
        if ("1".equals(arr[0])) {
            //人工补偿订单
            AccountOpTrade trade = accountOpTradeRepository.queryOpTradeById(Long.parseLong(arr[1]));
            AccountOpTradeBO bo = new AccountOpTradeBO();
            BeanUtils.copyProperties(trade, bo);
            AccountOpTradeResp resp = accountOpTradeService.execute(bo, true);
            Logger.info("AccountUpdateCompensateJob AccountOpTradeResp:{}", DataMask.toJSONString(resp));
            if (resp != null && AccountUtils.isFinalStatus(resp.getStatus())) {
                //解锁账户
                accountService.unlockAccount(AccountUtils.getSellerCode(trade.getCustomerCode(), trade.getCustomerType()), trade.getCustomerType());
            }
        } else if ("2".equals(arr[0])) {
            //人工修改状态
            AccountOpTradeDetailBO detailBO = new AccountOpTradeDetailBO();
            detailBO.setId(Long.parseLong(arr[2]));
            detailBO.setStatus(arr[3]);
            detailBO.setReplyMsg("人工修改状态");
            accountOpTradeRepository.updateAccountOpTradeDetailStatus(detailBO);
        } else if ("3".equals(arr[0])) {
            AccountOpTrade trade = accountOpTradeRepository.queryOpTradeById(Long.parseLong(arr[1]));
            //解锁账户
            accountService.unlockAccount(AccountUtils.getSellerCode(trade.getCustomerCode(), trade.getCustomerType()), trade.getCustomerType());
        } else if ("4".equals(arr[0])) {
            //归集状态
            if (StringUtils.isNotBlank(arr[3])) {
                amountService.getBaseMapper().update(AccountOpTransferAmount.builder().collectStatus(arr[3]).build(),
                        new LambdaQueryWrapper<AccountOpTransferAmount>()
                                .eq(AccountOpTransferAmount::getAccountTradeId, arr[1])
                                .eq(AccountOpTransferAmount::getDetailOrderNo, arr[2]));
            }
            //划归状态
            if (StringUtils.isNotBlank(arr[4])) {
                amountService.getBaseMapper().update(AccountOpTransferAmount.builder().allocateStatus(arr[4]).build(),
                        new LambdaQueryWrapper<AccountOpTransferAmount>()
                                .eq(AccountOpTransferAmount::getAccountTradeId, arr[1])
                                .eq(AccountOpTransferAmount::getDetailOrderNo, arr[2]));
            }
        }
        //注销平安账户
        else if ("5".equals(arr[0])) {
            AccountRegisterVO vo = new AccountRegisterVO();
            vo.setCustomerCode(arr[1]);
            vo.setCustomerType(arr[2]);
            merchantServiceApi.directCancelPinganAcct(vo);
        }
        //本地账户处理
        else if ("6".equals(arr[0])) {
            PinganAccount account = new PinganAccount();
            account.setCustomerCode(arr[1]);
            account.setCustomerType(arr[2]);
            //账户禁用
            account.setStatus(1);
            merchantServiceApi.updatePinganAccount(account);
        }
        //手机号加密处理
        else if ("7".equals(arr[0])) {
            long begin = Long.parseLong(arr[1]);
            long end = Long.parseLong(arr[2]);
            List<AccountOpTrade> list = accountOpTradeRepository.queryAccountOpTradeList(begin, end);
            list.forEach(trade -> {
                if (StringUtils.isNotEmpty(trade.getMobile()) && StringUtils.isNumeric(trade.getMobile())) {
                    trade.setMobile(CodeUtils.encrypt(trade.getMobile()).getData());
                    accountOpTradeRepository.updateAccountOpTrade(trade);
                }
            });
        }else if ("8".equals(arr[0])) {
            //人工变更主流程状态
            AccountOpTrade trade = accountOpTradeRepository.queryOpTradeById(Long.parseLong(arr[1]));
            if (trade != null){
                trade.setStatus(arr[2]);
                accountOpTradeRepository.updateAccountOpTrade(trade);
            }
        }


        Logger.info("AccountUpdateCompensateJob finished!");
        return ReturnT.SUCCESS;
    }


}
