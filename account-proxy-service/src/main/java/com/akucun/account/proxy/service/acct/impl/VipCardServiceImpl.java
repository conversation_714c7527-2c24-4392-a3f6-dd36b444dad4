package com.akucun.account.proxy.service.acct.impl;

import com.akucun.account.proxy.common.constant.CommonConstants;
import com.akucun.account.proxy.common.enums.ResponseEnum;
import com.akucun.account.proxy.common.enums.VipCardEnum;
import com.akucun.account.proxy.common.exception.AccountProxyException;
import com.akucun.account.proxy.common.utils.AESUtils;
import com.akucun.account.proxy.common.utils.RedisLock;
import com.akucun.account.proxy.dao.mapper.MshopVipCardMapper;
import com.akucun.account.proxy.dao.model.MshopVipCard;
import com.akucun.account.proxy.facade.stub.others.dto.req.VipCardInfoReq;
import com.akucun.account.proxy.facade.stub.others.dto.req.VipCardWriteOffReq;
import com.akucun.account.proxy.facade.stub.others.dto.res.VipCardInfoResp;
import com.akucun.account.proxy.service.acct.VipCardService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mengxiang.base.common.log.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/01/03 14:44
 */
@Service
public class VipCardServiceImpl  extends ServiceImpl<MshopVipCardMapper, MshopVipCard>  implements VipCardService {
    @Autowired
    private RedisTemplate redisTemplate;

    public static final String VIP_CARD_WRITEOFF_PREFIX = "account:proxy:vipcard:writeoff:%s";

    @Override
    public VipCardInfoResp vipCardValidInfo(VipCardInfoReq vipCardInfoReq) {
        String inviteCode = AESUtils.encrypt(vipCardInfoReq.getInviteCode(), CommonConstants.VIP_CARD_AES_KEY);
        MshopVipCard mshopVipCard = queryByCardNo(vipCardInfoReq.getCardNo(), inviteCode);
        // 校验vip卡
        checkVipCard(mshopVipCard);
        VipCardInfoResp result = new VipCardInfoResp();
        BeanUtils.copyProperties(mshopVipCard, result);
        // 解密卡密
        result.setInviteCode(AESUtils.decrypt(result.getInviteCode(), CommonConstants.VIP_CARD_AES_KEY));
        // 转化为元
        if(Objects.nonNull(result.getAmount())) {
            BigDecimal fmtBg = new BigDecimal(new BigDecimal(result.getAmount()).divide(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString());
            result.setAmountY(fmtBg);
        }
        return result;
    }

    @Override
    @Transactional
    public Boolean vipCardWriteOff(VipCardWriteOffReq vipCardWriteOffReq) {
        String inviteCode = AESUtils.encrypt(vipCardWriteOffReq.getInviteCode(), CommonConstants.VIP_CARD_AES_KEY);
        String lockKey = String.format(VIP_CARD_WRITEOFF_PREFIX, vipCardWriteOffReq.getInviteCode());
        // lock，按卡密以防重复提交
        RedisLock lock = new RedisLock(redisTemplate, lockKey);
        if (!lock.tryLock()) {
            throw new AccountProxyException(ResponseEnum.VIPCARD_WRITEOFF_TOO_OFEN);
        }
        try {
            MshopVipCard mshopVipCard = queryByCardNo(vipCardWriteOffReq.getCardNo(), inviteCode);
            // 校验vip卡
            checkVipCard(mshopVipCard);
            // 检验同一个爱豆只能绑定一张vip卡
//            List<MshopVipCard> mshopVipCards = queryByCustomerCode(vipCardWriteOffReq.getCustomerCode());
//            if(CollectionUtils.isNotEmpty(mshopVipCards)) {
//                throw new AccountProxyException(ResponseEnum.VIPCARD_SAME_CUSTOMER_BIND);
//            }
            mshopVipCard.setStatus(VipCardEnum.VipCardStatus.STATUS_20.getCode());
            mshopVipCard.setActivationTime(new Date());
            mshopVipCard.setCustomerCode(vipCardWriteOffReq.getCustomerCode());
            return this.baseMapper.updateById(mshopVipCard) > 0;
        } catch (Exception e) {
            Logger.error("vip卡核销异常，异常信息：{}", e);
            throw e;
        } finally {
            // 释放锁
            lock.unlock();
        }
    }

    private void checkVipCard(MshopVipCard mshopVipCard) {
        // 卡号记录不存在
        if(Objects.isNull(mshopVipCard)) {
            throw new AccountProxyException(ResponseEnum.VIPCARD_RECORD_NOT_EXISTS);
        }
        // 状态为待激活的才为有效状态
        if(!VipCardEnum.VipCardStatus.STATUS_10.getCode().equals(mshopVipCard.getStatus())) {
            throw new AccountProxyException(ResponseEnum.VIPCARD_STATUS_ACTIVATE);
        }
        // 是否在有效期内
        Date beginTime = mshopVipCard.getBeginTime();
        Date endTime = mshopVipCard.getEndTime();
        Date now = new Date();
        if((Objects.nonNull(beginTime) && beginTime.after(now))
                || (Objects.nonNull(endTime) && endTime.before(now))) {
            throw new AccountProxyException(ResponseEnum.VIPCARD_TIME_EXPIRED);
        }
    }

    private MshopVipCard queryByCardNo(String cardNo, String inviteCode) {
        LambdaQueryWrapper wrapper = new LambdaQueryWrapper<MshopVipCard>()
                .eq(MshopVipCard::getIsDelete, 0)
                .eq(MshopVipCard::getCardNo, cardNo)
                .eq(MshopVipCard::getInviteCode, inviteCode);
        MshopVipCard mshopVipCard = this.baseMapper.selectOne(wrapper);
        return mshopVipCard;
    }

    private List<MshopVipCard> queryByCustomerCode(String customerCode) {
        LambdaQueryWrapper wrapper = new LambdaQueryWrapper<MshopVipCard>()
                .eq(MshopVipCard::getIsDelete, 0)
                .eq(MshopVipCard::getCustomerCode, customerCode);
        List<MshopVipCard> mshopVipCards = this.baseMapper.selectList(wrapper);
        return mshopVipCards;
    }

}
