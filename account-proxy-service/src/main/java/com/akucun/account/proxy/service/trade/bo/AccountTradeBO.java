package com.akucun.account.proxy.service.trade.bo;

import com.mengxiang.base.common.utils.datamasking.Sensitive;
import com.mengxiang.base.common.utils.datamasking.SensitiveType;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: silei
 * @Date: 2020/11/30
 * @desc: 账户交易业务实体
 */
@Data
public class AccountTradeBO {
    /**
     * 主键
     */
    private Long id;

    /**
     * 订单来源系统编码
     */
    private String sourceCode;

    /**
     * 来源订单号
     */
    private String sourceNo;

    /**
     * 交易订单号
     */
    private String tradeNo;

    /**
     * 业务交易类型(账户中心交易类型)
     */
    private String bizTradeType;

    /**
     * 交易类型
     */
    private String tradeType;

    /**
     * 客户编码
     */
    private String customerCode;

    /**
     * 客户名称
     */
    @Sensitive(type = SensitiveType.Name)
    private String customerName;

    /**
     * 客户类型
     */
    private String customerType;

    /**
     * 交易金额（元）
     */
    private BigDecimal amount;

    /**
     * 交易状态(I初始化，S成功，F失败，P处理中)
     */
    private String status;

    /**
     * 返回码
     */
    private String replyCode;

    /**
     * 返回消息
     */
    private String replyMsg;

    /**
     * 扩展域（json格式）
     */
    private String ext;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 扩展域
     */
    private Map<String, String> extField = new HashMap<>();

    /**
     * 明细流程记录
     */
    private AccountTradeDetailBO accountTradeDetailBO;
}
