package com.akucun.account.proxy.service.postaction.task;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.common.enums.PostActionTypes;
import com.akucun.account.proxy.dao.model.PostActionItem;
import com.akucun.account.proxy.dao.model.WechatWithdrawSummary;
import com.akucun.account.proxy.service.acct.WechatWithdrawSummaryService;
import com.akucun.account.proxy.service.postaction.common.AbsPostActionExecutor;
import com.akucun.fps.common.util.GsonUtils;
import com.mengxiang.base.common.log.Logger;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 微信提现失败累计金额退回任务
 *
 * <AUTHOR>
 */
@Component
public class WechatWithdrawFailSumAmountTask extends AbsPostActionExecutor {

    @Autowired
    private WechatWithdrawSummaryService wechatWithdrawSummaryService;

    @XxlJob("wechatWithdrawFailSumAmountTask")
    public ReturnT<String> execute(String param) {
        return this.executeEntrance();
    }

    @Override
    protected String getActionType() {
        return PostActionTypes.WECHAT_WITHDRAW_FAIL_AMOUNT.getName();
    }

    @Override
    public Result<Void> execute(PostActionItem item) {
        Result<Void> result = Result.success();
        try {
            WechatWithdrawSummary summary = GsonUtils.getInstance().fromJson(item.getParam(), WechatWithdrawSummary.class);
            //扣手续费交易
            wechatWithdrawSummaryService.subtractAmount(summary.getId(), summary.getSumWithdrawAmt());
        } catch (Exception e) {
            result.setSuccess(false);
            result.setMessage(e.getMessage());
            Logger.error("微信提现失败累计金额退回失败", e);
        }
        return result;
    }
}
