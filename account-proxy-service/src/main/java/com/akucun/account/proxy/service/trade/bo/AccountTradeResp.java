package com.akucun.account.proxy.service.trade.bo;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: silei
 * @Date: 2020/12/2
 * @desc:
 */
@Data
public class AccountTradeResp {

    //请求流水号
    private String sourceNo;
    //交易状态
    private String status;
    //返回码
    private String replyCode;
    //返回消息
    private String replyMsg;
    //扩展信息
    private Map<String, String> extField = new HashMap<>();
}
