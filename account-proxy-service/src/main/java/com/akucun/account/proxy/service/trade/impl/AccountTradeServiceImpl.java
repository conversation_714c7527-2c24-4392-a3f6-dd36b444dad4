package com.akucun.account.proxy.service.trade.impl;

import com.aikucun.common2.utils.CollectionUtils;
import com.aikucun.common2.utils.DateUtils;
import com.aikucun.common2.utils.StringUtils;
import com.akucun.account.center.client.model.TradeInfo;
import com.akucun.account.proxy.client.account.AccountClient;
import com.akucun.account.proxy.common.constant.CommonConstants;
import com.akucun.account.proxy.common.enums.ResponseEnum;
import com.akucun.account.proxy.common.enums.ResultStatus;
import com.akucun.account.proxy.common.exception.AccountProxyException;
import com.akucun.account.proxy.dao.mapper.AccountTradeMapper;
import com.akucun.account.proxy.dao.model.AccountTrade;
import com.akucun.account.proxy.facade.stub.enums.TradeChannel;
import com.akucun.account.proxy.facade.stub.others.dto.req.AccountTradeRequest;
import com.akucun.account.proxy.facade.stub.others.dto.res.AccountTradeResponse;
import com.akucun.account.proxy.service.common.bo.AccountExecStepContext;
import com.akucun.account.proxy.service.common.bo.AccountResp;
import com.akucun.account.proxy.service.common.bo.AccountTradeReq;
import com.akucun.account.proxy.service.trade.AccountTradeExecuteService;
import com.akucun.account.proxy.service.trade.AccountTradeService;
import com.akucun.account.proxy.service.trade.bo.AccountCustomerRouteBO;
import com.akucun.account.proxy.service.trade.bo.AccountTradeBO;
import com.akucun.account.proxy.service.trade.bo.AccountTradeResp;
import com.akucun.account.proxy.service.trade.repository.AccountCustomerRouteRepository;
import com.akucun.account.proxy.service.trade.repository.AccountTradeRepository;
import com.akucun.common.Result;
import com.akucun.fps.common.util.StringUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2020/12/22 21:17
 */
@Service
public class AccountTradeServiceImpl extends ServiceImpl<AccountTradeMapper, AccountTrade> implements AccountTradeService {

    @Resource
    private AccountTradeExecuteService accountOpTradeService;
    @Autowired
    private AccountTradeRepository accountTradeRepository;
    @Autowired
    private AccountCustomerRouteRepository routeRepository;
    @Autowired
    private AccountClient accountClient;

    @Override
    public AccountTradeResp accountTrade(AccountTradeRequest request, String tradeType) {
        AccountTradeBO tradeBO = buildBonusAccountBO(request, tradeType);
        if(CommonConstants.BONUS_ACCOUNT_REFUND_TRADE_TYPE.equals(tradeType)
                || CommonConstants.AKC_ACCOUNT_REFUND_TRADE_TYPE.equals(tradeType)
                || CommonConstants.OPENAPI_ACCOUNT_REFUND_TRADE_TYPE.equals(tradeType)
                || CommonConstants.XD_ACCOUNT_REFUND_TRADE_TYPE.equals(tradeType)) {
            // 根据交易单号查询customerCode
            AccountCustomerRouteBO routeBO = routeRepository.query(request.getSourceNo());
            if(Objects.isNull(routeBO)) {
                throw new AccountProxyException(ResponseEnum.TRADE_ROUTE_NOT_EXIST);
            }
            String customerCode = routeBO.getCustomerCode();
            // 退款时查询支付交易单号
            AccountTradeBO queryTradeBO = new AccountTradeBO();
            if(CommonConstants.BONUS_ACCOUNT_REFUND_TRADE_TYPE.equals(tradeType)) {
                queryTradeBO.setTradeType(CommonConstants.BONUS_ACCOUNT_PAY_TRADE_TYPE);
            } else if(CommonConstants.AKC_ACCOUNT_REFUND_TRADE_TYPE.equals(tradeType)) {
                queryTradeBO.setTradeType(CommonConstants.AKC_ACCOUNT_PAY_TRADE_TYPE);
            } else if(CommonConstants.OPENAPI_ACCOUNT_REFUND_TRADE_TYPE.equals(tradeType)) {
                queryTradeBO.setTradeType(CommonConstants.OPENAPI_ACCOUNT_PAY_TRADE_TYPE);
            } else if(CommonConstants.XD_ACCOUNT_REFUND_TRADE_TYPE.equals(tradeType)) {
                queryTradeBO.setTradeType(CommonConstants.XD_ACCOUNT_PAY_TRADE_TYPE);
            } else {
                queryTradeBO.setTradeType(tradeType);
            }
            queryTradeBO.setTradeNo(tradeBO.getSourceNo());
            queryTradeBO.setCustomerCode(customerCode);
            AccountTradeBO accountTradeBO = accountTradeRepository.queryAcctTrade(queryTradeBO);
            if(Objects.isNull(accountTradeBO) || !ResultStatus.S.getCode().equals(accountTradeBO.getStatus())) {
                throw new AccountProxyException(ResponseEnum.TRADE_NOT_EXIST);
            }
            tradeBO.setCustomerCode(accountTradeBO.getCustomerCode());
            if(!StringUtils.isNullOrEmpty(tradeBO.getExt())) {
                Map<String, String> extField = (Map<String, String>)JSON.parse(accountTradeBO.getExt());
                extField.putAll(tradeBO.getExtField());
                tradeBO.setExtField(extField);
                tradeBO.setExt(JSON.toJSONString(extField));
            }
        }
        // 校验
        tradePreCheck(tradeBO);
        AccountTradeResp accountTradeResp = accountOpTradeService.executeTrade(tradeBO, false);
        return accountTradeResp;
    }

    @Override
    @Transactional
    public AccountTradeResponse bonusAccountQuery(String tradeNo, String tradeType) {
        // 根据交易单号查询customerCode
        AccountCustomerRouteBO routeBO = routeRepository.query(tradeNo);
        if(Objects.isNull(routeBO)) {
            throw new AccountProxyException(ResponseEnum.TRADE_ROUTE_NOT_EXIST);
        }
        String customerCode = routeBO.getCustomerCode();

        AccountTradeBO tradeBO = new AccountTradeBO();
        tradeBO.setTradeType(tradeType);
        tradeBO.setTradeNo(tradeNo);
        tradeBO.setCustomerCode(customerCode);
        AccountTradeBO accountTradeBO = accountTradeRepository.queryAcctTrade(tradeBO);
        if (Objects.isNull(accountTradeBO)) {
            throw new AccountProxyException(ResponseEnum.TRADE_NOT_EXIST);
        }
        String status = accountTradeBO.getStatus();
        if (ResultStatus.S.getCode().equals(status)) {
            return buildTradeRespSuccess(accountTradeBO.getAmount(), tradeNo);
        } else {
            // 查询账户交易记录
            String branch = accountClient.getBalanceBranch(accountTradeBO.getCustomerCode());
            boolean isSuccess = false;
            if (CommonConstants.BRANCH_MEMBER.equals(branch)) {
                isSuccess = accountClient.findAccountList(tradeNo, accountTradeBO.getAmount());
            } else if (CommonConstants.BRANCH_ACCOUNT_CENTER.equals(branch)) {
                isSuccess = accountClient.queryAccountDetailList(accountTradeBO.getCustomerCode(), tradeNo, accountTradeBO.getAmount(), CommonConstants.NEW_BALANCE_CUSTOMER_KEY);
            }
            return buildAndSaveResp(accountTradeBO.getAmount(), tradeNo, isSuccess, tradeType, customerCode);
        }
    }

    @Override
    @Transactional
    public AccountTradeResponse akcAccountQuery(String tradeNo, String tradeType) {
        // 根据交易单号查询customerCode
        AccountCustomerRouteBO routeBO = routeRepository.query(tradeNo);
        if(Objects.isNull(routeBO)) {
            throw new AccountProxyException(ResponseEnum.TRADE_ROUTE_NOT_EXIST);
        }
        String customerCode = routeBO.getCustomerCode();

        AccountTradeBO tradeBO = new AccountTradeBO();
        tradeBO.setTradeType(tradeType);
        tradeBO.setTradeNo(tradeNo);
        tradeBO.setCustomerCode(customerCode);
        AccountTradeBO accountTradeBO = accountTradeRepository.queryAcctTrade(tradeBO);
        if (Objects.isNull(accountTradeBO)) {
            throw new AccountProxyException(ResponseEnum.TRADE_NOT_EXIST);
        }
        String status = accountTradeBO.getStatus();
        if (ResultStatus.S.getCode().equals(status)) {
            return buildTradeRespSuccess(accountTradeBO.getAmount(), tradeNo);
        } else {
            String channle = accountTradeBO.getExtField().get(CommonConstants.TRADE_CHANNEL_NAME);
            String accountKey = null;
            if (TradeChannel.OPENAPI.getCode().equals(channle)) {
                accountKey = CommonConstants.OPENAPI_BALANCE_CUSTOMER_KEY;
            } else if (TradeChannel.APP.getCode().equals(channle)) {
                accountKey = CommonConstants.CASH_WITHDRAWAL_BALANCE_CUSTOMER_KEY;
            }
            if (StringUtils.isNullOrEmpty(accountKey)) {
                throw new AccountProxyException(ResponseEnum.TRADE_NOT_CONFIG);
            }
            // 查询账户交易记录
            boolean isSuccess = accountClient.queryAccountDetailList(accountTradeBO.getCustomerCode(), tradeNo, accountTradeBO.getAmount(), accountKey);
            return buildAndSaveResp(accountTradeBO.getAmount(), tradeNo, isSuccess, tradeType, customerCode);
        }
    }

    @Override
    @Transactional
    public AccountTradeResponse openApiAccountQuery(String tradeNo, String tradeType) {
        // 根据交易单号查询customerCode
        AccountCustomerRouteBO routeBO = routeRepository.query(tradeNo);
        if(Objects.isNull(routeBO)) {
            throw new AccountProxyException(ResponseEnum.TRADE_ROUTE_NOT_EXIST);
        }
        String customerCode = routeBO.getCustomerCode();

        AccountTradeBO tradeBO = new AccountTradeBO();
        tradeBO.setTradeType(tradeType);
        tradeBO.setTradeNo(tradeNo);
        tradeBO.setCustomerCode(customerCode);
        AccountTradeBO accountTradeBO = accountTradeRepository.queryAcctTrade(tradeBO);
        if (Objects.isNull(accountTradeBO)) {
            throw new AccountProxyException(ResponseEnum.TRADE_NOT_EXIST);
        }
        String status = accountTradeBO.getStatus();
        if (ResultStatus.S.getCode().equals(status)) {
            return buildTradeRespSuccess(accountTradeBO.getAmount(), tradeNo);
        } else {
            String channle = accountTradeBO.getExtField().get(CommonConstants.TRADE_CHANNEL_NAME);
            String accountKey = null;
            if (TradeChannel.OPENAPI.getCode().equals(channle)) {
                accountKey = CommonConstants.OPENAPI_CREDIT_CUSTOMER_KEY;
            }
            if (StringUtils.isNullOrEmpty(accountKey)) {
                throw new AccountProxyException(ResponseEnum.TRADE_NOT_CONFIG);
            }
            // 查询账户交易记录
            boolean isSuccess = accountClient.queryAccountDetailList(accountTradeBO.getCustomerCode(), tradeNo, accountTradeBO.getAmount(), accountKey);
            return buildAndSaveResp(accountTradeBO.getAmount(), tradeNo, isSuccess, tradeType, customerCode);
        }
    }

    @Override
    public AccountTradeResponse xdAccountQuery(String tradeNo, String tradeType) {
        return commonQuery(tradeNo, tradeType);
    }

    @Override
    public AccountTradeResponse pointAccountQuery(String tradeNo, String tradeType) {
        return commonQuery(tradeNo, tradeType);
    }

    private AccountTradeResponse commonQuery(String tradeNo, String tradeType) {
        // 根据交易单号查询customerCode
        AccountCustomerRouteBO routeBO = routeRepository.query(tradeNo);
        if(Objects.isNull(routeBO)) {
            throw new AccountProxyException(ResponseEnum.TRADE_ROUTE_NOT_EXIST);
        }
        String customerCode = routeBO.getCustomerCode();

        AccountTradeBO tradeBO = new AccountTradeBO();
        tradeBO.setTradeType(tradeType);
        tradeBO.setTradeNo(tradeNo);
        tradeBO.setCustomerCode(customerCode);
        AccountTradeBO accountTradeBO = accountTradeRepository.queryAcctTrade(tradeBO);
        if (Objects.isNull(accountTradeBO)) {
            throw new AccountProxyException(ResponseEnum.TRADE_NOT_EXIST);
        }
        String status = accountTradeBO.getStatus();
        if (ResultStatus.S.getCode().equals(status)) {
            return buildTradeRespSuccess(accountTradeBO.getAmount(), tradeNo);
        } else {
            AccountTradeResponse response = new AccountTradeResponse();
            response.setStatus(status);
            response.setSuccessTime(DateUtils.format(new Date()));
            response.setAmount(accountTradeBO.getAmount());
            response.setTradeNo(tradeNo);
            return response;
        }
    }

    private void tradePreCheck(AccountTradeBO tradeBO) {
        if(StringUtils.isNullOrEmpty(tradeBO.getCustomerCode())
                || CollectionUtils.isEmpty(tradeBO.getExtField())
                || StringUtils.isNullOrEmpty(tradeBO.getExt())) {
            throw new AccountProxyException(ResponseEnum.PARAM_EXCEPTION);
        }
    }

    private AccountTradeBO buildBonusAccountBO(AccountTradeRequest request, String tradeType) {
        AccountTradeBO tradeBO = new AccountTradeBO();
        tradeBO.setSourceCode(request.getSourceCode());
        tradeBO.setSourceNo(request.getSourceNo());
        tradeBO.setTradeNo(request.getTradeNo());
        tradeBO.setTradeType(tradeType);
        tradeBO.setCustomerCode(request.getCustomerCode());
        tradeBO.setAmount(request.getAmount());
        tradeBO.setRemark(request.getRemark());
        Map<String, String> extField = new HashMap<>();
        if(!StringUtils.isNullOrEmpty(request.getChannel())) {
            extField.put(CommonConstants.TRADE_CHANNEL_NAME, request.getChannel());
        }
        if(!StringUtils.isNullOrEmpty(request.getUserId())) {
            extField.put(CommonConstants.TRADE_USERID, request.getUserId());
        }
        if(!StringUtils.isNullOrEmpty(request.getBizRefundNo())) {
            extField.put(CommonConstants.BIZ_REFUND_NO, request.getBizRefundNo());
        }
        if(!StringUtils.isNullOrEmpty(request.getUserRole())) {
            extField.put(CommonConstants.USER_ROLE, request.getUserRole());
        }
        if(!StringUtils.isNullOrEmpty(request.getTenantId())) {
            extField.put(CommonConstants.TENANT_ID, request.getTenantId());
        }
        tradeBO.setExtField(extField);
        tradeBO.setExt(JSON.toJSONString(extField));
        return tradeBO;
    }

    private AccountTradeResponse buildTradeRespSuccess(BigDecimal amount, String tradeNo) {
        AccountTradeResponse response = new AccountTradeResponse();
        response.setStatus(ResultStatus.S.getCode());
        response.setSuccessTime(DateUtils.format(new Date()));
        response.setAmount(amount);
        response.setTradeNo(tradeNo);
        return response;
    }

    private AccountTradeResponse buildAndSaveResp(BigDecimal amount, String tradeNo, Boolean isSuccess, String tradeType, String customerCode) {
        AccountTradeResponse response = new AccountTradeResponse();
        response.setSuccessTime(DateUtils.format(new Date()));
        response.setAmount(amount);
        response.setTradeNo(tradeNo);
        if (isSuccess) {
            LambdaQueryWrapper wrapper = new LambdaQueryWrapper<AccountTrade>()
                            .eq(AccountTrade::getTradeNo, tradeNo)
                            .eq(AccountTrade::getTradeType, tradeType)
                            .eq(AccountTrade::getCustomerCode, customerCode);
            // 更新为成功
            AccountTrade accountTrade = AccountTrade.builder().status(ResultStatus.S.getCode()).build();
            this.update(accountTrade, wrapper);
            response.setStatus(ResultStatus.S.getCode());
        } else {
            response.setStatus(ResultStatus.P.getCode());
        }
        return response;
    }

}
