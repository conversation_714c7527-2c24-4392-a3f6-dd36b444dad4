package com.akucun.account.proxy.service.help;

import com.aikucun.common2.base.exception.BusinessException;
import com.akucun.account.proxy.common.enums.TenantTypeEnum;
import com.alibaba.fastjson.JSON;
import com.mengxiang.base.common.log.Logger;
import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.saas.service.facade.common.enums.WechatComponentEnum;
import com.mengxiang.saas.service.facade.common.feign.tenant.TenantFeign;
import com.mengxiang.saas.service.facade.common.response.tenant.TenantResp;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;

@Service
public class TenantCoreHelper {

    static final String  COMPONENT = "supportTaxation";
    @Resource
    private TenantFeign tenantFeign;
    public Pair<Boolean, Integer> isCompanyShop(Long tenantId) throws BusinessException {
        Result<TenantResp> tenantRespResult = tenantFeign.queryByTenantId(tenantId);
        if(tenantRespResult==null || !tenantRespResult.isSuccess()){
            Logger.error("MEMBER_RELATE_UNITY_REGISTER_TOPIC【MEMBER_RELATE_UNITY_DISTRIBUTOR_REGISTER_GROUP】查询租户类型系统异常，result={}", JSON.toJSONString(tenantRespResult));
            throw new BusinessException("系统异常：调用会员租户中心（tenant-core）查询租户信息异常");
        }
        TenantResp tenantResp=tenantRespResult.getData();
        if(tenantResp==null || tenantResp.getTenantType()==null){
            Logger.error("MEMBER_RELATE_UNITY_REGISTER_TOPIC【MEMBER_RELATE_UNITY_DISTRIBUTOR_REGISTER_GROUP】租户信息/类型信息不存在，result={}",JSON.toJSONString(tenantRespResult));
            throw new BusinessException("系统异常：调用会员租户中心（tenant-core）查询租户信息-租户信息/类型信息不存在");
        }
        if (TenantTypeEnum.tenantType2.getValue().equals(tenantResp.getTenantType())
                || TenantTypeEnum.tenantType4.getValue().equals(tenantResp.getTenantType())) {
            Logger.info("MEMBER_RELATE_UNITY_REGISTER_TOPIC【MEMBER_RELATE_UNITY_DISTRIBUTOR_REGISTER_GROUP】,饷店注册忽略，目前只支持企业饷店,{}",tenantId);
            return Pair.of(Boolean.TRUE, tenantResp.getTenantType());
        }

        /**
         * tenantType = 6 -> SUPPLY  供应链-经销      -> 需要开保证金帐户
         * tentantType = 7 and enablePayApplets = 1 ->  SUPPLY_CONSIGN 供应链-代销代收  -> 需要开佣金帐户     变更为   tenantType = 8 -> OPEN_SUPPLY_CONSIGNMENT_COLLECTING 供应链-代销代收
         * tentantType = 7 and enablePayApplets = 0 -> SUPPLY_CONSIGN 供应链-代销账扣   -> 需要开保证金帐户    变更为   tenantType = 7 -> OPEN_SUPPLY_CONSIGNMENT_WITHHOLD 供应链-代销账扣
         * tenantType = 9 -> OPEN_SUPPLY_CONSIGNMENT_SHARING 供应链-代销分账 -> 需要开保证金帐户
         */
        if (TenantTypeEnum.OPEN_SUPPLY_CONSIGNMENT_COLLECTING.getValue().equals(tenantResp.getTenantType())) {
            return Pair.of(Boolean.TRUE, tenantResp.getTenantType());
        }
        return Pair.of(Boolean.FALSE, tenantResp.getTenantType());
    }

    public Integer queryTenantPayComponentValue(Long tenantId) throws BusinessException {
        Result<Integer> queryTenantComponentValueResult = tenantFeign.getComponentValueByTenantId(tenantId, WechatComponentEnum.ENABLE_PAY_APPLETS.getCode());
        if (queryTenantComponentValueResult == null || !queryTenantComponentValueResult.isSuccess()) {
            throw new BusinessException("系统异常：调用会员租户中心（tenant-core）查询租户组件信息异常");
        }
        return queryTenantComponentValueResult.getData();
    }

    /**
     * 是否为平台Saas租户
     * -- 企业饷店的平台租户，支持乐税纳税，非平台的企业自行纳税
     * @param tenantId
     * @return
     */
    public Boolean isPlatformSaasTenant(Long tenantId){
        Result<Object> checkPlatformSaasTenantResult = tenantFeign.queryComponentValue(tenantId,COMPONENT);
        Logger.info("租户是否为平台Saas租户,租户ID:{},结果:{}",tenantId,JSON.toJSONString(checkPlatformSaasTenantResult));
        if(!ObjectUtils.isEmpty(checkPlatformSaasTenantResult) && checkPlatformSaasTenantResult.isSuccess()){
            if(!ObjectUtils.isEmpty(checkPlatformSaasTenantResult.getData()) && checkPlatformSaasTenantResult.getData().toString().equals("1")){
                Logger.info("租户是否为平台Saas租户,租户ID:{}:平台租户",tenantId,JSON.toJSONString(checkPlatformSaasTenantResult));
                return true;
            }
            return false;
        }
        return false;
    }
}
