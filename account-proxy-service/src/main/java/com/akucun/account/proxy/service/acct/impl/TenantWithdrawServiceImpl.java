package com.akucun.account.proxy.service.acct.impl;

import com.aikucun.common2.base.Result;
import com.aikucun.common2.base.exception.BusinessException;
import com.aikucun.common2.utils.DateUtils;
import com.aikucun.common2.utils.datamasking.DataMask;
import com.akucun.account.center.client.model.TradeInfo;
import com.akucun.account.center.feign.AccountCenterService;
import com.akucun.account.proxy.common.constant.ApplyStatus;
import com.akucun.account.proxy.common.constant.CommonConstants;
import com.akucun.account.proxy.common.enums.*;
import com.akucun.account.proxy.common.exception.AccountProxyException;
import com.akucun.account.proxy.common.utils.AccountNoUtils;
import com.akucun.account.proxy.common.utils.AccountUtils;
import com.akucun.account.proxy.common.utils.RedisLock;
import com.akucun.account.proxy.common.utils.Results;
import com.akucun.account.proxy.dao.mapper.TenantWithdrawApplyMapper;
import com.akucun.account.proxy.dao.mapper.WithdrawServicefeeSummaryMapper;
import com.akucun.account.proxy.dao.model.AccountTenantCustomer;
import com.akucun.account.proxy.dao.model.TenantWithdrawApply;
import com.akucun.account.proxy.dao.model.WithdrawApplyRecord;
import com.akucun.account.proxy.dao.model.WithdrawServicefeeSummary;
import com.akucun.account.proxy.dao.model.WithdrawTaxDetail;
import com.akucun.account.proxy.facade.stub.enums.AccountKeyConstants;
import com.akucun.account.proxy.facade.stub.enums.CustomerType;
import com.akucun.account.proxy.facade.stub.enums.DetailTypeConstants;
import com.akucun.account.proxy.facade.stub.others.account.vo.AccountWithdrawVO;
import com.akucun.account.proxy.facade.stub.others.aggregation.req.TenantWithdrawQueryReq;
import com.akucun.account.proxy.facade.stub.others.aggregation.vo.TenantWithdrawApplyVO;
import com.akucun.account.proxy.service.acct.TenantWithdrawService;
import com.akucun.account.proxy.service.acct.WithdrawTaxService;
import com.akucun.account.proxy.service.acct.bo.WithdrawThresholdCheckBO;
import com.akucun.account.proxy.service.config.DecouplingConfig;
import com.akucun.account.proxy.service.help.TenantCoreHelper;
import com.akucun.account.proxy.service.postaction.PostActionService;
import com.akucun.account.proxy.service.postaction.bo.PostActionItemBO;
import com.akucun.account.proxy.service.transfer.convert.FinTaskAcceptRequestConvert;
import com.akucun.common.util.MD5Utils;
import com.akucun.fps.common.entity.ResultList;
import com.akucun.fps.pingan.client.constants.BankCardStatusConstants;
import com.akucun.fps.pingan.client.constants.CustomerNatureType;
import com.akucun.fps.pingan.client.model.query.BindCardQueryReq;
import com.akucun.fps.pingan.client.vo.PinganCardVO;
import com.akucun.fps.pingan.client.vo.WithdrawVO;
import com.akucun.fps.pingan.feign.api.merchantquery.MerchantQueryServiceApi;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mengxiang.base.common.log.Logger;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @Author: silei
 * @Date: 2021/4/21
 * @desc:
 */
@Service
public class TenantWithdrawServiceImpl implements TenantWithdrawService {

    @Resource
    private AccountCenterService accountCenterService;
    @Resource
    private MerchantQueryServiceApi merchantQueryServiceApi;
    @Resource
    private PostActionService postActionService;
    @Resource
    private TenantWithdrawApplyMapper tenantWithdrawApplyMapper;
    private static final String FORMAT_MONTH = "yyyyMM";

    @Resource
    private WithdrawServicefeeSummaryMapper withdrawServicefeeSummaryMapper;
    @Resource
    private WithdrawTaxService withdrawTaxService;
    
    @Autowired
    private DecouplingConfig decouplingConfig;
    
    @Autowired
    private TenantServiceImpl tenantServiceImpl;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    private RedisTemplate redisTemplate;

    @Value("${free.withdrawals.switch:true}")
    private boolean freeWithdrawalsSwitch;

    /**
     * 提现手续费次数
     */
    @Value("${free.withdrawals.num:5}")
    private long freeWithdrawalsNum;
    /**
     * 提现手续费金额
     */
    @Value("${free.withdrawals.fee.deploy:1}")
    private BigDecimal freeWithdrawalsFeeDeploy;
    @Resource
    private TenantCoreHelper tenantCoreHelper;

    //临时测试使用，后续会员提供接口
    @Value("#{'${withdraw.tax.saas.tenantids:12334,56789}'.split(',')}")
    private List<String> akcSaasTenantIdsInWhiteList;
    @Value("${withdraw.tax.expire.time:300}")
    public int lockExpireTime;

    @Override
    public Result<String> tenantWithdraw(TenantWithdrawApply apply) {
        String wihthdrawNo = AccountNoUtils.generateWithdrawNo();
        Result<String> result = Result.success(wihthdrawNo);

        //00-店长提现校验：白名单店长 -- 企业饷店是否需要?
        /*if (checkShopAgentWhitelist(record)){
            return Results.error(CommonConstants.GENERAL_CODE, "店长提现异常，请联系店主");
        }*/

        //01-构建提现申请信息
        apply.setWithdrawNo(wihthdrawNo);
        apply.setApplyStatus(ApplyStatus.DOING.name());
        apply.setWithdrawChannel(WithdrawChannelConstants.PINGAN.getName());
        //01-1：根据店主id 获取映射的 租户id
        AccountTenantCustomer accountTenantCustomer =  tenantServiceImpl.selectOne(apply.getCustomerCode(), apply.getCustomerType());
        if(Objects.isNull(accountTenantCustomer)) {
        	return Results.error(ErrorCodeConstants.ACCORE_101802.getErrorCode(), ErrorCodeConstants.ACCORE_101802.getErrorMessage());
        }
        apply.setTenantId(accountTenantCustomer.getTenantId());
        String bankNoWithPlaintext = apply.getBankNo();
        //01-2: 银行卡号加密
        Result<String> res = AccountUtils.encrypt(apply.getBankNo());
        if (!res.getSuccess()) {
            return Results.error(res.getCode(), res.getMessage());
        }
        apply.setBankNo(res.getData());
        String month = DateUtils.format(new Date(), FORMAT_MONTH);
        apply.setMonth(month);

        //02-提现手续费 & 算税 & 平安解耦提现(异步任务)
        Pair<Boolean,Result<String>> feeOperateRslt = commonSaasWithdraw(apply,bankNoWithPlaintext);
        if(!feeOperateRslt.getLeft()){
            return feeOperateRslt.getRight();
        }

        //03-未参与平安解耦的历史提现记录操作
        if(decouplingConfig.decoupleTenantSwitch(apply.getCustomerCode(), apply.getCustomerType())) {
        	/*try {
				decouplingTenantWithdraw( apply, bankNo);
			} catch (IOException e) {
                Logger.error("commonWithdraw exception, req:{}, e: ", DataMask.toJSONString(apply), e);
                return Results.error(CommonConstants.GENERAL_CODE, e.getMessage());
			}*/
        }else {
        	return originTenantWithdraw(apply,bankNoWithPlaintext);
        }

        return result;
    }

    /**
     * 企业饷店提现：算手续费 & 算税 & 平安解耦提现
     * -- 注意：默认上送的银行卡号为明文、身份证号码为密文
     * -- 提现的算费涉及的3张表：身份证id & 提现月份month 为关联查询条件
     *
     * @param applyRecord
     * @param bankNoWithPlaintext
     * @return
     */
    public Pair<Boolean,Result<String>> commonSaasWithdraw(TenantWithdrawApply applyRecord,String bankNoWithPlaintext){
        RedisLock lock = null;

        try {
            //01-手续费计算
            WithdrawServicefeeSummary withdrawServicefeeSummary = null;
            if (freeWithdrawalsSwitch) {
                withdrawServicefeeSummary = getTenantWithdrawServiceFeeSummary(applyRecord);
            }
            if (withdrawServicefeeSummary != null && applyRecord.getServiceAmount() != null) {
                Logger.info("commonSaasWithdraw:本次收取提现手续费-WithdrawNo={},ServiceAmount={}", applyRecord.getWithdrawNo(), applyRecord.getServiceAmount());
                //提现金额扣减手续费(如果算税，会二次重新计算)
                BigDecimal withAmount = applyRecord.getAmount().subtract(applyRecord.getServiceAmount());
                applyRecord.setRemark("到账：" + withAmount + "，银行手续费：" + applyRecord.getServiceAmount());
                //提现金额不足以扣税，无法提现
                if (withAmount.compareTo(BigDecimal.ZERO) < 1) {
                    return Pair.of(Boolean.FALSE, Results.error(CommonConstants.GENERAL_CODE, "提现金额需大于" + freeWithdrawalsFeeDeploy + "元，请重新输入"));
                }
            } else {
                Logger.info("commonSaasWithdraw:本次提现免收手续费-WithdrawNo={}", applyRecord.getWithdrawNo());
            }

            //02-我司租户下用户提现，则主动扣税(会员提供-属于我司的-企业饷店-的白名单)
            WithdrawTaxDetail taxDetail = null;
            Boolean isPlatformSassTenant = tenantCoreHelper.isPlatformSaasTenant(Long.valueOf(applyRecord.getTenantId()));
            if (akcSaasTenantIdsInWhiteList.contains(applyRecord.getTenantId()) || isPlatformSassTenant) {
                Logger.info("commonSaasWithdraw:开始算税-当前租户为我司企业饷店租户={},WithdrawNo={}", applyRecord.getTenantId(),applyRecord.getWithdrawNo());
                //02-1:判断是否达到提现扣税金额限制
                WithdrawThresholdCheckBO withdrawThreshold = withdrawTaxService.checkWithdrawThreshold(applyRecord);
                if (!withdrawThreshold.getSuccess()) {
                    Logger.error("commonSaasWithdraw:本次提现触发扣税金额限制-WithdrawNo={},tipCode={},tips={}", applyRecord.getWithdrawNo(), withdrawThreshold.getCode(), withdrawThreshold.getTip());
                    return Pair.of(Boolean.FALSE, Results.error(withdrawThreshold.getCode(), withdrawThreshold.getTip()));
                }

                //02-2：算税
                taxDetail = withdrawTaxService.applyWithdrawTax(applyRecord);
                Logger.info("commonSaasWithdraw:算税成功-WithdrawNo={},taxDetail={}", applyRecord.getWithdrawNo(), JSON.toJSONString(taxDetail));

                //02-3:落地扣税记录：使用分布式锁保证并发安全，然后插入扣税记录，并更新扣税汇总 -- 后置到声明式事务中处理
                /*Result<Void> taxRes = withdrawTaxService.withdrawTax(taxDetail);
                if (!taxRes.getSuccess()) {
                    Logger.error("shopkeeperWithdraw withdrawTax taxRes:{}", com.mengxiang.base.common.utils.datamasking.DataMask.toJSONString(taxRes));
                    return Pair.of(Boolean.FALSE,Results.error(CommonConstants.GENERAL_CODE, taxRes.getMessage()));
                }*/

                //02-4: 落地提现手续费 -- 后置到声明式事务中处理
                /*if (null != withdrawServicefeeSummary) {
                    Result<Void> serviceFeeRes = withdrawTaxService.withdrawServiceFee(record, taxDetail, withdrawServicefeeSummary);
                    if (!serviceFeeRes.getSuccess()) {
                        return Pair.of(Boolean.FALSE, Results.error(CommonConstants.GENERAL_CODE, serviceFeeRes.getMessage()));
                    }
                }*/

                //02-5: 账户中心记账 TODO：待确认是否fin-clearing-core是否会执行该操作
                /*TradeInfo tradeInfo = generateTradeInfo(record, AccountKeyConstants.NM.name(), DetailTypeConstants.TRADE_TYPE_044.name());
                com.akucun.common.Result<Void> tradeResult = accountCenterService.dealTrade(tradeInfo);
                if (!tradeResult.isSuccess()) {
                    Logger.warn("shopkeeperWithdraw accountCenterService.dealTrade tradeResult: {}", com.mengxiang.base.common.utils.datamasking.DataMask.toJSONString(tradeResult));
                    //逆向操作
                    bankWithdrawFailProcess(record, tradeResult.getMessage());
                    return Pair.of(Boolean.FALSE,  Results.error(CommonConstants.GENERAL_CODE, tradeResult.getMessage()));
                }*/
            }

            //03-获得分布式锁，
            Result<String> amountKeyResult = withdrawTaxService.getWithdrawTaxKey(ObjectUtils.isEmpty(taxDetail)?applyRecord.getIdentifyNo():taxDetail.getIdentifyNo(),
                    ObjectUtils.isEmpty(taxDetail)?applyRecord.getMonth():taxDetail.getMonth());
            if (!amountKeyResult.getSuccess()) {
                return Pair.of(Boolean.FALSE, Results.error(CommonConstants.GENERAL_CODE, "解密身份证失败"));
            }
            String amountKey = amountKeyResult.getData();
            lock = new RedisLock(redisTemplate, amountKey, lockExpireTime);
            if (!lock.tryLock()) {
                Logger.warn("withdrawTax RedisLock is lock, lockKey:{}", amountKey);
                return Pair.of(Boolean.FALSE, Results.error(ResponseEnum.ACCORE_101511));
            }

            //04-落地数据：声明式事务 -- 插入提现申请 & 手续 & 提现detail+summary
            String idNo = MD5Utils.md5(applyRecord.getIdentifyNo());
            applyRecord.setIdentifyNo(idNo);

            // 20250813新增改动, 参考饷店提现改动, 此处赋值移到事务内
            /*if (!ObjectUtils.isEmpty(withdrawServicefeeSummary) && ObjectUtils.isEmpty(withdrawServicefeeSummary.getId())) {
                if (null != taxDetail) {
                    BeanUtils.copyProperties(taxDetail, withdrawServicefeeSummary);
                }
                withdrawServicefeeSummary.setIdentifyName(applyRecord.getCustomerName());
                withdrawServicefeeSummary.setIdentifyNo(applyRecord.getIdentifyNo());
            }*/
            WithdrawServicefeeSummary withdrawServicefeeSummaryTmp = withdrawServicefeeSummary;
            WithdrawTaxDetail taxDetailTmp = taxDetail;
            Triple<Boolean, Integer, String> dbOperateRst = transactionTemplate.execute(new TransactionCallback<Triple<Boolean, Integer, String>>() {
                @Override
                public Triple<Boolean, Integer, String> doInTransaction(TransactionStatus transactionStatus) {
                    try {
                        //03-1：落地提现申请记录
                        int effectNum = tenantWithdrawApplyMapper.insert(applyRecord);
                        if (effectNum != 1) {
                            throw new AccountProxyException(ResponseEnum.ACCORE_101507);
                        }

                        //03-2:落地手续费
                        if (!ObjectUtils.isEmpty(withdrawServicefeeSummaryTmp)) {
                            if (null != withdrawServicefeeSummaryTmp.getId()) {
                                effectNum = withdrawServicefeeSummaryMapper.addAmount(withdrawServicefeeSummaryTmp);
                            } else {
                                if (null != taxDetailTmp) {
                                    BeanUtils.copyProperties(taxDetailTmp, withdrawServicefeeSummaryTmp);
                                }
                                withdrawServicefeeSummaryTmp.setIdentifyName(applyRecord.getCustomerName());
                                withdrawServicefeeSummaryTmp.setIdentifyNo(applyRecord.getIdentifyNo());
                                // 避免BeanUtils.copyProperties拷贝了其它id
                                withdrawServicefeeSummaryTmp.setId(null);

                                effectNum = withdrawServicefeeSummaryMapper.insert(withdrawServicefeeSummaryTmp);
                            }
                            if (effectNum != 1) {
                                throw new AccountProxyException(ResponseEnum.ACCORE_101516);
                            }
                        }

                        //03-3:落地提现扣税detail+summary
                        if (null != taxDetailTmp) {
                            Result<Void> result = null;
                            //企业和个体工商户提现存入税费明细表，用于财务对账
                            if (MemberGrade.PERSON_AUTH.getName().equals(taxDetailTmp.getIdentifyCategory()) || MemberGrade.ENTERPRISE_AUTH.getName().equals(taxDetailTmp.getIdentifyCategory())) {
                                result = withdrawTaxService.taxDetail(taxDetailTmp);
                            } else {
                                //更新税费汇总表
                                result = withdrawTaxService.taxSummary(taxDetailTmp);
                                if (!result.getSuccess()) {
                                    throw new AccountProxyException(result.getMessage());
                                }
                                //插入扣税明细表
                                result = withdrawTaxService.taxDetail(taxDetailTmp);
                            }
                            if (!result.getSuccess()) {
                                throw new AccountProxyException(ResponseEnum.ACCORE_101512.getCode(),result.getMessage());
                            }
                        }

                        //03-4-落地异步提现任务
                        if (decouplingConfig.decoupleTenantSwitch(applyRecord.getCustomerCode(), applyRecord.getCustomerType())) {
                            decouplingTenantWithdraw(applyRecord, taxDetailTmp,bankNoWithPlaintext);
                        }

                        return Triple.of(Boolean.TRUE, ResponseEnum.SUCCESS.getCode(), "suss");
                    } catch (Exception e) {
                        //回滚事务
                        transactionStatus.setRollbackOnly();
                        Logger.error("[taxCalc4SaasWithdraw]-提现记录等落地异常：", e);
                        if (e instanceof AccountProxyException) {
                            AccountProxyException e1 = (AccountProxyException) e;
                            return Triple.of(Boolean.FALSE, e1.getErrorCode(), e1.getErrorMsg());
                        }
                        return Triple.of(Boolean.FALSE, ResponseEnum.CONCURRENT_REQUEST.getCode(), "提现数据落地异常");
                    }
                }
            });
            if (!dbOperateRst.getLeft()) {
                return Pair.of(Boolean.FALSE, Results.error(dbOperateRst.getMiddle(), dbOperateRst.getRight()));
            }

            return Pair.of(Boolean.TRUE, Results.success());
        }catch (Exception e){
            Logger.error("[taxCalc4SaasWithdraw]-提现记录落地异常：WithdrawNo={}", applyRecord.getWithdrawNo(),e);
            return Pair.of(Boolean.FALSE, Results.error(ResponseEnum.SYSTEM_EXCEPTION));
        }finally {
            if(!ObjectUtils.isEmpty(lock)){
                lock.unlock();
            }
        }
    }

    private WithdrawServicefeeSummary getTenantWithdrawServiceFeeSummary(TenantWithdrawApply apply) {
        WithdrawServicefeeSummary withdrawServicefeeSummary = new WithdrawServicefeeSummary();
        String month = com.aikucun.common2.utils.DateUtils.format(new Date(), FORMAT_MONTH);
        //会员确认-一个手机号码只能注册饷店或企业饷店，因此这里暂且认为身份证号码就能区分开饷店或企业饷店
        String idNo = MD5Utils.md5(apply.getIdentifyNo());
        LambdaQueryWrapper<WithdrawServicefeeSummary> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WithdrawServicefeeSummary::getIdentifyNo, idNo)
                .eq(WithdrawServicefeeSummary::getMonth, month);
        List<WithdrawServicefeeSummary> list = withdrawServicefeeSummaryMapper.selectList(wrapper);
        apply.setServiceAmount(BigDecimal.ZERO);
        if (!CollectionUtils.isEmpty(list)) {
            //判断当前提现次数是否符合收取手续费标准
            if (list.get(0).getWithdrawNum() >= freeWithdrawalsNum){
                apply.setServiceAmount(freeWithdrawalsFeeDeploy);
            }
            withdrawServicefeeSummary = list.get(0);
        }else {
            withdrawServicefeeSummary.setMonth(month);
        }

        return withdrawServicefeeSummary;
    }

    /**
     * 构建tradeInfo
     *
     * @param apply
     * @return
     */
    private TradeInfo generateTradeInfo(TenantWithdrawApply apply, String accountTypeKey, String tradeType) {
        TradeInfo tradeInfo = new TradeInfo();
        tradeInfo.setAccountTypeKey(accountTypeKey);
        tradeInfo.setTradeType(tradeType);
        tradeInfo.setSourceBillNo(apply.getWithdrawNo());
        tradeInfo.setTradeNo(apply.getWithdrawNo());
        tradeInfo.setAmount(apply.getAmount());
        tradeInfo.setCustomerCode(apply.getCustomerCode());
        tradeInfo.setRemark(apply.getRemark());
        return tradeInfo;
    }

    /**
     * 构建平安提现请求参数
     *
     * @param apply
     * @param bankNo
     * @return
     */
    private WithdrawVO generateWithdrawVO(TenantWithdrawApply apply, String bankNo) {
        WithdrawVO vo = new WithdrawVO();
        if (null != apply.getServiceAmount()) {
            BigDecimal withdrawAmount = apply.getAmount().subtract(apply.getServiceAmount());
            vo.setAmount(withdrawAmount.multiply(new BigDecimal(100)).intValue());
            vo.setHandFee(apply.getServiceAmount().multiply(new BigDecimal(100)).intValue());
        } else {
            vo.setAmount(apply.getAmount().multiply(new BigDecimal(100)).intValue());
        }
        vo.setOutacctid(bankNo);
        vo.setThirdcustid(apply.getCustomerCode());
        vo.setThirdLogNo(apply.getWithdrawNo());
        vo.setCustomerType(apply.getCustomerType());
        return vo;
    }

    /**
     * 平安异步提现
     *
     * @param withdrawVO
     */
    private void asyncWithdraw(WithdrawVO withdrawVO) {
        PostActionItemBO itemBO = PostActionItemBO.builder()
                .bizId(withdrawVO.getThirdLogNo())
                .paramObject(withdrawVO)
                .remark("租户店主店长提现异步请求")
                .actionType(PostActionTypes.PINGAN_DELAY_WITHDRAW.getName())
                .status(PostActionExecStatus.EXECUTE.value())
                .nextRetryTime(LocalDateTime.now().plusSeconds(10))
                .retryNums(0)
                .build();
        postActionService.addAction(itemBO);
    }

    @Override
    public Result<TenantWithdrawApplyVO> queryTenantWithdrawDetail(TenantWithdrawQueryReq req) {
        Result<TenantWithdrawApplyVO> result = new Result<>();
        result.setSuccess(true);
        try {
            if (req == null || StringUtils.isBlank(req.getWithdrawNo())) {
                result.setSuccess(Boolean.FALSE);
                result.setMessage("提现查询详情参数不可为空");
                return result;
            }
            TenantWithdrawApply apply = tenantWithdrawApplyMapper.selectOne(new LambdaQueryWrapper<TenantWithdrawApply>().eq(TenantWithdrawApply::getWithdrawNo, req.getWithdrawNo()));
            if (apply == null) {
                result.setSuccess(Boolean.FALSE);
                result.setMessage("无此提现详情记录");
                return result;
            }
            TenantWithdrawApplyVO vo = new TenantWithdrawApplyVO();
            BeanUtils.copyProperties(apply, vo);
            String customerCode = apply.getCustomerCode();
            //查询绑卡信息
            BindCardQueryReq var1 = new BindCardQueryReq();
            var1.setCustomerCode(customerCode);
            var1.setCustomerType((CustomerNatureType) CustomerNatureType.getEnum(CustomerNatureType.class, apply.getCustomerType()));
            var1.setStatus(BankCardStatusConstants.BINDACTIVE);
            ResultList<PinganCardVO> cardList = merchantQueryServiceApi.selectBindCardListByParams(var1);
            if (null != cardList && cardList.isSuccess() && !CollectionUtils.isEmpty(cardList.getDatalist())) {
                for (PinganCardVO card : cardList.getDatalist()) {
                    vo.setBankLogo(card.getBankLogo());
                }
            }
            result.setData(vo);
        } catch (Exception e) {
            result.setSuccess(Boolean.FALSE);
            result.setMessage("查询提现详情异常");
            Logger.error(result.getMessage(), e);
        }
        return result;
    }
    
    
    private Result decouplingTenantWithdraw(TenantWithdrawApply apply,WithdrawTaxDetail taxDetailTmp,String bankNo) throws IOException {
    	PostActionItemBO itemBO = PostActionItemBO.builder()
                .bizId(apply.getWithdrawNo())
                .paramObject(FinTaskAcceptRequestConvert.employeeWithdrawFinTaskAcceptRequest(apply,taxDetailTmp,bankNo))
                .remark("租户店主店长提现异步请求")
                .actionType(PostActionTypes.PINGAN_WITHDRAW.getName())
                .status(PostActionExecStatus.EXECUTE.value())
                .nextRetryTime(LocalDateTime.now().plusSeconds(1))
                .retryNums(0)
                .build();
        postActionService.addAction(itemBO);
        return Results.success(); 
    }
    
    private Result originTenantWithdraw(TenantWithdrawApply apply,String bankNo) {
    	//账户中心提现参数组装
        TradeInfo tradeInfo = generateTradeInfo(apply, AccountKeyConstants.NM.getName(), DetailTypeConstants.TRADE_TYPE_044.name());
        if (CustomerType.NMDL.name().equals(apply.getCustomerType())) {
            tradeInfo.setAccountTypeKey(AccountKeyConstants.NMDL.name());
            tradeInfo.setTradeType(DetailTypeConstants.TRADE_TYPE_261.name());
        }
        com.akucun.common.Result<Void> tradeResult = accountCenterService.dealTrade(tradeInfo);
        if (!tradeResult.isSuccess()) {
            tenantWithdrawApplyMapper.updateWithdrawRecordStatus(apply.getWithdrawNo(), ApplyStatus.FAIL.name(), tradeResult.getMessage());
            //手续费回退
            withdrawTaxService.tenantWithdrawFailForServiceFee(apply);
            return Results.error(CommonConstants.GENERAL_CODE, tradeResult.getMessage());
        }else {
            //4.平安账户异步提现
            WithdrawVO withdrawVO = generateWithdrawVO(apply, bankNo);
            asyncWithdraw(withdrawVO);
        }
	    return Results.success();    
    }
    
    

    
}
