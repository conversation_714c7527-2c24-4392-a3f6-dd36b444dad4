package com.akucun.account.proxy.service.tradeflow.agreement;

import com.akucun.account.proxy.service.tradeflow.domain.TradePhase;
import com.alibaba.fastjson.JSON;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfig;
import org.springframework.stereotype.Service;

@Service
public class TradeAgreementService {

    @ApolloConfig
    private Config config;

    private final static String AGREEMENT_PREFIX = "AGREEMENT";
    private final static String DELIMITER = ".";

    public TradeAgreement getAgreement(String agreementId) {
        String agreementStr = config.getProperty(String.join(DELIMITER, AGREEMENT_PREFIX, agreementId), null);
        if(agreementStr == null) {
            return null;
        } else {
            return JSON.parseObject(agreementStr, TradeAgreement.class);
        }
    }

    public TradeAgreementPhase nextAgreementPhase(TradeAgreement agreement, TradePhase currentPhase) {
        if(currentPhase == null) {//第一个阶段
            return agreement.getPhases().get(0);
        }
        if(currentPhase.getAgreement().getPhaseMap() != null
                && currentPhase.getAgreement().getPhaseMap().containsKey(currentPhase.getStatus())) {//跳转阶段
            return getPhaseByCode(agreement, currentPhase.getAgreement().getPhaseMap().get(currentPhase.getStatus()));
        }
        return getNextPhaseByCode(agreement, currentPhase.getAgreement().getCode());//默认下一阶段
    }

    public TradeAgreementPhase getPhaseByCode(TradeAgreement agreement, String phaseCode) {
        for(TradeAgreementPhase phase : agreement.getPhases()) {
            if(phase.getCode().equals(phaseCode)) {
                return phase;
            }
        }
        return null;
    }

    public TradeAgreementPhase getNextPhaseByCode(TradeAgreement agreement, String phaseCode) {
        boolean current = false;
        for(TradeAgreementPhase phase : agreement.getPhases()) {
            if(phase.getCode().equals(phaseCode)) {
                current = true;
                continue;
            }
            if(current) {
                return phase;
            }
        }
        return null;
    }

}
