package com.akucun.account.proxy.service.acct.bo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 提现税费试算响应
 *
 * <AUTHOR>
 */
@Data
public class WithdrawTaxCalcResult implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 2275124837190493195L;
    /**
     * 本次提现金额
     */
    private BigDecimal amount;
    /**
     * 税费金额
     */
    private BigDecimal taxFee;
    /**
     * 到账金额
     */
    private BigDecimal withdraw;
    /**
     * 税率提示
     */
    private String tip;
    /**
     * /当月累计提现金额（包含本次提现金额）
     */
    private BigDecimal accumAmount;

}
