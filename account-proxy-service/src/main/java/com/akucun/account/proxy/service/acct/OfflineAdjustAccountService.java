package com.akucun.account.proxy.service.acct;

import java.util.List;

import com.akucun.account.proxy.facade.stub.others.account.req.OfflineAdjustAccountPageQueryReq;
import com.akucun.account.proxy.facade.stub.others.account.req.OfflineAdjustAccountSubmitReq;
import com.akucun.account.proxy.facade.stub.others.account.vo.OfflineAdjustAccountBatchStatisticVO;
import com.akucun.account.proxy.facade.stub.others.account.vo.OfflineAdjustAccountVO;
import com.mengxiang.base.common.model.result.Pagination;
import com.mengxiang.base.common.model.result.Result;

public interface OfflineAdjustAccountService {

    /**
     * 新增线下调账记录
     * @param addVO
     * @return
     */
    Result<Void> submit(OfflineAdjustAccountSubmitReq addVO);

    /**
     * 分页查询线下调账记录
     * @param queryReq
     * @return
     */
    Result<Pagination<OfflineAdjustAccountVO>> pageQuery(OfflineAdjustAccountPageQueryReq queryReq);

    /**
     * 统计线下调账记录
     * @param queryReq
     * @return
     */
    Result<OfflineAdjustAccountBatchStatisticVO> statistic(OfflineAdjustAccountPageQueryReq queryReq);
    
    /**
     * 审核线下调账记录
     * @param id
     * @param operator
     * @return  
     */
    Result<Void> auditPass(Long id, String operator);

    /**
     * 审核拒绝线下调账记录
     * @param id
     * @param operator
     * @return
     */
    Result<Void> auditRefuse(Long id, String operator);

    /**
     * 批量删除线下调账记录
     * @param ids
     * @param operator
     * @return
     */
    Result<Integer> batchDelete(List<Long> ids, String operator);

    /**
     * 一键删除
     * @param queryReq
     * @param operator
     * @return
     */
    Result<Void> onKeyDelete(OfflineAdjustAccountPageQueryReq queryReq, String operator); 

    /**
     * 异常重试
     * @param id
     * @param operator
     * @return
     */
    Result<Void> retry(Long id, String operator);
}
