package com.akucun.account.proxy.service.acct.job;

import com.aikucun.common2.base.Result;
import com.aikucun.common2.log.Logger;
import com.akucun.account.proxy.common.utils.RestTemplateUtils;
import com.akucun.account.proxy.service.acct.bo.NotifyPromoBO;
import com.akucun.account.proxy.service.common.SpringContextHolder;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.mengxiang.transaction.framework.dao.TransactionTaskLogDO;
import com.mengxiang.transaction.framework.enums.TaskExecuteErrorCodeEnum;
import com.mengxiang.transaction.framework.enums.TaskExecuteStatusEnum;
import com.mengxiang.transaction.framework.task.InsurableTask;
import com.mengxiang.transaction.framework.task.TaskExecuteResult;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.env.Environment;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : 异步通知营销-奖励下发-最新状态
 * @Create on : 2025/2/17 11:55
 **/
public class NotifyPayRsltTask extends InsurableTask<TaskExecuteResult> {
    //任务执行的业务对象,通过构造函数注入
    private NotifyPromoBO notifyDTO;

    @Override
    public TaskExecuteResult doExecute() {
        //初始化一致性框架返回结果
        TaskExecuteResult taskExecuteResult = new TaskExecuteResult();

        try{
            //业务执行：通过Spring上下文获取业务Bean并执行业务操作
            String groupCode = StringUtils.isEmpty(notifyDTO.getGroupCode())?null:notifyDTO.getGroupCode();
            Logger.info("营销-奖励发放-最新状态通知外部开始[{}]：{}", groupCode,JSON.toJSONString(notifyDTO));

            Result<Void> notifyRslt = SpringContextHolder.getApplicationContext().getBean(RestTemplateUtils.class).doPost(
                    notifyDTO.getNotifyUrl(),
                    notifyDTO.getNotifyDTO(),
                    new ParameterizedTypeReference<Result<Void>>() {
                    }, groupCode);
            Logger.info("营销-奖励发放-最新状态通知外部结果[{}]：{}", groupCode,JSON.toJSONString(notifyRslt));

            //通过业务返回结果构建一致性框架的返回结果
            if (!ObjectUtils.isEmpty(notifyRslt) && !ObjectUtils.isEmpty(notifyRslt.getSuccess()) && notifyRslt.getSuccess()) {
                taskExecuteResult.setExecuteStatus(TaskExecuteStatusEnum.SUCCESS);
                return taskExecuteResult;
            }

            taskExecuteResult.setExecuteStatus(TaskExecuteStatusEnum.EXCEPTION);
            taskExecuteResult.setErrorCode(TaskExecuteErrorCodeEnum.SYSTEM_ERROR.name());
            taskExecuteResult.setErrorMessage(!ObjectUtils.isEmpty(notifyRslt)?notifyRslt.getMessage():TaskExecuteErrorCodeEnum.SYSTEM_ERROR.getDesc());
        }catch (Exception e){
            Logger.error("营销-奖励发放-最新状态通知外部失败:{}", JSON.toJSONString(notifyDTO),e);
            taskExecuteResult.setErrorMessage(e.getMessage().substring(0, 80));
        } finally {
            Logger.info("一致性框架-营销-奖励发放-最新状态通知外部完成:{}", JSON.toJSONString(taskExecuteResult));
        }

        return taskExecuteResult;
    }

    @Override
    public void rebuild(TransactionTaskLogDO transactionTaskLogDO) {
        String requestAdditionalInfo = transactionTaskLogDO.getRequestAdditionalInfo();
        this.setNotifyDTO(JSON.parseObject(requestAdditionalInfo, new TypeReference<NotifyPromoBO>() {}));
    }

    /**
     * 重要：序列化业务参数，用于保存到数据库后一旦执行失败，重新构建任务对象
     *
     * @return
     */
    @Override
    public String serializeAdditionalInfo() {
        return JSON.toJSONString(notifyDTO);
    }

    /**
     * 返回任务ID, 这个是任务的唯一标识
     * -- 默认使用任务的幂等id即可
     *
     * @return
     */
    @Override
    public String getTaskId() {
        return notifyDTO.getNotifyDTO().getBizType() + "_" + notifyDTO.getNotifyDTO().getTradeNo();
    }

    //================= 通过构造函数注入业务请求参数 ======================
    public NotifyPayRsltTask() {
    }

    public NotifyPayRsltTask(NotifyPromoBO notifyDTO) {
        this.notifyDTO = notifyDTO;
    }

    public NotifyPromoBO getNotifyDTO() {
        return notifyDTO;
    }

    public void setNotifyDTO(NotifyPromoBO notifyDTO) {
        this.notifyDTO = notifyDTO;
    }
}
