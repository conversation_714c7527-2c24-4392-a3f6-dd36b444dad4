package com.akucun.account.proxy.service.acct.job;

import com.aikucun.common2.log.Logger;
import com.akucun.account.center.client.model.account.AccountBookDetailDO;
import com.akucun.account.proxy.client.center.AccountCenterClient;
import com.akucun.account.proxy.client.marketaccount.MerchantMarketAccountOperateClient;
import com.akucun.account.proxy.facade.stub.enums.PromoActivityIncentiveTypeEnum;
import com.akucun.account.proxy.facade.stub.enums.PromoActivityTypeEnum;
import com.akucun.account.proxy.facade.stub.others.merchant.market.MerchantFullReturnMarketAddFreezeRequest;
import com.akucun.account.proxy.service.merchant.market.constants.MerchantMarketConstants;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Create on : 2025/1/8 13:53
 **/
@Component
public class MerchantFullReturnMarketFundRepairGlueJavaJob extends IJobHandler {

    @Resource
    private MerchantMarketAccountOperateClient merchantMarketAccountOperateClient;

    @Resource
    private AccountCenterClient accountCenterClient;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        MerchantFullReturnMarketAddFreezeRequest request = JSONObject.parseObject(s, MerchantFullReturnMarketAddFreezeRequest.class);
        try {
            //活动结束根据预期花费金额进行预冻结
            BigDecimal addFreezeAmount = request.getAmount().add(request.getTaxAmount());
            if (addFreezeAmount.compareTo(BigDecimal.ZERO) == 0) {
                Logger.warn("营销活动预期加冻金额为0，忽略，bizNo：{}，merchantCode：{}，objectId：{}，amount：{}，taxAmount：{}",
                        request.getBizNo(), request.getMerchantCode(), request.getObjectId(), request.getAmount(), request.getTaxAmount());
                return new ReturnT<>(ReturnT.FAIL_CODE, "营销活动预期加冻金额为0，忽略");
            }

            //商家编码
            String merchantCode = request.getMerchantCode();
            //源单号=营销活动编号+店铺编码，此处为了兼容同一个商家多个店铺报名同一场平台营销的场景，便于后续出款解冻区分
            String sourceBillNo = String.format("%s_%s", request.getPromoActivityId(), request.getObjectId());
            //活动名称
            String activityName = request.getBizExplain();
            //活动类型
            String activityType = PromoActivityTypeEnum.findEnumByValue(request.getPromoActivityType()).getDesc();
            //备注
            String remark = "";
            if (PromoActivityIncentiveTypeEnum.SHORT.name().equals(request.getIncentiveType())) {
                /*merchantMarketAccountOperateClient.marketUpdateAccount(addFreezeAmount, MARKET_ACCOUNT_FREEZE_TRADE_TYPE, request.getBizNo(),
                        request.getObjectId(), activityName, request.getPromoActivityId(), "活动结束/"+activityType);*/
                remark = "活动结束";
            } else if (PromoActivityIncentiveTypeEnum.LONG.name().equals(request.getIncentiveType())) {
                remark = "活动周期冻结";
            }

            //调用账户中心查询营销预付冻结、营销预付解冻交易记录
            List<AccountBookDetailDO> accountBookDetailDOS = accountCenterClient.queryAccountDetails(merchantCode, "B19002B29407357DF8FAD51471DBFFA5", null,
                    sourceBillNo, Arrays.asList(MerchantMarketConstants.MARKET_ACCOUNT_FREEZE_TRADE_TYPE, MerchantMarketConstants.MARKET_ACCOUNT_UNFREEZE_TRADE_TYPE), true);

            //统计当前商家+活动累计营销预付冻结金额
            /*BigDecimal sumFreezeAmount = accountBookDetailDOS.stream().filter(accountBookDetailDO -> StringUtils.equals(accountBookDetailDO.getTradeType(), MerchantMarketConstants.MARKET_ACCOUNT_FREEZE_TRADE_TYPE))
                    .map(AccountBookDetailDO::getAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO).abs();*/

            BigDecimal sumFreezeAmount = BigDecimal.ZERO;
            for (AccountBookDetailDO accountBookDetailDO : accountBookDetailDOS) {
                if (StringUtils.equals(accountBookDetailDO.getTradeType(), MerchantMarketConstants.MARKET_ACCOUNT_FREEZE_TRADE_TYPE)) {
                    sumFreezeAmount = sumFreezeAmount.add(accountBookDetailDO.getAmount());
                }
            }
            sumFreezeAmount = sumFreezeAmount.abs();

            //统计当前商家+活动累计营销预付解冻金额
            /*BigDecimal sumUnFreezeAmount = accountBookDetailDOS.stream().filter(accountBookDetailDO -> StringUtils.equals(accountBookDetailDO.getTradeType(), MerchantMarketConstants.MARKET_ACCOUNT_UNFREEZE_TRADE_TYPE))
                    .map(AccountBookDetailDO::getAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO).abs();*/
            BigDecimal sumUnFreezeAmount = BigDecimal.ZERO;
            for (AccountBookDetailDO accountBookDetailDO : accountBookDetailDOS) {
                if (StringUtils.equals(accountBookDetailDO.getTradeType(), MerchantMarketConstants.MARKET_ACCOUNT_UNFREEZE_TRADE_TYPE)) {
                    sumUnFreezeAmount = sumUnFreezeAmount.add(accountBookDetailDO.getAmount());
                }
            }
            sumUnFreezeAmount = sumUnFreezeAmount.abs();

            /*if (sumUnFreezeAmount.compareTo(BigDecimal.ZERO) > 0) {
                throw new AccountProxyException(ResponseEnum.SYSTEM_EXCEPTION, String.format("营销活动结束前存在异常解冻数据，customerCode=%s，objectId=%s，sourceBillNo=%s", request.getMerchantCode(), request.getObjectId(), sourceBillNo));
            }*/

            //计算历史剩余冻结金额
            BigDecimal historyRemainingFreezeAmount = sumFreezeAmount.subtract(sumUnFreezeAmount);

            //如果历史剩余冻结金额大于本次加冻金额
            if (historyRemainingFreezeAmount.compareTo(addFreezeAmount) >= 0) {
                Logger.warn("营销活动加冻金额低于历史剩余冻结金额，忽略，historyRemainingFreezeAmount：{}，request：{}", historyRemainingFreezeAmount, JSONObject.toJSONString(request));
                return ReturnT.SUCCESS;
            }
            //本次实际加冻金额
            BigDecimal actualAddFreezeAmount = addFreezeAmount.subtract(historyRemainingFreezeAmount);
            merchantMarketAccountOperateClient.marketUpdateAccount(actualAddFreezeAmount, MerchantMarketConstants.MARKET_ACCOUNT_FREEZE_TRADE_TYPE, request.getBizNo(), sourceBillNo,
                    merchantCode, activityName, request.getPromoActivityId(), remark + "/" + activityType, true);
        } catch (Exception e) {
            return new ReturnT<>(ReturnT.FAIL_CODE, e.getMessage());
        }
        return ReturnT.SUCCESS;
    }
}
