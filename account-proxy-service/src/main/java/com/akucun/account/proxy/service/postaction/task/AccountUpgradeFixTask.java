package com.akucun.account.proxy.service.postaction.task;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import com.aikucun.common2.log.Logger;
import com.akucun.account.proxy.common.utils.AccountUtils;
import com.akucun.account.proxy.common.utils.SpringBeanUtil;
import com.akucun.account.proxy.dao.model.AccountOpTrade;
import com.akucun.account.proxy.service.acct.AccountService;
import com.akucun.account.proxy.service.acct.impl.AccountServiceImpl;
import com.akucun.account.proxy.service.acct.repository.AccountOpTradeRepository;
import com.alibaba.fastjson.JSON;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;

import java.util.Date;
import java.util.List;
import java.util.Optional;

public class AccountUpgradeFixTask extends IJobHandler {

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        Logger.info("AccountUpgradeFixTask执行，参数：{}", param);
        AccountOpTradeRepository accountOpTradeRepository = SpringBeanUtil.getBean("accountOpTradeRepository", AccountOpTradeRepository.class);
        AccountService accountService = SpringBeanUtil.getBean("accountServiceImpl", AccountServiceImpl.class);

        Date queryDate = DateUtil.offset(new Date(), DateField.MINUTE, -22);
        List<AccountOpTrade> accountTradeList = accountOpTradeRepository.queryAccountUpgrade(queryDate);
        Logger.info("AccountUpgradeFixTask执行，accountTradeList：{}", JSON.toJSONString(accountTradeList));
        for(AccountOpTrade accountOpTrade : accountTradeList) {
            accountService.unlockAccount(AccountUtils.getSellerCode(accountOpTrade.getCustomerCode(), accountOpTrade.getCustomerType()), accountOpTrade.getCustomerType());
        }

        Logger.info("AccountUpgradeFixTask执行，结束");
        return ReturnT.SUCCESS;
    }

}
