package com.akucun.account.proxy.service.compensation.task;

import com.aikucun.common2.log.Logger;
import com.akucun.account.proxy.facade.stub.enums.CompensationFillPayTypeEnum;
import com.akucun.account.proxy.facade.stub.others.dto.req.CompensationFillPayApplyReq;
import com.akucun.account.proxy.service.common.SpringContextHolder;
import com.akucun.account.proxy.service.compensation.impl.CompensationNotifyService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.mengxiang.transaction.framework.dao.TransactionTaskLogDO;
import com.mengxiang.transaction.framework.enums.TaskExecuteErrorCodeEnum;
import com.mengxiang.transaction.framework.enums.TaskExecuteStatusEnum;
import com.mengxiang.transaction.framework.task.InsurableTask;
import com.mengxiang.transaction.framework.task.TaskExecuteResult;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description :
 * @Create on : 2024/11/27 22:41
 **/
public class CompensationPayApplyTask extends InsurableTask<TaskExecuteResult> {
    //任务执行的业务对象,通过构造函数注入
    private CompensationFillPayApplyReq busiData;

    /**
     * 业务执行
     * @return TaskExecuteResult 一致性框架的返回对象
     */
    @Override
    public TaskExecuteResult doExecute() {
        //初始化一致性框架返回结果
        TaskExecuteResult taskExecuteResult = new TaskExecuteResult();

        try{
            //业务执行：通过Spring上下文获取业务Bean并执行业务操作
            CompensationFillPayTypeEnum payTypeEnum = CompensationFillPayTypeEnum.getByCode(busiData.getType());
            Pair<Boolean, String> execRslt = SpringContextHolder.getBean(CompensationNotifyService.class).asynNotify(payTypeEnum,busiData);

            //通过业务返回结果构建一致性框架的返回结果
            if (!ObjectUtils.isEmpty(execRslt) && execRslt.getLeft()) {
                taskExecuteResult.setExecuteStatus(TaskExecuteStatusEnum.SUCCESS);
                return taskExecuteResult;
            }

            taskExecuteResult.setExecuteStatus(TaskExecuteStatusEnum.EXCEPTION);
            taskExecuteResult.setErrorCode(TaskExecuteErrorCodeEnum.SYSTEM_ERROR.name());
            taskExecuteResult.setErrorMessage(!ObjectUtils.isEmpty(execRslt)?execRslt.getRight():TaskExecuteErrorCodeEnum.SYSTEM_ERROR.getDesc());
        }catch (Exception e){
            Logger.error("供应链渠道商统一补款异步回调失败:{}", JSON.toJSONString(busiData),e);
            taskExecuteResult.setErrorMessage(e.getMessage().substring(0, 80));
        } finally {
            Logger.info("一致性框架-供应链渠道商统一补款通知业务方完成:{},taskId:{}", JSON.toJSONString(taskExecuteResult), getTaskId());
        }

        return taskExecuteResult;
    }

    /**
     * 重要：根据数据库里保存的任务信息， 重建任务对象
     *
     * @param transactionTaskLogDO
     */
    @Override
    public void rebuild(TransactionTaskLogDO transactionTaskLogDO) {
        String requestAdditionalInfo = transactionTaskLogDO.getRequestAdditionalInfo();
        this.setBusiData(JSON.parseObject(requestAdditionalInfo, new TypeReference<CompensationFillPayApplyReq>() {}));
    }

    /**
     * 重要：序列化业务参数，用于保存到数据库后一旦执行失败，重新构建任务对象
     * @return
     */
    @Override
    public String serializeAdditionalInfo() {
        return JSON.toJSONString(busiData);
    }

    /**
     * 返回任务ID, 这个是任务的唯一标识
     * -- 默认使用任务的幂等id即可
     * @return
     */
    @Override
    public String getTaskId() {
        return busiData.getType() +"_"+busiData.getSourceBusinessNo();
    }

    //================= 通过构造函数注入业务请求参数 ======================

    //空构造函数
    public CompensationPayApplyTask() {
    }
    //重载构造函数
    public CompensationPayApplyTask(CompensationFillPayApplyReq busiData) {
        this.busiData = busiData;
    }
    //业务参数的 get/set方法
    public CompensationFillPayApplyReq getBusiData() {
        return busiData;
    }
    public void setBusiData(CompensationFillPayApplyReq busiData) {
        this.busiData = busiData;
    }
}
