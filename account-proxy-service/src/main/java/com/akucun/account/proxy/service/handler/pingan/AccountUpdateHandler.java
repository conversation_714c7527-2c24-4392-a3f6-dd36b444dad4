package com.akucun.account.proxy.service.handler.pingan;

import com.aikucun.common2.utils.datamasking.DataMask;
import com.akucun.account.proxy.common.constant.CommonConstants;
import com.akucun.account.proxy.common.enums.ResponseEnum;
import com.akucun.account.proxy.common.enums.ResultStatus;
import com.akucun.account.proxy.common.exception.AccountProxyException;
import com.akucun.account.proxy.service.common.bo.AccountExecStepContext;
import com.akucun.account.proxy.service.common.bo.AccountReq;
import com.akucun.account.proxy.service.common.bo.AccountResp;
import com.akucun.account.proxy.service.common.step.AbstractHandler;
import com.akucun.fps.common.entity.Result;
import com.akucun.fps.pingan.client.model.po.PinganAccount;
import com.akucun.fps.pingan.feign.api.merchant.MerchantServiceApi;
import com.alibaba.fastjson.JSON;
import com.mengxiang.base.common.log.Logger;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @Author: silei
 * @Date: 2020/8/28
 * @desc: 账户状态更新（升级完成和置为失效）
 */
@Component
public class AccountUpdateHandler extends AbstractHandler {

    @Resource
    private MerchantServiceApi merchantServiceApi;

    @Override
    protected boolean preCheck(AccountExecStepContext accountExecStepContext) {
        AccountReq req = accountExecStepContext.getAccountReq();
        AccountResp resp = accountExecStepContext.getAccountResp();
        //参数校验
        if (StringUtils.isBlank(req.getCustomerCode())) {
            resp.setStatus(ResultStatus.F.getCode());
            resp.setReplyMsg("用户客户编码为空");
        }
        if (StringUtils.isBlank(req.getCustomerType())) {
            resp.setStatus(ResultStatus.F.getCode());
            resp.setReplyMsg("用户客户类型为空");
        }
        return !ResultStatus.F.getCode().equals(resp.getStatus());
    }

    @Override
    protected void doSubmitBefore(AccountExecStepContext accountExecStepContext) {
        AccountReq req = accountExecStepContext.getAccountReq();
        Logger.info("AccountUpdateHandler doSubmitBefore req:{}", DataMask.toJSONString(req));
        PinganAccount pinganAccount = formAccountParam(req);
        accountExecStepContext.setReqMessage(pinganAccount);
    }

    private PinganAccount formAccountParam(AccountReq req) {
        PinganAccount pinganAccount = new PinganAccount();
        //店主店长编号都要拼接
        pinganAccount.setCustomerCode(req.getCustomerType() + req.getCustomerCode());
        pinganAccount.setCustomerType(req.getCustomerType());
        pinganAccount.setStatus(req.getAccountStatus());
        return pinganAccount;
    }

    @Override
    protected void doSubmit(AccountExecStepContext accountExecStepContext) {
        PinganAccount account = (PinganAccount) accountExecStepContext.getReqMessage();
        Logger.info("AccountUpdateHandler doSubmit req:{}", DataMask.toJSONString(account));
        try {
            Result<Void> result = merchantServiceApi.updatePinganAccount(account);
            Logger.info("AccountUpdateHandler doSubmit result:{}", DataMask.toJSONString(result));
            accountExecStepContext.setRespMessage(result);
        } catch (Exception e) {
            Logger.error("AccountUpdateHandler doSubmit customerCode:{}, customerType:{}, accountUpdateError:{}", account.getCustomerCode(), account.getCustomerType(), e);
            throw new AccountProxyException(ResponseEnum.SUBMIT_EXCEPTION);
        }
    }

    @Override
    protected void doSubmitAfter(AccountExecStepContext accountExecStepContext) {
        AccountResp resp = accountExecStepContext.getAccountResp();
        resp.setStatus(ResultStatus.P.getCode());
        Result<Void> result = (Result<Void>) accountExecStepContext.getRespMessage();
        if (result.isSuccess()) {
            resp.setStatus(ResultStatus.S.getCode());
            resp.setReplyMsg(ResultStatus.S.getDesc());
            resp.setReplyCode(CommonConstants.SUCC_CODE);
        } else {
            resp.setStatus(ResultStatus.F.getCode());
            resp.setReplyMsg(ResultStatus.F.getDesc());
            resp.setReplyCode(CommonConstants.FAIL_CODE);
            AccountReq req = accountExecStepContext.getAccountReq();
            Logger.error("AccountUpdateHandler doSubmitAfter accountUpdateError, customerCode:{}, customerType:{}", req.getCustomerCode(), req.getCustomerType());
        }
    }
}
