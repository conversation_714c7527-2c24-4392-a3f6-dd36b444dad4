package com.akucun.account.proxy.service.tradeflow.dto;

import com.aikucun.common2.log.Logger;
import com.alibaba.fastjson.JSON;

public class BizInfos {

    public static Object getBizInfo(String bizInfo, String cls) {
        try {
            return JSON.parseObject(bizInfo, Class.forName("com.akucun.account.proxy.service.tradeflow.dto.bizinfo." + cls));
        } catch (ClassNotFoundException e) {
            Logger.error("未找到业务信息类型：{}", cls);
            return null;
        }
    }

}
