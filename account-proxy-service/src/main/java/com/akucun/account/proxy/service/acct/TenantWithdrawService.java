package com.akucun.account.proxy.service.acct;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.dao.model.TenantWithdrawApply;
import com.akucun.account.proxy.facade.stub.others.aggregation.req.TenantWithdrawQueryReq;
import com.akucun.account.proxy.facade.stub.others.aggregation.vo.TenantWithdrawApplyVO;

/**
 * @Author: silei
 * @Date: 2021/4/21
 * @desc: 租户提现service
 */
public interface TenantWithdrawService {

    /**
     * 租户店主店长提现
     * @param withdrawApply
     * @return
     */
    Result<String> tenantWithdraw(TenantWithdrawApply withdrawApply);


    /**
     * 查询租户店主店长提现明细
     * @param req
     * @return
     */
    Result<TenantWithdrawApplyVO> queryTenantWithdrawDetail(TenantWithdrawQueryReq req);
}
