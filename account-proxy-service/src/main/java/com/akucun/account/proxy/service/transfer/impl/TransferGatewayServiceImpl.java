package com.akucun.account.proxy.service.transfer.impl;

import com.akucun.account.proxy.dao.mapper.TransferGatewayMapper;
import com.akucun.account.proxy.dao.model.TransferGateway;
import com.akucun.account.proxy.facade.stub.others.dto.req.TransferGatewayReq;
import com.akucun.account.proxy.service.transfer.TransferGatewayService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <p>
 * 付款网关表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-26
 */
@Service
public class TransferGatewayServiceImpl extends ServiceImpl<TransferGatewayMapper, TransferGateway> implements TransferGatewayService {

    @Override
    public void saveUpdate(TransferGatewayReq req) {

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LambdaQueryWrapper<TransferGateway> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TransferGateway::getMchCode, req.getMchCode())
                .eq(TransferGateway::getStatus,0)
                .eq(TransferGateway::getIsDelete,0);
        TransferGateway transferGateway = this.getOne(wrapper);
        if (transferGateway != null){
            transferGateway.setGatewayCode(req.getGatewayCode());
            transferGateway.setGatewayType(req.getGatewayType());
            if (StringUtils.isNotBlank(req.getCompanyName())){
                transferGateway.setCompanyName(req.getCompanyName());
            }
            if (StringUtils.isNotBlank(req.getGatewayInfo())){
                transferGateway.setGatewayInfo(req.getGatewayInfo());
            }
            transferGateway.setStatus(req.getStatus());
            if (StringUtils.isNotBlank(req.getCertExpireTime())){
                transferGateway.setCertExpiretime(LocalDateTime.parse(req.getCertExpireTime(),formatter));
            }
            this.updateById(transferGateway);
        } else {
            transferGateway = new TransferGateway();
            BeanUtils.copyProperties(req, transferGateway);
            transferGateway.setCertExpiretime(LocalDateTime.parse(req.getCertExpireTime(),formatter));
            this.save(transferGateway);
        }
    }
}
