package com.akucun.account.proxy.service.acct;

import com.akucun.account.proxy.facade.stub.enums.RewardTypeEnum;
import com.akucun.account.proxy.facade.stub.others.dto.req.BatchBonusPayReq;
import com.akucun.account.proxy.facade.stub.others.dto.req.BonusPayReq;
import com.akucun.account.proxy.facade.stub.others.dto.req.BonusPaySubmitReq;
import com.akucun.account.proxy.facade.stub.others.dto.res.BonusPayResp;
import com.akucun.common.Result;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : 营销-奖励下发-业务处理
 * @Create on : 2025/1/15 11:51
 **/
public interface PromoTradeHandler {

    RewardTypeEnum getBusiType();

    //============= 业务受理 ====================
    Result<BonusPayResp> dealTrade(BonusPayReq bonusPayReq);

    Result<BonusPayResp> batchDeal(BatchBonusPayReq batchBonusPayReq);

    //============ 任务提交 ======================
    Result<BonusPayResp> submit(BonusPaySubmitReq bonusPaySubmitReq);

    //============ 异步通知 ======================
    Result<Void> syncNotify(String tradeNo, String bizType,String transBillDate,String newStatus,String errorMsg);

}
