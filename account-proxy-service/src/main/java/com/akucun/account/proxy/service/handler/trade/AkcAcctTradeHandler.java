package com.akucun.account.proxy.service.handler.trade;

import com.aikucun.common2.utils.StringUtils;
import com.akucun.account.center.client.model.TradeInfo;
import com.akucun.account.proxy.client.account.AccountClient;
import com.akucun.account.proxy.common.constant.CommonConstants;
import com.akucun.account.proxy.common.enums.ResponseEnum;
import com.akucun.account.proxy.common.exception.AccountProxyException;
import com.akucun.account.proxy.facade.stub.enums.TradeChannel;
import com.akucun.account.proxy.service.common.bo.AccountExecStepContext;
import com.akucun.account.proxy.service.common.bo.AccountTradeReq;
import com.akucun.account.proxy.service.common.step.AbstractHandler;
import com.akucun.account.proxy.service.trade.AccountTradeCommonService;
import com.akucun.account.proxy.service.trade.AccountTradeService;
import com.akucun.common.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;


/**
 * <AUTHOR>
 * @date 2020/12/21 16:45
 */
@Component
public class AkcAcctTradeHandler extends AbstractHandler {

    @Autowired
    private AccountTradeService accountTradeService;
    @Autowired
    private AccountClient accountClient;
    @Autowired
    private AccountTradeCommonService accountTradeCommonService;

    @Override
    protected boolean preCheck(AccountExecStepContext accountExecStepContext) {
        return true;
    }

    @Override
    protected void doSubmitBefore(AccountExecStepContext accountExecStepContext) {

    }

    @Override
    protected void doSubmit(AccountExecStepContext accountExecStepContext) {
        AccountTradeReq req = accountExecStepContext.getAccountTradeReq();
        Result result = Result.success();
        TradeInfo tradeInfo = new TradeInfo();
        if(TradeChannel.OPENAPI.getCode().equals(req.getChannel())) {
            tradeInfo.setAccountTypeKey(CommonConstants.OPENAPI_BALANCE_CUSTOMER_KEY);
            tradeInfo.setTradeType(CommonConstants.OPENAPI_PAY_BALANCE_CUSTOMER_TRADE_TYPE_120);
        } else if(TradeChannel.APP.getCode().equals(req.getChannel())) {
            tradeInfo.setAccountTypeKey(CommonConstants.CASH_WITHDRAWAL_BALANCE_CUSTOMER_KEY);
            tradeInfo.setTradeType(CommonConstants.CASH_WITHDRAWAL_BALANCE_CUSTOMER_TRADE_TYPE_008);
        }

        if(StringUtils.isNullOrEmpty(tradeInfo.getAccountTypeKey()) || StringUtils.isNullOrEmpty(tradeInfo.getTradeType())){
            throw new AccountProxyException(ResponseEnum.TRADE_NOT_CONFIG);
        }

        tradeInfo.setCustomerCode(req.getCustomerCode());
        tradeInfo.setAmount(req.getAmount().setScale(2, BigDecimal.ROUND_HALF_UP));
        tradeInfo.setTradeNo(req.getTradeNo());
        tradeInfo.setSourceBillNo(req.getSourceNo());
        tradeInfo.setRemark(TradeChannel.queryDescByCode(req.getChannel()) + ":" + req.getSourceNo());
        Result<Void> tradeResult = accountClient.newBalanceTrade(tradeInfo);
        if (null != tradeResult && tradeResult.isSuccess()) {
            result.setSuccess(Boolean.TRUE);
        } else {
            result.setSuccess(Boolean.FALSE);
            result.setCode(tradeResult.getCode());
            result.setMessage(tradeResult.getMessage());
        }
        accountExecStepContext.setRespMessage(result);
    }

    @Override
    protected void doSubmitAfter(AccountExecStepContext accountExecStepContext) throws Exception {
        accountTradeCommonService.handlerAfterCommit(accountExecStepContext);
    }

}
