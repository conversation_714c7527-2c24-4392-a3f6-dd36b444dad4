package com.akucun.account.proxy.service.compensation.bo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description :  新支付回调参数(售后程进波提供)
 * @Create on : 2024/11/26 15:06
 **/
@Data
@NoArgsConstructor
public class RefundNotifyResDTO {
    /**
     * 商家编号
     */
    private String merchantCode;

    /**
     * 业务退款单号
     */
    private String outRefundNo;

    /**
     * 支付退款单号
     */
    private String refundNo;

    /**
     * 第三方退款单号
     */
    private String thirdTradeNo;

    /**
     * 原业务子订单号
     */
    private String subOrderNo;

    /**
     * 原业务合单号
     */
    private String orderNo;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 退款通道
     */
    private String channelCode;

    /**
     * 退款类型
     */
    private String refundType;

    /**
     * 退款状态
     */
    private String status;

    /**
     * 退款成功时间
     */
    private String successTime;

    /**
     * 响应结果（成功失败信息）
     */
    private String responseMsg;
}
