package com.akucun.account.proxy.service.postaction.task;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.common.enums.PostActionExecStatus;
import com.akucun.account.proxy.common.utils.Results;
import com.akucun.account.proxy.common.enums.PostActionTypes;
import com.akucun.account.proxy.dao.model.PostActionItem;
import com.akucun.account.proxy.facade.stub.enums.CustomerType;
import com.akucun.account.proxy.service.postaction.PostActionService;
import com.akucun.account.proxy.service.postaction.bo.PostActionItemBO;
import com.akucun.account.proxy.service.postaction.common.AbsPostActionExecutor;
import com.akucun.fps.common.util.GsonUtils;
import com.akucun.fps.pingan.client.vo.WithdrawVO;
import com.akucun.fps.pingan.feign.api.assets.AssetsServiceApi;
import com.mengxiang.base.common.log.Logger;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * @Author: silei
 * @Date: 2021/3/11
 * @desc: 平安账户提现延迟定时任务
 */
@Component
public class DelayWithdrawTask extends AbsPostActionExecutor {

    @Resource
    private AssetsServiceApi assetsServiceApi;

    @Autowired
    private PostActionService postActionService;


    @XxlJob("delayWithdrawTask")
    public ReturnT<String> execute(String param) {
        return this.executeEntrance();
    }

    @Override
    protected String getActionType() {
        return PostActionTypes.PINGAN_DELAY_WITHDRAW.getName();
    }

    @Override
    public Result<Void> execute(PostActionItem item) {
        Result<Void> result = Result.success();
        WithdrawVO withdrawVO = null;
        try {
            withdrawVO = GsonUtils.getInstance().fromJson(item.getParam(), WithdrawVO.class);
            com.akucun.fps.common.entity.Result<String> withdrawRes = assetsServiceApi.cashWithdraw(withdrawVO);
            if (!withdrawRes.isSuccess()) {
                //平安提现只进行一次执行，不进行重试
                String msg = "延迟提现失败" + withdrawRes.getErrorMessage();
                Logger.error(msg + ", customerCode:{}, withdrawNo :{}", withdrawVO.getThirdcustid(), withdrawVO.getThirdLogNo());
            }
        } catch (Exception e) {
            Logger.error("延迟提现调用异常: ", e);
            if (Objects.nonNull(withdrawVO)) {
                PostActionItemBO itemBO = PostActionItemBO.builder()
                        .bizId(withdrawVO.getThirdLogNo())
                        .paramObject(withdrawVO)
                        .remark("异步提现结果查询")
                        .actionType(PostActionTypes.PINGAN_DELAY_WITHDRAW_QUERY.getName())
                        .status(PostActionExecStatus.EXECUTE.value())
                        .nextRetryTime(LocalDateTime.now().plusSeconds(10))
                        .retryNums(0)
                        .build();
                postActionService.addAction(itemBO);
            } else {
                return Result.error(e);
            }
        }
        return result;
    }
}
