package com.akucun.account.proxy.service.transfer;

import com.akucun.account.proxy.dao.model.PaymentTransfer;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 企业付款到零钱 服务类
 *
 * <AUTHOR>
 * @since 2021-01-26
 */
public interface PaymentTransferService extends IService<PaymentTransfer> {

    PaymentTransfer queryBySourceNo(String sourceCode, String sourceNo);

    Boolean saveOne(PaymentTransfer paymentTransfer);

    Boolean updateOne(PaymentTransfer paymentTransfer);

}
