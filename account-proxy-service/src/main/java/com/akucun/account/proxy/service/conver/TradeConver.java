package com.akucun.account.proxy.service.conver;

import org.springframework.beans.factory.annotation.Autowired;

import com.akucun.account.proxy.common.enums.BizTypeEnum;
import com.akucun.account.proxy.common.enums.ProductCategoryEnum;
import com.akucun.account.proxy.common.enums.RequestPlatformEnum;
import com.akucun.account.proxy.common.help.StringHelper;
import com.akucun.account.proxy.facade.stub.others.trade.req.AccountCenterRegisterReq;
import com.akucun.account.proxy.facade.stub.others.trade.req.TradeReq;
import com.akucun.account.proxy.servicea.account.bo.MemberRegisterBo;
import com.alibaba.fastjson.JSON;
import com.mengxiang.member.service.facade.common.feign.seller.SellerInfoFeign;

public class TradeConver {
	
	@Autowired
	private SellerInfoFeign sellerInfoFeign;
	
	public static TradeReq TradeReqConver(MemberRegisterBo memberRegisterBo, Integer tenantType) {
		TradeReq tradeReq = new TradeReq();
		tradeReq.setBizType(BizTypeEnum.ACCOUNT_CENTER_REGISTER.getValue());
		tradeReq.setTradeNo(StringHelper.uuid());
		tradeReq.setBizNo(StringHelper.uuid());
		tradeReq.setProductCategory(ProductCategoryEnum.XD.getValue());
		tradeReq.setRequestPlatform(RequestPlatformEnum.SERVE.getValue());
		AccountCenterRegisterReq accountCenterRegisterReq = AccountCenterRegisterConver.accountCenterRegisterReqConver(memberRegisterBo, tenantType);
		tradeReq.setBizInfo(JSON.toJSONString(accountCenterRegisterReq));
		return tradeReq;
	}

}
