package com.akucun.account.proxy.service.acct.impl;


import com.aikucun.common2.base.Pagination;
import com.akucun.account.proxy.common.enums.AdjustAccountStatusEnum;
import com.akucun.account.proxy.dao.mapper.AdjustAccountMapper;
import com.akucun.account.proxy.dao.model.AdjustAccount;
import com.akucun.account.proxy.facade.stub.others.dto.req.AccountAdjustReq;
import com.akucun.account.proxy.facade.stub.others.dto.req.AdjustAccountQueryReq;
import com.akucun.account.proxy.facade.stub.others.dto.req.PinganAdjustReq;
import com.akucun.account.proxy.facade.stub.others.dto.res.AdjustAccountQueryRes;
import com.akucun.account.proxy.service.acct.AdjustAccountService;
import com.akucun.account.proxy.service.acct.repository.AdjustAccountRepository;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mengxiang.base.common.log.Logger;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class AdjustAccountServiceImpl implements AdjustAccountService {
    @Resource
    private AdjustAccountMapper adjustAccountMapper;

    @Autowired
    private AdjustAccountRepository adjustAccountRepository;

    /**
     * 根据请求号更新状态
     * @param status
     * @param requestNo
     * @return
     */
    @Override
    public Boolean updateStatusByRequestNo(String status,String requestNo) {
        if(status.isEmpty()||requestNo.isEmpty()){
            return false;
        }
        return adjustAccountMapper.updateStatusByRequestNo(status,requestNo);
    }

    @Override
    public Boolean updateValuesByRequestNo(String status,String sourceBillNo,String tradeNo,String requestNo) {
        if(status.isEmpty()||requestNo.isEmpty()){
            return false;
        }
        return adjustAccountMapper.updateValuesByRequestNo(status,sourceBillNo,tradeNo,requestNo);
    }

    /**
     * 平安调账插入数据
     * @param pinganAdjustReq
     * @return
     */
    @Override
    public Pair<Integer,String> insertPinganAdjudt(PinganAdjustReq pinganAdjustReq) {
        if(ObjectUtils.isEmpty(pinganAdjustReq)||pinganAdjustReq.getRequestNo().isEmpty()||pinganAdjustReq.getOperator().isEmpty()||
                pinganAdjustReq.getCustomerCode().isEmpty()||pinganAdjustReq.getCustomerType().isEmpty()||pinganAdjustReq.getAdjustmentType().isEmpty()||
                pinganAdjustReq.getRemark().isEmpty()||pinganAdjustReq.getAmount()==null
        ){
            return Pair.of(9,"服务出错，请稍后再试");
        }
        AdjustAccount adjustAccount=new AdjustAccount();
        adjustAccount.setRequestNo(pinganAdjustReq.getRequestNo());
        adjustAccount.setOperator(pinganAdjustReq.getOperator());
        adjustAccount.setAdjustmentType(pinganAdjustReq.getAdjustmentType());
        adjustAccount.setCustomerCode(pinganAdjustReq.getCustomerCode());
        adjustAccount.setCustomerType(pinganAdjustReq.getCustomerType());
        adjustAccount.setAmount(pinganAdjustReq.getAmount());
        adjustAccount.setRemark(pinganAdjustReq.getRemark());
        adjustAccount.setStatus(AdjustAccountStatusEnum.NONE.getValue());
        int row=adjustAccountMapper.insert(adjustAccount);
        if (row == 1) {
            return Pair.of(0,"插入数据成功");
        } else {
            return Pair.of(1,"插入数据失败");
        }
    }

    /**
     * 账户中心调账插入数据
     * @param accountAdjustReq
     * @return
     */
    @Override
    public Pair<Integer,String> insertAccountAdjust(AccountAdjustReq accountAdjustReq) {
        //校验参数
        if(ObjectUtils.isEmpty(accountAdjustReq)||accountAdjustReq.getRequestNo().isEmpty()||accountAdjustReq.getOperator().isEmpty()||
                accountAdjustReq.getCustomerCode().isEmpty()||accountAdjustReq.getAccountTypeKey().isEmpty()||
                accountAdjustReq.getAdjustmentType().isEmpty()||accountAdjustReq.getRemark().isEmpty()||
                accountAdjustReq.getAmount()==null
        ){
            return Pair.of(9,"服务出错，请稍后再试");
        }
        AdjustAccount adjustAccount=new AdjustAccount();
        adjustAccount.setRequestNo(accountAdjustReq.getRequestNo());
        adjustAccount.setOperator(accountAdjustReq.getOperator());
        adjustAccount.setAccountTypeKey(accountAdjustReq.getAccountTypeKey());
        adjustAccount.setCustomerCode(accountAdjustReq.getCustomerCode());
        adjustAccount.setAdjustmentType(accountAdjustReq.getAdjustmentType());
        adjustAccount.setAmount(accountAdjustReq.getAmount());
        adjustAccount.setRemark(accountAdjustReq.getRemark());
        adjustAccount.setStatus(AdjustAccountStatusEnum.NONE.getValue());
        if(!ObjectUtils.isEmpty(accountAdjustReq.getSourceBillNo())){
            adjustAccount.setSourceBillNo(accountAdjustReq.getSourceBillNo());
        }
        if(!ObjectUtils.isEmpty(accountAdjustReq.getTradeNo())){
            adjustAccount.setTradeNo(accountAdjustReq.getTradeNo());
        }
        int row = adjustAccountMapper.insert(adjustAccount);
        if (row == 1) {
            return Pair.of(0,"插入数据成功");
        } else {
            return Pair.of(1,"插入数据失败");
        }
    }

    /**
     * 调账分页查询
     * @param adjustAccountQueryReq
     * @return
     */
    @Override
    public Pagination<AdjustAccountQueryRes> adjustAccountQuery(AdjustAccountQueryReq adjustAccountQueryReq) {
        Logger.info("adjustAccountQueryReq:{}",adjustAccountQueryReq);

        Pagination<AdjustAccountQueryRes> resResultPage=new  Pagination<>();
        Page<AdjustAccount> adjustAccountPage = adjustAccountRepository.adjustAccountQuery(adjustAccountQueryReq);
        List<AdjustAccount> records = adjustAccountPage.getRecords();

        resResultPage.setResult(CollectionUtils.emptyIfNull(records).stream().map(t->{
            AdjustAccountQueryRes data=new AdjustAccountQueryRes();
            data.setId(t.getId());
            data.setRequestNo(t.getRequestNo());
            data.setOperator(t.getOperator());
            data.setAdjustmentType(t.getAdjustmentType());
            data.setCustomerCode(t.getCustomerCode());
            data.setCustomerType(t.getCustomerType());
            data.setAccountTypeKey(t.getAccountTypeKey());
            data.setStatus(t.getStatus());
            data.setAmount(t.getAmount());
            data.setRemark(t.getRemark());
            data.setUpdateTime(t.getUpdateTime());
            data.setCreateTime(t.getCreateTime());
            data.setSourceBillNo(t.getSourceBillNo());
            data.setTradeNo(t.getTradeNo());
            return data;
        }).collect(Collectors.toList()));
        resResultPage.setPageIndex(adjustAccountQueryReq.getPageNum());
        resResultPage.setTotal(adjustAccountPage.getTotal());
        return resResultPage;
    }

    /**
     * 查询配置分钟内是否被操作
     * @param opreator
     * @param customterCode
     * @param limitTime
     * @return
     */
    @Override
    public Boolean selectOperation(String opreator, String customterCode, Date limitTime) {
        Logger.info("参数："+opreator+"+"+customterCode+"+"+limitTime);
        if(opreator.isEmpty()||customterCode.isEmpty()||ObjectUtils.isEmpty(limitTime)){
            return false;
        }
        LambdaQueryWrapper<AdjustAccount> wrapper=new LambdaQueryWrapper<>();
        wrapper.eq(AdjustAccount::getOperator,opreator).eq(AdjustAccount::getCustomerCode,customterCode).
                ge(AdjustAccount::getUpdateTime,limitTime);
        if(adjustAccountMapper.selectCount(wrapper)!=0){
            return false;
        }
        return true;
    }


}
