package com.akucun.account.proxy.service.transfer.factory;

import com.akucun.account.proxy.common.enums.ResponseEnum;
import com.akucun.account.proxy.common.exception.AccountProxyException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/01/28 19:49
 */
@Component
public class PayTransferFactory {

    @Autowired
    private List<TransferService> transferServiceList;

    public TransferService getTransferService(String transferType) {
        for(TransferService transferService : transferServiceList) {
            if(transferService.isSupport(transferType)) {
                return transferService;
            }
        }
        throw new AccountProxyException(ResponseEnum.TRANSFER_TYPE_NOT_SUPPORT);
    }

}
