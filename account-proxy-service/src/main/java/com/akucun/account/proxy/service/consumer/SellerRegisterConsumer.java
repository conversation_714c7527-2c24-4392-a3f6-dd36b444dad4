package com.akucun.account.proxy.service.consumer;

import com.akucun.account.proxy.facade.stub.others.trade.req.TradeReq;
import com.akucun.account.proxy.facade.stub.others.trade.res.TradeRes;
import com.akucun.account.proxy.facade.stub.others.trade.res.TradeStatusEnum;
import com.akucun.account.proxy.service.conver.TradeConver;
import com.akucun.account.proxy.service.help.TenantCoreHelper;
import com.akucun.account.proxy.service.tradeflow.TradeService;
import com.akucun.account.proxy.servicea.account.bo.MemberRegisterBo;
import com.maihaoche.starter.mq.annotation.MQConsumer;
import com.maihaoche.starter.mq.base.AbstractMQPushConsumer;
import com.mengxiang.base.common.log.Logger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;

@Slf4j
@MQConsumer(instances = {
        @MQConsumer.Instance(
                instanceName = "DEFAULT",
                topic = "MEMBER_RELATE_UNITY_REGISTER_TOPIC",
                tag = "MEMBER_REGISTER_SELLER_TAG",
                consumerGroup = "MEMBER_RELATE_UNITY_SELLER_REGISTER_GROUP")})
public class SellerRegisterConsumer extends AbstractMQPushConsumer<MemberRegisterBo>{

	@Autowired
    private TradeService tradeService;
	@Resource
	private TenantCoreHelper tenantCoreHelper;
	
	@Override
    public boolean process(MemberRegisterBo message, Map<String, Object> extMap) {
		Logger.info("MEMBER_RELATE_UNITY_REGISTER_TOPIC【MEMBER_RELATE_UNITY_SELLER_REGISTER_GROUP】,{}",message);
		if(Objects.isNull(message)) {
			return Boolean.TRUE;
		}
		try {
			 if(!message.check()) {
				 Logger.info("MEMBER_RELATE_UNITY_REGISTER_TOPIC【MEMBER_RELATE_UNITY_SELLER_REGISTER_GROUP】,不符合注册条件跳过,{}",message);
				 return Boolean.TRUE;
			 }
			Pair<Boolean, Integer> pair = tenantCoreHelper.isCompanyShop(message.getTenantId());
			Boolean isCompanyShop = pair.getLeft();
			Integer tenantType = pair.getRight();
			if(Boolean.FALSE.equals(isCompanyShop)){
				 return Boolean.TRUE;
			 }
			 TradeReq tradeReq = TradeConver.TradeReqConver(message, tenantType);
			 TradeRes tradeRes = tradeService.trade(tradeReq);
			 if(tradeRes.getStatus().equals(TradeStatusEnum.SUCCESS)) {
				 return Boolean.TRUE;
			 }
		}catch (Exception e) {
			Logger.error("MEMBER_RELATE_UNITY_REGISTER_TOPIC【MEMBER_RELATE_UNITY_SELLER_REGISTER_GROUP】,注册失败,{}",message,e);
		}
		return Boolean.FALSE;
	}

}
