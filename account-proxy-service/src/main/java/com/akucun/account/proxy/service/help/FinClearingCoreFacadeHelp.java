package com.akucun.account.proxy.service.help;

import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.aikucun.common2.base.Result;
import com.aikucun.common2.log.Logger;
import com.akucun.account.proxy.common.utils.Results;
import com.akucun.account.proxy.task.request.FinTaskAcceptRequest;
import com.mengxiang.fin.clearing.core.service.facade.common.feign.FinClearingCoreFacade;
import com.mengxiang.fin.clearing.core.service.facade.common.result.FinTaskVO;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;

@Component
public class FinClearingCoreFacadeHelp {
	
	@Autowired
	private FinClearingCoreFacade finClearingCoreFacade;
	
	public Result<Void> accept(FinTaskAcceptRequest request){
		com.mengxiang.fin.clearing.core.service.facade.common.request.FinTaskAcceptRequest finTaskAcceptRequest = new com.mengxiang.fin.clearing.core.service.facade.common.request.FinTaskAcceptRequest();
		BeanUtil.copyProperties(request, finTaskAcceptRequest);
		try {
			Logger.info("FinClearingCoreFacadeHelp accept ,request:{}",JSONUtil.toJsonStr(finTaskAcceptRequest));
			com.mengxiang.base.common.model.result.Result<FinTaskVO>  resp = finClearingCoreFacade.accept(finTaskAcceptRequest);
			Logger.info("FinClearingCoreFacadeHelp accept ,response:{}",resp);
			if(Objects.isNull(resp)) {
				return Results.error("任务提交失败");
			}
			if(!resp.isSuccess()) {
				Logger.error("FinClearingCoreFacadeHelp accept ,fail:{}",resp);
				return Results.error(resp.getMessage());
			}
			return Results.success();
		}catch (Exception e) {
			Logger.error("提交任务失败",e);
			return Results.error(e.getMessage());
		}
		
	}

}
