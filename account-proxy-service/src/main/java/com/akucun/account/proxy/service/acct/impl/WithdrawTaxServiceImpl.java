package com.akucun.account.proxy.service.acct.impl;

import com.aikucun.common2.base.Result;
import com.aikucun.common2.base.exception.BusinessException;
import com.aikucun.common2.base.exception.BusinessRuntimeException;
import com.aikucun.common2.utils.DateUtils;
import com.aikucun.common2.utils.datamasking.DataMask;
import com.akucun.account.center.client.model.TradeInfo;
import com.akucun.account.center.feign.AccountCenterService;
import com.akucun.account.proxy.common.constant.ApplyStatus;
import com.akucun.account.proxy.common.constant.Constant;
import com.akucun.account.proxy.common.enums.*;
import com.akucun.account.proxy.common.exception.AccountProxyException;
import com.akucun.account.proxy.common.utils.RedisLock;
import com.akucun.account.proxy.common.utils.Results;
import com.akucun.account.proxy.dao.mapper.*;
import com.akucun.account.proxy.dao.model.*;
import com.akucun.account.proxy.facade.stub.enums.AccountKeyConstants;
import com.akucun.account.proxy.facade.stub.enums.CustomerType;
import com.akucun.account.proxy.facade.stub.enums.DetailTypeConstants;
import com.akucun.account.proxy.facade.stub.others.account.req.ShopkeeperIncentiveAwardWithdrawCalcTaxReq;
import com.akucun.account.proxy.facade.stub.others.account.vo.WithdrawTaxDetailVO;
import com.akucun.account.proxy.facade.stub.others.aggregation.req.WithdrawTaxCalcReq;
import com.akucun.account.proxy.facade.stub.others.aggregation.req.WithdrawTaxDetailReq;
import com.akucun.account.proxy.facade.stub.others.aggregation.req.WithdrawTaxSummaryReq;
import com.akucun.account.proxy.facade.stub.others.aggregation.res.*;
import com.akucun.account.proxy.service.acct.WithdrawTaxService;
import com.akucun.account.proxy.service.acct.bo.CustomerAuthBO;
import com.akucun.account.proxy.service.acct.bo.WithdrawTaxBO;
import com.akucun.account.proxy.service.acct.bo.WithdrawThresholdCheckBO;
import com.akucun.account.proxy.service.common.DuplicateCheckService;
import com.akucun.account.proxy.service.config.DecouplingConfig;
import com.akucun.account.proxy.service.postaction.PostActionService;
import com.akucun.account.proxy.service.postaction.bo.PostActionItemBO;
import com.akucun.cloud.security.CodeUtils;
import com.akucun.common.util.MD5Utils;
import com.akucun.fps.common.entity.Query;
import com.akucun.fps.common.entity.ResultList;
import com.akucun.fps.pingan.client.util.DateUtil;
import com.akucun.fps.pingan.client.vo.DealTradeVO;
import com.akucun.fps.pingan.feign.api.member.MemberServiceApi;
import com.akucun.member.audit.facade.stub.fallback.api.FeignMemberAuthService;
import com.akucun.member.audit.model.dto.auth.QueryAuthStatusReqDTO;
import com.akucun.member.audit.model.dto.auth.QueryAuthStatusRespDTO;
import com.akucun.member.audit.model.dto.auth.QueryUserAuthBaseReqDTO;
import com.akucun.member.audit.model.dto.auth.QueryUserAuthBaseRespDTO;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mengxiang.base.common.log.Logger;
import lombok.Getter;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: silei
 * @Date: 2021/3/9
 * @desc: 提现扣税service
 */
@Service
public class WithdrawTaxServiceImpl implements WithdrawTaxService {

    @Resource
    private MemberServiceApi memberServiceApi;
    @Resource
    private FeignMemberAuthService feignMemberAuthService;
    @Resource
    private WithdrawApplyRecordMapper withdrawApplyRecordMapper;
    @Resource
    private WithdrawTaxSummaryMapper withdrawTaxSummaryMapper;
    @Resource
    private WithdrawTaxDetailMapper withdrawTaxDetailMapper;
    @Resource
    private WechatWithdrawSummaryMapper wechatWithdrawSummaryMapper;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private PostActionService postActionService;
    @Autowired
    private DuplicateCheckService duplicateCheckService;
    @Resource
    private AccountCenterService accountCenterService;
    @Resource
    private WithdrawServicefeeSummaryMapper withdrawServicefeeSummaryMapper;
    @Resource
    private DecouplingConfig decouplingConfig;
    @Resource
    private TenantWithdrawApplyMapper tenantWithdrawApplyMapper;

    @Value("${withdraw.tax.expire.time:300}")
    public int lockExpireTime;
    /**
     * 提现扣税开关
     */
    @Value("${withdraw.tax.switch}")
    private String withdrawTaxSwitch;

    @Value("${withdraw.tax.startdate}")
    private String withdrawTaxStartDate;
    /**
     * 提现查询会员等级判断开关
     */
    @Value("${withdraw.grade.charge.switch}")
    private String gradeChargeSwitch;
    /**
     * 免除扣税客户等级
     */
//    @Value("${without.tax.grade}")
    @Value("#{'${without.tax.grade:PERSON_AUTH,ENTERPRISE_AUTH}'.split(',')}")
    private List<String> withoutTaxGradeList;
    /**
     * 店主会员提现阈值(提示语)
     */
    @Value("${nm.grade.withdraw.thresholds}")
    private String shopkeeperWithdrawThresholds;
    /**
     * 店长会员提现阈值(提示语)
     */
    @Value("${nmdl.grade.withdraw.thresholds}")
    private String shopAgentWithdrawThresholds;

    /**
     * 店主会员提现阈值(提示语)
     */
    @Value("${tenant.nm.grade.withdraw.thresholds}")
    private String tenantShopkeeperWithdrawThresholds;
    /**
     * 店长会员提现阈值(提示语)
     */
    @Value("${tenant.nmdl.grade.withdraw.thresholds}")
    private String tenantShopAgentWithdrawThresholds;
    
    @Value("${withdraw.switch:false}")
    private Boolean withdrawSwitch;

    @Value("${query.user.highest.auth.status.switch:false}")
    private Boolean queryUserHighestAuthStatusSwitch;

    /**
     * 店主激励奖励自动提现固定税率，默认为2.5%
     */
    @Value("${seller.intcentive.award.auto.withdraw.fixed.tax.rate:0.025}")
    private BigDecimal sellerIncentiveAwardAutoWithdrawFixedTaxRate;

    /**
     * 同身份不同账户提现累计金额汇总计算开关,默认开启
     * 开启后，饷店/企业饷店提现时同身份不同账户提现累计金额会汇总计算，否则不汇总计算
     */
    @Value("${same.identity.different.account.accumulate.withdraw.amount.switch:true}")
    private Boolean sameIdentityDifferentAccountAccumulateWithdrawAmountSwitch;
    
//    /**
//     * 提现扣税白名单
//     */
//    private List<String> taxWhiteList;
//
//    @Value("${withdraw.tax.whiteList:}")
//    public void setTaxWhiteList(String taxWhiteCustomer) {
//        Logger.info("setTaxWhiteList taxWhiteCustomer:{}", taxWhiteCustomer);
//        if (StringUtils.isEmpty(taxWhiteCustomer)) {
//            taxWhiteList = new ArrayList<>();
//        } else {
//            taxWhiteList = Arrays.asList(taxWhiteCustomer.split(","));
//        }
//    }

    /**
     * 店主扣税起始金额
     */
    @Value("${nm.without.tax.threshold}")
    private String shopkeeperTaxThreshold;
    /**
     * 店长扣税起始金额
     */
    @Value("${nmdl.without.tax.threshold}")
    private String agentTaxThreshold;

    /**
     * 企业饷店-店主扣税起始金额
     */
    @Value("${tenant.nm.without.tax.threshold}")
    private String tenantShopkeeperTaxThreshold;
    /**
     * 企业饷店-店长扣税起始金额
     */
    @Value("${tenant.nmdl.without.tax.threshold}")
    private String tenantAgentTaxThreshold;

    @Value("${tax.threshold.tip1:'根据提现规则，您当前提现需扣除%s的综合服务费及税金；lish'}")
    private String taxTip1;
    @Value("${tax.threshold.tip1_2:'您本月累计提现金额>%s万元，全部提现扣除%s的综合服务费及税金；lish'}")
    private String taxTip1_2;
    @Value("${tax.threshold.tip2:'综合服务费及税金中%s为委托第三方服务商代办工商税务登记、月度申报和年检等商事服务费。lish'}")
    private String taxTip2;
    @Value("${tax.threshold.ignore.tip2:false}")
    private Boolean ignoreTaxTip2;


    private static BigDecimal[] AMOUNT_THRESHOLDS = null;

    private static BigDecimal MIN_AMOUNT = null;

    private static BigDecimal[] AMOUNT_RATES = null;
    /**
     * 金额梯度
     *
     * @param withdrawTaxAmountThresholds
     */
    @Value("${withdraw.tax.amount}")
    public void setAmountThresholds(String withdrawTaxAmountThresholds) {
        Logger.info("withdrawTaxAmountThresholds:{}", withdrawTaxAmountThresholds);
        String[] amountThresholds = withdrawTaxAmountThresholds.split(",");
        AMOUNT_THRESHOLDS = new BigDecimal[amountThresholds.length];
        for (int i = 0; i <= amountThresholds.length - 1; i++) {
            AMOUNT_THRESHOLDS[i] = new BigDecimal(amountThresholds[i]);
        }
        Logger.info("AMOUNT_THRESHOLDS:{}", Arrays.toString(AMOUNT_THRESHOLDS));
    }

    /**
     * 税率梯度
     *
     * @param withdrawTaxRateThresholds
     */
    @Value("${withdraw.tax.rate}")
    public void setAmountRates(String withdrawTaxRateThresholds) {
        Logger.info("withdrawTaxRateThresholds:{}", withdrawTaxRateThresholds);
        String[] rateThresholds = withdrawTaxRateThresholds.split(",");
        AMOUNT_RATES = new BigDecimal[rateThresholds.length];
        for (int i = 0; i <= rateThresholds.length - 1; i++) {
            AMOUNT_RATES[i] = new BigDecimal(rateThresholds[i]);
        }
        Logger.info("AMOUNT_RATES:{}", Arrays.toString(AMOUNT_RATES));
        //1分钱 ÷ 最低税率 = 最低需交税金额 （eg：0.01 ÷ 0.001 = 10元，当提现金额达到10元时，才需缴纳0.01元的税费）
        MIN_AMOUNT = BigDecimal.valueOf(0.01).divide(AMOUNT_RATES[0]);
        Logger.info("MIN_AMOUNT:{}", MIN_AMOUNT);
    }

    private static final String WITHDRAW_TAX_SWITCH_ON = "on";
    private static final String FORMAT_MONTH = "yyyyMM";
    private static final String FORMAT_YEAR = "yyyy";
    private static final String WITHDRAW_TAX_REMARK = "提现代扣个税";
    private static final int ERR_ILLEGAL_ARGS = 4000;
    private static final int ERR_ILLEGAL_AMOUNT = 4001;
    private static final int ERR_ILLEGAL_TAX = 4002;
    private static final int ERR_ILLEGAL_MAX = 4009;
    /**
     * 提现手续费开关
     */
    @Value("${free.withdrawals.switch:true}")
    private boolean freeWithdrawalsSwitch;
    /**
     * 提现扣手续费开始时间
     */
    @Value("${free.withdrawals.startDate.deploy:2021年7月01日}")
    private String freeWithdrawalsStartDateDeploy;
    /**
     * 提现手续费次数
     */
    @Value("${free.withdrawals.num:5}")
    private long freeWithdrawalsNum;
    /**
     * 提现手续费金额
     */
    @Value("${free.withdrawals.fee.deploy}")
    private BigDecimal freeWithdrawalsFeeDeploy;
    private static final String WITHDRAW_SERCVICEFEE_SWITCH_ON = "on";
    private static final String WITHDRAW_SERCVICEFEE_SWITCH_OFF = "off";

    @Value("${platform.account.no:XDJS000001}")
    private String platformAccountNo;
    /**
     * 微信提现免息券开关
     */
    @Value("${wechat.free.coupon.switch:true}")
    private boolean freeCouponSwitch;
    /**
     * 微信提现最低限额
     */
    @Value("${wechat.withdraw.quote.limit:1}")
    private String wechatQuoteLimit;


    @Override
    public Result<WithdrawTaxSwitchResp> taxSwitch(String customerCode) {
        Result<WithdrawTaxSwitchResp> result = new Result<>();
        result.setSuccess(true);
        WithdrawTaxSwitchResp data = new WithdrawTaxSwitchResp();
//        if (taxWhiteList.contains(customerCode)) {
//            data.setTaxSwitch(WITHDRAW_TAX_SWITCH_ON);
//        } else {
//            data.setTaxSwitch(withdrawTaxSwitch);
//        }
        data.setTaxSwitch(withdrawTaxSwitch);
        data.setTaxStartDate(withdrawTaxStartDate);
        result.setData(data);
        return result;
    }

    @Override
    public Result<WithdrawServiceFeeSwitchResp> serviceFeeSwitch(String identifyNo) {
        Logger.info("手续费开关入参1：{}",identifyNo);
        Result<WithdrawServiceFeeSwitchResp> result = new Result<>();
        WithdrawServiceFeeSwitchResp resp = new WithdrawServiceFeeSwitchResp();
        result.setSuccess(true);
        if (freeWithdrawalsSwitch){
            resp.setServiceFeeSwitch(WITHDRAW_SERCVICEFEE_SWITCH_ON);
            //查询手续费累计次数
            WithdrawApplyRecord record = new WithdrawApplyRecord();
            record.setIdentifyNo(MD5Utils.md5(identifyNo));
            WithdrawServicefeeSummary withdrawServicefeeSummary = getServiceFeeSum(record);
            Logger.info("手续费开关入参2：{}",JSON.toJSON(withdrawServicefeeSummary));
            resp.setWithdrawTotalNum(0);
            resp.setSurplusWithdrawTotalNum(freeWithdrawalsNum);
            if (null != withdrawServicefeeSummary){
                Logger.info("手续费开关入参3：{}",JSON.toJSON(withdrawServicefeeSummary));
                resp.setWithdrawTotalNum(withdrawServicefeeSummary.getWithdrawNum());
                resp.setSurplusWithdrawTotalNum(0);
                if (withdrawServicefeeSummary.getWithdrawNum() <= freeWithdrawalsNum){
                    resp.setSurplusWithdrawTotalNum(freeWithdrawalsNum-withdrawServicefeeSummary.getWithdrawNum());
                }
            }
        }else {
            resp.setServiceFeeSwitch(WITHDRAW_SERCVICEFEE_SWITCH_OFF);
        }
        resp.setServiceFeeStartDate(freeWithdrawalsStartDateDeploy);
        resp.setServiceFeeFreeNum(freeWithdrawalsNum);
        resp.setServiceFeeAmount(freeWithdrawalsFeeDeploy);
        resp.setMouldNum(1);
        if (freeWithdrawalsNum == 0){
            resp.setMouldNum(2);
        }
        result.setData(resp);
        return result;
    }

    @Override
    public Result<WithdrawTaxCalcResp> taxCalc(WithdrawTaxCalcReq req) {
        Logger.warn("提现试算请求参数 :{}", DataMask.toJSONString(req));
        Result<WithdrawTaxCalcResp> result = new Result<>();
        result.setSuccess(true);
        //计算税费
        WithdrawTaxCalcResp resp = new WithdrawTaxCalcResp();
        resp.setAmount(req.getAmount());
        resp.setTaxFee(BigDecimal.ZERO);
        resp.setWithdraw(req.getAmount());
        resp.setServiceFee(BigDecimal.ZERO);

        WithdrawApplyRecord record = new WithdrawApplyRecord();
        try {
            WithdrawTaxBO taxCheck = this.checkWithdrawTax(req.getCustomerType(), req.getCustomerCode(), true, true, req.getIdentifyNo(), null);
            //查询手续费汇总数据
            if (freeWithdrawalsSwitch && Constant.BANK_WITHDRAW.equals(req.getWithdrawType())){
                record.setIdentifyNo(MD5Utils.md5(req.getIdentifyNo()));
                WithdrawServicefeeSummary withdrawServicefeeSummary = getServiceFeeSum(record);
                if (null == withdrawServicefeeSummary){
                    Logger.info("taxCalc提现试算当月无手续费汇总相关记录：{}",req.getCustomerCode());
                }
                if (withdrawServicefeeSummary != null && withdrawServicefeeSummary.getWithdrawNum() >= freeWithdrawalsNum){
                    resp.setServiceFee(freeWithdrawalsFeeDeploy);
                }
            }
            if (taxCheck.getIsTax()) {
                //判断参数
                this.checkWithdrawTaxParam(req);
                //查询当月提现汇总
                String month = DateUtils.format(new Date(), FORMAT_MONTH);
                WithdrawTaxSummary currMonthSummary = this.getWithdrawTaxSummary(req.getIdentifyNo(), month);
                // 计算税费
                WithdrawTaxDetail taxDetail = new WithdrawTaxDetail();
                taxDetail.setServiceFee(resp.getServiceFee());
                taxCalc(req, currMonthSummary, resp, taxDetail, taxCheck);
            }else {
            	 //税费总额
            	 /**String month = DateUtils.format(new Date(), FORMAT_MONTH);
            	 WithdrawTaxSummary currMonthSummary = this.getWithdrawTaxSummary(req.getIdentifyNo(), month);
            	 BigDecimal accumAmount = req.getAmount();
            	 if (null != currMonthSummary) {
                     accumAmount = accumAmount.add(currMonthSummary.getAmount());
                 }
            	 resp.setAccumAmount(accumAmount);**/
                //如果不扣税费，处理手续费
                resp.setWithdraw(req.getAmount().subtract(resp.getServiceFee()));
            }
            if (!StringUtils.isEmpty(taxCheck.getTip())) {
                resp.setTip(taxCheck.getTip());
            }
            //如果是企业
            if (MemberGrade.ENTERPRISE_AUTH.getCode().equals(req.getMemberGrade())) {
                resp.setAmount(req.getAmount());
                resp.setTaxFee(BigDecimal.ZERO);
                resp.setWithdraw(req.getAmount());
                resp.setServiceFee(BigDecimal.ZERO);
                String tip = "完成企业认证，平台将不再扣除您的税费，由您自行完成纳税义务";
                resp.setTip(tip);
            }
        } catch (BusinessException be) {
            result.setSuccess(false);
            result.setCode(be.getCode());
            result.setMessage(be.getMessage());
        }
        result.setData(resp);
        return result;
    }

    /**
     * 查询指定月份类型扣税额度
     *
     * @param identifyNo
     * @param month
     * @return
     */
    private WithdrawTaxSummary getWithdrawTaxSummary(String identifyNo, String month) {
        LambdaQueryWrapper<WithdrawTaxSummary> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WithdrawTaxSummary::getIdentifyNo, identifyNo)
                .eq(WithdrawTaxSummary::getMonth, month);
        List<WithdrawTaxSummary> list = withdrawTaxSummaryMapper.selectList(wrapper);
        if (!CollectionUtils.isEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

    /**
     * 税费计算
     * -- 企业饷店提现算税入口
     * @param req
     * @param currMonthSummary ：唯一记录：身份证+扣税月份
     * @param resp
     * @throws BusinessException
     */
    private void taxCalc(WithdrawTaxCalcReq req, WithdrawTaxSummary currMonthSummary, WithdrawTaxCalcResp resp, WithdrawTaxDetail taxDetail, WithdrawTaxBO taxCheck) throws BusinessException {

        if (req.getAmount().compareTo(MIN_AMOUNT) < 0) {
            throw new BusinessException(ERR_ILLEGAL_AMOUNT, "提示：提现金额需大于" + MIN_AMOUNT + "元，请重新输入");
        }
        BigDecimal taxFee = BigDecimal.ZERO;
        // 累计申请提现金额、缴税金额
        BigDecimal accumAmount = req.getAmount();
        BigDecimal accumTaxFee = BigDecimal.ZERO;

        if (null != currMonthSummary) {
            accumAmount = accumAmount.add(currMonthSummary.getAmount());
            accumTaxFee = currMonthSummary.getTaxFee();
        }
        resp.setAccumAmount(accumAmount);
        taxDetail.setCurrMonthSummary(accumAmount);
        taxDetail.setFeeRate("0");

        BigDecimal withoutTaxAmount;
        String taxThreshold;
        //财务要求：企业饷店的店主店长提现tax起征点都是500，因此这里做了饷店、企业饷店tax起征点的隔离
        if (CustomerType.NM.getName().equals(req.getCustomerType()) || taxCheck.getAuthBO().includeSeller()) {
            String shopkeeperTaxThresholdTmp = shopkeeperTaxThreshold;
            if(!StringUtils.isEmpty(req.getChannelCode()) && "SAAS".equalsIgnoreCase(req.getChannelCode())){
                shopkeeperTaxThresholdTmp = tenantShopkeeperTaxThreshold;
            }
            withoutTaxAmount = new BigDecimal(shopkeeperTaxThresholdTmp);
            taxThreshold = shopkeeperTaxThresholdTmp;
        } else if (CustomerType.NMDL.getName().equals(req.getCustomerType())) {
            String agentTaxThresholdTmp = agentTaxThreshold;
            if(!StringUtils.isEmpty(req.getChannelCode()) && "SAAS".equalsIgnoreCase(req.getChannelCode())){
                agentTaxThresholdTmp = tenantAgentTaxThreshold;
            }
            withoutTaxAmount = new BigDecimal(agentTaxThresholdTmp);
            taxThreshold = agentTaxThresholdTmp;
        } else {
            throw new BusinessException(ERR_ILLEGAL_TAX, "用户身份角色错误");
        }
        if (accumAmount.compareTo(withoutTaxAmount) <= 0) {
            String tip = "您当月累计提现金额未大于" + taxThreshold + "元，本次无需扣除税费及服务费";
            resp.setTip(tip);
            resp.setTaxFee(taxFee);
            resp.setWithdraw(resp.getAmount().subtract(taxFee).subtract(taxDetail.getServiceFee()));
            if (Constant.BANK_WITHDRAW.equals(req.getWithdrawType()) && resp.getWithdraw().compareTo(BigDecimal.ZERO) < 1) {
                throw new BusinessException(ERR_ILLEGAL_TAX, "实际到账金额需大于0才可提现");
            }else if(Constant.WECHAT_WITHDRAW.equals(req.getWithdrawType()) && resp.getWithdraw().compareTo(new BigDecimal(wechatQuoteLimit)) < 0){
                throw new BusinessException(ERR_ILLEGAL_TAX, "实际到账金额需不少于" + wechatQuoteLimit + "元才可提现");
            }
            return;
        }

        for (int i = 1, len = AMOUNT_THRESHOLDS.length; i < len; i++) {
            if (accumAmount.compareTo(AMOUNT_THRESHOLDS[i - 1]) > 0 && accumAmount.compareTo(AMOUNT_THRESHOLDS[i]) <= 0) {
                String rate = AMOUNT_RATES[i - 1].multiply(BigDecimal.valueOf(100)).setScale(1, BigDecimal.ROUND_UP) + "%";
                taxDetail.setFeeRate(rate);
                String tip;
                if (i == 1) {
                    //tip = "1.根据提现规则，您当前提现需扣除" + rate + "的综合服务费及税金；";
                    tip = String.format(taxTip1, rate);
                } else {
                    //tip = "1.您本月累计提现金额>" + AMOUNT_THRESHOLDS[i - 1].intValue() / 10000 + "万元，全部提现扣除" + rate + "的综合服务费及税金；";
                    tip = String.format(taxTip1_2, (AMOUNT_THRESHOLDS[i - 1].intValue() / 10000),rate);
                }
                //String tip2 = "2.综合服务费及税金中" + rate + "为委托第三方服务商代办工商税务登记、月度申报和年检等商事服务费。";
                String tip2 = String.format(taxTip2, rate);
                resp.setTip(tip);
                resp.setTip2(tip2);
                if(ignoreTaxTip2){
                    resp.setTip2(null);
                }
                BigDecimal shouldTaxFee = accumAmount.multiply(AMOUNT_RATES[i - 1]);
                taxFee = shouldTaxFee.subtract(accumTaxFee).setScale(2, BigDecimal.ROUND_HALF_UP);
                //如果扣税<0说明上一次多扣税本次不扣税
                if (taxFee.compareTo(BigDecimal.ZERO) < 0) {
                    taxFee = BigDecimal.ZERO;
                    taxDetail.setFeeRate("0");
                }
                resp.setTaxFee(taxFee);
                resp.setWithdraw(resp.getAmount().subtract(taxFee).subtract(taxDetail.getServiceFee()));
                if (resp.getWithdraw().compareTo(BigDecimal.ZERO) < 0) {
                    resp.setWithdraw(BigDecimal.ZERO);
                }
                if (Constant.BANK_WITHDRAW.equals(req.getWithdrawType()) && resp.getWithdraw().compareTo(BigDecimal.ZERO) < 1) {
                    throw new BusinessException(ERR_ILLEGAL_TAX, "实际到账金额需大于0才可提现");
                }else if(Constant.WECHAT_WITHDRAW.equals(req.getWithdrawType()) && resp.getWithdraw().compareTo(new BigDecimal(wechatQuoteLimit)) < 0){
                    throw new BusinessException(ERR_ILLEGAL_TAX, "实际到账金额需不少于" + wechatQuoteLimit + "元才可提现");
                }
//                BigDecimal fee = taxFee.add(taxDetail.getServiceFee());
//                if (fee.compareTo(req.getAmount()) >= 0) {
//                    if (resp.getWithdraw().compareTo(BigDecimal.ZERO) < 0) {
//                        resp.setWithdraw(BigDecimal.ZERO);
//                    }
//                    throw new BusinessException(ERR_ILLEGAL_TAX, "实际到账金额需大于0才可提现");
//                }
                break;
            }
        }
        // 如果大于最高提现额
        if (accumAmount.compareTo(AMOUNT_THRESHOLDS[AMOUNT_THRESHOLDS.length - 1]) > 0) {
            throw new BusinessException(ERR_ILLEGAL_MAX, "提示：您本月累计提现金额>" + AMOUNT_THRESHOLDS[AMOUNT_THRESHOLDS.length - 1].intValue() / 10000 + "万元，本月无法继续提现，请下月再试");
        }
    }

    @Override
    public Result<WithdrawTaxCalcResp> getCurrMonthTaxSummary(String customerCode, String customerType) {
        Result<WithdrawTaxCalcResp> result = new Result<>();
        result.setSuccess(true);

        String month = DateUtils.format(new Date(), FORMAT_MONTH);
        //查询身份证
        WithdrawTaxDetail detail = this.getWithdrawTaxDetail(customerCode, month);
        if (null != detail) {
            WithdrawTaxSummary summary = getWithdrawTaxSummary(detail.getIdentifyNo(), month);
            WithdrawTaxCalcResp resp = new WithdrawTaxCalcResp();
            if (null != summary) {
                BeanUtils.copyProperties(summary, resp);
                result.setData(resp);
            }
        }
        return result;
    }

    private WithdrawTaxDetail getWithdrawTaxDetail(String customerCode, String month) {
        LambdaQueryWrapper<WithdrawTaxDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WithdrawTaxDetail::getCustomerCode, customerCode);
        if (!StringUtils.isEmpty(month)) {
            wrapper.eq(WithdrawTaxDetail::getMonth, month);
        }
        List<WithdrawTaxDetail> list = withdrawTaxDetailMapper.selectList(wrapper);
        if (!CollectionUtils.isEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

    @Override
    public ResultList<WithdrawTaxSummaryResp> queryWithdrawTaxSummaryByPage(Query<WithdrawTaxSummaryReq> query) {
        ResultList<WithdrawTaxSummaryResp> result = new ResultList<>();
        Page<WithdrawTaxSummary> page = new Page<>(query.getStart(), query.getPageSize());
        //查询条件组装
        LambdaQueryWrapper<WithdrawTaxSummary> wrapper = new LambdaQueryWrapper<>();
        WithdrawTaxSummaryReq req = query.getData();
        if (!StringUtils.isEmpty(req.getStartMonth()) && !StringUtils.isEmpty(req.getEndMonth())) {
            wrapper.between(WithdrawTaxSummary::getMonth, req.getStartMonth(), req.getEndMonth());
        }
        if (!StringUtils.isEmpty(req.getIdentifyCategory())) {
            wrapper.eq(WithdrawTaxSummary::getIdentifyCategory, req.getIdentifyCategory());
        }
        if (!StringUtils.isEmpty(req.getCustomerName())) {
            wrapper.eq(WithdrawTaxSummary::getIdentifyName, req.getCustomerName());
        }
        //查询身份证
        if (!StringUtils.isEmpty(req.getCustomerCode())) {
            WithdrawTaxDetail taxDetail = this.getWithdrawTaxDetail(req.getCustomerCode(), null);
            if (null != taxDetail) {
                wrapper.eq(WithdrawTaxSummary::getIdentifyNo, taxDetail.getIdentifyNo());
            } else {
                result.setTotal(0);
                return result;
            }
        }
        if (!StringUtils.isEmpty(req.getIdentifyNo())) {
            wrapper.eq(WithdrawTaxSummary::getIdentifyNo, req.getIdentifyNo());
        }
        //查询总记录数
        long total = withdrawTaxSummaryMapper.selectCount(wrapper);

        wrapper.orderByDesc(WithdrawTaxSummary::getId);
        //分页查询
        Page<WithdrawTaxSummary> pageResult = withdrawTaxSummaryMapper.selectPage(page, wrapper);

        List<WithdrawTaxSummaryResp> dataList = pageResult.getRecords().stream().map(summary -> {
            WithdrawTaxSummaryResp resp = new WithdrawTaxSummaryResp();
            BeanUtils.copyProperties(summary, resp);
            resp.setSummaryId(summary.getId());
            return resp;
        }).collect(Collectors.toList());
        result.setDatalist(dataList);
        result.setTotal(total);
        return result;
    }

    @Override
    public ResultList<WithdrawTaxDetailResp> queryWithdrawTaxDetailByPage(Query<WithdrawTaxDetailReq> query) {
        ResultList<WithdrawTaxDetailResp> result = new ResultList<>();

        WithdrawTaxSummary summary = withdrawTaxSummaryMapper.selectById(query.getData().getSummaryId());
        Page<WithdrawTaxDetail> page = new Page<>(query.getStart(), query.getPageSize());
        LambdaQueryWrapper<WithdrawTaxDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WithdrawTaxDetail::getIdentifyNo, summary.getIdentifyNo())
                .eq(WithdrawTaxDetail::getMonth, summary.getMonth());
        //查询总数
        long total = withdrawTaxDetailMapper.selectCount(wrapper);
        wrapper.orderByDesc(WithdrawTaxDetail::getId);
        //分页查询
        Page<WithdrawTaxDetail> pageResult = withdrawTaxDetailMapper.selectPage(page, wrapper);
        List<WithdrawTaxDetailResp> dataList = pageResult.getRecords().stream().map(detail -> {
            WithdrawTaxDetailResp resp = new WithdrawTaxDetailResp();
            BeanUtils.copyProperties(detail, resp);
            resp.setCreateTimeStr(DateUtils.format(resp.getCreateTime()));
            return resp;
        }).collect(Collectors.toList());
        result.setDatalist(dataList);
        result.setTotal(total);
        return result;
    }

    @Override
    public WithdrawTaxDetail applyWithdrawTax(WithdrawApplyRecord record) throws BusinessException {
        String month = DateUtils.format(new Date(), FORMAT_MONTH);
        //判断该用户是否收取税费
        WithdrawTaxBO taxCheck = this.checkWithdrawTax(record.getCustomerType(), record.getCustomerCode(), true, true, record.getIdentifyNo(), month);
        record.setCustomerGrade(taxCheck.getAuthBO().getGrade().name());
        //如果本次提现收取手续费在税费明细中扣除此项
        WithdrawTaxDetail taxDetail = new WithdrawTaxDetail();
        BeanUtils.copyProperties(record, taxDetail);
        taxDetail.setIdentifyCategory(taxCheck.getAuthBO().getGrade().name());
        taxDetail.setMonth(month);
        taxDetail.setUserId(taxCheck.getAuthBO().getUserId());
        taxDetail.setSellerCustomerCode(taxCheck.getAuthBO().getSellerCustomerCode());
        taxDetail.setTaxId(taxCheck.getAuthBO().getTaxId());
        taxDetail.setCreditCode(taxCheck.getAuthBO().getCreditCode());
        taxDetail.setCorpName(taxCheck.getAuthBO().getCorpName());
        taxDetail.setCorpRepName(taxCheck.getAuthBO().getCorpRepName());
        taxDetail.setServiceFee(null == record.getServiceAmount() ? BigDecimal.ZERO : record.getServiceAmount());
        taxDetail.setWithdraw(record.getAmount().subtract(taxDetail.getServiceFee()));
        taxDetail.setTaxFee(BigDecimal.ZERO);
        if (taxCheck.getIsTax()) {
            WithdrawTaxCalcReq req = new WithdrawTaxCalcReq();
            BeanUtils.copyProperties(record, req);
            //验证参数
            this.checkWithdrawTaxParam(req);
            // 查询当月提现汇总
            WithdrawTaxSummary currMonthSummary = this.getWithdrawTaxSummary(req.getIdentifyNo(), month);
            WithdrawTaxCalcResp resp = new WithdrawTaxCalcResp();
            resp.setAmount(req.getAmount());
            // 计算税费
            this.taxCalc(req, currMonthSummary, resp, taxDetail, taxCheck);
            taxDetail.setTaxFee(resp.getTaxFee());
            taxDetail.setWithdraw(resp.getWithdraw());
            if (null != currMonthSummary) {
                //用于判断汇总记录是否存在
                taxDetail.setId(currMonthSummary.getId());
            }
            String remark;
            //手续费为0，不展示
            if (taxDetail.getServiceFee() != null && taxDetail.getServiceFee().compareTo(BigDecimal.ZERO) > 0) {
                remark = "到账：" + taxDetail.getWithdraw() + "，代扣税费：" + taxDetail.getTaxFee() + "，银行手续费：" + taxDetail.getServiceFee();
            } else {
                remark = "到账：" + taxDetail.getWithdraw() + "，代扣税费：" + taxDetail.getTaxFee();
            }
            taxDetail.setRemark(remark);
        } else {
            //企业和个体工商户提现存入税费明细表，用于财务对账
            if (taxDetail.getWithdraw().compareTo(BigDecimal.ZERO) < 1) {
                throw new BusinessException(ERR_ILLEGAL_TAX, "实际到账金额需大于0才可提现");
            }
            if (taxDetail.getServiceFee() != null && taxDetail.getServiceFee().compareTo(BigDecimal.ZERO) > 0) {
                taxDetail.setRemark("到账：" + taxDetail.getWithdraw() + "，银行手续费：" + taxDetail.getServiceFee());
            }
            this.taxDetail(taxDetail);
        }
        record.setRemark(taxDetail.getRemark());
        return taxDetail;
    }

    /**
     * 优化 -- 这里只计算税费，不落地数据
     * @param record
     * @return
     * @throws BusinessException
     */
    public WithdrawTaxDetail applyWithdrawTax(TenantWithdrawApply record) throws BusinessException {
        //String month = DateUtils.format(new Date(), FORMAT_MONTH);
        String month = record.getMonth();
        //判断该用户是否收取税费
        WithdrawTaxBO taxCheck = this.checkWithdrawTax(record.getCustomerType(), record.getCustomerCode(), true, true, record.getIdentifyNo(), month);
        record.setCustomerGrade(taxCheck.getAuthBO().getGrade().name());
        //如果本次提现收取手续费在税费明细中扣除此项
        WithdrawTaxDetail taxDetail = new WithdrawTaxDetail();
        BeanUtils.copyProperties(record, taxDetail);
        taxDetail.setIdentifyCategory(taxCheck.getAuthBO().getGrade().name());
        taxDetail.setMonth(record.getMonth());
        taxDetail.setUserId(taxCheck.getAuthBO().getUserId());
        taxDetail.setSellerCustomerCode(taxCheck.getAuthBO().getSellerCustomerCode());
        taxDetail.setTaxId(taxCheck.getAuthBO().getTaxId());
        taxDetail.setCreditCode(taxCheck.getAuthBO().getCreditCode());
        taxDetail.setCorpName(taxCheck.getAuthBO().getCorpName());
        taxDetail.setCorpRepName(taxCheck.getAuthBO().getCorpRepName());
        taxDetail.setServiceFee(null == record.getServiceAmount() ? BigDecimal.ZERO : record.getServiceAmount());
        //先剔除手续费
        taxDetail.setWithdraw(record.getAmount().subtract(taxDetail.getServiceFee()));
        taxDetail.setTaxFee(BigDecimal.ZERO);
        if (taxCheck.getIsTax()) {
            WithdrawTaxCalcReq req = new WithdrawTaxCalcReq();
            BeanUtils.copyProperties(record, req);
            //算费的时候区分企业饷店和饷店
            req.setChannelCode("SAAS");
            //验证参数
            this.checkWithdrawTaxParam(req);
            // 查询当月提现汇总
            WithdrawTaxSummary currMonthSummary = this.getWithdrawTaxSummary(req.getIdentifyNo(), month);
            WithdrawTaxCalcResp resp = new WithdrawTaxCalcResp();
            resp.setAmount(req.getAmount());
            // 计算税费
            this.taxCalc(req, currMonthSummary, resp, taxDetail, taxCheck);
            taxDetail.setTaxFee(resp.getTaxFee());
            taxDetail.setWithdraw(resp.getWithdraw());
            if (null != currMonthSummary) {
                //用于判断汇总记录是否存在
                taxDetail.setId(currMonthSummary.getId());
            }
            String remark;
            //手续费为0，不展示
            if (taxDetail.getServiceFee() != null && taxDetail.getServiceFee().compareTo(BigDecimal.ZERO) > 0) {
                remark = "到账：" + taxDetail.getWithdraw() + "，代扣税费：" + taxDetail.getTaxFee() + "，银行手续费：" + taxDetail.getServiceFee();
            } else {
                remark = "到账：" + taxDetail.getWithdraw() + "，代扣税费：" + taxDetail.getTaxFee();
            }
            taxDetail.setRemark(remark);
        } else {
            //企业和个体工商户提现存入税费明细表，用于财务对账
            if (taxDetail.getWithdraw().compareTo(BigDecimal.ZERO) < 1) {
                throw new BusinessException(ERR_ILLEGAL_TAX, "实际到账金额需大于0才可提现");
            }
            if (taxDetail.getServiceFee() != null && taxDetail.getServiceFee().compareTo(BigDecimal.ZERO) > 0) {
                taxDetail.setRemark("到账：" + taxDetail.getWithdraw() + "，银行手续费：" + taxDetail.getServiceFee());
            }
            //this.taxDetail(taxDetail); 后置，统一做数据库的处理
        }
        record.setRemark(taxDetail.getRemark());

        return taxDetail;
    }

    @Override
    public Result<Void> withdrawTax(WithdrawTaxDetail taxDetail) {
        Result<Void> result = Result.success();
        //非企业认证(企业需自行纳税)才会扣税(我司月度代理提现用户纳税)
        if (null != taxDetail
                && !MemberGrade.PERSON_AUTH.getName().equals(taxDetail.getIdentifyCategory())
                && !MemberGrade.ENTERPRISE_AUTH.getName().equals(taxDetail.getIdentifyCategory())) {
            //插入去重表去重
            Result<String> amountKeyResult = this.getWithdrawTaxKey(taxDetail.getIdentifyNo(), taxDetail.getMonth());
            if (!amountKeyResult.getSuccess()) {
                result.setSuccess(false);
                result.setMessage(amountKeyResult.getMessage());
                return result;
            }
            String amountKey = amountKeyResult.getData();
            //使用分布式锁
            RedisLock lock = new RedisLock(redisTemplate, amountKey, lockExpireTime);
            if (!lock.tryLock()) {
                Logger.warn("withdrawTax RedisLock is lock, lockKey:{}", amountKey);
                return Results.error(ResponseEnum.ACCORE_101511);
            }
            try {
                //更新税费汇总表
                result = this.taxSummary(taxDetail);
                if (!result.getSuccess()) {
                    return result;
                }
                //插入扣税明细表
                result = this.taxDetail(taxDetail);
            } catch (Exception e) {
                Logger.warn("withdrawTax exception:", e);
                return Results.error(ResponseEnum.ACCORE_101512);
            } finally {
                lock.unlock();
            }
        }
        return result;
    }

    @Override
    public Result<Void> withdrawServiceFee(WithdrawApplyRecord record, WithdrawTaxDetail taxDetail, WithdrawServicefeeSummary withdrawServicefeeSummary) {
        Result<Void> result = Result.success();
        withdrawServicefeeSummary.setServiceFee(null == record.getServiceAmount() ? BigDecimal.ZERO : record.getServiceAmount());
        try {
            if (null != withdrawServicefeeSummary.getId()) {
                //更改
                withdrawServicefeeSummaryMapper.addAmount(withdrawServicefeeSummary);
            } else {
                //新增
                if (null != taxDetail) {
                    BeanUtils.copyProperties(taxDetail, withdrawServicefeeSummary);
                }
                withdrawServicefeeSummary.setIdentifyName(record.getCustomerName());
                withdrawServicefeeSummary.setIdentifyNo(record.getIdentifyNo());
                withdrawServicefeeSummaryMapper.insert(withdrawServicefeeSummary);
            }
        } catch (Exception e) {
            Logger.warn("withdrawServiceFee exception:{}", e);
            return Results.error(ResponseEnum.ACCORE_101516);
        }
        return result;
    }

    @Override
    public Result<Void> tenantWithdrawServiceFee(TenantWithdrawApply apply, WithdrawServicefeeSummary withdrawServicefeeSummary) {
        Result<Void> result = Result.success();
        withdrawServicefeeSummary.setServiceFee(null == apply.getServiceAmount() ? BigDecimal.ZERO : apply.getServiceAmount());
        try {
            if(null != withdrawServicefeeSummary.getId()){
                //更改
                withdrawServicefeeSummaryMapper.addAmount(withdrawServicefeeSummary);
            }else {
                withdrawServicefeeSummary.setIdentifyName(apply.getCustomerName());
                withdrawServicefeeSummary.setIdentifyNo(apply.getIdentifyNo());
                withdrawServicefeeSummaryMapper.insert(withdrawServicefeeSummary);
            }
        }catch (Exception e){
            Logger.warn("tenantWithdrawServiceFee exception:{}", e);
            return Results.error(ResponseEnum.ACCORE_101516);
        }
        return result;
    }

    public Result<Void> taxDetail(WithdrawTaxDetail taxDetail) {
        Result<Void> result = Result.success();
        taxDetail.setStatus(ApplyStatus.INIT.name());
        int dbNum = withdrawTaxDetailMapper.insert(taxDetail);
        if (dbNum != 1) {
            result.setSuccess(Boolean.FALSE);
            result.setMessage("插入税费明细表失败");
            return result;
        }
        return result;
    }


    public Result<Void> taxSummary(WithdrawTaxDetail taxDetail) {
        Result<Void> result = Result.success();
        WithdrawTaxSummary summary = new WithdrawTaxSummary();
        BeanUtils.copyProperties(taxDetail, summary);
        summary.setIdentifyName(taxDetail.getCustomerName());
        int dbNum;
        //已存在则更新
        if (null != taxDetail.getId()) {
            dbNum = withdrawTaxSummaryMapper.addAmount(summary);
        } else {
            dbNum = withdrawTaxSummaryMapper.insert(summary);
        }
        if (dbNum != 1) {
            result.setSuccess(Boolean.FALSE);
            result.setMessage("更新税费汇总表失败");
            return result;
        }
        return result;
    }

    public Result<String> getWithdrawTaxKey(String encrptIdNo, String month) {
        Result<String> result = new Result<>();
        result.setSuccess(true);
        String decrptIdentifyNo;
        try {
            decrptIdentifyNo = this.decryptIdentifyNo(encrptIdNo);
            result.setData("WITHDRAWTAX" + decrptIdentifyNo + month);
        } catch (BusinessException e) {
            Logger.error("withdrawTax 解密身份证失败", e);
            result.setSuccess(false);
            result.setMessage("解密身份证失败");
            return result;
        }
        return result;
    }

    private String decryptIdentifyNo(String encryptIdentifyNo) throws BusinessException {
        try {
            com.akucun.common.Result<String> denCrypt = CodeUtils.decrypt(encryptIdentifyNo);
            if (!denCrypt.isSuccess() || org.apache.commons.lang.StringUtils.isEmpty(denCrypt.getData())) {
                Logger.error("身份证号解密失败:" + encryptIdentifyNo, DataMask.toJSONString(denCrypt));
                throw new BusinessException("身份证号解密失败");
            }
            return denCrypt.getData();
        } catch (Exception e) {
            Logger.error("身份证号解密失败:" + encryptIdentifyNo, e);
            throw new BusinessException("身份证号解密失败");
        }
    }

    @Override
    public void withdrawSuccForTax(WithdrawApplyRecord record) {
        WithdrawTaxBO taxCheck = this.checkWithdrawTax(record.getCustomerType(), record.getCustomerCode(), false, false, null, null);
        WithdrawTaxDetail taxDetail = this.getWithdrawTaxDetail(record.getWithdrawNo());
        if (null == taxDetail) {
            Logger.warn("Withdraw Succ Tax is empty!withdrawNo:{}", record.getWithdrawNo());
            return;
        }
        if (taxCheck.getIsTax()) {
            //查询提现扣税明细
            if (ApplyStatus.DOING.name().equals(taxDetail.getStatus()) || ApplyStatus.SUCC.name().equals(taxDetail.getStatus())) {
                return;
            }
            if (decouplingConfig.decouplingSwitch(record.getCustomerCode(), record.getCustomerType())) {
                taxDetail.setStatus(ApplyStatus.SUCC.name());
                withdrawTaxDetailMapper.updateById(taxDetail);
            } else {
                String uniqueId = "WITHDRAWTAX_MEMBERTRADE" + record.getWithdrawNo();
                Result<Void> result = this.duplicateCheckService.checkDuplicate(uniqueId, false);
                if (result.getSuccess()) {
                    //更新状态为处理中
                    taxDetail.setStatus(ApplyStatus.DOING.name());
                    withdrawTaxDetailMapper.updateById(taxDetail);
                    //发起会员交易扣税
                    Result<Void> tradeResult = this.withdrawTaxTrade(taxDetail);
                    //失败重试
                    if (!tradeResult.getSuccess()) {
                        PostActionItemBO itemBO = PostActionItemBO.builder()
                                .bizId(record.getWithdrawNo())
                                .paramObject(taxDetail)
                                .remark("提现扣税会员交易任务")
                                .actionType(PostActionTypes.WITHDRAWTAX_MEMBERTRADE.getName())
                                .status(PostActionExecStatus.EXECUTE.value())
                                .nextRetryTime(LocalDateTime.now().plusSeconds(5 * 60))
                                .retryNums(0)
                                .build();
                        postActionService.addAction(itemBO);
                    }
                } else {
                    Logger.warn("withdrawSuccForTax succ duplicate: {}", record.getWithdrawNo());
                }
            }
        } else {
            if (MemberGrade.ENTERPRISE_AUTH == taxCheck.getAuthBO().getGrade()
                    || MemberGrade.PERSON_AUTH == taxCheck.getAuthBO().getGrade()) {
                taxDetail.setStatus(ApplyStatus.SUCC.name());
                withdrawTaxDetailMapper.updateById(taxDetail);
            }
        }
    }

    @Override
    public void wechatWithdrawSuccForTax(WithdrawApplyRecord record) {
        WithdrawTaxBO taxCheck = this.checkWithdrawTax(record.getCustomerType(), record.getCustomerCode(), false, false, null, null);
        if (taxCheck.getIsTax()) {
            //查询提现扣税明细
            WithdrawTaxDetail taxDetail = this.getWithdrawTaxDetail(record.getWithdrawNo());
            if (null == taxDetail) {
                Logger.warn("wechatWithdrawSuccForTax Succ Tax is empty!withdrawNo:{}", record.getWithdrawNo());
                return;
            }
            if (ApplyStatus.DOING.name().equals(taxDetail.getStatus()) || ApplyStatus.SUCC.name().equals(taxDetail.getStatus())) {
                return;
            }

            if (decouplingConfig.decouplingSwitch(record.getCustomerCode(), record.getCustomerType())) {
                taxDetail.setStatus(ApplyStatus.SUCC.name());
                withdrawTaxDetailMapper.updateById(taxDetail);
            } else {
                String uniqueId = "WECHAT_WITHDRAW_TAX_SUCC" + record.getWithdrawNo();
                Result<Void> result = this.duplicateCheckService.checkDuplicate(uniqueId, false);
                if (result.getSuccess()) {
                    //更新状态为处理中
                    taxDetail.setStatus(ApplyStatus.DOING.name());
                    withdrawTaxDetailMapper.updateById(taxDetail);
                    if (taxDetail.getTaxFee().compareTo(BigDecimal.ZERO) > 0) {
                        //计入平台记账账户
                        TradeInfo tradeInfo = new TradeInfo();
                        tradeInfo.setAccountTypeKey(AccountKeyConstants.PT.getName());
                        tradeInfo.setCustomerCode(platformAccountNo);
                        tradeInfo.setTradeType(DetailTypeConstants.TRADE_TYPE_245.name());
                        tradeInfo.setAmount(taxDetail.getTaxFee());
                        tradeInfo.setSourceBillNo(record.getWithdrawNo());
                        tradeInfo.setTradeNo(record.getWithdrawNo());
                        tradeInfo.setRemark(record.getRemark());
                        com.akucun.common.Result<Void> result1 = accountCenterService.dealTrade(tradeInfo);
                        if (result1 == null || !result1.isSuccess()) {
                            PostActionItemBO itemBO = PostActionItemBO.builder()
                                    .bizId(tradeInfo.getSourceBillNo())
                                    .paramObject(tradeInfo)
                                    .remark("提现扣税记入平台账户")
                                    .actionType(PostActionTypes.ACCOUNT_CENTER_COMPENSATE.getName())
                                    .status(PostActionExecStatus.EXECUTE.value()).build();
                            postActionService.addAction(itemBO);
                        }
                    }else{
                        Logger.warn("Withdraw Tax dealTrade ignore! withdrawNo:" + taxDetail.getWithdrawNo());
                    }
                    //不扣税也更新状态成功
                    taxDetail.setStatus(ApplyStatus.SUCC.name());
                    withdrawTaxDetailMapper.updateById(taxDetail);
                } else {
                    Logger.warn("wechatWithdrawSuccForTax succ duplicate, withdrawNo:{}", record.getWithdrawNo());
                }
            }
        }else {
            Logger.warn("wechatWithdrawSuccForTax abnormal, should not enter here!!! customerCode: {}，withdrawNo:{}", record.getCustomerCode(), record.getWithdrawNo());
        }
    }

    @Override
    public void tenantWithdrawFailForTax(TenantWithdrawApply apply, String failReason){
        WithdrawApplyRecord record = new WithdrawApplyRecord();
        record.setCustomerCode(apply.getCustomerCode());
        record.setCustomerType(apply.getCustomerType());
        record.setMonth(apply.getMonth());
        record.setWithdrawNo(apply.getWithdrawNo());

        //复用 - 逆向提现tax扣款逻辑
        withdrawFailForTax(record, failReason);
    }


    @Override
    public void withdrawFailForTax(WithdrawApplyRecord record, String failReason) {
        WithdrawTaxBO taxCheck = this.checkWithdrawTax(record.getCustomerType(), record.getCustomerCode(), false, false, null, null);
        //查询提现扣税明细
        WithdrawTaxDetail detail = this.getWithdrawTaxDetail(record.getWithdrawNo());
        if (null == detail) {
            Logger.warn("withdrawFailForTax detail is empty! withdrawNo:{}", record.getWithdrawNo());
            return;
        }
        if (!StringUtils.isEmpty(failReason) && failReason.length() > 500) {
            failReason = failReason.substring(0, 500);
        }
        if (taxCheck.getIsTax()) {
            if (ApplyStatus.FAIL.name().equals(detail.getStatus())) {
                Logger.warn("withdrawFailForTax detail already failed! withdrawNo:{}", record.getWithdrawNo());
                return;
            }
            WithdrawTaxSummary summary = this.getWithdrawTaxSummary(detail.getIdentifyNo(), detail.getMonth());
            if (null == summary) {
                Logger.warn("withdrawFailForTax summary is empty! customerCode:{}", record.getCustomerCode());
                return;
            }

            String uniqueId = "WITHDRAWTAX" + record.getWithdrawNo();
            Result<Void> result = this.duplicateCheckService.checkDuplicate(uniqueId, false);
            if (result.getSuccess()) {
                detail.setStatus(ApplyStatus.FAIL.name());
                detail.setRemark(failReason);
                withdrawTaxDetailMapper.updateById(detail);

                boolean needRetry = false;
                Result<String> amountKeyResult = this.getWithdrawTaxKey(detail.getIdentifyNo(), detail.getMonth());
                if (!amountKeyResult.getSuccess()) {
                    Logger.error("withdrawFailForTax getWithdrawTaxKey fail");
                    needRetry = true;
                } else {
                    String amountKey = amountKeyResult.getData();
                    RedisLock lock = new RedisLock(redisTemplate, amountKey, lockExpireTime);
                    if (!lock.tryLock()) {
                        needRetry = true;
                        Logger.warn("withdrawFailForTax RedisLock is lock, lockKey:{}, withdrawNo:{}", amountKey, record.getWithdrawNo());
                    }
                    try {
                        summary.setAmount(detail.getAmount());
                        summary.setTaxFee(detail.getTaxFee());
                        summary.setWithdraw(detail.getWithdraw());
                        summary.setServiceFee(detail.getServiceFee());
                        withdrawTaxSummaryMapper.subtractAmount(summary);
                    } catch (Exception e) {
                        Logger.error("withdrawFailForTax error:", e);
                        needRetry = true;
                    } finally {
                        lock.unlock();
                    }
                }
                if (needRetry) {
                    addWithdrawFailRetrySchedule(detail);
                }
                
            } else {
                Logger.info("withdrawFailForTax fail duplicate:{}" + record.getWithdrawNo());
            }
            Logger.info("withdrawFailForTax finished，customerCode:{}", record.getCustomerCode());
        } else {
            if (MemberGrade.ENTERPRISE_AUTH == taxCheck.getAuthBO().getGrade()
                    || MemberGrade.PERSON_AUTH == taxCheck.getAuthBO().getGrade()) {
                detail.setStatus(ApplyStatus.FAIL.name());
                detail.setRemark(failReason);
                withdrawTaxDetailMapper.updateById(detail);
            }
        }
    }

    @Override
    public void withdrawFailForServiceFee(WithdrawApplyRecord record) {
        if (null == record.getServiceAmount()){
            return;
        }
        //查询手续费汇总数据
        WithdrawServicefeeSummary withdrawServicefeeSummary = getServiceFeeSum(record);
        if (null == withdrawServicefeeSummary){
            Logger.info("withdrawFailForServiceFee 当月未查询到手续费汇总相关记录：{}",record.getCustomerCode());
            return;
        }
        String uniqueId = "WITHDRAWSERVICEFEE" + record.getWithdrawNo();
        Result<Void> result = this.duplicateCheckService.checkDuplicate(uniqueId, false);
        if (result.getSuccess()){
            boolean needRetry = false;
            String amountKey = "WITHDRAWSERVICEFEE" + withdrawServicefeeSummary.getIdentifyNo() + withdrawServicefeeSummary.getMonth();
            RedisLock lock = new RedisLock(redisTemplate, amountKey, lockExpireTime);
            if (!lock.tryLock()) {
                needRetry = true;
                Logger.warn("withdrawFailForServiceFee RedisLock is lock, lockKey:{}, withdrawNo:{}", amountKey, record.getWithdrawNo());
            }
            try {
                withdrawServicefeeSummary.setServiceFee(null == record.getServiceAmount() ? BigDecimal.ZERO : record.getServiceAmount());
                withdrawServicefeeSummaryMapper.subtractAmount(withdrawServicefeeSummary);
            } catch (Exception e) {
                Logger.error("withdrawFailForTax error:", e);
                needRetry = true;
            } finally {
                lock.unlock();
            }
            if (needRetry) {
                addWithdrawServiceFeeFailRetrySchedule(record);
            }

        }else {
            Logger.info("withdrawFailForServiceFee fail duplicate:{}" + record.getWithdrawNo());
        }

    }

    /**
     * 租户店主店长提现成功-税费状态处理
     * @param apply
     */
    public void tenantWithdrawSussForTax(TenantWithdrawApply apply){
        WithdrawTaxDetail taxDetail = this.getWithdrawTaxDetail(apply.getWithdrawNo());

        if (null == taxDetail) {
            Logger.warn("tenantWithdrawSussForTax Succ Tax is empty!withdrawNo:{}", apply.getWithdrawNo());
            return;
        }

        if (ApplyStatus.SUCC.name().equalsIgnoreCase(taxDetail.getStatus())) {
            Logger.warn("tenantWithdrawSussForTax : record status is suss!withdrawNo:{}", apply.getWithdrawNo());
            return;
        }

        taxDetail.setStatus(ApplyStatus.SUCC.name());
        withdrawTaxDetailMapper.updateById(taxDetail);
    }

    @Override
    public void tenantWithdrawFailForServiceFee(TenantWithdrawApply apply) {
        WithdrawApplyRecord record = new WithdrawApplyRecord();
        record.setIdentifyNo(apply.getIdentifyNo());
        record.setWithdrawNo(apply.getWithdrawNo());
        if (apply.getServiceAmount() == null){
            return;
        }
        //查询手续费汇总数据：存在问题，提现月份不能取当前时间 TODO
        WithdrawServicefeeSummary withdrawServicefeeSummary = getServiceFeeSum(record);
        if (null == withdrawServicefeeSummary){
            Logger.info("tenantWithdrawFailForServiceFee 当月未查询到手续费汇总相关记录：{}",record.getCustomerCode());
            return;
        }
        String uniqueId = "WITHDRAW_TEN_SERVICEFEE" + record.getWithdrawNo();
        Result<Void> result = this.duplicateCheckService.checkDuplicate(uniqueId, false);
        if (result.getSuccess()){
            boolean needRetry = false;
            String amountKey = "WITHDRAW_TEN_SERVICEFEE" + withdrawServicefeeSummary.getIdentifyNo() + withdrawServicefeeSummary.getMonth();
            RedisLock lock = new RedisLock(redisTemplate, amountKey, lockExpireTime);
            if (!lock.tryLock()) {
                needRetry = true;
                Logger.warn("tenantWithdrawFailForServiceFee RedisLock is lock, lockKey:{}, withdrawNo:{}", amountKey, record.getWithdrawNo());
            }
            try {
                withdrawServicefeeSummary.setServiceFee(null == apply.getServiceAmount() ? BigDecimal.ZERO : apply.getServiceAmount());
                withdrawServicefeeSummaryMapper.subtractAmount(withdrawServicefeeSummary);
            } catch (Exception e) {
                Logger.error("tenantWithdrawFailForServiceFee error:", e);
                needRetry = true;
            } finally {
                lock.unlock();
            }
            if (needRetry) {
                addWithdrawServiceFeeFailRetrySchedule(record);
            }

        }else {
            Logger.info("tenantWithdrawFailForServiceFee fail duplicate:{}", record.getWithdrawNo());
        }



    }

    private WithdrawServicefeeSummary getServiceFeeSum(WithdrawApplyRecord record) {
        String month = DateUtils.format(new Date(), FORMAT_MONTH);
        LambdaQueryWrapper<WithdrawServicefeeSummary> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WithdrawServicefeeSummary::getIdentifyNo, record.getIdentifyNo())
                .eq(WithdrawServicefeeSummary::getMonth, month);
        List<WithdrawServicefeeSummary> list = withdrawServicefeeSummaryMapper.selectList(wrapper);
        if (!CollectionUtils.isEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

    private void addWithdrawFailRetrySchedule(WithdrawTaxDetail detail) {
        try {
            PostActionItemBO itemBO = PostActionItemBO.builder()
                    .bizId(detail.getWithdrawNo())
                    .paramObject(detail)
                    .remark("提现扣税会员交易任务")
                    .actionType(PostActionTypes.WITHDRAWTAX_FAILAMOUNT.getName())
                    .status(PostActionExecStatus.EXECUTE.value())
                    .nextRetryTime(LocalDateTime.now().plusSeconds(5 * 60))
                    .retryNums(0)
                    .build();
            postActionService.addAction(itemBO);

        } catch (Exception e) {
            Logger.error("addWithdrawFailRetrySchedule withdrawNo:{}, exception:", detail.getWithdrawNo(), e.getMessage());
        }
    }

    private void addWithdrawServiceFeeFailRetrySchedule(WithdrawApplyRecord record) {
        try {
            PostActionItemBO itemBO = PostActionItemBO.builder()
                    .bizId(record.getWithdrawNo())
                    .paramObject(record)
                    .remark("提现扣手续费失败金额更新任务")
                    .actionType(PostActionTypes.WITHDRAWSERVICEFEE_FAILAMOUNT.getName())
                    .status(PostActionExecStatus.EXECUTE.value())
                    .nextRetryTime(LocalDateTime.now().plusSeconds(5 * 60))
                    .retryNums(0)
                    .build();
            postActionService.addAction(itemBO);

        } catch (Exception e) {
            Logger.error("addWithdrawFailRetrySchedule withdrawNo:{}, exception:", record.getWithdrawNo(), e.getMessage());
        }
    }

    @Override
    public Result<Void> withdrawTaxTrade(WithdrawTaxDetail withdrawTaxDetail) {
        Result<Void> result = Result.success();
        if (withdrawTaxDetail.getTaxFee().compareTo(BigDecimal.ZERO) > 0) {
            DealTradeVO dealTrade = new DealTradeVO();
            dealTrade.setRemark(WITHDRAW_TAX_REMARK);
            dealTrade.setTranAmount(withdrawTaxDetail.getTaxFee());
            dealTrade.setOrderNo("SF" + withdrawTaxDetail.getWithdrawNo());
            dealTrade.setCustomerCode(withdrawTaxDetail.getCustomerCode());
            dealTrade.setCustomerType(withdrawTaxDetail.getCustomerType());
            com.akucun.fps.common.entity.Result<Void> res = memberServiceApi.dealTrade(dealTrade);
            //更新提现税费状态成功
            if (res.isSuccess()) {
                withdrawTaxDetail.setStatus(ApplyStatus.SUCC.name());
                withdrawTaxDetailMapper.updateById(withdrawTaxDetail);
            } else {
                Logger.error("Withdraw Tax dealTrade fail!{}", DataMask.toJSONString(res));
                result.setSuccess(false);
                result.setCode(res.getErrorCode());
                result.setMessage(res.getErrorMessage());
            }
        } else {
            //不扣税也更新状态成功
            Logger.warn("Withdraw Tax dealTrade ignore!withdrawNo:" + withdrawTaxDetail.getWithdrawNo());
            withdrawTaxDetail.setStatus(ApplyStatus.SUCC.name());
            withdrawTaxDetailMapper.updateById(withdrawTaxDetail);
        }
        return result;
    }

    @Override
    public WithdrawTaxDetail getWithdrawTaxDetail(String withdrawNo) {
        LambdaQueryWrapper<WithdrawTaxDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WithdrawTaxDetail::getWithdrawNo, withdrawNo)
                .orderByDesc(WithdrawTaxDetail::getId);
        List<WithdrawTaxDetail> list = withdrawTaxDetailMapper.selectList(wrapper);
        if (!CollectionUtils.isEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

    @Override
    public Result<Void> withdrawTaxFailAmount(WithdrawTaxDetail detail) {
        Result<Void> result = Result.success();
        Result<String> amountKeyResult = this.getWithdrawTaxKey(detail.getIdentifyNo(), detail.getMonth());
        if (!amountKeyResult.getSuccess()) {
            Logger.error("withdrawTaxFailAmount getWithdrawTaxKey fail, withdrawNo:{}", detail.getWithdrawNo());
            result.setSuccess(false);
            result.setMessage(amountKeyResult.getMessage());
            return result;
        }
        String amountKey = amountKeyResult.getData();
        RedisLock lock = new RedisLock(redisTemplate, amountKey, lockExpireTime);
        if (!lock.tryLock()) {
            Logger.warn("withdrawFailForTax RedisLock is lock, lockKey:{}, withdrawNo:{}", amountKey, detail.getWithdrawNo());
            return Results.error("并发减税异常");
        }
        try {
            WithdrawTaxSummary summary = this.getWithdrawTaxSummary(detail.getIdentifyNo(), detail.getMonth());
            if (summary != null) {
                summary.setAmount(detail.getAmount());
                summary.setTaxFee(detail.getTaxFee());
                summary.setWithdraw(detail.getWithdraw());
                withdrawTaxSummaryMapper.subtractAmount(summary);
            }
        } catch (Exception e) {
            Logger.error("withdrawFailForTax error:", e);
            result.setSuccess(false);
            result.setMessage(e.getMessage());
        } finally {
            lock.unlock();
        }
        return result;
    }

    @Override
    public Result<Void> withdrawServiceFeeFailAmount(WithdrawApplyRecord record) {
        Result<Void> result = Result.success();
        //查询手续费汇总数据
        WithdrawServicefeeSummary withdrawServicefeeSummary = getServiceFeeSum(record);
        if (null == withdrawServicefeeSummary){
            Logger.info("withdrawServiceFeeFailAmount 当月未查询到手续费汇总相关记录：{}",record.getCustomerCode());
            return result;
        }
        String amountKey = "WITHDRAWSERVICEFEE" + withdrawServicefeeSummary.getIdentifyNo() + withdrawServicefeeSummary.getMonth();
        RedisLock lock = new RedisLock(redisTemplate, amountKey, lockExpireTime);
        if (!lock.tryLock()) {
            Logger.warn("withdrawServiceFeeFailAmount RedisLock is lock, lockKey:{}, withdrawNo:{}", amountKey, record.getWithdrawNo());
            result.setSuccess(false);
            result.setMessage("手续费回退并发");
            return result;
        }
        try {
            withdrawServicefeeSummary.setServiceFee(null == record.getServiceAmount() ? BigDecimal.ZERO : record.getServiceAmount());
            withdrawServicefeeSummaryMapper.subtractAmount(withdrawServicefeeSummary);
        } catch (Exception e) {
            Logger.error("withdrawServiceFeeFailAmount error:", e);
            result.setMessage("手续费回退失败");
            result.setSuccess(false);
            return result;
        } finally {
            lock.unlock();
        }
        return result;
    }

    private void checkWithdrawTaxParam(WithdrawTaxCalcReq req) throws BusinessException {
        if (org.springframework.util.StringUtils.isEmpty(req.getCustomerCode())
                || org.springframework.util.StringUtils.isEmpty(req.getCustomerType())
                || org.springframework.util.StringUtils.isEmpty(req.getAmount())
                || org.springframework.util.StringUtils.isEmpty(req.getIdentifyNo())) {
            throw new BusinessException(ERR_ILLEGAL_ARGS, "提现税费计算参数缺失");
        }
    }

    /**
     * 判断提现是否扣税
     *
     * @param customerType
     * @return
     */
    private WithdrawTaxBO checkWithdrawTax(String customerType, String customerCode, boolean withAuthInfo, boolean handleMonthIncludeSeller, String identifyNo, String month) {
        WithdrawTaxBO taxCheck = new WithdrawTaxBO();
        taxCheck.setIsTax(false);
        CustomerAuthBO authBO = this.getCustomerGrade(customerType, customerCode, withAuthInfo);
        if(handleMonthIncludeSeller) {
            this.handleMonthIncludeSeller(identifyNo, month, authBO);
        }
        taxCheck.setAuthBO(authBO);
        if ((WITHDRAW_TAX_SWITCH_ON.equalsIgnoreCase(withdrawTaxSwitch))
                && (CustomerType.NM.name().equals(customerType) || CustomerType.NMDL.name().equals(customerType))) {
            //判断是否免除扣税
            if (withoutTaxGradeList.contains(taxCheck.getAuthBO().getGrade().name())) {
                //如果是个体工商户判断是否自有渠道
                MemberGrade memberGrade = MemberGrade.PERSON_AUTH;
                if (memberGrade == taxCheck.getAuthBO().getGrade() && MemberChannel.SELF.getValue().intValue() == taxCheck.getAuthBO().getChannel()) {
                    taxCheck.setTip("提示：您当前为自行认证的个体工商户，平台将不再扣除您的税费，由您自行完成纳税义务");
                    return taxCheck;
                } else if (MemberGrade.ENTERPRISE_AUTH == taxCheck.getAuthBO().getGrade()) {
                    taxCheck.setTip("提示：您当前为企业认证，平台将不再扣除您的税费，由您自行完成纳税义务");
                    return taxCheck;
                }
            }
            taxCheck.setIsTax(true);
        }
        Logger.info("checkWithdrawTax customerCode:{}, taxCheck:{}", customerCode, DataMask.toJSONString(taxCheck));
        return taxCheck;
    }

    private CustomerAuthBO getCustomerGrade(String customerType, String customerCode, boolean withAuthInfo) {
        CustomerAuthBO authBO = new CustomerAuthBO();
        QueryAuthStatusReqDTO reqDTO = new QueryAuthStatusReqDTO();
        if (CustomerType.NM.name().equals(customerType)) {
            reqDTO.setUserCode(customerCode.substring(2));
        } else {
            reqDTO.setUserCode(customerCode);
        }
        reqDTO.setUserType(CustomerType.NM.name().equals(customerType) ? 2 : 3);
        try {
            Logger.info("查询会员等级，请求：{}", DataMask.toJSONString(reqDTO));
            com.akucun.common.Result<QueryAuthStatusRespDTO> result = null;
            if (queryUserHighestAuthStatusSwitch) {
                result = feignMemberAuthService.queryUserHighestAuthStatus(reqDTO);
            } else {
                // 会员升级：-3（个体工商户注销）代表个人等逻辑；
                result = feignMemberAuthService.queryUserHighestAuthStatusV2(reqDTO);
            }
            Logger.info("查询会员等级，请求：{}，返回：{}", DataMask.toJSONString(reqDTO), DataMask.toJSONString(result));
            if (null == result) {
                Logger.error("查询会员等级返回空 : {}-{}", customerType, customerCode);
                throw new BusinessRuntimeException("查询会员等级返回空 : " + customerType + "-" + customerCode);
            }
            if (!result.isSuccess() || (null != result.getData() && null == result.getData().getCurrentHighestAuthType())) {
                //默认是个人
                Logger.warn("查询会员等级失败，默认PERSON，{}-{}", customerType, customerCode);
                if (null == result.getData()) {
                    result.setData(new QueryAuthStatusRespDTO());
                }
                authBO.setGrade(MemberGrade.PERSON);
                authBO.setIsIncludeSeller(false);
                authBO.setChannel(MemberChannel.SELF.getValue().intValue());
            } else {
                authBO.setGrade(MemberGrade.getByCode(result.getData().getCurrentHighestAuthType()));
                authBO.setIsIncludeSeller(result.getData().isMoreRoleIncludeSellerFlag());
                authBO.setChannel(result.getData().getChannel());
            }
        } catch (Exception e) {
            Logger.error("查询会员等级异常，请求：{}", DataMask.toJSONString(reqDTO), e);
            throw new BusinessRuntimeException("查询会员等级异常");
        }
        if(withAuthInfo) {
            QueryUserAuthBaseReqDTO reqDTO2 = new QueryUserAuthBaseReqDTO();
            reqDTO2.setUserCode(reqDTO.getUserCode());
            reqDTO2.setUserType(reqDTO.getUserType());
            reqDTO2.setAuthType(authBO.getGrade().getCode());
            reqDTO2.setMoreRoleIncludeSellerFlag(true);
            try {
                Logger.info("查询会员认证信息，请求：{}", DataMask.toJSONString(reqDTO2));
                com.akucun.common.Result<QueryUserAuthBaseRespDTO> result  = feignMemberAuthService.queryUserAuthByUserIdUserType(reqDTO2);
                Logger.info("查询会员认证信息，请求：{}，返回：{}", DataMask.toJSONString(reqDTO2), DataMask.toJSONString(result));
                if(result.isSuccess()) {
                    authBO.setUserId(result.getData().getUserId());
                    if(org.apache.commons.lang3.StringUtils.isNotBlank(result.getData().getMoreRoleResellerId())) {
                        authBO.setSellerCustomerCode(CustomerType.NM.name() + result.getData().getMoreRoleResellerId());
                    }
                    authBO.setTaxId(result.getData().getMerchantId());
                    authBO.setCreditCode(result.getData().getCreditCode());
                    authBO.setCorpName(result.getData().getMerchantName());
                    authBO.setCorpRepName(result.getData().getLegalPersonName());
                } else {
                    Logger.error("查询会员认证信息失败，请求：{}，返回：{}", DataMask.toJSONString(reqDTO2), DataMask.toJSONString(result));
                }
            } catch (Exception e) {
                Logger.error("查询会员认证信息异常，请求：{}", DataMask.toJSONString(reqDTO2), e);
                throw new BusinessRuntimeException("查询会员认证信息异常");
            }
        }
        return authBO;
    }

    private void handleMonthIncludeSeller(String identifyNo, String month, CustomerAuthBO customerAuthBO) {
        if(month == null) {
            month = DateUtils.format(new Date(), FORMAT_MONTH);
        }
        LambdaQueryWrapper<WithdrawTaxDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WithdrawTaxDetail::getIdentifyNo, identifyNo)
                .eq(WithdrawTaxDetail::getMonth, month)
                .eq(WithdrawTaxDetail::getStatus, ApplyStatus.SUCC.name())
                .isNotNull(WithdrawTaxDetail::getSellerCustomerCode)
                .ne(WithdrawTaxDetail::getSellerCustomerCode, "")
                .last(" limit 1");
        //查询总数
        WithdrawTaxDetail detail = withdrawTaxDetailMapper.selectOne(wrapper);
        if((customerAuthBO.getIsIncludeSeller() != null && customerAuthBO.getIsIncludeSeller()) || detail != null) {
            customerAuthBO.setIsIncludeSeller(true);
        }
        if(detail != null) {
            customerAuthBO.setSellerCustomerCode(detail.getSellerCustomerCode());
        }
    }

    @Override
    public WithdrawThresholdCheckBO checkWithdrawThreshold(WithdrawApplyRecord record) {
        WithdrawThresholdCheckBO result = new WithdrawThresholdCheckBO();
        result.setSuccess(true);
        if (WITHDRAW_TAX_SWITCH_ON.equalsIgnoreCase(gradeChargeSwitch)) {
            // 查询当月提现汇总
            String month = DateUtils.format(new Date(), FORMAT_MONTH);
            record.setMonth(month);
            String encodeIdNo = MD5Utils.md5(record.getIdentifyNo());
            //TODO: bug待修复：这里汇总了当月提现总金额，但是区分企业饷店或饷店 - 一旦存在一个人跨越了饷店和企业饷店（身份证号码一致）会造成 没有认证的 拦截失败
            Long currMonthSummary = withdrawApplyRecordMapper.selectWithdrawSummary(encodeIdNo, month, Boolean.TRUE);

            // 同身份不同账户提现累计金额汇总计算开关,默认开启
            if (sameIdentityDifferentAccountAccumulateWithdrawAmountSwitch) {
                Long tenantCurrMonthSummary = tenantWithdrawApplyMapper.selectWithdrawSummary(encodeIdNo, month);
                // 如果currMonthSummary为空，则直接赋值tenantCurrMonthSummary, 否则将tenantCurrMonthSummary累加到currMonthSummary
                if (currMonthSummary == null) {
                    currMonthSummary = tenantCurrMonthSummary;
                } else if (tenantCurrMonthSummary == null) {
                    currMonthSummary = currMonthSummary;
                } else {
                    currMonthSummary = currMonthSummary + tenantCurrMonthSummary;
                }
            }

            //TODO：修复方案 - 从提现汇总中查询当月总提现金额
            /*WithdrawTaxSummary withDrawTaxSummaryTmp = this.getWithdrawTaxSummary(record.getIdentifyNo(), month);
            if(!ObjectUtils.isEmpty(withDrawTaxSummaryTmp)){
                long newCurrMonthSummary = (withDrawTaxSummaryTmp.getAmount().multiply(new BigDecimal(100))).longValue();
                if(newCurrMonthSummary > currMonthSummary.longValue()){
                    currMonthSummary = newCurrMonthSummary;
                }
            }*/
            //获取会员信息
            CustomerAuthBO authBO = this.getCustomerGrade(record.getCustomerType(), record.getCustomerCode(), true);
            //如果当前角色包含店主身份，则从历史提现记录中获取店长的店主，并重载到会员信息中
            this.handleMonthIncludeSeller(record.getIdentifyNo(), null, authBO);
            //个人工商户和企业不允许提现到微信余额：是否支持wx提现的标识符
            if (MemberGrade.ENTERPRISE_AUTH == authBO.getGrade()&&withdrawSwitch){
                result.setWechatWithdrawFlag(Boolean.FALSE);
                result.setWechatWithdrawTip("抱歉，微信暂不支持企业提现至个人微信钱包");
            }else if (MemberGrade.PERSON_AUTH == authBO.getGrade()&&withdrawSwitch){
                result.setWechatWithdrawFlag(Boolean.FALSE);
                result.setWechatWithdrawTip("抱歉，微信暂不支持个体工商户提现至个人微信钱包");
            }else {
                result.setWechatWithdrawFlag(Boolean.TRUE);
            }
            //身份合并
            String customerType = record.getCustomerType();
            String[] thresholds;
            if (customerType.equals(CustomerType.NM.getName()) || authBO.includeSeller()) {
                customerType = CustomerType.NM.getName();
                thresholds = shopkeeperWithdrawThresholds.split(",");
            } else {
                thresholds = shopAgentWithdrawThresholds.split(",");
            }
            BigDecimal[] gradeThresholds = new BigDecimal[thresholds.length];
            for (int i = 0; i <= gradeThresholds.length - 1; i++) {
                gradeThresholds[i] = new BigDecimal(thresholds[i]);
            }
            //累计提现金额
            BigDecimal accumAmount = record.getAmount();
            if (null != currMonthSummary) {
                BigDecimal monthAmount = BigDecimal.valueOf(currMonthSummary).divide(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
                accumAmount = accumAmount.add(monthAmount);
            }
            //如果是个人
            if (MemberGrade.PERSON == authBO.getGrade()) {
                if (CustomerType.NM.name().equals(customerType)) {
                    if (accumAmount.compareTo(gradeThresholds[0]) > 0 && accumAmount.compareTo(gradeThresholds[1]) <= 0) {
                        result.setSuccess(false);
                        result.setCode(ErrorCodeConstants.ACCORE_101524.getErrorCode());
                        result.setTip("您的当月累计提现金额大于" + thresholds[0] + "元,您需要完成个体工商户认证,进行继续提现");
                    } /**else if (accumAmount.compareTo(gradeThresholds[1]) > 0 && accumAmount.compareTo(gradeThresholds[2]) <= 0) {
                     result.setSuccess(false);
                     result.setCode(ErrorCodeConstants.ACCORE_101525.getErrorCode());
                     //                        int threshold = gradeThresholds[1].intValue() / 10000;**/
                    /**result.setTip("您的当月累计提现金额大于" + thresholds[1] + "元,您需要完成个体工商户认证,进行继续提现");
                     }*/ else if (accumAmount.compareTo(gradeThresholds[1]) > 0) {
                        result.setSuccess(false);
                        result.setCode(ErrorCodeConstants.ACCORE_101526.getErrorCode());
                        int threshold = gradeThresholds[1].intValue() / 10000;
                        result.setTip("您的当月累计提现金额大于" + threshold + "万元,您需要完成企业认证,进行继续提现");
                    }
                } else if (CustomerType.NMDL.name().equals(customerType)) {
                    if (accumAmount.compareTo(gradeThresholds[0]) > 0 && accumAmount.compareTo(gradeThresholds[1]) <= 0) {
                        result.setSuccess(false);
                        result.setCode(ErrorCodeConstants.ACCORE_101524.getErrorCode());
                        result.setTip("您的当月累计提现金额大于" + thresholds[0] + "元,您需要完成个体工商户认证,进行继续提现");
                    } //else if (accumAmount.compareTo(gradeThresholds[1]) > 0 && accumAmount.compareTo(gradeThresholds[2]) <= 0) {
                    //result.setSuccess(false);
                    //result.setCode(ErrorCodeConstants.ACCORE_101525.getErrorCode());
//                        int threshold = gradeThresholds[1].intValue() / 10000;
                    /**result.setTip("您的当月累计提现金额大于" + thresholds[1] + "元,您需要完成个体工商户认证,进行继续提现");
                     }*/else if (accumAmount.compareTo(gradeThresholds[1]) > 0) {
                        result.setSuccess(false);
                        result.setCode(ErrorCodeConstants.ACCORE_101526.getErrorCode());
                        int threshold = gradeThresholds[1].intValue() / 10000;
                        result.setTip("您的当月累计提现金额大于" + threshold + "万元,您需要完成企业认证,进行继续提现");
                    }
                }
            }
            //如果是 临时税务登记
            else if (MemberGrade.SHORT_VERIFY == authBO.getGrade()) {
                if (CustomerType.NM.name().equals(customerType)) {
                    if (accumAmount.compareTo(gradeThresholds[0]) > 0 && accumAmount.compareTo(gradeThresholds[1]) <= 0) {
                        result.setSuccess(false);
                        result.setCode(ErrorCodeConstants.ACCORE_101525.getErrorCode());
//                        int threshold = gradeThresholds[1].intValue() / 10000;
                        result.setTip("您的当月累计提现金额大于" + thresholds[0] + "元,您需要完成个体工商户认证,进行继续提现");
                    } else if (accumAmount.compareTo(gradeThresholds[1]) > 0) {
                        result.setSuccess(false);
                        result.setCode(ErrorCodeConstants.ACCORE_101526.getErrorCode());
                        int threshold = gradeThresholds[1].intValue() / 10000;
                        result.setTip("您的当月累计提现金额大于" + threshold + "万元,您需要完成企业认证,进行继续提现");
                    }
                } else if (CustomerType.NMDL.name().equals(customerType)) {
                    if (accumAmount.compareTo(gradeThresholds[0]) > 0 && accumAmount.compareTo(gradeThresholds[1]) <= 0) {
                        result.setSuccess(false);
                        result.setCode(ErrorCodeConstants.ACCORE_101526.getErrorCode());
//                        int threshold = gradeThresholds[1].intValue() / 10000;
                        result.setTip("您的当月累计提现金额大于" + thresholds[0] + "元,您需要完成个体工商户认证,进行继续提现");
                    } else if (accumAmount.compareTo(gradeThresholds[1]) > 0) {
                        result.setSuccess(false);
                        result.setCode(ErrorCodeConstants.ACCORE_101526.getErrorCode());
                        int threshold = gradeThresholds[1].intValue() / 10000;
                        result.setTip("您的当月累计提现金额大于" + threshold + "万元,您需要完成企业认证,进行继续提现");
                    }
                }
            }
            //如果是个体工商户
            else if (MemberGrade.LESHUI_AUTH == authBO.getGrade()) {
                if (CustomerType.NM.name().equals(customerType)) {
                    if (accumAmount.compareTo(gradeThresholds[1]) > 0) {
                        result.setSuccess(false);
                        result.setCode(ErrorCodeConstants.ACCORE_101526.getErrorCode());
                        int threshold = gradeThresholds[1].intValue() / 10000;
                        result.setTip("您的当月累计提现金额大于" + threshold + "万元,您需要完成企业认证,进行继续提现");
                    }
                } else if (CustomerType.NMDL.name().equals(customerType)) {
                    if (accumAmount.compareTo(gradeThresholds[1]) > 0) {
                        result.setSuccess(false);
                        result.setCode(ErrorCodeConstants.ACCORE_101526.getErrorCode());
                        int threshold = gradeThresholds[1].intValue() / 10000;
                        result.setTip("您的当月累计提现金额大于" + threshold + "万元,您需要完成企业认证,进行继续提现");
                    }
                }
            }
        }
        return result;
    }

    public WithdrawThresholdCheckBO checkWithdrawThreshold(TenantWithdrawApply record){
        WithdrawThresholdCheckBO result = new WithdrawThresholdCheckBO();
        result.setSuccess(true);

        if (WITHDRAW_TAX_SWITCH_ON.equalsIgnoreCase(gradeChargeSwitch)) {
            // 查询当月提现汇总
            String month = DateUtils.format(new Date(), FORMAT_MONTH);
            record.setMonth(month);
            String encodeIdNo = MD5Utils.md5(record.getIdentifyNo());
            //TODO: bug待修复：这里汇总了当月提现总金额，但是区分企业饷店或饷店 - 一旦存在一个人跨越了饷店和企业饷店（身份证号码一致）会造成 没有认证的 拦截失败
            // 统计当月申请提现的总金额(处理中和成功状态)
            Long currMonthSummary = tenantWithdrawApplyMapper.selectWithdrawSummary(encodeIdNo, month);

            // 同身份不同账户提现累计金额汇总计算开关,默认开启
            if (sameIdentityDifferentAccountAccumulateWithdrawAmountSwitch) {
                Long withdrawApplyRecordCurrMonthSummary = withdrawApplyRecordMapper.selectWithdrawSummary(encodeIdNo,
                        month, Boolean.TRUE);
                // 如果currMonthSummary为空，则直接赋值withdrawApplyRecordCurrMonthSummary, 否则将withdrawApplyRecordCurrMonthSummary累加到currMonthSummary
                if (currMonthSummary == null) {
                    currMonthSummary = withdrawApplyRecordCurrMonthSummary;
                } else if (withdrawApplyRecordCurrMonthSummary == null) {
                    currMonthSummary = currMonthSummary;
                } else {
                    currMonthSummary = currMonthSummary + withdrawApplyRecordCurrMonthSummary;
                }
            }

            //TODO：修复方案 - 从提现汇总中查询当月总提现金额
            /*WithdrawTaxSummary withDrawTaxSummaryTmp = this.getWithdrawTaxSummary(record.getIdentifyNo(), month);
            if(!ObjectUtils.isEmpty(withDrawTaxSummaryTmp)){
                long newCurrMonthSummary = (withDrawTaxSummaryTmp.getAmount().multiply(new BigDecimal(100))).longValue();
                if(newCurrMonthSummary > currMonthSummary.longValue()){
                    currMonthSummary = newCurrMonthSummary;
                }
            }*/
            //获取会员信息
            CustomerAuthBO authBO = this.getCustomerGrade(record.getCustomerType(), record.getCustomerCode(), true);
            //如果当前角色包含店主身份，则从历史提现记录中获取店长的店主，并重载到会员信息中
            this.handleMonthIncludeSeller(record.getIdentifyNo(), null, authBO);
            //个人工商户和企业不允许提现到微信余额：是否支持wx提现的标识符 -- 企业饷店-店主提现-暂时不使用该标识符
            /*if (MemberGrade.ENTERPRISE_AUTH == authBO.getGrade()&&withdrawSwitch){
                result.setWechatWithdrawFlag(Boolean.FALSE);
                result.setWechatWithdrawTip("抱歉，微信暂不支持企业提现至个人微信钱包");
            }else if (MemberGrade.PERSON_AUTH == authBO.getGrade()&&withdrawSwitch){
                result.setWechatWithdrawFlag(Boolean.FALSE);
                result.setWechatWithdrawTip("抱歉，微信暂不支持个体工商户提现至个人微信钱包");
            }else {
                result.setWechatWithdrawFlag(Boolean.TRUE);
            }*/
            //身份合并
            String customerType = record.getCustomerType();
            String[] thresholds;
            if (customerType.equals(CustomerType.NM.getName()) || authBO.includeSeller()) {
                customerType = CustomerType.NM.getName();
                thresholds = tenantShopkeeperWithdrawThresholds.split(",");
            } else {
                thresholds = tenantShopAgentWithdrawThresholds.split(",");
            }
            BigDecimal[] gradeThresholds = new BigDecimal[thresholds.length];
            for (int i = 0; i <= gradeThresholds.length - 1; i++) {
                gradeThresholds[i] = new BigDecimal(thresholds[i]);
            }
            //累计提现金额：这里汇总申请提现记录（tenant_withdraw_apply），而非扣税汇总记录()
            BigDecimal accumAmount = record.getAmount();
            if (null != currMonthSummary) {
                BigDecimal monthAmount = BigDecimal.valueOf(currMonthSummary).divide(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
                accumAmount = accumAmount.add(monthAmount);
            }
            //如果是个人
            if (MemberGrade.PERSON == authBO.getGrade()) {
                if (CustomerType.NM.name().equals(customerType)) {
                    if (accumAmount.compareTo(gradeThresholds[0]) > 0 && accumAmount.compareTo(gradeThresholds[1]) <= 0) {
                        result.setSuccess(false);
                        result.setCode(ErrorCodeConstants.ACCORE_101524.getErrorCode());
                        result.setTip("您的当月累计提现金额大于" + thresholds[0] + "元,您需要完成个体工商户认证,进行继续提现");
                    } /**else if (accumAmount.compareTo(gradeThresholds[1]) > 0 && accumAmount.compareTo(gradeThresholds[2]) <= 0) {
                     result.setSuccess(false);
                     result.setCode(ErrorCodeConstants.ACCORE_101525.getErrorCode());
                     //                        int threshold = gradeThresholds[1].intValue() / 10000;**/
                    /**result.setTip("您的当月累计提现金额大于" + thresholds[1] + "元,您需要完成个体工商户认证,进行继续提现");
                     }*/ else if (accumAmount.compareTo(gradeThresholds[1]) > 0) {
                        result.setSuccess(false);
                        result.setCode(ErrorCodeConstants.ACCORE_101526.getErrorCode());
                        int threshold = gradeThresholds[1].intValue() / 10000;
                        result.setTip("您的当月累计提现金额大于" + threshold + "万元,您需要完成企业认证,进行继续提现");
                    }
                } else if (CustomerType.NMDL.name().equals(customerType)) {
                    if (accumAmount.compareTo(gradeThresholds[0]) > 0 && accumAmount.compareTo(gradeThresholds[1]) <= 0) {
                        result.setSuccess(false);
                        result.setCode(ErrorCodeConstants.ACCORE_101524.getErrorCode());
                        result.setTip("您的当月累计提现金额大于" + thresholds[0] + "元,您需要完成个体工商户认证,进行继续提现");
                    } //else if (accumAmount.compareTo(gradeThresholds[1]) > 0 && accumAmount.compareTo(gradeThresholds[2]) <= 0) {
                    //result.setSuccess(false);
                    //result.setCode(ErrorCodeConstants.ACCORE_101525.getErrorCode());
//                        int threshold = gradeThresholds[1].intValue() / 10000;
                    /**result.setTip("您的当月累计提现金额大于" + thresholds[1] + "元,您需要完成个体工商户认证,进行继续提现");
                     }*/else if (accumAmount.compareTo(gradeThresholds[1]) > 0) {
                        result.setSuccess(false);
                        result.setCode(ErrorCodeConstants.ACCORE_101526.getErrorCode());
                        int threshold = gradeThresholds[1].intValue() / 10000;
                        result.setTip("您的当月累计提现金额大于" + threshold + "万元,您需要完成企业认证,进行继续提现");
                    }
                }
            }
            //如果是 临时税务登记 -- 已废止该类型
            else if (MemberGrade.SHORT_VERIFY == authBO.getGrade()) {
                if (CustomerType.NM.name().equals(customerType)) {
                    if (accumAmount.compareTo(gradeThresholds[0]) > 0 && accumAmount.compareTo(gradeThresholds[1]) <= 0) {
                        result.setSuccess(false);
                        result.setCode(ErrorCodeConstants.ACCORE_101525.getErrorCode());
//                        int threshold = gradeThresholds[1].intValue() / 10000;
                        result.setTip("您的当月累计提现金额大于" + thresholds[0] + "元,您需要完成个体工商户认证,进行继续提现");
                    } else if (accumAmount.compareTo(gradeThresholds[1]) > 0) {
                        result.setSuccess(false);
                        result.setCode(ErrorCodeConstants.ACCORE_101526.getErrorCode());
                        int threshold = gradeThresholds[1].intValue() / 10000;
                        result.setTip("您的当月累计提现金额大于" + threshold + "万元,您需要完成企业认证,进行继续提现");
                    }
                } else if (CustomerType.NMDL.name().equals(customerType)) {
                    if (accumAmount.compareTo(gradeThresholds[0]) > 0 && accumAmount.compareTo(gradeThresholds[1]) <= 0) {
                        result.setSuccess(false);
                        result.setCode(ErrorCodeConstants.ACCORE_101526.getErrorCode());
//                        int threshold = gradeThresholds[1].intValue() / 10000;
                        result.setTip("您的当月累计提现金额大于" + thresholds[0] + "元,您需要完成个体工商户认证,进行继续提现");
                    } else if (accumAmount.compareTo(gradeThresholds[1]) > 0) {
                        result.setSuccess(false);
                        result.setCode(ErrorCodeConstants.ACCORE_101526.getErrorCode());
                        int threshold = gradeThresholds[1].intValue() / 10000;
                        result.setTip("您的当月累计提现金额大于" + threshold + "万元,您需要完成企业认证,进行继续提现");
                    }
                }
            }
            //如果是个体工商户
            else if (MemberGrade.LESHUI_AUTH == authBO.getGrade()) {
                if (CustomerType.NM.name().equals(customerType)) {
                    if (accumAmount.compareTo(gradeThresholds[1]) > 0) {
                        result.setSuccess(false);
                        result.setCode(ErrorCodeConstants.ACCORE_101526.getErrorCode());
                        int threshold = gradeThresholds[1].intValue() / 10000;
                        result.setTip("您的当月累计提现金额大于" + threshold + "万元,您需要完成企业认证,进行继续提现");
                    }
                } else if (CustomerType.NMDL.name().equals(customerType)) {
                    if (accumAmount.compareTo(gradeThresholds[1]) > 0) {
                        result.setSuccess(false);
                        result.setCode(ErrorCodeConstants.ACCORE_101526.getErrorCode());
                        int threshold = gradeThresholds[1].intValue() / 10000;
                        result.setTip("您的当月累计提现金额大于" + threshold + "万元,您需要完成企业认证,进行继续提现");
                    }
                }
            }
        }

        return result;
    }

    @Override
    public Result<Void> wechatWithdrawSumAmount(WithdrawApplyRecord record, WithdrawTaxDetail taxDetail) {
        Result<Void> result = Result.success();
        //免息券不展示，微信提现金额不累计
        if (!freeCouponSwitch){
            return result;
        }
        try {
            BigDecimal withdrawAmount = taxDetail != null ? taxDetail.getWithdraw() : record.getAmount();
            WechatWithdrawSummary summary = new WechatWithdrawSummary();
            String year = com.aikucun.common2.utils.DateUtils.format(new Date(), FORMAT_YEAR);
            String idNo = MD5Utils.md5(record.getIdentifyNo());
            LambdaQueryWrapper<WechatWithdrawSummary> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(WechatWithdrawSummary::getIdentifyNo, idNo)
                    .eq(WechatWithdrawSummary::getYear, year);
            List<WechatWithdrawSummary> list = wechatWithdrawSummaryMapper.selectList(wrapper);
            if (!CollectionUtils.isEmpty(list)) {
                //判断当前提现次数是否符合收取手续费标准
                summary = list.get(0);
                wechatWithdrawSummaryMapper.addAmount(summary.getId(), withdrawAmount);
            } else {
                summary.setIdentifyNo(idNo);
                summary.setYear(year);
                summary.setSumWithdrawAmt(withdrawAmount);
                summary.setWithdrawNum(1);
                wechatWithdrawSummaryMapper.insert(summary);
            }
        } catch (Exception e) {
            Logger.warn("wechatWithdrawSumAmount exception:", e);
            return Results.error(ResponseEnum.ACCORE_101529);
        }
        return result;

    }

    @Override
    public void wechatWithdrawFailSumAmount(WithdrawApplyRecord record, WithdrawTaxDetail taxDetail) {
        //免息券不展示时，不做累计金额扣减
        if (!freeCouponSwitch){
            return;
        }
        String year = DateUtils.format(new Date(), FORMAT_YEAR);
        LambdaQueryWrapper<WechatWithdrawSummary> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WechatWithdrawSummary::getIdentifyNo, record.getIdentifyNo())
                .eq(WechatWithdrawSummary::getYear, year);
        List<WechatWithdrawSummary> list = wechatWithdrawSummaryMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(list)) {
            Logger.info("wechatWithdrawFailSumAmount 当年未查询到微信提现金额汇总相关记录：{}", record.getCustomerCode());
            return;
        }
        BigDecimal withdrawAmount = taxDetail != null ? taxDetail.getWithdraw() : record.getAmount();
        WechatWithdrawSummary summary = list.get(0);
        String uniqueId = "WECHATWITHDRAWSUMAMT" + record.getWithdrawNo();
        Result<Void> result = this.duplicateCheckService.checkDuplicate(uniqueId, false);
        if (result.getSuccess()) {
            boolean needRetry = false;
            String amountKey = "WECHATWITHDRAWSUMAMT" + summary.getIdentifyNo() + summary.getYear();
            RedisLock lock = new RedisLock(redisTemplate, amountKey, lockExpireTime);
            if (!lock.tryLock()) {
                needRetry = true;
                Logger.warn("wechatWithdrawFailSumAmount RedisLock is lock, lockKey:{}, withdrawNo:{}", amountKey, record.getWithdrawNo());
            }
            try {
                wechatWithdrawSummaryMapper.subtractAmount(summary.getId(), withdrawAmount);
            } catch (Exception e) {
                Logger.error("wechatWithdrawFailSumAmount error:", e);
                needRetry = true;
            } finally {
                lock.unlock();
            }
            if (needRetry) {
                summary.setSumWithdrawAmt(withdrawAmount);
                addWechatWithdrawFailRetrySchedule(summary, record.getWithdrawNo());
            }
        } else {
            Logger.info("wechatWithdrawFailSumAmount fail duplicate:{}" + record.getWithdrawNo());
        }
    }

    @Override
    public WechatWithdrawSummary queryWechatWithdrawSummary(String identifyNo, Date year) {
        //免息券不展示时不做查询
        if (!freeCouponSwitch){
            return null;
        }
        String yearStr = DateUtil.formatDate(year, "yyyy");
        LambdaQueryWrapper<WechatWithdrawSummary> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WechatWithdrawSummary::getIdentifyNo, identifyNo)
                .eq(WechatWithdrawSummary::getYear, yearStr);
        return wechatWithdrawSummaryMapper.selectOne(wrapper);
    }

    private void addWechatWithdrawFailRetrySchedule(WechatWithdrawSummary summary, String withdrawNo) {
        try {
            PostActionItemBO itemBO = PostActionItemBO.builder()
                    .bizId(withdrawNo)
                    .paramObject(summary)
                    .remark("微信提现失败累计金额退回任务")
                    .actionType(PostActionTypes.WECHAT_WITHDRAW_FAIL_AMOUNT.getName())
                    .status(PostActionExecStatus.EXECUTE.value())
                    .nextRetryTime(LocalDateTime.now().plusSeconds(5 * 60))
                    .retryNums(0)
                    .build();
            postActionService.addAction(itemBO);
        } catch (Exception e) {
            Logger.error("addWechatWithdrawFailRetrySchedule withdrawNo:{}, exception:", withdrawNo, e.getMessage());
        }
    }
    public static void main(String[] args) {
		System.out.println(new BigDecimal(1000).compareTo(new BigDecimal(5000)));
	}

    public Result<WithdrawTaxDetailVO> shopkeeperIncentiveAwardWithdrawCalcTax(ShopkeeperIncentiveAwardWithdrawCalcTaxReq req) {
        try {
            String month = DateUtils.format(new Date(), FORMAT_MONTH);
            //判断该用户是否收取税费
            WithdrawTaxBO taxCheck = this.checkWithdrawTax(req.getCustomerType(), req.getCustomerCode(), true, true, req.getIdentifyNo(), month);
            WithdrawTaxDetail taxDetail = new WithdrawTaxDetail();
            taxDetail.setIdentifyCategory(taxCheck.getAuthBO().getGrade().name());
            taxDetail.setMonth(month);
            taxDetail.setUserId(taxCheck.getAuthBO().getUserId());
            taxDetail.setSellerCustomerCode(taxCheck.getAuthBO().getSellerCustomerCode());
            taxDetail.setTaxId(taxCheck.getAuthBO().getTaxId());
            taxDetail.setCreditCode(taxCheck.getAuthBO().getCreditCode());
            taxDetail.setCorpName(taxCheck.getAuthBO().getCorpName());
            taxDetail.setCorpRepName(taxCheck.getAuthBO().getCorpRepName());
            taxDetail.setServiceFee(BigDecimal.ZERO);
            taxDetail.setAmount(req.getAmount());
            taxDetail.setIdentifyNo(req.getIdentifyNo());
            taxDetail.setCustomerCode(req.getCustomerCode());
            taxDetail.setCustomerType(req.getCustomerType());
            taxDetail.setCustomerName(req.getCustomerName());
            taxDetail.setWithdrawNo(req.getWithdrawNo());
            //直接默认初始化状态
            taxDetail.setStatus(ApplyStatus.INIT.name());

            //设置固定扣税税率
            BigDecimal percentageValue = sellerIncentiveAwardAutoWithdrawFixedTaxRate.multiply(new BigDecimal("100")); // 乘以 100
            DecimalFormat df = new DecimalFormat("0.00%"); // 定义百分数格式
            String percentageFeeRate = df.format(percentageValue.divide(new BigDecimal("100"))); // 转换为百分数
            taxDetail.setFeeRate(percentageFeeRate);

            //需要记录的扣税金额
            BigDecimal shouldTaxFee = req.getAmount().multiply(sellerIncentiveAwardAutoWithdrawFixedTaxRate).setScale(2, BigDecimal.ROUND_HALF_UP);
            if (shouldTaxFee.compareTo(BigDecimal.ZERO) >= 0) {
                taxDetail.setTaxFee(shouldTaxFee);
            }
            //最终的可提现金额
            BigDecimal shouldWithdrawAmount = req.getAmount().subtract(shouldTaxFee);
            taxDetail.setWithdraw(shouldWithdrawAmount);
            if (shouldWithdrawAmount.compareTo(BigDecimal.ZERO) < 0) {
                taxDetail.setWithdraw(BigDecimal.ZERO);
            }

            String remark;
            //手续费为0，不展示
            if (taxDetail.getServiceFee() != null && taxDetail.getServiceFee().compareTo(BigDecimal.ZERO) > 0) {
                remark = "到账：" + taxDetail.getWithdraw() + "，代扣税费：" + taxDetail.getTaxFee() + "，银行手续费：" + taxDetail.getServiceFee() + "(单独计税)";
            } else {
                remark = "到账：" + taxDetail.getWithdraw() + "，代扣税费：" + taxDetail.getTaxFee() + "(单独计税)";
            }
            taxDetail.setRemark((StringUtils.isEmpty(req.getRemark()) ? "" : req.getRemark() + "，") + remark);

            //是否提交记税
            if (Boolean.TRUE.equals(req.getSubmitTax())) {
                taxDetail.setStatus(ApplyStatus.SUCC.name());
                int dbNum = withdrawTaxDetailMapper.insert(taxDetail);
                if (dbNum != 1) {
                    throw new AccountProxyException("插入税费明细表失败");
                }
            }

            WithdrawTaxDetailVO withdrawTaxDetailVO = new WithdrawTaxDetailVO();
            BeanUtils.copyProperties(taxDetail, withdrawTaxDetailVO);
            return Result.success(withdrawTaxDetailVO);
        } catch (Exception e) {
            Logger.error("shopkeeperIncentiveAwardWithdrawCalcTax error:", e);
            return Result.error(ResponseEnum.ACCORE_101512.getCode(), ResponseEnum.ACCORE_101512.getMessage());
        }
    }
}
