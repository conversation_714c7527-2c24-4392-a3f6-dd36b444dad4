package com.akucun.account.proxy.service.acct.bo;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: silei
 * @Date: 2020/8/26
 * @desc:
 */
@Data
public class AccountOpTradeDetailBO {

    private Long id;

    private Long accountTradeId;

    private String tradeType;

    private String subTradeType;

    private String detailOrderNo;

    private String status;
    //返回码
    private String replyCode;
    //返回消息
    private String replyMsg;

    private Integer retryTimes;

    private Map<String, String> extField = new HashMap<>();

}
