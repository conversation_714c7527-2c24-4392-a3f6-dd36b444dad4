package com.akucun.account.proxy.service.acct.bo;

import lombok.Data;

import java.io.Serializable;

/**
 * 扣税判断返回参数
 *
 * <AUTHOR>
 * @version [版本号, 2020年9月3日]
 */
@Data
public class WithdrawTaxBO implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = -1709524852839955458L;

    /**
     * 是否扣税
     */
    private Boolean isTax;

    /**
     * 提示信息
     */
    private String tip;

    /**
     * 店铺认证信息
     */
    private CustomerAuthBO authBO;

}
