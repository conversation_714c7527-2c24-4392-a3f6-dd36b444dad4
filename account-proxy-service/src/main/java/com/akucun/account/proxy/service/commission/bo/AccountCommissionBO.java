package com.akucun.account.proxy.service.commission.bo;

import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * @Author: silei
 * @Date: 2020/12/8
 * @desc:
 */
@Data
@ToString
public class AccountCommissionBO {

    /**
     * 是否结束
     */
    private boolean isEnd = false;

    /**
     * 客户编码
     */
    private String customerCode;
    /**
     * 客户类型 店主NM  店长NMDL
     */
    private String customerType;
    /**
     * 金额
     */
    private BigDecimal amount;
    /**
     * 交易类型
     */
    private String tradeType;
    /**
     * 交易单号
     */
    private String tradeNo;
    /**
     * 来源单号
     */
    private String sourceBillNo;
    /**
     * 店铺编码
     */
    private String shopId;

    /**
     * 扩展字段（json）
     */
    private String ext;

    /**
     * 备注
     */
    private String remark;

}
