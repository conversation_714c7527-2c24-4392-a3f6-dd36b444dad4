package com.akucun.account.proxy.domain.model;

import com.akucun.account.proxy.facade.stub.others.dto.req.PinganAdjustReq;
import lombok.Data;
import org.springframework.beans.BeanUtils;

@Data
public abstract class PinganAdjust extends Adjust {
    /**
     *用户类型
     */
    private String customerType;
    /**
     * 平安转账最大限额
     */
    private String maxPinganTransferLimit;
    /**
     * 平安分账最大限额
     */
    private String maxPinganClearingLimit;

    public static PinganAdjust build(PinganAdjustReq pinganAdjustReq) {
        PinganAdjust pinganAdjust = null;
        if (pinganAdjustReq.getAdjustmentType().equals("1") ){
            //分账
            pinganAdjust = new PinganClearing();

        } else if (pinganAdjustReq.getAdjustmentType().equals("2")) {
            //转账
            pinganAdjust = new PinganTransfer();

        } else if (pinganAdjustReq.getAdjustmentType().equals("3")) {
            //罚扣
            pinganAdjust = new PinganDeduction();
        } else {
            throw new RuntimeException("不支持调账类型");
        }

        BeanUtils.copyProperties(pinganAdjustReq, pinganAdjust);
        return pinganAdjust;
    }

}
