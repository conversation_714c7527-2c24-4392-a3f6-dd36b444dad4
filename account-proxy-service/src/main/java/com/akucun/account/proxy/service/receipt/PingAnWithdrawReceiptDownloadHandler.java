package com.akucun.account.proxy.service.receipt;

import com.akucun.account.proxy.service.common.SpringContextHolder;
import org.apache.commons.lang3.StringUtils;

import com.aikucun.common2.base.Result;
import com.aikucun.common2.base.exception.IErrorCode;
import com.akucun.account.proxy.client.file.FileUploadClient;
import com.akucun.account.proxy.common.constant.ApplyStatus;
import com.akucun.account.proxy.common.constant.WithdrawChannelConstants;
import com.akucun.account.proxy.common.utils.SpringBeanUtil;
import com.akucun.account.proxy.dao.mapper.TenantWithdrawApplyMapper;
import com.akucun.account.proxy.dao.mapper.WithdrawApplyRecordMapper;
import com.akucun.account.proxy.dao.model.TenantWithdrawApply;
import com.akucun.account.proxy.dao.model.WithdrawApplyRecord;
import com.akucun.account.proxy.service.receipt.bo.WithdrawReceiptDownloadBO;
import com.akucun.fps.pingan.client.vo.PinganReceiptFileVO;
import com.akucun.fps.pingan.client.vo.PinganReceiptQueryVO;
import com.akucun.fps.pingan.feign.api.settlement.SettlementServiceApi;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.mengxiang.base.common.log.Logger;
import com.mengxiang.fileupload.dto.FileInfo;

public class PingAnWithdrawReceiptDownloadHandler extends AbstractWithdrawReceiptDownloadHandler {

    public PingAnWithdrawReceiptDownloadHandler(WithdrawReceiptDownloadBO bo) {
        super(bo);
    }

    @Override
    public Result<Void> handle() {
        try {
            // 普通提现
            WithdrawApplyRecordMapper withdrawApplyRecordMapper = SpringContextHolder.getBean(WithdrawApplyRecordMapper.class);
            // 租户(店主/店长/租户)提现
            TenantWithdrawApplyMapper tenantWithdrawApplyMapper = SpringContextHolder.getBean(TenantWithdrawApplyMapper.class);

            String customerCode = null;
            // 先判断是否为租户提现
            if (Boolean.TRUE.equals(bo.getIsTenantCustomer())) {
                TenantWithdrawApply apply = tenantWithdrawApplyMapper.selectOne(new LambdaQueryWrapper<TenantWithdrawApply>().eq(TenantWithdrawApply::getWithdrawNo, bo.getWithdrawNo()));
                if (apply == null) {
                    return Result.error(IErrorCode.ARGUMENT_ERROR, "未查询到提现单据");
                }
                if (!WithdrawChannelConstants.PINGAN.getName().equals(apply.getWithdrawChannel())) {
                    return Result.error(IErrorCode.ARGUMENT_ERROR, "非平安提现, 无法下载回单");
                }
                if (!ApplyStatus.SUCC.getName().equals(apply.getApplyStatus())) {
                    return Result.error(IErrorCode.ARGUMENT_ERROR, "提现未成功, 无法下载回单");
                }
                customerCode = apply.getCustomerCode();
            } else {
                //常规提现
                WithdrawApplyRecord record = withdrawApplyRecordMapper.selectOne(new LambdaQueryWrapper<WithdrawApplyRecord>().eq(WithdrawApplyRecord::getWithdrawNo, bo.getWithdrawNo()));
                if (record == null) {
                    return Result.error(IErrorCode.ARGUMENT_ERROR, "未查询到提现单据");
                }
                if (!WithdrawChannelConstants.PINGAN.getName().equals(record.getWithdrawChannel())) {
                    return Result.error(IErrorCode.ARGUMENT_ERROR, "非平安提现, 无法下载回单");
                }
                if (!ApplyStatus.SUCC.getName().equals(record.getApplyStatus())) {
                    return Result.error(IErrorCode.ARGUMENT_ERROR, "提现未成功, 无法下载回单");
                }
                customerCode = record.getCustomerCode();
            }

            // 调用平安接口查询提现回单
            PinganReceiptQueryVO queryVO = new PinganReceiptQueryVO();
            queryVO.setTradeId(bo.getWithdrawNo());
            queryVO.setTradeType("WITHDRAW");
            com.akucun.fps.common.entity.Result<PinganReceiptFileVO> queryReceiptResult = SpringContextHolder.getBean(SettlementServiceApi.class).queryReceipt(queryVO);
            if (!queryReceiptResult.isSuccess() || queryReceiptResult.getData() == null) {
                return Result.error(IErrorCode.ARGUMENT_ERROR, "调用fps-pingan查询平安提现回单失败");
            }
            PinganReceiptFileVO queryReceipt = queryReceiptResult.getData();
            if (StringUtils.isBlank(queryReceipt.getFilePrivateUrl())) {
                return Result.error(IErrorCode.ARGUMENT_ERROR, "调用fps-pingan查询平安提现回单返回地址为空");
            }
            String receiptFileTmpUrl = queryReceipt.getFileUrl();
            String originFileName = queryReceipt.getFileName();

            // 下载提现回单
            FileUploadClient fileUploadClient = SpringContextHolder.getBean(FileUploadClient.class);
            byte[] fileBytes = fileUploadClient.getHttpFileBytes(receiptFileTmpUrl);
            if (fileBytes == null) {
                return Result.error(IErrorCode.ARGUMENT_ERROR, "平安提现回单远程下载失败");
            }

            // 原文件后缀
            String originFileSuffix = originFileName.substring(originFileName.lastIndexOf(".") + 1);
            // 新文件名
            String newFileName = String.format("%s_%s.%s", bo.getWithdrawNo(), customerCode, originFileSuffix);
            // 上传文件
            Result<FileInfo> uploadFileResult = fileUploadClient.upload(fileBytes, originFileName, newFileName, FileUploadClient.WITHDRAW_RECEIPT_PARENT_DIR);
            if (!uploadFileResult.getSuccess()) {
                return Result.error(IErrorCode.ARGUMENT_ERROR, "平安提现回单上传OBS失败");
            }
            FileInfo fileInfo = uploadFileResult.getData();
            if (fileInfo == null || StringUtils.isBlank(fileInfo.getFileUrl())) {
                return Result.error(IErrorCode.ARGUMENT_ERROR, "平安提现回单上传OBS失败");
            }

            // 更新提现中的回单地址
            if (Boolean.TRUE.equals(bo.getIsTenantCustomer())) {
                TenantWithdrawApply updateEntity = new TenantWithdrawApply();
                updateEntity.setReceiptUrl(fileInfo.getFileUrl());
                
                tenantWithdrawApplyMapper.update(updateEntity, new LambdaUpdateWrapper<TenantWithdrawApply>().eq(TenantWithdrawApply::getWithdrawNo, bo.getWithdrawNo()));
            } else {
                WithdrawApplyRecord updateEntity = new WithdrawApplyRecord();
                updateEntity.setReceiptUrl(fileInfo.getFileUrl());

                withdrawApplyRecordMapper.update(updateEntity, new LambdaUpdateWrapper<WithdrawApplyRecord>().eq(WithdrawApplyRecord::getWithdrawNo, bo.getWithdrawNo()));
            }
            return Result.success();
        } catch (Exception e) {
            Logger.error("平安提现回单下载异常, withdrawNo:{}", bo.getWithdrawNo(), e);
            return Result.error(IErrorCode.SYSTEM_ERROR, "平安提现回单下载异常");
        }
    }
}
