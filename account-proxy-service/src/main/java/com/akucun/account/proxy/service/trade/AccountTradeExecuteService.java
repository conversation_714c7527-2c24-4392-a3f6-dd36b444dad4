package com.akucun.account.proxy.service.trade;

import com.akucun.account.proxy.service.trade.bo.AccountTradeBO;
import com.akucun.account.proxy.service.trade.bo.AccountTradeResp;

/**
 * @Author: silei
 * @Date: 2020/12/2
 * @desc:
 */
public interface AccountTradeExecuteService {

    /**
     * 账户交易执行
     * @param tradeBO
     * @param isResume
     * @return
     */
    AccountTradeResp executeTrade(AccountTradeBO tradeBO, boolean isResume);

}
