package com.akucun.account.proxy.domain.model;

import com.aikucun.common2.log.Logger;
import com.akucun.account.proxy.service.common.SpringContextHolder;
import com.akucun.fps.common.entity.Result;
import com.akucun.fps.pingan.client.vo.DealTradeVO;
import com.akucun.fps.pingan.feign.api.member.MemberServiceApi;
import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.Random;

/**
 * 转账
 */
@Service
public class PinganTransfer extends PinganAdjust {

    @Override
    public Pair<Integer,String> done() {
        BigDecimal maxLimitAmount=new BigDecimal(this.getMaxPinganTransferLimit());
        if(this.getAmount().compareTo(maxLimitAmount)==1){
            return Pair.of(88,"超过最大可调帐上限"+maxLimitAmount);
        }
        //构建请求体
        DealTradeVO dealTradeVO=new DealTradeVO();
        dealTradeVO.setContent(this.getRemark());
        dealTradeVO.setCustomerCode(this.getCustomerCode());
        dealTradeVO.setCustomerType(this.getCustomerType());
        dealTradeVO.setOrderNo(System.currentTimeMillis() + "" + (new Random().nextInt(10)));
        dealTradeVO.setRemark(this.getRemark());
        dealTradeVO.setTranAmount(this.getAmount());

        if(dealTradeVO.getContent().isEmpty()||dealTradeVO.getCustomerCode().isEmpty()||dealTradeVO.getCustomerType().isEmpty()||
                dealTradeVO.getOrderNo().isEmpty()|| ObjectUtils.isEmpty(dealTradeVO.getTranAmount())
        ){
            return Pair.of(9,"服务异常请重试");
        }
        //请求
        Logger.info("平安转账开始，dealTradeVO:{}",JSON.toJSONString(dealTradeVO));
        MemberServiceApi memberServiceApi=SpringContextHolder.getBean(MemberServiceApi.class);
        Result<Void> result = memberServiceApi.dealTrade(dealTradeVO);
        Logger.info("平安转账处理结束，参数dealTradeVO:{}, 响应result:{}", JSON.toJSONString(dealTradeVO), JSON.toJSONString(result));

        //返回
        if (result.isSuccess()) {
            return Pair.of( 0 , "操作成功");
        } else {
            return Pair.of( result.getErrorCode() , result.getErrorMessage());
        }
    }

}
