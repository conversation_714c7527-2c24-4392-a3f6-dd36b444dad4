package com.akucun.account.proxy.service.acct.impl;

import com.akucun.account.proxy.facade.stub.enums.RewardTypeEnum;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : 月勤奖下发
 * @Create on : 2025/2/17 14:43
 **/
@Service
public class MonthlyBonusServiceImpl extends AbstractPromoTradeService{
    static final RewardTypeEnum busiType = RewardTypeEnum.MONTHLY_BONUS;


    //=========== 获取当前业务类别 ==============
    @Override
    public RewardTypeEnum getBusiType() {
        return busiType;
    }
}
