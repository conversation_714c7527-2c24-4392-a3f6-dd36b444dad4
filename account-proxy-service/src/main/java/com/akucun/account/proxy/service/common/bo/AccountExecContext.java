package com.akucun.account.proxy.service.common.bo;

import com.aikucun.common2.utils.datamasking.DataMask;
import com.akucun.account.proxy.common.constant.StepConstant;
import com.akucun.account.proxy.common.enums.ResultStatus;
import com.akucun.account.proxy.common.enums.StepEnum;
import com.akucun.account.proxy.common.utils.WechatNotifyTool;
import com.akucun.account.proxy.service.acct.bo.AccountOpTradeBO;
import com.akucun.account.proxy.service.acct.bo.AccountOpTradeDetailBO;
import com.akucun.account.proxy.service.acct.repository.AccountOpTradeRepository;
import com.akucun.account.proxy.service.acct.util.AccountRequestConverter;
import com.akucun.account.proxy.service.common.step.Step;
import com.akucun.account.proxy.service.initializing.InitializingStep;
import com.akucun.account.proxy.service.trade.bo.AccountCustomerRouteBO;
import com.akucun.account.proxy.service.trade.bo.AccountTradeBO;
import com.akucun.account.proxy.service.trade.bo.AccountTradeDetailBO;
import com.akucun.account.proxy.service.trade.repository.AccountCustomerRouteRepository;
import com.akucun.account.proxy.service.trade.repository.AccountTradeRepository;
import com.akucun.cloud.security.CodeUtils;
import com.alibaba.fastjson.JSON;
import com.mengxiang.base.common.log.Logger;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @Author: silei
 * @Date: 2020/8/26
 * @desc: 账户流程执行上下文
 */
@Data
public class AccountExecContext {

    private ApplicationContext applicationContext;

    //明细流程执行上下文
    private List<AccountExecStepContext> accountExecStepContexts = new ArrayList<>();
    //账户升级
    private AccountOpTradeBO accountOpTradeBO;
    //账户升级明细
    private List<AccountOpTradeDetailBO> accountOpTradeDetailBOList = new ArrayList<>();
    //当前流程步骤
    private Step currentStep;
    //执行过程中的异常
    private Throwable exception;
    //是否修复推进
    private boolean isResume;
    //当前交易类型（确定执行流程）
    private String tradeType;


    public AccountExecContext(ApplicationContext applicationContext, AccountOpTradeBO req, boolean isResume) {
        this.applicationContext = applicationContext;
        initAccountOpTradeReq(req);
        initAccountOpTradeCurrentStep();
        this.tradeType = req.getTradeType();
        this.isResume = isResume;
    }

    /**
     * 账户升级初始化处理
     *
     * @param req
     */
    private void initAccountOpTradeReq(AccountOpTradeBO req) {
        if (ResultStatus.I.getCode().equals(req.getStatus())) {
            //更新状态为处理中
            req.setStatus(ResultStatus.P.getCode());
            getAcctOpTradeRepository().updateAccountOpTradeStatus(req);
        }
        if (!StringUtils.isNumeric(req.getMobile())) {
            req.setMobile(CodeUtils.decrypt(req.getMobile()).getData());
        }
        //查询最新的明细记录
        req = getAcctOpTradeRepository().queryAcctOpTradeDetail(req);
        //查询全部的明细列表
        this.accountOpTradeDetailBOList = getAcctOpTradeRepository().queryAcctOpTradeDetailList(req.getId());
        Logger.info("initAccountOpTradeReq 初始化 accountTrade:{}", DataMask.toJSONString(req));
        this.accountOpTradeBO = req;

    }

    /**
     * 执行流程初始化
     */
    private void initAccountOpTradeCurrentStep() {
        AccountOpTradeBO accountTrade = this.accountOpTradeBO;
        if (Objects.isNull(accountTrade.getAccountOpTradeDetailBO())) {
            this.currentStep = getStepCache().getStep(accountTrade.getTradeType());
        } else {
            AccountOpTradeDetailBO detailBO = accountTrade.getAccountOpTradeDetailBO();
            this.currentStep = getStepCache().getStep(accountTrade.getTradeType(), detailBO.getSubTradeType());
        }
        Logger.info("initAccountOpTradeCurrentStep 初始化 currentStep:{}", currentStep);
    }


    /**
     * 账户升级明细流程初始化
     *
     * @param step
     * @return
     */
    public AccountExecStepContext initAccountExecStepContext(Step step) {
        AccountExecStepContext accountExecStepContext = new AccountExecStepContext();
        //传递初始化属性
        accountExecStepContext.getExtMap().putAll(step.getExtMap());
        this.accountExecStepContexts.add(accountExecStepContext);

        //检查当前流程detail
        AccountOpTradeDetailBO detailBO = getCurrentStepDetail(step);
        if (detailBO != null && ResultStatus.S.getCode().equals(detailBO.getStatus())) {
            accountExecStepContext.setStepStatus(ResultStatus.S.getCode());
            accountExecStepContext.setContinue(false);
            return accountExecStepContext;
        } else if (Objects.nonNull(detailBO) && ResultStatus.P.getCode().equals(detailBO.getStatus())) {
            //查询订单
            accountExecStepContext.setAction(StepConstant.ACTION_QUERY);
        } else {
            accountExecStepContext.setAction(StepConstant.ACTION_EXEC);
            if (Objects.isNull(detailBO)) {
                //新流程
                detailBO = this.addAccountOpTradeDetailAndPersist(0, ResultStatus.P.getCode(), step);
            } else if (ResultStatus.F.getCode().equals(detailBO.getStatus())
                    && null != step.getExtMap()
                    && StepEnum.IsRetryAble.Y.getCode().equals(step.getExtMap().get(StepConstant.IS_RETRYABLE))
                    && StringUtils.isNotEmpty(step.getExtMap().get(StepConstant.MAX_RETRY_TIMES))) {
                //重试流程
                int retryTimes = detailBO.getRetryTimes() + 1;
                String maxRetryTimes = step.getExtMap().get(StepConstant.MAX_RETRY_TIMES);
                //除主动修复场景外，超过最大重试次数，流程结束
                if (!isResume && retryTimes >= Integer.parseInt(maxRetryTimes)) {
                    //发送通知消息
                    if (retryTimes == 2){
                        getWechatNotifyTool().sendNotifyMsg("account-proxy initAccountExecStepContext retry exceed max limit! subTradeType:" + step.getSubTradeType() + ", customerCode:" + this.accountOpTradeBO.getCustomerCode());
                    }
                    Logger.info("initAccountExecStepContext retry exceed max limit! subTradeType:{}, customerCode:{}", step.getSubTradeType(),this.accountOpTradeBO.getCustomerCode());
                    //超过最大次数终止流程
                    accountExecStepContext.setStepStatus(ResultStatus.F.getCode());
                    accountExecStepContext.setContinue(false);
                    return accountExecStepContext;
                } else {
                    detailBO = this.addAccountOpTradeDetailAndPersist(retryTimes, ResultStatus.P.getCode(), step);
                }
            } else {
                //非法状态
                accountExecStepContext.setContinue(false);
                accountExecStepContext.setStepStatus(ResultStatus.F.getCode());
                return accountExecStepContext;
            }
        }
        accountExecStepContext.setAccountOpTradeDetailBO(detailBO);
        //组装明细流程参数
        AccountReq accountReq = getAccountRequestConverter().buildAccountReq(accountOpTradeBO, detailBO, accountExecStepContext.getAction());
        accountExecStepContext.setAccountReq(accountReq);
        return accountExecStepContext;
    }

    /**
     * 账户升级明细落库
     *
     * @param retryTimes
     * @param status
     * @param step
     * @return
     */
    private AccountOpTradeDetailBO addAccountOpTradeDetailAndPersist(int retryTimes, String status, Step step) {
        AccountOpTradeDetailBO detailBO = new AccountOpTradeDetailBO();
        detailBO.setAccountTradeId(this.accountOpTradeBO.getId());
        detailBO.setExtField(step.getExtMap());
        detailBO.setRetryTimes(retryTimes);
        if (StringUtils.isNotEmpty(status)) {
            detailBO.setStatus(status);
        } else {
            detailBO.setStatus(ResultStatus.P.getCode());
        }
        detailBO.setTradeType(accountOpTradeBO.getTradeType());
        detailBO.setSubTradeType(step.getSubTradeType());
        String detailOrderNo;

        int suffix = this.getAccountOpTradeDetailBOList().size();
        String detailOrderNoPre = accountOpTradeBO.getOrderNo();
        detailOrderNo = step.getDetailOrderNo(detailOrderNoPre, suffix + "");
        detailBO.setDetailOrderNo(detailOrderNo);
        this.addAccountOpTradeDetailListAndPersist(detailBO);
        return detailBO;
    }

    /**
     * 获取当前流程明细
     *
     * @param step
     * @return
     */
    private AccountOpTradeDetailBO getCurrentStepDetail(Step step) {
        //这个detail 是初始化的，或者是上一个阶段的执行结果
        AccountOpTradeDetailBO detailBO = accountOpTradeBO.getAccountOpTradeDetailBO();
        if (Objects.isNull(detailBO)) {
            return null;
        }
        //流程子类型不一致返回null
        if (!step.getSubTradeType().equals(detailBO.getSubTradeType())) {
            return null;
        }
        return detailBO;
    }

    /**
     * 判断流程是否结束
     *
     * @return
     */
    public boolean isEnd() {
        return getNextStep() == null
                && null != getLatestAccountStepExecContext()
                && getLatestAccountStepExecContext().isEnd();
    }

    public void updateOpTradeRespCode() {
        accountOpTradeBO.setReplyCode(accountOpTradeBO.getAccountOpTradeDetailBO().getReplyCode());
        accountOpTradeBO.setReplyMsg(accountOpTradeBO.getAccountOpTradeDetailBO().getReplyMsg());
    }

    /**
     * 获取下一步流程
     *
     * @return
     */
    public Step getNextStep() {
        //上一步流程结果为空，流程中断
        if (this.getLatestAccountStepExecContext().accountResp == null) {
            return null;
        }
        //上一步流程的结果状态作为获取下一步流程的key
        String nextStepKey = getLatestAccountStepExecContext().getStepStatus();
        if (StringUtils.isEmpty(nextStepKey)) {
            Logger.info("===Step getNextStep is null, nextStepKey:{}", nextStepKey);
            return null;
        }
        //下一步流程的subTradeType
        String nextStepSubTradeType = currentStep.getNextStepMap().get(nextStepKey);
        Logger.info("===Step getNextStep nextStepSubTradeType :{} ", nextStepSubTradeType);

        if (StepConstant.STEP_END.equals(nextStepSubTradeType)) {
            Logger.info("===Step getNextStep is null, nextStepSubTradeType:{}", nextStepSubTradeType);
            return null;
        }
        Step nextStep = getStepCache().getStep(this.tradeType, nextStepSubTradeType);
        Logger.info("===Step getNextStep.nextStep:{}", nextStep);
        return nextStep;
    }

    /**
     * 取最近一次的流程上下文
     *
     * @return
     */
    public AccountExecStepContext getLatestAccountStepExecContext() {
        if (getAccountExecStepContexts() == null || getAccountExecStepContexts().isEmpty()) {
            return null;
        }
        return getAccountExecStepContexts().get(getAccountExecStepContexts().size() - 1);
    }

    /**
     * 账户升级明细持久化
     *
     * @param detailBO
     */
    private void addAccountOpTradeDetailListAndPersist(AccountOpTradeDetailBO detailBO) {
        this.accountOpTradeBO.setAccountOpTradeDetailBO(detailBO);
        this.getAccountOpTradeDetailBOList().add(detailBO);
        getAcctOpTradeRepository().addAccountOpTradeDetail(detailBO);
    }

    /**
     * 账户升级明细信息更新
     */
    public void updateAccountOpTradeDetail() {
        AccountOpTradeDetailBO detailBO = accountOpTradeBO.getAccountOpTradeDetailBO();
        if (StringUtils.isNotEmpty(detailBO.getReplyMsg()) && detailBO.getReplyMsg().length() > 64) {
            detailBO.setReplyMsg(detailBO.getReplyMsg().substring(0, 64));
        }
        getAcctOpTradeRepository().updateAccountOpTradeDetailStatus(detailBO);
    }

    public void updateOpTradeAndPersist() {
        //明细返回信息转到总订单
        convertOpTrade(accountOpTradeBO, accountOpTradeBO.getAccountOpTradeDetailBO());
        AccountOpTradeBO tradeBO = getAccountOpTradeBO();
        if (StringUtils.isNotEmpty(tradeBO.getReplyMsg()) && tradeBO.getReplyMsg().length() > 80) {
            tradeBO.setReplyMsg(tradeBO.getReplyMsg().substring(0, 80));
        }
        getAcctOpTradeRepository().updateAccountOpTradeStatus(tradeBO);
    }

    private void convertOpTrade(AccountOpTradeBO tradeBO, AccountOpTradeDetailBO detailBO) {
        tradeBO.setStatus(detailBO.getStatus());
        tradeBO.setReplyCode(detailBO.getReplyCode());
        tradeBO.setReplyMsg(detailBO.getReplyMsg());
    }


    private AccountOpTradeRepository getAcctOpTradeRepository() {
        return applicationContext.getBean(AccountOpTradeRepository.class);
    }

    private InitializingStep getStepCache() {
        return applicationContext.getBean(InitializingStep.class);
    }

    private AccountRequestConverter getAccountRequestConverter() {
        return applicationContext.getBean(AccountRequestConverter.class);
    }

    private WechatNotifyTool getWechatNotifyTool(){
        return applicationContext.getBean(WechatNotifyTool.class);
    }






    /**
     * 账户交易相关
     */
    //账户支付
    private AccountTradeBO accountTradeBO;
    //账户支付明细
    private List<AccountTradeDetailBO> accountTradeDetailBOList = new ArrayList<>();

    public AccountExecContext(ApplicationContext applicationContext, AccountTradeBO req, boolean isResume) {
        this.applicationContext = applicationContext;
        initAccountTradeReq(req);
        initAccountTradeCurrentStep();
        this.tradeType = req.getTradeType();
        this.isResume = isResume;
    }

    /**
     * 账户交易初始化
     * @param req
     */
    private void initAccountTradeReq(AccountTradeBO req) {
        AccountTradeBO accountTradeBO = getAccountTradeRepository().queryAcctTrade(req);
        if(Objects.isNull(accountTradeBO)) {
            // 新增交易表
            req.setStatus(ResultStatus.P.getCode());
            getAccountTradeRepository().addAccountTrade(req);
            // 新增交易用户路由表
            getAccountCustomerRouteRepository().addAccountCustomerRoute(new AccountCustomerRouteBO(req.getTradeNo(), req.getCustomerCode()));
        } else {
            //查询最新的明细记录
            req = getAccountTradeRepository().queryAcctTradeDetail(accountTradeBO);
            //查询全部的明细列表
            this.accountTradeDetailBOList = getAccountTradeRepository().queryAcctTradeDetailList(accountTradeBO);
        }
        Logger.info("initAccountPayReq 初始化 accountTrade:{}", DataMask.toJSONString(req));
        this.accountTradeBO = req;
    }

    /**
     * 账户支付流程初始化
     */
    private void initAccountTradeCurrentStep() {
        AccountTradeBO accountTradeBO = this.accountTradeBO;
        if (Objects.isNull(accountTradeBO.getAccountTradeDetailBO())) {
            this.currentStep = getStepCache().getStep(accountTradeBO.getTradeType());
        } else {
            AccountTradeDetailBO detailBO = accountTradeBO.getAccountTradeDetailBO();
            this.currentStep = getStepCache().getStep(accountTradeBO.getTradeType(), detailBO.getSubTradeType());
        }
        Logger.info("initAccountTradeCurrentStep 初始化 currentStep:{}", currentStep);
    }


    private AccountTradeRepository getAccountTradeRepository() {
        return applicationContext.getBean(AccountTradeRepository.class);
    }

    private AccountCustomerRouteRepository getAccountCustomerRouteRepository() {
        return applicationContext.getBean(AccountCustomerRouteRepository.class);
    }

    /**
     * 账户交易子流程上下文初始化
     * @param step
     * @return
     */
    public AccountExecStepContext initAccountTradeStepContext(Step step) {
        AccountExecStepContext accountExecStepContext = new AccountExecStepContext();
        //传递初始化属性
        accountExecStepContext.getExtMap().putAll(step.getExtMap());
        this.accountExecStepContexts.add(accountExecStepContext);
        //检查当前流程detail
        AccountTradeDetailBO detailBO = getCurrentStepTradeDetail(step);
        //终态直接返回
        if (detailBO != null && ResultStatus.S.getCode().equals(detailBO.getStatus())) {
            accountExecStepContext.setStepStatus(ResultStatus.S.getCode());
            accountExecStepContext.setContinue(false);
            return accountExecStepContext;
        } else if (Objects.nonNull(detailBO) && ResultStatus.P.getCode().equals(detailBO.getStatus())) {
            //查询订单
            accountExecStepContext.setAction(StepConstant.ACTION_QUERY);
        } else {
            accountExecStepContext.setAction(StepConstant.ACTION_EXEC);
            if (Objects.isNull(detailBO)) {
                //新流程
                detailBO = this.addAccountTradeDetailAndPersist(0, ResultStatus.P.getCode(), step);
            } else if (ResultStatus.F.getCode().equals(detailBO.getStatus())
                    && null != step.getExtMap()
                    && StepEnum.IsRetryAble.Y.getCode().equals(step.getExtMap().get(StepConstant.IS_RETRYABLE))
                    && StringUtils.isNotEmpty(step.getExtMap().get(StepConstant.MAX_RETRY_TIMES))) {
                //重试流程
                int retryTimes = detailBO.getRetryTimes() + 1;
                String maxRetryTimes = step.getExtMap().get(StepConstant.MAX_RETRY_TIMES);
                //除主动修复场景外，超过最大重试次数，流程结束
                if (!isResume && retryTimes >= Integer.parseInt(maxRetryTimes)) {
                    Logger.info("initAccountTradeStepContext retry exceed max limit! subTradeType:{}", step.getSubTradeType());
                    //超过最大次数终止流程
                    accountExecStepContext.setStepStatus(ResultStatus.F.getCode());
                    accountExecStepContext.setContinue(false);
                    return accountExecStepContext;
                } else {
                    detailBO = this.addAccountTradeDetailAndPersist(retryTimes, ResultStatus.P.getCode(), step);
                }
            } else {
                //非法状态
                accountExecStepContext.setContinue(false);
                accountExecStepContext.setStepStatus(ResultStatus.F.getCode());
                return accountExecStepContext;
            }
        }
        accountExecStepContext.setAccountTradeDetailBO(detailBO);
        //组装明细流程参数
        AccountTradeReq accountReq = getAccountRequestConverter().buildAccountTradeReq(accountTradeBO, detailBO, accountExecStepContext.getAction());
        accountExecStepContext.setAccountTradeReq(accountReq);
        return accountExecStepContext;
    }

    /**
     * 账户交易子流程记录持久化
     * @param retryTimes
     * @param status
     * @param step
     * @return
     */
    private AccountTradeDetailBO addAccountTradeDetailAndPersist(int retryTimes, String status, Step step) {
        AccountTradeDetailBO detailBO = new AccountTradeDetailBO();
        detailBO.setAccountTradeId(this.accountTradeBO.getId());
        detailBO.setCustomerCode(accountTradeBO.getCustomerCode());
        detailBO.setRetryTimes(retryTimes);
        detailBO.setExtField(accountTradeBO.getExtField());
        detailBO.setExtField(step.getExtMap());
        if (StringUtils.isNotEmpty(status)) {
            detailBO.setStatus(status);
        } else {
            detailBO.setStatus(ResultStatus.P.getCode());
        }
        detailBO.setTradeType(accountTradeBO.getTradeType());
        detailBO.setSubTradeType(step.getSubTradeType());
        String detailPayNo;
        int suffix = this.getAccountTradeDetailBOList().size();
        String detailOrderNoPre = accountTradeBO.getTradeNo();
        detailPayNo = step.getDetailOrderNo(detailOrderNoPre, suffix + "");
        detailBO.setDetailPayNo(detailPayNo);
        detailBO.setTradeNo(this.accountTradeBO.getTradeNo());
        this.addAccountTradeDetailListAndPersist(detailBO);
        return detailBO;
    }

    private void addAccountTradeDetailListAndPersist(AccountTradeDetailBO detailBO) {
        this.accountTradeBO.setAccountTradeDetailBO(detailBO);
        this.getAccountTradeDetailBOList().add(detailBO);
        getAccountTradeRepository().addAccountTradeDetail(detailBO);
    }

    /**
     * 账户交易明细
     *
     * @param step
     * @return
     */
    private AccountTradeDetailBO getCurrentStepTradeDetail(Step step) {
        //这个detail 是初始化的，或者是上一个阶段的执行结果
        AccountTradeDetailBO detailBO = accountTradeBO.getAccountTradeDetailBO();
        if (Objects.isNull(detailBO)) {
            return null;
        }
        //流程子类型不一致返回null
        if (!step.getSubTradeType().equals(detailBO.getSubTradeType())) {
            return null;
        }
        return detailBO;
    }

    /**
     * 账户交易主流程记录更新
     */
    public void updateTradeAndPersist() {
        //明细返回信息转到总订单
        convertTrade(accountTradeBO, accountTradeBO.getAccountTradeDetailBO());
        if (StringUtils.isNotEmpty(accountTradeBO.getReplyMsg()) && accountTradeBO.getReplyMsg().length() > 64) {
            accountTradeBO.setReplyMsg(accountTradeBO.getReplyMsg().substring(0, 64));
        }
        getAccountTradeRepository().updateAccountTradeStatus(accountTradeBO);
    }

    private void convertTrade(AccountTradeBO accountTradeBO, AccountTradeDetailBO detailBO) {
        accountTradeBO.setStatus(detailBO.getStatus());
        accountTradeBO.setReplyCode(detailBO.getReplyCode());
        accountTradeBO.setReplyMsg(detailBO.getReplyMsg());
    }

    /**
     * 账户交易返回信息
     */
    public void updateTradeRespCode() {
        accountTradeBO.setReplyCode(accountTradeBO.getAccountTradeDetailBO().getReplyCode());
        accountTradeBO.setReplyMsg(accountTradeBO.getAccountTradeDetailBO().getReplyMsg());
    }

    /**
     * 账户交易明细记录更新
     */
    public void updateAccountTradeDetail() {
        AccountTradeDetailBO detailBO = accountTradeBO.getAccountTradeDetailBO();
        if (StringUtils.isNotEmpty(detailBO.getReplyMsg()) && detailBO.getReplyMsg().length() > 64) {
            detailBO.setReplyMsg(detailBO.getReplyMsg().substring(0, 64));
        }
        getAccountTradeRepository().updateAccountTradeDetailStatus(detailBO);
    }
}
