package com.akucun.account.proxy.service.acct;

import com.akucun.account.proxy.service.acct.bo.AccountOpTradeBO;
import com.akucun.account.proxy.service.acct.bo.AccountOpTradeResp;

/**
 * @Author: silei
 * @Date: 2020/8/26
 * @desc: 账户升级相关
 */
public interface AccountOpTradeService {

    /**
     * 账户升级流程执行接口
     * @param req
     * @param isResume 是否强制修复
     * @return
     */
    AccountOpTradeResp execute(AccountOpTradeBO req, boolean isResume);

}
