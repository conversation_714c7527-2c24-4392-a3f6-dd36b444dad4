package com.akucun.account.proxy.service.withdraw.task.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.service.help.FinClearingCoreFacadeHelp;
import com.akucun.account.proxy.service.withdraw.task.WithdrawTask;
import com.akucun.account.proxy.task.request.FinTaskAcceptRequest;

@Component
public class WithdrawTaskImpl implements WithdrawTask{
	
	@Autowired
	private FinClearingCoreFacadeHelp finClearingCoreFacadeHelp;
	
	@Override
	public Result<Void> accept(FinTaskAcceptRequest finTaskAcceptRequest) {
		return finClearingCoreFacadeHelp.accept(finTaskAcceptRequest);
	}

}
