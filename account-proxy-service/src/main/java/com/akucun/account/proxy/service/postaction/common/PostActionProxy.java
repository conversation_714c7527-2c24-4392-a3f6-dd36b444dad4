package com.akucun.account.proxy.service.postaction.common;

import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: silei
 * @Date: 2020/12/8
 * @desc:
 */
@Component
public class PostActionProxy {

    private final Map<String, AbsPostActionExecutor> executorMap = new HashMap<>();

    public void register(String actionType, AbsPostActionExecutor executor) {
        executorMap.put(actionType, executor);
    }

    public AbsPostActionExecutor getExecutor(String actionType){
        return executorMap.get(actionType);
    }


}
