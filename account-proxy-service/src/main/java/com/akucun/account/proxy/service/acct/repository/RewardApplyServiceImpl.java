package com.akucun.account.proxy.service.acct.repository;

import com.akucun.account.proxy.dao.mapper.RewardApplyMapper;
import com.akucun.account.proxy.dao.model.RewardApply;
import com.akucun.account.proxy.service.acct.RewardApplyService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : 奖励申请-数据库操作
 * @Create on : 2025/1/15 13:46
 **/
@Repository
public class RewardApplyServiceImpl extends ServiceImpl<RewardApplyMapper, RewardApply> implements RewardApplyService {
    @Override
    public List<RewardApply> queryRewardApplyList(String requestNo, String busiType, String transBillDate, String activityNo) {
        return getBaseMapper().selectList(new LambdaQueryWrapper<RewardApply>()
                .eq(!StringUtils.isEmpty(requestNo), RewardApply::getRequestNo, requestNo)
                .eq(!StringUtils.isEmpty(busiType), RewardApply::getBusiType, busiType)
                .eq(!StringUtils.isEmpty(transBillDate), RewardApply::getTransBillDate, transBillDate)
                .eq(!StringUtils.isEmpty(activityNo), RewardApply::getActivityNo, activityNo)
        );
    }

    @Override
    public List<RewardApply> queryRewardApplyRecords(String requestNo, String busiType, String transBillDate, String userCode, String userType) {
        return getBaseMapper().selectList(new LambdaQueryWrapper<RewardApply>()
                .eq(!StringUtils.isEmpty(requestNo), RewardApply::getRequestNo, requestNo)
                .eq(!StringUtils.isEmpty(busiType), RewardApply::getBusiType, busiType)
                .eq(!StringUtils.isEmpty(transBillDate), RewardApply::getTransBillDate, transBillDate)
                .eq(!StringUtils.isEmpty(userCode), RewardApply::getUserCode, userCode)
                .eq(!StringUtils.isEmpty(userType), RewardApply::getUserType, userType)
        );
    }

    @Override
    public List<RewardApply> queryByBatchNo(String batchNo) {
        return getBaseMapper().selectList(new LambdaQueryWrapper<RewardApply>()
                .eq(!StringUtils.isEmpty(batchNo), RewardApply::getBatchNo, batchNo)
        );
    }

    @Override
    public Integer batchSave(List<RewardApply> records) {
        int effectNums = 0;
        for (RewardApply item : records) {
            int effectNum =this.getBaseMapper().insert(item);
            effectNums += effectNum;
        }

        return effectNums;
    }

    @Override
    public Integer saveRecord(RewardApply record) {
        return this.getBaseMapper().insert(record);
    }

    @Override
    public Integer updatesStatusById(RewardApply record) {
        return getBaseMapper().updatesStatusById(record);
    }

    @Override
    public RewardApply load(Long id) {
        return getBaseMapper().selectById(id);
    }

}
