package com.akucun.account.proxy.service.acct;

import com.akucun.account.proxy.dao.model.WechatWithdrawSummary;
import com.baomidou.mybatisplus.extension.service.IService;

import java.math.BigDecimal;

/**
 * @Author: silei
 * @Date: 2021/9/4
 * @desc:
 */
public interface WechatWithdrawSummaryService extends IService<WechatWithdrawSummary> {

    void addAmount(Long id, BigDecimal withdrawAmount);

    void subtractAmount(Long id, BigDecimal withdrawAmount);
}
