package com.akucun.account.proxy.service.acct.impl;

import com.akucun.account.proxy.facade.stub.enums.RewardTypeEnum;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : 赋能营-奖励下发
 * @Create on : 2025/2/17 14:45
 **/
@Service
public class EmpowermentCampServiceImpl extends AbstractPromoTradeService{
    static final RewardTypeEnum busiType = RewardTypeEnum.EMPOWERMENT_CAMP;

    //=========== 获取当前业务类别 ==============
    @Override
    public RewardTypeEnum getBusiType() {
        return busiType;
    }
}
