package com.akucun.account.proxy.domain.model;

import com.aikucun.common2.log.Logger;
import com.akucun.account.proxy.service.common.SpringContextHolder;
import com.akucun.fps.common.entity.Result;
import com.akucun.fps.pingan.client.vo.DealTradeVO;
import com.akucun.fps.pingan.feign.api.member.MemberServiceApi;
import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.Random;

/**
 * 罚扣
 */
@Service
public class PinganDeduction extends PinganAdjust {
    @Override
    public Pair<Integer,String> done() {
        //构建请求体
        DealTradeVO dealTradeVO=new DealTradeVO();
        dealTradeVO.setContent(this.getRemark());
        dealTradeVO.setCustomerCode(this.getCustomerCode());
        dealTradeVO.setCustomerType(this.getCustomerType());
        dealTradeVO.setOrderNo(System.currentTimeMillis() + "" + (new Random().nextInt(10)));
        dealTradeVO.setRemark(this.getRemark());
        dealTradeVO.setTranAmount(this.getAmount());

        if(dealTradeVO.getContent().isEmpty()||dealTradeVO.getCustomerCode().isEmpty()||dealTradeVO.getCustomerType().isEmpty()||
                dealTradeVO.getOrderNo().isEmpty()|| ObjectUtils.isEmpty(dealTradeVO.getTranAmount())
        ){
            return Pair.of(9,"服务异常请重试");
        }
        //请求
        Logger.info("平安罚扣处理开始，dealTradeVO:{}",JSON.toJSONString(dealTradeVO));
        MemberServiceApi memberServiceApi= SpringContextHolder.getBean(MemberServiceApi.class);
        Result<Void> result = memberServiceApi.transaction(dealTradeVO);
        Logger.info("平安罚扣处理结束，参数dealTradeVO:{}, 响应result:{}", JSON.toJSONString(dealTradeVO), JSON.toJSONString(result));

        //结果返回判断
        if (result.isSuccess()) {
            return Pair.of( 0 , "操作成功");
        } else {
            return Pair.of( result.getErrorCode() , result.getErrorMessage());
        }

    }
}
