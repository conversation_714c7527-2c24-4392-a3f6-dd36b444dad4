package com.akucun.account.proxy.service.notify.impl;

import com.akucun.account.proxy.common.constant.ApplyStatus;
import com.akucun.account.proxy.common.constant.CommonConstants;
import com.akucun.account.proxy.common.constant.WithdrawChannelConstants;
import com.akucun.account.proxy.dao.mapper.WithdrawApplyRecordMapper;
import com.akucun.account.proxy.dao.model.WithdrawApplyRecord;
import com.akucun.account.proxy.facade.stub.others.account.req.Notify;
import com.akucun.account.proxy.facade.stub.others.account.req.NotifyReq;
import com.akucun.account.proxy.service.common.SpringContextHolder;
import com.akucun.account.proxy.service.notify.Handler;
import com.akucun.account.proxy.service.postaction.task.WithdrawReceiptDownloadTask;
import com.akucun.common.Result;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mengxiang.base.common.log.Logger;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;

/**
 * 租户提现的消息通知
 */
@Service("TENEMENT_PLATFORM_PINGAN_WITHDRAW_HANDLER")
public class TenementHandler implements Handler<Result<Void>,NotifyReq>{
	
	@Resource
    private WithdrawApplyRecordMapper withdrawApplyRecordMapper;
	
	@Override
	public Result<Void> deal(NotifyReq notifyReq) {
		 Notify bizInfo = notifyReq.getBizInfo();
		 WithdrawApplyRecord record = withdrawApplyRecordMapper.selectOne(new LambdaQueryWrapper<WithdrawApplyRecord>().eq(WithdrawApplyRecord::getWithdrawNo, bizInfo.getWithdrawNo()));
         if (record == null) {
             return Result.error(CommonConstants.GENERAL_CODE, "提现通知未查询到提现单据");
         }
         record.setRemark(notifyReq.getErrorMessage());
         String dbApplyStatus = record.getApplyStatus();
         if (StringUtils.isBlank(dbApplyStatus) || StringUtils.equals(dbApplyStatus, ApplyStatus.SUCC.name()) || StringUtils.equals(dbApplyStatus, ApplyStatus.FAIL.name())) {
             Logger.warn("提现状态已经为终态, 异步通知参数：{}", JSONObject.toJSONString(notifyReq));
             return Result.success();
         }
        if (notifyReq.isSuccess()) {
			success(bizInfo,record);
		} else {
			fail(notifyReq,record);
		}
        return Result.success();
	}
	
	public void success(Notify bizInfo,WithdrawApplyRecord record) {
		SpringContextHolder.getBean(TransactionTemplate.class).execute(new TransactionCallbackWithoutResult() {
            @Override
            protected void doInTransactionWithoutResult(TransactionStatus status) {
				//更新提现单据状态
				withdrawApplyRecordMapper.updateWithdrawRecordStatus(bizInfo.getWithdrawNo(), ApplyStatus.SUCC.name(), "");

				//新增拉取回执单地址定时任务
				SpringContextHolder.getBean(WithdrawReceiptDownloadTask.class).init(WithdrawChannelConstants.PINGAN.getName(),
						bizInfo.getWithdrawNo(), null, null, false, record.getCustomerType());
            }
        });
	}

	public void fail(NotifyReq notifyReq,WithdrawApplyRecord record) {
		Logger.info("notifyWithdrawResult 用户提现失败:{}", JSONObject.toJSONString(record));
        withdrawApplyRecordMapper.updateWithdrawRecordStatus(notifyReq.getBizInfo().getWithdrawNo(), ApplyStatus.FAIL.name(), notifyReq.getErrorMessage());
	}
}
