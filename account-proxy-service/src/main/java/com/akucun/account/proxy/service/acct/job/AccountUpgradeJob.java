package com.akucun.account.proxy.service.acct.job;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;

import com.aikucun.common2.utils.datamasking.DataMask;
import com.akucun.account.proxy.common.constant.StepConstant;
import com.akucun.account.proxy.common.enums.ResultStatus;
import com.akucun.account.proxy.common.utils.AccountUtils;
import com.akucun.account.proxy.common.utils.AmountUtils;
import com.akucun.account.proxy.dao.mapper.WithdrawApplyRecordMapper;
import com.akucun.account.proxy.dao.model.AccountOpTrade;
import com.akucun.account.proxy.service.acct.AccountOpTradeService;
import com.akucun.account.proxy.service.acct.AccountService;
import com.akucun.account.proxy.service.acct.bo.AccountOpTradeBO;
import com.akucun.account.proxy.service.acct.bo.AccountOpTradeResp;
import com.akucun.account.proxy.service.acct.repository.AccountOpTradeRepository;
import com.akucun.fps.common.entity.Result;
import com.akucun.fps.common.entity.ResultList;
import com.akucun.fps.pingan.client.constants.SelectFlagTypeConstants;
import com.akucun.fps.pingan.client.model.AccountSearchReqDO;
import com.akucun.fps.pingan.client.model.PingAnAccountVO;
import com.akucun.fps.pingan.client.model.po.PinganAccount;
import com.akucun.fps.pingan.client.vo.AccountRegisterVO;
import com.akucun.fps.pingan.feign.api.merchant.MerchantServiceApi;
import com.akucun.fps.pingan.feign.api.merchantquery.MerchantQueryServiceApi;
import com.akucun.fps.pingan.feign.api.settlement.SettlementServiceApi;
import com.alibaba.fastjson.JSON;
import com.mengxiang.base.common.log.Logger;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Component
public class AccountUpgradeJob {

    @Value("${account.proxy.plus.days:-7}")
    private int plusDays;
    @Resource
    private AccountOpTradeRepository accountOpTradeRepository;
    @Resource
    private AccountOpTradeService accountOpTradeService;
    @Resource
    private MerchantQueryServiceApi merchantQueryServiceApi;
    @Autowired
    private AccountService accountService;
    @Resource
    private SettlementServiceApi settlementServiceApi;
    @Resource
    private MerchantServiceApi merchantServiceApi;
    @Autowired
    private WithdrawApplyRecordMapper withdrawApplyRecordMapper;

    @Value("${account.upgrade.withdraw.check.switch:true}")
    private boolean withdrawCheckSwitch;//账户升级提现校验开关
    @Value("${account.proxy.lockTime:86400}")
    private long lockTime;//账户锁定时间24小时

    @XxlJob("accountUpgradeHandler")
    public ReturnT<String> execute(String param) {
        Logger.info("AccountUpgradeJob started!");
        // 分片参数
        Date queryDate = DateUtil.offset(new Date(), DateField.DAY_OF_MONTH, plusDays);
        List<AccountOpTrade> accountTradeList = accountOpTradeRepository.queryAccountUpgrade(queryDate);

        Optional.ofNullable(accountTradeList).ifPresent(accountTrades -> accountTrades.forEach(accountTrade -> {
            AccountOpTradeBO accountOpTradeBO = new AccountOpTradeBO();
            BeanUtils.copyProperties(accountTrade, accountOpTradeBO);
            try {
                //初次请求
                if (ResultStatus.I.getCode().equals(accountTrade.getStatus())){
                    if(checkAndLockAccount(accountTrade)){
                        //检查用户有无平安账户
                        if (accountService.hasPinganAccount(accountTrade.getCustomerCode(), accountTrade.getCustomerType())) {
                            //校验账户有无在途资金、及参数
                            if (checkAccount(accountOpTradeBO)) {
                                //设置交易类型
                                accountOpTradeBO.setTradeType(StepConstant.ACCOUNT_UPDATE);
                                AccountOpTradeResp resp = accountOpTradeService.execute(accountOpTradeBO, false);
                                if (resp != null && AccountUtils.isFinalStatus(resp.getStatus())){
                                    //解锁账户
                                    accountService.unlockAccount(AccountUtils.getSellerCode(accountTrade.getCustomerCode(), accountTrade.getCustomerType()), accountTrade.getCustomerType());
                                }
                            }
                        } else {
                            accountOpTradeBO.setStatus(ResultStatus.S.getCode());
                            accountOpTradeBO.setReplyMsg("用户未开户");
                            accountOpTradeRepository.updateAccountOpTradeStatus(accountOpTradeBO);
                            accountService.unlockAccount(AccountUtils.getSellerCode(accountTrade.getCustomerCode(), accountTrade.getCustomerType()), accountTrade.getCustomerType());
                        }
                    }
                }
                //状态为处理中时
                else {
                    AccountOpTradeResp resp = accountOpTradeService.execute(accountOpTradeBO, false);
                    if (resp != null && AccountUtils.isFinalStatus(resp.getStatus())){
                        //解锁账户
                        accountService.unlockAccount(AccountUtils.getSellerCode(accountTrade.getCustomerCode(), accountTrade.getCustomerType()), accountTrade.getCustomerType());
                    }
                }
            } catch (Exception e) {
                Logger.error("账户升级异常 customerCode:{}", accountTrade.getCustomerCode(), e);
            }
        }));
        Logger.info("AccountUpgradeJob finished!");
        return ReturnT.SUCCESS;
    }

    private boolean checkAndLockAccount(AccountOpTrade accountTrade) {
        if(accountService.isTenantCustomer(accountTrade.getCustomerCode(), accountTrade.getCustomerType())) {
            return true;//SAAS店主店长在请求实名变更时已锁定
        }
        if(accountService.hasProcessingTrade(accountTrade.getCustomerCode(), accountTrade.getCustomerType())) {
            return false;
        }
        //转换客户编码（提现记录和锁定账户用到的店主编码是带NM的，但accountOpTrade中店主编码不带NM）
        String customerCode = AccountUtils.getSellerCode(accountTrade.getCustomerCode(), accountTrade.getCustomerType());
        //锁定账户
        // accountService.lockAccount(customerCode, accountTrade.getCustomerType(), lockTime);
        return true;
    }

    /**
     * 检查账户是否存在在途资金、参数
     * @param trade
     * @return
     */
    private boolean checkAccount(AccountOpTradeBO trade) {
        AccountRegisterVO vo = new AccountRegisterVO();
        vo.setCustomerCode(AccountUtils.getSellerCode(trade.getCustomerCode(), trade.getCustomerType()));
        vo.setCustomerType(trade.getCustomerType());
        Result<PinganAccount> result = merchantServiceApi.selectPinganAccount(vo);
        Logger.info("AccountUpgradeJob PinganAccount query result:{}", DataMask.toJSONString(result));
        if (Objects.isNull(result) || !result.isSuccess()|| Objects.isNull(result.getData())){
            Logger.warn("AccountUpgradeJob query PinganAccount exception!,customerCode:{}",trade.getCustomerCode());
            return !ResultStatus.I.getCode().equals(trade.getStatus());
        }
        //参数检查
//        if (!processParam(result.getData(),trade)){
//            Logger.warn("checkAccount param mobile exception!");
//            return false;
//        }
        //查询平安账户余额
        AccountSearchReqDO reqDO = new AccountSearchReqDO();
        reqDO.setSelectFlag(String.valueOf(SelectFlagTypeConstants.GENERAL.getValue()));
        reqDO.setCustAcctId(result.getData().getPinganAccountCode());
        reqDO.setCustomerType(trade.getCustomerType());
        ResultList<PingAnAccountVO> list = settlementServiceApi.selectPlatformAccount(reqDO);
        Logger.info("AccountUpgradeJob balance query result:{}", DataMask.toJSONString(list));
        if (Objects.isNull(list) || !list.isSuccess() || CollectionUtils.isEmpty(list.getDatalist())){
            Logger.warn("AccountUpgradeJob query PingAnAccountVO error, customerCode:{}",trade.getCustomerCode());
            return !ResultStatus.I.getCode().equals(trade.getStatus());
        }
        List<PingAnAccountVO> datalist = (List<PingAnAccountVO>) list.getDatalist();
        Long totalBalance = AmountUtils.yuan2fen(datalist.get(0).getTotalBalance());
        Long totalTranOutAmount = AmountUtils.yuan2fen(datalist.get(0).getTotalTranOutAmount());

        if (totalBalance.compareTo(totalTranOutAmount) != 0) {
            Logger.warn("AccountUpgradeJob accountUpdateError 用户账户余额和可提现余额不相等，存在在途资金！customerCode:{}", trade.getCustomerCode());
            return false;
        }
        return true;
    }

    private boolean processParam(PinganAccount pinganAccount, AccountOpTradeBO trade) {
        if (StringUtils.isBlank(trade.getMobile()) && StringUtils.isBlank(pinganAccount.getMobile())){
            return false;
        }
        boolean paramFlag = false;
        //参数检测
        if (StringUtils.isBlank(trade.getCustomerName())){
            trade.setCustomerName(pinganAccount.getCustomerName());
            paramFlag = true;
        }
        if (StringUtils.isBlank(trade.getMobile()) && StringUtils.isNotEmpty(pinganAccount.getMobile())){
            trade.setMobile(pinganAccount.getMobile());
            paramFlag = true;
        }
        if (paramFlag){
            //更新参数
            accountOpTradeRepository.updateAccountOpTradeStatus(trade);
        }
        return true;
    }

}
