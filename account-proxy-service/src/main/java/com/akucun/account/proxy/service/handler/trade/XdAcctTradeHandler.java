package com.akucun.account.proxy.service.handler.trade;

import com.aikucun.common2.utils.StringUtils;
import com.akucun.account.center.client.model.TradeInfo;
import com.akucun.account.proxy.client.account.AccountClient;
import com.akucun.account.proxy.common.constant.CommonConstants;
import com.akucun.account.proxy.common.constant.CustomerType;
import com.akucun.account.proxy.common.constant.DetailTypeConstants;
import com.akucun.account.proxy.common.enums.ResponseEnum;
import com.akucun.account.proxy.common.enums.ResultStatus;
import com.akucun.account.proxy.common.exception.AccountProxyException;
import com.akucun.account.proxy.common.utils.AccountUtils;
import com.akucun.account.proxy.common.utils.WechatNotifyTool;
import com.akucun.account.proxy.dao.model.AccountTrade;
import com.akucun.account.proxy.facade.stub.enums.AccountKeyConstants;
import com.akucun.account.proxy.facade.stub.enums.TradeChannel;
import com.akucun.account.proxy.service.common.bo.AccountExecStepContext;
import com.akucun.account.proxy.service.common.bo.AccountResp;
import com.akucun.account.proxy.service.common.bo.AccountTradeReq;
import com.akucun.account.proxy.service.common.step.AbstractHandler;
import com.akucun.account.proxy.service.trade.AccountTradeCommonService;
import com.akucun.account.proxy.service.trade.AccountTradeService;
import com.akucun.common.Result;
import com.akucun.fps.pingan.feign.api.member.MemberServiceApi;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;


/**
 * <AUTHOR>
 * @date 2020/12/21 16:45
 */
@Component
public class XdAcctTradeHandler extends AbstractHandler {

    @Autowired
    private AccountClient accountClient;
    @Autowired
    private AccountTradeCommonService accountTradeCommonService;
    @Resource
    private MemberServiceApi memberService;
    @Autowired
    private WechatNotifyTool wechatNotifyTool;
    @Autowired
    private AccountTradeService accountTradeService;
    private static final String TRADE_FAIL = "购买金币未成功，请联系客服处理～！";

    @Override
    protected boolean preCheck(AccountExecStepContext accountExecStepContext) {
        return true;
    }

    @Override
    protected void doSubmitBefore(AccountExecStepContext accountExecStepContext) {

    }

    @Override
    protected void doSubmit(AccountExecStepContext accountExecStepContext) {
        AccountTradeReq req = accountExecStepContext.getAccountTradeReq();
        Result result = Result.success();
        TradeInfo tradeInfo = new TradeInfo();

        if(CommonConstants.USER_ROLE_3.equals(req.getUserRole())) {
            tradeInfo.setAccountTypeKey(AccountKeyConstants.NMDL.getName());
            tradeInfo.setTradeType(DetailTypeConstants.TRADE_TYPE_267.getName());
        } else if(CommonConstants.USER_ROLE_2.equals(req.getUserRole())) {
            tradeInfo.setAccountTypeKey(AccountKeyConstants.NM.getName());
            tradeInfo.setTradeType(DetailTypeConstants.TRADE_TYPE_040.getName());
        }

        if(StringUtils.isNullOrEmpty(tradeInfo.getAccountTypeKey()) || StringUtils.isNullOrEmpty(tradeInfo.getTradeType())){
            throw new AccountProxyException(ResponseEnum.TRADE_NOT_CONFIG);
        }
        //店主编号需要拼接
        String customerType = CommonConstants.USER_ROLE_2.equals(req.getUserRole()) ? CustomerType.NM.getName() : CustomerType.NMDL.getName();
        tradeInfo.setCustomerCode(AccountUtils.getSellerCode(req.getCustomerCode(), customerType));
        tradeInfo.setAmount(req.getAmount().setScale(2, BigDecimal.ROUND_HALF_UP));
        tradeInfo.setTradeNo(req.getTradeNo());
        tradeInfo.setSourceBillNo(req.getSourceNo());
        tradeInfo.setRemark(TradeChannel.queryDescByCode(req.getChannel()) + ":" + req.getSourceNo());
        Result<Void> tradeResult = accountClient.newBalanceTrade(tradeInfo);

        if (null != tradeResult && tradeResult.isSuccess()) {
            result.setSuccess(Boolean.TRUE);

        } else {
            result.setSuccess(Boolean.FALSE);
            result.setCode(tradeResult.getCode());
            if (100600 != tradeResult.getCode()) {
                result.setMessage(TRADE_FAIL);
            } else {
                result.setMessage(tradeResult.getMessage());
            }
        }
        accountExecStepContext.setRespMessage(result);
    }

    @Override
    protected void doSubmitAfter(AccountExecStepContext accountExecStepContext) throws Exception {
        Result<Void> result = (Result<Void>) accountExecStepContext.getRespMessage();
        AccountResp resp = accountExecStepContext.getAccountResp();
        AccountTradeReq req = accountExecStepContext.getAccountTradeReq();

        LambdaQueryWrapper wrapper =
                new LambdaQueryWrapper<AccountTrade>()
                        .eq(AccountTrade::getTradeNo, req.getTradeNo())
                        .eq(AccountTrade::getTradeType, CommonConstants.BONUS_ACCOUNT_PAY_TRADE_TYPE)
                        .eq(AccountTrade::getCustomerCode, req.getCustomerCode());
        if(result.isSuccess()) {
            // 更新为成功
            AccountTrade accountTrade = AccountTrade.builder().status(ResultStatus.S.getCode()).build();
            accountTradeService.update(accountTrade, wrapper);
            resp.setStatus(ResultStatus.S.getCode());
            resp.setReplyCode(CommonConstants.SUCC_CODE);
            resp.setReplyMsg(ResultStatus.S.getDesc());
        } else {
            // 更新为失败
            AccountTrade accountTrade = AccountTrade.builder().status(ResultStatus.P.getCode()).build();
            accountTradeService.update(accountTrade, wrapper);
            resp.setStatus(ResultStatus.F.getCode());
            resp.setReplyCode(Integer.toString(result.getCode()));
            resp.setReplyMsg(result.getMessage());
        }
    }



}
