package com.akucun.account.proxy.service.acct.util;

import com.akucun.account.proxy.common.constant.CommonConstants;
import com.akucun.account.proxy.dao.model.AccountTrade;
import com.akucun.account.proxy.service.acct.bo.AccountOpTradeBO;
import com.akucun.account.proxy.service.acct.bo.AccountOpTradeDetailBO;
import com.akucun.account.proxy.service.common.bo.AccountReq;
import com.akucun.account.proxy.service.common.bo.AccountTradeReq;
import com.akucun.account.proxy.service.trade.bo.AccountTradeBO;
import com.akucun.account.proxy.service.trade.bo.AccountTradeDetailBO;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

/**
 * @Author: silei
 * @Date: 2020/8/27
 * @desc:
 */
@Component
public class AccountRequestConverter {

    public AccountReq buildAccountReq(AccountOpTradeBO tradeBO, AccountOpTradeDetailBO detailBO, String action) {
        AccountReq req = new AccountReq();
        req.setAccountTradeId(tradeBO.getId());
        req.setAccountProperty(tradeBO.getAccountProperty());
        req.setCustomerCode(tradeBO.getCustomerCode());
        req.setCustomerType(tradeBO.getCustomerType());
        req.setCustomerName(tradeBO.getCustomerName());
        req.setMobile(tradeBO.getMobile());
        req.setNickName(tradeBO.getNickName());
        req.setDetailOrderNo(detailBO.getDetailOrderNo());
        req.setTradeType(detailBO.getTradeType());
        req.setSubTradeType(detailBO.getSubTradeType());
        //平安侧账户编码
        req.getReqMap().put(CommonConstants.PINGAN_CUST_ID, tradeBO.getExtField().get(CommonConstants.PINGAN_CUST_ID));
        req.setBankCardCode(tradeBO.getExtField().get(CommonConstants.BANK_CARD_CODE));
        if (StringUtils.isNotBlank(tradeBO.getExtField().get(CommonConstants.UPDATE_STATUS))) {
            req.setAccountStatus(Integer.valueOf(tradeBO.getExtField().get(CommonConstants.UPDATE_STATUS)));
        }

        return req;

    }


    public AccountTradeReq buildAccountTradeReq(AccountTradeBO tradeBO, AccountTradeDetailBO detailBO, String action) {
        AccountTradeReq req = new AccountTradeReq();
        req.setSourceCode(tradeBO.getSourceCode());
        req.setChannel(tradeBO.getExtField().get(CommonConstants.TRADE_CHANNEL_NAME));
        req.setSourceNo(tradeBO.getSourceNo());
        req.setTradeNo(tradeBO.getTradeNo());
        req.setUserId(tradeBO.getExtField().get(CommonConstants.TRADE_USERID));
        req.setCustomerCode(tradeBO.getCustomerCode());
        req.setCustomerType(tradeBO.getCustomerType());
        req.setCustomerName(tradeBO.getCustomerName());
        req.setAmount(tradeBO.getAmount());
        req.setTradeType(tradeBO.getTradeType());
        req.setBizRefundNo(tradeBO.getExtField().get(CommonConstants.BIZ_REFUND_NO));
        req.setUserRole(tradeBO.getExtField().get(CommonConstants.USER_ROLE));
        req.setTenantId(tradeBO.getExtField().get(CommonConstants.TENANT_ID));
        return req;
    }


}
