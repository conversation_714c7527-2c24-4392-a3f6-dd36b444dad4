package com.akucun.account.proxy.service.acct.impl;

import com.aikucun.common2.utils.datamasking.DataMask;
import com.akucun.account.proxy.common.enums.ResponseEnum;
import com.akucun.account.proxy.common.enums.ResultStatus;
import com.akucun.account.proxy.common.exception.AccountProxyException;
import com.akucun.account.proxy.service.acct.AccountOpTradeService;
import com.akucun.account.proxy.service.acct.bo.AccountOpTradeBO;
import com.akucun.account.proxy.service.acct.bo.AccountOpTradeResp;
import com.akucun.account.proxy.service.common.bo.AccountExecContext;
import com.akucun.account.proxy.service.common.bo.AccountExecStepContext;
import com.akucun.account.proxy.service.common.step.Step;
import com.alibaba.fastjson.JSON;
import com.mengxiang.base.common.log.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * @Author: silei
 * @Date: 2020/8/26
 * @desc:
 */
@Service
public class AccountOpTradeServiceImpl implements AccountOpTradeService {

    @Autowired
    ApplicationContext applicationContext;

    @Override
    public AccountOpTradeResp execute(AccountOpTradeBO req, boolean isResume) {
        AccountOpTradeResp resp;
        AccountExecContext accountExecContext;
        Logger.info("AccountTradeServiceImpl execute 入参:{}", DataMask.toJSONString(req));
        try {
            accountExecContext = new AccountExecContext(applicationContext, req, isResume);
            if (ResultStatus.S.getCode().equals(req.getStatus()) || ResultStatus.F.getCode().equals(req.getStatus())) {
                resp = buildTradeResp(accountExecContext);
                return resp;
            }
            //执行
            startExecuteStep(accountExecContext);
            Logger.info("AccountTradeServiceImpl 交易完成 执行上下文 accountExecContext：" + accountExecContext);
            if (accountExecContext.isEnd()) {
                accountExecContext.updateOpTradeAndPersist();
            } else {
                // 不改状态，只更新返回信息
                accountExecContext.updateOpTradeRespCode();
            }
            resp = buildTradeResp(accountExecContext);
        } catch (Exception e) {
            Logger.error("AccountTradeServiceImpl execute Exception", e);
            resp = new AccountOpTradeResp();
            resp.setStatus(ResultStatus.P.getCode());
            resp.setReplyCode(ResponseEnum.PROCESS.getCode() + "");
            resp.setReplyMsg(ResponseEnum.PROCESS.getMessage());
        }
        Logger.info("AccountTradeServiceImpl execute 返回值:{}", resp);
        return resp;
    }

    private void startExecuteStep(AccountExecContext accountExecContext) {

        Step step = accountExecContext.getCurrentStep();
        Logger.info("AccountTradeServiceImpl 当前执行step：" + step + "，当前执行上下文：" + accountExecContext);
        if (Objects.isNull(step)) {
            throw new AccountProxyException("account step为空");
        }
        execStep(step, accountExecContext);
    }

    private void execStep(Step step, AccountExecContext accountExecContext) {
        //具体执行
        step.exec(accountExecContext);
        AccountExecStepContext accountExecStepContext = accountExecContext.getLatestAccountStepExecContext();
        if (!accountExecStepContext.isEnd()) {
            //当前流程异常，无法进行下一步
            Logger.info("AccountTradeServiceImpl=========流程已结束==========");
        } else {
            Step nextStep = accountExecContext.getNextStep();
            if (!Objects.isNull(nextStep)) {
                accountExecContext.setCurrentStep(nextStep);
                execStep(nextStep, accountExecContext);
            } else {
                Logger.info("AccountTradeServiceImpl 获取NextStep为空，step流程已结束！");
            }
        }
    }

    private AccountOpTradeResp buildTradeResp(AccountExecContext accountExecContext) {
        AccountOpTradeResp resp = new AccountOpTradeResp();
        AccountOpTradeBO tradeBO = accountExecContext.getAccountOpTradeBO();

        resp.setOrderNo(tradeBO.getOrderNo());
        resp.setStatus(tradeBO.getStatus());
        resp.setReplyCode(tradeBO.getReplyCode());
        resp.setReplyMsg(tradeBO.getReplyMsg());

        return resp;
    }
}
