package com.akucun.account.proxy.service.handler.trade;

import com.aikucun.common2.utils.datamasking.DataMask;
import com.akucun.account.center.client.model.TradeInfo;
import com.akucun.account.center.client.model.account.AccountBookDetailDO;
import com.akucun.account.proxy.client.account.AccountClient;
import com.akucun.account.proxy.common.constant.CommonConstants;
import com.akucun.account.proxy.common.enums.ResponseEnum;
import com.akucun.account.proxy.common.enums.ResultStatus;
import com.akucun.account.proxy.common.exception.AccountProxyException;
import com.akucun.account.proxy.common.utils.AmountUtils;
import com.akucun.account.proxy.dao.model.AccountTrade;
import com.akucun.account.proxy.facade.stub.enums.TradeChannel;
import com.akucun.account.proxy.service.common.bo.AccountExecStepContext;
import com.akucun.account.proxy.service.common.bo.AccountResp;
import com.akucun.account.proxy.service.common.bo.AccountTradeReq;
import com.akucun.account.proxy.service.common.step.AbstractHandler;
import com.akucun.account.proxy.service.trade.AccountTradeCommonService;
import com.akucun.account.proxy.service.trade.AccountTradeService;
import com.akucun.common.Result;
import com.akucun.member.api.vo.MoneyConsumedVO;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mengxiang.base.common.log.Logger;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2020/12/21 16:45
 */
@Component
public class BonusAcctTradeHandler extends AbstractHandler {

    @Autowired
    private AccountTradeService accountTradeService;
    @Autowired
    private AccountClient accountClient;
    @Autowired
    private AccountTradeCommonService accountTradeCommonService;
    private Object AcccountRe;

    @Override
    protected boolean preCheck(AccountExecStepContext accountExecStepContext) {
        return true;
    }

    @Override
    protected void doSubmitBefore(AccountExecStepContext accountExecStepContext) {

    }

    @Override
    protected void doSubmit(AccountExecStepContext accountExecStepContext) {
        AccountTradeReq accountTradeReq = accountExecStepContext.getAccountTradeReq();
        AccountResp resp = accountExecStepContext.getAccountResp();

        Result result = null;
        try {
            // 获取账户灰度
            String branch = accountClient.getBalanceBranch(accountTradeReq.getCustomerCode());
            if (StringUtils.isEmpty(branch)) {
                throw new AccountProxyException(ResponseEnum.TRADE_GET_ACCOUNT_BRANCH_ERROR);
            }
            if (CommonConstants.BRANCH_MEMBER.equals(branch)) {
                result = consumeMoney(accountTradeReq);
                //余额系统支付成功再次通知新账户系统，后面灰度成功删除
                if (result.isSuccess()) {
                    try {
                        dealTrade(accountTradeReq);
                    } catch (Exception e) {
                        Logger.warn("余额支付成功,再次调用新余额系统失败：{}", e);
                    }
                }
            } else if (CommonConstants.BRANCH_ACCOUNT_CENTER.equals(branch)) {
                result = dealTrade(accountTradeReq);
                //新余额系统支付成功再次通知老账户系统，后面灰度成功删除
                if (result.isSuccess()) {
                    try {
                        consumeMoney(accountTradeReq);
                    } catch (Exception e) {
                        Logger.warn("新余额支付成功,再次调用老余额系统失败：{}", e);
                    }
                }
            }
        } catch (Exception e) {
            Logger.error("余额支付异常，请求参数：{}，异常信息：{}", DataMask.toJSONString(accountTradeReq), e);
            throw new AccountProxyException(ResponseEnum.TRADE_PAY_ERROR);
        }

        if(Objects.isNull(result)) {
            Logger.error("余额支付异常，请求参数：{}，结果为空", DataMask.toJSONString(accountTradeReq));
            throw new AccountProxyException(ResponseEnum.TRADE_PAY_RETURN_NULL);
        }
        accountExecStepContext.setRespMessage(result);
    }

    @Override
    protected void doSubmitAfter(AccountExecStepContext accountExecStepContext) throws Exception {
        Result<Void> result = (Result<Void>) accountExecStepContext.getRespMessage();
        AccountResp resp = accountExecStepContext.getAccountResp();
        AccountTradeReq req = accountExecStepContext.getAccountTradeReq();

        LambdaQueryWrapper wrapper =
                new LambdaQueryWrapper<AccountTrade>()
                        .eq(AccountTrade::getTradeNo, req.getTradeNo())
                        .eq(AccountTrade::getTradeType, CommonConstants.BONUS_ACCOUNT_PAY_TRADE_TYPE)
                        .eq(AccountTrade::getCustomerCode, req.getCustomerCode());
        if(result.isSuccess()) {
            // 更新为成功
            AccountTrade accountTrade = AccountTrade.builder().status(ResultStatus.S.getCode()).build();
            accountTradeService.update(accountTrade, wrapper);
            resp.setStatus(ResultStatus.S.getCode());
            resp.setReplyCode(CommonConstants.SUCC_CODE);
            resp.setReplyMsg(ResultStatus.S.getDesc());
        } else {
            // 更新为失败
            AccountTrade accountTrade = AccountTrade.builder().status(ResultStatus.P.getCode()).build();
            accountTradeService.update(accountTrade, wrapper);
            resp.setStatus(ResultStatus.P.getCode());
            if(result.getCode() > 0){
                resp.setReplyCode("" + result.getCode());
            } else {
                resp.setReplyCode(CommonConstants.FAIL_CODE);
            }
            resp.setReplyMsg(result.getMessage());
        }
    }

    /**
     * 老账户余额支付
     *
     * @param accountTradeReq
     * @return
     */
    private Result<Void> consumeMoney(AccountTradeReq accountTradeReq) {
        Result<Void> result = Result.success();
        MoneyConsumedVO req = new MoneyConsumedVO();
        Map<String, Object> reqdate = new HashMap<String, Object>();
        req.setUserid(accountTradeReq.getUserId());
        req.setJine(AmountUtils.yuan2fen(accountTradeReq.getAmount().setScale(2, BigDecimal.ROUND_HALF_UP).toString()).intValue());
        req.setYuanyin(100);
        req.setParams(reqdate);
        reqdate.put("tuikuanjiluID", accountTradeReq.getTradeNo());
        reqdate.put("dingdanID", accountTradeReq.getTradeNo());
        reqdate.put("transId", accountTradeReq.getTradeNo());
        boolean isSuccess;
        try {
            isSuccess = accountClient.balanceTrade(req);
        } catch (Exception e) {
            Logger.warn("奖励金支付异常，请求参数：{}，异常信息：{}", DataMask.toJSONString(accountTradeReq), e);
            return Result.error(ResponseEnum.BONUS_PAY_EXCEPTION.getCode(), ResponseEnum.BONUS_PAY_EXCEPTION.getMessage());
        }

        if (isSuccess) {
            result.setSuccess(Boolean.TRUE);
        } else {
            result.setSuccess(Boolean.FALSE);
        }
        return result;
    }


    /**
     * 新余额支付
     *
     * @param accountTradeReq
     * @return
     */
    private Result<Void> dealTrade(AccountTradeReq accountTradeReq) {
        Result<Void> result = Result.success();
        TradeInfo tradeInfo = new TradeInfo();
        tradeInfo.setAccountTypeKey(CommonConstants.NEW_BALANCE_CUSTOMER_KEY);
        tradeInfo.setCustomerCode(accountTradeReq.getCustomerCode());
        tradeInfo.setTradeType(CommonConstants.NEW_PAY_BALANCE_CUSTOMER_TRADE_TYPE_008);
        tradeInfo.setAmount(accountTradeReq.getAmount());
        tradeInfo.setTradeNo(accountTradeReq.getTradeNo());
        tradeInfo.setSourceBillNo(accountTradeReq.getSourceNo());
        tradeInfo.setRemark(TradeChannel.queryDescByCode(accountTradeReq.getChannel()) + ":" +  accountTradeReq.getSourceNo());
        // 查询支付数据，获取sourceBillNo
        AccountBookDetailDO bookDetailDO = accountClient.queryAccountDetail(accountTradeReq.getCustomerCode(), accountTradeReq.getSourceNo(), tradeInfo.getAccountTypeKey());
        if(Objects.nonNull(bookDetailDO)) {
            tradeInfo.setSourceBillNo(bookDetailDO.getSourceBillNo());
        }
        Result<Void> tradeResult = accountClient.newBalanceTrade(tradeInfo);
        if (null != tradeResult && tradeResult.isSuccess()) {
            result.setSuccess(Boolean.TRUE);
        } else {
            result.setSuccess(Boolean.FALSE);
            result.setCode(tradeResult.getCode());
            result.setMessage(tradeResult.getMessage());
            Logger.warn("新余额支付失败result={}", DataMask.toJSONString(tradeResult));
        }
        return result;
    }

}
