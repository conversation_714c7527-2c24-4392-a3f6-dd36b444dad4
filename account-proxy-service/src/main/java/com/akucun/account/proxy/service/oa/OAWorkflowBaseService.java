package com.akucun.account.proxy.service.oa;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.facade.stub.others.oa.req.OAWorkflowCreateRequest;
import com.akucun.account.proxy.facade.stub.others.oa.req.OAWorkflowQueryRequest;
import com.akucun.account.proxy.facade.stub.others.oa.resp.OAWorkflowCreateResponse;
import com.akucun.account.proxy.facade.stub.others.oa.resp.OAWorkflowResponseInfo;
import com.akucun.account.proxy.service.oa.bo.OAWorkflowStatusPollingBo;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Create on : 2025/1/14 17:39
 **/
public interface OAWorkflowBaseService {

    /**
     * 创建OA工作流程
     * @param request
     * @return
     */
    Result<OAWorkflowCreateResponse> create(OAWorkflowCreateRequest request);

    /**
     * 查询OA工作流程
     * @param request
     * @return
     */
    Result<OAWorkflowResponseInfo> queryWorkflowByRequestId(OAWorkflowQueryRequest request);

    /**
     * 提交工作流状态轮询任务
     *
     * @param pollingBo
     * @param bizId
     * @param remark
     */
    void submitPollingTask(OAWorkflowStatusPollingBo pollingBo, String bizId, String remark);

}
