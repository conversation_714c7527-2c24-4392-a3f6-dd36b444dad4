package com.akucun.account.proxy.service.notify.impl;

import com.akucun.account.proxy.common.constant.ApplyStatus;
import com.akucun.account.proxy.common.constant.CommonConstants;
import com.akucun.account.proxy.common.constant.WithdrawChannelConstants;
import com.akucun.account.proxy.dao.mapper.TenantWithdrawApplyMapper;
import com.akucun.account.proxy.dao.model.TenantWithdrawApply;
import com.akucun.account.proxy.facade.stub.others.account.req.Notify;
import com.akucun.account.proxy.facade.stub.others.account.req.NotifyReq;
import com.akucun.account.proxy.service.acct.WithdrawTaxService;
import com.akucun.account.proxy.service.common.SpringContextHolder;
import com.akucun.account.proxy.service.notify.Handler;
import com.akucun.account.proxy.service.postaction.task.WithdrawReceiptDownloadTask;
import com.akucun.common.Result;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mengxiang.base.common.log.Logger;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;

/**
 * 企业饷店的店主、店长的提现消息通知
 * 
 * <AUTHOR>
 *
 */
@Service("EMPLOYEE_PLATFORM_PINGAN_WITHDRAW_HANDLER")
public class EmployeMsgHandler implements Handler<Result<Void>, NotifyReq> {

	@Resource
    private TenantWithdrawApplyMapper tenantWithdrawApplyMapper;
	
	@Resource
	private WithdrawTaxService withdrawTaxService;
	
	@Override
	public Result<Void> deal(NotifyReq notifyReq) {
		 Notify bizInfo = notifyReq.getBizInfo();
		 TenantWithdrawApply apply = tenantWithdrawApplyMapper.selectOne(new LambdaQueryWrapper<TenantWithdrawApply>().eq(TenantWithdrawApply::getWithdrawNo, bizInfo.getWithdrawNo()));
		 Logger.info("EmployeMsgHandler 用户提现申请记录:{}", JSONObject.toJSONString(apply));
		 if (apply == null) {
            return Result.error(CommonConstants.GENERAL_CODE, "提现通知未查询到提现单据");
		 }
	     String dbApplyStatus = apply.getApplyStatus();
        if (StringUtils.isBlank(dbApplyStatus) || StringUtils.equals(dbApplyStatus, ApplyStatus.SUCC.name()) || StringUtils.equals(dbApplyStatus, ApplyStatus.FAIL.name())) {
            Logger.warn("提现状态已经为终态, 异步通知参数：{}", JSONObject.toJSONString(notifyReq));
            return Result.success();
        }
        if (notifyReq.isSuccess()) {
			success(bizInfo,apply);
		} else {
			fail(notifyReq,apply);
		}
        return Result.success();
	}

	public void success(Notify bizInfo,TenantWithdrawApply apply) {
		SpringContextHolder.getBean(TransactionTemplate.class).execute(new TransactionCallbackWithoutResult() {
            @Override
            protected void doInTransactionWithoutResult(TransactionStatus status) {
				//更新提现单据状态
				tenantWithdrawApplyMapper.updateWithdrawRecordStatus(bizInfo.getWithdrawNo(), ApplyStatus.SUCC.name(), "");

				//税费更新
				withdrawTaxService.tenantWithdrawSussForTax(apply);
				
				//新增拉取回执单地址定时任务
				SpringContextHolder.getBean(WithdrawReceiptDownloadTask.class).init(WithdrawChannelConstants.PINGAN.getName(),
						bizInfo.getWithdrawNo(), null, null, true, apply.getCustomerType());
            }
        });
	}

	public void fail(NotifyReq notifyReq,TenantWithdrawApply apply) {
		Logger.info("EmployeMsgHandler 用户提现失败:{}", JSONObject.toJSONString(notifyReq));
		//提现状态修改
        tenantWithdrawApplyMapper.updateWithdrawRecordStatus(notifyReq.getBizInfo().getWithdrawNo(), ApplyStatus.FAIL.name(), notifyReq.getErrorMessage());
		//回退税费
		withdrawTaxService.tenantWithdrawFailForTax(apply, notifyReq.getErrorMessage());
		//回退手续费
        withdrawTaxService.tenantWithdrawFailForServiceFee(apply);
	}

}
