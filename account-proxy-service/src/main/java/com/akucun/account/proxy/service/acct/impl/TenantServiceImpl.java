package com.akucun.account.proxy.service.acct.impl;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.akucun.account.proxy.dao.mapper.AccountTenantCustomerMapper;
import com.akucun.account.proxy.dao.mapper.PostActionItemMapper;
import com.akucun.account.proxy.dao.model.AccountTenantCustomer;
import com.akucun.account.proxy.dao.model.PostActionItem;
import com.akucun.account.proxy.service.acct.TenantService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

@Service
public class TenantServiceImpl extends ServiceImpl<AccountTenantCustomerMapper, AccountTenantCustomer> implements TenantService{
	
	@Override
	public AccountTenantCustomer selectOne(String customerCode,String customerType) {
		LambdaQueryWrapper<AccountTenantCustomer> tenantWrapper = new LambdaQueryWrapper<>();
		if(StringUtils.isBlank(customerCode)||StringUtils.isBlank(customerType)) {
			return null;
		}
        tenantWrapper.eq(AccountTenantCustomer::getCustomerCode, customerCode)
                .eq(AccountTenantCustomer::getCustomerType, customerType);
        return this.baseMapper.selectOne(tenantWrapper);
	}

}
