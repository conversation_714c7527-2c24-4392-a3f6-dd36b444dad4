package com.akucun.account.proxy.service.postaction.task;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.common.enums.PostActionTypes;
import com.akucun.account.proxy.dao.model.PostActionItem;
import com.akucun.account.proxy.service.postaction.common.AbsPostActionExecutor;
import com.akucun.account.proxy.service.withdraw.task.WithdrawTask;
import com.akucun.account.proxy.task.request.FinTaskAcceptRequest;
import com.akucun.fps.common.util.GsonUtils;
import com.alibaba.fastjson.JSON;
import com.mengxiang.base.common.log.Logger;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;

@Component
public class WithdrawServiceTask extends AbsPostActionExecutor {
	@Autowired
	private WithdrawTask withdrawTask;

	@Override
	protected String getActionType() {
		return PostActionTypes.PINGAN_WITHDRAW.getName();
	}

	@XxlJob("withdrawServiceTask")
	public ReturnT<String> execute(String param) {
		return this.executeEntrance();
	}

	@Override
	public Result<Void> execute(PostActionItem item) {
		Result<Void> resp = Result.success();
		try {
			FinTaskAcceptRequest finTaskAcceptRequest = GsonUtils.getInstance().fromJson(item.getParam(), FinTaskAcceptRequest.class);
			resp =  withdrawTask.accept(finTaskAcceptRequest);
			Logger.info("WithdrawServiceTask execute result:{}", JSON.toJSONString(resp));
		}catch (Exception e) {
			resp.setSuccess(false);
			resp.setMessage(e.getMessage());
            Logger.error("提交任务失败：", e);
		}
		 return resp;
	}
}
