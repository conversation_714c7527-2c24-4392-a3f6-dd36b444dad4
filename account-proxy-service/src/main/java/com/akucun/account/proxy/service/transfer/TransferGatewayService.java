package com.akucun.account.proxy.service.transfer;

import com.akucun.account.proxy.dao.model.TransferGateway;
import com.akucun.account.proxy.facade.stub.others.dto.req.TransferGatewayReq;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 付款网关表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-26
 */
public interface TransferGatewayService extends IService<TransferGateway> {

    /**
     * 保存或更新
     * @param transferGatewayReq
     */
    void saveUpdate(TransferGatewayReq transferGatewayReq);
}
