package com.akucun.account.proxy.service.acct.impl;

import com.aikucun.common2.log.Logger;
import com.aikucun.common2.utils.BeanUtils;
import com.aikucun.common2.utils.CollectionUtils;
import com.aikucun.common2.utils.StringUtils;
import com.aikucun.common2.utils.datamasking.DataMask;
import com.akucun.account.proxy.client.customer.CustomerServiceClient;
import com.akucun.account.proxy.client.customer.entity.SubmitCertificationAuditReq;
import com.akucun.account.proxy.common.enums.ResponseEnum;
import com.akucun.account.proxy.common.enums.TaxTripPactionApplyStatusEnum;
import com.akucun.account.proxy.common.exception.AccountProxyException;
import com.akucun.account.proxy.common.utils.AuditNoUtils;
import com.akucun.account.proxy.dao.mapper.TaxTripPactionApplyMapper;
import com.akucun.account.proxy.dao.model.TaxTripPactionApply;
import com.akucun.account.proxy.facade.stub.enums.CustomerType;
import com.akucun.account.proxy.service.acct.TaxTripPactionApplyService;
import com.akucun.account.proxy.service.acct.bo.TaxTripPactionApplyBO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class TaxTripPactionApplyServiceImpl implements TaxTripPactionApplyService {

    @Resource
    private TaxTripPactionApplyMapper taxTripPactionApplyMapper;
    @Autowired
    private CustomerServiceClient customerServiceClient;
    @Value("${tax.trip.apply.reject.reason}")
    private String rejectReasonMapping;


    @Override
    public TaxTripPactionApply selectValidApply(String customerCode, String customerType) {
        if(StringUtils.isNullOrBlank(customerCode) || StringUtils.isNullOrBlank(customerType)){
            Logger.info("TaxTripPactionApplyServiceImpl.selectValidApply入参不能为空");
            return null;
        }
        LambdaQueryWrapper<TaxTripPactionApply> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaxTripPactionApply::getCustomerCode, customerCode);
        wrapper.eq(TaxTripPactionApply::getCustomerType, customerType);
        wrapper.ne(TaxTripPactionApply::getStatus, TaxTripPactionApplyStatusEnum.AUDIT_REJECT.getStatus());

        return taxTripPactionApplyMapper.selectOne(wrapper);
    }

    @Override
    public boolean hasAuditingApply(String customerCode, String customerType) {
        if(StringUtils.isNullOrBlank(customerCode) || StringUtils.isNullOrBlank(customerType)){
            Logger.info("TaxTripPactionApplyServiceImpl.hasAuditingApply入参不能为空");
            return false;
        }
        LambdaQueryWrapper<TaxTripPactionApply> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaxTripPactionApply::getCustomerCode, customerCode);
        wrapper.eq(TaxTripPactionApply::getCustomerType, customerType);
        wrapper.eq(TaxTripPactionApply::getStatus, TaxTripPactionApplyStatusEnum.AUDITING.getStatus());
        TaxTripPactionApply taxTripPactionApply = taxTripPactionApplyMapper.selectOne(wrapper);
        return taxTripPactionApply != null;
    }

    @Override
    public TaxTripPactionApply selectByAuditNo(String auditNo) {
        if(StringUtils.isNullOrBlank(auditNo)){
            Logger.info("TaxTripPactionApplyServiceImpl.selectByAuditNo入参不能为空");
            return null;
        }
        LambdaQueryWrapper<TaxTripPactionApply> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaxTripPactionApply::getAuditNo, auditNo);
        return taxTripPactionApplyMapper.selectOne(wrapper);
    }

    @Override
    public List<TaxTripPactionApply> selectAllApply(String customerCode, String customerType) {
        if(StringUtils.isNullOrBlank(customerCode) || StringUtils.isNullOrBlank(customerType)){
            Logger.info("TaxTripPactionApplyServiceImpl.selectAllApply入参不能为空");
            return null;
        }
        LambdaQueryWrapper<TaxTripPactionApply> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaxTripPactionApply::getCustomerCode, customerCode);
        wrapper.eq(TaxTripPactionApply::getCustomerType, customerType);

        return taxTripPactionApplyMapper.selectList(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveApply(TaxTripPactionApplyBO taxTripPactionApplyBO) {
        TaxTripPactionApply taxTripPactionApply = BeanUtils.copy(taxTripPactionApplyBO, TaxTripPactionApply.class);
        String auditNo = AuditNoUtils.getNextId();
        taxTripPactionApply.setAuditNo(auditNo);
        int result = taxTripPactionApplyMapper.insert(taxTripPactionApply);
        if(result != 1){
            Logger.warn("保存申请记录失败");
            return false;
        }
        SubmitCertificationAuditReq auditReq = builderAuditReq(taxTripPactionApply);
        boolean submitResult = customerServiceClient.submitApply(auditReq);
        if(!submitResult){
            Logger.warn("调用客服接口提交申请失败");
            throw new AccountProxyException(ResponseEnum.SYSTEM_ERROR);
        }
        return true;
    }

    /**
     *
     * @param taxTripPactionApply
     * @return
     */
    private SubmitCertificationAuditReq builderAuditReq(TaxTripPactionApply taxTripPactionApply){
        SubmitCertificationAuditReq auditReq = new SubmitCertificationAuditReq();
        auditReq.setAuditNo(taxTripPactionApply.getAuditNo());
        if(CustomerType.NM.getName().equals(taxTripPactionApply.getCustomerType())){
            //ROLE_OWNER("店主", 2)
            auditReq.setUserRole(2);
        }else{
            //ROLE_DISTRIBUTOR("店长", 3),
            auditReq.setUserRole(3);
        }
        auditReq.setUserId(taxTripPactionApply.getCustomerCode());
        //默认100：税库银三方协议
        auditReq.setCardType(100);
        //默认100：私人银行卡绑定
        auditReq.setAuditType(100);
        auditReq.setIdCardName(taxTripPactionApply.getUserName());
        auditReq.setIdCardNumber(taxTripPactionApply.getBankCardNo());
        auditReq.setSocialCreditCode(taxTripPactionApply.getSocialCreditCode());
        auditReq.setMerchantName(taxTripPactionApply.getMerchantName());
        auditReq.setLegalPersonName(taxTripPactionApply.getLegalPersonName());
        auditReq.setIdCardFrontPic(taxTripPactionApply.getPactionImgUrl());
        auditReq.setIdCardBackPic(StringUtils.empty());

        return auditReq;
    }

    @Override
    public boolean updateApply(TaxTripPactionApply taxTripPactionApply) {
        LambdaQueryWrapper<TaxTripPactionApply> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaxTripPactionApply::getId, taxTripPactionApply.getId());

        int result = taxTripPactionApplyMapper.update(taxTripPactionApply, wrapper);
        return result == 1;
    }

    @Override
    public String convertRejectReason(List<String> rejectReason) {
        Logger.info("驳回原因:{}", DataMask.toJSONString(rejectReason));
        if(CollectionUtils.isEmpty(rejectReason)){
            return "";
        }
        try {
            JSONObject reasonObj = JSON.parseObject(rejectReasonMapping);
            StringBuilder reasonBuilder = new StringBuilder();
            int i = 1;
            for (String key : rejectReason){
                String reasonMsg = reasonObj.getString(key);
                if(!StringUtils.isNullOrBlank(reasonMsg)){
                    reasonBuilder.append(i++).append(".").append(reasonMsg).append(";");
                }
            }
            return reasonBuilder.toString();
        }catch (Exception e){
            Logger.error("convertRejectReason执行时异常", e);
            return "";
        }
    }
}
