package com.akucun.account.proxy.service.postaction.task;

import com.akucun.account.proxy.dao.mapper.WithdrawTaxDetailMapper;
import com.akucun.account.proxy.dao.mapper.WithdrawTaxSummaryMapper;
import com.akucun.account.proxy.dao.model.WithdrawTaxDetail;
import com.akucun.account.proxy.dao.model.WithdrawTaxSummary;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mengxiang.base.common.log.Logger;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @Author: silei
 * @Date: 2021/4/3
 * @desc: 提现扣税相关
 */
@Component
public class WithdrawTaxClearTask {

    @Resource
    private WithdrawTaxSummaryMapper withdrawTaxSummaryMapper;
    @Resource
    private WithdrawTaxDetailMapper withdrawTaxDetailMapper;

    @XxlJob("withdrawTaxClearTask")
    public ReturnT<String> execute(String param) {
        Logger.info("withdrawTaxClearTask start! param:{}", param);
        if (StringUtils.isEmpty(param)){
            return ReturnT.FAIL;
        }
        String[] arr = param.split(",");
        if ("1".equals(arr[0])){
            //清除detail表数据
            LambdaQueryWrapper<WithdrawTaxDetail> wrapper = new LambdaQueryWrapper<>();
            wrapper.gt(WithdrawTaxDetail::getId, Integer.parseInt(arr[1]))
                    .le(WithdrawTaxDetail::getId, Integer.parseInt(arr[2]));
            withdrawTaxDetailMapper.delete(wrapper);
        }else if ("2".equals(arr[0])){
            //清除summary表数据
            LambdaQueryWrapper<WithdrawTaxSummary> wrapper = new LambdaQueryWrapper<>();
            wrapper.gt(WithdrawTaxSummary::getId, Integer.parseInt(arr[1]))
                    .le(WithdrawTaxSummary::getId, Integer.parseInt(arr[2]));
            withdrawTaxSummaryMapper.delete(wrapper);
        }
        Logger.info("withdrawTaxClearTask finished!");
        return ReturnT.SUCCESS;
    }

}
