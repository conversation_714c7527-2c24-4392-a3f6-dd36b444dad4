package com.akucun.account.proxy.service.transfer.bo;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/01/29 15:22
 */
public class TransferGatewayBO implements Serializable {

    /**
     * 通道网关编号
     */
    private String gatewayCode;

    /**
     * 应用Id
     */
    private String appId;

    /**
     * 支付通道方分配的商户号
     */
    private String mchCode;

    /**
     * 公司主体
     */
    private String companyName;

    /**
     * 网关信息
     */
    private String gatewayInfo;

    /**证书编号**/
    private String certSerialNo;

    /**
     * 证书1
     */
    private String certificate1;

    /**
     * 证书2
     */
    private String certificate2;

    /**
     * 证书3
     */
    private String certificate3;

    /**
     * 证书4
     */
    private String certificate4;

    /**
     * 证书5
     * isNullAble:0
     */
    private String certificate5;

    public String getGatewayCode() {
        return gatewayCode;
    }

    public void setGatewayCode(String gatewayCode) {
        this.gatewayCode = gatewayCode;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getMchCode() {
        return mchCode;
    }

    public void setMchCode(String mchCode) {
        this.mchCode = mchCode;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getGatewayInfo() {
        return gatewayInfo;
    }

    public void setGatewayInfo(String gatewayInfo) {
        this.gatewayInfo = gatewayInfo;
    }

    public String getCertSerialNo() {
        return certSerialNo;
    }

    public void setCertSerialNo(String certSerialNo) {
        this.certSerialNo = certSerialNo;
    }


    public void setCertificate1(String certificate1) {
        this.certificate1 = certificate1;
    }

    public String getCertificate2() {
        return certificate2;
    }

    public void setCertificate2(String certificate2) {
        this.certificate2 = certificate2;
    }

    public String getCertificate3() {
        return certificate3;
    }

    public void setCertificate3(String certificate3) {
        this.certificate3 = certificate3;
    }

    public String getCertificate4() {
        return certificate4;
    }

    public void setCertificate4(String certificate4) {
        this.certificate4 = certificate4;
    }

    public String getCertificate5() {
        return certificate5;
    }

    public void setCertificate5(String certificate5) {
        this.certificate5 = certificate5;
    }
}
