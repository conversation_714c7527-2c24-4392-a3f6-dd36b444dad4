package com.akucun.account.proxy.service.acct.impl;

import com.akucun.account.proxy.dao.mapper.AccountOpTransferAmountMapper;
import com.akucun.account.proxy.dao.model.AccountOpTransferAmount;
import com.akucun.account.proxy.service.acct.AccountOpTransferAmountService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * @Author: silei
 * @Date: 2020/9/2
 * @desc:
 */
@Service
public class AccountOpTransferAmountServiceImpl extends ServiceImpl<AccountOpTransferAmountMapper, AccountOpTransferAmount> implements AccountOpTransferAmountService {

}
