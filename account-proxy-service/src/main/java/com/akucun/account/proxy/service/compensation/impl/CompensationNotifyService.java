package com.akucun.account.proxy.service.compensation.impl;

import com.aikucun.common2.base.Result;
import com.aikucun.common2.log.Logger;
import com.aikucun.common2.utils.DateUtils;
import com.akucun.account.proxy.facade.stub.enums.CompensationFillPayTypeEnum;
import com.akucun.account.proxy.facade.stub.others.dto.req.CompensationFillPayApplyReq;
import com.akucun.account.proxy.service.compensation.bo.RefundNotifyResDTO;
import com.akucun.aftersale.mgt.facade.stub.CallBackOrderFeign;
import com.akucun.bcs.bill.facade.stub.api.BcsBillIntegrationApi;
import com.akucun.bcs.bill.facade.stub.model.req.integration.SimpleReceiveBillOrderRequest;
import com.akucun.bcs.bill.facade.stub.model.res.integration.SimpleReceiveBillIntegrationRes;
import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Create on : 2024/11/27 22:56
 **/
@Service
public class CompensationNotifyService {
    //供应链渠道商-运费险理赔-事件编码
    @Value("${insurance.case.subBillType.eventCode:COMPENSATION_DFICP}")
    private String insuranceCaseEventCode;

    @Resource
    private BcsBillIntegrationApi bcsBillIntegrationApi;
    @Resource
    private CallBackOrderFeign callBackOrderFeign;

     public Pair<Boolean, String> asynNotify(CompensationFillPayTypeEnum payTypeEnum, CompensationFillPayApplyReq req){
        RefundNotifyResDTO refundNotifyResDTO = new RefundNotifyResDTO();
        SimpleReceiveBillOrderRequest simpleReceiveBillOrderRequest = new SimpleReceiveBillOrderRequest();
        try {
            if (payTypeEnum == CompensationFillPayTypeEnum.SHIPPING_FEE_COMPENSATION || payTypeEnum == CompensationFillPayTypeEnum.GOODS_FEE_COMPENSATION) {
                // 运费补偿、补货款类型回调给售后系统
                buildRefundNotifyResDTO(req, refundNotifyResDTO);
                Logger.info("akucun-aftersale-mgt newSubsidiesCallback req:{}", JSON.toJSONString(refundNotifyResDTO));
                com.akucun.common.Result<Void> resultTemp = callBackOrderFeign.newSubsidiesCallback(JSON.toJSONString(refundNotifyResDTO));
                Logger.info("akucun-aftersale-mgt newSubsidiesCallback req:{},resp:{}",JSON.toJSONString(refundNotifyResDTO),JSON.toJSONString(resultTemp));
                if (ObjectUtils.isEmpty(resultTemp) || !resultTemp.isSuccess()) {
                    return Pair.of(false, !ObjectUtils.isEmpty(resultTemp) ? resultTemp.getMessage() : "系统异常");
                }
            } else if (payTypeEnum == CompensationFillPayTypeEnum.SHIPPING_INSURANCE_CLAIM) {
                // 运费险理赔提交bcs-bill统一收单接口
                buildsimpleBillRequest(req, simpleReceiveBillOrderRequest);
                //01-2:运费险理赔 -> 提交到bcs-bill统一收单接口
                Logger.info("bcs-bill simpleReceiveBillOrder req:{}",JSON.toJSONString(refundNotifyResDTO));
                Result<SimpleReceiveBillIntegrationRes> resultTemp = bcsBillIntegrationApi.simpleReceiveBillOrder(simpleReceiveBillOrderRequest);
                Logger.info("bcs-bill simpleReceiveBillOrder req:{},resp:{}",JSON.toJSONString(refundNotifyResDTO),JSON.toJSONString(resultTemp));
                if (ObjectUtils.isEmpty(resultTemp) || !resultTemp.getSuccess() || ObjectUtils.isEmpty(resultTemp.getData())) {
                    return Pair.of(false, !ObjectUtils.isEmpty(resultTemp) ? resultTemp.getMessage() : "系统异常");
                }
            }
        }catch (Exception e){
            Logger.error("asynNotify fail:{}", JSON.toJSONString(req),e);
            return Pair.of(false, "异步通知失败:"+e.getMessage());
        }

        return Pair.of(true, "suss");
    }
    /**
     * {"merchantCode":null,"outRefundNo":"BH5SC2411180600969500701000_1","refundNo":"RF8935904138022800807",
     * "thirdTradeNo":"50301600972024111835347690607","subOrderNo":"202410150030100040239862","orderNo":"202410150030100040239861",
     * "refundAmount":3.1000,"channelCode":"WECHAT","refundType":"2","status":"S","successTime":"2024-11-18 17:10:30",
     * "responseCode":"0","responseMsg":"成功","returnParameter":null,"mergeRefundNo":""}
     * @param req
     * @return
     */
    private RefundNotifyResDTO buildRefundNotifyResDTO(CompensationFillPayApplyReq req,RefundNotifyResDTO notifyResDTO){
        notifyResDTO.setOutRefundNo(req.getSourceBusinessNo());//业务退款单号--使用业务唯一单号
        notifyResDTO.setMerchantCode(req.getMerchantCode());
        //notifyResDTO.setRefundNo();//支付退款单号
        //notifyResDTO.setThirdTradeNo();//第三方退款单号
        notifyResDTO.setOrderNo(req.getSecondOrderNo());//原业务合单号
        if(!StringUtils.isEmpty(req.getOrderSkuNo())){
            notifyResDTO.setSubOrderNo(req.getOrderSkuNo());
        } else if (!StringUtils.isEmpty(req.getThirdOrderNo())) {
            notifyResDTO.setSubOrderNo(req.getThirdOrderNo());
        }
        notifyResDTO.setRefundAmount(req.getAmount());//退款金额
        /**
         * 售后预定义的退款渠道类型：
         *  BALANCE(3, "奖励金", "BALANCE"),
         *     WECHAT(2, "微信", "WECHAT"),
         *     ALIPAY(1, "支付宝", "ALIPAY"),
         *     UNKNOWN(0, "未知", ""),
         *     WALLET(9, "钱包", "NEWBALANCE"),
         *     OWNER_BALANCE(10, "店主余额", ""),
         *     POINT_ACCOUNT(11, "奖励金", ""),
         *     WX_JSAPI(5, "微信", "WJSAPI"),
         *     C_WECHAT(12, "顾客微信", ""),
         *     AKC_ACCOUNT(13, "钱包", "AKC_ACCOUNT"),
         *     MY_BALANCE(23, "我的余额", ""),
         *     MARGIN_ACCOUNT(24,"保证金账户","MARGIN_ACCOUNT"),;
         *
         */
        notifyResDTO.setChannelCode("MARGIN_ACCOUNT");//退款通道
        notifyResDTO.setRefundType(req.getType());//退款类型
        notifyResDTO.setStatus("S");//退款状态
        notifyResDTO.setSuccessTime(DateUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));//退款成功时间
        notifyResDTO.setResponseMsg("成功");//响应结果（成功失败信息）

        return notifyResDTO;
    }

    private SimpleReceiveBillOrderRequest buildsimpleBillRequest(CompensationFillPayApplyReq req,SimpleReceiveBillOrderRequest billOrderRequest){
        Logger.info("构建SimpleReceiveBillOrderRequest对象开始,source:{}",JSON.toJSONString(req));
        billOrderRequest.setEventCode(insuranceCaseEventCode);
        billOrderRequest.setMerchantCode(req.getMerchantCode());
        billOrderRequest.setMerchantName(req.getMerchantName());
        billOrderRequest.setShopCode(req.getShopCode());
        billOrderRequest.setTenantId(req.getTenantId());
        billOrderRequest.setSourceBusinessNo(req.getSourceBusinessNo());
        billOrderRequest.setBusinessDate(req.getBusinessDate());
        billOrderRequest.setAmount(req.getAmount());
        billOrderRequest.setSecondOrderNo(req.getSecondOrderNo());
        billOrderRequest.setThirdOrderNo(req.getThirdOrderNo());
        billOrderRequest.setThirdOrderId(req.getThirdOrderId());
        billOrderRequest.setChannel(req.getChannel());

        Map<String,Object> exts = new HashMap<>();
        exts.put("orderSkuNo",req.getOrderSkuNo());
        exts.put("type",req.getType());
        if(null != req.getExts()  && req.getExts().size()>0){
            exts.putAll(req.getExts());
        }
        billOrderRequest.setExts(exts);

        return billOrderRequest;
    }
}
