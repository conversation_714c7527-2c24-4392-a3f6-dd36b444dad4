package com.akucun.account.proxy.service.transfer.bo;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/01/29 16:06
 */
public class PaymentTransferBO {
    /**
     * 是否付款成功
     */
    private Boolean isSuccess;

    /**
     * 网关返回信息
     */
    private String gatewayMsg;

    /**
     * 请求报文
     */
    private String requestMsg;

    /**
     * 返回报文
     */
    private String responseMsg;

    /**
     * 第三方交易流水号
     */
    private String transactionId;

    /**
     * 付款成功时间
     */
    private Date successTime;

    private String status;

    public Boolean getSuccess() {
        return isSuccess;
    }

    public void setSuccess(Boolean success) {
        isSuccess = success;
    }

    public String getGatewayMsg() {
        return gatewayMsg;
    }

    public void setGatewayMsg(String gatewayMsg) {
        this.gatewayMsg = gatewayMsg;
    }

    public String getRequestMsg() {
        return requestMsg;
    }

    public void setRequestMsg(String requestMsg) {
        this.requestMsg = requestMsg;
    }

    public String getResponseMsg() {
        return responseMsg;
    }

    public void setResponseMsg(String responseMsg) {
        this.responseMsg = responseMsg;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public Date getSuccessTime() {
        return successTime;
    }

    public void setSuccessTime(Date successTime) {
        this.successTime = successTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
