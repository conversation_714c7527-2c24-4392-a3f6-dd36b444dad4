package com.akucun.account.proxy.service.trade.repository;

import com.aikucun.common2.utils.datamasking.DataMask;
import com.akucun.account.proxy.common.enums.ResponseEnum;
import com.akucun.account.proxy.common.exception.AccountProxyException;
import com.akucun.account.proxy.dao.mapper.AccountTradeDetailMapper;
import com.akucun.account.proxy.dao.mapper.AccountTradeMapper;
import com.akucun.account.proxy.dao.model.AccountTrade;
import com.akucun.account.proxy.dao.model.AccountTradeDetail;
import com.akucun.account.proxy.service.trade.bo.AccountTradeBO;
import com.akucun.account.proxy.service.trade.bo.AccountTradeDetailBO;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.mengxiang.base.common.log.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: silei
 * @Date: 2020/11/30
 * @desc:
 */
@Component
public class AccountTradeRepository {

    @Resource
    private AccountTradeMapper accountTradeMapper;

    @Resource
    private AccountTradeDetailMapper accountTradeDetailMapper;

    public AccountTradeBO queryAcctTrade(AccountTradeBO payBO) {
        LambdaQueryWrapper<AccountTrade> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AccountTrade::getTradeNo, payBO.getTradeNo())
                .eq(AccountTrade::getTradeType, payBO.getTradeType())
                .eq(AccountTrade::getCustomerCode, payBO.getCustomerCode())
                .last(" ORDER BY id DESC LIMIT 1");
        AccountTradeBO accountTradeBO = null;
        AccountTrade accountTrade = accountTradeMapper.selectOne(wrapper);
        if (Objects.nonNull(accountTrade)) {
            accountTradeBO = new AccountTradeBO();
            BeanUtils.copyProperties(accountTrade, accountTradeBO);
            accountTradeBO.setExtField((Map<String, String>) JSON.parse(accountTrade.getExt()));
        }
        return accountTradeBO;
    }

    public void addAccountTrade(AccountTradeBO tradeBO) {
        AccountTrade accountTrade = new AccountTrade();
        BeanUtils.copyProperties(tradeBO, accountTrade);
        Logger.info("AccountTradeRepository 新增 addAccountTrade :{}", DataMask.toJSONString(accountTrade));
        accountTradeMapper.insert(accountTrade);
        tradeBO.setId(accountTrade.getId());
    }

    public int updateAccountTradeStatus(AccountTradeBO req) {
        AccountTrade accountTrade = new AccountTrade();
        BeanUtils.copyProperties(req, accountTrade);
        LambdaQueryWrapper<AccountTrade> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AccountTrade::getTradeNo, accountTrade.getTradeNo())
                .eq(AccountTrade::getCustomerCode, accountTrade.getCustomerCode());
        int i = accountTradeMapper.update(accountTrade, queryWrapper);
        if (i != 1) {
            throw new AccountProxyException(ResponseEnum.STEP_EXEC_CONCURRENCY.getCode(), ResponseEnum.STEP_EXEC_CONCURRENCY.getMessage());
        }
        return i;
    }

    /**
     * 查询最新的明细流程记录
     * @param payBO
     * @return
     */
    public AccountTradeBO queryAcctTradeDetail(AccountTradeBO payBO) {
        LambdaQueryWrapper<AccountTradeDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AccountTradeDetail::getTradeNo, payBO.getTradeNo())
                .eq(AccountTradeDetail::getCustomerCode, payBO.getCustomerCode())
                .last(" ORDER BY id DESC LIMIT 1");
        AccountTradeDetailBO detailBO = new AccountTradeDetailBO();
        AccountTradeDetail detail = accountTradeDetailMapper.selectOne(wrapper);
        if (Objects.nonNull(detail)) {
            BeanUtils.copyProperties(detail, detailBO);
            payBO.setAccountTradeDetailBO(detailBO);
        }
        return payBO;
    }

    /**
     * 根据主交易id记录查询明细流程列表
     * @param payBO
     * @return
     */
    public List<AccountTradeDetailBO> queryAcctTradeDetailList(AccountTradeBO payBO) {
        List<AccountTradeDetailBO> list = Lists.newArrayList();
        LambdaQueryWrapper<AccountTradeDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AccountTradeDetail::getTradeNo, payBO.getTradeNo())
                .eq(AccountTradeDetail::getCustomerCode, payBO.getCustomerCode());
        List<AccountTradeDetail> poList = accountTradeDetailMapper.selectList(wrapper);
        poList.forEach(t -> {
            AccountTradeDetailBO detailBO = new AccountTradeDetailBO();
            BeanUtils.copyProperties(t, detailBO);
            list.add(detailBO);
        });
        return list;
    }

    public void addAccountTradeDetail(AccountTradeDetailBO detailBO) {
        AccountTradeDetail detail = new AccountTradeDetail();
        BeanUtils.copyProperties(detailBO, detail);
        Logger.info("AccountTradeRepository 新增 AccountTradeDetail :{}",  DataMask.toJSONString(detail));
        accountTradeDetailMapper.insert(detail);
        detailBO.setId(detail.getId());
    }

    public int updateAccountTradeDetailStatus(AccountTradeDetailBO detailBO) {
        AccountTradeDetail detail = new AccountTradeDetail();
        BeanUtils.copyProperties(detailBO, detail);
        LambdaQueryWrapper<AccountTradeDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AccountTradeDetail::getTradeNo, detailBO.getTradeNo())
                .eq(AccountTradeDetail::getCustomerCode, detailBO.getCustomerCode());
        int i = accountTradeDetailMapper.update(detail, queryWrapper);
        if (i != 1) {
            throw new AccountProxyException(ResponseEnum.STEP_EXEC_CONCURRENCY.getCode(), ResponseEnum.STEP_EXEC_CONCURRENCY.getMessage());
        }
        return i;
    }
}
