package com.akucun.account.proxy.service.transfer;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.facade.stub.others.dto.req.PaymentTransferReq;
import com.akucun.account.proxy.facade.stub.others.dto.res.PayTransferResp;
import com.akucun.account.proxy.facade.stub.others.dto.res.PaymentTransferResp;

/**
 * <AUTHOR>
 */
public interface PayTransferService {

    /**
     * 提现到微信零钱
     * @param request
     * @return
     */
    Result<PaymentTransferResp> transfer(PaymentTransferReq request);

    /**
     * 批量提现到微信
     * @param request
     * @return
     */
    Result<Void> wechatWithdraw(PaymentTransferReq request);


    Result<Void> queryWithdrawResp(PaymentTransferReq request);

}
