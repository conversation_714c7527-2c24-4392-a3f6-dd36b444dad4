package com.akucun.account.proxy.service.postaction.task;

import com.aikucun.common2.base.Result;
import com.aikucun.common2.utils.datamasking.DataMask;
import com.akucun.account.proxy.common.enums.PostActionTypes;
import com.akucun.account.proxy.dao.model.PostActionItem;
import com.akucun.account.proxy.service.postaction.common.AbsPostActionExecutor;
import com.akucun.fps.common.util.GsonUtils;
import com.akucun.fps.pingan.client.vo.WithdrawVO;
import com.akucun.fps.pingan.feign.api.assets.AssetsServiceApi;
import com.alibaba.fastjson.JSON;
import com.mengxiang.base.common.log.Logger;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @Author: silei
 * @Date: 2021/12/10
 * @desc: 平安提现异常结果查询任务
 */
@Component
public class DelayWithdrawQueryTask extends AbsPostActionExecutor {

    @Resource
    private AssetsServiceApi assetsServiceApi;


    @XxlJob("delayWithdrawQueryTask")
    public ReturnT<String> execute(String param) {
        return this.executeEntrance();
    }

    @Override
    protected String getActionType() {
        return PostActionTypes.PINGAN_DELAY_WITHDRAW_QUERY.getName();
    }

    @Override
    public Result<Void> execute(PostActionItem item) {
        Result<Void> result = Result.success();
        WithdrawVO withdrawVO;
        try {
            withdrawVO = GsonUtils.getInstance().fromJson(item.getParam(), WithdrawVO.class);
            Logger.info("DelayWithdrawQueryTask execute req:{}", DataMask.toJSONString(withdrawVO));
            com.akucun.fps.common.entity.Result<Void> withdrawRes = assetsServiceApi.cashWithdrawQuery(withdrawVO);
            result.setSuccess(withdrawRes.isSuccess());
        } catch (Exception e) {
            Logger.error("延迟提现结果查询异常: ", e);
            return Result.error(e);
        }
        return result;
    }

}
