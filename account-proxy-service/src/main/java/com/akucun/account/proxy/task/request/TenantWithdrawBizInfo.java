package com.akucun.account.proxy.task.request;

import java.io.Serializable;
import java.math.BigDecimal;

import org.apache.commons.lang3.StringUtils;

import com.akucun.account.proxy.common.constant.CustomerType;
import com.akucun.account.proxy.dao.model.WithdrawApplyRecord;
import com.akucun.fps.pingan.client.vo.WithdrawVO;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class TenantWithdrawBizInfo implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = -6437924833335601708L;
	
	/**
	 * 用户编号
	 */
	private String customerCode;
	
	/**
	 * 用户类型
	 */
	private String customerType;
	
	/**
	 * 用户名称
	 */
	private String customerName;
	
	/**
	 * 提现号
	 */
	private String withdrawNo;
	
	/**
	 * 金额
	 */
	private BigDecimal amount;
	
	/**
	 * 备注
	 */
	private String remark;
	
	/**
	 * 银行号
	 */
	private String bankNo;
	
	public TenantWithdrawBizInfo(WithdrawApplyRecord apply,String bankNo) {
		this.customerCode = apply.getCustomerCode();
		this.customerType = apply.getCustomerType();
		this.customerName = apply.getCustomerName();
		this.withdrawNo = apply.getWithdrawNo();
		this.amount = apply.getAmount();
		this.remark = apply.getRemark();
		if(StringUtils.isBlank(apply.getRemark())) {
			this.remark = "租户提现";
		}
		this.bankNo = bankNo;
	}

}
