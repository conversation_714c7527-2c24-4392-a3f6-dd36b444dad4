package com.akucun.account.proxy.service.notify.factory;

import java.io.File;
import java.nio.file.Files;

import com.akucun.account.proxy.facade.stub.others.account.req.NotifyReq;
import com.akucun.account.proxy.service.common.SpringContextHolder;
import com.akucun.account.proxy.service.notify.Handler;
import com.akucun.common.Result;

/**
 * 消息通知处理工厂
 * <AUTHOR>
 *
 */
public class NotifyFactory {
	
	private static String suffix="HANDLER";
	
	private static String separator = "_";
	
	public static Handler<Result<Void>,NotifyReq> getNotifyHandler(String requestPlatform, String bizCategory) {
		return SpringContextHolder.getBeanByName(getHandlerName(requestPlatform, bizCategory), Handler.class);
	}
	
	public static String getHandlerName(String requestPlatform, String bizCategory) {
		return requestPlatform + separator + bizCategory + separator + suffix;
	}

}
