package com.akucun.account.proxy.service.tradeflow.impl;

import com.aikucun.common2.log.Logger;
import com.aikucun.common2.utils.datamasking.DataMask;
import com.akucun.account.proxy.facade.stub.others.trade.req.TradeReq;
import com.akucun.account.proxy.facade.stub.others.trade.res.TradePhaseRes;
import com.akucun.account.proxy.facade.stub.others.trade.res.TradeRes;
import com.akucun.account.proxy.facade.stub.others.trade.res.TradeStatusEnum;
import com.akucun.account.proxy.service.tradeflow.TradeService;
import com.akucun.account.proxy.service.tradeflow.agreement.TradeAgreement;
import com.akucun.account.proxy.service.tradeflow.agreement.TradeAgreementService;
import com.akucun.account.proxy.service.tradeflow.constants.ErrorCodes;
import com.akucun.account.proxy.service.tradeflow.context.TradeContext;
import com.akucun.account.proxy.service.tradeflow.domain.Trade;
import com.akucun.account.proxy.service.tradeflow.domain.TradePhase;
import com.akucun.account.proxy.service.tradeflow.dto.BizInfos;
import com.akucun.account.proxy.service.tradeflow.dto.ErrorDTO;
import com.akucun.account.proxy.service.tradeflow.exception.TradeValidException;
import com.akucun.account.proxy.service.tradeflow.instruction.TradeInstruction;
import com.akucun.account.proxy.service.tradeflow.instruction.TradeInstructionService;
import com.akucun.account.proxy.service.tradeflow.repository.TradePhaseRepository;
import com.akucun.account.proxy.service.tradeflow.repository.TradeRepository;
import com.akucun.account.proxy.service.tradeflow.script.TradeScripts;
import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class TradeServiceImpl implements TradeService {

    @Autowired
    private TradeInstructionService tradeInstructionService;

    @Autowired
    private TradeAgreementService tradeAgreementService;

    @Autowired
    private TradeRepository tradeRepository;

    @Autowired
    private TradePhaseRepository tradePhaseRepository;

    @Override
    public TradeRes trade(TradeReq tradeReq) {
        Logger.info("交易流程请求：{}", DataMask.toJSONString(tradeReq));
        TradeRes tradeRes = null;
        try {
            //请求参数校验
            validReq(tradeReq);
            //初始化上下文
            TradeContext.init();
            //初始化交易流程
            Trade trade = initTrade(tradeReq);
            //交易执行
            trade.execute();
            //返回结果
            tradeRes = buildRes(TradeContext.get().getTrade(), TradeContext.get().getPhases());
        } catch (TradeValidException e) {
            Logger.warn("交易流程校验失败，请求：{}，原因：{}", DataMask.toJSONString(tradeReq), DataMask.toJSONString(e.getErrorDTO()));
            tradeRes = new TradeRes(TradeStatusEnum.FAIL, e.getErrorDTO().getCode(), e.getErrorDTO().getMessage(), null);
        } catch (DuplicateKeyException e) {
            Logger.info("交易流程重复，请求：{}", DataMask.toJSONString(tradeReq));
            Trade trade = tradeRepository.load(tradeReq.getTradeNo());
            tradeRes = buildRes(trade, tradePhaseRepository.load(trade.getId()));
        } catch (Throwable e) {
            Logger.error("交易流程异常，请求：{}", DataMask.toJSONString(tradeReq), e);
            tradeRes = new TradeRes(TradeStatusEnum.PROCESSING, ErrorCodes.SYSTEM_EXCEPTION, "系统异常", null);
        } finally {
            TradeContext.clean();
            Logger.info("交易流程请求：{}，返回：{}", DataMask.toJSONString(tradeReq), DataMask.toJSONString(tradeRes));
        }
        return tradeRes;
    }

    /**
     * 校验通过返回null，否则返回错误信息
     * @param tradeReq
     * @return
     */
    private void validReq(TradeReq tradeReq) throws TradeValidException {
        if(StringUtils.isBlank(tradeReq.getTradeNo())) {
            throw new TradeValidException(new ErrorDTO(ErrorCodes.PARAM_ERROR, "tradeNo为空"));
        }
        if(StringUtils.isBlank(tradeReq.getBizNo())) {
            throw new TradeValidException(new ErrorDTO(ErrorCodes.PARAM_ERROR, "bizNo为空"));
        }
        if(StringUtils.isBlank(tradeReq.getProductCategory())) {
            throw new TradeValidException(new ErrorDTO(ErrorCodes.PARAM_ERROR, "productCategory为空"));
        }
        if(StringUtils.isBlank(tradeReq.getBizType())) {
            throw new TradeValidException(new ErrorDTO(ErrorCodes.PARAM_ERROR, "bizType为空"));
        }
        if(StringUtils.isBlank(tradeReq.getRequestPlatform())) {
            throw new TradeValidException(new ErrorDTO(ErrorCodes.PARAM_ERROR, "requestPlatform为空"));
        }
        if(StringUtils.isBlank(tradeReq.getBizInfo())) {
            throw new TradeValidException(new ErrorDTO(ErrorCodes.PARAM_ERROR, "bizInfo为空"));
        }
    }

    private Trade initTrade(TradeReq tradeReq) throws TradeValidException, ClassNotFoundException {
        TradeInstruction instruction = tradeInstructionService.getInstruction(tradeReq.getBizType(), tradeReq.getProductCategory(), tradeReq.getRequestPlatform());
        if(instruction == null) {
            throw new TradeValidException(new ErrorDTO(ErrorCodes.NO_INSTRUCTION, "无此交易"));
        }
        TradeAgreement agreement = tradeAgreementService.getAgreement(instruction.getAgreementId());
        if(agreement == null) {
            throw new TradeValidException(new ErrorDTO(ErrorCodes.NO_AGREEMENT, "无此协议"));
        }
        Object bizInfo = BizInfos.getBizInfo(tradeReq.getBizInfo(), agreement.getBizInfoCls());
        TradeScripts.getBizInfoValidScript(agreement.getBizInfoValidScript()).valid(bizInfo);
        Trade trade = new Trade();
        trade.setTradeNo(tradeReq.getTradeNo());
        trade.setBizNo(tradeReq.getBizNo());
        trade.setProductCategory(tradeReq.getProductCategory());
        trade.setBizType(tradeReq.getBizType());
        trade.setRequestPlatform(tradeReq.getRequestPlatform());
        trade.setBizInfo(bizInfo);
        trade.setRemark(tradeReq.getRemark());
        trade.setStatus(TradeStatusEnum.PROCESSING);
        trade.setAgreement(agreement);
        tradeRepository.persist(trade);
        TradeContext.get().setTrade(trade);
        return trade;
    }

    private TradeRes buildRes(Trade trade, List<TradePhase> phases) {
        TradeRes tradeRes = new TradeRes();
        tradeRes.setStatus(trade.getStatus());
        if(phases != null) {
            tradeRes.setPhaseResList(new ArrayList<>());
            for(TradePhase phase : phases) {
                TradePhaseRes phaseRes = new TradePhaseRes();
                phaseRes.setPhaseCode(phase.getAgreement().getCode());
                phaseRes.setStatus(phase.getStatus());
                phaseRes.setErrorCode(phase.getErrorCode());
                phaseRes.setErrorMessage(phase.getErrorMessage());
                tradeRes.getPhaseResList().add(phaseRes);
                if(StringUtils.isNotBlank(phaseRes.getErrorCode())) {
                    tradeRes.setFailCode(phaseRes.getErrorCode());
                    tradeRes.setFailMessage(phaseRes.getErrorMessage());
                }
            }
        }
        return tradeRes;
    }

}
