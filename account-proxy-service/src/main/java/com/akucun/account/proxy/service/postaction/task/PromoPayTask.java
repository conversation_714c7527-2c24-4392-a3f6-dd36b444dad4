package com.akucun.account.proxy.service.postaction.task;

import com.aikucun.common2.base.Result;
import com.aikucun.common2.log.Logger;
import com.aikucun.common2.utils.DateUtils;
import com.akucun.account.proxy.client.risk.RiskService;
import com.akucun.account.proxy.common.enums.ErrorCodeConstants;
import com.akucun.account.proxy.common.enums.MemberGrade;
import com.akucun.account.proxy.common.enums.PostActionTypes;
import com.akucun.account.proxy.common.enums.ResponseEnum;
import com.akucun.account.proxy.dao.model.PostActionItem;
import com.akucun.account.proxy.dao.model.RewardApply;
import com.akucun.account.proxy.facade.stub.enums.RewardTypeEnum;
import com.akucun.account.proxy.service.acct.PromoTradeFactory;
import com.akucun.account.proxy.service.acct.PromoTradeService;
import com.akucun.account.proxy.service.acct.RewardApplyService;
import com.akucun.account.proxy.service.enums.PromoTradeStatusEnum;
import com.akucun.account.proxy.service.postaction.common.AbsPostActionExecutor;
import com.akucun.fps.common.util.GsonUtils;
import com.akucun.fps.common.util.MD5Util;
import com.alibaba.fastjson.JSON;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Create on : 2025/2/12 23:56
 **/
@Component
public class PromoPayTask  extends AbsPostActionExecutor {
    @Autowired
    private RewardApplyService rewardApplyService;
    @Autowired
    private PromoTradeService promoTradeService;
    @Autowired
    private PromoTradeFactory factory;
    @Autowired
    private RiskService riskService;
    @Value("${yqj.pay.bizno.mock:true}")
    private Boolean PAY_MOCK;
    @Value("${yqj.pay.noBindCard.status.switch:false}")
    private Boolean NO_BIND_CARD_STATUS_SWITCH;

    static final String PERSON_AUTH_NAME = "person";
    static final String LESHUI_AUTH_NAME = "leshui";

    @XxlJob("PromoPayTask")
    public ReturnT<String> execute(String param) {
        // 执行入口：模版方法提供的通用任务查询和执行
        return this.executeEntrance();
    }

    /**
     * 处理单个任务
     * @param item
     * @return
     */
    @Override
    public Result<Void> execute(PostActionItem item) {
        Result<Void> result = Result.success();

        try {
            //01-序列化对象
            RewardApply record = GsonUtils.getInstance().fromJson(item.getParam(), RewardApply.class);

            //02-状态的double check
            List<RewardApply> existRecords = rewardApplyService.queryRewardApplyRecords(record.getRequestNo(), record.getBusiType(), record.getTransBillDate(), record.getUserCode(),record.getUserType());
            if(CollectionUtils.isEmpty(existRecords)){
                return Result.error(ErrorCodeConstants.ACCORE_101900.getErrorCode(),String.format("PromoOAStartTask奖励下发-启动出款任务时，数据异常:记录不存在：{}",item.getParam()));
            }
            RewardApply existRecord = existRecords.get(0);
            RewardTypeEnum busiTypeEnum = RewardTypeEnum.getByCode(existRecord.getBusiType());
            PromoTradeStatusEnum statusEnum = PromoTradeStatusEnum.getByCode(existRecord.getStatus());
            //--风控和未绑卡的拦截，都发生在真正的启动出款任务前
            if(statusEnum != PromoTradeStatusEnum.SUBMIT){
                if(statusEnum == PromoTradeStatusEnum.SUSS || statusEnum == PromoTradeStatusEnum.FAIL){
                    return Result.success();
                }else{
                    return Result.error(ErrorCodeConstants.ACCORE_101900.getErrorCode(),String.format("PromoOAStartTask奖励下发-启动出款任务时，数据异常:状态异常：{}", JSON.toJSONString(existRecord)));
                }
            }

            //03-风控:支付必须先走风控-风控被拦截则异常中断
            Pair<Boolean, String> riskResp = riskService.checkName(existRecord.getUserCode(),existRecord.getTransBillDate());
            Logger.info("PromoOAStartTask奖励下发-启动出款任务时-风控处理结果:{}-{}-{}:{}",existRecord.getRequestNo(),existRecord.getUserCode(),existRecord.getTransBillDate(),ObjectUtils.isEmpty(riskResp)?"拦截":(riskResp.getLeft()+riskResp.getRight()));
            if(ObjectUtils.isEmpty(riskResp) || !riskResp.getLeft()){
                Logger.warn("PromoOAStartTask奖励下发-启动出款任务时-被风控拦截:{}",JSON.toJSONString(existRecord));
                com.akucun.common.Result<Void> notifyRslt = factory.getHandler(busiTypeEnum).syncNotify(existRecord.getRequestNo(),existRecord.getBusiType(),existRecord.getTransBillDate()
                        ,PromoTradeStatusEnum.RISK_REJECT.getCode(), ObjectUtils.isEmpty(riskResp)?"风控异常":riskResp.getRight());
                if(!ObjectUtils.isEmpty(notifyRslt) && !notifyRslt.isSuccess()){
                    result.setSuccess(false);
                    String notifyErrorMsg = String.format("PromoOAStartTask奖励下发-启动出款任务时-被风控拦截-异步通知失败:bizNo=%s,notifyRslt=%s",existRecord.getRequestNo(),JSON.toJSONString(notifyRslt));
                    result.setMessage(notifyErrorMsg);
                    Logger.error(notifyErrorMsg);
                }
                return result;
            }

            //04-付款申请
            String userAuthType = MemberGrade.getAuthName(existRecord.getUserGrade());
            String customerCode = existRecord.getUserCode();
            String customerType = StringUtils.isEmpty(existRecord.getUserType())?"NM":existRecord.getUserType();
            //String payBillDate = existRecord.getTransBillDate();
            RewardTypeEnum rewardTypeEnum = RewardTypeEnum.getByCode(existRecord.getBusiType());
            String remark = existRecord.getRemark();
            String accountType = "XD_BALANCE";
            //初始化待更新的状态
            String newStatus = PromoTradeStatusEnum.SUSS.getCode();
            String errorMsg = "suss";
            String payBizNo = existRecord.getRequestNo();
            //月勤奖赋能营奖励发放升级：个人认证不在发放到奖励金，修订为直接发放到余额账户，因此：
            //乐税认证 = 发放到余额账户并提现  企业认证+个人=发放到余额账户
            /*if(PERSON_AUTH_NAME.equalsIgnoreCase(userAuthType)){
                //发放奖励金账户
                customerCode = customerCode.startsWith("NM") ? customerCode.substring(2) : customerCode;
                accountType = "BONUS";
                //上游上送的业务流水号过长，而老奖励金字段长度32，因此这里强制md5，弊端就是关联查询数据库很麻烦
                payBizNo = MD5Util.md5(existRecord.getRequestNo());
            }else{*/
                //余额提现账户
                customerCode = customerCode.startsWith("NM") ? customerCode : String.format("%s%s","NM",customerCode);
                accountType = "XD_BALANCE";
                //乐税级别，走发放饷店并提现，其他（这里有企业认证+个人）只发放饷店余额同步就返回结果了
                if(LESHUI_AUTH_NAME.equalsIgnoreCase(userAuthType)){
                    newStatus = PromoTradeStatusEnum.PAYING.getCode();
                }
                //为方便测试同一单号重复提现(提现涉及的表删除较为麻烦，这里mock了请求单号)
                if(PAY_MOCK){
                    payBizNo = String.format("%s#%s",existRecord.getRequestNo(), DateUtils.format(new Date(),"MMddHHmm"));
                }
            //}
            Logger.info("PromoPayTask{}req:customerCode={},customerType={},bizNo={},amount={},businessType={},accountType={}",remark,customerCode,customerType,payBizNo,existRecord.getTradeAmt(),rewardTypeEnum.getCode(),accountType);
            Result<Void> accountPayRslt = promoTradeService.monthlyDiligenceEmpowerAwardSettle(customerCode,customerType,payBizNo,existRecord.getTradeAmt(),remark,rewardTypeEnum.getCode(),accountType,existRecord.getUserGrade());
            Logger.info("PromoPayTask{}resp:{}", remark,JSON.toJSONString(accountPayRslt));

            //05-状态更新和异步通知
            if(ObjectUtils.isEmpty(accountPayRslt) || !accountPayRslt.getSuccess()){
                newStatus = PromoTradeStatusEnum.FAIL.getCode();
                errorMsg = ObjectUtils.isEmpty(accountPayRslt)?"出款异常，请联系管理员处理":accountPayRslt.getMessage();
                //未绑卡的特殊处理：已经预定义了该状态，暂时没有启用
                if(NO_BIND_CARD_STATUS_SWITCH && !ObjectUtils.isEmpty(accountPayRslt) && ResponseEnum.BIND_CARD_ERROR.getCode().intValue() == accountPayRslt.getCode().intValue()){
                    newStatus = PromoTradeStatusEnum.NO_CARD_BIND.getCode();
                }
            }
            com.akucun.common.Result<Void> notifyRslt = factory.getHandler(busiTypeEnum).syncNotify(existRecord.getRequestNo(),existRecord.getBusiType(),existRecord.getTransBillDate(),newStatus,errorMsg);
            if(!ObjectUtils.isEmpty(notifyRslt) && !notifyRslt.isSuccess()){
                result.setSuccess(false);
                String notifyErrorMsg = String.format("%s异步通知失败:bizNo=%s,notifyRslt=%s",remark,existRecord.getRequestNo(),JSON.toJSONString(notifyRslt));
                result.setMessage(notifyErrorMsg);
                Logger.error(notifyErrorMsg);
            }
        }catch (Exception e){
            result.setSuccess(false);
            result.setMessage(e.getMessage());
            Logger.error("营销月勤奖下发异常", e);
        }

        return result;
    }

    /**
     * 支付结果回调
     * @param tradeNo
     * @param payRslt
     * @param errorMsg
     * @return
     */
    public Result<Void> payCallback(String tradeNo, Boolean payRslt, String errorMsg){
        Logger.info("PromoPayTask支付结果异步回调:{}-{}-失败原因:{}", tradeNo,payRslt?"成功":"失败",errorMsg);
        if(PAY_MOCK || (!StringUtils.isEmpty(tradeNo) && tradeNo.contains("#"))){
            tradeNo = tradeNo.substring(0,tradeNo.indexOf("#"));
            Logger.info("PromoPayTask支付结果异步回调2:{}-{}-失败原因:{}", tradeNo,payRslt?"成功":"失败",errorMsg);
        }

        //01-获取奖励发放记录
        List<RewardApply> existRecords = rewardApplyService.queryRewardApplyList(tradeNo, null, null, null);
        if(CollectionUtils.isEmpty(existRecords)){
            Logger.warn("PromoPayTask支付结果回调-数据异常:记录不存在：{}",tradeNo);
            return Result.error(ErrorCodeConstants.ACCORE_101900.getErrorCode(),String.format("PromoPayTask支付结果回调-数据异常:记录不存在：%s",tradeNo));
        }
        RewardApply existRecord = existRecords.get(0);

        //02-状态变更判断
        String oldStatus = existRecord.getStatus();
        String newStatus = payRslt ? PromoTradeStatusEnum.SUSS.getCode() : PromoTradeStatusEnum.FAIL.getCode();
        if(oldStatus.equalsIgnoreCase(newStatus)){
            Logger.warn("PromoPayTask支付结果回调[{}-{}-{}]-跳过更新-当前任务状态一致:{}", tradeNo,payRslt?"成功":"失败",errorMsg,oldStatus);
            return Result.success();
        }

        //03-更新状态并通知上游
        RewardTypeEnum busiTypeEnum = RewardTypeEnum.getByCode(existRecord.getBusiType());
        com.akucun.common.Result<Void> notifyRslt = factory.getHandler(busiTypeEnum).syncNotify(existRecord.getRequestNo(),existRecord.getBusiType(),existRecord.getTransBillDate(),newStatus,errorMsg);
        if(!ObjectUtils.isEmpty(notifyRslt) && !notifyRslt.isSuccess()){
            return Result.error(ErrorCodeConstants.ACCORE_101900.getErrorCode(),String.format("PromoPayTask支付结果-回调上游失败：{}", JSON.toJSONString(existRecord)));
        }

        return Result.success();
    }


    @Override
    protected String getActionType() {
        return PostActionTypes.PROMO_PAY.getName();
    }

}
