package com.akucun.account.proxy.service.acct;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.facade.stub.others.account.req.WithdrawQueryReq;
import com.akucun.account.proxy.facade.stub.others.account.vo.AccountWithdrawVO;
import com.akucun.account.proxy.facade.stub.others.account.vo.NotifyWithdrawVO;
import com.akucun.bcs.channel.facade.stub.dto.WechatWithdrawRequest;

/**
 * @Author: silei
 * @Date: 2021/3/8
 * @desc:
 */
public interface AccountWithdrawService {

    /**
     * 账户提现接口
     * @param accountWithdrawVO
     * @return
     */
    Result<String> accountWithdraw(AccountWithdrawVO accountWithdrawVO);

    /**
     * 账户提现结果通知接口
     * @param notifyWithdrawVO
     * @return
     */
    Result<Void> notifyWithdrawResult(NotifyWithdrawVO notifyWithdrawVO);

    /**
     * 查询提现明细
     * @param withdrawQueryReq
     * @return
     */
    Result<AccountWithdrawVO> queryWithdrawDetail(WithdrawQueryReq withdrawQueryReq);

    /**
     * 商家微信账户提现结果查询
     * @param request
     * @return
     */
    Result<Void> queryMerchantWechatWithdraw(WechatWithdrawRequest request);



    /**
     * 是否存在处理中的提现
     * @param customerCode
     * @param customerType
     * @return
     */
    Result<Boolean> processingWithdrawExist(String customerCode, String customerType);

    /**
     * 提现到微信余额
     *
     * @param accountWithdrawVO
     * @return
     */
    Result<String> accountWechatWithdraw(AccountWithdrawVO accountWithdrawVO);

    /**
     * 添加提现记录
     * @param accountWithdrawVO
     * @return
     */
    Result<String> addWithdrawRecord(AccountWithdrawVO accountWithdrawVO);

    /**
     * 获取提现回单下载地址
     * @param withdrawNo
     * @return
     */
    Result<String> getWithdrawReceiptDownloadUrl(String withdrawNo);
}
