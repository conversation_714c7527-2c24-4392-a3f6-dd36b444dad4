package com.akucun.account.proxy.service.acct.impl;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.common.constant.CommonConstants;
import com.akucun.account.proxy.common.utils.Results;
import com.akucun.account.proxy.dao.mapper.AccountTenantCustomerMapper;
import com.akucun.account.proxy.dao.model.AccountTenantCustomer;
import com.akucun.account.proxy.facade.stub.others.dto.req.IsTenantCustomerReq;
import com.akucun.account.proxy.service.acct.AccountCheckService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Author: mapenghui
 * @Date: 2022/3/24
 * @desc:
 */
@Service
public class AccountCheckServiceImpl implements AccountCheckService {

    @Autowired
    private AccountTenantCustomerMapper accountTenantCustomerMapper;

    @Override
    public Result<Boolean> isTenantCustomer(IsTenantCustomerReq req) {
        if(req == null || StringUtils.isBlank(req.getCustomerType()) || StringUtils.isBlank(req.getCustomerCode())){
            return Results.error(CommonConstants.GENERAL_CODE, "参数缺失");
        }
        LambdaQueryWrapper<AccountTenantCustomer> wrapper = new LambdaQueryWrapper<AccountTenantCustomer>().eq(AccountTenantCustomer::getCustomerCode, req.getCustomerCode()).eq(AccountTenantCustomer::getCustomerType, req.getCustomerType());
        AccountTenantCustomer tenantCustomer = accountTenantCustomerMapper.selectOne(wrapper);
        if (tenantCustomer != null) {
            return Result.success(true);
        } else {
            return Result.success(false);
        }
    }

}
