package com.akucun.account.proxy.service.tradeflow.agreement;

import com.akucun.account.proxy.facade.stub.others.trade.res.TradePhaseStatusEnum;
import com.akucun.account.proxy.facade.stub.others.trade.res.TradeStatusEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

@Getter
@Setter
public class TradeAgreementPhase {

    /**
     * 阶段编码，即阶段标识
     */
    private String code;

    /**
     * 业务参数类型
     */
    private String bizInfoCls;

    /**
     * 阶段构建脚本
     */
    private String buildScript;

    /**
     * 阶段执行脚本
     */
    private String execScript;

    /**
     * 阶段状态到交易流程状态的映射
     */
    private Map<TradePhaseStatusEnum, TradeStatusEnum> statusMap;

    /**
     * 阶段状态到下一执行阶段的映射
     */
    private Map<TradePhaseStatusEnum, String> phaseMap;

}
