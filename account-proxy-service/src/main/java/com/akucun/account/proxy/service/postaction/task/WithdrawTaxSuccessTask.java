package com.akucun.account.proxy.service.postaction.task;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.common.enums.PostActionTypes;
import com.akucun.account.proxy.dao.model.PostActionItem;
import com.akucun.account.proxy.dao.model.WithdrawTaxDetail;
import com.akucun.account.proxy.service.acct.WithdrawTaxService;
import com.akucun.account.proxy.service.postaction.common.AbsPostActionExecutor;
import com.akucun.fps.common.util.GsonUtils;
import com.akucun.fps.schedule.model.ScheduleItem;
import com.mengxiang.base.common.log.Logger;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author: silei
 * @Date: 2021/3/10
 * @desc: 提现成功会员间交易扣税
 */
@Component
public class WithdrawTaxSuccessTask extends AbsPostActionExecutor {

    @Autowired
    private WithdrawTaxService withdrawTaxService;

    @XxlJob("withdrawTaxSuccessTask")
    public ReturnT<String> execute(String param) {
        return this.executeEntrance();
    }

    @Override
    protected String getActionType() {
        return PostActionTypes.WITHDRAWTAX_MEMBERTRADE.getName();
    }

    @Override
    public Result<Void> execute(PostActionItem item) {
        Result<Void> result = Result.success();
        try {
            WithdrawTaxDetail taxDetail = GsonUtils.getInstance().fromJson(item.getParam(),WithdrawTaxDetail.class);
            //扣税交易
            result = withdrawTaxService.withdrawTaxTrade(taxDetail);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setMessage(e.getMessage());
            Logger.error("提现扣税会员交易任务失败", e);
        }
        return result;
    }
}
