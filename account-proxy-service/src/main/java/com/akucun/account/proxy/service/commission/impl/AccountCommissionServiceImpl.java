package com.akucun.account.proxy.service.commission.impl;

import com.aikucun.common2.base.Result;
import com.aikucun.common2.utils.datamasking.DataMask;
import com.akucun.account.center.client.model.TradeInfo;
import com.akucun.account.center.client.model.account.AccountBookDO;
import com.akucun.account.center.client.model.account.AccountBookDetailDO;
import com.akucun.account.center.client.model.query.AccountDetailQuery;
import com.akucun.account.center.client.model.query.AccountQuery;
import com.akucun.account.center.common.entity.Query;
import com.akucun.account.center.common.entity.ResultList;
import com.akucun.account.center.feign.AccountCenterService;
import com.akucun.account.proxy.service.config.DecouplingConfig;
import com.akucun.account.proxy.common.constant.CommonConstants;
import com.akucun.account.proxy.common.enums.PostActionExecStatus;
import com.akucun.account.proxy.common.enums.PostActionTypes;
import com.akucun.account.proxy.common.enums.ResponseEnum;
import com.akucun.account.proxy.common.enums.ResultStatus;
import com.akucun.account.proxy.common.utils.Results;
import com.akucun.account.proxy.common.utils.WechatNotifyTool;
import com.akucun.account.proxy.dao.mapper.AccountCommissionTradeMapper;
import com.akucun.account.proxy.dao.mapper.AccountTenantCustomerMapper;
import com.akucun.account.proxy.dao.mapper.AccountTenantMerchantMapper;
import com.akucun.account.proxy.dao.model.AccountCommissionTrade;
import com.akucun.account.proxy.dao.model.AccountTenantCustomer;
import com.akucun.account.proxy.dao.model.AccountTenantMerchant;
import com.akucun.account.proxy.dao.model.AgentWhiteListAccount;
import com.akucun.account.proxy.facade.stub.enums.AccountKeyConstants;
import com.akucun.account.proxy.facade.stub.enums.CustomerType;
import com.akucun.account.proxy.facade.stub.enums.DetailTypeConstants;
import com.akucun.account.proxy.service.acct.AccountService;
import com.akucun.account.proxy.service.commission.AccountCommissionService;
import com.akucun.account.proxy.service.commission.bo.AccountCommissionBO;
import com.akucun.account.proxy.service.common.AgentWhiteListAccountService;
import com.akucun.account.proxy.service.postaction.PostActionService;
import com.akucun.account.proxy.service.postaction.bo.PostActionItemBO;
import com.akucun.fps.pingan.client.model.po.PinganAccount;
import com.akucun.fps.pingan.client.vo.AccountRegisterVO;
import com.akucun.fps.pingan.client.vo.CashCreditVO;
import com.akucun.fps.pingan.client.vo.DealTradeVO;
import com.akucun.fps.pingan.feign.api.assets.AssetsServiceApi;
import com.akucun.fps.pingan.feign.api.member.MemberServiceApi;
import com.akucun.fps.pingan.feign.api.merchant.MerchantServiceApi;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mengxiang.base.common.log.Logger;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

import static com.akucun.account.proxy.common.enums.PostActionTypes.DELAY_COMMISSION;

/**
 * @Author: silei
 * @Date: 2020/12/8
 * @desc: 分佣service
 */
@Service
public class AccountCommissionServiceImpl extends ServiceImpl<AccountCommissionTradeMapper, AccountCommissionTrade> implements AccountCommissionService {

    @Resource
    private MerchantServiceApi merchantServiceApi;
    @Resource
    private AssetsServiceApi assetsServiceApi;
    @Resource
    private MemberServiceApi memberServiceApi;
    @Resource
    private AccountService accountService;
    @Resource
    private AccountCenterService accountCenterService;
    @Resource
    private PostActionService postActionService;
    @Resource
    private AgentWhiteListAccountService agentWhiteListAccountService;
    @Resource
    private AccountTenantCustomerMapper accountTenantCustomerMapper;
    @Resource
    private AccountTenantMerchantMapper accountTenantMerchantMapper;
    @Autowired
    private WechatNotifyTool wechatNotifyTool;
    @Autowired
    private DecouplingConfig decouplingConfig;
    
    @Value("${withdraw.disconnect.switch:true}")
    private Boolean disconnectSwitch;

    @Value("${tenant.commission.relation.check.switch:true}")
    private Boolean tenantCommissionRelationCheckSwitch;

    /**
     * 记账账户类型
     */
    private List<String> accountCustomerTypeList;

    @Value("${account.customer.types:TB}")
    public void setAccountCustomerTypes(String accountCustomerTypes) {

        Logger.info("AccountCommissionServiceImpl accountCustomerTypes :{}",accountCustomerTypes);
       if (StringUtils.isBlank(accountCustomerTypes)){
           accountCustomerTypeList = new ArrayList<>();
       }else {
           accountCustomerTypeList = Arrays.asList(accountCustomerTypes.split(","));
       }
    }

    /**
     * 企业饷店店主店长分佣交易类型
     */
    private List<String> saasTradeTypeList;

    @Value("${saas.trade.types:TRADE_TYPE_461}")
    public void setSaasTradeTypeList(String saasTradeTypes) {
        Logger.info("AccountCommissionServiceImpl saasTradeTypes :{}",saasTradeTypes);
        if (StringUtils.isBlank(saasTradeTypes)){
            saasTradeTypeList = new ArrayList<>();
        }else {
            saasTradeTypeList = Arrays.asList(saasTradeTypes.split(","));
        }
    }

    /**
     * 企业饷店B-b-e交易类型映射
     */
    private HashMap<String, String> saasTradeTypeMap;

    @Value("${saas.trade.type.maps:TRADE_TYPE_461:TRADE_TYPE_424}")
    public void setSaasTradeTypeMap(String saasTradeTypeMaps) {
        Logger.info("AccountCommissionServiceImpl saasTradeTypeMaps :{}",saasTradeTypeMaps);
        saasTradeTypeMap = new HashMap<>();
        List<String> list = Arrays.asList(saasTradeTypeMaps.split(","));
        list.forEach(t -> {
            String[] map = t.split(":");
            saasTradeTypeMap.put(map[0], map[1]);
        });
    }

    /**
     * 企业饷店B-b-e交易类型映射
     */
    private HashMap<String, String> saasFreezeTradeTypeMap;

    @Value("${saas.freeze.type.maps:TRADE_TYPE_461:TRADE_TYPE_423}")
    public void setSaasFreezeTradeTypeMap(String saasFreezeTradeTypeMaps) {
        Logger.info("AccountCommissionServiceImpl saasFreezeTradeTypeMaps :{}",saasFreezeTradeTypeMaps);
        saasFreezeTradeTypeMap = new HashMap<>();
        List<String> list = Arrays.asList(saasFreezeTradeTypeMaps.split(","));
        list.forEach(t -> {
            String[] map = t.split(":");
            saasFreezeTradeTypeMap.put(map[0], map[1]);
        });
    }

    /**
     * 非分佣交易类型, 目前包括佣金立减交易
     */
    private List<String> nonCommissionTradeTypeList;

    @Value("${non.commission.trade.types:TRADE_TYPE_433,TRADE_TYPE_452,TRADE_TYPE_254}")
    public void setNonCommissionTradeTypeList(String nonCommissionTradeTypes) {
        Logger.info("AccountCommissionServiceImpl nonCommissionTradeTypes :{}",nonCommissionTradeTypes);
        if (StringUtils.isBlank(nonCommissionTradeTypes)){
            nonCommissionTradeTypeList = new ArrayList<>();
        }else {
            nonCommissionTradeTypeList = Arrays.asList(nonCommissionTradeTypes.split(","));
        }
    }

    /**
     * 是否是非分佣交易
     * @param tradeType
     * @return
     */
    public boolean isNonCommissionTrade(String tradeType) {
        if (StringUtils.isBlank(tradeType)) {
            return false;
        }
        return nonCommissionTradeTypeList.contains(tradeType);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> allocate(AccountCommissionBO bo) {
        //1.参数校验
        Result<Void> result = checkCommissionParams(bo);
        if (!result.getSuccess()) {
            return result;
        }
        if (!accountCustomerTypeList.contains(bo.getCustomerType())){
            //2.平安账户校验
            AccountRegisterVO vo = new AccountRegisterVO();
            vo.setCustomerCode(bo.getCustomerCode());
            vo.setCustomerType(bo.getCustomerType());
            com.akucun.fps.common.entity.Result<PinganAccount> pinganAccountResult = merchantServiceApi.selectPinganAccount(vo);
            if (!pinganAccountResult.isSuccess() || pinganAccountResult.getData() == null) {
                return Results.error(ResponseEnum.NO_PINGAN_ACCOUNT);
            }
        }
        //3.账户校验
        result = accountCheck(bo);
        if (!result.getSuccess()){
            return result;
        }
        //4.请求记录入库
        AccountCommissionTrade trade = new AccountCommissionTrade();
        BeanUtils.copyProperties(bo, trade);
        trade.setStatus(ResultStatus.I.getCode());
        try {
            this.baseMapper.insert(trade);
        } catch (DuplicateKeyException de) {
            Logger.info("allocate commission record exist, customerCode:{},tradeNo:{}", trade.getCustomerCode(), trade.getTradeNo());
            //幂等冲突，返回成功
            return Result.success();
        }

        //5.加入异步任务
        PostActionItemBO itemBO = PostActionItemBO.builder()
                .actionType(DELAY_COMMISSION.getName())
                .bizId(bo.getTradeNo())
                .paramObject(trade)
                .status(PostActionExecStatus.EXECUTE.value())
                //延迟10秒执行
                .nextRetryTime(LocalDateTime.now().plusSeconds(10))
                .retryNums(0)
                .remark(bo.getRemark())
                .build();
        postActionService.addAction(itemBO);
        return Result.success();
    }

    /**
     * 参数校验
     *
     * @param bo
     * @return
     */
    private Result<Void> checkCommissionParams(AccountCommissionBO bo) {
        if (bo == null) {
            return Result.error(ResponseEnum.LACK_OF_PARAM.getCode(), "分佣请求对象不可为空");
        }
        if (StringUtils.isBlank(bo.getCustomerCode())) {
            return Result.error(ResponseEnum.LACK_OF_PARAM.getCode(), "客户编码不可为空");
        }
        if (StringUtils.isBlank(bo.getCustomerType())) {
            return Result.error(ResponseEnum.LACK_OF_PARAM.getCode(), "客户类型不可为空");
        }
        if (bo.getAmount() == null || BigDecimal.ZERO.compareTo(bo.getAmount()) >= 0) {
            return Result.error(ResponseEnum.LACK_OF_PARAM.getCode(), "分佣金额必须大于零");
        }
        //店长时，shopId不可为空
        if (bo.getCustomerType().equals(CustomerType.NMDL.getName()) && StringUtils.isBlank(bo.getShopId())) {
            return Result.error(ResponseEnum.LACK_OF_PARAM.getCode(), "店长分佣时，shopId不可为空");
        }
        return Result.success();
    }

    /**
     * 账户检查
     * @param bo
     * @return
     */
    private Result<Void> accountCheck(AccountCommissionBO bo) {
        AccountQuery query = new AccountQuery();
        query.setCustomerCode(bo.getCustomerCode());
        query.setAccountTypeKey(accountService.convertAccountTypeKey(bo.getCustomerType()));
        com.akucun.common.Result<AccountBookDO> result = accountCenterService.queryAccount(query);
        if (result == null || !result.isSuccess()){
            Logger.error("accountCenterService.queryAccount, result:{}", JSONObject.toJSONString(result));
            return Results.error(ResponseEnum.SERVER_RESP_ERROR);
        }
        if (result.getData() == null){
            Logger.error("no account center account, customerCode:{}", bo.getCustomerCode());
            return Results.error(ResponseEnum.NO_CENTER_ACCOUNT);
        }
        if (result.getData().isOptionFreeze()){
            Logger.error("allocate account is locked, customerCode:{}", bo.getCustomerCode());
            return Results.error(ResponseEnum.ACCOUNT_UPGRADE_LOCK);
        }
        return Result.success();
    }


    @Override
    public Result<Void> asyncAllocate(AccountCommissionTrade trade) {
        boolean res;
        switch (trade.getCustomerType()) {
            case "NMDL":
                if (isNonCommissionTrade(trade.getTradeType())) {
                    //店长非分佣交易
                    res = nonCommissionTrade(trade);
                } else {
                    //店长分佣
                    res = shopAgentCommission(trade);
                }
                break;
            case "NM":
            case "DG":
            case "DCC":
            case "AT":
                if (isNonCommissionTrade(trade.getTradeType())) {
                    //店主、代购、大仓仓、SaaS租户非分佣交易
                    res = nonCommissionTrade(trade);
                } else {
                    //店主、代购、大仓仓、SaaS租户分佣
                    res = normalCommission(trade);
                }
                break;
            case "TB":
                res = groupCommission(trade);
                break;
            default:
                return Results.error(CommonConstants.GENERAL_CODE, "账户类型错误");
        }
        if (res) {
            trade.setStatus(ResultStatus.S.getCode());
            LambdaQueryWrapper<AccountCommissionTrade> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(AccountCommissionTrade::getId, trade.getId())
                    .eq(AccountCommissionTrade::getCustomerCode, trade.getCustomerCode());
            baseMapper.update(trade, wrapper);
            return Result.success();
        } else {
            return Results.error(ResponseEnum.ALLOCATE_COMMISSION_EXCEPTION);
        }
    }

    /**
     * 团长大营分佣（业务已停）
     * @param trade
     * @return
     */
    private boolean groupCommission(AccountCommissionTrade trade) {
        //2.账户中心登账
        TradeInfo tradeInfo = new TradeInfo();
        tradeInfo.setAccountTypeKey(accountService.convertAccountTypeKey(trade.getCustomerType()));
        tradeInfo.setTradeNo(trade.getTradeNo());
        tradeInfo.setSourceBillNo(trade.getSourceBillNo());
        tradeInfo.setTradeType(trade.getTradeType());
        tradeInfo.setAmount(trade.getAmount());
        tradeInfo.setCustomerCode(trade.getCustomerCode());
        tradeInfo.setRemark(trade.getRemark());
        //调用账户中心自动分账
        com.akucun.common.Result<Void> tradeResult = accountCenterService.dealTrade(tradeInfo);
        if (tradeResult == null || !tradeResult.isSuccess()) {
            Logger.error("groupCommission 店长调用账户中心 参数:{}, 结果:{}", JSONObject.toJSONString(tradeInfo), JSONObject.toJSONString(tradeResult));
            return false;
        }
        return true;
    }

    /**
     * 正常分佣
     *
     * @param trade
     * @return
     */
    private boolean normalCommission(AccountCommissionTrade trade) {
        boolean result;
        //查询是否是租户
        LambdaQueryWrapper<AccountTenantCustomer> tenantWrapper = new LambdaQueryWrapper<>();
        tenantWrapper.eq(AccountTenantCustomer::getCustomerCode, trade.getCustomerCode())
                .eq(AccountTenantCustomer::getCustomerType, trade.getCustomerType());
        AccountTenantCustomer accountTenantCustomer = accountTenantCustomerMapper.selectOne(tenantWrapper);

        //针对企业饷店的店主/店长分佣添加拦截判断，如果关联关系不存在则报错
        if (!checkTenantCustomerRelation(trade, accountTenantCustomer)) {
            return false;
        }

        if (accountTenantCustomer != null) {
            Logger.info("租户店主分佣 customer:{}", JSONObject.toJSONString(accountTenantCustomer));
            //租户店主分佣
            result = tenantCommission(trade, accountTenantCustomer);
        } else {
            if (decouplingConfig.decouplingSwitch(trade.getCustomerCode(),trade.getCustomerType())) {
                result = decouplingCommission(trade);
            } else {
                result = commonCommission(trade);
            }
        }
        return result;
    }

    /**
     * 租户店主店长分佣
     * @param trade
     * @param customer
     * @return
     */
    private boolean tenantCommission(AccountCommissionTrade trade, AccountTenantCustomer customer) {
        //查看租户是否存在
        AccountTenantMerchant merchant = accountTenantMerchantMapper.selectOne(new LambdaQueryWrapper<AccountTenantMerchant>().eq(AccountTenantMerchant::getTenantId, customer.getTenantId()).eq(AccountTenantMerchant::getStatus,0));
        Logger.info("tenantCommission merchant:{}", JSONObject.toJSONString(merchant));
        if (merchant == null){
            Logger.error("tenantCommission merchant not exist, customerCode: {}, tenantId: {}",customer.getCustomerCode(), customer.getTenantId());
            return false;
        }
        //新老企业饷店店主店长分佣逻辑区分
        if (saasTradeTypeList.contains(trade.getTradeType())) {
            return newTenantCommission(trade, merchant);
        } else {
            return originTenantCommission(trade, merchant);
        }
    }

    /**
     * 企业饷店B-b-e店主店长分佣
     * @param trade
     * @param merchant
     * @return
     */
    private boolean newTenantCommission(AccountCommissionTrade trade, AccountTenantMerchant merchant) {

        //1.B账户中心明细记录
        Query<AccountDetailQuery> query = new Query<>();
        AccountDetailQuery accountDetail = new AccountDetailQuery();
        accountDetail.setAccountTypeKey(AccountKeyConstants.AT.getName());
        accountDetail.setCustomerCode(merchant.getTenantId());
        accountDetail.setTradeNo(trade.getTradeNo());
        accountDetail.setTradeType(saasFreezeTradeTypeMap.get(trade.getTradeType()));
        query.setData(accountDetail);
        Logger.info("newTenantCommission 租户分账冻查询 参数：{}", DataMask.toJSONString(accountDetail));
        ResultList<AccountBookDetailDO> resultList = accountCenterService.queryAccountDetail(query);
        if(Objects.isNull(resultList) || CollectionUtils.isEmpty(resultList.getDatalist())) {
            Logger.error("newTenantCommission 未查询到租户分账冻结记录 参数:{}, 结果:{}", JSONObject.toJSONString(query), JSONObject.toJSONString(resultList));
            wechatNotifyTool.sendNotifyMsg("未查询到租户分账冻结记录，customerCode：" + trade.getCustomerCode() + "， 租户ID：" + merchant.getTenantId() + "，tradeNo：" + trade.getTradeNo());
            return false;
        }

        //2.B账户中心冻结余额扣减
        TradeInfo tenantInfo = new TradeInfo();
        tenantInfo.setAccountTypeKey(AccountKeyConstants.AT.getName());
        tenantInfo.setCustomerCode(merchant.getTenantId());
        tenantInfo.setSourceBillNo(trade.getSourceBillNo());
        tenantInfo.setTradeNo(trade.getTradeNo());
        tenantInfo.setTradeType(saasTradeTypeMap.get(trade.getTradeType()));
        tenantInfo.setAmount(trade.getAmount());
        tenantInfo.setRemark(trade.getRemark());
        Logger.info("newTenantCommission 租户账户中心冻结余额扣减 tenantInfo :{}",JSONObject.toJSONString(tenantInfo));
        com.akucun.common.Result<Void> tenantResult = accountCenterService.dealTrade(tenantInfo);
        if (tenantResult == null || !tenantResult.isSuccess()) {
            Logger.error("newTenantCommission 租户账户中心冻结余额扣减 参数:{}, 结果:{}", JSONObject.toJSONString(tenantInfo), JSONObject.toJSONString(tenantResult));
            return false;
        }
        //3.B平安账户会员间交易到店主店长平安账户
        if(!decouplingConfig.decoupleTenantSwitch(trade.getCustomerCode(), trade.getCustomerType())) {
        	 DealTradeVO vo = new DealTradeVO();
             String saasTradeType = saasTradeTypeMap.get(trade.getTradeType());
             vo.setOrderNo(trade.getTradeNo() + saasTradeType.substring(saasTradeType.length() - 3));
             vo.setCustomerCode(merchant.getTenantId());
             vo.setCustomerType(CustomerType.AT.getName());
             vo.setTranAmount(trade.getAmount());
             vo.setDealTradeType(saasTradeType);
             vo.setContent("租户分佣");
             vo.setSellerCode(trade.getCustomerCode());
             vo.setSellerType(trade.getCustomerType());
             Logger.info("newTenantCommission 租户店主店长平安会员间交易 vo:{}",JSONObject.toJSONString(vo));
             com.akucun.fps.common.entity.Result<Void> tenantTradeResult = memberServiceApi.tenantDealTrade(vo);
             if (tenantTradeResult ==null || !tenantTradeResult.isSuccess()){
                 Logger.error("newTenantCommission 租户店主店长平安会员间交易 参数:{}, 结果:{}", JSONObject.toJSONString(vo), JSONObject.toJSONString(tenantTradeResult));
                 return false;
             }
        }
        //4.店主店长账户中心登账
        TradeInfo tradeInfo = new TradeInfo();
        tradeInfo.setAccountTypeKey(accountService.convertAccountTypeKey(trade.getCustomerType()));
        tradeInfo.setTradeNo(trade.getTradeNo());
        tradeInfo.setTradeType(trade.getTradeType());
        tradeInfo.setAmount(trade.getAmount());
        tradeInfo.setCustomerCode(trade.getCustomerCode());
        tradeInfo.setSourceBillNo(trade.getSourceBillNo());
        tradeInfo.setRemark(trade.getRemark());
        Logger.info("newTenantCommission 店主店长账户中心登账 tradeInfo :{}",JSONObject.toJSONString(tradeInfo));
        com.akucun.common.Result<Void> tradeResult = accountCenterService.dealTrade(tradeInfo);
        if (tradeResult == null || !tradeResult.isSuccess()) {
            Logger.error("newTenantCommission 店主店长账户中心登账 参数:{}, 结果:{}", JSONObject.toJSONString(tradeInfo), JSONObject.toJSONString(tradeResult));
            return false;
        }
        return true;
    }

    /**
     * 原有企业饷店店主店长分佣
     * @param trade
     * @param merchant
     * @return
     */
    private boolean originTenantCommission(AccountCommissionTrade trade, AccountTenantMerchant merchant) {
        if(!decouplingConfig.decoupleTenantSwitch(trade.getCustomerCode(), trade.getCustomerType())) {
	    	//1.租户分账
	        CashCreditVO cashCreditVO = new CashCreditVO();
	        cashCreditVO.setBillNo(trade.getTradeNo());
	        cashCreditVO.setBillType(trade.getTradeType());
	        cashCreditVO.setThirdCustId(merchant.getTenantId());
	        cashCreditVO.setCustomerType(CustomerType.AT.getName());
	        //转换成分(四舍五入)
	        BigDecimal bd = trade.getAmount().setScale(2, BigDecimal.ROUND_HALF_UP);
	        cashCreditVO.setTranAmount(bd.multiply(BigDecimal.valueOf(100)).intValue());
	        cashCreditVO.setRemark(trade.getRemark());
	        Logger.info("tenantCommission cashCreditVO:{}",JSONObject.toJSONString(cashCreditVO));
	        com.akucun.fps.common.entity.Result<String> assetResult = assetsServiceApi.credit(cashCreditVO);
	        if (assetResult == null || !assetResult.isSuccess()) {
	            Logger.error("tenantCommission 租户平安账户清分 参数:{}, 结果:{}", JSONObject.toJSONString(cashCreditVO), JSONObject.toJSONString(assetResult));
	            return false;
	        }
	        //2.租户店主店长会员间交易(租户转给店主店长)
	        DealTradeVO vo = new DealTradeVO();
	        vo.setOrderNo(trade.getTradeNo() + trade.getTradeType().substring(trade.getTradeType().length() - 3));
	        vo.setCustomerCode(merchant.getTenantId());
	        vo.setCustomerType(CustomerType.AT.getName());
	        vo.setTranAmount(trade.getAmount());
	        vo.setDealTradeType(trade.getTradeType());
	        vo.setContent("租户分佣");
	        vo.setSellerCode(trade.getCustomerCode());
	        vo.setSellerType(trade.getCustomerType());
	        Logger.info("tenantCommission tenantDealTrade vo:{}",JSONObject.toJSONString(vo));
	        com.akucun.fps.common.entity.Result<Void> tenantTradeResult = memberServiceApi.tenantDealTrade(vo);
	        if (tenantTradeResult ==null || !tenantTradeResult.isSuccess()){
	            Logger.error("tenantCommission 租户店主店长平安账户会员间交易 参数:{}, 结果:{}", JSONObject.toJSONString(vo), JSONObject.toJSONString(tenantTradeResult));
	            return false;
	        }
        }
        //3.账户中心登账
        TradeInfo tradeInfo = new TradeInfo();
        tradeInfo.setAccountTypeKey(accountService.convertAccountTypeKey(trade.getCustomerType()));
        tradeInfo.setTradeNo(trade.getTradeNo());
        tradeInfo.setTradeType(trade.getTradeType());
        tradeInfo.setAmount(trade.getAmount());
        tradeInfo.setCustomerCode(trade.getCustomerCode());
        tradeInfo.setSourceBillNo(trade.getSourceBillNo());
        tradeInfo.setRemark(trade.getRemark());
        Logger.info("tenantCommission dealTrade tradeInfo :{}",JSONObject.toJSONString(tradeInfo));
        com.akucun.common.Result<Void> tradeResult = accountCenterService.dealTrade(tradeInfo);
        if (tradeResult == null || !tradeResult.isSuccess()) {
            Logger.error("tenantCommission 调用账户中心 参数:{}, 结果:{}", JSONObject.toJSONString(tradeInfo), JSONObject.toJSONString(tradeResult));
            return false;
        }
        return true;
    }

    /**
     * 店主分佣
     *
     * @param trade
     * @return
     */
    private boolean commonCommission(AccountCommissionTrade trade) {
        Logger.info("commonCommission allocate req:{}", trade);
        try {
            //1.平安账户清分(DG时，账户中心为托管账户，会自动调用平安系统);
        	
            if (!CustomerType.DG.getName().equals(trade.getCustomerType())&&!decouplingConfig.decoupleTenantSwitch(trade.getCustomerCode(), trade.getCustomerType())) {
                CashCreditVO cashCreditVO = new CashCreditVO();
                cashCreditVO.setBillNo(trade.getTradeNo());
                cashCreditVO.setBillType(trade.getTradeType());
                cashCreditVO.setThirdCustId(trade.getCustomerCode());
                //转换成分(四舍五入)
                BigDecimal bd = trade.getAmount().setScale(2, BigDecimal.ROUND_HALF_UP);
                cashCreditVO.setTranAmount(bd.multiply(BigDecimal.valueOf(100)).intValue());
                cashCreditVO.setRemark(trade.getRemark());
                cashCreditVO.setCustomerType(trade.getCustomerType());
                com.akucun.fps.common.entity.Result<String> assetResult = assetsServiceApi.credit(cashCreditVO);
                if (assetResult == null || !assetResult.isSuccess()) {
                    Logger.error("shopkeeperCommission 店主平安账户清分 参数:{}, 结果:{}", JSONObject.toJSONString(cashCreditVO), JSONObject.toJSONString(assetResult));
                    return false;
                }
            }
            //2.账户中心登账
            TradeInfo tradeInfo = new TradeInfo();
            tradeInfo.setAccountTypeKey(accountService.convertAccountTypeKey(trade.getCustomerType()));
            tradeInfo.setTradeNo(trade.getTradeNo());
            tradeInfo.setTradeType(trade.getTradeType());
            tradeInfo.setAmount(trade.getAmount());
            tradeInfo.setCustomerCode(trade.getCustomerCode());
            tradeInfo.setSourceBillNo(trade.getSourceBillNo());
            tradeInfo.setRemark(trade.getRemark());
            //调用账户中心自动分账
            com.akucun.common.Result<Void> tradeResult = accountCenterService.dealTrade(tradeInfo);
            if (tradeResult == null || !tradeResult.isSuccess()) {
                Logger.error("commonCommission 调用账户中心 参数:{}, 结果:{}", JSONObject.toJSONString(tradeInfo), JSONObject.toJSONString(tradeResult));
                return false;
            }
        } catch (Exception e) {
            Logger.warn("commonCommission exception:", e);
            return false;
        }
        return true;
    }

    private boolean decouplingCommission(AccountCommissionTrade trade) {
        Logger.info("decouplingCommission allocate req:{}", trade);
        try {
            //1.账户中心登账
            TradeInfo tradeInfo = new TradeInfo();
            tradeInfo.setAccountTypeKey(accountService.convertAccountTypeKey(trade.getCustomerType()));
            tradeInfo.setTradeNo(trade.getTradeNo());
            tradeInfo.setTradeType(trade.getTradeType());
            tradeInfo.setAmount(trade.getAmount());
            tradeInfo.setCustomerCode(trade.getCustomerCode());
            tradeInfo.setSourceBillNo(trade.getSourceBillNo());
            tradeInfo.setRemark(trade.getRemark());
            //调用账户中心自动分账
            com.akucun.common.Result<Void> tradeResult = accountCenterService.dealTrade(tradeInfo);
            if (tradeResult == null || !tradeResult.isSuccess()) {
                Logger.error("decouplingCommission 调用账户中心 参数:{}, 结果:{}", JSONObject.toJSONString(tradeInfo), JSONObject.toJSONString(tradeResult));
                return false;
            }
        } catch (Exception e) {
            Logger.warn("decouplingCommission exception:", e);
            return false;
        }
        return true;
    }



    /**
     * 店长分佣
     *
     * @param trade
     * @return
     */
    private boolean shopAgentCommission(AccountCommissionTrade trade) {
        Logger.info("shopAgentCommission allocate req:{}", trade);
        boolean result;
        //查询白名单
        try {
            //查询是否是租户
            LambdaQueryWrapper<AccountTenantCustomer> tenantWrapper = new LambdaQueryWrapper<>();
            tenantWrapper.eq(AccountTenantCustomer::getCustomerCode, trade.getCustomerCode())
                    .eq(AccountTenantCustomer::getCustomerType, trade.getCustomerType());
            AccountTenantCustomer accountTenantCustomer = accountTenantCustomerMapper.selectOne(tenantWrapper);

            //针对企业饷店的店主/店长分佣添加拦截判断，如果关联关系不存在则报错
            if (!checkTenantCustomerRelation(trade, accountTenantCustomer)) {
                return false;
            }

            if (accountTenantCustomer != null) {
                Logger.info("租户店长分佣 customer:{}", JSONObject.toJSONString(accountTenantCustomer));
                //租户店主店长分佣
                result = tenantCommission(trade, accountTenantCustomer);
            }else{
                LambdaQueryWrapper<AgentWhiteListAccount> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(AgentWhiteListAccount::getShopId, trade.getShopId())
                        .eq(AgentWhiteListAccount::getStatus, 1);
                AgentWhiteListAccount whiteAccount = agentWhiteListAccountService.getOne(wrapper);
                if (whiteAccount != null) {
                    //白名单店长
                    if (decouplingConfig.decouplingSwitch(trade.getCustomerCode(), trade.getCustomerType())){
                        result = decouplingWhiteAgentCommission(trade, whiteAccount);
                    }else {
                        result = whiteAgentCommission(trade, whiteAccount);
                    }
                } else {
                    //普通店长分佣
                    if (decouplingConfig.decouplingSwitch(trade.getCustomerCode(), trade.getCustomerType())) {
                        result = decouplingAgentCommission(trade);
                    } else {
                        result = normalAgentCommission(trade);
                    }

                }
            }
        } catch (Exception e) {
            Logger.warn("shopAgentCommission Exception:", e);
            return false;
        }

        return result;
    }

    /**
     * 企业饷店分佣时校验租户与店主/店长关系是否存在，不存在则中止分佣
     * @param trade
     * @param accountTenantCustomer
     * @return
     */
    private boolean checkTenantCustomerRelation(AccountCommissionTrade trade, AccountTenantCustomer accountTenantCustomer) {
        if (tenantCommissionRelationCheckSwitch) {
            if (saasTradeTypeList.contains(trade.getTradeType())) {
                if (accountTenantCustomer == null) {
                    Logger.error("租户与店长/店主关联记录不存在，无法执行分佣，customerCode：{}，customerType：{}", trade.getCustomerCode(), trade.getCustomerType());
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 白名单店长分佣
     *
     * @param trade
     * @param whiteAccount
     * @return
     */
    private boolean whiteAgentCommission(AccountCommissionTrade trade, AgentWhiteListAccount whiteAccount) {

        //1.白名单店主平安账户挂账清分
        CashCreditVO cashCreditVO = new CashCreditVO();
        cashCreditVO.setBillNo(trade.getCustomerCode() + trade.getSourceBillNo());
        if (DetailTypeConstants.TRADE_TYPE_265.name().equalsIgnoreCase(trade.getTradeType())
                || DetailTypeConstants.TRADE_TYPE_266.name().equalsIgnoreCase(trade.getTradeType())) {
            //加码防止平安分账幂等（同一个单号，两笔分佣）
            cashCreditVO.setBillNo(trade.getCustomerCode() + trade.getSourceBillNo() + trade.getTradeType().substring(trade.getTradeType().length() - 4));
        }
        //固定交易类型
        cashCreditVO.setBillType(DetailTypeConstants.TRADE_TYPE_043.name());
        //转换成分，（四舍五入）
        BigDecimal bd = trade.getAmount().setScale(2, BigDecimal.ROUND_HALF_UP);
        cashCreditVO.setTranAmount(bd.multiply(BigDecimal.valueOf(100)).intValue());
        //设置备注摘要
        cashCreditVO.setRemark(trade.getRemark());
        //白名单店长分佣是分给其店主账户中
        cashCreditVO.setCustomerType(CustomerType.NM.name());
        cashCreditVO.setThirdCustId(whiteAccount.getCustomerCode());
        com.akucun.fps.common.entity.Result<String> assetResult = assetsServiceApi.credit(cashCreditVO);
        if (assetResult == null || !assetResult.isSuccess()) {
            Logger.error("whiteAgentCommission 店主账户分账 参数 cashCreditVO:{} , 结果 assetResult:{}", JSONObject.toJSONString(cashCreditVO), JSONObject.toJSONString(assetResult));
            return false;
        }
        //2.店主账户中心登账
        TradeInfo tradeInfo = new TradeInfo();
        tradeInfo.setAmount(trade.getAmount());
        tradeInfo.setSourceBillNo(trade.getSourceBillNo());
        tradeInfo.setTradeNo(trade.getCustomerCode() + trade.getSourceBillNo());
        if (DetailTypeConstants.TRADE_TYPE_265.name().equalsIgnoreCase(trade.getTradeType())
                || DetailTypeConstants.TRADE_TYPE_266.name().equalsIgnoreCase(trade.getTradeType())) {
            //加码防止账户中心幂等
            tradeInfo.setTradeNo(trade.getCustomerCode() + trade.getSourceBillNo() + trade.getTradeType().substring(trade.getTradeType().length() - 4));
        }
        tradeInfo.setCustomerCode(whiteAccount.getCustomerCode());
        tradeInfo.setTradeType(DetailTypeConstants.TRADE_TYPE_043.name());
        tradeInfo.setAccountTypeKey(accountService.convertAccountTypeKey(CustomerType.NM.getName()));
        tradeInfo.setRemark("店长服务费");
        com.akucun.common.Result<Void> tradeResult = accountCenterService.dealTrade(tradeInfo);
        if (tradeResult == null || !tradeResult.isSuccess()) {
            Logger.error("whiteAgentCommission 店主分佣调用账户中心 参数:{}, 结果:{}", JSONObject.toJSONString(tradeInfo), JSONObject.toJSONString(tradeResult));
            return false;
        }
        //3.店长账户中心登账
        TradeInfo agentTrade = new TradeInfo();
        BeanUtils.copyProperties(tradeInfo, agentTrade);
        agentTrade.setCustomerCode(trade.getCustomerCode());
        agentTrade.setTradeType(trade.getTradeType());
        agentTrade.setAccountTypeKey(accountService.convertAccountTypeKey(CustomerType.NMDL.getName()));
        agentTrade.setRemark("服务费");
        com.akucun.common.Result<Void> agentResult = accountCenterService.dealTrade(agentTrade);
        if (agentResult == null || !agentResult.isSuccess()) {
            Logger.error("whiteAgentCommission 店长分佣调用账户中心 参数:{} ,结果:{}", JSONObject.toJSONString(agentTrade), JSONObject.toJSONString(agentResult));
            return false;
        }
        return true;
    }

    private boolean decouplingWhiteAgentCommission(AccountCommissionTrade trade, AgentWhiteListAccount whiteAccount) {
        //1.店主账户中心登账
        TradeInfo tradeInfo = new TradeInfo();
        tradeInfo.setAmount(trade.getAmount());
        tradeInfo.setSourceBillNo(trade.getSourceBillNo());
        tradeInfo.setTradeNo(trade.getTradeNo());
        tradeInfo.setTradeType(DetailTypeConstants.TRADE_TYPE_043.name());
        if (DetailTypeConstants.TRADE_TYPE_265.name().equalsIgnoreCase(trade.getTradeType())) {
            tradeInfo.setTradeType(DetailTypeConstants.TRADE_TYPE_467.name());
        } else if (DetailTypeConstants.TRADE_TYPE_266.name().equalsIgnoreCase(trade.getTradeType())) {
            tradeInfo.setTradeType(DetailTypeConstants.TRADE_TYPE_468.name());
        } else if (DetailTypeConstants.TRADE_TYPE_269.name().equalsIgnoreCase(trade.getTradeType())) {
            tradeInfo.setTradeType(DetailTypeConstants.TRADE_TYPE_470.name());
        }
        tradeInfo.setCustomerCode(whiteAccount.getCustomerCode());
        tradeInfo.setAccountTypeKey(AccountKeyConstants.NM.getName());
        tradeInfo.setRemark("店长贡献 customerCode:"+trade.getCustomerCode());
        com.akucun.common.Result<Void> tradeResult = accountCenterService.dealTrade(tradeInfo);
        if (tradeResult == null || !tradeResult.isSuccess()) {
            Logger.error("decouplingWhiteAgentCommission 店主分佣调用账户中心 参数:{}, 结果:{}", JSONObject.toJSONString(tradeInfo), JSONObject.toJSONString(tradeResult));
            return false;
        }
        //3.店长账户中心登账
        TradeInfo agentTrade = new TradeInfo();
        BeanUtils.copyProperties(tradeInfo, agentTrade);
        agentTrade.setCustomerCode(trade.getCustomerCode());
        agentTrade.setTradeType(trade.getTradeType());
        if (DetailTypeConstants.TRADE_TYPE_265.name().equalsIgnoreCase(trade.getTradeType())){
            agentTrade.setTradeType(DetailTypeConstants.TRADE_TYPE_274.name());
        }else if (DetailTypeConstants.TRADE_TYPE_266.name().equalsIgnoreCase(trade.getTradeType())){
            agentTrade.setTradeType(DetailTypeConstants.TRADE_TYPE_275.name());
        }else if (DetailTypeConstants.TRADE_TYPE_269.name().equalsIgnoreCase(trade.getTradeType())) {
            agentTrade.setTradeType(DetailTypeConstants.TRADE_TYPE_278.name());
        }
        agentTrade.setAccountTypeKey(AccountKeyConstants.NMDL.getName());
        agentTrade.setRemark(trade.getRemark());
        com.akucun.common.Result<Void> agentResult = accountCenterService.dealTrade(agentTrade);
        if (agentResult == null || !agentResult.isSuccess()) {
            Logger.error("decouplingWhiteAgentCommission 店长分佣调用账户中心 参数:{} ,结果:{}", JSONObject.toJSONString(agentTrade), JSONObject.toJSONString(agentResult));
            return false;
        }
        return true;

    }

    /**
     * 普通店长
     *
     * @param trade
     * @return
     */
    private boolean normalAgentCommission(AccountCommissionTrade trade) {
        //1.平安账户挂账清分
        CashCreditVO cashCreditVO = new CashCreditVO();
        cashCreditVO.setThirdCustId(trade.getCustomerCode());
        //设置代理客户类型
        cashCreditVO.setCustomerType(CustomerType.NMDL.name());
        cashCreditVO.setBillNo(trade.getCustomerCode() + trade.getSourceBillNo());
        cashCreditVO.setBillType(trade.getTradeType());
        if (DetailTypeConstants.TRADE_TYPE_265.name().equalsIgnoreCase(trade.getTradeType())
                || DetailTypeConstants.TRADE_TYPE_266.name().equalsIgnoreCase(trade.getTradeType())) {
            //加码防止平安分账幂等
            cashCreditVO.setBillNo(trade.getCustomerCode() + trade.getSourceBillNo() + trade.getTradeType().substring(trade.getTradeType().length() - 4));
        }
        //转换成分，（四舍五入）
        BigDecimal bd = trade.getAmount().setScale(2, BigDecimal.ROUND_HALF_UP);
        cashCreditVO.setTranAmount(bd.multiply(BigDecimal.valueOf(100)).intValue());
        //设置备注摘要
        cashCreditVO.setRemark(trade.getRemark());
        com.akucun.fps.common.entity.Result<String> assetResult = assetsServiceApi.credit(cashCreditVO);
        if (assetResult == null || !assetResult.isSuccess()) {
            Logger.error("normalAgentCommission 店长平安账户分账 参数:{} ,结果 assetResult:{}", JSONObject.toJSONString(cashCreditVO), JSONObject.toJSONString(assetResult));
            return false;
        }

        //2.账户中心分佣登账
        TradeInfo tradeInfo = new TradeInfo();
        tradeInfo.setAccountTypeKey(AccountKeyConstants.NMDL.getName());
        tradeInfo.setAmount(trade.getAmount());
        tradeInfo.setCustomerCode(trade.getCustomerCode());
        tradeInfo.setRemark(trade.getRemark());
        tradeInfo.setSourceBillNo(trade.getSourceBillNo());
        tradeInfo.setTradeNo(trade.getTradeNo());
        tradeInfo.setTradeType(trade.getTradeType());
        if (DetailTypeConstants.TRADE_TYPE_265.name().equalsIgnoreCase(trade.getTradeType())
                || DetailTypeConstants.TRADE_TYPE_266.name().equalsIgnoreCase(trade.getTradeType())) {
            //加码防止账户中心幂等
            tradeInfo.setTradeNo(trade.getCustomerCode() + trade.getSourceBillNo() + trade.getTradeType().substring(trade.getTradeType().length() - 4));
        }
        com.akucun.common.Result<Void> tradeResult = accountCenterService.dealTrade(tradeInfo);
        if (tradeResult == null || !tradeResult.isSuccess()) {
            Logger.error("normalAgentCommission 店长分佣请求账户中心 参数:{}, 结果:{}", JSONObject.toJSONString(tradeInfo), JSONObject.toJSONString(tradeResult));
            return false;
        }
        return true;

    }

    /**
     * 普通店长
     *
     * @param trade
     * @return
     */
    private boolean decouplingAgentCommission(AccountCommissionTrade trade) {
        //1.账户中心分佣登账
        TradeInfo tradeInfo = new TradeInfo();
        tradeInfo.setAccountTypeKey(AccountKeyConstants.NMDL.getName());
        tradeInfo.setAmount(trade.getAmount());
        tradeInfo.setCustomerCode(trade.getCustomerCode());
        tradeInfo.setRemark(trade.getRemark());
        tradeInfo.setSourceBillNo(trade.getSourceBillNo());
        tradeInfo.setTradeNo(trade.getTradeNo());
        tradeInfo.setTradeType(trade.getTradeType());
        com.akucun.common.Result<Void> tradeResult = accountCenterService.dealTrade(tradeInfo);
        if (tradeResult == null || !tradeResult.isSuccess()) {
            Logger.error("decouplingAgentCommission 店长分佣请求账户中心 参数:{}, 结果:{}", JSONObject.toJSONString(tradeInfo), JSONObject.toJSONString(tradeResult));
            return false;
        }
        return true;

    }

    /**
     * 非分佣交易
     * @param trade
     * @return
     */
    private boolean nonCommissionTrade(AccountCommissionTrade trade) {
        return decouplingCommission(trade);
    }

}
