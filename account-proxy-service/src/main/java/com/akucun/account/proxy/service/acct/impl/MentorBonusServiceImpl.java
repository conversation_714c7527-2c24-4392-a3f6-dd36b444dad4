package com.akucun.account.proxy.service.acct.impl;

import com.akucun.account.proxy.dao.model.MentorInfo;
import com.akucun.account.proxy.facade.stub.enums.RewardTypeEnum;
import com.akucun.account.proxy.facade.stub.others.dto.req.BatchBonusPayReq;
import com.akucun.account.proxy.service.acct.MentorInfoService;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : 导师激励-业务处理
 * @Create on : 2025/1/15 14:20
 **/
@Service
public class MentorBonusServiceImpl extends AbstractPromoTradeService {
    static final RewardTypeEnum busiType = RewardTypeEnum.MENTOR_BONUS;
    static final String FJ = "fj";//附件
    static final String MX = "mx";//明细
    static final String FP = "fp";//发票

    @Autowired
    private MentorInfoService mentorInfoService;


    /**
     * 预校验
     * @param batchBonusPayReq
     * @return
     */
    @Override
    Pair<Boolean, String> preCheck(BatchBonusPayReq batchBonusPayReq) {
        if(ObjectUtils.isEmpty(batchBonusPayReq.getExt1())){
            return Pair.of(false,"月勤奖业务，附件信息(明细excel等)不能为空");
        }

        //01-发票不能为空
        /*if(!batchBonusPayReq.getExt1().containsKey(FP)){
            return Pair.of(false,"月勤奖业务，发票的附件不能为空");
        }*/
        //02-明细不能为空
        if(!batchBonusPayReq.getExt1().containsKey(MX)){
            return Pair.of(false,"月勤奖业务，明细excel的附件不能为空");
        }
        //03-导师是否存在
        List<MentorInfo> mentorInfos = mentorInfoService.loadAll();
        Map<String,MentorInfo> mentorInfoMap =mentorInfos.stream().collect(Collectors.toMap(MentorInfo::getUserCode, v->v, (v1, v2) -> v1));
        List<String> errorUserCodes = new ArrayList<>();
        batchBonusPayReq.getDetails().stream().forEach(detail->{
            if(!mentorInfoMap.containsKey(detail.getUserCode())){
                errorUserCodes.add(detail.getUserCode());
            }
        });
        if(!CollectionUtils.isEmpty(errorUserCodes)){
            return Pair.of(false,"如下导师没有初始化:"+errorUserCodes.toString());
        }

        return Pair.of(true,"suss");
    }

    //=========== 获取当前业务类别 ==============
    @Override
    public RewardTypeEnum getBusiType() {
        return busiType;
    }



}
