package com.akucun.account.proxy.service.handler.pingan;

import com.aikucun.common2.utils.datamasking.DataMask;
import com.akucun.account.proxy.common.constant.CommonConstants;
import com.akucun.account.proxy.common.enums.ResponseEnum;
import com.akucun.account.proxy.common.enums.ResultStatus;
import com.akucun.account.proxy.common.exception.AccountProxyException;
import com.akucun.account.proxy.common.utils.AccountUtils;
import com.akucun.account.proxy.service.acct.AccountService;
import com.akucun.account.proxy.service.common.bo.AccountExecStepContext;
import com.akucun.account.proxy.service.common.bo.AccountReq;
import com.akucun.account.proxy.service.common.bo.AccountResp;
import com.akucun.account.proxy.service.common.step.AbstractHandler;
import com.akucun.fps.common.entity.Result;
import com.akucun.fps.pingan.client.model.po.PinganAccount;
import com.akucun.fps.pingan.client.vo.AccountRegisterVO;
import com.akucun.fps.pingan.feign.api.merchant.MerchantServiceApi;
import com.alibaba.fastjson.JSON;
import com.mengxiang.base.common.log.Logger;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @Author: silei
 * @Date: 2020/9/3
 * @desc: 平安账户查询
 */
@Component
public class AcctQueryHandler extends AbstractHandler {

    @Resource
    private MerchantServiceApi merchantServiceApi;

    @Autowired
    private AccountService accountService;

    @Override
    protected boolean preCheck(AccountExecStepContext accountExecStepContext) {
        AccountReq req = accountExecStepContext.getAccountReq();
        AccountResp resp = accountExecStepContext.getAccountResp();
        //参数校验
        if (StringUtils.isBlank(req.getCustomerCode())) {
            resp.setStatus(ResultStatus.F.getCode());
            resp.setReplyMsg("用户客户编码为空");
        }
        if (StringUtils.isBlank(req.getCustomerType())) {
            resp.setStatus(ResultStatus.F.getCode());
            resp.setReplyMsg("用户客户类型为空");
        }
        return !ResultStatus.F.getCode().equals(resp.getStatus());
    }

    @Override
    protected void doSubmitBefore(AccountExecStepContext accountExecStepContext) {
        AccountReq req = accountExecStepContext.getAccountReq();
        AccountResp resp = accountExecStepContext.getAccountResp();
        if(!accountService.isTenantCustomer(req.getCustomerCode(), req.getCustomerType())) {//饷店账户在绑卡时再开通
            resp.setStatus(ResultStatus.S.getCode());
            resp.setReplyCode(CommonConstants.SUCC_CODE);
            resp.setReplyMsg(ResultStatus.S.getDesc());
            return;
        }
        try {
            Logger.info("AcctQueryHandler doSubmitBefore req:{}", DataMask.toJSONString(req));
            //店主编码需要拼接
            AccountRegisterVO vo = new AccountRegisterVO();
            vo.setCustomerCode(AccountUtils.getSellerCode(req.getCustomerCode(), req.getCustomerType()));
            vo.setCustomerType(req.getCustomerType());
            Result<PinganAccount> result = merchantServiceApi.selectPinganAccount(vo);
            if (Objects.nonNull(result) && result.isSuccess() && Objects.nonNull(result.getData())){
                resp.setStatus(ResultStatus.S.getCode());
                resp.setReplyCode(CommonConstants.SUCC_CODE);
                resp.setReplyMsg(ResultStatus.S.getDesc());
            }
        } catch (Exception e) {
            Logger.info("AcctQueryHandler doSubmitBefore error", e);
            throw new AccountProxyException(ResponseEnum.SUBMIT_EXCEPTION);
        }
    }

    @Override
    protected void doSubmit(AccountExecStepContext accountExecStepContext) {

    }

    @Override
    protected void doSubmitAfter(AccountExecStepContext accountExecStepContext) throws Exception {

    }
}
