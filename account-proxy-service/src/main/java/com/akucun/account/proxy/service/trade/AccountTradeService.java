package com.akucun.account.proxy.service.trade;

import com.akucun.account.proxy.dao.model.AccountTrade;
import com.akucun.account.proxy.facade.stub.others.dto.req.AccountTradeRequest;
import com.akucun.account.proxy.facade.stub.others.dto.res.AccountTradeResponse;
import com.akucun.account.proxy.service.common.bo.AccountExecStepContext;
import com.akucun.account.proxy.service.trade.bo.AccountTradeResp;
import com.baomidou.mybatisplus.extension.service.IService;

public interface AccountTradeService extends IService<AccountTrade> {

    AccountTradeResp accountTrade(AccountTradeRequest request, String tradeType);

    AccountTradeResponse bonusAccountQuery(String tradeNo, String tradeType);

    AccountTradeResponse akcAccountQuery(String tradeNo, String tradeType);

    AccountTradeResponse openApiAccountQuery(String tradeNo, String tradeType);

    AccountTradeResponse xdAccountQuery(String tradeNo, String tradeType);

    AccountTradeResponse pointAccountQuery(String tradeNo, String tradeType);
}
