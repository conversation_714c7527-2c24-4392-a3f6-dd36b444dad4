package com.akucun.account.proxy.service.postaction;

import com.akucun.account.proxy.dao.model.PostActionItem;
import com.akucun.account.proxy.service.postaction.bo.PostActionItemBO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * @Author: silei
 * @Date: 2020/12/8
 * @desc: 异步任务service
 */
public interface PostActionService extends IService<PostActionItem> {

    /**
     * 添加异步任务
     *
     * @param itemBO
     */
    void addAction(PostActionItemBO itemBO);

    /**
     * 查询异步任务
     *
     * @param createTime
     * @param batchNum
     * @param total
     * @param index
     * @param actionType
     * @param maxRetryTimes
     * @return
     */
    List<PostActionItem> selectPage(String createTime, int batchNum, int total, int index, String actionType, int maxRetryTimes);


    /**
     * 处理任务
     * @param item
     */
    void processAction(PostActionItem item);

    /**
     * 根据id查询任务
     * @param start
     * @param end
     * @param retryTimes
     * @param actionType
     * @return
     */
    List<PostActionItem> selectByIndex(long start, long end, int retryTimes, String actionType);

    /**
     * 清除历史异步任务
     * @param clearDate
     */
    void clearHistoryPostAction(String clearDate);
    /**
     * 清除历史成功执行异步任务
     * @param clearDate
     */
    void clearHistorySuccessTask(String clearDate);

    /**
     * 更新异步任务参数
     *
     * @param id
     * @param param
     */
    void updateParamById(Long id, String param);

    /**
     * 查询异步任务, 下次执行时间小于当前时间
     *
     * @param createTime
     * @param batchNum
     * @param total
     * @param index
     * @param actionType
     * @param maxRetryTimes
     * @param nextRetryTime
     * @return
     */
    List<PostActionItem> selectPageByNextRetryTime(String createTime, int batchNum, int total, int index, String actionType, int maxRetryTimes, String nextRetryTime);
}
