package com.akucun.account.proxy.service.postaction.task;

import com.akucun.account.proxy.service.postaction.PostActionService;
import com.mengxiang.base.common.log.Logger;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * @Author: silei
 * @Date: 2020/12/9
 * @desc: 清除历史异步任务job
 */
@Component
public class ClearPostActionTask {

    @Autowired
    private PostActionService postActionService;

    @Value("${clear.day.off:-7}")
    private int clearDayOff;

    @XxlJob("clearPostActionTask")
    public ReturnT<String> execute(String param) {
        Logger.info("clearPostActionTask start! param:{}", param);
        String clearDate = LocalDate.now().plusDays(clearDayOff).format(DateTimeFormatter.ofPattern("yyyy-MM-dd 00:00:00"));
        if (StringUtils.isNotEmpty(param)) {
            postActionService.clearHistoryPostAction(param);
        }else {
            postActionService.clearHistorySuccessTask(clearDate);
        }
        Logger.info("clearPostActionTask finished!");
        return ReturnT.SUCCESS;
    }

}
