package com.akucun.account.proxy.service.common.step;

import com.akucun.account.proxy.service.common.bo.AccountExecContext;
import com.mengxiang.base.common.log.Logger;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: silei
 * @Date: 2020/8/26
 * @desc:
 */
@Data
public abstract class Step {

    private String tradeType;
    //最细粒度的交易执行类型
    private String subTradeType;
    //此流程的下一个执行步骤分支
    private Map<String, String> nextStepMap = new HashMap();

    private StepAdapter stepAdapter;
    //扩展字段
    private Map<String, String> extMap = new HashMap();

    protected abstract boolean submitBefore(AccountExecContext accountExecContext);

    protected abstract void submit(AccountExecContext accountExecContext);

    protected abstract void submitAfter(AccountExecContext accountExecContext);

    public void exec(AccountExecContext accountExecContext) {
        Logger.info("Step.exec() step->:{}", this);
        try {
            boolean isContinue = submitBefore(accountExecContext);
            Logger.info("入参 AccountExecStepContext ->:{}", accountExecContext.getLatestAccountStepExecContext());
            if (isContinue) {
                submit(accountExecContext);
                submitAfter(accountExecContext);
            }
        } catch (Exception e) {
            accountExecContext.setException(e);
            Logger.error("Step abstract exec exception", e);
        }
    }

    public String getDetailOrderNo(String detailOrderNoPre, String suffix) {
        return getStepAdapter().getExecHandler().getDetailOrderNo(detailOrderNoPre, suffix);
    }

}
