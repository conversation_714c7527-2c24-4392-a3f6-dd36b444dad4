package com.akucun.account.proxy.service.acct;

import com.akucun.account.proxy.dao.model.RewardApply;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : 奖励申请
 * @Create on : 2025/1/15 13:46
 **/
public interface RewardApplyService {
    List<RewardApply> queryRewardApplyList(String requestNo, String busiType, String transBillDate, String activityNo);

    List<RewardApply> queryRewardApplyRecords(String requestNo, String busiType, String transBillDate, String userCode, String userType);

    List<RewardApply> queryByBatchNo(String batchNo);

    Integer batchSave(List<RewardApply> records);

    Integer saveRecord(RewardApply record);

    Integer updatesStatusById(RewardApply record);

    RewardApply load(Long id);
}
