package com.akucun.account.proxy.service.transfer.convert;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import com.aikucun.common2.utils.JsonUtils;
import com.akucun.account.proxy.dao.model.TenantWithdrawApply;
import com.akucun.account.proxy.dao.model.WithdrawApplyRecord;
import com.akucun.account.proxy.dao.model.WithdrawTaxDetail;
import com.akucun.account.proxy.task.request.EmployeeWithdrawBizInfo;
import com.akucun.account.proxy.task.request.FinTaskAcceptRequest;
import com.akucun.account.proxy.task.request.TenantWithdrawBizInfo;
import com.akucun.fps.common.excel.JSONUtils;
import com.akucun.fps.common.util.UUIDUtils;
import com.alibaba.fastjson.JSON;

public class FinTaskAcceptRequestConvert {
	
	/**
	 * 租户店主店长提现请求
	 */
	private static String EMPLOYEE_PLATFORM ="EMPLOYEE_PLATFORM";
	
	/**
	 * 租户提现请求
	 */
	private static String TENEMENT_PLATFORM = "TENEMENT_PLATFORM";
	
	/**
	 * 平安提现业务类别
	 */
	private static String PINGAN_WITHDRAW = "PINGAN_WITHDRAW";
	
	/**
	 * 租户【店长、店长提现】请求描述
	 */
	private static String EMPLOYEE_MEMO="租户店长/店长提现";
	
	/**
	 * 租户请求描述
	 */
	private static String TENEMENT_MEMO="租户提现";
	
	/**
	 * 来源
	 */
	private static String SOURCE = "SAAS";
	
	/**
	 * 用户类型
	 */
	private static String customerCode = "customerCode";
	
	/**
	 * 用户类型
	 */
	private static String customerType = "customerType";
	
	/**
	 * 用户名称
	 */
	private static String customerName = "customerName";
	
	/**
	 * 提现业务号
	 */
	private static String withdrawNo = "withdrawNo";
	
	/**
	 * 金额
	 */
	private static String amount = "amount";
	
	/**
	 * 银行卡号
	 */
	private static String bankNo = "customerCode";
	
	/**
	 * 构建租户【店长、店长】提现实体->>清分核心
	 * @param apply
	 * @return
	 * @throws IOException 
	 */
	public static FinTaskAcceptRequest employeeWithdrawFinTaskAcceptRequest(TenantWithdrawApply apply, WithdrawTaxDetail taxDetail,String bankNo) throws IOException {
		FinTaskAcceptRequest finTaskAcceptRequest = new FinTaskAcceptRequest();
		finTaskAcceptRequest.setRequestNo(apply.getCustomerType()+UUIDUtils.getUUID());
		finTaskAcceptRequest.setRequestPlatform(EMPLOYEE_PLATFORM);
		finTaskAcceptRequest.setCreateUserType(apply.getCustomerType());
		finTaskAcceptRequest.setCreateUserId(apply.getCustomerCode());
		finTaskAcceptRequest.setBizCategory(PINGAN_WITHDRAW);
		finTaskAcceptRequest.setBizNo(apply.getWithdrawNo());
		finTaskAcceptRequest.setSource(SOURCE);
		finTaskAcceptRequest.setMemo(EMPLOYEE_MEMO);
		Map<String, String> bizInfo = JsonUtils.toMap(JSON.toJSONString(new EmployeeWithdrawBizInfo(apply,taxDetail,bankNo)));
		finTaskAcceptRequest.setBizInfo(bizInfo);
		finTaskAcceptRequest.setExt(new HashMap<>());
		return finTaskAcceptRequest;
	}
	
	/**
	 * 构建租户提现实体->>清分核心
	 * @param apply
	 * @return
	 * @throws IOException
	 */
	public static FinTaskAcceptRequest tenantWithdrawFinTaskAcceptRequest(WithdrawApplyRecord apply,String bankNo) throws IOException {
		FinTaskAcceptRequest finTaskAcceptRequest = new FinTaskAcceptRequest();
		finTaskAcceptRequest.setRequestNo(apply.getCustomerType()+UUIDUtils.getUUID());
		finTaskAcceptRequest.setRequestPlatform(TENEMENT_PLATFORM);
		finTaskAcceptRequest.setCreateUserType(apply.getCustomerType());
		finTaskAcceptRequest.setCreateUserId(apply.getCustomerCode());
		finTaskAcceptRequest.setBizCategory(PINGAN_WITHDRAW);
		finTaskAcceptRequest.setBizNo(apply.getWithdrawNo());
		finTaskAcceptRequest.setSource(SOURCE);
		finTaskAcceptRequest.setMemo(TENEMENT_MEMO);
		Map<String, String> bizInfo = JsonUtils.toMap(JSON.toJSONString(new TenantWithdrawBizInfo(apply,bankNo)));
		finTaskAcceptRequest.setBizInfo(bizInfo);
		finTaskAcceptRequest.setExt(new HashMap<>());
		return finTaskAcceptRequest;
	}

}
