package com.akucun.account.proxy.service.handler.trade;

import com.aikucun.common2.utils.StringUtils;
import com.akucun.account.proxy.client.account.AccountClient;
import com.akucun.account.proxy.common.constant.CommonConstants;
import com.akucun.account.proxy.common.enums.ResponseEnum;
import com.akucun.account.proxy.common.enums.ResultStatus;
import com.akucun.account.proxy.common.exception.AccountProxyException;
import com.akucun.account.proxy.facade.stub.enums.TradeChannel;
import com.akucun.account.proxy.service.common.bo.AccountExecStepContext;
import com.akucun.account.proxy.service.common.bo.AccountResp;
import com.akucun.account.proxy.service.common.bo.AccountTradeReq;
import com.akucun.account.proxy.service.common.step.AbstractHandler;
import com.akucun.account.proxy.service.trade.AccountTradeService;
import com.mengxiang.base.common.log.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @date 2020/12/21 16:45
 */
@Component
public class OpenApiAcctQueryHandler extends AbstractHandler {

    @Autowired
    private AccountTradeService accountTradeService;
    @Autowired
    private AccountClient accountClient;
    @Autowired
    private OpenApiAcctTradeHandler openApiAcctTradeHandler;
    @Autowired
    private OpenApiAcctRefundHandler openApiAcctRefundHandler;

    @Override
    protected boolean preCheck(AccountExecStepContext accountExecStepContext) {
        return true;
    }

    @Override
    protected void doSubmitBefore(AccountExecStepContext accountExecStepContext) {

    }

    @Override
    protected void doSubmit(AccountExecStepContext accountExecStepContext) {
        AccountTradeReq req = accountExecStepContext.getAccountTradeReq();
        AccountResp resp = accountExecStepContext.getAccountResp();
        try {
            String channle = req.getChannel();
            String accountKey = null;
            if (TradeChannel.OPENAPI.getCode().equals(channle)) {
                accountKey = CommonConstants.OPENAPI_CREDIT_CUSTOMER_KEY;
            }
            boolean isSuccess = false;
            if(!StringUtils.isNullOrEmpty(accountKey)) {
                // 查询账户交易记录
                isSuccess = accountClient.queryAccountDetailList(req.getCustomerCode(), req.getTradeNo(), req.getAmount(), accountKey);
            }
            if(isSuccess) {
                resp.setStatus(ResultStatus.S.getCode());
                resp.setReplyCode(CommonConstants.SUCC_CODE);
                resp.setReplyMsg(ResultStatus.S.getDesc());
            }  else {
                String tradeType = req.getTradeType();
                if(CommonConstants.OPENAPI_ACCOUNT_PAY_TRADE_TYPE.equals(tradeType)) {
                    // 如果查询不到支付数据，则重新调用支付
                    openApiAcctTradeHandler.exec(accountExecStepContext);
                } else if(CommonConstants.OPENAPI_ACCOUNT_REFUND_TRADE_TYPE.equals(tradeType)) {
                    // 如果查询不到退款数据，则重新退款
                    openApiAcctRefundHandler.exec(accountExecStepContext);
                }
            }
        } catch (Exception e) {
            Logger.info("AkcAcctQueryHandler doSubmit error", e);
            throw new AccountProxyException(ResponseEnum.SUBMIT_EXCEPTION);
        }
    }

    @Override
    protected void doSubmitAfter(AccountExecStepContext accountExecStepContext) throws Exception {

    }
}
