package com.akucun.account.proxy.service.acct.job;

import com.aikucun.common2.utils.datamasking.DataMask;
import com.akucun.account.center.client.model.TradeInfo;
import com.akucun.account.center.feign.AccountCenterService;
import com.akucun.account.proxy.common.constant.CommonConstants;
import com.akucun.account.proxy.common.enums.VipCardEnum;
import com.akucun.account.proxy.common.enums.VipCardEnum.VipCardStatus;
import com.akucun.account.proxy.common.enums.VipCardEnum.VipCardType;
import com.akucun.account.proxy.common.utils.AESUtils;
import com.akucun.account.proxy.dao.model.MshopVipCard;
import com.akucun.account.proxy.facade.stub.enums.DetailTypeConstants;
import com.akucun.account.proxy.service.acct.VipCardService;
import com.akucun.common.Result;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mengxiang.base.common.log.Logger;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2021/01/03 16:56
 */
@Component
public class VipCardBonusTradeJob {

    @Autowired
    private VipCardService vipCardService;
    @Resource
    private AccountCenterService accountCenterService;

    public static final String BONUS_TRADE_REMARK = "金饷VIP客户";

    @XxlJob("vipCardBonusTradeJob")
    public ReturnT<String> execute(String s) throws Exception {
        Logger.info("vip卡发放奖励金任务开始....{}", s);
        List<MshopVipCard> vipCards = null;
        int count = 0;
        do {
            // 查询未发放奖励金的vip卡记录
            vipCards = queryMshopVipCard();
            if (CollectionUtils.isEmpty(vipCards)) {
                break;
            }
            // 循环发放奖励金
            for (MshopVipCard vipCard : vipCards) {
                try {
                    Result result = bonusTrade(vipCard);
                    if (result.isSuccess()) {
                        vipCard.setStatus(VipCardEnum.VipCardStatus.STATUS_30.getCode());
                        vipCard.setVerificationTime(new Date());
                        vipCardService.updateById(vipCard);
                    } else {
                        Logger.warn("奖励金发放错误，参数：{}，错误信息：{}", DataMask.toJSONString(vipCard), result.getMessage());
                    }
                } catch (Exception e) {
                    Logger.error("奖励金发放异常，参数：{}，异常信息：{}", DataMask.toJSONString(vipCard), e);
                }
            }
            count += vipCards.size();
        } while (CollectionUtils.isNotEmpty(vipCards));
        Logger.info("vip卡发放奖励金任务结束，共处理数据{}条", count);
        return ReturnT.SUCCESS;
    }

    private List<MshopVipCard> queryMshopVipCard() {
        long pageNum = 1;
        // 每次取500条
        long pageSize = 500;
        Page<MshopVipCard> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<MshopVipCard> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MshopVipCard::getIsDelete, 0)
                .in(MshopVipCard::getStatus, Arrays.asList(VipCardStatus.STATUS_20.getCode(), VipCardStatus.STATUS_40.getCode()))
                // 只查询发放奖励金的数据
                .eq(MshopVipCard::getCardType, VipCardType.TYPE_BONUS.getCode())
                .orderByAsc(MshopVipCard::getId);
        Page<MshopVipCard> vipCardPage = vipCardService.page(page, queryWrapper);
        return vipCardPage.getRecords();
    }

    /**
     * 奖励金发放
     */
    private Result bonusTrade(MshopVipCard mshopVipCard) {
        TradeInfo tradeInfo = new TradeInfo();
        tradeInfo.setAccountTypeKey(CommonConstants.NEW_BALANCE_CUSTOMER_KEY);
        tradeInfo.setCustomerCode(mshopVipCard.getCustomerCode());
        tradeInfo.setTradeType(DetailTypeConstants.TRADE_TYPE_005.getName());
        tradeInfo.setAmount(new BigDecimal(mshopVipCard.getAmount()).divide(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP));
        tradeInfo.setTradeNo(AESUtils.decrypt(mshopVipCard.getInviteCode(), CommonConstants.VIP_CARD_AES_KEY));
        tradeInfo.setSourceBillNo(mshopVipCard.getCardNo());
        tradeInfo.setRemark(BONUS_TRADE_REMARK);
        tradeInfo.setSourceSystem("ACCOUNT");
        Logger.info("发放奖励金请求参数：{}", DataMask.toJSONString(tradeInfo));
        Result<Void> result = accountCenterService.dealTrade(tradeInfo);
        Logger.info("发放奖励金返回参数：{}，请求参数：{}", DataMask.toJSONString(result), DataMask.toJSONString(tradeInfo));
        return result;
    }

}
