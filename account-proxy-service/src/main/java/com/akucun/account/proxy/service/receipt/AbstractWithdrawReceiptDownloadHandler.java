package com.akucun.account.proxy.service.receipt;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.common.constant.WithdrawChannelConstants;
import com.akucun.account.proxy.service.receipt.bo.WithdrawReceiptDownloadBO;

public abstract class AbstractWithdrawReceiptDownloadHandler {

    protected WithdrawReceiptDownloadBO bo;

    public AbstractWithdrawReceiptDownloadHandler(WithdrawReceiptDownloadBO bo) {
        this.bo = bo;
    }

    public abstract Result<Void> handle();

    public static AbstractWithdrawReceiptDownloadHandler getHandler(WithdrawReceiptDownloadBO bo) {
        if (WithdrawChannelConstants.PINGAN.getName().equals(bo.getWithdrawChannel())) {
            return new PingAnWithdrawReceiptDownloadHandler(bo);
        } else if (WithdrawChannelConstants.WECHAT.getName().equals(bo.getWithdrawChannel())) {
            return new WechatWithdrawReceiptDownloadHandler(bo);
        } else {
            throw new IllegalArgumentException("Invalid withdraw channel: " + bo.getWithdrawChannel());
        }
    }

}
