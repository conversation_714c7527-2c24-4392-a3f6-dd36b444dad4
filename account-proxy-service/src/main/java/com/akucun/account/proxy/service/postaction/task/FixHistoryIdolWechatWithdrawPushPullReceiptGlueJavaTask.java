/*
 * @Author: Lee
 * @Date: 2025-03-28 17:00:20
 * @Description: Do not edit
 * @License: Copyright (c) 2025, Lee
 */
package com.akucun.account.proxy.service.postaction.task;

import com.aikucun.common2.log.Logger;
import com.akucun.account.proxy.common.constant.Constant;
import com.akucun.account.proxy.common.constant.WithdrawChannelConstants;
import com.akucun.account.proxy.common.enums.PostActionExecStatus;
import com.akucun.account.proxy.common.enums.PostActionTypes;
import com.akucun.account.proxy.dao.mapper.PaymentTransferMapper;
import com.akucun.account.proxy.dao.mapper.PostActionItemMapper;
import com.akucun.account.proxy.dao.mapper.WithdrawApplyRecordMapper;
import com.akucun.account.proxy.dao.model.PaymentTransfer;
import com.akucun.account.proxy.dao.model.PostActionItem;
import com.akucun.account.proxy.dao.model.WithdrawApplyRecord;
import com.akucun.account.proxy.service.acct.AccountWithdrawService;
import com.akucun.account.proxy.service.common.SpringContextHolder;
import com.akucun.account.proxy.service.postaction.PostActionService;
import com.akucun.account.proxy.service.postaction.bo.PostActionItemBO;
import com.akucun.account.proxy.service.receipt.bo.WithdrawReceiptDownloadBO;
import com.akucun.account.proxy.service.transfer.PaymentTransferService;
import com.akucun.bcs.channel.facade.stub.dto.WechatWithdrawRequest;
import com.akucun.fps.common.util.GsonUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.log.XxlJobLogger;

import cn.hutool.core.util.RandomUtil;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Random;

import javax.annotation.Resource;

/**
 * @Description
 * @aurhor lee
 * @date: 2022/9/13 14:34
 */
public class FixHistoryIdolWechatWithdrawPushPullReceiptGlueJavaTask extends IJobHandler {

    @Resource
    private WithdrawApplyRecordMapper withdrawApplyRecordMapper;

    @Resource
    private PaymentTransferMapper paymentTransferMapper;

    @Resource
    private PaymentTransferService paymentTransferService;

    @Resource
    private PostActionItemMapper postActionItemMapper;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        Logger.info("FixHistoryIdolWHistoryIdolhdrawPushPullRPushPullReceipteceiptGlueJavaTask执行参数：{}", param);
        if (StringUtils.isBlank(param)) {
            Logger.info("FixHistoryIdolWechatWithdrawPushPullReceiptGlueJavaTask执行异常：参数不能为空");
            return ReturnT.SUCCESS;
        }
        String[] params = param.split(",");
        String startDate = params[0];
        String endDate = params[1];
        /**
         * SELECT * FROM withdraw_apply_record WHERE create_time >= '2025-01-01' and create_time < '2025-04-01' 
         * and withdraw_channel = 'WECHAT' and apply_status = 'SUCC' 
         * and customer_type not in ('SH_WC','SS_WC');
         */
        QueryWrapper<WithdrawApplyRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("withdraw_channel", "WECHAT");
        queryWrapper.eq("apply_status", "SUCC");
        queryWrapper.notIn("customer_type", Arrays.asList("SH_WC","SS_WC"));
        queryWrapper.ge("create_time", startDate);
        queryWrapper.lt("create_time", endDate);
        //只返回withdrawNo, batchNo, customerType
        queryWrapper.select("withdraw_no", "batch_no", "customer_type");
        List<WithdrawApplyRecord> list = withdrawApplyRecordMapper.selectList(queryWrapper); 

        XxlJobLogger.log("FixHistoryIdolWechatWithdrawPushPullReceiptGlueJavaTask执行: {}, 共{}条数据", param, list.size());
        XxlJobLogger.log("FixHistoryIdolWechatWithdrawPushPullReceiptGlueJavaTask执行样例：{}", JSONObject.toJSONString(list.get(0)));

        for (WithdrawApplyRecord withdrawApplyRecord : list) {
            if (StringUtils.isBlank(withdrawApplyRecord.getWithdrawNo()) || StringUtils.isBlank(withdrawApplyRecord.getBatchNo()) 
                || StringUtils.isBlank(withdrawApplyRecord.getCustomerType())) {
                Logger.info("FixHistoryIdolWechatWithdrawPushPullReceiptGlueJavaTask执行参数为空：{}", JSON.toJSONString(withdrawApplyRecord));
                continue;
            }

            Logger.info("FixHistoryIdolWechatWithdrawPushPullReceiptGlueJavaTask执行参数：{}", withdrawApplyRecord);
            PaymentTransfer paymentTransfer = paymentTransferService.queryBySourceNo(Constant.XD_WECHAT, withdrawApplyRecord.getWithdrawNo());
            if (paymentTransfer == null) {
                Logger.info("FixHistoryIdolWechatWithdrawPushPullReceiptGlueJavaTask-paymentTransfer数据不存在, withdrawNo: {}", withdrawApplyRecord.getWithdrawNo());
                continue;
            }
            // SpringContextHolder.getBean(WithdrawReceiptDownloadTask.class)
            //     .init(WithdrawChannelConstants.WECHAT.getName(), withdrawApplyRecord.getWithdrawNo(), withdrawApplyRecord.getBatchNo(), 
            //     paymentTransfer.getMchCode(), false, withdrawApplyRecord.getCustomerType());

            QueryWrapper<PostActionItem> PostActionItemwrapper = new QueryWrapper<>();
            PostActionItemwrapper.eq("biz_id", withdrawApplyRecord.getWithdrawNo())
                   .eq("action_type", PostActionTypes.WITHDRAW_RECEIPT_DOWNLOAD.getName());
            List<PostActionItem> selectList = postActionItemMapper.selectList(PostActionItemwrapper);
            if (CollectionUtils.isEmpty(selectList)) {
                WithdrawReceiptDownloadBO bo = WithdrawReceiptDownloadBO.builder()
                    .withdrawNo(withdrawApplyRecord.getWithdrawNo())
                    .withdrawChannel(WithdrawChannelConstants.WECHAT.getName())
                    .isTenantCustomer(false)
                    .merchantCode(paymentTransfer.getMchCode())
                    .batchNo(withdrawApplyRecord.getBatchNo())
                    .customerType(withdrawApplyRecord.getCustomerType())
                    .build();
                PostActionItemBO itemBO = PostActionItemBO.builder()
                        .bizId(withdrawApplyRecord.getWithdrawNo())
                        .paramObject(bo)
                        .remark("微信提现回执单下载")
                        .actionType(PostActionTypes.WITHDRAW_RECEIPT_DOWNLOAD.getName())
                        .status(PostActionExecStatus.EXECUTE.value())
                        .nextRetryTime(LocalDateTime.now().plusMinutes(RandomUtil.randomLong(1, 240)))
                        .build();
                SpringContextHolder.getBean(PostActionService.class).addAction(itemBO);
            }
        }
        return ReturnT.SUCCESS;
    }

}
