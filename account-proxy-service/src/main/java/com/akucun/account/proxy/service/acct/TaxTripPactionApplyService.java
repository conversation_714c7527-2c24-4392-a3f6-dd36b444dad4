package com.akucun.account.proxy.service.acct;

import com.akucun.account.proxy.dao.model.TaxTripPactionApply;
import com.akucun.account.proxy.service.acct.bo.TaxTripPactionApplyBO;

import java.util.List;

/**
 * 税库银三方协议审核申请接口
 * <AUTHOR>
 */
public interface TaxTripPactionApplyService {

    /**
     * 查询有效的申请记录(状态不为2-驳回)
     * @param customerCode
     * @param customerType
     * @return
     */
     TaxTripPactionApply selectValidApply(String customerCode, String customerType);

    /**
     * 查询是否有审核中的申请记录
     * @param customerCode
     * @param customerType
     * @return
     */
    boolean hasAuditingApply(String customerCode, String customerType);

    /**
     * 根据审核工单编码查询申请记录
     * @param auditNo
     * @return
     */
    TaxTripPactionApply selectByAuditNo(String auditNo);

    /**
     * 查询所有申请记录
     * @param customerCode
     * @param customerType
     * @return
     */
    List<TaxTripPactionApply> selectAllApply(String customerCode, String customerType);

    /**
     * 保存申请记录
     * @param taxTripPactionApplyBO
     * @return
     */
    boolean saveApply(TaxTripPactionApplyBO taxTripPactionApplyBO);

    /**
     * 修改申请记录
     * @param taxTripPactionApply
     * @return
     */
    boolean updateApply(TaxTripPactionApply taxTripPactionApply);

    /**
     * 转换驳回原因
     * @param rejectReason
     * @return
     */
    String convertRejectReason(List<String> rejectReason);
}
