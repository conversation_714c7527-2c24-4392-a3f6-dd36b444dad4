package com.akucun.account.proxy.domain.model;

import com.aikucun.common2.log.Logger;
import com.akucun.account.center.client.model.TradeInfo;
import com.akucun.account.center.client.model.req.QueryAccountBookDetailReqData;
import com.akucun.account.center.client.model.vo.AccountBookDetailVO;
import com.akucun.account.center.common.entity.QueryPage;
import com.akucun.account.center.common.entity.ResultPage;
import com.akucun.account.center.feign.AccountCenterService;
import com.akucun.account.proxy.facade.stub.others.dto.req.AccountAdjustReq;
import com.akucun.account.proxy.service.common.SpringContextHolder;
import com.akucun.common.Result;
import com.alibaba.fastjson.JSON;
import lombok.Data;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Random;

@Data
public class AccountAdjust extends Adjust {
    /**
     *账户类型-key
     */
    private String accountTypeKey;

    /**
     * 来源单号
     */
    private String sourceBillNo;

    /**
     * 交易流水号
     */
    private String tradeNo;

    private String maxAccountAdjustLimit;

    @Override
    public Pair<Integer,String> done() {

        BigDecimal maxLimitAmount=new BigDecimal( maxAccountAdjustLimit );
        if(this.getAmount().compareTo(maxLimitAmount)==1){
            return Pair.of(88,"超过最大可调帐上限："+maxAccountAdjustLimit);
        }
        //调用账户中心查询
        AccountCenterService accountCenterService = SpringContextHolder.getBean(AccountCenterService.class);
        QueryAccountBookDetailReqData queryAccountBookDetailReqData=new QueryAccountBookDetailReqData();
        queryAccountBookDetailReqData.setAccountTypeKey(this.getAccountTypeKey());
        queryAccountBookDetailReqData.setCustomerCode(this.getCustomerCode());
        queryAccountBookDetailReqData.setTradeNo(this.tradeNo);
        queryAccountBookDetailReqData.setTradeTypes(Arrays.asList(this.getAdjustmentType()));
        QueryPage<QueryAccountBookDetailReqData> queryAccountBookDetailReqDataQueryPage=new QueryPage<>();
        queryAccountBookDetailReqDataQueryPage.setData(queryAccountBookDetailReqData);

        Logger.info("账户中心查询tradeNo是否重复，参数：{}",JSON.toJSONString(queryAccountBookDetailReqData));
        ResultPage<AccountBookDetailVO> resultPage= accountCenterService.queryAccountBookDetail(queryAccountBookDetailReqDataQueryPage);
        Logger.info("账户中心查询tradeNo是否重复，参数：{},响应：{}",JSON.toJSONString(queryAccountBookDetailReqData),JSON.toJSONString(resultPage.getMessage()));

        int line=resultPage.getData().size();
        if(line!=0){
            return Pair.of(66,"交易单流水号重复，请更换");
        }

        // 构建账户请求对象
        TradeInfo tradeInfo = new TradeInfo();
        tradeInfo.setAccountTypeKey(this.getAccountTypeKey());
        tradeInfo.setCustomerCode(this.getCustomerCode());
        tradeInfo.setAmount(this.getAmount());
        tradeInfo.setTradeType(this.getAdjustmentType());
        tradeInfo.setSourceBillNo(this.getSourceBillNo());
        tradeInfo.setTradeNo(this.getTradeNo());
        tradeInfo.setRemark(this.getRemark());


        if(tradeInfo.getAccountTypeKey().isEmpty()||tradeInfo.getCustomerCode().isEmpty()|| ObjectUtils.isEmpty(tradeInfo.getAmount())||
                tradeInfo.getTradeType().isEmpty()||tradeInfo.getSourceBillNo().isEmpty()||tradeInfo.getTradeNo().isEmpty()||tradeInfo.getRemark().isEmpty()
        ){
            return Pair.of(9,"服务异常请重试");
        }

        // 请求
        Logger.info("账户中心调账处理开始，tradeInfo:{}", JSON.toJSONString(tradeInfo));
        Result<Void> result = accountCenterService.dealTrade(tradeInfo);
        Logger.info("账户中心调账处理结束，参数tradeInfo:{},响应resp:{}", JSON.toJSONString(tradeInfo),JSON.toJSONString(result));
        // 结果判断，返回对应码，0为成功
        if (result.isSuccess()) {
            return Pair.of(result.getCode() , result.getMessage());
        } else {
            return Pair.of(result.getCode() , result.getMessage());
        }
    }

    public static AccountAdjust build(AccountAdjustReq accountAdjustReq) {
        AccountAdjust accountAdjust = new AccountAdjust();
        BeanUtils.copyProperties(accountAdjustReq, accountAdjust);
        if(ObjectUtils.isEmpty(accountAdjust.getSourceBillNo())){
            accountAdjust.setSourceBillNo(System.currentTimeMillis() + "" + (new Random().nextInt(10)));
        }
        if(ObjectUtils.isEmpty(accountAdjust.tradeNo)){
            accountAdjust.setTradeNo(accountAdjust.getSourceBillNo());
        }
        return accountAdjust;
    }

    public String getMaxAccountAdjustLimit() {
        return maxAccountAdjustLimit;
    }

    public void setMaxAccountAdjustLimit(String maxAccountAdjustLimit) {
        this.maxAccountAdjustLimit = maxAccountAdjustLimit;
    }
}

