package com.akucun.account.proxy.service.notify.impl;

import org.springframework.stereotype.Service;

import com.akucun.account.proxy.facade.stub.exception.AccountProxyException;
import com.akucun.account.proxy.facade.stub.others.account.req.NotifyReq;
import com.akucun.account.proxy.service.notify.NotifyService;
import com.akucun.account.proxy.service.notify.factory.NotifyFactory;
import com.akucun.common.Result;
import com.mengxiang.base.common.log.Logger;
/**
 * 
 * <AUTHOR>
 * 通知服务
 *
 */
@Service
public class NotifyServiceImpl implements NotifyService{
	
	@Override
	public Result<Void> withdrawNotify(NotifyReq notifyReq) {
		Logger.info("NotifyServiceImpl withdrawNotify req:{}", notifyReq);
		try {
			return NotifyFactory.getNotifyHandler(notifyReq.getRequestPlatform(), notifyReq.getBizCategory()).deal(notifyReq);
		}catch (AccountProxyException e) {
			Logger.info("NotifyServiceImpl withdrawNotify 失败 req:{}", notifyReq,e);
			return Result.error(e.getErrorMsg());
		}catch (Exception e) {
			Logger.info("NotifyServiceImpl withdrawNotify 失败 req:{}", notifyReq,e);
			return Result.error();
		}
	}

}
