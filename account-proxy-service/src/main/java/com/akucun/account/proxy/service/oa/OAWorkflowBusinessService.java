package com.akucun.account.proxy.service.oa;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.facade.stub.others.oa.req.business.MultiSupplierCorporatePaymentRequest;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Create on : 2025/1/14 17:39
 **/
public interface OAWorkflowBusinessService {

    /**
     * 多供商对公付款
     * @param request
     * @return
     */
    Result<String> multiSupplierCorporatePayment(MultiSupplierCorporatePaymentRequest request);

}
