package com.akucun.account.proxy.service.postaction.task;

import com.aikucun.common2.base.Result;
import com.aikucun.common2.utils.datamasking.DataMask;
import com.akucun.account.proxy.common.utils.Results;
import com.akucun.account.proxy.common.enums.PostActionTypes;
import com.akucun.account.proxy.common.enums.ResponseEnum;
import com.akucun.account.proxy.common.utils.RedisLock;
import com.akucun.account.proxy.dao.model.AccountCommissionTrade;
import com.akucun.account.proxy.dao.model.PostActionItem;
import com.akucun.account.proxy.service.commission.AccountCommissionService;
import com.akucun.account.proxy.service.postaction.PostActionService;
import com.akucun.account.proxy.service.postaction.common.AbsPostActionExecutor;
import com.akucun.fps.common.util.GsonUtils;
import com.alibaba.fastjson.JSON;
import com.mengxiang.base.common.log.Logger;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.util.ShardingUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.Semaphore;

/**
 * @Author: silei
 * @Date: 2020/12/11
 * @desc: 分佣任务执行task
 */
@Component
public class CommissionPostExecutorTask extends AbsPostActionExecutor {

    @Autowired
    private AccountCommissionService commissionService;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private PostActionService postActionService;
    @Autowired
    private ThreadPoolTaskExecutor executor;

    private Semaphore semaphore;

    @Value("${commission.lock.expire.time:300}")
    private int lockExpireTime;
    @Value("${commission.lock.switch:true}")
    private boolean lockSwitch;

    @Value("${post.action.executor.semaphoreNum:5}")
    private void setExecutorParams(int semaphoreNum) {
        Logger.info("setExecutorParams semaphoreNum:{}", semaphoreNum );
        if (semaphoreNum <= 0) {
            semaphoreNum = 5;
        }
        semaphore = new Semaphore(semaphoreNum);
    }

    @Value("${commission.post.day.off:-3}")
    private int dayOff;
    @Value("${commission.post.batch.num:1000}")
    private int batchNum;

    @Value("${commission.max.retry.times:6}")
    private int maxRetryTimes;

    @Override
    protected String getActionType() {
        return PostActionTypes.DELAY_COMMISSION.getName();
    }

    @XxlJob("commissionPostExecutorTask")
    public ReturnT<String> execute(String param) {
        Logger.info("CommissionPostExecutorTask start! param:{}", param);
        // 获取xxlJob数据分片
        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        Logger.info("异步任务执行定时任务:数据分片信息{}", DataMask.toJSONString(shardingVO));
        //获取执行器总数量，默认1
        int total = (shardingVO == null ? 1 : shardingVO.getTotal());
        //获取当前分片，默认0
        int index = (shardingVO == null ? 0 : shardingVO.getIndex());
        if (StringUtils.isNotEmpty(param) && index == 0) {
            //特定任务执行
            String[] arr = param.split(",");
            //起始任务id
            long start = Long.parseLong(arr[0]);
            long end = Long.parseLong(arr[1]);
            int retryTimes = Integer.parseInt(arr[2]);
            List<PostActionItem> list = postActionService.selectByIndex(start, end, retryTimes, getActionType());
            list.forEach(t -> postActionService.processAction(t));
        } else {
            //计算日期
            LocalDate date = LocalDate.now().plusDays(dayOff);
            String createTime = date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd 00:00:00"));
            processPostAction(createTime, total, index);
        }
        Logger.info("CommissionPostExecutorTask finished!");
        return ReturnT.SUCCESS;
    }

    /**
     * 处理任务
     *
     * @param createTime
     * @param total
     * @param index
     */
    private void processPostAction(String createTime, int total, int index) {
        try {
            List<PostActionItem> list = postActionService.selectPage(createTime, batchNum, total, index, getActionType(), getMaxRetryTimes());
            if (CollectionUtils.isEmpty(list)) {
                Logger.info("CommissionPostExecutorTask.selectPage list size is 0");
                return;
            }
            //遍历执行
            list.forEach(t -> executor.execute(() -> {
                try {
                    semaphore.acquire();
                    postActionService.processAction(t);
                    semaphore.release();
                } catch (InterruptedException e) {
                    Logger.warn("CommissionPostExecutorTask semaphore.acquire exception:", e);
                }
            }));
        } catch (Exception e) {
            Logger.warn("CommissionPostExecutorTask exception:", e);
        }
    }

    @Override
    public Result<Void> execute(PostActionItem item) {
        AccountCommissionTrade trade = GsonUtils.getInstance().fromJson(item.getParam(), AccountCommissionTrade.class);
        String lockKey = trade.getCustomerCode() + "_" + trade.getTradeType() + "_" + trade.getTradeNo();
        RedisLock lock = new RedisLock(redisTemplate, lockKey, lockExpireTime);
        if (lockSwitch && !lock.tryLock()) {
            Logger.warn("CommissionPostExecutor RedisLock is lock,lockKey:{}", lockKey);
            return Results.error(ResponseEnum.ALLOCATE_COMMISSION_PROCESSING);
        }
        try {
            return commissionService.asyncAllocate(trade);
        } catch (Exception e) {
            Logger.warn("CommissionPostExecutor Exception:", e);
            return Results.error(ResponseEnum.ALLOCATE_COMMISSION_EXCEPTION);
        } finally {
            lock.unlock();
        }
    }

    @Override
    public int getMaxRetryTimes(){
        return maxRetryTimes;
    }
}
