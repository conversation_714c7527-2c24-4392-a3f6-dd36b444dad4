package com.akucun.account.proxy.service.acct;

import com.aikucun.common2.base.Result;
import com.aikucun.common2.base.exception.BusinessException;
import com.akucun.account.proxy.dao.model.*;
import com.akucun.account.proxy.facade.stub.others.account.req.ShopkeeperIncentiveAwardWithdrawCalcTaxReq;
import com.akucun.account.proxy.facade.stub.others.account.vo.WithdrawTaxDetailVO;
import com.akucun.account.proxy.facade.stub.others.aggregation.req.WithdrawTaxCalcReq;
import com.akucun.account.proxy.facade.stub.others.aggregation.req.WithdrawTaxDetailReq;
import com.akucun.account.proxy.facade.stub.others.aggregation.req.WithdrawTaxSummaryReq;
import com.akucun.account.proxy.facade.stub.others.aggregation.res.*;
import com.akucun.account.proxy.service.acct.bo.WithdrawThresholdCheckBO;
import com.akucun.fps.common.entity.Query;
import com.akucun.fps.common.entity.ResultList;

import java.util.Date;

/**
 * @Author: silei
 * @Date: 2021/3/9
 * @desc: 提现扣税相关
 */
public interface WithdrawTaxService {

    /**
     * 提现扣税开关：on-开
     * @param customerCode
     * @return
     */
    Result<WithdrawTaxSwitchResp> taxSwitch(String customerCode);

    /**
     * 提现手续费开关：on-开
     * @return
     */
    Result<WithdrawServiceFeeSwitchResp> serviceFeeSwitch(String identifyNo);

    /**
     * 税费试算
     * @param withdrawTaxCalcReq
     * @return
     */
    Result<WithdrawTaxCalcResp> taxCalc(WithdrawTaxCalcReq withdrawTaxCalcReq);

    /**
     * 获取当月提现汇总
     * @param customerCode
     * @return
     */
    Result<WithdrawTaxCalcResp> getCurrMonthTaxSummary(String customerCode, String customerType);

    /**
     * 提现扣税汇总分页查询
     * @param query
     * @return
     */
    ResultList<WithdrawTaxSummaryResp> queryWithdrawTaxSummaryByPage(Query<WithdrawTaxSummaryReq> query);

    /**
     * 提现扣税明细分页查询
     * @param query
     * @return
     */
    ResultList<WithdrawTaxDetailResp> queryWithdrawTaxDetailByPage(Query<WithdrawTaxDetailReq> query);

    /**
     * 提现申请扣税
     * @param record
     * @return
     * @throws BusinessException
     */
    WithdrawTaxDetail applyWithdrawTax(WithdrawApplyRecord record) throws BusinessException;

    WithdrawTaxDetail applyWithdrawTax(TenantWithdrawApply record) throws BusinessException;

    /**
     * 扣税处理
     * @param taxDetail
     * @return
     */
    Result<Void> withdrawTax(WithdrawTaxDetail taxDetail);

    /**
     * 店主店长扣手续费处理
     * @param record
     * @return
     */
    Result<Void> withdrawServiceFee(WithdrawApplyRecord record,WithdrawTaxDetail taxDetail,WithdrawServicefeeSummary withdrawServicefeeSummary);
    /**
     * 租户下店主店长扣手续费处理
     * @return
     */
    Result<Void> tenantWithdrawServiceFee(TenantWithdrawApply apply, WithdrawServicefeeSummary withdrawServicefeeSummary);
    /**
     * 提现成功税费处理
     * @param record
     */
    void withdrawSuccForTax(WithdrawApplyRecord record);

    /**
     * 微信提现成功税费处理
     * @param record
     */
    void wechatWithdrawSuccForTax(WithdrawApplyRecord record);

    /**
     * 提现失败税费处理
     * @param record
     * @param failReason
     *
     */
    void withdrawFailForTax(WithdrawApplyRecord record, String failReason);

    /**
     * 提现失败税费处理
     * @param apply
     * @param failReason
     *
     */
    void tenantWithdrawFailForTax(TenantWithdrawApply apply, String failReason);

    /**
     * 提现失败手续费处理
     * @param record
     */
    void withdrawFailForServiceFee(WithdrawApplyRecord record);
    /**
     * 租户店主店长提现失败手续费处理
     * @param apply
     */
    void tenantWithdrawFailForServiceFee(TenantWithdrawApply apply);

    /**
     * 租户店主店长提现失败手续费处理
     * @param apply
     */
    void tenantWithdrawSussForTax(TenantWithdrawApply apply);

    /**
     * 提现扣税交易
     * @param withdrawTaxDetail
     * @return
     */
    Result<Void> withdrawTaxTrade(WithdrawTaxDetail withdrawTaxDetail);

    /**
     * 查询提现扣税详情
     * @param withdrawNo
     * @return
     */
    WithdrawTaxDetail getWithdrawTaxDetail(String withdrawNo);

    /**
     * 提现扣税失败金额更新
     * @param taxDetail
     * @return
     */
    Result<Void> withdrawTaxFailAmount(WithdrawTaxDetail taxDetail);

    /**
     * 提现扣手续费失败金额更新
     * @param record
     * @return
     */
    Result<Void> withdrawServiceFeeFailAmount(WithdrawApplyRecord record);
    /**
     * 判断提现阈值
     * @param record
     * @return
     */
    WithdrawThresholdCheckBO checkWithdrawThreshold(WithdrawApplyRecord record);

    WithdrawThresholdCheckBO checkWithdrawThreshold(TenantWithdrawApply record);

    /**
     * 微信提现汇总金额
     *
     * @param record
     * @param taxDetail
     * @return
     */
    Result<Void> wechatWithdrawSumAmount(WithdrawApplyRecord record, WithdrawTaxDetail taxDetail);

    /**
     * 微信提现失败汇总金额处理
     * @param record
     * @param taxDetail
     * @return
     */
    void wechatWithdrawFailSumAmount(WithdrawApplyRecord record, WithdrawTaxDetail taxDetail);


    /**
     * 查询微信累计提现金额
     * @param identifyNo
     * @param year
     * @return
     */
    WechatWithdrawSummary queryWechatWithdrawSummary(String identifyNo, Date year);

    /**
     * 店主激励奖励发放自动提现扣税计算
     * @param req
     * @return
     */
    Result<WithdrawTaxDetailVO> shopkeeperIncentiveAwardWithdrawCalcTax(ShopkeeperIncentiveAwardWithdrawCalcTaxReq req);

    Result<String> getWithdrawTaxKey(String encrptIdNo, String month);

    Result<Void> taxDetail(WithdrawTaxDetail taxDetail);

    Result<Void> taxSummary(WithdrawTaxDetail taxDetail);

}
