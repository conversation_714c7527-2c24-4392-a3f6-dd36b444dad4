package com.akucun.account.proxy.service.enums;

import lombok.Getter;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : 奖励金下发任务-枚举值
 * @Create on : 2025/1/15 15:38
 **/
public enum PromoTradeStatusEnum {
    INVOICE_WAIT_UPLOAD("INVOICE_WAIT_UPLOAD","待上传发票"),
    INVOICE_WAIT_AUDIT("INVOICE_WAIT_AUDIT","待审核发票"),
    INVOICE_REJECT("INVOICE_REJECT","发票被驳回"),

    INIT("INIT","任务受理成功"),
    SUBMIT("SUBMIT","任务提交成功"),

    RISK_REJECT("RISK_REJECT","风控驳回"),
    NO_CARD_BIND("NO_CARD_BIND","未绑卡"),

    PAYING("PAYING","付款中"),
    FAIL("FAIL","付款失败"),
    SUSS("SUSS","付款成功"),
    ;

    @Getter
    private String code;
    @Getter
    private String codeDesc;

    PromoTradeStatusEnum(String code, String codeDesc) {
        this.code = code;
        this.codeDesc = codeDesc;
    }

    public static PromoTradeStatusEnum getByCode(String code) {
        for (PromoTradeStatusEnum statusEnum : PromoTradeStatusEnum.values()) {
            if (statusEnum.getCode().equalsIgnoreCase(code)) {
                return statusEnum;
            }
        }
        return null;
    }

    /**
     * 是否支持重复请求
     *
     * @param code
     * @return
     */
    public static Boolean supportRepeatReq(String code){
        if(ObjectUtils.isEmpty(code)){
            return false;
        }

        PromoTradeStatusEnum statusEnum = getByCode(code);
        if(ObjectUtils.isEmpty(statusEnum)){
            return false;
        }

        if(RISK_REJECT == statusEnum || NO_CARD_BIND == statusEnum){
            return true;
        }

        return false;
    }
}
