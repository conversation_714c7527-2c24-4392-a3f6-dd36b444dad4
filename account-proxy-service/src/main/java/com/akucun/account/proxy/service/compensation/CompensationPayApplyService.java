package com.akucun.account.proxy.service.compensation;

import com.akucun.account.proxy.dao.model.CompensationPayApply;
import com.akucun.account.proxy.facade.stub.others.dto.req.CompensationFillPayApplyReq;
import com.baomidou.mybatisplus.extension.service.IService;
import org.apache.commons.lang3.tuple.Pair;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Create on : 2024/11/25 21:56
 **/
public interface CompensationPayApplyService extends IService<CompensationPayApply> {

    Pair<Boolean,String> deal(CompensationFillPayApplyReq req);
}
