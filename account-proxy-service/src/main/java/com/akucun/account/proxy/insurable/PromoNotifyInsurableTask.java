package com.akucun.account.proxy.insurable;

import com.aikucun.common2.log.Logger;
import com.akucun.account.proxy.common.utils.HttpClient;
import com.akucun.account.proxy.dao.model.RewardApply;
import com.akucun.account.proxy.facade.stub.others.dto.res.OANotifyDTO;
import com.akucun.account.proxy.service.acct.PromoTradeService;
import com.akucun.account.proxy.service.common.SpringContextHolder;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.mengxiang.transaction.framework.dao.TransactionTaskLogDO;
import com.mengxiang.transaction.framework.enums.TaskExecuteErrorCodeEnum;
import com.mengxiang.transaction.framework.enums.TaskExecuteStatusEnum;
import com.mengxiang.transaction.framework.task.InsurableTask;
import com.mengxiang.transaction.framework.task.TaskExecuteResult;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * @Description
 * @aurhor lee
 * @date: 2022/7/18 16:00
 */
public class PromoNotifyInsurableTask extends InsurableTask<TaskExecuteResult> {

    public RewardApply rewardApply;

    public PromoNotifyInsurableTask() {

    }

    public PromoNotifyInsurableTask(RewardApply rewardApply) {
        this.rewardApply = rewardApply;
    }

    @Override
    public String getTaskId() {
        return rewardApply.getBatchNo() + "-" + rewardApply.getStatus();
    }

    @Override
    public TaskExecuteResult doExecute() {
        TaskExecuteResult taskExecuteResult = new TaskExecuteResult();
        try {

            Config config = ConfigService.getAppConfig();
            String promoNotifyUrl = config.getProperty("promo.oa.outer.notifyUrl", "");

            //03-异步通知
            OANotifyDTO notifyDTO = SpringContextHolder.getBean(PromoTradeService.class).buildOANotifyDTO(rewardApply);
            Logger.info("营销-奖励发放-OA最新状态通知外部开始：{}", JSON.toJSONString(notifyDTO));
            String notifyRslt = HttpClient.doPost(promoNotifyUrl, JSON.toJSONString(Arrays.asList(notifyDTO)));
            Logger.info("营销-奖励发放-OA最新状态通知外部结果：{}", JSON.toJSONString(notifyRslt));

            if (StringUtils.isNotEmpty(notifyRslt)) {
                JSONObject jsonObject = JSONObject.parseObject(notifyRslt);
                if (BooleanUtils.isTrue(jsonObject.getBoolean("success"))) {
                    taskExecuteResult.setExecuteStatus(TaskExecuteStatusEnum.SUCCESS);
                    return taskExecuteResult;
                }
            }
        } catch (Exception e) {
            Logger.warn("营销-奖励发放-OA最新状态通知外部异常taskId：{}", getTaskId(), e);
        }
        taskExecuteResult.setExecuteStatus(TaskExecuteStatusEnum.EXCEPTION);
        taskExecuteResult.setErrorCode(TaskExecuteErrorCodeEnum.SYSTEM_ERROR.name());
        taskExecuteResult.setErrorMessage(TaskExecuteErrorCodeEnum.SYSTEM_ERROR.getDesc());
        return taskExecuteResult;
    }

    @Override
    public void rebuild(TransactionTaskLogDO logDo) {
        String requestAdditionalInfo = logDo.getRequestAdditionalInfo();
        this.setAcctMultiRecordReq(JSON.parseObject(requestAdditionalInfo, new TypeReference<RewardApply>() {}));
    }

    @Override
    public String serializeAdditionalInfo() {
        return JSON.toJSONString(rewardApply);
    }

    public void setAcctMultiRecordReq(RewardApply rewardApply) {
        this.rewardApply = rewardApply;
    }

}
