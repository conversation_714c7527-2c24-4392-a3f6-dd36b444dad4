package com.akucun.account.proxy.service.acct;

import com.akucun.account.proxy.dao.model.MshopVipCard;
import com.akucun.account.proxy.facade.stub.others.dto.req.VipCardInfoReq;
import com.akucun.account.proxy.facade.stub.others.dto.req.VipCardWriteOffReq;
import com.akucun.account.proxy.facade.stub.others.dto.res.VipCardInfoResp;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <AUTHOR>
 * @date 2021/01/03 14:42
 */
public interface VipCardService extends IService<MshopVipCard> {

    VipCardInfoResp vipCardValidInfo(VipCardInfoReq vipCardInfoReq);

    Boolean vipCardWriteOff(VipCardWriteOffReq vipCardWriteOffReq);
}
