package com.akucun.account.proxy.service.postaction.task;

import com.aikucun.common2.base.Result;
import com.aikucun.common2.log.Logger;
import com.akucun.account.proxy.common.enums.PostActionTypes;
import com.akucun.account.proxy.dao.model.PostActionItem;
import com.akucun.account.proxy.service.acct.AccountWithdrawService;
import com.akucun.account.proxy.service.postaction.common.AbsPostActionExecutor;
import com.akucun.bcs.channel.facade.stub.dto.WechatWithdrawRequest;
import com.akucun.fps.common.util.GsonUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @Author: silei
 * @Date: 2021/10/19
 * @desc: 商家微信账户提现结果查询任务
 */
@Component
public class MerchantWechatWithdrawQueryTask extends AbsPostActionExecutor {

    @Autowired
    private AccountWithdrawService accountWithdrawService;

    @Override
    protected String getActionType() {
        return PostActionTypes.MERCHANT_WECHAT_WITHDRAW_QUERY.getName();
    }

    @Value("${merchant.wechat.query.times:15}")
    private int queryRetryTimes;

    @Override
    public int getMaxRetryTimes(){
        return queryRetryTimes;
    }

    @XxlJob("merchantWechatWithdrawQueryTask")
    public ReturnT<String> execute(String param) {
        return this.executeEntrance();
    }

    @Override
    public Result<Void> execute(PostActionItem item) {
        Result<Void> result = Result.success();
        try {
            WechatWithdrawRequest request = GsonUtils.getInstance().fromJson(item.getParam(), WechatWithdrawRequest.class);
            result = accountWithdrawService.queryMerchantWechatWithdraw(request);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setMessage(e.getMessage());
            Logger.error("交易补偿任务执行失败:",e);
        }
        return result;
    }
}
