package com.akucun.account.proxy.service.trade.bo;

import lombok.Data;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: silei
 * @Date: 2020/11/30
 * @desc:
 */
@Data
public class AccountTradeDetailBO {

    /**
     * 主键
     */
    private Long id;

    /**
     * account_pay表id
     */
    private Long accountTradeId;

    /**
     * 客户编码
     */
    private String customerCode;
    /**
     * 交易流水号
     */
    private String tradeNo;
    /**
     * 明细请求流水号
     */
    private String detailPayNo;

    /**
     * 交易类型
     */
    private String tradeType;

    /**
     * 交易子类型
     */
    private String subTradeType;

    /**
     * 交易状态
     */
    private String status;

    /**
     * 返回码
     */
    private String replyCode;

    /**
     * 返回消息
     */
    private String replyMsg;

    /**
     * 重试次数
     */
    private Integer retryTimes;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除 0:未删除
     */
    private Integer isDelete;

    /**
     * 备注
     */
    private String remark;

    private Map<String, String> extField = new HashMap<>();
}
