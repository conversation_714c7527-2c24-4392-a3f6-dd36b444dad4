package com.akucun.account.proxy.service.trade;

import com.akucun.account.proxy.common.constant.CommonConstants;
import com.akucun.account.proxy.common.enums.ResultStatus;
import com.akucun.account.proxy.dao.model.AccountTrade;
import com.akucun.account.proxy.service.common.bo.AccountExecStepContext;
import com.akucun.account.proxy.service.common.bo.AccountResp;
import com.akucun.account.proxy.service.common.bo.AccountTradeReq;
import com.akucun.common.Result;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2020/12/24 15:52
 */
@Component
public class AccountTradeCommonService {

    @Autowired
    private AccountTradeService accountTradeService;

    public void handlerAfterCommit(AccountExecStepContext accountExecStepContext) {
        Result<Void> result = (Result<Void>) accountExecStepContext.getRespMessage();
        AccountResp resp = accountExecStepContext.getAccountResp();
        AccountTradeReq req = accountExecStepContext.getAccountTradeReq();

        LambdaQueryWrapper wrapper =
                new LambdaQueryWrapper<AccountTrade>()
                        .eq(AccountTrade::getTradeNo, req.getTradeNo())
                        .eq(AccountTrade::getTradeType, CommonConstants.BONUS_ACCOUNT_PAY_TRADE_TYPE)
                        .eq(AccountTrade::getCustomerCode, req.getCustomerCode());
        if(result.isSuccess()) {
            // 更新为成功
            AccountTrade accountTrade = AccountTrade.builder().status(ResultStatus.S.getCode()).build();
            accountTradeService.update(accountTrade, wrapper);
            resp.setStatus(ResultStatus.S.getCode());
            resp.setReplyCode(CommonConstants.SUCC_CODE);
            resp.setReplyMsg(ResultStatus.S.getDesc());
        } else {
            // 更新为失败
            AccountTrade accountTrade = AccountTrade.builder().status(ResultStatus.P.getCode()).build();
            accountTradeService.update(accountTrade, wrapper);
            resp.setStatus(ResultStatus.P.getCode());
            resp.setReplyCode(CommonConstants.FAIL_CODE);
            resp.setReplyMsg(result.getMessage());
        }
    }

    public void handlerErrorAfterCommit(AccountExecStepContext accountExecStepContext) {
        Result<Void> result = (Result<Void>) accountExecStepContext.getRespMessage();
        AccountResp resp = accountExecStepContext.getAccountResp();
        AccountTradeReq req = accountExecStepContext.getAccountTradeReq();

        LambdaQueryWrapper wrapper =
                new LambdaQueryWrapper<AccountTrade>()
                        .eq(AccountTrade::getTradeNo, req.getTradeNo())
                        .eq(AccountTrade::getTradeType, CommonConstants.BONUS_ACCOUNT_PAY_TRADE_TYPE)
                        .eq(AccountTrade::getCustomerCode, req.getCustomerCode());
        if(result.isSuccess()) {
            // 更新为成功
            AccountTrade accountTrade = AccountTrade.builder().status(ResultStatus.S.getCode()).build();
            accountTradeService.update(accountTrade, wrapper);
            resp.setStatus(ResultStatus.S.getCode());
            resp.setReplyCode(CommonConstants.SUCC_CODE);
            resp.setReplyMsg(ResultStatus.S.getDesc());
        } else {
            // 更新为失败
            AccountTrade accountTrade = AccountTrade.builder().status(ResultStatus.P.getCode()).build();
            accountTradeService.update(accountTrade, wrapper);
            resp.setStatus(ResultStatus.F.getCode());
            resp.setReplyCode(CommonConstants.FAIL_CODE);
            resp.setReplyMsg(result.getMessage());
        }
    }
}
