package com.akucun.account.proxy.service.common;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.dao.model.DuplicateCheck;
import com.baomidou.mybatisplus.extension.service.IService;

/**
* @Author: silei
* @Date: 2021/3/11 
* @desc:
*/
public interface DuplicateCheckService extends IService<DuplicateCheck> {

    Result<Void> checkDuplicate(String uniqueId, Boolean isRemoveDuplicate);


}
