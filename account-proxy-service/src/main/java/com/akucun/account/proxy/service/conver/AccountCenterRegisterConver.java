package com.akucun.account.proxy.service.conver;

import com.akucun.account.proxy.common.constant.CustomerType;
import com.akucun.account.proxy.common.enums.AccountTypeEnum;
import com.akucun.account.proxy.common.enums.MemberRegisterType;
import com.akucun.account.proxy.common.enums.TenantTypeEnum;
import com.akucun.account.proxy.facade.stub.others.trade.req.AccountCenterRegisterReq;
import com.akucun.account.proxy.service.common.SpringContextHolder;
import com.akucun.account.proxy.service.help.SellerInfoFeignHelp;
import com.akucun.account.proxy.servicea.account.bo.MemberRegisterBo;
import com.mengxiang.member.service.facade.common.response.seller.SellerBaseResp;

import java.util.Objects;

public class AccountCenterRegisterConver {
	
	public static AccountCenterRegisterReq accountCenterRegisterReqConver(MemberRegisterBo memberRegisterBo, Integer tenantType) {
		AccountCenterRegisterReq accountCenterRegisterReq = new AccountCenterRegisterReq();
		accountCenterRegisterReq.setTenantId(String.valueOf(memberRegisterBo.getTenantId()));
		if(memberRegisterBo.getType()==MemberRegisterType.type2.getValue()) {
			accountCenterRegisterReq.setCustomerType(CustomerType.NM.name());
			accountCenterRegisterReq.setCustomerName(memberRegisterBo.getUserId());
			accountCenterRegisterReq.setAccountType(AccountTypeEnum.XD_NM.getValue());
			SellerBaseResp sellerBaseResp = SpringContextHolder.getBean(SellerInfoFeignHelp.class).queryBaseSellerById(Long.valueOf(memberRegisterBo.getUserId()));
			memberRegisterBo.setResellerId(sellerBaseResp.getResellerId());
			accountCenterRegisterReq.setCustomerCode(sellerBaseResp.getResellerId());
			accountCenterRegisterReq.setTenantType(TenantTypeEnum.tenantType2.getValue());
			if (Objects.nonNull(tenantType)) {
				accountCenterRegisterReq.setTenantType(tenantType);
			}
		}else if(memberRegisterBo.getType()==MemberRegisterType.type3.getValue()) {
			accountCenterRegisterReq.setCustomerType(CustomerType.NMDL.name());
			accountCenterRegisterReq.setCustomerName(memberRegisterBo.getUserId());
			accountCenterRegisterReq.setAccountType(AccountTypeEnum.XD_NMDL.getValue());
			accountCenterRegisterReq.setCustomerCode(memberRegisterBo.getUserId());
			accountCenterRegisterReq.setTenantType(TenantTypeEnum.tenantType2.getValue());
			if (Objects.nonNull(tenantType)) {
				accountCenterRegisterReq.setTenantType(tenantType);
			}
		}
		return accountCenterRegisterReq;
	}

}
