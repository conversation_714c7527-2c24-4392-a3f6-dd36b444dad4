package com.akucun.account.proxy.service.acct.bo;

import com.mengxiang.base.common.utils.datamasking.Sensitive;
import com.mengxiang.base.common.utils.datamasking.SensitiveType;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: silei
 * @Date: 2020/8/26
 * @desc:
 */
@Data
public class AccountOpTradeBO {

    private Long id;

    /**
     * 请求流水号(取时间戳)
     */
    private String orderNo;

    /**
     * 交易类型
     */
    private String tradeType;

    /**
     * 客户编码
     */
    private String customerCode;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 手机号
     */
    @Sensitive(type = SensitiveType.Phone)
    private String mobile;

    /**
     * 客户类型
     */
    private String customerType;

    /**
     * 账户属性
     */
    private String accountProperty;


    private Map<String, String> extField = new HashMap<>();

    /**
     * 交易状态
     */
    private String status;

    /**
     * 返回码
     */
    private String replyCode;

    /**
     * 返回消息
     */
    private String replyMsg;

    /**
     * 交易明细
     */
    private AccountOpTradeDetailBO accountOpTradeDetailBO;

}
