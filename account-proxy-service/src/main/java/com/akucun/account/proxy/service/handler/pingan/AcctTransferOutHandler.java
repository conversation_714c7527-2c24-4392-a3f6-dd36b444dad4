package com.akucun.account.proxy.service.handler.pingan;

import com.aikucun.common2.utils.datamasking.DataMask;
import com.akucun.account.proxy.common.constant.CommonConstants;
import com.akucun.account.proxy.common.enums.ResponseEnum;
import com.akucun.account.proxy.common.enums.ResultStatus;
import com.akucun.account.proxy.common.exception.AccountProxyException;
import com.akucun.account.proxy.common.utils.AccountUtils;
import com.akucun.account.proxy.common.utils.AmountUtils;
import com.akucun.account.proxy.common.utils.WechatNotifyTool;
import com.akucun.account.proxy.dao.model.AccountOpTransferAmount;
import com.akucun.account.proxy.service.acct.AccountOpTransferAmountService;
import com.akucun.account.proxy.service.common.bo.AccountExecStepContext;
import com.akucun.account.proxy.service.common.bo.AccountReq;
import com.akucun.account.proxy.service.common.bo.AccountResp;
import com.akucun.account.proxy.service.common.step.AbstractHandler;
import com.akucun.fps.common.entity.Result;
import com.akucun.fps.common.entity.ResultList;
import com.akucun.fps.pingan.client.model.AccountSearchReqDO;
import com.akucun.fps.pingan.client.model.PingAnAccountVO;
import com.akucun.fps.pingan.client.vo.DealTradeVO;
import com.akucun.fps.pingan.feign.api.member.MemberServiceApi;
import com.akucun.fps.pingan.feign.api.settlement.SettlementServiceApi;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mengxiang.base.common.log.Logger;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * @Author: silei
 * @Date: 2020/9/2
 * @desc: 账户余额归集到公司账户
 */
@Component
public class AcctTransferOutHandler extends AbstractHandler {

    @Resource
    private SettlementServiceApi settlementServiceApi;
    @Resource
    private MemberServiceApi memberService;
    @Autowired
    private AccountOpTransferAmountService amountService;
    @Autowired
    private WechatNotifyTool wechatNotifyTool;

    @Override
    protected boolean preCheck(AccountExecStepContext accountExecStepContext) {
        AccountResp resp = accountExecStepContext.getAccountResp();
        AccountReq req = accountExecStepContext.getAccountReq();
        Logger.info("CancelAccountHandler preCheck req:{}", req);
        //参数校验
        if (StringUtils.isBlank(req.getCustomerCode())) {
            resp.setStatus(ResultStatus.F.getCode());
            resp.setReplyMsg("客户编码为空");
        }
        if (StringUtils.isBlank(req.getCustomerType())) {
            resp.setStatus(ResultStatus.F.getCode());
            resp.setReplyMsg("客户类型为空");
        }
        if (StringUtils.isBlank(req.getDetailOrderNo())) {
            resp.setStatus(ResultStatus.F.getCode());
            resp.setReplyMsg("请求流水号为空");
        }
        return !ResultStatus.F.getCode().equals(resp.getStatus());
    }

    @Override
    protected void doSubmitBefore(AccountExecStepContext accountExecStepContext) {
        AccountReq req = accountExecStepContext.getAccountReq();
        //查询平安账户余额信息
        AccountSearchReqDO accountSearchReqDO = new AccountSearchReqDO();
        accountSearchReqDO.setCustomerCode(AccountUtils.getSellerCode(req.getCustomerCode(), req.getCustomerType()));
        accountSearchReqDO.setCustomerType(req.getCustomerType());
        ResultList<PingAnAccountVO> list = settlementServiceApi.queryPlatformAccount(accountSearchReqDO);
        Logger.info("AcctTransferOutHandler doSubmitBefore balance query result:{}", DataMask.toJSONString(list));
        if (Objects.isNull(list) || !list.isSuccess() || CollectionUtils.isEmpty(list.getDatalist())) {
            Logger.error("AcctTransferOutHandler doSubmitBefore query balance exception, accountUpdateError!");
            throw new AccountProxyException(ResponseEnum.PARAM_EXCEPTION);
        }
        List<PingAnAccountVO> datalist = (List<PingAnAccountVO>) list.getDatalist();
        Long totalBalance = AmountUtils.yuan2fen(datalist.get(0).getTotalBalance());
        if (totalBalance == 0) {
            Logger.info("AcctTransferOutHandler doSubmitBefore account balance is 0 !");
        } else {
            //记录资金归集金额
            int i = amountService.getBaseMapper().insert(formData(req, totalBalance));
            if (i != 1) {
                Logger.error("AcctTransferOutHandler doSubmit accountUpdateError, transferAmount record exception !! customerCode:{}, customerType:{}", req.getCustomerCode(), req.getCustomerType());
                throw new AccountProxyException(ResponseEnum.AMOUNT_COLLECT_EXCEPTION);
            }
            accountExecStepContext.setReqMessage(totalBalance);
        }
    }

    @Override
    protected void doSubmit(AccountExecStepContext accountExecStepContext) {
        Long amount = (Long) accountExecStepContext.getReqMessage();
        AccountReq req = accountExecStepContext.getAccountReq();
        if (Objects.nonNull(amount)) {
            try {
                //构造参数
                DealTradeVO vo = generateParam(req, amount);
                Logger.info("AcctTransferOutHandler doSubmit req:{}", DataMask.toJSONString(vo));
                //发起请求
                Result<Void> result = memberService.dealTrade(vo);
                Logger.info("AcctTransferOutHandler doSubmit result:{}", DataMask.toJSONString(result));
                if (Objects.isNull(result)) {
                    Logger.error("AcctTransferOutHandler doSubmit accountUpdateError, transferAmount result is null! customerCode:{}, customerType:{}", req.getCustomerCode(), req.getCustomerType());
                    //返回参数异常
                    throw new AccountProxyException(ResponseEnum.PARAM_EXCEPTION);
                }
                accountExecStepContext.setRespMessage(result);
            } catch (Exception e) {
                Logger.error("AcctTransferOutHandler doSubmit customerCode:{}, customerType:{},accountUpdateError:{}", req.getCustomerCode(), req.getCustomerType(), e);
                throw new AccountProxyException(ResponseEnum.SUBMIT_EXCEPTION);
            }
        }
    }


    @Override
    protected void doSubmitAfter(AccountExecStepContext accountExecStepContext) throws Exception {
        Result<Void> result = (Result<Void>) accountExecStepContext.getRespMessage();
        AccountResp resp = accountExecStepContext.getAccountResp();
        AccountReq req = accountExecStepContext.getAccountReq();
        if (Objects.nonNull(result)) {
            resp.setStatus(ResultStatus.P.getCode());
            if (result.isSuccess()) {
                //资金归集状态变更成功
                int i = amountService.getBaseMapper().update(AccountOpTransferAmount.builder().collectStatus(ResultStatus.S.getCode()).build(),
                        new LambdaQueryWrapper<AccountOpTransferAmount>()
                                .eq(AccountOpTransferAmount::getAccountTradeId, req.getAccountTradeId())
                                .eq(AccountOpTransferAmount::getDetailOrderNo, req.getDetailOrderNo()));
                if (i != 1) {
                    wechatNotifyTool.sendNotifyMsg("Account-Proxy AcctTransferOutHandler doSubmitAfter transferAmount exception! customerCode:" + req.getCustomerCode() + ", customerType:" + req.getCustomerType());
                    Logger.error("AcctTransferOutHandler doSubmitAfter accountUpdateError, transferAmount exception! customerCode:{}, customerType:{}", req.getCustomerCode(), req.getCustomerType());
                    throw new AccountProxyException(ResponseEnum.AMOUNT_COLLECT_EXCEPTION);
                }
            } else {
                //资金归集状态变更失败
                amountService.getBaseMapper().update(AccountOpTransferAmount.builder().collectStatus(ResultStatus.F.getCode()).build(),
                        new LambdaQueryWrapper<AccountOpTransferAmount>()
                                .eq(AccountOpTransferAmount::getAccountTradeId, req.getAccountTradeId())
                                .eq(AccountOpTransferAmount::getDetailOrderNo, req.getDetailOrderNo()));

                resp.setStatus(ResultStatus.F.getCode());
                resp.setReplyCode(CommonConstants.FAIL_CODE);
                resp.setReplyMsg(ResultStatus.F.getDesc());

                wechatNotifyTool.sendNotifyMsg("Account-Proxy AcctTransferOutHandler doSubmitAfter transferAmount fail! customerCode:" + req.getCustomerCode() + ", customerType:" + req.getCustomerType());
                return;
            }
        }
        //结果为空说明账户余额为0，跳过资金归集流程
        resp.setStatus(ResultStatus.S.getCode());
        resp.setReplyCode(CommonConstants.SUCC_CODE);
        resp.setReplyMsg(ResultStatus.S.getDesc());

    }

    private AccountOpTransferAmount formData(AccountReq req, Long totalBalance) {
        return AccountOpTransferAmount.builder()
                .accountTradeId(req.getAccountTradeId())
                .detailOrderNo(req.getDetailOrderNo())
                .customerCode(req.getCustomerCode())
                .customerType(req.getCustomerType())
                .customerName(req.getCustomerName())
                .amount(totalBalance)
                .collectStatus(ResultStatus.P.getCode())
                .allocateStatus(ResultStatus.P.getCode())
                .build();
    }

    private DealTradeVO generateParam(AccountReq req, Long amount) {
        DealTradeVO vo = new DealTradeVO();
        vo.setOrderNo(req.getDetailOrderNo());
        //店主编号需要拼接
        vo.setCustomerCode(AccountUtils.getSellerCode(req.getCustomerCode(), req.getCustomerType()));
        vo.setCustomerType(req.getCustomerType());
        vo.setCustomerName(req.getCustomerName());
        vo.setTranAmount(new BigDecimal(AmountUtils.fen2yuan(amount.toString())));
        vo.setContent("账户资金归集");
        vo.setSettlementPeriod("0");
        return vo;
    }

}
