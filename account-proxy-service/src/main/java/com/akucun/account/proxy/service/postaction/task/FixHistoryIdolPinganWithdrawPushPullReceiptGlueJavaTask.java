/*
 * @Author: Lee
 * @Date: 2025-03-28 17:00:20
 * @Description: Do not edit
 * @License: Copyright (c) 2025, Lee
 */
package com.akucun.account.proxy.service.postaction.task;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;

import com.aikucun.common2.log.Logger;
import com.akucun.account.proxy.common.utils.HttpClient;
import com.akucun.account.proxy.dao.mapper.PaymentTransferMapper;
import com.akucun.account.proxy.dao.mapper.PostActionItemMapper;
import com.akucun.account.proxy.dao.mapper.TenantWithdrawApplyMapper;
import com.akucun.account.proxy.dao.mapper.WithdrawApplyRecordMapper;
import com.akucun.account.proxy.dao.model.TenantWithdrawApply;
import com.akucun.account.proxy.dao.model.WithdrawApplyRecord;
import com.akucun.account.proxy.service.transfer.PaymentTransferService;
import com.akucun.fps.common.entity.Result;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.log.XxlJobLogger;

import cn.hutool.core.util.RandomUtil;

/**
 * @Description
 * @aurhor lee
 * @date: 2022/9/13 14:34
 */
public class FixHistoryIdolPinganWithdrawPushPullReceiptGlueJavaTask extends IJobHandler {

    @Resource
    private WithdrawApplyRecordMapper withdrawApplyRecordMapper;

    @Resource
    private PaymentTransferMapper paymentTransferMapper;

    @Resource
    private PaymentTransferService paymentTransferService;

    @Resource
    private PostActionItemMapper postActionItemMapper;

    @Resource
    private TenantWithdrawApplyMapper tenantWithdrawApplyMapper;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        Logger.info("FixHistoryIdolPinganWithdrawPushPullReceiptGlueJavaTask执行参数：{}", param);
        if (StringUtils.isBlank(param)) {
            Logger.info("FixHistoryIdolPinganWithdrawPushPullReceiptGlueJavaTask执行异常：参数不能为空");
            return ReturnT.SUCCESS;
        }
        String[] params = param.split(",");
        String startDate = params[0];
        String endDate = params[1];
        /**
         * SELECT *  FROM withdraw_apply_record WHERE create_time >= '2025-01-01 00:00:00' and create_time < '2025-03-29 00:00:00' 
            and withdraw_channel = 'PINGAN' and apply_status = 'SUCC';
         */
        QueryWrapper<WithdrawApplyRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("withdraw_channel", "PINGAN");
        queryWrapper.eq("apply_status", "SUCC");
        queryWrapper.ge("create_time", startDate);
        queryWrapper.lt("create_time", endDate);
        //只返回withdrawNo, customerType
        queryWrapper.select("withdraw_no", "customer_type");
        List<WithdrawApplyRecord> list = withdrawApplyRecordMapper.selectList(queryWrapper); 

        QueryWrapper<TenantWithdrawApply> tenantQueryWrapper = new QueryWrapper<>();
        tenantQueryWrapper.eq("withdraw_channel", "PINGAN");
        tenantQueryWrapper.eq("apply_status", "SUCC");
        tenantQueryWrapper.ge("create_time", startDate);
        tenantQueryWrapper.lt("create_time", endDate);
        //只返回withdrawNo, customerType
        tenantQueryWrapper.select("withdraw_no", "customer_type");
        List<TenantWithdrawApply> tenantList = tenantWithdrawApplyMapper.selectList(tenantQueryWrapper); 

        XxlJobLogger.log("FixHistoryIdolPinganWithdrawPushPullReceiptGlueJavaTask执行: {}, 共{}, {}条数据", param, list.size(), tenantList.size());
        XxlJobLogger.log("FixHistoryIdolPinganWithdrawPushPullReceiptGlueJavaTask执行样例：{}, {}", JSONObject.toJSONString(list.get(0)), tenantList.size() > 0 ? JSON.toJSONString(tenantList.get(0)) : null);

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        for (WithdrawApplyRecord withdrawApplyRecord : list) {
            if (StringUtils.isBlank(withdrawApplyRecord.getWithdrawNo()) || StringUtils.isBlank(withdrawApplyRecord.getCustomerType())) {
                Logger.info("FixHistoryIdolPinganWithdrawPushPullReceiptGlueJavaTask执行参数为空：{}", JSON.toJSONString(withdrawApplyRecord));
                continue;
            }
            JSONObject applyRequest = new JSONObject();
            applyRequest.put("tradeId", withdrawApplyRecord.getWithdrawNo());
            applyRequest.put("customerType", withdrawApplyRecord.getCustomerType());
            applyRequest.put("tradeType", "WITHDRAW");
            applyRequest.put("executeTimeStr", (LocalDateTime.now().plusMinutes(RandomUtil.randomLong(1, 240))).format(formatter));
            String responseBody = HttpClient.doPost("http://*************:8080/api/pingan/settlement/applyReceipt", JSONObject.toJSONString(applyRequest));
            Result<Void> result = JSONObject.parseObject(responseBody, new TypeReference<Result<Void>>() {});
            if (!result.isSuccess()) {
                Logger.warn("FixHistoryIdolPinganWithdrawPushPullReceiptGlueJavaTask申请回单失败：{},{}", withdrawApplyRecord.getWithdrawNo(), JSON.toJSONString(result));
            }
        }

        for (TenantWithdrawApply withdrawApplyRecord : tenantList) {
            if (StringUtils.isBlank(withdrawApplyRecord.getWithdrawNo()) || StringUtils.isBlank(withdrawApplyRecord.getCustomerType())) {
                Logger.info("FixHistoryIdolPinganWithdrawPushPullReceiptGlueJavaTask执行参数为空：{}", JSON.toJSONString(withdrawApplyRecord));
                continue;
            }
            JSONObject applyRequest = new JSONObject();
            applyRequest.put("tradeId", withdrawApplyRecord.getWithdrawNo());
            applyRequest.put("customerType", withdrawApplyRecord.getCustomerType());
            applyRequest.put("tradeType", "WITHDRAW");
            applyRequest.put("executeTimeStr", (LocalDateTime.now().plusMinutes(RandomUtil.randomLong(1, 240))).format(formatter));
            String responseBody = HttpClient.doPost("http://*************:8080/api/pingan/settlement/applyReceipt", JSONObject.toJSONString(applyRequest));
            Result<Void> result = JSONObject.parseObject(responseBody, new TypeReference<Result<Void>>() {});
            if (!result.isSuccess()) {
                Logger.warn("FixHistoryIdolPinganWithdrawPushPullReceiptGlueJavaTask申请回单失败：{},{}", withdrawApplyRecord.getWithdrawNo(), JSON.toJSONString(result));
            }
        }

        return ReturnT.SUCCESS;
    }

}
