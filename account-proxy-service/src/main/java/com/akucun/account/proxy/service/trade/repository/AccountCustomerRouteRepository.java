package com.akucun.account.proxy.service.trade.repository;

import com.aikucun.common2.utils.datamasking.DataMask;
import com.akucun.account.proxy.dao.mapper.AccountCustomerRouteMapper;
import com.akucun.account.proxy.dao.model.AccountCustomerRoute;
import com.akucun.account.proxy.service.trade.bo.AccountCustomerRouteBO;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mengxiang.base.common.log.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @Author: silei
 * @Date: 2020/11/30
 * @desc:
 */
@Component
public class AccountCustomerRouteRepository {

    @Resource
    private AccountCustomerRouteMapper accountCustomerRouteMapper;


    public AccountCustomerRouteBO query(String tradeNo) {
        LambdaQueryWrapper<AccountCustomerRoute> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AccountCustomerRoute::getTradeNo, tradeNo)
                .last(" ORDER BY id DESC LIMIT 1");
        AccountCustomerRouteBO result = null;
        AccountCustomerRoute accountCustomerRoute = accountCustomerRouteMapper.selectOne(wrapper);
        if (Objects.nonNull(accountCustomerRoute)) {
            result = new AccountCustomerRouteBO();
            BeanUtils.copyProperties(accountCustomerRoute, result);
        }
        return result;
    }

    public void addAccountCustomerRoute(AccountCustomerRouteBO routeBO) {
        AccountCustomerRoute route = new AccountCustomerRoute();
        BeanUtils.copyProperties(routeBO, route);
        Logger.info("AccountCustomerRouteRepository 新增 addAccountCustomerRoute :{}", DataMask.toJSONString(route));
        accountCustomerRouteMapper.insert(route);
    }


}
