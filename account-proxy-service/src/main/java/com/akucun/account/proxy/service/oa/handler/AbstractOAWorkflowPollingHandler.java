package com.akucun.account.proxy.service.oa.handler;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.client.oa.WorkflowClient;
import com.akucun.account.proxy.common.exception.AccountProxyException;
import com.akucun.account.proxy.common.utils.SpringBeanUtil;
import com.akucun.account.proxy.dao.model.PostActionItem;
import com.akucun.account.proxy.facade.stub.enums.OAWorkflowBusinessTypeEnum;
import com.akucun.account.proxy.facade.stub.others.oa.resp.OAWorkflowResponseInfo;
import com.akucun.account.proxy.service.oa.bo.OAWorkflowStatusPollingBo;
import com.akucun.fps.common.util.GsonUtils;
import org.apache.commons.lang3.StringUtils;


/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Create on : 2025/1/15 10:39
 **/
public abstract class AbstractOAWorkflowPollingHandler {

    protected PostActionItem postActionItem;

    protected OAWorkflowStatusPollingBo pollingBo;

    public AbstractOAWorkflowPollingHandler(PostActionItem postActionItem, OAWorkflowStatusPollingBo pollingBo) {
        this.postActionItem = postActionItem;
        this.pollingBo = pollingBo;
    }

    protected abstract Result<Void> doHandle(OAWorkflowResponseInfo responseInfo);

    protected abstract String getBusinessType();

    public Result<Void> handle() {
        try {
            //获取OA工作流客户端
            WorkflowClient workflowClient = SpringBeanUtil.getApplicationContext().getBean(WorkflowClient.class);

            //查询流程状态
            OAWorkflowResponseInfo responseInfo = workflowClient.queryWorkflowByRequestId(pollingBo.getRequestId(), pollingBo.getUserId());

            //查询结果处理
            return this.doHandle(responseInfo);
        } catch (AccountProxyException e) {
            return Result.error(e.getErrorCode(), e.getErrorMsg());
        } catch (Exception e) {
            return Result.error(e);
        }
    }

    public static AbstractOAWorkflowPollingHandler getHandler(PostActionItem postActionItem) {
        OAWorkflowStatusPollingBo pollingBo = GsonUtils.getInstance().fromJson(postActionItem.getParam(), OAWorkflowStatusPollingBo.class);
        if (StringUtils.equals(pollingBo.getBusinessType(), OAWorkflowBusinessTypeEnum.MENTOR_BONUS.name())) {
            return new MentorBonusOAWorkflowPollingHandler(postActionItem, pollingBo);
        } else {
            throw new AccountProxyException("未找到对应的OA状态轮询处理器");
        }
    }

}
