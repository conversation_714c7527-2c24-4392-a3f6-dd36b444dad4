package com.akucun.account.proxy.service.tradeflow.script.phaseexec;

import com.aikucun.common2.log.Logger;
import com.aikucun.common2.utils.datamasking.DataMask;
import com.akucun.account.proxy.common.enums.ErrorCodeConstants;
import com.akucun.account.proxy.common.utils.CustomerUtils;
import com.akucun.account.proxy.common.utils.EncryptUtils;
import com.akucun.account.proxy.facade.stub.enums.IdentifyEnum;
import com.akucun.account.proxy.facade.stub.others.trade.res.TradePhaseStatusEnum;
import com.akucun.account.proxy.service.acct.AccountUpgradeService;
import com.akucun.account.proxy.service.common.SpringContextHolder;
import com.akucun.account.proxy.service.tradeflow.constants.ErrorCodes;
import com.akucun.account.proxy.service.tradeflow.dto.PhaseExecResult;
import com.akucun.account.proxy.service.tradeflow.dto.bizinfo.BizInfoBindcard;
import com.akucun.account.proxy.service.tradeflow.script.IPhaseExecScript;
import com.akucun.fps.common.entity.Result;
import com.akucun.fps.pingan.client.vo.PinganCardVO;
import com.akucun.fps.pingan.feign.api.merchant.MerchantServiceApi;
import com.akucun.fps.pingan.feign.api.unionpayauth.UnionpayAuthServiceApi;
import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.StringUtils;

public class ScriptPhaseExecBindcard implements IPhaseExecScript {

    @Override
    public PhaseExecResult exec(Object bizInfo) {
        BizInfoBindcard bindcard = (BizInfoBindcard) bizInfo;

        if("XD_NM".equals(bindcard.getAccountType()) || "XD_NMDL".equals(bindcard.getAccountType())) {
            if(SpringContextHolder.getBean(AccountUpgradeService.class).hasRecentUpgradeProcessing(bindcard.getCustomerCode(), bindcard.getCustomerType())) {
                return new PhaseExecResult(TradePhaseStatusEnum.FAIL, ErrorCodes.BUSINESS_ERROR, ErrorCodeConstants.BINDCARD_101901.getErrorMessage());
            }
        }

        String convertCustomerCode = CustomerUtils.convertCustomerCode(bindcard.getCustomerCode(), bindcard.getCustomerType(), bindcard.getAccountType());
        String pinganCustomerType = CustomerUtils.convertPinganCustomerType(bindcard.getCustomerType(), bindcard.getAccountType());
        String decryptIdCode = null;
        if(!IdentifyEnum.CREDIT_CODE.name().equals(bindcard.getIdType())) {
            decryptIdCode = EncryptUtils.decrypt(bindcard.getIdCode());
            if(StringUtils.isBlank(decryptIdCode)) {
                return new PhaseExecResult(TradePhaseStatusEnum.FAIL, ErrorCodes.DATA_ERROR, "bizInfo.idCode解密失败");
            }
        }
        String decryptBankCardCode = EncryptUtils.decrypt(bindcard.getBankCardCode());
        if(StringUtils.isBlank(decryptBankCardCode)) {
            return new PhaseExecResult(TradePhaseStatusEnum.FAIL, ErrorCodes.DATA_ERROR, "bizInfo.bankCardCode解密失败");
        }
        String decryptMobilePhone = EncryptUtils.decrypt(bindcard.getMobilePhone());
        if(StringUtils.isBlank(decryptMobilePhone)) {
            return new PhaseExecResult(TradePhaseStatusEnum.FAIL, ErrorCodes.DATA_ERROR, "bizInfo.mobilePhone解密失败");
        }

        if("SMS".equals(bindcard.getBindWay())) {
            PinganCardVO cardVO = new PinganCardVO();
            cardVO.setCustomerType(pinganCustomerType);
            cardVO.setCustomerCode(convertCustomerCode);
            cardVO.setIdType("" + IdentifyEnum.valueOf(bindcard.getIdType()).getCode());
            if(!IdentifyEnum.CREDIT_CODE.name().equals(bindcard.getIdType())) {
                cardVO.setIdCode(decryptIdCode);
            } else {
                cardVO.setIdCode(bindcard.getIdCode());
            }
            cardVO.setCustomerName(bindcard.getCustomerName());
            cardVO.setsBankCode(bindcard.getSbankCode());
            cardVO.setSbankName(bindcard.getSbankName());
            cardVO.setBankCardCode(decryptBankCardCode);
            cardVO.setMobilePhone(decryptMobilePhone);
            Logger.info("ScriptPhaseExecBindcard短信验证，请求：{}", DataMask.toJSONString(cardVO));
            Result<String> result = SpringContextHolder.getBean(UnionpayAuthServiceApi.class).unionpayAuthApply(cardVO);
            Logger.info("ScriptPhaseExecBindcard短信验证，请求：{}，返回：{}", DataMask.toJSONString(cardVO), result);
            if(!result.isSuccess()) {
                return new PhaseExecResult(TradePhaseStatusEnum.FAIL, ErrorCodes.CHANNEL_ERROR, result.getErrorMessage());
            } else {
                return new PhaseExecResult(TradePhaseStatusEnum.SUCCESS, null, null);
            }
        } else if("SMA".equals(bindcard.getBindWay())) {
            PinganCardVO cardVO = new PinganCardVO();
            cardVO.setCustomerType(pinganCustomerType);
            cardVO.setCustomerCode(convertCustomerCode);
            cardVO.setIdType("" + IdentifyEnum.valueOf(bindcard.getIdType()).getCode());
            if(!IdentifyEnum.CREDIT_CODE.name().equals(bindcard.getIdType())) {
                cardVO.setIdCode(decryptIdCode);
            } else {
                cardVO.setIdCode(bindcard.getIdCode());
            }
            cardVO.setCustomerName(bindcard.getCustomerName());
            cardVO.setsBankCode(bindcard.getSbankCode());
            cardVO.setSbankName(bindcard.getSbankName());
            cardVO.setBankCode(bindcard.getBankCode());
            cardVO.setBankName(bindcard.getBankName());
            cardVO.setBankCardCode(decryptBankCardCode);
            cardVO.setMobilePhone(decryptMobilePhone);
            Logger.info("ScriptPhaseExecBindcard小额鉴权，请求：{}", DataMask.toJSONString(cardVO));
            Result<String> result = SpringContextHolder.getBean(MerchantServiceApi.class).bindCardForMerFir(cardVO);
            Logger.info("ScriptPhaseExecBindcard小额鉴权，请求：{}，返回：{}", DataMask.toJSONString(cardVO), DataMask.toJSONString(result));
            if(!result.isSuccess()) {
                return new PhaseExecResult(TradePhaseStatusEnum.FAIL, ErrorCodes.CHANNEL_ERROR, result.getErrorMessage());
            } else {
                return new PhaseExecResult(TradePhaseStatusEnum.SUCCESS, null, null);
            }
        } else {
            return new PhaseExecResult(TradePhaseStatusEnum.FAIL, ErrorCodes.DATA_ERROR, "bizInfo.bindWay错误");
        }
    }

}
