package com.akucun.account.proxy.service.transfer.impl;

import com.aikucun.common2.utils.StringUtils;
import com.akucun.account.proxy.dao.mapper.TransferCallLogMapper;
import com.akucun.account.proxy.dao.model.TransferCallLog;
import com.akucun.account.proxy.service.transfer.TransferCallLogService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 付款请求日志表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-26
 */
@Service
public class TransferCallLogServiceImpl extends ServiceImpl<TransferCallLogMapper, TransferCallLog> implements TransferCallLogService {

    @Autowired
    private ThreadPoolTaskExecutor executor;

    @Override
    public void saveOne(String tradeNo, String requestType, String requestMsg, String responseMsg) {
        if(StringUtils.isNullOrEmpty(requestMsg) || StringUtils.isNullOrEmpty(responseMsg)) {
            return;
        }
        executor.execute(() -> {
            TransferCallLog transferCallLog = new TransferCallLog();
            transferCallLog.setTradeNo(tradeNo);
            transferCallLog.setRequestType(requestType);
            transferCallLog.setReqMessage(requestMsg);
            transferCallLog.setResMessage(responseMsg);
            this.save(transferCallLog);
        });
    }
}
