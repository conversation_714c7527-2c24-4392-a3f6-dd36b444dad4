package com.akucun.account.proxy.service.acct.bo;

import lombok.Data;

import java.io.Serializable;

@Data
public class AccountUpgradeBO implements Serializable {

    private static final long serialVersionUID = -1347899873556975884L;
    /**
     * 客户编码
     */
    private String customerCode;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 升级类型
     */
    private String upgradeType;

    /**
     * 昵称
     */
    private String nickName;
    /**
     * 手机号
     */
    private String mobile;

    /**
     * 客户类型
     */
    private String customerType;

    /**
     * 保留域
     */
    private String reserve;

    /**
     * 账户属性   注意：该字段是否要传，请咨询接口提供方
     * 对应枚举：AccountPropertyType
     * @return
     */
    private String accountProperty;



}
