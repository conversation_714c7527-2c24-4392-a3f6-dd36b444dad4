package com.akucun.account.proxy.service.acct.impl;

import com.aikucun.common2.base.Result;
import com.aikucun.common2.base.exception.IErrorCode;
import com.aikucun.common2.utils.datamasking.DataMask;
import com.akucun.account.center.client.model.TradeInfo;
import com.akucun.account.proxy.common.enums.MemberGrade;
import com.akucun.account.proxy.common.enums.ResponseEnum;
import com.akucun.account.proxy.common.exception.AccountProxyException;
import com.akucun.account.proxy.common.utils.AccountNoUtils;
import com.akucun.account.proxy.dao.model.RewardApply;
import com.akucun.account.proxy.facade.stub.enums.AccountKeyConstants;
import com.akucun.account.proxy.facade.stub.enums.CustomerType;
import com.akucun.account.proxy.facade.stub.enums.DetailTypeConstants;
import com.akucun.account.proxy.facade.stub.enums.RewardTypeEnum;
import com.akucun.account.proxy.facade.stub.others.dto.req.BonusPayQueryReq;
import com.akucun.account.proxy.facade.stub.others.dto.res.BonusPayInfoResp;
import com.akucun.account.proxy.facade.stub.others.dto.res.OANotifyDTO;
import com.akucun.account.proxy.facade.stub.others.oa.req.OAWorkflowStatusNotifyRequest;
import com.akucun.account.proxy.insurable.PromoNotifyInsurableTask;
import com.akucun.account.proxy.service.acct.AccountService;
import com.akucun.account.proxy.service.acct.PromoTradeService;
import com.akucun.account.proxy.service.acct.RewardApplyService;
import com.akucun.account.proxy.service.acct.WithdrawTaxService;
import com.akucun.account.proxy.service.acct.util.BigDecimalUtils;
import com.akucun.account.proxy.service.common.SpringContextHolder;
import com.akucun.account.proxy.service.enums.PromoTradeStatusEnum;
import com.akucun.account.proxy.service.help.FinClearingCoreFacadeHelp;
import com.akucun.account.proxy.service.help.SellerInfoFeignHelp;
import com.akucun.account.proxy.service.postaction.PostActionService;
import com.akucun.fps.pingan.client.constants.BankCardStatusConstants;
import com.akucun.fps.pingan.client.model.query.PinganCardQueryDO;
import com.akucun.fps.pingan.client.vo.PinganCardVO;
import com.akucun.fps.pingan.feign.api.settlement.SettlementServiceApi;
import com.akucun.member.audit.facade.stub.fallback.api.FeignMemberIndentityService;
import com.akucun.member.audit.model.dto.identity.MemberIdentityVerifyQueryReqDTO;
import com.akucun.member.audit.model.dto.identity.MemberIdentityVerifyRespDTO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.mengxiang.base.common.log.Logger;
import com.mengxiang.member.service.facade.common.enums.CurrentRoleEnum;
import com.mengxiang.member.service.facade.common.response.SellerResp;
import com.mengxiang.transaction.framework.executor.InsurableTaskExecutor;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Create on : 2025/1/15 16:49
 **/
@Service
public class PromoTradeServiceImpl implements PromoTradeService {
    @Autowired
    private RewardApplyService rewardApplyService;
    @Autowired
    private TransactionTemplate transactionTemplate;
    @Autowired
    private PostActionService postActionService;

    @Resource
    private SettlementServiceApi settlementServiceApi;

    @Resource
    private WithdrawTaxService withdrawTaxService;

    @Autowired
    private FinClearingCoreFacadeHelp finClearingCoreFacadeHelp;

    @Autowired
    private FeignMemberIndentityService feignMemberIndentityService;

    @Autowired
    private SellerInfoFeignHelp sellerInfoFeignHelp;

    @Autowired
    private AccountService accountService;

    @Override
    public BonusPayInfoResp queryTradeInfo(BonusPayQueryReq bonusPayQueryReq) {
        //01-查询申请记录
        List<RewardApply> records = rewardApplyService.queryRewardApplyList(bonusPayQueryReq.getTradeNo(), bonusPayQueryReq.getBizType(), bonusPayQueryReq.getTransBillDate(), bonusPayQueryReq.getActivityNo());
        if (CollectionUtils.isEmpty(records)) {
            return null;
        }

        //02-数据组装
        RewardApply rewardApplyRecord = records.stream().filter(record -> !StringUtils.isEmpty(record.getExt1()) && !StringUtils.isEmpty(record.getExt2())).findFirst().orElse(null);
        HashMap<String, String> ext1 = JSON.parseObject(rewardApplyRecord.getExt1(), new TypeReference<HashMap<String, String>>() {
        });
        HashMap<String, String> ext2 = JSON.parseObject(rewardApplyRecord.getExt2(), new TypeReference<HashMap<String, String>>() {
        });
        BonusPayInfoResp resp = new BonusPayInfoResp();
        BeanUtils.copyProperties(rewardApplyRecord, resp);
        resp.setTradeNo(rewardApplyRecord.getRequestNo());
        resp.setBizType(rewardApplyRecord.getBusiType());
        resp.setTransTime(rewardApplyRecord.getTradeTime());
        resp.setTotalAmount(new BigDecimal(ext2.get("totalAmount")));
        resp.setTotalAmountDesc(BigDecimalUtils.convertToChineseUppercase(resp.getTotalAmount()));
        resp.setTotalTaxAmount(new BigDecimal(ext2.get("totalTaxAmount")));
        resp.setExt1(ext1);
        if (RewardTypeEnum.MENTOR_BONUS.getCode().equalsIgnoreCase(bonusPayQueryReq.getBizType())) {
            if (!StringUtils.isEmpty(rewardApplyRecord.getAttachmentPaths())) {
                resp.setAllFjs(JSON.parseObject(rewardApplyRecord.getAttachmentPaths(), new TypeReference<List<String>>() {
                }));
            } else {
                List<String> fj = new ArrayList<>();
                if (ext1.containsKey(MentorBonusServiceImpl.FP)) {
                    String[] tmp1 = ext1.get(MentorBonusServiceImpl.FP).split(",");
                    if (tmp1.length > 0) {
                        fj.addAll(Arrays.asList(tmp1));
                    }
                }
                if (ext1.containsKey(MentorBonusServiceImpl.FJ)) {
                    String[] tmp2 = ext1.get(MentorBonusServiceImpl.FJ).split(",");
                    if (tmp2.length > 0) {
                        fj.addAll(Arrays.asList(tmp2));
                    }
                }
                if (ext1.containsKey(MentorBonusServiceImpl.MX)) {
                    String[] tmp3 = ext1.get(MentorBonusServiceImpl.MX).split(",");
                    if (tmp3.length > 0) {
                        fj.addAll(Arrays.asList(tmp3));
                    }
                }
                resp.setAllFjs(fj);
            }
        }

        return resp;
    }

    @Override
    public Boolean notifyFromOA(OAWorkflowStatusNotifyRequest notifyMsg) {
        //01-查询申请记录
        List<RewardApply> records = rewardApplyService.queryByBatchNo(notifyMsg.getBizNo());
        if (CollectionUtils.isEmpty(records)) {
            Logger.error("[notifyFromOA]查询OA申请记录为空:{}", JSON.toJSONString(notifyMsg));
            return Boolean.FALSE;
        }
        RewardApply rewardApplyRecord = records.stream().filter(record -> !StringUtils.isEmpty(record.getExt1()) && !StringUtils.isEmpty(record.getExt2())).findFirst().orElse(null);
        //HashMap<String, String> ext1 = JSON.parseObject(rewardApplyRecord.getExt1(), new TypeReference<HashMap<String, String>>() {});
        HashMap<String, String> ext2 = JSON.parseObject(rewardApplyRecord.getExt2(), new TypeReference<HashMap<String, String>>() {});
        if (rewardApplyRecord.getStatus().equalsIgnoreCase(PromoTradeStatusEnum.SUSS.getCode()) ||
                rewardApplyRecord.getStatus().equalsIgnoreCase(PromoTradeStatusEnum.FAIL.getCode())) {
            Logger.warn("[notifyFromOA]重复的异步通知结果:{}", JSON.toJSONString(notifyMsg));
            return Boolean.TRUE;
        }

        //02-更新通知信息
        Boolean updateFlag = Boolean.FALSE;
        String bizNo = null;
        if (!ext2.containsKey("workflowNo") && !StringUtils.isEmpty(notifyMsg.getWorkflowNo())) {
            updateFlag = Boolean.TRUE;
            ext2.put("workflowNo", notifyMsg.getWorkflowNo());
            rewardApplyRecord.setExt2(JSON.toJSONString(ext2));
            bizNo = notifyMsg.getWorkflowNo();
        }
        if (notifyMsg.isFinished() || notifyMsg.isCanceled()) {
            updateFlag = Boolean.TRUE;
            ext2.put("oaStatus", notifyMsg.getStatus());
            ext2.put("oaStatusDesc", notifyMsg.getStatusDesc());
            ext2.put("oaErrorMsg", notifyMsg.getErrorMsg());
            if(notifyMsg.isFinished()) {
                rewardApplyRecord.setStatus(PromoTradeStatusEnum.SUSS.getCode());
            }else if (notifyMsg.isCanceled()) {
                rewardApplyRecord.setStatus(PromoTradeStatusEnum.FAIL.getCode());
            }
            if(ext2.containsKey("oaRequestId")) {
                bizNo = ext2.get("oaRequestId");
            }else{
                bizNo = notifyMsg.getRequestId();
            }
            rewardApplyRecord.setExt2(JSON.toJSONString(ext2));
        } else if (notifyMsg.isProcessing()) {
            Logger.info("[notifyFromOA]异步通知(付款中):{}", JSON.toJSONString(notifyMsg));
        }
        if (updateFlag) {
            final String bizIdTmp = bizNo;
            Pair<Boolean, String> dbOperateRst = transactionTemplate.execute(new TransactionCallback<Pair<Boolean, String>>() {
                @Override
                public Pair<Boolean, String> doInTransaction(TransactionStatus transactionStatus) {
                    try {
                        //01-更新状态
                        rewardApplyService.updatesStatusById(rewardApplyRecord);

                        /*//02-异步通知
                        PostActionItemBO postActionItemBO = PostActionItemBO.builder()
                                .actionType(PostActionTypes.PROMO_REWARD_NOTIFY.getName())
                                .bizId(bizIdTmp)
                                .paramObject(rewardApplyRecord)
                                .remark("奖励金下发-更新最新状态")
                                .status(PostActionExecStatus.EXECUTE.value())
                                .build();
                        postActionService.addAction(postActionItemBO);*/

                        //02-异步通知提交状态
                        PromoNotifyInsurableTask promoNotifyInsurableTask = new PromoNotifyInsurableTask(rewardApplyRecord);
                        SpringContextHolder.getBean(InsurableTaskExecutor.class).execute(promoNotifyInsurableTask);

                        Logger.info("[notifyFromOA]异步通知成功:{}", JSON.toJSONString(notifyMsg));
                        return Pair.of(Boolean.TRUE, "suss");
                    } catch (Exception e) {
                        //回滚事务
                        transactionStatus.setRollbackOnly();
                        Logger.error("[notifyFromOA]异步通知-更新任务时发生异常：", e);
                        return Pair.of(Boolean.FALSE, e.getMessage());
                    }
                }
            });

            if (!dbOperateRst.getLeft()) {
                return Boolean.FALSE;
            }
        }

        return Boolean.TRUE;
    }

    @Override
    public OANotifyDTO queryStatus(BonusPayQueryReq queryReq) {
        //01-查询申请记录
        List<RewardApply> records = rewardApplyService.queryRewardApplyList(queryReq.getTradeNo(), queryReq.getBizType(), queryReq.getTransBillDate(), queryReq.getActivityNo());
        if (CollectionUtils.isEmpty(records)) {
            return null;
        }

        //02-数据组装
        RewardApply rewardApplyRecord = records.stream().filter(record -> !StringUtils.isEmpty(record.getExt1()) && !StringUtils.isEmpty(record.getExt2())).findFirst().orElse(null);
        return buildOANotifyDTO(rewardApplyRecord);
    }

    public OANotifyDTO buildOANotifyDTO(RewardApply rewardApplyRecord) {
        //01-校验

        //02-组装
        HashMap<String, String> ext1 = JSON.parseObject(rewardApplyRecord.getExt1(), new TypeReference<HashMap<String, String>>() {
        });
        HashMap<String, String> ext2 = JSON.parseObject(rewardApplyRecord.getExt2(), new TypeReference<HashMap<String, String>>() {
        });
        OANotifyDTO oaNotifyDTO = new OANotifyDTO();
        BeanUtils.copyProperties(rewardApplyRecord, oaNotifyDTO);
        oaNotifyDTO.setTradeNo(rewardApplyRecord.getRequestNo());
        oaNotifyDTO.setRequestId(rewardApplyRecord.getBatchNo());
        oaNotifyDTO.setBizType(rewardApplyRecord.getBusiType());
        oaNotifyDTO.setTransBillDate(rewardApplyRecord.getTransBillDate());
        oaNotifyDTO.setTransTime(rewardApplyRecord.getTradeTime());
        oaNotifyDTO.setActivityNo(rewardApplyRecord.getActivityNo());
        if(!StringUtils.isEmpty(ext2.get("workflowNo"))){
            oaNotifyDTO.setWorkflowId(ext2.get("workflowNo"));
        }
        oaNotifyDTO.setStatus(rewardApplyRecord.getStatus());
        oaNotifyDTO.setStatusDesc(PromoTradeStatusEnum.getByCode(rewardApplyRecord.getStatus()).getCodeDesc());
        if(!StringUtils.isEmpty(ext2.get("oaErrorMsg"))){
            oaNotifyDTO.setErrorMsg(ext2.get("oaErrorMsg"));
        }

        return oaNotifyDTO;
    }

    /**
     *
     * @param customerCode BONUS-奖励金不带NM前缀  XD_BALANCE-饷店余额-必须带NM前缀
     * @param customerType 默认NM
     * @param bizNo
     * @param amount
     * @param remark
     * @param businessType  月勤奖/赋能营等的枚举值
     * @param accountType XD_BALANCE-饷店余额(异步响应结果) BONUS-奖励金(同步响应结果)
     * @return
     */

    @Override
    public Result<Void> monthlyDiligenceEmpowerAwardSettle(String customerCode, String customerType, String bizNo,
                                                           BigDecimal amount, String remark, String businessType, String accountType,Integer userGrade) {
        if (org.apache.commons.lang3.StringUtils.equals("XD_BALANCE", accountType)) {
            if(!ObjectUtils.isEmpty(userGrade)){
                String authName =MemberGrade.getAuthName(userGrade);
                //乐税走饷店余额+提现
                if(!StringUtils.isEmpty(authName) && authName.trim().equalsIgnoreCase("leshui")){
                    return monthlyDiligenceEmpowerAwardSettleToXDBalance(customerCode, customerType, bizNo, amount, remark, businessType,userGrade);
                }
            }
            //其他直接下发到饷店余额
            return monthlyDiligenceEmpowerAwardSettle2AC(customerCode, customerType, bizNo, amount, remark, businessType,AccountKeyConstants.NM.getName(),DetailTypeConstants.TRADE_TYPE_051.getName());
        } else if (org.apache.commons.lang3.StringUtils.equals("BONUS", accountType)) {
            return monthlyDiligenceEmpowerAwardSettleToBonusBalance(customerCode, customerType, bizNo, amount, remark, businessType,userGrade);
        } else {
            return Result.error(IErrorCode.ARGUMENT_ERROR, "不支持的结算账户");
        }
    }

    private Result<Void> monthlyDiligenceEmpowerAwardSettleToXDBalance(String customerCode, String customerType, String bizNo,
                                                           BigDecimal amount, String remark, String businessType,Integer userGrade){
        try {
            //调用平安查询绑卡信息
            com.akucun.fps.common.entity.Query<PinganCardQueryDO> var1 = new com.akucun.fps.common.entity.Query<>();
            PinganCardQueryDO pinganCardQueryDO = new PinganCardQueryDO();
            pinganCardQueryDO.setCustomerCode(customerCode);
            pinganCardQueryDO.setCustomerType(customerType);
            pinganCardQueryDO.setStatus(BankCardStatusConstants.BINDACTIVE.getName());
            var1.setData(pinganCardQueryDO);
            com.akucun.fps.common.entity.ResultList<PinganCardVO> pinganCardList = settlementServiceApi.selectCardByCustomerCodePage(var1);
            //校验绑卡信息是否为空
            if(null == pinganCardList || null == pinganCardList.getDatalist() || com.aikucun.common2.utils.CollectionUtils.isEmpty(pinganCardList.getDatalist())){
                return Result.error(ResponseEnum.BIND_CARD_ERROR.getCode(), ResponseEnum.BIND_CARD_ERROR.getMessage());
            }

            String shopId = null;
            String identifyNo = null;
            if (CustomerType.NM.getName().equals(customerType)) {
                //使用resellerId查询店主基础信息获取店主ackMemberId
                SellerResp sellerResp = sellerInfoFeignHelp.queryBaseSellerById(org.apache.commons.lang3.StringUtils.replace(customerCode, customerType, ""));

                //查询用户实名身份认证信息
                MemberIdentityVerifyQueryReqDTO idInfoReq = new MemberIdentityVerifyQueryReqDTO();
                idInfoReq.setUserType(CurrentRoleEnum.SHOP_OWNER.getCode());
                //店主用的是 akc_member_user_info表里的user_id 也就是app的memberuserid
                idInfoReq.setUserId(sellerResp.getAkcMemberId());
                idInfoReq.setChannel("bc41849ca6b4483ab461b2638f93dd5e");//无用但必传参数
                Logger.info("查询店主实名信息，请求：{}", DataMask.toJSONString(idInfoReq));
                com.akucun.common.Result<MemberIdentityVerifyRespDTO> idInfoResult = feignMemberIndentityService.queryIdentityInfoByUserId(idInfoReq);
                Logger.info("查询店主实名信息，请求：{}，返回：{}", DataMask.toJSONString(idInfoReq), DataMask.toJSONString(idInfoResult));
                if(!idInfoResult.isSuccess() || null == idInfoResult.getData()
                        || org.apache.commons.lang3.StringUtils.isBlank(idInfoResult.getData().getIDNo())) {
                    throw new AccountProxyException(ResponseEnum.SYSTEM_EXCEPTION, "用户未实名认证");
                }
                identifyNo = idInfoResult.getData().getIDNo();
                shopId = String.valueOf(sellerResp.getShopId());
            } else {
                throw new AccountProxyException(ResponseEnum.SYSTEM_EXCEPTION, "当前类型用户不支持");
            }

            List<PinganCardVO> datalist = (List<PinganCardVO>) pinganCardList.getDatalist();
            PinganCardVO pinganCardVO = datalist.get(0);
            com.akucun.account.proxy.task.request.FinTaskAcceptRequest finTaskAcceptRequest = new com.akucun.account.proxy.task.request.FinTaskAcceptRequest();
            finTaskAcceptRequest.setRequestNo(UUID.randomUUID().toString());
            finTaskAcceptRequest.setRequestPlatform("ACCOUNT");
            finTaskAcceptRequest.setCreateUserId(customerCode);
            finTaskAcceptRequest.setCreateUserType(customerType);
            finTaskAcceptRequest.setBizCategory("MD_EMP_AWARD_SETTLE");
            finTaskAcceptRequest.setBizNo(bizNo);
            finTaskAcceptRequest.setSource("account-proxy");

            Map<String, String> paramMap = new HashedMap();
            paramMap.put("customerCode", customerCode);
            paramMap.put("customerType", customerType);
            paramMap.put("customerName", pinganCardVO.getCustomerName());
            paramMap.put("withdrawNo", AccountNoUtils.generateWithdrawNo());
            paramMap.put("amount", amount.toString());
            paramMap.put("bankNo", pinganCardVO.getBankCardCode());
            paramMap.put("tranFee", BigDecimal.ZERO.toPlainString());
            paramMap.put("remark", remark);
            paramMap.put("businessType", businessType);
            paramMap.put("identifyNo", identifyNo);
            paramMap.put("bankName", pinganCardVO.getBankName());
            paramMap.put("shopId", shopId);
            finTaskAcceptRequest.setBizInfo(paramMap);

            Logger.info("店主月勤奖/励新奖发放平安渠道自动提现请求：{}，withdrawNo：{}", JSON.toJSONString(finTaskAcceptRequest), bizNo);
            com.aikucun.common2.base.Result<Void> finTaskVOResult = finClearingCoreFacadeHelp.accept(finTaskAcceptRequest);
            Logger.info("店主月勤奖/励新奖发放平安渠道自动提现响应：{}，withdrawNo：{}", JSON.toJSONString(finTaskVOResult), bizNo);
            if(!finTaskVOResult.getSuccess()) {
                return Result.error(finTaskVOResult.getCode(), finTaskVOResult.getMessage());
            }
            return Result.success();
        } catch (AccountProxyException e) {
            return Result.error(e.getErrorCode(), e.getErrorMsg());
        } catch (Exception e) {
            return Result.error(e);
        }
    }

    private Result<Void> monthlyDiligenceEmpowerAwardSettleToBonusBalance(String customerCode, String customerType, String bizNo,
                                                                                 BigDecimal amount, String remark, String businessType,Integer userGrade){
        TradeInfo tradeInfo = new TradeInfo();
        tradeInfo.setCustomerCode(customerCode);
        tradeInfo.setCustomerName(customerCode);
        tradeInfo.setAccountTypeKey(AccountKeyConstants.AWARD.getName());
        tradeInfo.setTradeType(DetailTypeConstants.TRADE_TYPE_005.getName());
        tradeInfo.setSourceBillNo(bizNo);
        tradeInfo.setTradeNo(bizNo);
        tradeInfo.setAmount(amount);
        tradeInfo.setRemark(remark);
        com.akucun.common.Result<Void> result = accountService.dealTrade(tradeInfo);
        if (!result.isSuccess()) {
            return Result.error(result.getCode(), result.getMessage());
        }
        return Result.success();
    }

    private Result<Void> monthlyDiligenceEmpowerAwardSettle2AC(String customerCode, String customerType, String bizNo,
            BigDecimal amount, String remark, String businessType,String accountTypeKey,String tradeType){
        TradeInfo tradeInfo = new TradeInfo();
        tradeInfo.setCustomerCode(customerCode);
        tradeInfo.setCustomerName(customerCode);
        tradeInfo.setAccountTypeKey(accountTypeKey);
        tradeInfo.setTradeType(tradeType);
        tradeInfo.setSourceBillNo(bizNo);
        tradeInfo.setTradeNo(bizNo);
        tradeInfo.setAmount(amount);
        tradeInfo.setRemark(remark);
        com.akucun.common.Result<Void> result = accountService.dealTrade(tradeInfo);
        if (!result.isSuccess()) {
            return Result.error(result.getCode(), result.getMessage());
        }
        return Result.success();
    }
}
