package com.akucun.account.proxy.service.acct.impl;

import com.aikucun.common2.base.Result;
import com.aikucun.common2.utils.datamasking.DataMask;
import com.akucun.account.proxy.client.pingan.MerchantClient;
import com.akucun.account.proxy.common.constant.CommonConstants;
import com.akucun.account.proxy.common.constant.CustomerType;
import com.akucun.account.proxy.common.enums.AccountPropertyType;
import com.akucun.account.proxy.common.enums.ResponseEnum;
import com.akucun.account.proxy.common.enums.ResultStatus;
import com.akucun.account.proxy.common.utils.AccountSequenceManager;
import com.akucun.account.proxy.common.utils.AccountUtils;
import com.akucun.account.proxy.dao.mapper.AccountTenantCustomerMapper;
import com.akucun.account.proxy.dao.model.AccountOpTrade;
import com.akucun.account.proxy.facade.stub.others.dto.req.AccountInfoReq;
import com.akucun.account.proxy.facade.stub.others.dto.req.AccountUpgradePreCheckRequest;
import com.akucun.account.proxy.facade.stub.others.dto.req.AccountUpgradeRequest;
import com.akucun.account.proxy.facade.stub.others.dto.res.AccountInfoResp;
import com.akucun.account.proxy.facade.stub.others.dto.res.AccountUpgradeResp;
import com.akucun.account.proxy.service.acct.AccountService;
import com.akucun.account.proxy.service.acct.AccountUpgradeService;
import com.akucun.account.proxy.service.acct.bo.AccountUpgradeBO;
import com.akucun.account.proxy.service.acct.repository.AccountOpTradeRepository;
import com.akucun.cloud.security.CodeUtils;
import com.akucun.fps.pingan.client.model.po.PinganAccount;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.mengxiang.base.common.log.Logger;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


/**
 * <AUTHOR>
 */
@Service
public class AccountUpgradeServiceImpl implements AccountUpgradeService {

    @Resource
    private MerchantClient merchantClient;
    @Autowired
    private AccountService accountService;
    @Autowired
    private AccountOpTradeRepository accountOpTradeRepository;
    @Autowired
    private AccountSequenceManager accountSequenceManager;
    @Autowired
    private AccountTenantCustomerMapper accountTenantCustomerMapper;
    /**
     * 单位：秒
     */
    @Value("${account.proxy.lockTime:86400}")
    private long lockTime;
    @Value("${account.upgrade.recent.check.time:60}")
    private int recentUpgradeCheckTime;

    @Override
    public Result<Void> upgradePreCheck(AccountUpgradePreCheckRequest request) {
        Logger.info("AccountUpgradeServiceImpl upgradePreCheck, request: {}", DataMask.toJSONString(request));
        try {
            //非店主店长不支持
            if(!CustomerType.NM.getName().equals(request.getCustomerType()) && !CustomerType.NMDL.getName().equals(request.getCustomerType())){
                Logger.warn("AccountUpgradeServiceImpl upgradePreCheck, 非店主店长不支持账户实名变更，{}-{}", request.getCustomerType(), request.getCustomerCode());
                return Result.error(ResponseEnum.UPGRADE_CHECK_ERROR.getCode(), ResponseEnum.UPGRADE_CHECK_ERROR.getMessage());
            }
            if(StringUtils.isBlank(request.getCustomerCode())) {
                Logger.warn("AccountUpgradeServiceImpl upgradePreCheck, customerCode不能为空，{}-{}", request.getCustomerType(), request.getCustomerCode());
                return Result.error(ResponseEnum.UPGRADE_CHECK_ERROR.getCode(), ResponseEnum.UPGRADE_CHECK_ERROR.getMessage());
            }

            AccountInfoReq accountInfoReq = new AccountInfoReq();
            accountInfoReq.setCustomerCode(request.getCustomerCode());
            accountInfoReq.setCustomerType(request.getCustomerType());
            List<AccountInfoResp> respList = accountService.queryAccountInfo(Arrays.asList(accountInfoReq));
            if(CollectionUtils.isEmpty(respList)) {
                Logger.warn("AccountUpgradeServiceImpl upgradePreCheck, 未查询到账本，{}-{}", request.getCustomerType(), request.getCustomerCode());
                return Result.error(ResponseEnum.UPGRADE_CHECK_ERROR.getCode(), ResponseEnum.UPGRADE_CHECK_ERROR.getMessage());
            }

            if(!accountService.isTenantCustomer(request.getCustomerCode(), request.getCustomerType())) {//普通饷店店主店长可直接实名变更
                Logger.info("AccountUpgradeServiceImpl upgradePreCheck, 非SAAS店主店长，校验通过，{}-{}", request.getCustomerType(), request.getCustomerCode());
                return Result.success();
            }

            Result<PinganAccount> pinganResult = merchantClient.selectPinganAccount(request.getCustomerCode(), request.getCustomerType());
            if(!pinganResult.getSuccess()) {
                Logger.error("AccountUpgradeServiceImpl upgradePreCheck, 查询平安账户异常，{}-{}", request.getCustomerType(), request.getCustomerCode());
                return Result.error(ResponseEnum.UPGRADE_CHECK_EXCEPTION.getCode(), ResponseEnum.UPGRADE_CHECK_EXCEPTION.getMessage());
            }
            if(pinganResult.getData() == null) {
                Logger.warn("AccountUpgradeServiceImpl upgradePreCheck, 未查询到平安账户，{}-{}", request.getCustomerType(), request.getCustomerCode());
                return Result.error(ResponseEnum.UPGRADE_CHECK_ERROR.getCode(), ResponseEnum.UPGRADE_CHECK_ERROR.getMessage());
            }

            if(accountService.hasProcessingTrade(request.getCustomerCode(), request.getCustomerType())) {
                Logger.info("AccountUpgradeServiceImpl upgradePreCheck, 存在处理中的交易，校验不通过，{}-{}", request.getCustomerType(), request.getCustomerCode());
                return Result.error(ResponseEnum.UPGRADE_HAS_PROCESSING_TRADE.getCode(), ResponseEnum.UPGRADE_HAS_PROCESSING_TRADE.getMessage());
            }

            if(respList.get(0).getAmount().compareTo(BigDecimal.ZERO) == 0) {
                Logger.info("AccountUpgradeServiceImpl upgradePreCheck, 账户余额为0，校验通过，{}-{}", request.getCustomerType(), request.getCustomerCode());
                return Result.success();
            } else {
                Logger.info("AccountUpgradeServiceImpl upgradePreCheck, 账户余额不为0，校验不通过，{}-{}", request.getCustomerType(), request.getCustomerCode());
                return Result.error(ResponseEnum.UPGRADE_AMOUNT_REMAIN.getCode(), ResponseEnum.UPGRADE_AMOUNT_REMAIN.getMessage());
            }
//            Boolean hasBind = accountService.hasBindRecord(request.getCustomerCode(), request.getCustomerType());
//            if(hasBind == null) {
//                Logger.error("AccountUpgradeServiceImpl upgradePreCheck, 查询绑卡记录异常，{}-{}", request.getCustomerType(), request.getCustomerCode());
//                return Result.error(ResponseEnum.UPGRADE_CHECK_EXCEPTION.getCode(), ResponseEnum.UPGRADE_CHECK_EXCEPTION.getMessage());
//            }
//            if(hasBind) {
//                Logger.info("AccountUpgradeServiceImpl upgradePreCheck, 账户余额不为0且有绑卡记录，校验不通过，{}-{}", request.getCustomerType(), request.getCustomerCode());
//                return Result.error(ResponseEnum.UPGRADE_AMOUNT_REMAIN.getCode(), ResponseEnum.UPGRADE_AMOUNT_REMAIN.getMessage());
//            } else {
//                Logger.info("AccountUpgradeServiceImpl upgradePreCheck, 账户余额不为0但未绑过卡，校验通过，{}-{}", request.getCustomerType(), request.getCustomerCode());
//                return Result.success();
//            }
        } catch (Exception e) {
            Logger.error("AccountUpgradeServiceImpl upgradePreCheck, 异常，{}-{}", request.getCustomerType(), request.getCustomerCode(), e);
            return Result.error(ResponseEnum.UPGRADE_CHECK_EXCEPTION.getCode(), ResponseEnum.UPGRADE_CHECK_EXCEPTION.getMessage());
        }
    }

    @Override
    public Result<Void> accountUpgrade(AccountUpgradeBO accountUpgradeBO) {
        Logger.info("AccountUpgradeServiceImpl accountUpgrade, accountUpgradeBO: {}", DataMask.toJSONString(accountUpgradeBO));
        try {
            //检查相同客户编码和类型的账户，是否有处理中的升级记录
            if (hasProcessingRecord(accountUpgradeBO.getCustomerCode(), accountUpgradeBO.getCustomerType())) {
                Logger.info("AccountUpgradeServiceImpl accountUpgrade, 升级中，请求返回成功");
                return Result.success();//存在处理中的升级，则无需重复升级
            }

            boolean isTenantCustomer = accountService.isTenantCustomer(accountUpgradeBO.getCustomerCode(), accountUpgradeBO.getCustomerType());

            AccountUpgradePreCheckRequest preCheckRequest = new AccountUpgradePreCheckRequest();
            preCheckRequest.setCustomerType(accountUpgradeBO.getCustomerType());
            preCheckRequest.setCustomerCode(accountUpgradeBO.getCustomerCode());
            Result<Void> preCheckResult = upgradePreCheck(preCheckRequest);
            if(!preCheckResult.getSuccess()) {
                return preCheckResult;
            } else {
                if(isTenantCustomer) {
                    //锁定账户
                    // accountService.lockAccount(AccountUtils.getSellerCode(accountUpgradeBO.getCustomerCode(), accountUpgradeBO.getCustomerType()), accountUpgradeBO.getCustomerType(), lockTime);
                }
            }

            AccountOpTrade accountOpTrade = convertToAccountTrade(accountUpgradeBO);
            //持久化
            accountOpTradeRepository.saveAccountOpTrade(accountOpTrade);
            return Result.success();
        } catch (Exception e) {
            Logger.error("AccountUpgradeServiceImpl accountUpgrade, exception: ", e);
            return Result.error(ResponseEnum.SYSTEM_EXCEPTION.getCode(), ResponseEnum.SYSTEM_EXCEPTION.getMessage());
        }
    }

    /**
     * 查询同一用户有无处理中的记录
     *
     * @param customerCode
     * @param customerType
     * @return
     */
    private boolean hasProcessingRecord(String customerCode, String customerType) {
        List<AccountOpTrade> list = accountOpTradeRepository.queryProcessingAccountOpTrade(customerCode, customerType);
        return CollectionUtils.isNotEmpty(list) && list.size() > 0;
    }

    /**
     * 查询账户升级状态
     * @param list
     * @return
     */
    @Override
    public Result<List<AccountUpgradeResp>> queryUpgradeStatus(List<AccountUpgradeRequest> list) {
        List<AccountUpgradeResp> respList = new ArrayList<>();
        for (AccountUpgradeRequest request : list) {
            AccountUpgradeResp resp = new AccountUpgradeResp();
            AccountOpTrade accountOpTrade = accountOpTradeRepository.queryLatestAccountOpTrade(request.getCustomerCode(), request.getCustomerType());
            if (accountOpTrade != null) {
                BeanUtils.copyProperties(accountOpTrade, resp);
                if (isFinalStatus(accountOpTrade.getStatus())) {
                    resp.setUpgradeStatus(accountOpTrade.getStatus());
                } else {
                    resp.setUpgradeStatus(ResultStatus.P.getCode());
                }
            }
            respList.add(resp);
        }
        Logger.info("AccountUpgradeServiceImpl queryUpgradeStatus respList:{}", DataMask.toJSONString(respList));
        return Result.success(respList);
    }

    @Override
    public boolean hasRecentUpgradeProcessing(String customerCode, String customerType) {
        return accountOpTradeRepository.hasRecentProcessingAccountOpTrade(customerCode, customerType, recentUpgradeCheckTime);
    }

    private boolean isFinalStatus(String status) {
        return ResultStatus.S.getCode().equals(status) || ResultStatus.F.getCode().equals(status);
    }

    private AccountOpTrade convertToAccountTrade(AccountUpgradeBO accountUpgradeBO) {
        AccountOpTrade accountTrade = AccountOpTrade.builder().build();
        BeanUtils.copyProperties(accountUpgradeBO, accountTrade);
        accountTrade.setAccountProperty(AccountPropertyType.MERCHANTS.getCode());
        accountTrade.setOrderNo(accountSequenceManager.generateNo());
        accountTrade.setStatus(ResultStatus.I.getCode());
        if (StringUtils.isNotEmpty(accountUpgradeBO.getMobile())) {
            accountTrade.setMobile(CodeUtils.encrypt(accountUpgradeBO.getMobile()).getData());
        }
        if (StringUtils.isNotEmpty(accountUpgradeBO.getUpgradeType())) {
            JSONObject json = new JSONObject();
            json.put(CommonConstants.UPGRADE_TYPE, accountUpgradeBO.getUpgradeType());
            accountTrade.setReserve(json.toJSONString());
        }
        return accountTrade;
    }
}
