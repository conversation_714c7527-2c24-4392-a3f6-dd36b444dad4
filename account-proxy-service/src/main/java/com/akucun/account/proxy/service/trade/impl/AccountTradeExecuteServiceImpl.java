package com.akucun.account.proxy.service.trade.impl;

import com.aikucun.common2.utils.datamasking.DataMask;
import com.akucun.account.proxy.common.enums.ResponseEnum;
import com.akucun.account.proxy.common.enums.ResultStatus;
import com.akucun.account.proxy.common.exception.AccountProxyException;
import com.akucun.account.proxy.service.common.bo.AccountExecContext;
import com.akucun.account.proxy.service.common.bo.AccountExecStepContext;
import com.akucun.account.proxy.service.common.step.Step;
import com.akucun.account.proxy.service.trade.AccountTradeExecuteService;
import com.akucun.account.proxy.service.trade.bo.AccountTradeBO;
import com.akucun.account.proxy.service.trade.bo.AccountTradeResp;
import com.alibaba.fastjson.JSON;
import com.mengxiang.base.common.log.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * @Author: silei
 * @Date: 2020/12/2
 * @desc:
 */
@Service
public class AccountTradeExecuteServiceImpl implements AccountTradeExecuteService {

    @Autowired
    ApplicationContext applicationContext;

    @Override
    public AccountTradeResp executeTrade(AccountTradeBO req, boolean isResume) {
        AccountTradeResp resp = new AccountTradeResp();
        AccountExecContext accountExecContext;
        Logger.info("AccountTradeExecuteServiceImpl execute 入参:{}", DataMask.toJSONString(req));
        try {
            accountExecContext = new AccountExecContext(applicationContext, req, isResume);
            if (ResultStatus.S.getCode().equals(accountExecContext.getAccountTradeBO().getStatus())
                    || ResultStatus.F.getCode().equals(accountExecContext.getAccountTradeBO().getStatus())) {
                resp = buildTradeResp(accountExecContext);
                return resp;
            }
            //执行
            startExecuteStep(accountExecContext);
            Logger.info("AccountTradeExecuteServiceImpl 交易完成 执行上下文 accountExecContext：" + accountExecContext);
            if (accountExecContext.isEnd()) {
                accountExecContext.updateTradeAndPersist();
            } else {
                // 不改状态，只更新返回信息
                accountExecContext.updateTradeRespCode();
            }
            resp = buildTradeResp(accountExecContext);
        } catch (Exception e) {
            Logger.error("AccountTradeExecuteServiceImpl execute Exception：{}", e);
            resp.setStatus(ResultStatus.P.getCode());
            resp.setReplyCode(ResponseEnum.PROCESS.getCode() + "");
            resp.setReplyMsg(ResponseEnum.PROCESS.getMessage());
        }
        Logger.info("AccountTradeExecuteServiceImpl execute resp:{}", resp);
        return resp;
    }

    private void startExecuteStep(AccountExecContext accountExecContext) {

        Step step = accountExecContext.getCurrentStep();
        Logger.info("AccountTradeExecuteServiceImpl 当前执行step：" + step + "，当前执行上下文：" + accountExecContext);
        if (Objects.isNull(step)) {
            throw new AccountProxyException("account step为空");
        }
        execStep(step, accountExecContext);
    }

    private void execStep(Step step, AccountExecContext accountExecContext) {
        //具体执行
        step.exec(accountExecContext);
        AccountExecStepContext accountExecStepContext = accountExecContext.getLatestAccountStepExecContext();
        if (!accountExecStepContext.isEnd()) {
            //当前流程异常，无法进行下一步
            Logger.info("AccountTradeExecuteServiceImpl====流程已结束=====");
        } else {
            Step nextStep = accountExecContext.getNextStep();
            if (!Objects.isNull(nextStep)) {
                accountExecContext.setCurrentStep(nextStep);
                execStep(nextStep, accountExecContext);
            } else {
                Logger.info("AccountTradeExecuteServiceImpl 获取NextStep为空，step流程已结束,{}", accountExecContext);
            }
        }
    }

    private AccountTradeResp buildTradeResp(AccountExecContext accountExecContext) {
        AccountTradeResp resp = new AccountTradeResp();
        AccountTradeBO tradeBO = accountExecContext.getAccountTradeBO();

        resp.setSourceNo(tradeBO.getSourceNo());
        resp.setStatus(tradeBO.getStatus());
        resp.setReplyCode(tradeBO.getReplyCode());
        resp.setReplyMsg(tradeBO.getReplyMsg());

        return resp;
    }
}
