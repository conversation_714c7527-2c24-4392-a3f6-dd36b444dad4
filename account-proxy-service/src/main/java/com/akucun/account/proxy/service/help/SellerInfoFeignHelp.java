package com.akucun.account.proxy.service.help;

import com.akucun.account.proxy.common.exception.AccountProxyException;
import com.mengxiang.base.common.model.result.Result;
import com.mengxiang.member.service.facade.common.feign.seller.SellerInfoFeign;
import com.mengxiang.member.service.facade.common.response.SellerResp;
import com.mengxiang.member.service.facade.common.response.seller.SellerBaseResp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
public class SellerInfoFeignHelp {
	
	@Autowired
	private SellerInfoFeign sellerInfoFeign;
	
	public SellerBaseResp queryBaseSellerById(Long sellerId) {
		Result<SellerBaseResp>  resp =  sellerInfoFeign.queryBaseSellerById(sellerId);
		if(Objects.isNull(resp)||!resp.isSuccess()|| Objects.isNull(resp)) {
			throw new AccountProxyException("查询店主信息失败");
		}
		return resp.getData();
	}

	public SellerResp queryBaseSellerById(String resellerId) {
		Result<SellerResp> resp = sellerInfoFeign.querySellerByResellerId(resellerId);
		if(Objects.isNull(resp) || !resp.isSuccess() || Objects.isNull(resp.getData())) {
			throw new AccountProxyException("查询店主信息失败");
		}
		return resp.getData();
	}
	

}
