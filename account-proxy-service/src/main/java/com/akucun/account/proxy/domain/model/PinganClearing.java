package com.akucun.account.proxy.domain.model;

import com.aikucun.common2.log.Logger;
import com.akucun.account.center.common.constants.MagicNumber;
import com.akucun.account.center.common.util.DateUtils;
import com.akucun.account.proxy.service.common.SpringContextHolder;
import com.akucun.fps.account.client.constants.DetailTypeConstants;
import com.akucun.fps.common.entity.Result;
import com.akucun.fps.pingan.client.vo.CashCreditVO;
import com.akucun.fps.pingan.feign.api.assets.AssetsServiceApi;
import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 分账
 */
@Service
public class PinganClearing extends PinganAdjust {

    @Override
    public Pair<Integer,String> done() {
        BigDecimal maxLimitAmount=new BigDecimal(this.getMaxPinganClearingLimit());
        if(this.getAmount().compareTo(maxLimitAmount)==1){
            return Pair.of(88,"超过最大可调帐上限:"+maxLimitAmount);
        }
        //构建请求体
        CashCreditVO cashCredit = new CashCreditVO();
        cashCredit.setThirdCustId(this.getCustomerCode());
        cashCredit.setCustomerType(this.getCustomerType());
        cashCredit.setTranAmount(this.getAmount().multiply(new BigDecimal(100)).intValue());
        cashCredit.setTranFee(MagicNumber.int_0);
        cashCredit.setBillNo(DateUtils.formatDate(new Date(), "yyyyMMddHHmmssSSS"));
        cashCredit.setBillType(DetailTypeConstants.FILL.getName());
        cashCredit.setRemark("人工差额补款");
        cashCredit.setToAdvanceFundSubAcctNoClosedSwitch(true);

        if(cashCredit.getThirdCustId().isEmpty()||cashCredit.getCustomerType().isEmpty()|| ObjectUtils.isEmpty(cashCredit.getTranAmount())||
                ObjectUtils.isEmpty(cashCredit.getTranFee())||cashCredit.getBillNo().isEmpty()||cashCredit.getBillType().isEmpty()||cashCredit.getRemark().isEmpty()
        ){
            return Pair.of(9,"服务异常请重试");
        }
        //请求
        Logger.info("平安分账处理开始，参数CashCredit:{}", JSON.toJSONString(cashCredit));
        AssetsServiceApi assetsServiceApi = SpringContextHolder.getBean(AssetsServiceApi.class);
        Result<String> result = assetsServiceApi.credit(cashCredit);
        Logger.info("平安分账处理结束，参数CashCredit:{}, 响应result:{}",JSON.toJSONString(cashCredit), JSON.toJSONString(result));

        //返回
        if (result.isSuccess()) {
            return Pair.of( 0 , "操作成功");
        } else {
            return Pair.of( result.getErrorCode() , result.getErrorMessage());
        }

    }
}
