package com.akucun.account.proxy.service.postaction.task;

import com.aikucun.common2.base.Result;
import com.aikucun.common2.log.Logger;
import com.akucun.account.proxy.common.utils.Results;
import com.akucun.account.proxy.common.enums.PostActionTypes;
import com.akucun.account.proxy.dao.model.PostActionItem;
import com.akucun.account.proxy.service.postaction.common.AbsPostActionExecutor;
import com.akucun.fps.common.util.GsonUtils;
import com.akucun.fps.pingan.client.vo.RevokeCreditVO;
import com.akucun.fps.pingan.feign.api.assets.AssetsServiceApi;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @Author: silei
 * @Date: 2021/3/11
 * @desc: 分账撤销延迟处理任务
 */
@Component
public class DelayRevokeCreditTask extends AbsPostActionExecutor {

    @Resource
    private AssetsServiceApi assetsServiceApi;


    @XxlJob("delayRevokeCreditTask")
    public ReturnT<String> execute(String param) {
        return this.executeEntrance();
    }

    @Override
    protected String getActionType() {
        return PostActionTypes.PINGAN_DELAY_REVOKE.name();
    }

    @Override
    public Result<Void> execute(PostActionItem item) {
        try {
            RevokeCreditVO paramObject = GsonUtils.getInstance().fromJson(item.getParam(), RevokeCreditVO.class);
            com.akucun.fps.common.entity.Result<String> revokeResult = assetsServiceApi.revokeCredit(paramObject);
            if (!revokeResult.isSuccess()) {
                String msg = "提现撤销失败：" + revokeResult.getErrorMessage();
                Logger.error(msg);
                return Results.error(msg);
            }
        } catch (Exception e) {
            Logger.error("提现撤销调用异常：", e);
            return Results.error("提现撤销调用异常：" + e.getMessage());
        }
        return Result.success();
    }
}
