package com.akucun.account.proxy.service.transfer.convert;

import com.aikucun.common2.utils.StringUtils;
import com.akucun.account.proxy.common.constant.CommonConstants;
import com.akucun.account.proxy.dao.model.PaymentTransfer;
import com.akucun.account.proxy.facade.stub.enums.TransferCheckNameEnum;
import com.akucun.account.proxy.facade.stub.enums.TransferStatusEnum;
import com.akucun.account.proxy.facade.stub.others.dto.req.PaymentTransferReq;
import com.mengxiang.base.common.log.Logger;
import com.mengxiang.base.common.sequence.spring.SequenceGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.net.InetAddress;

/**
 * <AUTHOR>
 * @date 2021/01/29 10:00
 */
@Component
public class PaymentTransferConvert {

    @Autowired
    private SequenceGenerator sequenceGenerator;

    public PaymentTransfer buildPaymentTransfer(PaymentTransferReq request, String mchCode) {
        PaymentTransfer transfer = new PaymentTransfer();
        // 业务方来源编号
        transfer.setSourceCode(request.getSourceCode());
        // 业务方付款流水单号
        transfer.setSourceNo(request.getSourceNo());
        // 付款交易单号
        transfer.setTradeNo(CommonConstants.TRANSFER_SOURCE_NO_PREFIX + sequenceGenerator.getSequence());
        transfer.setOpenId(request.getOpenId());
        // 付款人客户编号
        transfer.setCustomerCode(request.getCustomerCode());
        // 付款人类型
        transfer.setCustomerType(request.getCustomerType());
        // 付款人姓名
        String customerName = StringUtils.isNullOrEmpty(request.getCustomerName()) ? request.getCustomerCode() : request.getCustomerName();
        transfer.setCustomerName(customerName);
        // 付款金额，单位：分
        transfer.setAmount(request.getAmount().setScale(2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).longValue());
        // 付款人所在渠道编码
        transfer.setChannelCode(request.getChannelCode());
        // 付款状态
        transfer.setStatus(TransferStatusEnum.P.getCode());
        // 终端IP
        String terminalIp = StringUtils.isNullOrEmpty(request.getTerminalIp()) ? getLocalIp() : request.getTerminalIp();
        transfer.setTerminalIp(terminalIp);
        // 是否校验姓名
        transfer.setCheckName(request.getCheckName() ? TransferCheckNameEnum.FORCE_CHECK.getCode() : TransferCheckNameEnum.NO_CHECK.getCode());
        transfer.setTransferType(request.getTransferType().getCode());
        // 付款商户号
        transfer.setMchCode(mchCode);
        // 付款备注
        transfer.setRemark(request.getRemark());
        // 租户ID
        transfer.setTenantId(request.getTenantId());
        return transfer;
    }

    private String getLocalIp() {
        String ip = "127.0.0.1";
        try {
            InetAddress inetAddress = InetAddress.getLocalHost();
            //获取本机ip
            ip = inetAddress.getHostAddress();
        } catch (Exception e) {
            Logger.warn("获取本机IP异常：", e);
        }
        return ip;
    }

}
