package com.akucun.account.proxy.service.postaction.task;

import com.aikucun.common2.base.Result;
import com.aikucun.common2.utils.datamasking.DataMask;
import com.akucun.account.proxy.common.enums.PostActionTypes;
import com.akucun.account.proxy.dao.model.PostActionItem;
import com.akucun.account.proxy.facade.stub.others.dto.req.PaymentTransferReq;
import com.akucun.account.proxy.service.postaction.common.AbsPostActionExecutor;
import com.akucun.account.proxy.service.transfer.PayTransferService;
import com.akucun.account.proxy.service.transfer.PaymentTransferService;
import com.akucun.fps.common.util.GsonUtils;
import com.akucun.fps.pingan.client.vo.WithdrawVO;
import com.alibaba.fastjson.JSON;
import com.mengxiang.base.common.log.Logger;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * 微信异步提现任务
 */
@Component
public class WechatDelayWithdrawTask extends AbsPostActionExecutor {

    @Autowired
    private PayTransferService payTransferService;

    @XxlJob("wechatDelayWithdrawTask")
    public ReturnT<String> execute(String param) {
        return this.executeEntrance();
    }

    @Override
    protected String getActionType() {
        return PostActionTypes.WECHAT_DELAY_WITHDRAW.getName();
    }

    @Override
    public Result<Void> execute(PostActionItem item) {
        Result<Void> result = Result.success();
        try {
            PaymentTransferReq paymentTransferReq = GsonUtils.getInstance().fromJson(item.getParam(), PaymentTransferReq.class);
            result = payTransferService.wechatWithdraw(paymentTransferReq);
            Logger.info("WechatDelayWithdrawTask execute result:{}", DataMask.toJSONString(result));
        } catch (Exception e) {
            result.setSuccess(false);
            result.setMessage(e.getMessage());
            Logger.error("微信异步提现任务失败：", e);
        }
        return result;
    }

}
