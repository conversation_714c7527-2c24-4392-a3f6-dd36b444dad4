package com.akucun.account.proxy.service.acct.impl;

import com.aikucun.common2.base.Result;
import com.aikucun.common2.base.exception.BusinessException;
import com.akucun.account.center.client.model.TradeInfo;
import com.akucun.account.center.feign.AccountCenterService;
import com.akucun.account.proxy.client.file.FileUploadClient;
import com.akucun.account.proxy.client.merchant.entity.QueryMerchantWithdrawalDTO;
import com.akucun.account.proxy.client.merchant.entity.ResQueryMerchantWithdrawalDTO;
import com.akucun.account.proxy.client.merchant.feign.WithdrawalFacade;
import com.akucun.account.proxy.common.constant.ApplyStatus;
import com.akucun.account.proxy.common.constant.CommonConstants;
import com.akucun.account.proxy.common.constant.Constant;
import com.akucun.account.proxy.common.enums.*;
import com.akucun.account.proxy.common.exception.AccountProxyException;
import com.akucun.account.proxy.common.utils.AccountNoUtils;
import com.akucun.account.proxy.common.utils.ErrorMessageConvertUtils;
import com.akucun.account.proxy.common.utils.Results;
import com.akucun.account.proxy.dao.mapper.*;
import com.akucun.account.proxy.dao.model.*;
import com.akucun.account.proxy.facade.stub.enums.AccountKeyConstants;
import com.akucun.account.proxy.facade.stub.enums.CustomerType;
import com.akucun.account.proxy.facade.stub.enums.DetailTypeConstants;
import com.akucun.account.proxy.facade.stub.enums.TransferTypeEnum;
import com.akucun.account.proxy.facade.stub.others.account.req.WithdrawQueryReq;
import com.akucun.account.proxy.facade.stub.others.account.vo.AccountWithdrawVO;
import com.akucun.account.proxy.facade.stub.others.account.vo.NotifyWithdrawVO;
import com.akucun.account.proxy.facade.stub.others.account.vo.WechatInfoBindVO;
import com.akucun.account.proxy.facade.stub.others.dto.req.PaymentTransferReq;
import com.akucun.account.proxy.facade.stub.others.dto.res.PaymentTransferResp;
import com.akucun.account.proxy.service.acct.*;
import com.akucun.account.proxy.service.acct.bo.WithdrawThresholdCheckBO;
import com.akucun.account.proxy.service.common.AgentWhiteListAccountService;
import com.akucun.account.proxy.service.common.DuplicateCheckService;
import com.akucun.account.proxy.service.common.SpringContextHolder;
import com.akucun.account.proxy.service.config.DecouplingConfig;
import com.akucun.account.proxy.service.postaction.PostActionService;
import com.akucun.account.proxy.service.postaction.bo.PostActionItemBO;
import com.akucun.account.proxy.service.postaction.task.WithdrawReceiptDownloadTask;
import com.akucun.account.proxy.service.transfer.PayTransferService;
import com.akucun.account.proxy.service.transfer.convert.FinTaskAcceptRequestConvert;
import com.akucun.akucunapi.common.pojo.SendSMSParam;
import com.akucun.akucunapi.mq.service.IRocketMqService;
import com.akucun.bcs.channel.facade.stub.api.WechatWithdrawApi;
import com.akucun.bcs.channel.facade.stub.dto.WechatWithdrawRequest;
import com.akucun.cloud.security.CodeUtils;
import com.akucun.common.util.MD5Utils;
import com.akucun.fps.account.client.util.DateUtils;
import com.akucun.fps.bill.client.api.CustomerInfoService;
import com.akucun.fps.bill.client.model.MerchantInfo;
import com.akucun.fps.bill.client.model.query.MerchantInfoQuery;
import com.akucun.fps.common.entity.ResultList;
import com.akucun.fps.pingan.client.constants.BankCardStatusConstants;
import com.akucun.fps.pingan.client.constants.CustomerNatureType;
import com.akucun.fps.pingan.client.model.BankInfoManageResp;
import com.akucun.fps.pingan.client.model.query.BindCardQueryReq;
import com.akucun.fps.pingan.client.vo.*;
import com.akucun.fps.pingan.feign.api.acquirejointlinenum.AcquireJointLineNumbeServiceApi;
import com.akucun.fps.pingan.feign.api.assets.AssetsServiceApi;
import com.akucun.fps.pingan.feign.api.member.MemberServiceApi;
import com.akucun.fps.pingan.feign.api.merchantquery.MerchantQueryServiceApi;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mengxiang.base.common.log.Logger;
import com.mengxiang.base.common.utils.datamasking.DataMask;
import com.mengxiang.fileupload.dto.FileInfo;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;

import java.io.IOException;
import java.math.BigDecimal;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

import static com.aikucun.common2.base.Result.*;
import static com.akucun.account.proxy.common.constant.Constant.*;


/**
 * @Author: silei
 * @Date: 2021/3/8
 * @desc: 账户提现
 */
@Service
public class AccountWithdrawServiceImpl implements AccountWithdrawService {

    @Resource
    private AssetsServiceApi assetsServiceApi;
    @Resource
    private MemberServiceApi memberServiceApi;
    @Resource
    private WechatWithdrawApi wechatWithdrawApi;
    @Resource
    private MerchantQueryServiceApi merchantQueryServiceApi;
    @Resource
    private AccountCenterService accountCenterService;
    @Resource
    private WithdrawTaxService withdrawTaxService;
    @Resource
    private WithdrawApplyRecordMapper withdrawApplyRecordMapper;
    @Resource
    private AccountTotalAmountMapper accountTotalAmountMapper;
    @Resource
    private AgentWhiteListAccountService agentWhiteListAccountService;
    @Resource
    private PostActionService postActionService;
    @Resource
    private DuplicateCheckService duplicateCheckService;
    @Resource
    private WechatAuthService wechatAuthService;
    @Autowired
    private PayTransferService payTransferService;
    @Reference(check = false)
    private CustomerInfoService customerInfoService;
    @Reference(check = false)
    private IRocketMqService iRocketMqService;
    @Resource
    private AccountTenantCustomerMapper accountTenantCustomerMapper;
    @Resource
    private TenantWithdrawApplyMapper tenantWithdrawApplyMapper;
    @Resource
    private WithdrawalFacade withdrawalFacade;
    @Resource
    private AcquireJointLineNumbeServiceApi acquireJointLineNumbeServiceApi;
    @Resource
    private WithdrawServicefeeSummaryMapper withdrawServicefeeSummaryMapper;
    @Autowired
    private DecouplingConfig decouplingConfig;
    @Resource
    private PinganDecoupleService pinganDecoupleService;
    @Autowired
    private AccountUpgradeService accountUpgradeService;
    @Autowired
    private FileUploadClient fileUploadClient;

    @Value("${notify.merchant.switch:false}")
    private boolean notifyMerchantSwitch;

    private static final String FORMAT_MONTH = "yyyyMM";

    @Value("${free.withdrawals.num:5}")
    private long freeWithdrawalsNum;

    @Value("${free.withdrawals.fee.deploy:1}")
    private BigDecimal freeWithdrawalsFeeDeploy;
    /**
     * 提现手续费开关
     */
    @Value("${free.withdrawals.switch:true}")
    private boolean freeWithdrawalsSwitch;
    /**
     * 自营微信二级商户号
     */
    @Value("#{'${self.wechat.subMchId:**********,**********,**********,**********}'.split(',')}")
    private List<String> selfSubMchIdList;
    /**
     * 微信提现年累计上限
     */
    @Value("${wechat.withdraw.summary.threshold:200000}")
    private String wechatWithdrawSummaryThreshold;

    @Value("${withdraw.processing.check.time:-30}")
    private int withdrawCheckTime;

    @Value("${wechat.withdraw.limit:0}")
    private BigDecimal wechatWithdrawLimit;

    @Value("${wechat.withdraw.limit.tips:抱歉，您本次提现金额大于__元，无法提现，请修改金额重新提现}")
    private String wechatWithdrawLimitTips;

    @Value("${pingan.maintain.switch:false}")
    private boolean pinganMaintainSwitch;
    @Value("${pingan.maintain.tips:系统维护中，请稍后重试}")
    private String pinganMaintainTips;
    @Value("${account.upgrade.check.switch:true}")
    private boolean accountUpgradeCheckSwitch;

    @Value("${isRevokeCreditThirdHtIdNewrule:true}")
    private boolean isRevokeCreditThirdHtIdNewrule;

//    /**
//     * 平安反清分开关
//     */
//    @Value("${pingan.antiClearing.switch:true}")
//    private boolean antiClearingSwitch;

    /**
     * 饷店小程序-店主提现
     * @param vo
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> accountWithdraw(AccountWithdrawVO vo) {
        Logger.info("AccountWithdrawServiceImpl accountWithdraw vo: {}", DataMask.toJSONString(vo));
        Result<String> result;
        //必传参数校验
        result = withdrawParamCheck(vo);
        if (!result.getSuccess()) {
            Logger.info("AccountWithdrawServiceImpl withdrawParamCheck fail customerCode: {}", vo.getCustomerCode());
            return result;
        }
        if(pinganMaintainSwitch){
            if(!"SH_WC".equals(vo.getCustomerType()) && !"SS_WC".equals(vo.getCustomerType()) && !"TB".equals(vo.getCustomerType())){
                return error(ErrorCodeConstants.SYSTEMMAINTAIN_900000.getErrorCode(), pinganMaintainTips);
            }
        }
        //参数传递
        vo.setWithdrawNo(AccountNoUtils.generateWithdrawNo());
        WithdrawApplyRecord record = new WithdrawApplyRecord();
        BeanUtils.copyProperties(vo, record);
        record.setWithdrawChannel(WithdrawChannelConstants.PINGAN.getName());
        record.setApplyStatus(ApplyStatus.DOING.name());

        switch (vo.getCustomerType()) {
            case "NM":
                result = shopkeeperWithdraw(record);
                break;
            case "NMDL":
                result = shopAgentWithdraw(record);
                break;
            case "DG":
                result = DGWithdraw(record);
                break;
            case "OP":
            case "DCC":
            case "AT":
            case "DXQDS":
                result = commonWithdraw(record);
                break;
            case "SH":
                result = merchantWithdraw(record);
                break;
            case "SH_WC":
            case "SS_WC":
                result = wechatMerchantWithdraw(vo, record);
                break;
            case "TB":
                result = groupSellerWithdraw(record);
                break;
            default:
                return Results.error(CommonConstants.GENERAL_CODE, "账户类型错误");
        }
        return result;
    }

    private WithdrawServicefeeSummary getWithdrawServiceFeeSummary(WithdrawApplyRecord record) {
        WithdrawServicefeeSummary withdrawServicefeeSummary = new WithdrawServicefeeSummary();
        String month = com.aikucun.common2.utils.DateUtils.format(new Date(), FORMAT_MONTH);
        String idNo = MD5Utils.md5(record.getIdentifyNo());
        LambdaQueryWrapper<WithdrawServicefeeSummary> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WithdrawServicefeeSummary::getIdentifyNo, idNo)
                .eq(WithdrawServicefeeSummary::getMonth, month);
        List<WithdrawServicefeeSummary> list = withdrawServicefeeSummaryMapper.selectList(wrapper);
        record.setServiceAmount(BigDecimal.ZERO);
        if (!CollectionUtils.isEmpty(list)) {
            //判断当前提现次数是否符合收取手续费标准
            if (list.get(0).getWithdrawNum() >= freeWithdrawalsNum) {
                record.setServiceAmount(freeWithdrawalsFeeDeploy);
            }
            withdrawServicefeeSummary = list.get(0);
        } else {
            withdrawServicefeeSummary.setMonth(month);
        }
        return withdrawServicefeeSummary;
    }

    /**
     * 提现参数校验
     *
     * @param vo
     * @return
     */
    private Result<String> withdrawParamCheck(AccountWithdrawVO vo) {
        Result<String> result;
        if (vo == null || vo.getAmount() == null || StringUtils.isEmpty(vo.getCustomerCode())
                || StringUtils.isEmpty(vo.getCustomerType())) {
            return Results.error(ResponseEnum.ACCORE_101500);
        }
        if (CustomerType.NMDL.getName().equals(vo.getCustomerType()) && StringUtils.isBlank(vo.getShopId())) {
            return Results.error(ResponseEnum.ACCORE_101513);
        }
        //提现金额不可为0或负数
        if (BigDecimal.ZERO.compareTo(vo.getAmount()) >= 0) {
            return Results.error(ResponseEnum.ACCORE_101509);
        }
        //微信提现除外，银行卡校验
        if (CustomerType.SH_WC.getName().equals(vo.getCustomerType()) || CustomerType.SS_WC.getName().equals(vo.getCustomerType())) {
            if (StringUtils.isEmpty(vo.getSubMerchantId()) || StringUtils.isEmpty(vo.getWithdrawType())) {
                return Results.error(ResponseEnum.ACCORE_101520);
            }
            return Results.success();
        } else if (CustomerType.TB.getName().equals(vo.getCustomerType())) {
            //团长帮忙提现到微信零钱
            return Results.success();
        } else {
            if (StringUtils.isEmpty(vo.getBankNo()) || StringUtils.isEmpty(vo.getApplyUser())) {
                return Results.error(ResponseEnum.ACCORE_101500);
            }
            // 账户升级中校验，避免提现与账户升级并行导致的资金锁死流程阻塞问题
            if (accountUpgradeCheckSwitch && (CustomerType.NM.getName().equals(vo.getCustomerType()) || CustomerType.NMDL.getName().equals(vo.getCustomerType())) && accountUpgradeService.hasRecentUpgradeProcessing(vo.getCustomerCode(), vo.getCustomerType())) {
                return Results.error(ResponseEnum.ACCORE_101517);
            }
            result = bindCardCheck(vo);
            if (!result.getSuccess()) {
                Logger.warn("withdrawParamCheck bindCardCheck 用户未绑卡 customerCode: {}", vo.getCustomerCode());
                return result;
            }
        }
        return result;
    }

    /**
     * 绑卡校验
     *
     * @param vo
     * @return
     */
    private Result<String> bindCardCheck(AccountWithdrawVO vo) {
        Result<String> result = Results.success();
        //调用平安查询绑卡信息
        BindCardQueryReq bindCardQueryReq = new BindCardQueryReq();
        bindCardQueryReq.setCustomerCode(vo.getCustomerCode());
        bindCardQueryReq.setCustomerType((CustomerNatureType) CustomerNatureType.getEnum(CustomerNatureType.class, vo.getCustomerType()));
        bindCardQueryReq.setStatus(BankCardStatusConstants.BINDACTIVE);
        ResultList<PinganCardVO> pinganCardList = merchantQueryServiceApi.selectBindCardListByParams(bindCardQueryReq);
        //校验绑卡信息是否为空
        if (pinganCardList == null || CollectionUtils.isEmpty(pinganCardList.getDatalist())) {
            return Results.error(ResponseEnum.ACCORE_101510);
        }
        return result;
    }

    /**
     * 商户微信提现
     *
     * @param vo
     * @param record
     * @return
     * @throws BusinessException
     */
    private Result<String> wechatMerchantWithdraw(AccountWithdrawVO vo, WithdrawApplyRecord record) {
        Result<String> result = success(vo.getWithdrawNo());
        record.setWithdrawChannel(WithdrawChannelConstants.WECHAT.getName());
        //1.插入提现申请记录表
        int insertNum = saveWithdrawRecord(record);
        if (insertNum != 1) {
            return Results.error(CommonConstants.GENERAL_CODE, "提现申请记录落库失败");
        }
        //2.构建账户中心提现参数，发起提现
        TradeInfo tradeInfo = null;
        //自营微信二级商户号提现不走账户中心
        if (!selfSubMchIdList.contains(vo.getCustomerCode())) {
            tradeInfo = generateTradeInfo(record, AccountKeyConstants.WCSH.getName(), DetailTypeConstants.TRADE_TYPE_401.name());
            if (CustomerType.SS_WC.getName().equals(vo.getCustomerType())) {
                tradeInfo.setAccountTypeKey(AccountKeyConstants.WCSS.getName());
                tradeInfo.setTradeType(DetailTypeConstants.TRADE_TYPE_481.name());
            }
            com.akucun.common.Result<Void> tradeResult = accountCenterService.dealTrade(tradeInfo);
            Logger.info("微信提现调用账户中心结果:{}", DataMask.toJSONString(tradeResult));
            if (!tradeResult.isSuccess()) {
                withdrawApplyRecordMapper.updateWithdrawRecordStatus(record.getWithdrawNo(), ApplyStatus.FAIL.getName(), tradeResult.getMessage());
                return Results.error(CommonConstants.GENERAL_CODE, tradeResult.getMessage());
            }
        }
        //3.微信提现
        try {
            WechatWithdrawRequest wechatWithdrawRequest = new WechatWithdrawRequest();
            wechatWithdrawRequest.setAccountType(vo.getAccountType());
            wechatWithdrawRequest.setBankMemo(vo.getBankMemo());
            wechatWithdrawRequest.setRemark(record.getRemark());
            wechatWithdrawRequest.setSubMerchantId(vo.getSubMerchantId());
            wechatWithdrawRequest.setWithdrawAmount(vo.getAmount());
            wechatWithdrawRequest.setWithdrawNo(record.getWithdrawNo());
            wechatWithdrawRequest.setWithdrawType(vo.getWithdrawType());
            com.akucun.common.Result<Void> withdrawRes = wechatWithdrawApi.withDraw(wechatWithdrawRequest);
            Logger.info("wechatMerchantWithdraw 微信提现结果: {}", DataMask.toJSONString(withdrawRes));
            if (!withdrawRes.isSuccess()) {
                if (!selfSubMchIdList.contains(vo.getCustomerCode())) {
                    //如果是微信商户调用账户中心
                    if (CustomerType.SH_WC.name().equals(vo.getCustomerType())) {
                        tradeInfo.setTradeType(DetailTypeConstants.TRADE_TYPE_402.name());
                    } else if (CustomerType.SS_WC.name().equals(vo.getCustomerType())) {
                        tradeInfo.setTradeType(DetailTypeConstants.TRADE_TYPE_482.name());
                    }
                    //渠道提现失败，账户中心资产退回
                    PostActionItemBO itemBO = PostActionItemBO.builder()
                            .bizId(record.getWithdrawNo()).paramObject(tradeInfo).remark(withdrawRes.getMessage())
                            .actionType(PostActionTypes.ACCOUNT_CENTER_COMPENSATE.getName()).status(PostActionExecStatus.EXECUTE.value())
                            .nextRetryTime(LocalDateTime.now().plusSeconds(5)).build();
                    postActionService.addAction(itemBO);
                    result = Results.error(CommonConstants.GENERAL_CODE, withdrawRes.getMessage());
                }
                //处理失败更新提现单状态
                withdrawApplyRecordMapper.updateWithdrawRecordStatus(record.getWithdrawNo(), ApplyStatus.FAIL.getName(), withdrawRes.getMessage());
                Logger.warn("wechatMerchantWithdraw 微信提现失败:{},msg:{}", record.getCustomerCode(), withdrawRes.getMessage());
            } else {
                PostActionItemBO itemBO = PostActionItemBO.builder()
                        .bizId(record.getWithdrawNo()).paramObject(wechatWithdrawRequest).remark(record.getRemark())
                        .actionType(PostActionTypes.MERCHANT_WECHAT_WITHDRAW_QUERY.getName()).status(PostActionExecStatus.EXECUTE.value())
                        .nextRetryTime(LocalDateTime.now().plusSeconds(10)).build();
                postActionService.addAction(itemBO);
            }
        } catch (Exception e) {
            Logger.error("wechatMerchantWithdraw 微信提现异常:{},Exception:", record.getCustomerCode(), e);
        }

        return result;
    }

    /**
     * 平安商户提现
     *
     * @param record
     * @return
     */
    private Result<String> merchantWithdraw(WithdrawApplyRecord record) {
        Result<String> result = success(record.getWithdrawNo());

        com.akucun.common.Result<String> encrypt = CodeUtils.decrypt(record.getBankNo());
        //账号解密失败或者为空直接返回
        if (!encrypt.isSuccess() || StringUtils.isEmpty(encrypt.getData())) {
            Logger.error("merchantWithdraw decrypt error:{}", encrypt.getMessage());
            return Results.error(ResponseEnum.ACCORE_101515);
        }
        //设置解密后银行账号
        String decBankNo = encrypt.getData();
        //1.插入提现申请记录表
        int insertNum = saveWithdrawRecord(record);
        if (insertNum != 1) {
            return Results.error(CommonConstants.GENERAL_CODE, "提现申请记录落库失败");
        }
        //2.提现申请
        TradeInfo tradeInfo = generateTradeInfo(record, AccountKeyConstants.SH.getName(), DetailTypeConstants.WITHDRAW.name());
        com.akucun.common.Result<Void> tradeResult = accountCenterService.dealTrade(tradeInfo);
        if (!tradeResult.isSuccess()) {
            withdrawApplyRecordMapper.updateWithdrawRecordStatus(record.getWithdrawNo(), ApplyStatus.FAIL.name(), tradeResult.getMessage());
            return Results.error(CommonConstants.GENERAL_CODE, tradeResult.getMessage());
        }
        //去重校验
        Result<Void> dupResult = duplicateCheckService.checkDuplicate(DetailTypeConstants.WITHDRAW.name() + "_" + record.getSourceBillNo(), Boolean.FALSE);
        if (!dupResult.getSuccess()) {
            return Results.error(dupResult.getCode(), "提现请求重复");
        }
        //3.平安异步提现
        WithdrawVO withdrawVO = generateWithdrawVO(record, decBankNo);
        asyncWithdraw(withdrawVO);
        //4.提现确认
        tradeInfo.setTradeType(DetailTypeConstants.WITHDRAW_AG.name());
        com.akucun.common.Result<Void> tradeResult2 = accountCenterService.dealTrade(tradeInfo);
        if (!tradeResult2.isSuccess()) {
            Logger.error("merchantWithdraw 提现失败: {}", tradeResult2.getMessage());
            return Results.error(CommonConstants.GENERAL_CODE, "提现失败:" + tradeResult2.getMessage());
        }
        return result;
    }

    /**
     * DCC、OpenApi、AT提现
     *
     * @param record
     * @return
     */
    private Result<String> commonWithdraw(WithdrawApplyRecord record) {
        Result<String> result = success(record.getWithdrawNo());
        String bankNo = record.getBankNo();
        //银行卡号加密
        Result<String> res = encrypt(record.getBankNo());
        if (!res.getSuccess()) {
            return Results.error(res.getCode(), res.getMessage());
        }
        record.setBankNo(res.getData());
        //插入提现申请记录表
        int insertNum = saveWithdrawRecord(record);
        if (insertNum != 1) {
            return Results.error(CommonConstants.GENERAL_CODE, "提现申请记录落库失败");
        }
        if(!decouplingConfig.decoupleTenantSwitch(record.getCustomerCode(), record.getCustomerType())) {
        	return originTenantWithdraw(record,bankNo);
        }else {
        	try {
				decouplingTenantWithdraw(record, bankNo);
			} catch (IOException e) {
                Logger.error("commonWithdraw exception, customerCode:{}, e: ", record.getCustomerCode(), e);
                return Results.error(CommonConstants.GENERAL_CODE, e.getMessage());
			}
        }
        return result;
    }

    /**
     * 代购账户提现（账户中心发起）
     *
     * @param record
     * @return
     */
    private Result<String> DGWithdraw(WithdrawApplyRecord record) {
        Result<String> result = success(record.getWithdrawNo());
        //去重校验
        Result<Void> dupResult = duplicateCheckService.checkDuplicate("DG" + record.getSourceBillNo(), Boolean.FALSE);
        if (!dupResult.getSuccess()) {
            return Results.error(dupResult.getCode(), "提现请求重复");
        }
        String bankNo = record.getBankNo();
        //银行卡号加密
        Result<String> res = encrypt(record.getBankNo());
        if (!res.getSuccess()) {
            return Results.error(res.getCode(), res.getMessage());
        }
        record.setBankNo(res.getData());
        //插入提现申请记录表
        int insertNum = saveWithdrawRecord(record);
        if (insertNum != 1) {
            return Results.error(CommonConstants.GENERAL_CODE, "提现申请记录落库失败");
        }
        WithdrawVO withdrawVO = generateWithdrawVO(record, bankNo);
        //4.平安账户异步提现
        asyncWithdraw(withdrawVO);
        return result;
    }

    /**
     * 平安异步提现
     *
     * @param withdrawVO
     */
    private void asyncWithdraw(WithdrawVO withdrawVO) {
        PostActionItemBO itemBO = PostActionItemBO.builder()
                .bizId(withdrawVO.getThirdLogNo())
                .paramObject(withdrawVO)
                .remark("异步提现请求")
                .actionType(PostActionTypes.PINGAN_DELAY_WITHDRAW.getName())
                .status(PostActionExecStatus.EXECUTE.value())
                .nextRetryTime(LocalDateTime.now().plusSeconds(10))
                .retryNums(0)
                .build();
        postActionService.addAction(itemBO);
    }


    /**
     * 店主提现
     *
     * @param record
     * @return
     */
    private Result<String> shopkeeperWithdraw(WithdrawApplyRecord record) {
        try {
            if (decouplingConfig.decouplingSwitch(record.getCustomerCode(), record.getCustomerType())) {
                return decouplingShopkeeperWithdraw(record);
            } else {
                return originShopkeeperWithdraw(record);
            }
        } catch (Exception e) {
            Logger.error("shopkeeperWithdraw exception, customerCode:{}, e: ", record.getCustomerCode(), e);
            throw e;
        }
    }

    private Result<String> originShopkeeperWithdraw(WithdrawApplyRecord record) {
        Result<String> result = success(record.getWithdrawNo());
        String bankNo = record.getBankNo();
        //银行卡号加密
        Result<String> res = encrypt(record.getBankNo());
        if (!res.getSuccess()) {
            return Results.error(res.getCode(), res.getMessage());
        }
        record.setBankNo(res.getData());
        //1、判断是否达到提现扣税金额限制
        WithdrawThresholdCheckBO withdrawThreshold = withdrawTaxService.checkWithdrawThreshold(record);
        if (!withdrawThreshold.getSuccess()) {
            return Results.error(withdrawThreshold.getCode(), withdrawThreshold.getTip());
        }
        // 手续费
        WithdrawServicefeeSummary withdrawServicefeeSummary = null;
        if (freeWithdrawalsSwitch) {
            withdrawServicefeeSummary = getWithdrawServiceFeeSummary(record);
        }
        //税费计算
        WithdrawTaxDetail taxDetail;
        try {
            taxDetail = withdrawTaxService.applyWithdrawTax(record);
        } catch (BusinessException e) {
            return Results.error(e.getCode(), e.getMessage());
        }
//        if (null != taxDetail) {
//            record.setRemark(taxDetail.getRemark());
//        }
//        //不扣除税费但收取手续费时
//        if (null == taxDetail && null != record.getServiceAmount()) {
//            if (record.getAmount().compareTo(freeWithdrawalsFeeDeploy) < 1) {
//                return Results.error(CommonConstants.GENERAL_CODE, "提现金额需大于" + freeWithdrawalsFeeDeploy + "元，请重新输入");
//            }
//            record.setRemark("到账：" + record.getAmount().subtract(record.getServiceAmount()) + "，银行手续费：" + record.getServiceAmount());
//        }

        //插入提现申请记录表
        int insertNum = saveWithdrawRecord(record);
        if (insertNum != 1) {
            return Results.error(CommonConstants.GENERAL_CODE, "提现申请记录落库失败");
        }
        //提现扣税
        Result<Void> taxRes = withdrawTaxService.withdrawTax(taxDetail);
        if (!taxRes.getSuccess()) {
            return Results.error(CommonConstants.GENERAL_CODE, taxRes.getMessage());
        }
        //提现扣手续费
        if (null != withdrawServicefeeSummary) {
            Result<Void> serviceFeeRes = withdrawTaxService.withdrawServiceFee(record, taxDetail, withdrawServicefeeSummary);
            if (!serviceFeeRes.getSuccess()) {
                return Results.error(CommonConstants.GENERAL_CODE, serviceFeeRes.getMessage());
            }
        }
        //2.同步调用账户中心登账
        TradeInfo tradeInfo = generateTradeInfo(record, AccountKeyConstants.NM.name(), DetailTypeConstants.TRADE_TYPE_044.name());
        com.akucun.common.Result<Void> tradeResult = accountCenterService.dealTrade(tradeInfo);
        if (!tradeResult.isSuccess()) {
            Logger.error("shopkeeperWithdraw tradeResult: {}", DataMask.toJSONString(tradeResult));
            withdrawApplyRecordMapper.updateWithdrawRecordStatus(record.getWithdrawNo(), ApplyStatus.FAIL.name(), tradeResult.getMessage());
            withdrawTaxService.withdrawFailForTax(record, tradeResult.getMessage());
            //提现手续费回退
            withdrawTaxService.withdrawFailForServiceFee(record);
            return Results.error(CommonConstants.GENERAL_CODE, tradeResult.getMessage());
        }
        //3.同步调用平安提现接口
        WithdrawVO withdrawVO = generateTaxWithdrawVO(record, bankNo, taxDetail.getWithdraw());
//        if (null != taxDetail) {
//            withdrawVO.setAmount(taxDetail.getWithdraw().multiply(new BigDecimal(100)).intValue());
//        } else {
//            //不扣税费
//            BigDecimal withdrawAmount = record.getAmount().subtract(null == record.getServiceAmount() ? BigDecimal.ZERO : record.getServiceAmount());
//            withdrawVO.setAmount(withdrawAmount.multiply(new BigDecimal(100)).intValue());
//        }
        com.akucun.fps.common.entity.Result<String> withdrawResult = assetsServiceApi.cashWithdraw(withdrawVO);
        //平安提现失败，账户中心及扣税退回
        if (!withdrawResult.isSuccess()) {
            Logger.error("shopkeeperWithdraw withdrawResult: {}", DataMask.toJSONString(withdrawResult));
            result = Results.error(CommonConstants.GENERAL_CODE, withdrawResult.getErrorMessage());
            //调用账户中心进行金额退回操作
            TradeInfo tradeInfoRefund = generateTradeInfo(record, AccountKeyConstants.NM.name(), DetailTypeConstants.TRADE_TYPE_045.name());
            tradeInfoRefund.setRemark(withdrawResult.getErrorMessage());
            com.akucun.common.Result<Void> tradeRefundResult = accountCenterService.dealTrade(tradeInfoRefund);
            if (!tradeRefundResult.isSuccess()) {
                result = Results.error(CommonConstants.GENERAL_CODE, tradeRefundResult.getMessage());
                Logger.error("shopkeeperWithdraw withdraw refund fail, withdrawNo: {}", record.getWithdrawNo());
            }
            withdrawApplyRecordMapper.updateWithdrawRecordStatus(record.getWithdrawNo(), ApplyStatus.FAIL.name(), withdrawResult.getErrorMessage());
            //回退扣税信息
            withdrawTaxService.withdrawFailForTax(record, result.getMessage());
            //提现手续费回退
            withdrawTaxService.withdrawFailForServiceFee(record);
            return result;
        }
        return result;

    }

    /**
     * 饷店小程序-店主提现
     * @param record
     * @return
     */
    private Result<String> decouplingShopkeeperWithdraw(WithdrawApplyRecord record) {
        Result<String> result = success(record.getWithdrawNo());
        String bankNo = record.getBankNo();
        Result<String> res = encrypt(record.getBankNo());
        if (!res.getSuccess()) {
            return Results.error(res.getCode(), res.getMessage());
        }
        record.setBankNo(res.getData());
        //1、判断是否达到提现扣税金额限制：当月历史提现总金额+当前今天 于 阶梯扣税的限制
        WithdrawThresholdCheckBO withdrawThreshold = withdrawTaxService.checkWithdrawThreshold(record);
        if (!withdrawThreshold.getSuccess()) {
            return Results.error(withdrawThreshold.getCode(), withdrawThreshold.getTip());
        }
        //2、手续费
        WithdrawServicefeeSummary withdrawServicefeeSummary = null;
        if (freeWithdrawalsSwitch) {
            withdrawServicefeeSummary = getWithdrawServiceFeeSummary(record);
        }
        //3、税费计算
        WithdrawTaxDetail taxDetail;
        try {
            taxDetail = withdrawTaxService.applyWithdrawTax(record);
        } catch (BusinessException e) {
            Logger.error("shopkeeperWithdraw applyWithdrawTax e:", e);
            return Results.error(e.getCode(), e.getMessage());
        }
        //4.插入提现申请记录表
        int insertNum = saveWithdrawRecord(record);
        if (insertNum != 1) {
            return Results.error(CommonConstants.GENERAL_CODE, "提现申请记录落库失败");
        }
        //5.提现扣税：使用分布式锁保证并发安全，然后插入扣税记录，并更新扣税汇总
        Result<Void> taxRes = withdrawTaxService.withdrawTax(taxDetail);
        if (!taxRes.getSuccess()) {
            Logger.error("shopkeeperWithdraw withdrawTax taxRes:{}", DataMask.toJSONString(taxRes));
            return Results.error(CommonConstants.GENERAL_CODE, taxRes.getMessage());
        }
        //6.提现扣手续费
        if (null != withdrawServicefeeSummary) {
            Result<Void> serviceFeeRes = withdrawTaxService.withdrawServiceFee(record, taxDetail, withdrawServicefeeSummary);
            if (!serviceFeeRes.getSuccess()) {
                return Results.error(CommonConstants.GENERAL_CODE, serviceFeeRes.getMessage());
            }
        }
        //7.同步调用账户中心登账
        TradeInfo tradeInfo = generateTradeInfo(record, AccountKeyConstants.NM.name(), DetailTypeConstants.TRADE_TYPE_044.name());
        com.akucun.common.Result<Void> tradeResult = accountCenterService.dealTrade(tradeInfo);
        if (!tradeResult.isSuccess()) {
            Logger.warn("shopkeeperWithdraw accountCenterService.dealTrade tradeResult: {}", DataMask.toJSONString(tradeResult));
            bankWithdrawFailProcess(record, tradeResult.getMessage());
            return Results.error(CommonConstants.GENERAL_CODE, tradeResult.getMessage());
        }
        //实际提现到账金额
        int withdrawAmt = taxDetail.getWithdraw().multiply(new BigDecimal(100)).intValue();
        //8.公司账户分账到店主子账户
        com.akucun.fps.common.entity.Result<String> creditResult;
        try {
            creditResult = companyAccountCredit(record, withdrawAmt, DetailTypeConstants.TRADE_TYPE_044.name());
        } catch (Exception e){
            Logger.warn("shopkeeperWithdraw companyAccountCredit exception : customerCode:{},withdrawNo:{} ",
                    record.getCustomerCode(), record.getWithdrawNo(), e);
            result = Results.error(CommonConstants.GENERAL_CODE, "提现失败，请重试");
            asyncAccountRefund(record, "提现失败请重试");
            bankWithdrawFailProcess(record, "提现失败请重试");
            return result;
        }
        if (!creditResult.isSuccess()) {
            Logger.warn("shopkeeperWithdraw companyAccountCredit reus:{}", JSONObject.toJSONString(creditResult));
            result = Results.error(CommonConstants.GENERAL_CODE, creditResult.getErrorMessage());
            asyncAccountRefund(record, creditResult.getErrorMessage());
            bankWithdrawFailProcess(record, creditResult.getErrorMessage());
            return result;
        }
        //9.异步调用平安提现接口
        WithdrawVO withdrawVO = generateWithdrawVO(record, bankNo);
        withdrawVO.setAmount(withdrawAmt);
        asyncWithdraw(withdrawVO);
        return result;
    }

    /**
     * 公司账户分账到子账户
     * @param record
     * @param withdrawAmt
     * @param tradeType
     * @return
     */
    private com.akucun.fps.common.entity.Result<String> companyAccountCredit(WithdrawApplyRecord record, int withdrawAmt, String tradeType) {
        CashCreditVO cashCreditVO = new CashCreditVO();
        cashCreditVO.setBillNo(record.getWithdrawNo());
        cashCreditVO.setBillType(tradeType);
        cashCreditVO.setThirdCustId(record.getCustomerCode());
        cashCreditVO.setTranAmount(withdrawAmt);
        cashCreditVO.setRemark(record.getRemark());
        cashCreditVO.setCustomerType(record.getCustomerType());
        Logger.info("shopkeeperWithdraw assetsServiceApi.credit 参数:{}", JSONObject.toJSONString(cashCreditVO));
        return assetsServiceApi.credit(cashCreditVO);
    }

    /**
     * 平安提现失败处理
     *
     * @param record
     * @param errorMsg
     */
    private void bankWithdrawFailProcess(WithdrawApplyRecord record, String errorMsg) {
        //提现记录状态变更
        withdrawApplyRecordMapper.updateWithdrawRecordStatus(record.getWithdrawNo(), ApplyStatus.FAIL.name(), errorMsg);
        //回退扣税信息
        withdrawTaxService.withdrawFailForTax(record, errorMsg);
        //提现手续费回退
        withdrawTaxService.withdrawFailForServiceFee(record);
    }

    /**
     * 持久化提现记录
     *
     * @param record
     * @return
     */
    private int saveWithdrawRecord(WithdrawApplyRecord record) {
        String originIdNo = record.getIdentifyNo();
        record.setIdentifyNo(MD5Utils.md5(originIdNo));
        //        record.setIdentifyNo(originIdNo);
        return withdrawApplyRecordMapper.insert(record);
    }


    /**
     * 构建平安提现请求参数（）
     *
     * @param record
     * @param bankNo
     * @param withdrawAmount 实际到账金额
     * @return
     */
    private WithdrawVO generateTaxWithdrawVO(WithdrawApplyRecord record, String bankNo, BigDecimal withdrawAmount) {
        WithdrawVO vo = new WithdrawVO();
        vo.setAmount(withdrawAmount.multiply(new BigDecimal(100)).intValue());
        if (null != record.getServiceAmount()) {
            vo.setHandFee(record.getServiceAmount().multiply(new BigDecimal(100)).intValue());
        }
        vo.setOutacctid(bankNo);
        vo.setThirdcustid(record.getCustomerCode());
        vo.setThirdLogNo(record.getWithdrawNo());
        vo.setCustomerType(record.getCustomerType());
        return vo;
    }

    /**
     * 构建平安提现请求参数(不扣税)
     *
     * @param record
     * @param bankNo
     * @return
     */
    private WithdrawVO generateWithdrawVO(WithdrawApplyRecord record, String bankNo) {
        WithdrawVO vo = new WithdrawVO();
        vo.setAmount(record.getAmount().multiply(new BigDecimal(100)).intValue());
        vo.setOutacctid(bankNo);
        vo.setThirdcustid(record.getCustomerCode());
        vo.setThirdLogNo(record.getWithdrawNo());
        vo.setCustomerType(record.getCustomerType());
        return vo;
    }

    /**
     * 字符串加密
     *
     * @param str
     * @return
     */
    private Result<String> encrypt(String str) {
        try {
            com.akucun.common.Result<String> encrypt = CodeUtils.encrypt(str);
            if (!encrypt.isSuccess() || StringUtils.isEmpty(encrypt.getData())) {
                Logger.error("AccountWithdrawServiceImpl encrypt fail :{}", encrypt.getMessage());
                return Results.error(ResponseEnum.ACCORE_101505);
            } else {
                return success(encrypt.getData());
            }
        } catch (Exception e) {
            Logger.error("AccountWithdrawServiceImpl encrypt exception:", e);
            return Results.error(ResponseEnum.ACCORE_101504);
        }
    }

    /**
     * 构建tradeInfo
     *
     * @param record
     * @return
     */
    private TradeInfo generateTradeInfo(WithdrawApplyRecord record, String accountTypeKey, String tradeType) {
        TradeInfo tradeInfo = new TradeInfo();
        tradeInfo.setAccountTypeKey(accountTypeKey);
        tradeInfo.setTradeType(tradeType);
        tradeInfo.setSourceBillNo(record.getWithdrawNo());
        tradeInfo.setTradeNo(record.getWithdrawNo());
        tradeInfo.setAmount(record.getAmount());
        tradeInfo.setCustomerCode(record.getCustomerCode());
        tradeInfo.setRemark(record.getRemark());
        return tradeInfo;
    }

    /**
     * 店长提现
     *x
     * @param record
     * @return
     */
    private Result<String> shopAgentWithdraw(WithdrawApplyRecord record) {
        try {
            if (decouplingConfig.decouplingSwitch(record.getCustomerCode(), record.getCustomerType())){
                return decouplingShopAgentWithdraw(record);
            }else {
                return originShopAgentWithdraw(record);
            }
        } catch (Exception e) {
            Logger.error("shopAgentWithdraw exception, customerCode:{}, e: ", record.getCustomerCode(), e);
            throw e;
        }
    }

    private Result<String> originShopAgentWithdraw(WithdrawApplyRecord record) {
        Result<String> result = success(record.getWithdrawNo());
//        if (StringUtils.isBlank(record.getShopId())) {
//            return Results.error(CommonConstants.GENERAL_CODE, "店长提现shopId不能为空");
//        }
        String bankNo = record.getBankNo();
        //银行卡号加密
        Result<String> res = encrypt(record.getBankNo());
        if (!res.getSuccess()) {
            return Results.error(res.getCode(), res.getMessage());
        }
        record.setBankNo(res.getData());
        //1、判断是否达到提现扣税金额限制
        WithdrawThresholdCheckBO withdrawThreshold = withdrawTaxService.checkWithdrawThreshold(record);
        if (!withdrawThreshold.getSuccess()) {
            return Results.error(withdrawThreshold.getCode(), withdrawThreshold.getTip());
        }
        // 手续费
        WithdrawServicefeeSummary withdrawServicefeeSummary = null;
        if (freeWithdrawalsSwitch) {
            withdrawServicefeeSummary = getWithdrawServiceFeeSummary(record);
        }
        // 税费
        WithdrawTaxDetail taxDetail;
        try {
            taxDetail = withdrawTaxService.applyWithdrawTax(record);
        } catch (BusinessException e) {
            Logger.error("originShopAgentWithdraw applyWithdrawTax e:",e);
            return Results.error(e.getCode(), e.getMessage());
        }
//        if (null != taxDetail) {
//            record.setRemark(taxDetail.getRemark());
//            tradeInfo.setRemark(taxDetail.getRemark());
//            //设置提现金额扣税后
//            withdrawVO.setAmount(taxDetail.getWithdraw().multiply(new BigDecimal(100)).intValue());
//        }
//        //不扣除税费但收取手续费时
//        if (null == taxDetail && null != record.getServiceAmount()) {
//            if (record.getAmount().compareTo(freeWithdrawalsFeeDeploy) < 1) {
//                return Results.error(CommonConstants.GENERAL_CODE, "提现金额需大于" + freeWithdrawalsFeeDeploy + "元，请重新输入");
//            }
//            record.setRemark("到账：" + record.getAmount().subtract(record.getServiceAmount()) + "，银行手续费：" + record.getServiceAmount());
//            tradeInfo.setRemark(record.getRemark());
//            BigDecimal withdrawAmount = record.getAmount().subtract(record.getServiceAmount());
//            //设置提现金额扣税后
//            withdrawVO.setAmount(withdrawAmount.multiply(new BigDecimal(100)).intValue());
//        }
        //2.提现申请记录入库
        int insertNum = saveWithdrawRecord(record);
        if (insertNum != 1) {
            return Results.error(CommonConstants.GENERAL_CODE, "提现申请记录落库失败");
        }
        //提现扣税
        Result<Void> taxResult = withdrawTaxService.withdrawTax(taxDetail);
        if (!taxResult.getSuccess()) {
            result.setSuccess(Boolean.FALSE);
            throw new AccountProxyException("店长提现操作频繁,请稍后再试");
        }
        //提现扣手续费
        if (null != withdrawServicefeeSummary) {
            Result<Void> serviceFeeRes = withdrawTaxService.withdrawServiceFee(record, taxDetail, withdrawServicefeeSummary);
            if (!serviceFeeRes.getSuccess()) {
                return Results.error(CommonConstants.GENERAL_CODE, serviceFeeRes.getMessage());
            }
        }
        //构建账户中心请求参数
        TradeInfo tradeInfo = generateTradeInfo(record, AccountKeyConstants.NMDL.getName(), DetailTypeConstants.TRADE_TYPE_261.name());
        //构建平安银行请求参数
        WithdrawVO withdrawVO = generateTaxWithdrawVO(record, bankNo, taxDetail.getWithdraw());
        //白名单店长处理
        AgentWhiteListAccount whiteAccount = agentWhiteListAccountService.getOne(new LambdaQueryWrapper<AgentWhiteListAccount>().eq(AgentWhiteListAccount::getShopId, record.getShopId()).eq(AgentWhiteListAccount::getStatus, 1));
        if (whiteAccount != null) {
            result = agentWhiteListWithdraw(record, whiteAccount, tradeInfo, withdrawVO);
        } else {
            //3.账户中心提现
            com.akucun.common.Result<Void> tradeResult = accountCenterService.dealTrade(tradeInfo);
            if (!tradeResult.isSuccess()) {
                result.setSuccess(false);
                result.setCode(tradeResult.getCode());
                result.setMessage(tradeResult.getMessage());
            } else {
                //4.平安账户异步提现
                asyncWithdraw(withdrawVO);
            }
        }
        if (!result.getSuccess()) {
            withdrawApplyRecordMapper.updateWithdrawRecordStatus(record.getWithdrawNo(), ApplyStatus.FAIL.name(), result.getMessage());
            //扣税扣除
            withdrawTaxService.withdrawFailForTax(record, result.getMessage());
            //提现手续费回退
            withdrawTaxService.withdrawFailForServiceFee(record);
        }
        return result;

    }


    private Result<String> decouplingShopAgentWithdraw(WithdrawApplyRecord record){
        Result<String> result = success(record.getWithdrawNo());
        if (checkShopAgentWhitelist(record)){
            return Results.error(CommonConstants.GENERAL_CODE, "店长提现异常，请联系店主");
        }
        String bankNo = record.getBankNo();
        //银行卡号加密
        Result<String> res = encrypt(record.getBankNo());
        if (!res.getSuccess()) {
            return Results.error(res.getCode(), res.getMessage());
        }
        record.setBankNo(res.getData());
        //1.判断是否达到提现扣税金额限制
        WithdrawThresholdCheckBO withdrawThreshold = withdrawTaxService.checkWithdrawThreshold(record);
        if (!withdrawThreshold.getSuccess()) {
            return Results.error(withdrawThreshold.getCode(), withdrawThreshold.getTip());
        }
        //2.手续费
        WithdrawServicefeeSummary withdrawServicefeeSummary = null;
        if (freeWithdrawalsSwitch) {
            withdrawServicefeeSummary = getWithdrawServiceFeeSummary(record);
        }
        //3.税费计算
        WithdrawTaxDetail taxDetail;
        try {
            taxDetail = withdrawTaxService.applyWithdrawTax(record);
        } catch (BusinessException e) {
            Logger.error("decouplingShopAgentWithdraw applyWithdrawTax e:", e);
            return Results.error(e.getCode(), e.getMessage());
        }
        //4.提现申请记录入库
        int insertNum = saveWithdrawRecord(record);
        if (insertNum != 1) {
            return Results.error(CommonConstants.GENERAL_CODE, "提现申请记录落库失败");
        }
        //5.提现扣税明细&汇总入库
        Result<Void> taxRes = withdrawTaxService.withdrawTax(taxDetail);
        if (!taxRes.getSuccess()) {
            return Results.error(CommonConstants.GENERAL_CODE, taxRes.getMessage());
        }
        //6.提现扣手续费
        if (null != withdrawServicefeeSummary) {
            Result<Void> serviceFeeRes = withdrawTaxService.withdrawServiceFee(record, taxDetail, withdrawServicefeeSummary);
            if (!serviceFeeRes.getSuccess()) {
                return Results.error(CommonConstants.GENERAL_CODE, serviceFeeRes.getMessage());
            }
        }
        //8.账户中心提现
        TradeInfo tradeInfo = generateTradeInfo(record, AccountKeyConstants.NMDL.getName(), DetailTypeConstants.TRADE_TYPE_261.name());
        com.akucun.common.Result<Void> tradeResult = accountCenterService.dealTrade(tradeInfo);
        if (!tradeResult.isSuccess()) {
            Logger.error("decouplingShopAgentWithdraw accountCenterService.dealTrade tradeResult: {}", DataMask.toJSONString(tradeResult));
            bankWithdrawFailProcess(record, tradeResult.getMessage());
            return Results.error(CommonConstants.GENERAL_CODE, tradeResult.getMessage());
        }
        //实际提现到账金额
        int withdrawAmt = taxDetail.getWithdraw().multiply(new BigDecimal(100)).intValue();
        //9.公司账户分账到店主子账户
        com.akucun.fps.common.entity.Result<String> creditResult;
        try {
            creditResult = companyAccountCredit(record, withdrawAmt, DetailTypeConstants.TRADE_TYPE_261.name());
        } catch (Exception e){
            Logger.error("shopAgentWithdraw companyAccountCredit exception : customerCode:{},withdrawNo:{} ",
                    record.getCustomerCode(), record.getWithdrawNo(), e);
            result = Results.error(CommonConstants.GENERAL_CODE, "提现失败请重试");
            asyncAccountRefund(record, "提现失败请重试");
            bankWithdrawFailProcess(record, "提现失败请重试");
            return result;
        }
        if (!creditResult.isSuccess()) {
            Logger.error("decouplingShopAgentWithdraw companyAccountCredit res:{}", JSONObject.toJSONString(creditResult));
            result = Results.error(CommonConstants.GENERAL_CODE, creditResult.getErrorMessage());
            asyncAccountRefund(record, creditResult.getErrorMessage());
            bankWithdrawFailProcess(record, creditResult.getErrorMessage());
            return result;
        }
        //10.异步调用平安提现接口
        WithdrawVO withdrawVO = generateWithdrawVO(record, bankNo);
        withdrawVO.setAmount(withdrawAmt);
        asyncWithdraw(withdrawVO);
        return result;
    }

    /**
     * 白名单店长提现问题：
     * 1.数据没有刷完有资损风险（包含角色交叉的店长）；
     * 2.新增白名单店长表，不在白名单表里的店长拒绝提现
     * @param record
     * @return true 表示白名单店长时未配置灰度白名单店长，提现拒绝；false表示白名单店长配置正常，可以正常提现
     */
    private boolean checkShopAgentWhitelist(WithdrawApplyRecord record) {
        AgentWhiteListAccount whiteAccount = agentWhiteListAccountService.getOne(new LambdaQueryWrapper<AgentWhiteListAccount>().eq(AgentWhiteListAccount::getShopId, record.getShopId()).eq(AgentWhiteListAccount::getStatus, 1));
        if(whiteAccount == null){
            return false;
        }
        //白名单店长时，非灰度白名单拒绝提现
        boolean grayWhitelist = pinganDecoupleService.checkShopAgentWhitelist(record.getCustomerCode(), record.getCustomerType());
        //grayWhitelist true 说明店长是灰度白名单店长
        if (!grayWhitelist){
            //用作日志监控
            Logger.warn("shopAgent gray whitelist not exist, customerCode:{}", record.getCustomerCode());
        }
        return !grayWhitelist;
    }

    /**
     * 白名单店长提现
     *
     * @param record
     * @param whiteAccount
     * @param tradeInfo
     */
    private Result<String> agentWhiteListWithdraw(WithdrawApplyRecord record, AgentWhiteListAccount whiteAccount, TradeInfo tradeInfo, WithdrawVO withdrawVO) {
        Result<String> result = success(record.getWithdrawNo());
        tradeInfo.setAccountTypeKey(AccountKeyConstants.NM.getName());
        tradeInfo.setCustomerCode(whiteAccount.getCustomerCode());
        tradeInfo.setTradeType(DetailTypeConstants.TRADE_TYPE_044.name());
        //1.账户中心店主提现
        com.akucun.common.Result<Void> shopkeeperTradeResult = accountCenterService.dealTrade(tradeInfo);
        if (!shopkeeperTradeResult.isSuccess()) {
            Logger.warn("白名单店主账户中心提现失败：{}", DataMask.toJSONString(shopkeeperTradeResult));
            return Results.error(CommonConstants.GENERAL_CODE, shopkeeperTradeResult.getMessage());
        }
        TradeInfo tradeNew = new TradeInfo();
        BeanUtils.copyProperties(tradeInfo, tradeNew);
        tradeNew.setCustomerCode(record.getCustomerCode());
        tradeNew.setTradeType(DetailTypeConstants.TRADE_TYPE_261.name());
        tradeNew.setAccountTypeKey(AccountKeyConstants.NMDL.getName());
        //2.白名单店主账户提现金额转移到公户
        DealTradeVO dealTrade = new DealTradeVO();
        dealTrade.setRemark("店长提现白名单交易");
        dealTrade.setTranAmount(record.getAmount());
        dealTrade.setCustomerCode(whiteAccount.getCustomerCode());
        dealTrade.setCustomerType(CustomerType.NM.name());
        dealTrade.setOrderNo(record.getWithdrawNo());
        com.akucun.fps.common.entity.Result<Void> dealResult = memberServiceApi.dealTrade(dealTrade);
        if (!dealResult.isSuccess()) {
            Logger.error("agentWhiteListWithdraw withdraw error: {}", dealResult.getErrorMessage());
            accountCenterRefund(tradeInfo, dealResult.getErrorMessage());
            return Results.error(CommonConstants.GENERAL_CODE, "提现失败，请联系店主");
        }
        //3.公户分账提现金额到店长账户
        CashCreditVO credit = new CashCreditVO();
        credit.setCustomerType(CustomerType.NMDL.name());
        credit.setTranFee(0);
        credit.setBillNo(record.getWithdrawNo());
        credit.setBillType("NMDL");
        credit.setTranAmount(record.getAmount().multiply(BigDecimal.valueOf(100)).intValue());
        credit.setThirdCustId(record.getCustomerCode());
        credit.setRemark("白名单店长提现分账");
        com.akucun.fps.common.entity.Result<String> creResult = assetsServiceApi.credit(credit);
        if (!creResult.isSuccess()) {
            Logger.error("白名单店长提现分账失败: {}", creResult.getErrorMessage());
            //白名单提现交易
            dealTrade.setRemark("白名单店长提现交易退款");
            dealTrade.setUniqueKey(record.getWithdrawNo());
            com.akucun.fps.common.entity.Result<Void> refundResult = memberServiceApi.dealTradeRefund(dealTrade);
            if (!refundResult.isSuccess()) {
                Logger.error("店长提现店主金额撤销返还失败: {}", refundResult.getErrorMessage());
            }
            accountCenterRefund(tradeInfo, refundResult.getErrorMessage());
            return Results.error(CommonConstants.GENERAL_CODE, "提现失败，请联系店主或稍后重试");
        }
        //4.账户中心店长提现
        com.akucun.common.Result<Void> tradeResult = accountCenterService.dealTrade(tradeNew);
        if (!tradeResult.isSuccess()) {
            Logger.info("白名单店长账户中心提现失败：{}", DataMask.toJSONString(tradeResult));
            accountCenterRefund(tradeInfo, tradeResult.getMessage());
            agentWhiteAccountRollback(record, whiteAccount);
            return Results.error(CommonConstants.GENERAL_CODE, "提现失败，请联系店主或稍后重试");
        }
        //5.店长平安账户异步提现
        asyncWithdraw(withdrawVO);
        return result;
    }

    private void accountCenterRefund(TradeInfo tradeInfo, String message) {
        if (AccountKeyConstants.NM.getName().equals(tradeInfo.getAccountTypeKey())) {
            tradeInfo.setTradeType(DetailTypeConstants.TRADE_TYPE_045.name());
        } else if (AccountKeyConstants.NMDL.getName().equals(tradeInfo.getAccountTypeKey())) {
            tradeInfo.setTradeType(DetailTypeConstants.TRADE_TYPE_262.name());
        }
        tradeInfo.setRemark(message);
        com.akucun.common.Result<Void> refundResult = accountCenterService.dealTrade(tradeInfo);
        if (!refundResult.isSuccess()) {
            Logger.warn("账户中心退款失败 tradeInfo:{}, result:{}", tradeInfo, DataMask.toJSONString(refundResult));
        }
    }

    /**
     * 白名单店长提现失败撤销处理
     *
     * @param record
     * @param whiteAccount
     */
    private void agentWhiteAccountRollback(WithdrawApplyRecord record, AgentWhiteListAccount whiteAccount) {
        RevokeCreditVO revoke = new RevokeCreditVO();
        revoke.setThirdHtId("NMDL" + record.getWithdrawNo());
        revoke.setCustomerType(CustomerType.NMDL.name());
        revoke.setTranAmount(record.getAmount().multiply(BigDecimal.valueOf(100)).intValue());
        //白名单撤销异步任务
        PostActionItemBO itemBO = PostActionItemBO.builder()
                .bizId(revoke.getThirdHtId()).paramObject(revoke).remark("白名单撤销任务")
                .actionType(PostActionTypes.PINGAN_DELAY_REVOKE.getName()).status(PostActionExecStatus.EXECUTE.value())
                .nextRetryTime(LocalDateTime.now().plusSeconds(15)).build();
        postActionService.addAction(itemBO);
        //白名单店主退款任务
        DealTradeVO dealTradeRefundVO = new DealTradeVO();
        dealTradeRefundVO.setRemark("代理提现白名单交易退款");
        dealTradeRefundVO.setTranAmount(record.getAmount());
        dealTradeRefundVO.setCustomerCode(whiteAccount.getCustomerCode());
        dealTradeRefundVO.setCustomerType(CustomerType.NM.name());
        dealTradeRefundVO.setOrderNo(record.getWithdrawNo());
        dealTradeRefundVO.setUniqueKey(record.getWithdrawNo());
        //退款异步任务
        PostActionItemBO refund = PostActionItemBO.builder()
                .bizId(revoke.getThirdHtId()).paramObject(dealTradeRefundVO).remark("白名单退款任务")
                .actionType(PostActionTypes.PINGAN_REFUND.getName()).status(PostActionExecStatus.EXECUTE.value())
                .nextRetryTime(LocalDateTime.now().plusSeconds(15)).build();
        postActionService.addAction(refund);
    }

    /**
     * 团长帮卖提现到微信余额
     *
     * @param record
     * @return
     */
    private Result<String> groupSellerWithdraw(WithdrawApplyRecord record) {
        //微信渠道
        record.setWithdrawChannel(WithdrawChannelConstants.WECHAT.getName());

        Result<WechatInfoBindVO> res = wechatAuthService.queryBindInfo(record.getCustomerCode(), record.getCustomerType());
        if (res == null || !res.getSuccess() || res.getData() == null) {
            return Results.error(CommonConstants.GENERAL_CODE, "用户微信信息未绑定");
        }
        Result<String> result = success(record.getWithdrawNo());
        //1.提现记录入库
        //插入提现申请记录表
        int insertNum = saveWithdrawRecord(record);
        if (insertNum != 1) {
            return Results.error(CommonConstants.GENERAL_CODE, "提现申请记录落库失败");
        }
        //2.账户中心余额扣减
        TradeInfo tradeInfo = generateTradeInfo(record, AccountKeyConstants.TB.name(), DetailTypeConstants.TRADE_TYPE_442.name());
        com.akucun.common.Result<Void> tradeResult = accountCenterService.dealTrade(tradeInfo);
        if (!tradeResult.isSuccess()) {
            Logger.error("groupSellerWithdraw tradeResult: {}", DataMask.toJSONString(tradeResult));
            withdrawApplyRecordMapper.updateWithdrawRecordStatus(record.getWithdrawNo(), ApplyStatus.FAIL.name(), tradeResult.getMessage());
            return Results.error(CommonConstants.GENERAL_CODE, tradeResult.getMessage());
        }
        //3.提现到微信余额
        WechatInfoBindVO vo = res.getData();
        PaymentTransferReq req = new PaymentTransferReq();
        req.setSourceCode("GROUP");
        req.setChannelCode(vo.getChannelCode());
        req.setSourceNo(record.getWithdrawNo() + record.getCustomerCode().substring(record.getCustomerCode().length() - 4));
        req.setAmount(record.getAmount());
        req.setOpenId(vo.getOpenId());
        req.setCustomerCode(record.getCustomerCode());
        req.setCustomerType(record.getCustomerType());
        req.setCustomerName(vo.getCustomerName());
        req.setTenantId(vo.getTenantId());
        req.setTransferType(TransferTypeEnum.WECHAT);
        req.setRemark("团咣咣提现：" + record.getWithdrawNo());
        req.setCheckName(Boolean.TRUE);
        try {
            InetAddress inetAddress = InetAddress.getLocalHost();
            req.setTerminalIp(inetAddress.getHostAddress());
        } catch (UnknownHostException e) {
            Logger.warn("InetAddress.getLocalHost exception!");
            req.setTerminalIp("127.0.0.1");
        }
        Result<PaymentTransferResp> transferRes = payTransferService.transfer(req);
        if (!transferRes.getSuccess()) {
            Logger.error("groupSellerWithdraw withdrawResult: {}", DataMask.toJSONString(transferRes));
            result = Results.error(CommonConstants.GENERAL_CODE, transferRes.getMessage());
            //调用账户中心进行金额退回操作
            TradeInfo tradeInfoRefund = generateTradeInfo(record, AccountKeyConstants.TB.name(), DetailTypeConstants.TRADE_TYPE_443.name());
            tradeInfoRefund.setRemark(transferRes.getMessage());
            com.akucun.common.Result<Void> tradeRefundResult = accountCenterService.dealTrade(tradeInfoRefund);
            if (!tradeRefundResult.isSuccess()) {
                result = Results.error(CommonConstants.GENERAL_CODE, tradeRefundResult.getMessage());
                Logger.error("groupSellerWithdraw withdraw refund fail, withdrawNo: {}", record.getWithdrawNo());
            }
            withdrawApplyRecordMapper.updateWithdrawRecordStatus(record.getWithdrawNo(), ApplyStatus.FAIL.name(), transferRes.getMessage());
            return result;
        } else {
            // 成功后更新提现状态成功
            withdrawApplyRecordMapper.updateWithdrawRecordStatus(record.getWithdrawNo(), ApplyStatus.SUCC.name(), transferRes.getMessage());

            //TODO 新增拉取回执单地址定时任务
        }

        return result;
    }

    @Override
    public Result<Void> notifyWithdrawResult(NotifyWithdrawVO vo) {
        Logger.info("notifyWithdrawResult 通知返回消息:{}", JSONObject.toJSONString(vo));
        Result<Void> result = success();
        if (vo == null || StringUtils.isEmpty(vo.getCustomerCode()) || vo.getAmount() == null || StringUtils.isEmpty(vo.getSourceBillNo())) {
            return Results.error(CommonConstants.GENERAL_CODE, "提现结果通知参数缺失");
        }
        if (!StringUtils.isEmpty(vo.getErrorMessage())) {
            String convertMsg = ErrorMessageConvertUtils.errorDescConvert(vo.getErrorMessage());
            vo.setErrorMessage(StringUtils.isNotEmpty(convertMsg) ? convertMsg : "操作失败，请咨询银行客服");
        }
        try {
            if (CustomerType.NMDL.name().equals(vo.getCustomerType())) {
                vo.setCustomerCode(vo.getCustomerCode().substring(4));
            }
            AccountTenantCustomer customer = accountTenantCustomerMapper.selectOne(new LambdaQueryWrapper<AccountTenantCustomer>().eq(AccountTenantCustomer::getCustomerCode, vo.getCustomerCode()).eq(AccountTenantCustomer::getCustomerType, vo.getCustomerType()));
            if (customer != null) {
                return tenantWithdrawNotify(vo);
            }
            //常规提现
            WithdrawApplyRecord record = withdrawApplyRecordMapper.selectOne(new LambdaQueryWrapper<WithdrawApplyRecord>().eq(WithdrawApplyRecord::getWithdrawNo, vo.getSourceBillNo()));
            if (record == null) {
                return Results.error(CommonConstants.GENERAL_CODE, "提现通知未查询到提现单据");
            }
            String dbApplyStatus = record.getApplyStatus();
            if (StringUtils.isBlank(dbApplyStatus) || dbApplyStatus == ApplyStatus.SUCC.name() || dbApplyStatus == ApplyStatus.FAIL.name()) {
                Logger.warn("平安提现状态已经为终态, 异步通知参数：{}", JSONObject.toJSONString(vo));
            }
            record.setRemark(vo.getErrorMessage());
            if (CustomerType.NM.name().equals(vo.getCustomerType()) || CustomerType.DG.name().equals(vo.getCustomerType())
                    || CustomerType.NMDL.name().equals(vo.getCustomerType()) || CustomerType.DCC.name().equals(vo.getCustomerType())
                    || CustomerType.OP.name().equals(vo.getCustomerType()) || CustomerType.AT.getName().equals(vo.getCustomerType())) {
                //提现成功
                if (vo.getResult()) {
                    withdrawApplyRecordMapper.updateWithdrawRecordStatus(vo.getSourceBillNo(), ApplyStatus.SUCC.name(), "");

                    //提现成功扣税
                    withdrawTaxService.withdrawSuccForTax(record);

                    //新增拉取回执单地址定时任务
                    SpringContextHolder.getBean(WithdrawReceiptDownloadTask.class).init(WithdrawChannelConstants.PINGAN.getName(),
                            vo.getSourceBillNo(), null, null, false, vo.getCustomerType());
                } else {
                    Logger.info("notifyWithdrawResult 用户提现失败:{}", JSONObject.toJSONString(vo));
                    withdrawApplyRecordMapper.updateWithdrawRecordStatus(vo.getSourceBillNo(), ApplyStatus.FAIL.name(), vo.getErrorMessage());
                    //提现失败扣税回退
                    withdrawTaxService.withdrawFailForTax(record, vo.getErrorMessage());
                    //提现失败手续费回退
                    withdrawTaxService.withdrawFailForServiceFee(record);
                    //提现失败平安子账户反清分处理
                    if (decouplingConfig.decouplingSwitch(record.getCustomerCode(), record.getCustomerType())) {
                        WithdrawTaxDetail taxDetail = withdrawTaxService.getWithdrawTaxDetail(record.getWithdrawNo());
                        if (taxDetail == null) {
                            Logger.error("withdraw antiClearingProcess fail, 未查询到计税明细，异步通知参数：{}", JSONObject.toJSONString(vo));
                            //返回给pingan-web通知状态成功，后续走人工处理账户中心退款
                            return success();
                        }
                        RevokeCreditVO creditVO = new RevokeCreditVO();
                        creditVO.setCustomerType(record.getCustomerType());

                        if (isRevokeCreditThirdHtIdNewrule) {
                            creditVO.setThirdHtId(record.getWithdrawNo());
                        } else {
                            creditVO.setThirdHtId(getPinganWithdrawTradeType(record.getCustomerType()) + record.getWithdrawNo());
                        }
                        creditVO.setTranAmount(taxDetail.getWithdraw().multiply(new BigDecimal(100)).intValue());
                        com.akucun.fps.common.entity.Result<String> revokeResult = assetsServiceApi.revokeCredit(creditVO);
                        //boolean antiClearingStatus = pinganDecoupleService.antiClearing4WithdrawFail(record.getCustomerCode(), record.getCustomerType(), vo.getAmount().toPlainString(), record.getWithdrawNo());
                        if (!revokeResult.isSuccess()){
                            Logger.warn("withdraw antiClearingProcess fail, 异步通知参数：{}，撤销分账请求：{}", JSONObject.toJSONString(vo), DataMask.toJSONString(creditVO));
                            // TODO:增加稽核告警项，超过十分钟任务执行失败则稽核告警；
                            PostActionItemBO itemBO = PostActionItemBO.builder()
                                    .bizId(record.getWithdrawNo())
                                    .paramObject(creditVO)
                                    .remark("异步平安提现失败分账撤销")
                                    .actionType(PostActionTypes.PINGAN_REVOKE_CREDIT.getName())
                                    .status(PostActionExecStatus.EXECUTE.value())
                                    .nextRetryTime(LocalDateTime.now().plusSeconds(10))
                                    .retryNums(0)
                                    .build();
                            postActionService.addAction(itemBO);
                            //返回给pingan-web通知状态成功，后续走人工处理账户中心退款
                            //return Result.success();
                        }
                    }

                    //为h5代理提现时，提现失败操作
                    if (CustomerType.NMDL.name().equals(vo.getCustomerType())) {
                        return shopAgentNotifyProcess(vo, record);
                    }
                    //调用账户中心进行金额操作
                    TradeInfo tradeInfoRefund = generateTradeInfo(record, AccountKeyConstants.NM.getName(), DetailTypeConstants.TRADE_TYPE_045.name());
                    if (CustomerType.DG.name().equals(record.getCustomerType())) {
                        tradeInfoRefund.setAccountTypeKey(AccountKeyConstants.DG.getName());
                        tradeInfoRefund.setTradeType(DetailTypeConstants.TRADE_TYPE_184.name());
                    } else if (CustomerType.DCC.name().equals(record.getCustomerType())) {
                        tradeInfoRefund.setAccountTypeKey(AccountKeyConstants.DCC.name());
                        tradeInfoRefund.setTradeType(DetailTypeConstants.TRADE_TYPE_362.name());
                    } else if (CustomerType.OP.name().equals(record.getCustomerType())) {
                        tradeInfoRefund.setAccountTypeKey(AccountKeyConstants.OP.name());
                        tradeInfoRefund.setTradeType(DetailTypeConstants.TRADE_TYPE_134.name());
                    } else if (CustomerType.AT.name().equals(record.getCustomerType())) {
                        tradeInfoRefund.setAccountTypeKey(AccountKeyConstants.AT.name());
                        tradeInfoRefund.setTradeType(DetailTypeConstants.TRADE_TYPE_422.name());
                    }
                    com.akucun.common.Result<Void> tradeRefundResult = accountCenterService.dealTrade(tradeInfoRefund);
                    if (!tradeRefundResult.isSuccess()) {
                        Logger.error("notifyWithdrawResult 提现返还失败 :{}", tradeRefundResult.getMessage());
                        return Results.error(CommonConstants.GENERAL_CODE, tradeRefundResult.getMessage());
                    }
                }
            } else {
                //商户提现
                return merchantWithdrawNotify(vo, record);
            }
        } catch (Exception e) {
            Logger.error("提现通知异常: {}", result.getMessage(), e);
            return Results.error(CommonConstants.GENERAL_CODE, "提现通知异常");
        }
        return result;
    }

    private String getPinganWithdrawTradeType(String customerType) {
        if(CustomerType.NM.name().equals(customerType)) {
            return "TRADE_TYPE_044";
        } else if(CustomerType.NMDL.name().equals(customerType)) {
            return "TRADE_TYPE_261";
        } else if(CustomerType.DG.name().equals(customerType)) {
            return "TRADE_TYPE_183";
        } else if(CustomerType.OP.name().equals(customerType)) {
            return "TRADE_TYPE_133";
        } else if(CustomerType.AT.name().equals(customerType)) {
            return "TRADE_TYPE_421";
        } else if(CustomerType.DCC.name().equals(customerType)) {
            return "TRADE_TYPE_361";
        } else {
            return null;
        }
    }

    /**
     * 店长提现失败处理
     *
     * @param vo
     * @param record
     * @return
     */
    private Result<Void> shopAgentNotifyProcess(NotifyWithdrawVO vo, WithdrawApplyRecord record) {
        Logger.info("shopAgentNotifyProcess vo:{}", vo);
        Result<Void> result = success();

        Result<Void> checkResult = duplicateCheckService.checkDuplicate("NMDL_REFUND_" + record.getWithdrawNo(), Boolean.FALSE);
        if (!checkResult.getSuccess()) {
            Logger.info("h5NotifyLogic 店长提现触发幂等:{}", "NMDL_REFUND_" + record.getWithdrawNo());
            return result;
        }
        try {
            //调用账户中心进行金额操作
            TradeInfo tradeInfo = generateTradeInfo(record, AccountKeyConstants.NMDL.getName(), DetailTypeConstants.TRADE_TYPE_262.name());
            if (!decouplingConfig.decouplingSwitch(record.getCustomerCode(), record.getCustomerType())){
                //查询代理白名单
                AgentWhiteListAccount whiteAccount = agentWhiteListAccountService.getOne(new LambdaQueryWrapper<AgentWhiteListAccount>().eq(AgentWhiteListAccount::getShopId, record.getShopId()).eq(AgentWhiteListAccount::getStatus, 1));
                Logger.info("shopAgentNotifyProcess whiteAccount:{}", DataMask.toJSONString(whiteAccount));
                //白名单代理处理逻辑
                if (whiteAccount != null) {
                    TradeInfo tradeNew = new TradeInfo();
                    //代理处理
                    tradeInfo.setAccountTypeKey(AccountKeyConstants.NM.getName());
                    tradeInfo.setCustomerCode(whiteAccount.getCustomerCode());
                    tradeInfo.setTradeType(DetailTypeConstants.TRADE_TYPE_045.name());
                    BeanUtils.copyProperties(tradeInfo, tradeNew);

                    tradeNew.setCustomerCode(record.getCustomerCode());
                    tradeNew.setTradeType(DetailTypeConstants.TRADE_TYPE_262.name());
                    tradeNew.setAccountTypeKey(AccountKeyConstants.NMDL.getName());

                    PostActionItemBO itemBO = PostActionItemBO.builder()
                            .bizId(vo.getSourceBillNo())
                            .paramObject(tradeNew)
                            .remark("白名单代理提现失败返还")
                            .actionType(PostActionTypes.ACCOUNT_CENTER_COMPENSATE.getName())
                            .status(PostActionExecStatus.EXECUTE.value()).build();
                    postActionService.addAction(itemBO);
                    agentWhiteAccountRollback(record, whiteAccount);
                }
            }
            Logger.info("shopAgentNotifyProcess accountCenterService.dealTrade tradeInfo:{}", DataMask.toJSONString(tradeInfo));
            com.akucun.common.Result<Void> tradeResult = accountCenterService.dealTrade(tradeInfo);
            if (!tradeResult.isSuccess()) {
                Logger.error("shopAgentNotifyProcess 调用账户中心失败{}", tradeResult.getMessage());
                result.setSuccess(Boolean.FALSE);
                result.setMessage(tradeResult.getMessage());
            }
        } catch (Exception e) {
            result.setMessage("代理提现失败返金异常");
            result.setSuccess(Boolean.FALSE);
            Logger.error(result.getMessage(), e);
        }
        return result;
    }

    /**
     * 商户提现
     *
     * @param vo
     * @return
     */
    private Result<Void> merchantWithdrawNotify(NotifyWithdrawVO vo, WithdrawApplyRecord record) {
        //平安商户提现 SH
        Result<Void> result = success();
        int doNum;
        if (vo.getResult()) {
            doNum = withdrawApplyRecordMapper.updateWithdrawRecordStatus(vo.getSourceBillNo(), ApplyStatus.SUCC.name(), "");

            //新增拉取回执单地址定时任务
            SpringContextHolder.getBean(WithdrawReceiptDownloadTask.class).init(WithdrawChannelConstants.PINGAN.getName(),
                    vo.getSourceBillNo(), null, null, false, record.getCustomerType());
        } else {
            Result<Void> dupResult = duplicateCheckService.checkDuplicate(DetailTypeConstants.UN_WITHDRAW.name() + "_" + vo.getSourceBillNo(), Boolean.FALSE);
            if (!dupResult.getSuccess()) {
                Logger.warn("notifyWithdrawResult 提现通知出现并发：{}", dupResult.getMessage() + vo.getSourceBillNo());
                return Results.error(CommonConstants.GENERAL_CODE, "提现通知出现并发");
            }
            doNum = withdrawApplyRecordMapper.updateWithdrawRecordStatus(vo.getSourceBillNo(), ApplyStatus.FAIL.name(), vo.getErrorMessage());
            //平安商户提现结果处理 调用账户中心进行金额操作
            TradeInfo tradeInfo = generateTradeInfo(record, AccountKeyConstants.SH.getName(), DetailTypeConstants.UN_WITHDRAW.name());
            com.akucun.common.Result<Void> tradeResult = accountCenterService.dealTrade(tradeInfo);
            if (!tradeResult.isSuccess()) {
                return Results.error(CommonConstants.GENERAL_CODE, "提现通知账户操作异常");
            }
        }
        if (doNum > 1) {
            return Results.error(CommonConstants.GENERAL_CODE, "更改提现结果异常");
        }
        if (vo.getResult() && result.getSuccess()) {
            withdrawPostProcess(vo.getCustomerCode(), vo.getAmount());
        }
        return result;
    }

    /**
     * 平安商户提现成功后置处理
     *
     * @param customerCode
     * @param amount
     */
    private void withdrawPostProcess(String customerCode, BigDecimal amount) {
        try {
            if (notifyMerchantSwitch) {
                MerchantInfoQuery var1 = new MerchantInfoQuery();
                var1.setMerchantCode(customerCode);
                com.akucun.fps.common.entity.Result<MerchantInfo> cusInfo = customerInfoService.selectCustomerInfo(var1);
                if (cusInfo != null && cusInfo.isSuccess() && cusInfo.getData() != null && !org.apache.commons.lang.StringUtils.isBlank(cusInfo.getData().getContactsPhoneEncrypt())) {
                    //解密手机号
                    SendSMSParam sendSMSParam = new SendSMSParam();
                    sendSMSParam.setRemark("2FPS162719");
                    Map<String, Object> params = new LinkedHashMap<>();
                    params.put("time", DateUtils.convert(new Date()));
                    params.put("money", amount);
                    sendSMSParam.setParams(params);
                    sendSMSParam.setType("2");
                    sendSMSParam.setSource("FPS");
                    sendSMSParam.setMobile(cusInfo.getData().getContactsPhoneEncrypt());
                    iRocketMqService.asyncSend("AKUCUN_SEND_MESSAGE_BUSINESS", "SEND_MESSAGE", sendSMSParam, sendSMSParam.getMobile());
                }
            }

            LambdaQueryWrapper<AccountTotalAmount> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(AccountTotalAmount::getCustomerCode, customerCode)
                    .eq(AccountTotalAmount::getCustomerType, CustomerType.SH.name());
            AccountTotalAmount param = accountTotalAmountMapper.selectOne(wrapper);
            if (param != null) {
                param.setCashAmount(param.getCashAmount().add(amount));
                accountTotalAmountMapper.updateById(param);
            } else {
                param = new AccountTotalAmount();
                param.setCustomerCode(customerCode);
                param.setCustomerType(CustomerType.SH.name());
                param.setIncomeAmount(BigDecimal.ZERO);
                param.setCashAmount(amount);
                accountTotalAmountMapper.insert(param);
            }
        } catch (Exception e) {
            Logger.error("withdrawPostProcess exception:", e);
        }

    }

    /**
     * 租户店主店长提现通知
     *
     * @param vo
     * @return
     */
    private Result<Void> tenantWithdrawNotify(NotifyWithdrawVO vo) {
        Result<Void> result = success();
        TenantWithdrawApply apply = tenantWithdrawApplyMapper.selectOne(new LambdaQueryWrapper<TenantWithdrawApply>().eq(TenantWithdrawApply::getWithdrawNo, vo.getSourceBillNo()));
        if (apply == null) {
            return Results.error(CommonConstants.GENERAL_CODE, "提现通知未查询到提现单据");
        }
        if (vo.getResult()) {
            tenantWithdrawApplyMapper.updateWithdrawRecordStatus(vo.getSourceBillNo(), ApplyStatus.SUCC.name(), "");
//            withdrawTaxService.tenantWithdrawSuccForServiceFee(apply);

            //新增拉取回执单地址定时任务
            SpringContextHolder.getBean(WithdrawReceiptDownloadTask.class).init(WithdrawChannelConstants.PINGAN.getName(),
                    vo.getSourceBillNo(), null, null, true, apply.getCustomerType());
        } else {
            Logger.info("tenantWithdrawNotify 用户提现失败:{}", JSONObject.toJSONString(vo));
            tenantWithdrawApplyMapper.updateWithdrawRecordStatus(vo.getSourceBillNo(), ApplyStatus.FAIL.name(), vo.getErrorMessage());
            withdrawTaxService.tenantWithdrawFailForServiceFee(apply);
            //调用账户中心进行金额操作
            TradeInfo tradeInfo = new TradeInfo();
            tradeInfo.setAccountTypeKey(AccountKeyConstants.NM.getName());
            tradeInfo.setTradeType(DetailTypeConstants.TRADE_TYPE_045.name());
            tradeInfo.setSourceBillNo(apply.getWithdrawNo());
            tradeInfo.setTradeNo(apply.getWithdrawNo());
            tradeInfo.setAmount(apply.getAmount());
            tradeInfo.setCustomerCode(apply.getCustomerCode());
            tradeInfo.setRemark(apply.getRemark());
            //为h5代理提现时，提现失败操作
            if (CustomerType.NMDL.name().equals(vo.getCustomerType())) {
                tradeInfo.setAccountTypeKey(AccountKeyConstants.NMDL.getName());
                tradeInfo.setTradeType(DetailTypeConstants.TRADE_TYPE_262.name());
            }
            com.akucun.common.Result<Void> tradeRefundResult = accountCenterService.dealTrade(tradeInfo);
            if (!tradeRefundResult.isSuccess()) {
                result = Results.error(CommonConstants.GENERAL_CODE, tradeRefundResult.getMessage());
                Logger.error("tenantWithdrawNotify 提现返还失败 :{}", tradeRefundResult.getMessage());
            }
        }
        return result;
    }


    @Override
    public Result<AccountWithdrawVO> queryWithdrawDetail(WithdrawQueryReq withdrawQueryReq) {
        Result<AccountWithdrawVO> result = new Result<>();
        result.setSuccess(true);
        try {
            if (withdrawQueryReq == null || StringUtils.isBlank(withdrawQueryReq.getWithdrawNo())) {
                result.setSuccess(Boolean.FALSE);
                result.setMessage("提现查询详情参数不可为空");
                return result;
            }
            WithdrawApplyRecord record = withdrawApplyRecordMapper.selectOne(new LambdaQueryWrapper<WithdrawApplyRecord>().eq(WithdrawApplyRecord::getWithdrawNo, withdrawQueryReq.getWithdrawNo()));
            if (record == null) {
                result.setSuccess(Boolean.FALSE);
                result.setMessage("无此提现详情记录");
                return result;
            }
            AccountWithdrawVO vo = new AccountWithdrawVO();
            BeanUtils.copyProperties(record, vo);
            vo.setExt(record.getExt());
            WithdrawTaxDetail detail = withdrawTaxService.getWithdrawTaxDetail(record.getWithdrawNo());
            if (null != detail) {
                vo.setTaxFee(detail.getTaxFee());
                vo.setWithdraw(detail.getWithdraw());
                if (null != detail.getCurrMonthSummary()) {
                    vo.setCurrMonthSummary(detail.getCurrMonthSummary());
                    vo.setFeeRate(detail.getFeeRate());
                }
                vo.setFreeFeeAmount(detail.getWithdraw());
            } else {
                vo.setTaxFee(BigDecimal.ZERO);
                vo.setWithdraw(null == record.getServiceAmount() ? record.getAmount() : record.getAmount().subtract(record.getServiceAmount()));
                vo.setFreeFeeAmount(record.getAmount());
            }
            vo.setServiceAmount(null == record.getServiceAmount() ? BigDecimal.ZERO : record.getServiceAmount());
            if (StringUtils.isNotBlank(record.getBankName())) {
                //银行名称展示优化
                String bankName = record.getBankName();
                int i = bankName.indexOf(BANK_STR);
                bankName = i > 0 ? bankName.substring(0, i + 2) : bankName;
                vo.setBankName(bankName);
                ResultList<BankInfoManageResp> resultList = acquireJointLineNumbeServiceApi.selectByBankName(bankName);
                if (null != resultList && resultList.isSuccess() && !CollectionUtils.isEmpty(resultList.getDatalist())) {
                    for (BankInfoManageResp resp : resultList.getDatalist()) {
                        vo.setBankLogo(resp.getImageUrl());
                    }
                }
            }
            if (WithdrawChannelConstants.WECHAT.getName().equals(record.getWithdrawChannel()) && !ApplyStatus.FAIL.name().equals(record.getApplyStatus())) {
                WechatWithdrawSummary summary = withdrawTaxService.queryWechatWithdrawSummary(record.getIdentifyNo(), record.getCreateTime());
                if (summary != null && summary.getSumWithdrawAmt() != null) {
                    BigDecimal discrepancy = new BigDecimal(wechatWithdrawSummaryThreshold).subtract(summary.getSumWithdrawAmt());
                    //总额度减累计金额结果为正值，说明额度未用完，展示全部优惠券
                    if (discrepancy.compareTo(BigDecimal.ZERO) < 0) {
                        //差额为负值，取当前交易金额相加，结果未为正值，则是需要展示的优惠券金额；为负值说明额度已用完，展示0.
                        BigDecimal addRes = vo.getFreeFeeAmount().add(discrepancy);
                        vo.setFreeFeeAmount(addRes.compareTo(BigDecimal.ZERO) > 0 ? addRes : BigDecimal.ZERO);
                    }
                }
            } else {
                vo.setFreeFeeAmount(BigDecimal.ZERO);
            }
            result.setData(vo);
        } catch (Exception e) {
            result.setSuccess(Boolean.FALSE);
            result.setMessage("查询提现详情异常");
            Logger.error(result.getMessage(), e);
        }
        return result;
    }

    @Override
    public Result<Void> queryMerchantWechatWithdraw(WechatWithdrawRequest request) {

        WithdrawApplyRecord record = withdrawApplyRecordMapper.selectOne(new LambdaQueryWrapper<WithdrawApplyRecord>().eq(WithdrawApplyRecord::getWithdrawNo, request.getWithdrawNo()));
        if (record == null) {
            return Results.error(CommonConstants.GENERAL_CODE, "未查询到微信商户提现单据");
        }
        QueryMerchantWithdrawalDTO withdrawalDTO = new QueryMerchantWithdrawalDTO();
        withdrawalDTO.setSubMchid(request.getSubMerchantId());
        withdrawalDTO.setOutRequestNo(request.getWithdrawNo());
        Logger.info("queryMerchantWechatWithdraw req:{}", DataMask.toJSONString(withdrawalDTO));
        Result<ResQueryMerchantWithdrawalDTO> result = withdrawalFacade.queryMerchantWithdrawal(withdrawalDTO);
        Logger.info("queryMerchantWechatWithdraw result:{}", DataMask.toJSONString(result));
        if (result == null) {
            return Results.error("商户提现查询失败");
        }
        if (!result.getSuccess()) {
            return error(result.getCode(), result.getMessage());
        }
        ResQueryMerchantWithdrawalDTO res = result.getData();
        if (WECHAT_WITHDRAW_SUCC.equalsIgnoreCase(res.getStatus())) {
            withdrawApplyRecordMapper.updateWithdrawRecordStatus(res.getOutRequestNo(), ApplyStatus.SUCC.name(), "");

            //TODO 新增拉取回执单地址定时任务

        } else if (WECHAT_WITHDRAW_FAIL.equalsIgnoreCase(res.getStatus())) {
            if (!selfSubMchIdList.contains(record.getCustomerCode())) {
                //账户中心回退
                asyncAccountRefund(record, res.getReason());
            }
            withdrawApplyRecordMapper.updateWithdrawRecordStatus(res.getOutRequestNo(), ApplyStatus.FAIL.name(), res.getReason());
        } else {
            Logger.warn("WechatWithdraw queryMerchantWechatWithdraw ignore status!" + DataMask.toJSONString(result));
            return Results.error("商户提现查询状态忽略");
        }
        return success();
    }

    /**
     * 账户中心异步退款
     * @param record
     * @param reason
     */
    private void asyncAccountRefund(WithdrawApplyRecord record, String reason) {
        record.setRemark(reason);
        TradeInfo tradeInfo = generateTradeInfo(record, AccountKeyConstants.WCSH.getName(), DetailTypeConstants.TRADE_TYPE_402.getName());
        if (CustomerType.SS_WC.getName().equals(record.getCustomerType())) {
            tradeInfo.setAccountTypeKey(AccountKeyConstants.WCSS.getName());
            tradeInfo.setTradeType(DetailTypeConstants.TRADE_TYPE_482.name());
        }else if (CustomerType.NM.getName().equals(record.getCustomerType())){
            tradeInfo.setAccountTypeKey( AccountKeyConstants.NM.name());
            tradeInfo.setTradeType(DetailTypeConstants.TRADE_TYPE_045.name());
        }else if (CustomerType.NMDL.getName().equals(record.getCustomerType())){
            tradeInfo.setAccountTypeKey( AccountKeyConstants.NMDL.name());
            tradeInfo.setTradeType(DetailTypeConstants.TRADE_TYPE_262.name());
        }
        //渠道提现失败，账户中心资产退回
        PostActionItemBO itemBO = PostActionItemBO.builder()
                .bizId(record.getWithdrawNo()).paramObject(tradeInfo).remark(reason)
                .actionType(PostActionTypes.ACCOUNT_CENTER_COMPENSATE.getName()).status(PostActionExecStatus.EXECUTE.value())
                .nextRetryTime(LocalDateTime.now().plusSeconds(5)).build();
        postActionService.addAction(itemBO);
    }


    @Override
    public Result<Boolean> processingWithdrawExist(String customerCode, String customerType) {
        LambdaQueryWrapper<WithdrawApplyRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WithdrawApplyRecord::getCustomerCode, customerCode)
                .eq(WithdrawApplyRecord::getCustomerType, customerType)
                .gt(WithdrawApplyRecord::getCreateTime, Date.from(LocalDateTime.now().plusSeconds(withdrawCheckTime).atZone(ZoneId.systemDefault()).toInstant()))
                .eq(WithdrawApplyRecord::getApplyStatus, ApplyStatus.DOING.getName())
                .last(" limit 1");
        WithdrawApplyRecord record = withdrawApplyRecordMapper.selectOne(wrapper);
        if (record != null) {
            Logger.info("processingWithdraw is exist, customerCode:{}", customerCode);
            return success(Boolean.TRUE);
        } else {
            return success(Boolean.FALSE);
        }
    }

    @Override
    public Result<String> accountWechatWithdraw(AccountWithdrawVO vo) {
        Logger.info("AccountWithdrawServiceImpl accountWechatWithdraw vo: {}", DataMask.toJSONString(vo));
        Result<String> result;
        //1.必传参数校验
        result = wechatWithdrawParamCheck(vo);
        if (!result.getSuccess()) {
            Logger.info("AccountWithdrawServiceImpl wechatWithdrawParamCheck fail customerCode: {}", vo.getCustomerCode());
            return result;
        }
        vo.setWithdrawNo(AccountNoUtils.generateWithdrawNo());
        if (CustomerType.NM.getName().equals(vo.getCustomerType())) {
            result = shopkeeperWechatWithdraw(vo);
        } else if (CustomerType.NMDL.getName().equals(vo.getCustomerType())) {
            if (decouplingConfig.decouplingSwitch(vo.getCustomerCode(), vo.getCustomerType())){
                result = decouplingShopAgentWechatWithdraw(vo);
            }else {
                result = originShopAgentWechatWithdraw(vo);
            }
        } else {
            result = Results.error(ResponseEnum.ACCORE_101527);
        }
        return result;
    }

    @Override
    public Result<String> addWithdrawRecord(AccountWithdrawVO vo) {
        Logger.info("AccountWithdrawServiceImpl addWithdrawRecord vo: {}", DataMask.toJSONString(vo));
        //幂等拦截校验
        if (StringUtils.isNotBlank(vo.getWithdrawNo())) {
            WithdrawApplyRecord record = withdrawApplyRecordMapper.selectOne(new LambdaQueryWrapper<WithdrawApplyRecord>().eq(WithdrawApplyRecord::getWithdrawNo, vo.getWithdrawNo()));
            if (record != null) {
                return Result.success(vo.getWithdrawNo());
            }
        } else {
            vo.setWithdrawNo(AccountNoUtils.generateWithdrawNo());
        }

        //必传参数校验
        Result<String> result = withdrawParamCheck(vo);
        if (!result.getSuccess()) {
            Logger.info("AccountWithdrawServiceImpl withdrawParamCheck fail customerCode: {}", vo.getCustomerCode());
            return result;
        }

        //获取当前提现月份
        String month = com.aikucun.common2.utils.DateUtils.format(new Date(), FORMAT_MONTH);

        WithdrawApplyRecord record = new WithdrawApplyRecord();
        BeanUtils.copyProperties(vo, record);
        record.setWithdrawChannel(WithdrawChannelConstants.PINGAN.getName());
        if (StringUtils.isNotBlank(vo.getApplyStatus())) {
            record.setApplyStatus(vo.getApplyStatus());
        } else {
            record.setApplyStatus(ApplyStatus.DOING.name());
        }
        //如果未传入服务费即给默认值
        if (Objects.isNull(record.getServiceAmount())) {
            record.setServiceAmount(BigDecimal.ZERO);
        }
        Result<String> res = encrypt(record.getBankNo());
        if (!res.getSuccess()) {
            return Results.error(res.getCode(), res.getMessage());
        }
        record.setBankNo(res.getData());
        record.setCustomerGrade(vo.getCustomerGrade());
        record.setRemark(vo.getRemark());
        //设置提现月份
        record.setMonth(month);

        //4.插入提现申请记录表
        int insertNum = saveWithdrawRecord(record);
        if (insertNum != 1) {
            return Results.error(CommonConstants.GENERAL_CODE, "提现申请记录落库失败");
        }

        // 成功后，初始化回执单下载任务, 新增拉取回执单地址定时任务
        if (ApplyStatus.SUCC.name().equals(record.getApplyStatus())) {
            SpringContextHolder.getBean(WithdrawReceiptDownloadTask.class).init(
                    WithdrawChannelConstants.PINGAN.getName(), record.getWithdrawNo(),
                            null, null, false, record.getCustomerType());
        }

        return Result.success(vo.getWithdrawNo());
    }

    private Result<String> originShopAgentWechatWithdraw(AccountWithdrawVO vo) {
        Result<String> result = success(vo.getWithdrawNo());
        WithdrawApplyRecord record = new WithdrawApplyRecord();
        BeanUtils.copyProperties(vo, record);
        record.setWithdrawChannel(WithdrawChannelConstants.WECHAT.getName());
        record.setApplyStatus(ApplyStatus.DOING.name());
        record.setBatchNo(AccountNoUtils.generateWithdrawBatchNo());
        if (StringUtils.isBlank(record.getShopId())) {
            return Results.error(CommonConstants.GENERAL_CODE, "店长提现shopId不能为空");
        }
        //1、判断是否达到提现扣税金额限制
        WithdrawThresholdCheckBO withdrawThreshold = withdrawTaxService.checkWithdrawThreshold(record);
        if (withdrawThreshold.getSuccess() && !withdrawThreshold.getWechatWithdrawFlag()) {
            return error(ResponseEnum.ACCORE_101528.getCode(), withdrawThreshold.getWechatWithdrawTip());
        }
        if (!withdrawThreshold.getSuccess()) {
            return Results.error(withdrawThreshold.getCode(), withdrawThreshold.getTip());
        }
        // 2、税费计算
        WithdrawTaxDetail taxDetail;
        try {
            taxDetail = withdrawTaxService.applyWithdrawTax(record);
        } catch (BusinessException e) {
            Logger.warn("shopAgentWechatWithdraw applyWithdrawTax exception:", e);
            return Results.error(e.getCode(), e.getMessage());
        }
//        if (null != taxDetail) {
//            record.setRemark(taxDetail.getRemark());
//        }
        //3.提现申请记录入库
        int insertNum = saveWithdrawRecord(record);
        if (insertNum != 1) {
            return Results.error(CommonConstants.GENERAL_CODE, "提现申请记录落库失败");
        }
        //4、提现扣税
        Result<Void> taxResult = withdrawTaxService.withdrawTax(taxDetail);
        if (!taxResult.getSuccess()) {
            result.setSuccess(Boolean.FALSE);
            throw new AccountProxyException("店长提现操作频繁，请稍后再试");
        }
        //5、微信提现累计提现金额
        Result<Void> withdrawSum = withdrawTaxService.wechatWithdrawSumAmount(record, taxDetail);
        if (!withdrawSum.getSuccess()) {
            return Results.error(CommonConstants.GENERAL_CODE, withdrawSum.getMessage());
        }
        //6、发起提现
        AgentWhiteListAccount whiteAccount = agentWhiteListAccountService.getOne(new LambdaQueryWrapper<AgentWhiteListAccount>().eq(AgentWhiteListAccount::getShopId, record.getShopId()).eq(AgentWhiteListAccount::getStatus, 1));
        if (whiteAccount != null) {
            //白名单店长提现
            result = agentWhiteListWechatWithdraw(record, whiteAccount, vo, taxDetail);
        } else {
            //普通店长提现
            result = agentWechatWithdraw(record, vo, taxDetail);
        }
        if (!result.getSuccess()) {
            wechatWithdrawFailProcess(record, taxDetail, result);
        }
        return result;
    }

    /**
     * 店主提现到微信
     *
     * @param vo
     * @return
     */
    private Result<String> shopkeeperWechatWithdraw(AccountWithdrawVO vo) {
        Result<String> result = success(vo.getWithdrawNo());
        WithdrawApplyRecord record = new WithdrawApplyRecord();
        BeanUtils.copyProperties(vo, record);
        record.setWithdrawChannel(WithdrawChannelConstants.WECHAT.getName());
        record.setApplyStatus(ApplyStatus.DOING.name());
        record.setBatchNo(AccountNoUtils.generateWithdrawBatchNo());
        record.setExt(vo.getExt());
        //1、判断是否达到提现扣税金额限制
        WithdrawThresholdCheckBO withdrawThreshold = withdrawTaxService.checkWithdrawThreshold(record);
        if (withdrawThreshold.getSuccess() && !withdrawThreshold.getWechatWithdrawFlag()) {
            return Results.error(ResponseEnum.ACCORE_101528.getCode(), withdrawThreshold.getWechatWithdrawTip());
        }
        if (!withdrawThreshold.getSuccess()) {
            return Results.error(withdrawThreshold.getCode(), withdrawThreshold.getTip());
        }
        //2、税费计算
        WithdrawTaxDetail taxDetail;
        try {
            taxDetail = withdrawTaxService.applyWithdrawTax(record);
        } catch (BusinessException e) {
            return Results.error(e.getCode(), e.getMessage());
        }
//        if (null != taxDetail) {
//            record.setRemark(taxDetail.getRemark());
//        }
        //3、提现记录入库
        int insertNum = saveWithdrawRecord(record);
        if (insertNum != 1) {
            return Results.error(CommonConstants.GENERAL_CODE, "提现申请记录落库失败");
        }
        //4、提现扣税入库
        Result<Void> taxRes = withdrawTaxService.withdrawTax(taxDetail);
        if (!taxRes.getSuccess()) {
            return Results.error(CommonConstants.GENERAL_CODE, taxRes.getMessage());
        }
        //5、微信提现累计提现金额
        Result<Void> withdrawSum = withdrawTaxService.wechatWithdrawSumAmount(record, taxDetail);
        if (!withdrawSum.getSuccess()) {
            return Results.error(CommonConstants.GENERAL_CODE, withdrawSum.getMessage());
        }
        //6.账户中心余额扣减
        TradeInfo tradeInfo = generateTradeInfo(record, AccountKeyConstants.NM.name(), DetailTypeConstants.TRADE_TYPE_465.name());
        com.akucun.common.Result<Void> tradeResult = accountCenterService.dealTrade(tradeInfo);
        if (!tradeResult.isSuccess()) {
            Logger.error("shopkeeperWechatWithdraw dealTrade tradeResult: {}", DataMask.toJSONString(tradeResult));
            wechatWithdrawFailProcess(record, taxDetail, result);
            return Results.error(CommonConstants.GENERAL_CODE, tradeResult.getMessage());
        }
        //7.平安账户提现金额转移到公户
        if (decouplingConfig.antiClearingSwitch(vo.getCustomerCode(), vo.getCustomerType())) {
            DealTradeVO dealTrade = new DealTradeVO();
            dealTrade.setRemark("资金归集");
            dealTrade.setTranAmount(record.getAmount());
            dealTrade.setCustomerCode(record.getCustomerCode());
            dealTrade.setCustomerType(record.getCustomerType());
            dealTrade.setOrderNo(record.getWithdrawNo());
            com.akucun.fps.common.entity.Result<Void> dealResult = memberServiceApi.dealTrade(dealTrade);
            if (!dealResult.isSuccess()) {
                Logger.error("shopkeeperWechatWithdraw memberServiceApi.dealTrade result: {}", dealResult.getErrorMessage());
                accountCenterWechatRefund(tradeInfo, dealResult.getErrorMessage());
                wechatWithdrawFailProcess(record, taxDetail, result);
                return Results.error(CommonConstants.GENERAL_CODE, "提现到微信失败");
            }
        }
        //8.提现到微信余额
        asyncWechatWithdraw(record, vo, taxDetail);
        return result;
    }

    private void asyncWechatWithdraw(WithdrawApplyRecord record, AccountWithdrawVO vo, WithdrawTaxDetail detail) {
        PaymentTransferReq req = new PaymentTransferReq();
        req.setSourceCode(Constant.XD_WECHAT);
        req.setChannelCode(vo.getChannelCode());
        req.setSourceNo(record.getWithdrawNo());
        req.setAmount(detail != null ? detail.getWithdraw() : record.getAmount());
        req.setOpenId(vo.getOpenId());
        req.setCustomerCode(record.getCustomerCode());
        req.setCustomerType(record.getCustomerType());
        req.setCustomerName(record.getApplyUser());
        req.setTenantId(vo.getTenantId());
        req.setTransferType(TransferTypeEnum.WECHAT);
        req.setRemark("提现：" + record.getWithdrawNo());
        req.setBatchNo(record.getBatchNo());
        req.setCheckName(true);

        PostActionItemBO itemBO = PostActionItemBO.builder()
                .bizId(req.getSourceNo())
                .paramObject(req)
                .remark("提现到微信余额")
                .actionType(PostActionTypes.WECHAT_DELAY_WITHDRAW.getName())
                .status(PostActionExecStatus.EXECUTE.value()).build();
        postActionService.addAction(itemBO);
    }

    /**
     * 提现到微信失败账户中心退款
     *
     * @param tradeInfo
     * @param message
     */
    private void accountCenterWechatRefund(TradeInfo tradeInfo, String message) {
        if (AccountKeyConstants.NM.getName().equals(tradeInfo.getAccountTypeKey())) {
            tradeInfo.setTradeType(DetailTypeConstants.TRADE_TYPE_466.name());
        } else if (AccountKeyConstants.NMDL.getName().equals(tradeInfo.getAccountTypeKey())) {
            tradeInfo.setTradeType(DetailTypeConstants.TRADE_TYPE_273.name());
        }
        tradeInfo.setRemark(message);
        com.akucun.common.Result<Void> refundResult = accountCenterService.dealTrade(tradeInfo);
        if (!refundResult.isSuccess()) {
            Logger.warn("accountCenterWechatRefund 提现到微信账户中心退款失败 tradeInfo:{}, result:{}", tradeInfo, DataMask.toJSONString(refundResult));
        }
    }

    /**
     * 店长提现到微信
     *
     * @param vo
     * @return
     */
    private Result<String> decouplingShopAgentWechatWithdraw(AccountWithdrawVO vo) {
        Result<String> result;
        WithdrawApplyRecord record = new WithdrawApplyRecord();
        BeanUtils.copyProperties(vo, record);
        record.setBatchNo(AccountNoUtils.generateWithdrawBatchNo());
        record.setWithdrawChannel(WithdrawChannelConstants.WECHAT.getName());
        record.setApplyStatus(ApplyStatus.DOING.name());
        if (checkShopAgentWhitelist(record)){
            return Results.error(CommonConstants.GENERAL_CODE, "店长提现异常，请联系店主");
        }
        //1、判断是否达到提现扣税金额限制
        WithdrawThresholdCheckBO withdrawThreshold = withdrawTaxService.checkWithdrawThreshold(record);
        if (withdrawThreshold.getSuccess() && !withdrawThreshold.getWechatWithdrawFlag()) {
            return error(ResponseEnum.ACCORE_101528.getCode(), withdrawThreshold.getWechatWithdrawTip());
        }
        if (!withdrawThreshold.getSuccess()) {
            return Results.error(withdrawThreshold.getCode(), withdrawThreshold.getTip());
        }
        // 2、税费计算
        WithdrawTaxDetail taxDetail;
        try {
            taxDetail = withdrawTaxService.applyWithdrawTax(record);
        } catch (BusinessException e) {
            Logger.warn("shopAgentWechatWithdraw applyWithdrawTax exception:", e);
            return Results.error(e.getCode(), e.getMessage());
        }
//        if (null != taxDetail) {
//            record.setRemark(taxDetail.getRemark());
//        }
        //3.提现申请记录入库
        int insertNum = saveWithdrawRecord(record);
        if (insertNum != 1) {
            return Results.error(CommonConstants.GENERAL_CODE, "提现申请记录落库失败");
        }
        //4、提现扣税
        Result<Void> taxResult = withdrawTaxService.withdrawTax(taxDetail);
        if (!taxResult.getSuccess()) {
            return Results.error(CommonConstants.GENERAL_CODE, taxResult.getMessage());
        }
        //5、微信提现累计提现金额
        Result<Void> withdrawSum = withdrawTaxService.wechatWithdrawSumAmount(record, taxDetail);
        if (!withdrawSum.getSuccess()) {
            return Results.error(CommonConstants.GENERAL_CODE, withdrawSum.getMessage());
        }
        //普通店长提现
        result = agentWechatWithdraw(record, vo, taxDetail);
        if (!result.getSuccess()) {
            wechatWithdrawFailProcess(record, taxDetail, result);
        }
        return result;
    }

    /**
     * 微信提现失败处理
     *
     * @param record
     * @param taxDetail
     * @param result
     */
    private void wechatWithdrawFailProcess(WithdrawApplyRecord record, WithdrawTaxDetail taxDetail, Result result) {
        //状态更新为失败
        withdrawApplyRecordMapper.updateWithdrawRecordStatus(record.getWithdrawNo(), ApplyStatus.FAIL.name(), result.getMessage());
        //扣税退回
        withdrawTaxService.withdrawFailForTax(record, result.getMessage());
        //微信累计提现金额退回
        withdrawTaxService.wechatWithdrawFailSumAmount(record, taxDetail);
    }

    /**
     * 店长白名单提现到微信
     *
     * @param record
     * @param whiteAccount
     * @param vo
     * @param taxDetail
     * @return
     */
    private Result<String> agentWhiteListWechatWithdraw(WithdrawApplyRecord record, AgentWhiteListAccount whiteAccount, AccountWithdrawVO vo, WithdrawTaxDetail taxDetail) {
        Result<String> result = success(record.getWithdrawNo());
        //1.构建账户中心请求参数
        TradeInfo tradeInfo = generateTradeInfo(record, AccountKeyConstants.NMDL.getName(), DetailTypeConstants.TRADE_TYPE_272.name());
        tradeInfo.setAccountTypeKey(AccountKeyConstants.NM.getName());
        tradeInfo.setCustomerCode(whiteAccount.getCustomerCode());
        tradeInfo.setTradeType(DetailTypeConstants.TRADE_TYPE_465.name());
        //2.账户中心店主提现
        com.akucun.common.Result<Void> shopkeeperTradeResult = accountCenterService.dealTrade(tradeInfo);
        if (!shopkeeperTradeResult.isSuccess()) {
            Logger.warn("agentWhiteListWechatWithdraw 白名单店主账户中心提现失败：{}", DataMask.toJSONString(shopkeeperTradeResult));
            return Results.error(CommonConstants.GENERAL_CODE, shopkeeperTradeResult.getMessage());
        }
        TradeInfo tradeNew = new TradeInfo();
        BeanUtils.copyProperties(tradeInfo, tradeNew);
        tradeNew.setCustomerCode(record.getCustomerCode());
        tradeNew.setTradeType(DetailTypeConstants.TRADE_TYPE_272.name());
        tradeNew.setAccountTypeKey(AccountKeyConstants.NMDL.getName());
        boolean antiClearingSwitch = decouplingConfig.antiClearingSwitch(vo.getCustomerCode(), vo.getCustomerType());
        if (antiClearingSwitch) {
            //3.白名单店主账户提现金额归集到公户
            DealTradeVO dealTrade = new DealTradeVO();
            dealTrade.setRemark("白名单店长提现");
            dealTrade.setTranAmount(record.getAmount());
            dealTrade.setCustomerCode(whiteAccount.getCustomerCode());
            dealTrade.setCustomerType(CustomerType.NM.name());
            dealTrade.setOrderNo(record.getWithdrawNo());
            com.akucun.fps.common.entity.Result<Void> dealResult = memberServiceApi.dealTrade(dealTrade);
            if (!dealResult.isSuccess()) {
                Logger.error("agentWhiteListWechatWithdraw withdraw error: {}", dealResult.getErrorMessage());
                accountCenterWechatRefund(tradeInfo, dealResult.getErrorMessage());
                return Results.error(CommonConstants.GENERAL_CODE, "提现失败，请联系店主");
            }
        }
        //4.账户中心店长提现
        com.akucun.common.Result<Void> tradeResult = accountCenterService.dealTrade(tradeNew);
        if (!tradeResult.isSuccess()) {
            Logger.info("agentWhiteListWechatWithdraw 白名单店长账户中心提现失败：{}", DataMask.toJSONString(tradeResult));
            accountCenterWechatRefund(tradeInfo, tradeResult.getMessage());
            if (antiClearingSwitch) {
                agentWhiteWechatRollback(record, whiteAccount);
            }
            return Results.error(CommonConstants.GENERAL_CODE, "提现失败，请联系店主或稍后重试");
        }
        //5.提现到微信余额
        asyncWechatWithdraw(record, vo, taxDetail);
        return result;
    }

    private void agentWhiteWechatRollback(WithdrawApplyRecord record, AgentWhiteListAccount whiteAccount) {
        //白名单店主退款任务
        DealTradeVO dealTradeRefundVO = new DealTradeVO();
        dealTradeRefundVO.setRemark("白名单店长提现退款");
        dealTradeRefundVO.setTranAmount(record.getAmount());
        dealTradeRefundVO.setCustomerCode(whiteAccount.getCustomerCode());
        dealTradeRefundVO.setCustomerType(CustomerType.NM.name());
        dealTradeRefundVO.setOrderNo(record.getWithdrawNo());
        dealTradeRefundVO.setUniqueKey(record.getWithdrawNo());
        //退款异步任务
        PostActionItemBO refund = PostActionItemBO.builder()
                .bizId("NMDL" + record.getWithdrawNo()).paramObject(dealTradeRefundVO).remark("白名单店长退款任务")
                .actionType(PostActionTypes.PINGAN_REFUND.getName()).status(PostActionExecStatus.EXECUTE.value())
                .nextRetryTime(LocalDateTime.now().plusSeconds(15)).build();
        postActionService.addAction(refund);
    }

    /**
     * 普通店长提现微信
     *
     * @param record
     * @param vo
     * @param taxDetail
     * @return
     */
    private Result<String> agentWechatWithdraw(WithdrawApplyRecord record, AccountWithdrawVO vo, WithdrawTaxDetail taxDetail) {
        Result<String> result = success(vo.getWithdrawNo());
        //构建账户中心请求参数
        TradeInfo tradeInfo = generateTradeInfo(record, AccountKeyConstants.NMDL.getName(), DetailTypeConstants.TRADE_TYPE_272.name());
        //1.账户中心提现
        com.akucun.common.Result<Void> tradeResult = accountCenterService.dealTrade(tradeInfo);
        if (!tradeResult.isSuccess()) {
            return Results.error(tradeResult.getCode(), tradeResult.getMessage());
        }
        if (decouplingConfig.antiClearingSwitch(vo.getCustomerCode(), vo.getCustomerType())) {
            //2.平安账户提现金额转移到公户
            DealTradeVO dealTrade = new DealTradeVO();
            dealTrade.setRemark("资金归集");
            dealTrade.setTranAmount(record.getAmount());
            dealTrade.setCustomerCode(record.getCustomerCode());
            dealTrade.setCustomerType(record.getCustomerType());
            dealTrade.setOrderNo(record.getWithdrawNo());
            com.akucun.fps.common.entity.Result<Void> dealResult = memberServiceApi.dealTrade(dealTrade);
            if (!dealResult.isSuccess()) {
                Logger.error("shopkeeperWechatWithdraw memberServiceApi.dealTrade result: {}", DataMask.toJSONString(dealResult));
                accountCenterWechatRefund(tradeInfo, dealResult.getErrorMessage());
                return Results.error(CommonConstants.GENERAL_CODE, "提现到微信失败");
            }
        }
        //3.提现到微信余额
        asyncWechatWithdraw(record, vo, taxDetail);
        return result;
    }

    /**
     * 提现到微信余额参数校验(只有饷店小程序和微信H5端支持)
     *
     * @param vo
     * @return
     */
    private Result<String> wechatWithdrawParamCheck(AccountWithdrawVO vo) {
        if (vo == null || vo.getAmount() == null || StringUtils.isEmpty(vo.getCustomerCode())
                || StringUtils.isEmpty(vo.getCustomerType()) || StringUtils.isEmpty(vo.getChannelCode())) {
            return Results.error(ResponseEnum.ACCORE_101500);
        }
        if (CustomerType.NMDL.getName().equals(vo.getCustomerType()) && StringUtils.isBlank(vo.getShopId())) {
            return Results.error(ResponseEnum.ACCORE_101513);
        }
        //提现金额不可为0或负数
        if (BigDecimal.ZERO.compareTo(vo.getAmount()) >= 0) {
            return Results.error(ResponseEnum.ACCORE_101509);
        }
        //提现限额校验
        if(wechatWithdrawLimit.compareTo(BigDecimal.ZERO) > 0){
            if(vo.getAmount().compareTo(wechatWithdrawLimit) > 0){
                return Results.error(ResponseEnum.ACCORE_101521.getCode(), wechatWithdrawLimitTips.replace("__", wechatWithdrawLimit.toString()));
            }
        }
        if (StringUtils.isEmpty(vo.getOpenId()) || StringUtils.isEmpty(vo.getTenantId())) {
            return Results.error(ResponseEnum.ACCORE_101520);
        }

        return Results.success();
    }
    
    private Result decouplingTenantWithdraw(WithdrawApplyRecord record,String bankNo) throws IOException {
    	PostActionItemBO itemBO = PostActionItemBO.builder()
                .bizId(record.getWithdrawNo())
                .paramObject(FinTaskAcceptRequestConvert.tenantWithdrawFinTaskAcceptRequest(record,bankNo))
                .remark(CustomerNatureType.DXQDS.getName().equals(record.getCustomerType()) ? "代销渠道商提现异步请求" : "租户店主店长提现异步请求")
                .actionType(PostActionTypes.PINGAN_WITHDRAW.getName())
                .status(PostActionExecStatus.EXECUTE.value())
                .nextRetryTime(LocalDateTime.now().plusSeconds(1))
                .retryNums(0)
                .build();
        postActionService.addAction(itemBO);
        return success(record.getWithdrawNo());
    }
    
    private Result originTenantWithdraw(WithdrawApplyRecord record,String bankNo) {
    	 //账户中心提现参数组装
         TradeInfo tradeInfo = generateTradeInfo(record, AccountKeyConstants.OP.getName(), DetailTypeConstants.TRADE_TYPE_133.name());
         if (CustomerType.DCC.name().equals(record.getCustomerType())) {
             tradeInfo.setAccountTypeKey(AccountKeyConstants.DCC.name());
             tradeInfo.setTradeType(DetailTypeConstants.TRADE_TYPE_361.name());
         } else if (CustomerType.AT.getName().equals(record.getCustomerType())) {
             tradeInfo.setAccountTypeKey(AccountKeyConstants.AT.name());
             tradeInfo.setTradeType(DetailTypeConstants.TRADE_TYPE_421.name());
         }
         com.akucun.common.Result<Void> tradeResult = accountCenterService.dealTrade(tradeInfo);
         if (!tradeResult.isSuccess()) {
             withdrawApplyRecordMapper.updateWithdrawRecordStatus(record.getWithdrawNo(), ApplyStatus.FAIL.name(), tradeResult.getMessage());
             return Results.error(CommonConstants.GENERAL_CODE, tradeResult.getMessage());
         }
         //4.平安账户异步提现
         WithdrawVO withdrawVO = generateWithdrawVO(record, bankNo);
         asyncWithdraw(withdrawVO);
         return success(record.getWithdrawNo());
    }

    @Override
    public Result<String> getWithdrawReceiptDownloadUrl(String withdrawNo) {
        LambdaQueryWrapper<WithdrawApplyRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WithdrawApplyRecord::getWithdrawNo, withdrawNo);
        WithdrawApplyRecord record = withdrawApplyRecordMapper.selectOne(queryWrapper);
        String withdrawReceiptPrivateUrl = null;
        String channel = null;
        if (record == null) {
            LambdaQueryWrapper<TenantWithdrawApply> tenantQueryWrapper = new LambdaQueryWrapper<>();
            tenantQueryWrapper.eq(TenantWithdrawApply::getWithdrawNo, withdrawNo);
            TenantWithdrawApply tenantRecord = tenantWithdrawApplyMapper.selectOne(tenantQueryWrapper);
            if (tenantRecord == null) {
                return Results.error(CommonConstants.GENERAL_CODE, "提现记录不存在");
            }
            withdrawReceiptPrivateUrl = tenantRecord.getReceiptUrl();
            channel = tenantRecord.getWithdrawChannel();
        } else {
            withdrawReceiptPrivateUrl = record.getReceiptUrl();
            channel = record.getWithdrawChannel();
        }
        
        if (StringUtils.isBlank(withdrawReceiptPrivateUrl)) {
            return Results.error(CommonConstants.GENERAL_CODE, "提现回单地址不存在");
        }

        String parentDirectory = null;
        if (StringUtils.equals(channel, WithdrawChannelConstants.WECHAT.getName())) {
            parentDirectory = FileUploadClient.WECHAT_WITHDRAW_RECEIPT_PARENT_DIR;
        } else if (StringUtils.equals(channel, WithdrawChannelConstants.PINGAN.getName())) {
            parentDirectory = FileUploadClient.WITHDRAW_RECEIPT_PARENT_DIR;
        } else {
            return Results.error(CommonConstants.GENERAL_CODE, "提现渠道不支持");
        }
        Result<FileInfo> tmpFileResult = fileUploadClient.getTmpSign(withdrawReceiptPrivateUrl, null, parentDirectory);
        if (!tmpFileResult.getSuccess() || tmpFileResult.getData() == null) {
            return Results.error(CommonConstants.GENERAL_CODE, "获取提现回单临时路径失败");
        }
        return Result.success(tmpFileResult.getData().getFileUrl());
    }

}
