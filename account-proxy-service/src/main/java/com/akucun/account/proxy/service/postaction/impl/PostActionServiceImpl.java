package com.akucun.account.proxy.service.postaction.impl;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.common.enums.PostActionBizStatus;
import com.akucun.account.proxy.common.enums.PostActionExecStatus;
import com.akucun.account.proxy.common.enums.ResponseEnum;
import com.akucun.account.proxy.common.utils.WechatNotifyTool;
import com.akucun.account.proxy.dao.mapper.PostActionItemMapper;
import com.akucun.account.proxy.dao.model.PostActionItem;
import com.akucun.account.proxy.service.postaction.PostActionService;
import com.akucun.account.proxy.service.postaction.bo.PostActionItemBO;
import com.akucun.account.proxy.service.postaction.common.AbsPostActionExecutor;
import com.akucun.account.proxy.service.postaction.common.PostActionProxy;
import com.akucun.fps.common.util.GsonUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mengxiang.base.common.log.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: silei
 * @Date: 2020/12/8
 * @desc: 异步任务service
 */
@Service
public class PostActionServiceImpl extends ServiceImpl<PostActionItemMapper, PostActionItem> implements PostActionService {

    @Autowired
    private PostActionProxy postActionProxy;

    @Autowired
    private WechatNotifyTool wechatNotifyTool;

    @Override
    public void addAction(PostActionItemBO itemBO) {
        PostActionItem item = new PostActionItem();
        item.setBizId(itemBO.getBizId());
        item.setActionType(itemBO.getActionType());
        item.setBizStatus(PostActionBizStatus.DEFAULT.value());
        item.setParam(GsonUtils.getInstance().toJson(itemBO.getParamObject()));
        item.setNextRetryTime(itemBO.getNextRetryTime() != null ? itemBO.getNextRetryTime() : LocalDateTime.now());
        if (itemBO.getId() != null) {
            item.setId(itemBO.getId());
        }
        item.setRetryNums(itemBO.getRetryNums());
        item.setErrorLog(itemBO.getErrorLog());
        item.setStatus(itemBO.getStatus());
        item.setRemark(itemBO.getRemark());

        this.baseMapper.insert(item);
    }

    @Override
    public List<PostActionItem> selectPage(String createTime, int batchNum, int total, int index, String actionType, int maxRetryTimes) {
        String lastSql = " limit " + batchNum;
        LambdaQueryWrapper<PostActionItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.ge(PostActionItem::getCreateTime, createTime)
                .in(PostActionItem::getBizStatus, PostActionBizStatus.DEFAULT.value(), PostActionBizStatus.RETRY.value())
                .eq(PostActionItem::getStatus, PostActionExecStatus.EXECUTE.value())
                .eq(PostActionItem::getActionType, actionType)
                .lt(PostActionItem::getRetryNums, maxRetryTimes)
                .apply("mod(id,{0}) = {1}", total, index)
                .last(lastSql);

        return this.baseMapper.selectList(wrapper);
    }

    @Override
    public void processAction(PostActionItem item) {
        //下次执行时间大于当前时间，跳过执行，等待下次执行
        if (item.getNextRetryTime() != null && LocalDateTime.now().compareTo(item.getNextRetryTime()) < 0) {
            return;
        }
        try {
            Logger.info("processAction item:{}", item);
            //1.获取执行类
            AbsPostActionExecutor executor = postActionProxy.getExecutor(item.getActionType());
            //2.执行任务
            Result<Void> result = executor.execute(item);
            //3.更新任务执行结果
            if (result != null && result.getSuccess()) {
                item.setBizStatus(PostActionBizStatus.SUCCESS.value());
            } else if (result != null && !result.getSuccess() && result.getCode().equals(ResponseEnum.ALLOCATE_COMMISSION_PROCESSING.getCode())) {
                Logger.info("processAction redis is locking, bizId:{}", item.getBizId());
                //命中分布式锁，分佣处理中,跳过此次操作；
                return;
            } else {
                item.setBizStatus(PostActionBizStatus.RETRY.value());
                item.setRetryNums(item.getRetryNums() + 1);
                //设置下次重试时间
                LocalDateTime localDateTime = executor.calculateNextExecuteTime(item);
                item.setNextRetryTime(localDateTime);
                if (item.getRetryNums() >= executor.getMaxRetryTimes()) {
                    wechatNotifyTool.sendNotifyMsg("account-proxy延迟任务执行达到最大重试次数，actionType:" + item.getActionType() + ", bizId:" + item.getBizId());
                    Logger.warn("processAction exceed max retry times! actionType:{},bizId:{}", item.getActionType(), item.getBizId());
                }
            }
            this.baseMapper.updateById(item);
        } catch (Exception e) {
            Logger.warn("PostActionServiceImpl.processAction  actionType:{} exception:", item.getActionType(), e);
        }

    }

    @Override
    public List<PostActionItem> selectByIndex(long start, long end, int retryTimes, String actionType) {
        LambdaQueryWrapper<PostActionItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.ge(PostActionItem::getId, start)
                .lt(PostActionItem::getId, end)
                .eq(PostActionItem::getActionType, actionType)
                .in(PostActionItem::getBizStatus, PostActionBizStatus.DEFAULT.value(), PostActionBizStatus.RETRY.value())
                .eq(PostActionItem::getStatus, PostActionExecStatus.EXECUTE.value())
                .lt(PostActionItem::getRetryNums, retryTimes);


        return this.baseMapper.selectList(wrapper);
    }

    @Override
    public void clearHistoryPostAction(String clearDate) {
        LambdaQueryWrapper<PostActionItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.lt(PostActionItem::getCreateTime, clearDate);
        this.baseMapper.delete(wrapper);
    }

    @Override
    public void clearHistorySuccessTask(String clearDate) {
        LambdaQueryWrapper<PostActionItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.lt(PostActionItem::getCreateTime, clearDate)
                .eq(PostActionItem::getBizStatus,PostActionBizStatus.SUCCESS.value());
        this.baseMapper.delete(wrapper);
    }

    @Override
    public void updateParamById(Long id, String param) {
        UpdateWrapper<PostActionItem> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", id).set("param", param);
        this.baseMapper.update(null, updateWrapper);
    }

    @Override
    public List<PostActionItem> selectPageByNextRetryTime(String createTime, int batchNum, int total, int index, String actionType, int maxRetryTimes, String nextRetryTime) {
        String lastSql = " limit " + batchNum;
        LambdaQueryWrapper<PostActionItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.ge(PostActionItem::getCreateTime, createTime)
                .in(PostActionItem::getBizStatus, PostActionBizStatus.DEFAULT.value(), PostActionBizStatus.RETRY.value())
                .eq(PostActionItem::getStatus, PostActionExecStatus.EXECUTE.value())
                .eq(PostActionItem::getActionType, actionType)
                .lt(PostActionItem::getRetryNums, maxRetryTimes)
                .lt(PostActionItem::getNextRetryTime, nextRetryTime)
                .apply("mod(id,{0}) = {1}", total, index)
                .last(lastSql)
                .orderByAsc(PostActionItem::getNextRetryTime);

        return this.baseMapper.selectList(wrapper);
    }
}
