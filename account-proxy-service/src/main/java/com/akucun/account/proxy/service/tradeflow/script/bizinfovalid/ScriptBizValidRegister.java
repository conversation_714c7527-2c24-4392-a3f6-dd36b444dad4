package com.akucun.account.proxy.service.tradeflow.script.bizinfovalid;

import com.akucun.account.proxy.facade.stub.enums.IdentifyEnum;
import com.akucun.account.proxy.service.tradeflow.constants.ErrorCodes;
import com.akucun.account.proxy.service.tradeflow.dto.ErrorDTO;
import com.akucun.account.proxy.service.tradeflow.dto.bizinfo.BizInfoRegister;
import com.akucun.account.proxy.service.tradeflow.exception.TradeValidException;
import com.akucun.account.proxy.service.tradeflow.script.IBizInfoValidScript;
import org.apache.commons.lang3.StringUtils;

public class ScriptBizValidRegister implements IBizInfoValidScript {

    @Override
    public void valid(Object bizInfo) throws TradeValidException {
        BizInfoRegister register = (BizInfoRegister) bizInfo;
        if(StringUtils.isBlank(register.getCustomerType())) {
            throw new TradeValidException(new ErrorDTO(ErrorCodes.PARAM_ERROR, "bizInfo.customerType为空"));
        }
        if(StringUtils.isBlank(register.getCustomerCode())) {
            throw new TradeValidException(new ErrorDTO(ErrorCodes.PARAM_ERROR, "bizInfo.customerCode为空"));
        }
        if(StringUtils.isBlank(register.getAccountType())) {
            throw new TradeValidException(new ErrorDTO(ErrorCodes.PARAM_ERROR, "bizInfo.accountType为空"));
        }
        if(StringUtils.isBlank(register.getIdType())) {
            throw new TradeValidException(new ErrorDTO(ErrorCodes.PARAM_ERROR, "bizInfo.idType为空"));
        }
        if(!IdentifyEnum.IDCARD.name().equals(register.getIdType()) && !IdentifyEnum.CREDIT_CODE.name().equals(register.getIdType())
            && !IdentifyEnum.HONGKONG_MACAO_TAI_TRAVEL_PERMIT.name().equals(register.getIdType())
            && !IdentifyEnum.CHINESE_PASSPORT.name().equals(register.getIdType()) && !IdentifyEnum.FOREIGN_PASSPORT.name().equals(register.getIdType())) {
            throw new TradeValidException(new ErrorDTO(ErrorCodes.PARAM_ERROR, "bizInfo.idType错误"));
        }
        if(StringUtils.isBlank(register.getIdCode())) {
            throw new TradeValidException(new ErrorDTO(ErrorCodes.PARAM_ERROR, "bizInfo.idCode为空"));
        }
        if(StringUtils.isBlank(register.getCustomerName())) {
            throw new TradeValidException(new ErrorDTO(ErrorCodes.PARAM_ERROR, "bizInfo.customerName为空"));
        }
        if("NM".equals(register.getCustomerType()) || "NMDL".equals(register.getCustomerType()) || "AT".equals(register.getCustomerType())) {
            if(StringUtils.isBlank(register.getTenantId())) {
                throw new TradeValidException(new ErrorDTO(ErrorCodes.PARAM_ERROR, "bizInfo.tenantId为空"));
            }
            if(register.getTenantType() == null) {
                throw new TradeValidException(new ErrorDTO(ErrorCodes.PARAM_ERROR, "bizInfo.tenantType为空"));
            }
        }
    }

}
