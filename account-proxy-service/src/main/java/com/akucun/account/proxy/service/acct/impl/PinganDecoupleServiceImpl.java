package com.akucun.account.proxy.service.acct.impl;

import com.aikucun.common2.base.Result;
import com.aikucun.common2.utils.datamasking.DataMask;
import com.akucun.account.center.client.model.TradeInfo;
import com.akucun.account.center.client.model.account.AccountBookDO;
import com.akucun.account.center.client.model.query.AccountQuery;
import com.akucun.account.center.feign.AccountCenterService;
import com.akucun.account.proxy.common.utils.AmountUtils;
import com.akucun.account.proxy.dao.mapper.AccountTenantCustomerMapper;
import com.akucun.account.proxy.dao.mapper.AccountTenantMerchantMapper;
import com.akucun.account.proxy.dao.mapper.PinganDecoupleWhitelistMapper;
import com.akucun.account.proxy.dao.model.AccountTenantCustomer;
import com.akucun.account.proxy.dao.model.PinganDecoupleWhitelist;
import com.akucun.account.proxy.facade.stub.enums.CustomerType;
import com.akucun.account.proxy.facade.stub.enums.DetailTypeConstants;
import com.akucun.account.proxy.facade.stub.others.account.vo.PinganDecoupleAccountVO;
import com.akucun.account.proxy.service.acct.AccountService;
import com.akucun.account.proxy.service.acct.AccountWithdrawService;
import com.akucun.account.proxy.service.acct.PinganDecoupleService;
import com.akucun.account.proxy.service.enums.AccountTenantStatusEnum;
import com.akucun.fps.common.entity.ResultList;
import com.akucun.fps.pingan.client.model.AccountSearchReqDO;
import com.akucun.fps.pingan.client.model.PingAnAccountVO;
import com.akucun.fps.pingan.client.vo.DealTradeVO;
import com.akucun.fps.pingan.feign.api.member.MemberServiceApi;
import com.akucun.fps.pingan.feign.api.settlement.SettlementServiceApi;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mengxiang.base.common.log.Logger;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @Author: silei
 * @Date: 2021/12/20
 * @desc:
 */
@Service
public class PinganDecoupleServiceImpl extends ServiceImpl<PinganDecoupleWhitelistMapper, PinganDecoupleWhitelist> implements PinganDecoupleService {

    @Resource
    private SettlementServiceApi settlementServiceApi;
    @Resource
    private MemberServiceApi memberServiceApi;
    @Autowired
    private AccountWithdrawService accountWithdrawService;
    @Autowired
    private AccountCenterService accountCenterService;
    @Autowired
    private AccountService accountService;

    @Resource
    private AccountTenantCustomerMapper accountTenantCustomerMapper;
    @Resource
    private AccountTenantMerchantMapper accountTenantMerchantMapper;
    /**
     * 平安解耦总开关
     */
    @Value("${pingan.decoupling.switch:false}")
    public boolean decouplingSwitch;
    /**
     * 白名单店长解耦后校验灰度配置开关，true校验，false不校验（直接可提现），
     */
    @Value("${shopAgent.decoupling.check.switch:true}")
    public boolean shopAgentCheckSwitch;
    
    @Value("${pingan.decoupling.tenant.switch:false}")
    public boolean decouplingTenantSwitch;

    @Override
    public Result<Void> saveOrUpdateData(PinganDecoupleAccountVO vo) {

        LambdaQueryWrapper<PinganDecoupleWhitelist> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PinganDecoupleWhitelist::getCustomerCode, vo.getCustomerCode())
                .eq(PinganDecoupleWhitelist::getCustomerType, vo.getCustomerType());
        PinganDecoupleWhitelist whitelist = this.baseMapper.selectOne(wrapper);
        if (whitelist != null) {
            if (vo.getStatus() != null) {
                whitelist.setStatus(vo.getStatus());
            }
            if (vo.getWhitelistStatus() != null) {
                whitelist.setWhitelistStatus(vo.getWhitelistStatus());
            }
            if (vo.getWhitelistShopAgentFlag() != null) {
                whitelist.setWhitelistShopAgentFlag(vo.getWhitelistShopAgentFlag());
            }
            if (vo.getAntiClearingStatus() != null) {
                whitelist.setAntiClearingStatus(vo.getAntiClearingStatus());
            }
            this.baseMapper.update(whitelist, wrapper);
            return Result.success();
        } else {
            whitelist = new PinganDecoupleWhitelist();
            BeanUtils.copyProperties(vo, whitelist);
            this.baseMapper.insert(whitelist);
        }

        return Result.success();
    }

    @Override
    public Boolean checkGrayWhitelist(String customerCode, String customerType) {
        Logger.info("DecouplingConfig checkWhitelist decouplingSwitch:{}, customerCode:{}", decouplingSwitch, customerCode);
        if (decouplingSwitch) {
            return true;
        }
        LambdaQueryWrapper<PinganDecoupleWhitelist> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PinganDecoupleWhitelist::getCustomerCode, customerCode)
                .eq(PinganDecoupleWhitelist::getCustomerType, customerType)
                .eq(PinganDecoupleWhitelist::getStatus, 0);
        PinganDecoupleWhitelist whitelist = this.baseMapper.selectOne(wrapper);
        return whitelist != null;
    }

    @Override
    public void accountAntiClearing(int beginIndex, int endIndex) {
        Logger.info("accountAntiClearing start！");
        //总处理数量
        int processCount = 0;
        int succCount = 0;
        int failCount = 0;
        List<PinganDecoupleWhitelist> list;
        do {
            list = this.baseMapper.queryAntiClearingAccount(beginIndex, endIndex, 500);
            if (CollectionUtils.isEmpty(list)) {
                Logger.warn("accountAntiClearing 账户查询结果为空！");
                break;
            }
            beginIndex = Math.toIntExact(list.get(list.size() - 1).getId());
            for (PinganDecoupleWhitelist account : list) {
                try {
                    processCount++;
                    AccountSearchReqDO req = new AccountSearchReqDO();
                    req.setCustomerCode(account.getCustomerCode());
                    req.setCustomerType(account.getCustomerType());
                    ResultList<PingAnAccountVO> resultList = settlementServiceApi.queryPlatformAccount(req);
                    if (Objects.nonNull(resultList) && resultList.isSuccess()) {
                        Optional<PingAnAccountVO> opt = resultList.getDatalist().stream().findFirst();
                        if (!opt.isPresent()) {
                            failCount++;
                            Logger.warn("accountAntiClearing account not exist in pingan system! customerCode:{}", account.getCustomerCode());
                            continue;
                        }
                        PingAnAccountVO vo = opt.get();
                        boolean result = antiClearingProcess(account.getCustomerCode(), account.getCustomerType(), vo.getTotalBalance(), "");
                        if (result) {
                            updateAntiClearingStatus(account);
                            succCount++;
                        } else {
                            failCount++;
                        }
                    }
                } catch (Exception e) {
                    Logger.warn("accountAntiClearing exception, processCount:{}", processCount);
                }
                if (processCount % 500 == 0) {
                    Logger.info("accountAntiClearing processCount:{}", processCount);
                }
            }
        } while (!CollectionUtils.isEmpty(list));

        Logger.info("accountAntiClearing processCount:{}, succCount:{}, failCount:{} ", processCount, succCount, failCount);
        Logger.info("accountAntiClearing finished！");
    }

    @Override
    public boolean checkShopAgentWhitelist(String customerCode, String customerType) {
        if(!shopAgentCheckSwitch){
            //白名单不校验灰度配置
            return true;
        }
        LambdaQueryWrapper<PinganDecoupleWhitelist> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PinganDecoupleWhitelist::getCustomerCode, customerCode)
                .eq(PinganDecoupleWhitelist::getCustomerType, customerType)
                .eq(PinganDecoupleWhitelist::getStatus, 0)
                .eq(PinganDecoupleWhitelist::getWhitelistShopAgentFlag, 1);
        PinganDecoupleWhitelist whitelist = this.baseMapper.selectOne(wrapper);
        return whitelist != null;
    }

    @Override
    public boolean checkAntiClearing(String customerCode, String customerType) {
        LambdaQueryWrapper<PinganDecoupleWhitelist> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PinganDecoupleWhitelist::getCustomerCode, customerCode)
                .eq(PinganDecoupleWhitelist::getCustomerType, customerType)
                .eq(PinganDecoupleWhitelist::getAntiClearingStatus, 1)
                .eq(PinganDecoupleWhitelist::getStatus, 0);
        PinganDecoupleWhitelist whitelist = this.baseMapper.selectOne(wrapper);
        return whitelist != null;
    }

    @Override
    public void shopAgentWhitelistProcess(int beginIndex, int endIndex) {
        Logger.info("shopAgentWhitelistProcess start！");
        //总处理数量
        int processCount = 0;
        int succCount = 0;
        int failCount = 0;
        List<PinganDecoupleWhitelist> list;
        do {
            list = this.baseMapper.queryWhitelistAgentAccount(beginIndex, endIndex, 500);
            if (CollectionUtils.isEmpty(list)) {
                Logger.warn("shopAgentWhitelistProcess 白名单账户查询结果为空！");
                break;
            }
            beginIndex = Math.toIntExact(list.get(list.size() - 1).getId());
            for (PinganDecoupleWhitelist account : list) {
                try {
                    processCount++;
                    AccountQuery accountQuery = new AccountQuery();
                    accountQuery.setCustomerCode(account.getCustomerCode());
                    accountQuery.setAccountTypeKey(accountService.convertAccountTypeKey(account.getCustomerType()));
                    com.akucun.common.Result<AccountBookDO> accountResult = accountCenterService.queryAccount(accountQuery);
                    if (accountResult == null || !accountResult.isSuccess() || accountResult.getData() == null) {
                        failCount++;
                        continue;
                    }
                    AccountBookDO accountBookDO = accountResult.getData();
                    if (accountBookDO.getBalance().compareTo(BigDecimal.ZERO) <= 0) {
                        failCount++;
                        continue;
                    }
                    boolean result = whitelistAgentProcess(account, accountBookDO.getBalance());
                    if (result) {
                        LambdaQueryWrapper<PinganDecoupleWhitelist> wrapper = new LambdaQueryWrapper<>();
                        wrapper.eq(PinganDecoupleWhitelist::getCustomerCode, account.getCustomerCode())
                                .eq(PinganDecoupleWhitelist::getCustomerType, account.getCustomerType());
                        account.setWhitelistStatus(1);
                        this.baseMapper.update(account, wrapper);
                        succCount++;
                    } else {
                        failCount++;
                    }
                } catch (Exception e) {
                    Logger.warn("shopAgentWhitelistProcess exception, processCount:{}", processCount);
                }
                if (processCount % 500 == 0) {
                    Logger.info("shopAgentWhitelistProcess processCount:{}", processCount);
                }
            }
        } while (!CollectionUtils.isEmpty(list));

        Logger.info("shopAgentWhitelistProcess processCount:{}, succCount:{}, failCount:{} ", processCount, succCount, failCount);
        Logger.info("shopAgentWhitelistProcess finished！");
    }

    /**
     * 白名单店长
     *
     * @param account
     * @param balance
     * @return
     */
    private boolean whitelistAgentProcess(PinganDecoupleWhitelist account, BigDecimal balance) {
        TradeInfo tradeInfo = new TradeInfo();
        tradeInfo.setAccountTypeKey(accountService.convertAccountTypeKey(account.getCustomerType()));
        tradeInfo.setCustomerCode(account.getCustomerCode());
        tradeInfo.setTradeType(DetailTypeConstants.TRADE_TYPE_277.getName());
        tradeInfo.setAmount(balance);
        String orderNo = "DPGX" + generateNo();
        tradeInfo.setTradeNo(orderNo);
        tradeInfo.setSourceBillNo(orderNo);
        tradeInfo.setRemark("店铺贡献");
        Logger.info("whitelistAgentProcess 参数：{}", DataMask.toJSONString(tradeInfo));
        com.akucun.common.Result<Void> result = accountCenterService.dealTrade(tradeInfo);
        Logger.info("whitelistAgentProcess 结果：{}", DataMask.toJSONString(result));
        return result != null && result.isSuccess();
    }

    private void updateAntiClearingStatus(PinganDecoupleWhitelist account) {
        LambdaQueryWrapper<PinganDecoupleWhitelist> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PinganDecoupleWhitelist::getCustomerCode, account.getCustomerCode())
                .eq(PinganDecoupleWhitelist::getCustomerType, account.getCustomerType())
                .eq(PinganDecoupleWhitelist::getStatus, 0);
        account.setAntiClearingStatus(1);
        this.baseMapper.update(account, wrapper);
    }

    @Override
    public boolean antiClearingProcess(String customerCode, String customerType, String antiClearAmount, String withdrawNo) {
        if (StringUtils.isBlank(withdrawNo)) {
            Result<Boolean> checkRes = accountWithdrawService.processingWithdrawExist(customerCode, customerType);
            if (checkRes.getSuccess() && checkRes.getData()) {
                Logger.error("antiClearingProcess customerCode:{} exist processing withdraw record!!", customerCode);
                return false;
            }
        }
        return pinganAntiClearing(customerCode, customerType, antiClearAmount);
    }

    @Override
    public boolean antiClearing4WithdrawFail(String customerCode, String customerType, String antiClearAmount, String withdrawNo) {
        return pinganAntiClearing(customerCode, customerType, antiClearAmount);
    }
    
    @Override
    public Boolean tenantGray(String customerCode, String customerType) {
        Logger.info("DecouplingConfig checkWhitelist decouplingSwitch:{}, customerCode:{}", decouplingTenantSwitch, customerCode);
        if (decouplingTenantSwitch) {
            return Boolean.TRUE;
        }
        // 判断用户是否未为租户若是租户的店长、店主直接转换成租户
        if(!CustomerType.AT.getName().equals(customerType)){
            AccountTenantCustomer accountTenantCustomer = accountTenantCustomerMapper.selectOne(new LambdaQueryWrapper<AccountTenantCustomer>()
                    .eq(AccountTenantCustomer::getCustomerCode,customerCode)
                    .eq(AccountTenantCustomer::getCustomerType,customerType)
                    .eq(AccountTenantCustomer::getStatus, AccountTenantStatusEnum.effect.getCode()));
            if(Objects.isNull(accountTenantCustomer)){
                return Boolean.FALSE;
            }
            customerCode = accountTenantCustomer.getTenantId();
            customerType = CustomerType.AT.getName();
        }
        if(StringUtils.isBlank(customerCode)||StringUtils.isBlank(customerType)){
            return Boolean.FALSE;
        }
        LambdaQueryWrapper<PinganDecoupleWhitelist> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PinganDecoupleWhitelist::getCustomerCode, customerCode)
                .eq(PinganDecoupleWhitelist::getCustomerType, customerType)
                .eq(PinganDecoupleWhitelist::getStatus, 0);
        PinganDecoupleWhitelist whitelist = this.baseMapper.selectOne(wrapper);
        return whitelist != null;
    }

    /**
     * 平安账户反清分
     * @param customerCode
     * @param customerType
     * @param antiClearAmount
     * @return
     */
    private boolean pinganAntiClearing(String customerCode, String customerType, String antiClearAmount) {
        if (AmountUtils.yuan2fen(antiClearAmount) == 0) {
            return true;
        }
        DealTradeVO dealTradeVO = new DealTradeVO();
        dealTradeVO.setOrderNo(generateNo());
        dealTradeVO.setCustomerCode(customerCode);
        dealTradeVO.setCustomerType(customerType);
        dealTradeVO.setTranAmount(new BigDecimal(antiClearAmount));
        dealTradeVO.setContent("账户资金归集");
        dealTradeVO.setSettlementPeriod("0");
        com.akucun.fps.common.entity.Result<Void> result = memberServiceApi.dealTrade(dealTradeVO);
        return result.isSuccess();
    }

    public static String generateNo() {
        //设置日期格式
        SimpleDateFormat df = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        String rdNum = df.format(new Date());
        Random random = new Random();
        int ird = random.nextInt(999999);
        String srd = String.format("%06d", ird);
        return rdNum + srd;
    }

}
