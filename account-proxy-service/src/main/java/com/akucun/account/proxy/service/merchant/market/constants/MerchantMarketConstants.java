package com.akucun.account.proxy.service.merchant.market.constants;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Create on : 2024/5/8 17:25
 **/
public class MerchantMarketConstants {

    //营销预付冻结
    public static final String MARKET_ACCOUNT_FREEZE_TRADE_TYPE = "TRADE_TYPE_326";

    //营销预付解冻
    public final static String MARKET_ACCOUNT_UNFREEZE_TRADE_TYPE = "TRADE_TYPE_327";

    //营销费用扣款
    public final static String MARKET_ACCOUNT_DEDUCT_TRADE_TYPE = "TRADE_TYPE_328";

    //平台出资扣款
    public final static String PLATFORM_DEDUCT_TRADE_TYPE = "TRADE_TYPE_244";

    //奖励金账户奖励发放
    public final static String BONUS_ACCOUNT_AWARD_TRADE_TYPE = "TRADE_TYPE_005";

    //idol饷店余额账户奖励发放
    public final static String IDOL_BALANCE_ACCOUNT_AWARD_TRADE_TYPE = "TRADE_TYPE_051";

    //店长饷店余额账户奖励发放
    public final static String DISTRIBUTOR_BALANCE_ACCOUNT_AWARD_TRADE_TYPE = "TRADE_TYPE_288";

}
