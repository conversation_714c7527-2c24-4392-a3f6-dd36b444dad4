package com.akucun.account.proxy.service.acct.job;

import com.akucun.account.proxy.service.initializing.InitializingStep;
import com.mengxiang.base.common.log.Logger;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author: silei
 * @Date: 2020/11/19
 * @desc:
 */
@Component
public class AccountStepRefreshJob {

    @Autowired
    private InitializingStep initializingStep;

    @XxlJob("AccountStepRefreshJob")
    public ReturnT<String> execute(String param) {
        Logger.info("AccountUpdateCompensateJob start!");
        initializingStep.initStepCache();
        Logger.info("AccountUpdateCompensateJob finished!");
        return ReturnT.SUCCESS;
    }
}
