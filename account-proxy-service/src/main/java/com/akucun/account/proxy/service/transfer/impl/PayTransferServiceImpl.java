package com.akucun.account.proxy.service.transfer.impl;

import com.aikucun.common2.base.Result;
import com.aikucun.common2.utils.DateUtils;
import com.aikucun.common2.utils.datamasking.DataMask;
import com.akucun.account.center.client.model.TradeInfo;
import com.akucun.account.center.feign.AccountCenterService;
import com.akucun.account.proxy.service.config.DecouplingConfig;
import com.akucun.account.proxy.common.constant.ApplyStatus;
import com.akucun.account.proxy.common.constant.CommonConstants;
import com.akucun.account.proxy.common.constant.Constant;
import com.akucun.account.proxy.common.constant.WithdrawChannelConstants;
import com.akucun.account.proxy.common.enums.PostActionExecStatus;
import com.akucun.account.proxy.common.enums.PostActionTypes;
import com.akucun.account.proxy.common.enums.ResponseEnum;
import com.akucun.account.proxy.common.utils.RedisLock;
import com.akucun.account.proxy.common.utils.Results;
import com.akucun.account.proxy.dao.mapper.WithdrawApplyRecordMapper;
import com.akucun.account.proxy.dao.model.AgentWhiteListAccount;
import com.akucun.account.proxy.dao.model.PaymentTransfer;
import com.akucun.account.proxy.dao.model.WithdrawApplyRecord;
import com.akucun.account.proxy.dao.model.WithdrawTaxDetail;
import com.akucun.account.proxy.facade.stub.enums.AccountKeyConstants;
import com.akucun.account.proxy.facade.stub.enums.CustomerType;
import com.akucun.account.proxy.facade.stub.enums.DetailTypeConstants;
import com.akucun.account.proxy.facade.stub.enums.TransferStatusEnum;
import com.akucun.account.proxy.facade.stub.others.dto.req.PaymentTransferReq;
import com.akucun.account.proxy.facade.stub.others.dto.res.PaymentTransferResp;
import com.akucun.account.proxy.service.acct.WithdrawTaxService;
import com.akucun.account.proxy.service.common.AgentWhiteListAccountService;
import com.akucun.account.proxy.service.common.DuplicateCheckService;
import com.akucun.account.proxy.service.common.SpringContextHolder;
import com.akucun.account.proxy.service.postaction.PostActionService;
import com.akucun.account.proxy.service.postaction.bo.PostActionItemBO;
import com.akucun.account.proxy.service.postaction.task.WithdrawReceiptDownloadTask;
import com.akucun.account.proxy.service.receipt.bo.WithdrawReceiptDownloadBO;
import com.akucun.account.proxy.service.transfer.PayTransferService;
import com.akucun.account.proxy.service.transfer.PaymentTransferService;
import com.akucun.account.proxy.service.transfer.TransferCallLogService;
import com.akucun.account.proxy.service.transfer.TransferChannelGatewayService;
import com.akucun.account.proxy.service.transfer.bo.PaymentTransferBO;
import com.akucun.account.proxy.service.transfer.bo.TransferGatewayBO;
import com.akucun.account.proxy.service.transfer.convert.PaymentTransferConvert;
import com.akucun.account.proxy.service.transfer.factory.PayTransferFactory;
import com.akucun.account.proxy.service.transfer.factory.TransferService;
import com.akucun.fps.pingan.client.vo.DealTradeVO;
import com.akucun.pay.gateway.wx.facade.stub.others.dto.req.transfer.BatchTransferReq;
import com.akucun.pay.gateway.wx.facade.stub.others.dto.req.transfer.TransferDetailQueryReq;
import com.akucun.pay.gateway.wx.facade.stub.others.dto.req.transfer.TransferDetailReq;
import com.akucun.pay.gateway.wx.facade.stub.others.dto.res.transfer.BatchTransferResp;
import com.akucun.pay.gateway.wx.facade.stub.others.dto.res.transfer.TransferDetailQueryResp;
import com.akucun.pay.gateway.wx.facade.stub.wechat.WechatTransferFacade;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mengxiang.base.common.log.Logger;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.akucun.account.proxy.common.constant.Constant.TRANSFER_FAIL_CODE;

/**
 * <AUTHOR>
 * @date 2021/01/28 11:50
 */
@Service
public class PayTransferServiceImpl implements PayTransferService {
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private PayTransferFactory transferFactory;
    @Autowired
    private PaymentTransferService paymentTransferService;
    @Autowired
    private PaymentTransferConvert transferConvert;
    @Autowired
    private TransferChannelGatewayService gatewayService;
    @Resource
    private WechatTransferFacade wechatTransferFacade;
    @Autowired
    private PostActionService postActionService;
    @Autowired
    private AgentWhiteListAccountService agentWhiteListAccountService;
    @Resource
    private DuplicateCheckService duplicateCheckService;
    @Autowired
    private TransferCallLogService callLogService;
    @Resource
    private WithdrawApplyRecordMapper withdrawApplyRecordMapper;
    @Autowired
    private WithdrawTaxService withdrawTaxService;
    @Resource
    private AccountCenterService accountCenterService;
    @Autowired
    private DecouplingConfig decouplingConfig;

    @Value("${default.tenant.id:151738493257170900}")
    private String defaultTenantId;
    @Value("${rdmp.tenant.id:151738493257170901}")
    private String rdMpTenantId;
    /**
     * 平安反清分开关
     */
//    @Value("${pingan.antiClearing.switch:true}")
//    private boolean antiClearingSwitch;

    /**
     * 微信提现异常超时失败，单位：分钟
     */
    @Value("${wechat.withdraw.fail.time:-60}")
    private int withdrawFailTime;

    /**
     * 微信提现长时间记录不存在作失败处理开关
     */
    @Value("${wechat.withdraw.norecord.fail.switch:true}")
    private boolean wechatWithdrawNorecordFailSwitch;

    @Override
    public Result<PaymentTransferResp> transfer(PaymentTransferReq request) {

        // 付款加锁防止同一单号重复付款
        String lockKey = String.format(CommonConstants.PAYMENT_TRANSFER_LOCK_PREFIX, request.getSourceNo());
        RedisLock lock = new RedisLock(redisTemplate, lockKey);
        if (!lock.tryLock()) {
            return Results.error(ResponseEnum.TRANSFER_TOO_OFTEN);
        }

        try {
            // 查询付款记录
            PaymentTransfer paymentTransfer = paymentTransferService.queryBySourceNo(request.getSourceCode(), request.getSourceNo());

            // 提现记录存在并且提现成功直接返回
            if (Objects.nonNull(paymentTransfer) && TransferStatusEnum.S.getCode().equals(paymentTransfer.getStatus())) {
                PaymentTransferResp resultData = buildTransferResp(paymentTransfer);
                return Result.success(resultData);
            }

            // 商户信息查询
            if (StringUtils.isEmpty(request.getTenantId())) {
                // 渠道为菜菜小程序时，使用菜菜默认租户id，否则使用默认租户id
                String tenantId = request.getChannelCode().equals(CommonConstants.ORDER_3RD_MP_CHANNEL_CODE) ? rdMpTenantId : defaultTenantId;
                request.setTenantId(tenantId);
            }
            TransferGatewayBO transferGatewayBO = gatewayService.queryGatewayByChannelCode(request.getChannelCode(), request.getTenantId(), request.getAppId());
            if (Objects.isNull(transferGatewayBO)) {
                return Result.error(ResponseEnum.TRANSFER_MERCHAT_NULL.getCode(), ResponseEnum.TRANSFER_MERCHAT_NULL.getMessage());
            }

            // 提现
            TransferService transferService = transferFactory.getTransferService(request.getTransferType().getCode());
            PaymentTransferBO transferBO = null;
            if (Objects.nonNull(paymentTransfer)) {
                String originCode = paymentTransfer.getChannelCode();
                // 同一单号可能变更付款渠道，采用最新值
                if (!originCode.equals(request.getChannelCode())) {
                    paymentTransfer.setChannelCode(request.getChannelCode());
                    paymentTransfer.setMchCode(transferGatewayBO.getMchCode());
                    paymentTransferService.updateOne(paymentTransfer);
                }
                // 失败情况下，先查询提现接口
                transferBO = transferService.transferQuery(paymentTransfer.getTradeNo(), transferGatewayBO);
                // 保存请求日志
                callLogService.saveOne(paymentTransfer.getTradeNo(), "TRANSFER_QUERY", transferBO.getRequestMsg(), transferBO.getResponseMsg());
            } else {
                // 保存提现记录
                paymentTransfer = transferConvert.buildPaymentTransfer(request, transferGatewayBO.getMchCode());
                Boolean saveResult = paymentTransferService.saveOne(paymentTransfer);
                if (!saveResult) {
                    return Result.error(ResponseEnum.TRANSFER_SAVE_ERROR.getCode(), ResponseEnum.TRANSFER_SAVE_ERROR.getMessage());
                }
            }
            // 提现/失败重试提现
            if (Objects.isNull(transferBO) || !transferBO.getSuccess()) {
                paymentTransfer.setOpenId(request.getOpenId());
                transferBO = transferService.transfer(paymentTransfer, transferGatewayBO);
                // 保存请求日志
                callLogService.saveOne(paymentTransfer.getTradeNo(), "TRANSFER", transferBO.getRequestMsg(), transferBO.getResponseMsg());
            }
            // 提现失败时，调用查询接口，防调单处理。
            if (!transferBO.getSuccess()) {
                String gatewayMsg = transferBO.getGatewayMsg();
                transferBO = transferService.transferQuery(paymentTransfer.getTradeNo(), transferGatewayBO);
                // 保存提现时网关错误信息
                transferBO.setGatewayMsg(gatewayMsg);
                // 保存请求日志
                callLogService.saveOne(paymentTransfer.getTradeNo(), "TRANSFER_QUERY", transferBO.getRequestMsg(), transferBO.getResponseMsg());
            }
            if (!transferBO.getSuccess()) {
                // 提现失败
                paymentTransfer.setStatus(StringUtils.isEmpty(transferBO.getStatus()) ? TransferStatusEnum.F.getCode() : transferBO.getStatus());
                paymentTransfer.setGatewayMsg(transferBO.getGatewayMsg());
                // 更新提现记录
                paymentTransferService.updateOne(paymentTransfer);
                return Result.error(ResponseEnum.TRANSFER_ERROR.getCode(), transferBO.getGatewayMsg());
            } else {
                // 提现成功
                paymentTransfer.setGatewayTransactionId(transferBO.getTransactionId());
                paymentTransfer.setTransferSuccessTime(transferBO.getSuccessTime());
                paymentTransfer.setStatus(TransferStatusEnum.S.getCode());
                // 更新提现记录
                paymentTransferService.updateOne(paymentTransfer);
                // 构建返回实体
                PaymentTransferResp resultData = buildTransferResp(paymentTransfer);
                return Result.success(resultData);
            }

        } catch (Exception e) {
            Logger.error("付款异常，请求参数：{}，", request.getSourceNo(), e);
            return Result.error(ResponseEnum.SYSTEM_ERROR.getCode(), ResponseEnum.SYSTEM_ERROR.getMessage());
        } finally {
            // 释放锁
            lock.unlock();
        }
    }


    private PaymentTransferResp buildTransferResp(PaymentTransfer paymentTransfer) {
        PaymentTransferResp transferResp = PaymentTransferResp.builder()
                .tradeNo(paymentTransfer.getTradeNo())
                .sourceNo(paymentTransfer.getSourceNo())
                .transferSuccessTime(paymentTransfer.getTransferSuccessTime())
                .gatewayTransactionId(paymentTransfer.getGatewayTransactionId())
                .amount(new BigDecimal(paymentTransfer.getAmount()).divide(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP))
                .build();
        return transferResp;
    }


    @Override
    @Transactional
    public Result<Void> wechatWithdraw(PaymentTransferReq request) {
        Logger.info("wechatWithdraw async request:{}", DataMask.toJSONString(request));
        // 查询付款记录
        PaymentTransfer paymentTransfer = paymentTransferService.queryBySourceNo(request.getSourceCode(), request.getSourceNo());
        // 提现记录存在并且提现成功直接返回
        if (Objects.nonNull(paymentTransfer)) {
            if (TransferStatusEnum.S.getCode().equals(paymentTransfer.getStatus()) || TransferStatusEnum.F.getCode().equals(paymentTransfer.getStatus())) {
                return Result.success();
            }
            if (TransferStatusEnum.P.getCode().equals(paymentTransfer.getStatus())) {
                return Results.error(ResponseEnum.TRANSFER_TOO_OFTEN);
            }
        }
        if (StringUtils.isEmpty(request.getTenantId())) {
            //使用默认租户id
            request.setTenantId(defaultTenantId);
        }
        TransferGatewayBO transferGatewayBO = gatewayService.queryTransferGateway(request.getChannelCode(), request.getTenantId());
        if (Objects.isNull(transferGatewayBO)) {
            return Results.error(ResponseEnum.TRANSFER_MERCHAT_NULL);
        }
        // 保存提现记录
        paymentTransfer = transferConvert.buildPaymentTransfer(request, transferGatewayBO.getMchCode());
        Boolean saveResult = paymentTransferService.saveOne(paymentTransfer);
        if (!saveResult) {
            return Results.error(ResponseEnum.TRANSFER_SAVE_ERROR);
        }
        //请求微信
        BatchTransferReq req = buildBatchTransferReq(request, transferGatewayBO);
        Logger.info("wechatWithdraw req:{}", DataMask.toJSONString(req));
        Result<BatchTransferResp> result = wechatTransferFacade.batchTransfer(req);
        Logger.info("wechatWithdraw result:{}", DataMask.toJSONString(result));
//            if (result == null || !result.getSuccess() || result.getData()== null){
//                return Results.error(ResponseEnum.TRANSFER_SYSTEM_EXCEPTION);
//            }
        //异步结果查询
        PostActionItemBO itemBO = PostActionItemBO.builder()
                .bizId(request.getBatchNo())
                .paramObject(request)
                .remark("提现到微信余额结果查询")
                .actionType(PostActionTypes.WECHAT_WITHDRAW_QUERY.getName())
                .status(PostActionExecStatus.EXECUTE.value()).build();
        postActionService.addAction(itemBO);
        return Result.success();
    }

    private BatchTransferReq buildBatchTransferReq(PaymentTransferReq request, TransferGatewayBO transferGatewayBO) {
        BatchTransferReq req = new BatchTransferReq();
        List<TransferDetailReq> list = new ArrayList<>();
        TransferDetailReq detailReq = new TransferDetailReq();
        detailReq.setOpenId(request.getOpenId());
        detailReq.setOutDetailNo(request.getSourceNo());
        detailReq.setUserName(request.getCustomerName());
        detailReq.setTransferAmount(request.getAmount().setScale(2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).intValue());
        detailReq.setTransferRemark(request.getRemark());
        list.add(detailReq);
        req.setMerCode(transferGatewayBO.getMchCode());
        req.setTransferDetailList(list);
        req.setBatchName("批量提现请求");
        req.setBatchRemark(request.getRemark());
        req.setOutBatchNo(request.getBatchNo());
        req.setAppId(transferGatewayBO.getAppId());
        req.setTotalNum(1);
        req.setTotalAmount(request.getAmount().setScale(2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).intValue());
        return req;
    }

    @Override
    @Transactional
    public Result<Void> queryWithdrawResp(PaymentTransferReq request) {
        Logger.info("queryWithdrawResp async request:{}",DataMask.toJSONString(request));
        Result<Void> result = Result.success();
        //查询提现记录
        WithdrawApplyRecord record = withdrawApplyRecordMapper.selectOne(new LambdaQueryWrapper<WithdrawApplyRecord>().eq(WithdrawApplyRecord::getWithdrawNo, request.getSourceNo()));
        if (record == null) {
            return Results.error(CommonConstants.GENERAL_CODE, "微信提现通知未查询到提现单据");
        }
        //查询转账记录
        PaymentTransfer paymentTransfer = paymentTransferService.queryBySourceNo(request.getSourceCode(), request.getSourceNo());
        TransferGatewayBO transferGatewayBO = gatewayService.queryTransferGateway(request.getChannelCode(), request.getTenantId());
        if (Objects.isNull(transferGatewayBO)) {
            return Results.error(ResponseEnum.TRANSFER_MERCHAT_NULL);
        }

        TransferDetailQueryReq queryReq = new TransferDetailQueryReq();
        queryReq.setMerCode(transferGatewayBO.getMchCode());
        queryReq.setOutDetailNo(request.getSourceNo());
        queryReq.setOutBatchNo(request.getBatchNo());
        Logger.info("微信提现结果查询 queryWithdrawResp 参数:{}", JSONObject.toJSONString(queryReq));
        Result<TransferDetailQueryResp> queryRespResult = wechatTransferFacade.transferDetailQuery(queryReq);
        Logger.info("微信提现结果查询 queryWithdrawResp 结果:{}", JSONObject.toJSONString(queryRespResult));
        if (queryRespResult == null) {
            return Results.error(ResponseEnum.BATCH_TRANSFER_QUERY_ERROR);
        }
        if (!queryRespResult.getSuccess()) {
            //批量转账异常超时失败处理
            if (TRANSFER_FAIL_CODE.equals(queryRespResult.getCode()) && DateUtils.addMinutes(new Date(), withdrawFailTime).compareTo(record.getCreateTime()) > 0) {
                Logger.info("微信提现queryWithdrawResp异常超时失败 参数:{}", DataMask.toJSONString(queryReq));
                if(wechatWithdrawNorecordFailSwitch){
                    record.setFailReason(queryRespResult.getMessage());
                    return wechatWithdrawFail(record, paymentTransfer);
                } else {
                    return Results.error(ResponseEnum.BATCH_TRANSFER_QUERY_ERROR);
                }
            }
            return Results.error(ResponseEnum.BATCH_TRANSFER_QUERY_ERROR);
        }
        if (queryRespResult.getData() == null) {
            return Results.error(ResponseEnum.BATCH_TRANSFER_EMPTY_RESULT);
        }
        TransferDetailQueryResp detailResp = queryRespResult.getData();
        if (StringUtils.isEmpty(detailResp.getOutDetailNo()) || StringUtils.isEmpty(detailResp.getDetailStatus())) {
            return Results.error(ResponseEnum.BATCH_TRANSFER_RESULT_PARAM_ERROR);
        }
        if (Constant.SUCCESS.equals(detailResp.getDetailStatus())) {
            //更新微信提现流水号
            paymentTransfer.setGatewayTransactionId(detailResp.getDetailId());
            paymentTransfer.setStatus(TransferStatusEnum.S.getCode());
            paymentTransferService.updateOne(paymentTransfer);
            withdrawApplyRecordMapper.updateWithdrawRecordStatus(detailResp.getOutDetailNo(), ApplyStatus.SUCC.name(), "");
            //提现成功扣税
            withdrawTaxService.wechatWithdrawSuccForTax(record);

            //新增拉取回执单地址定时任务
            SpringContextHolder.getBean(WithdrawReceiptDownloadTask.class).init(WithdrawChannelConstants.WECHAT.getName(),
                    record.getWithdrawNo(), request.getBatchNo(), transferGatewayBO.getMchCode(), false, record.getCustomerType());
        } else if (Constant.FAIL.equals(detailResp.getDetailStatus())) {
            Logger.info("Wechat queryWithdrawResp 用户提现失败:{}", JSONObject.toJSONString(detailResp));
            record.setFailReason(detailResp.getFailReason());
            return wechatWithdrawFail(record, paymentTransfer);
        } else {
            return Result.error(501, "交易处理中");
        }

        return result;
    }

    private Result<Void> wechatWithdrawFail(WithdrawApplyRecord record, PaymentTransfer paymentTransfer) {
        Result<Void> result = Result.success();

        paymentTransfer.setStatus(TransferStatusEnum.F.getCode());
        paymentTransferService.updateOne(paymentTransfer);
        //提现状态更新为失败
        withdrawApplyRecordMapper.updateWithdrawRecordStatus(record.getWithdrawNo(), ApplyStatus.FAIL.name(), record.getFailReason());
        //提现失败扣税回退
        withdrawTaxService.withdrawFailForTax(record, record.getFailReason());
        //微信累计提现金额退回
        WithdrawTaxDetail detail = withdrawTaxService.getWithdrawTaxDetail(record.getWithdrawNo());
        withdrawTaxService.wechatWithdrawFailSumAmount(record, detail);
        //账户中心备注记录失败原因
        record.setRemark(record.getFailReason());
        if (CustomerType.NMDL.name().equals(record.getCustomerType())) {
            if (decouplingConfig.decouplingSwitch(record.getCustomerCode(),record.getCustomerType())){
                result = decouplingShopAgentWechatWithdrawFail(record);
            }else {
                result = shopAgentWechatWithdrawFail(record);
            }
        } else if (CustomerType.NM.name().equals(record.getCustomerType())) {
            result = shopkeeperWechatWithdrawFail(record);
        }
        return result;
    }


    /**
     * 店主微信提现失败处理
     *
     * @param record
     * @return
     */
    private Result<Void> shopkeeperWechatWithdrawFail(WithdrawApplyRecord record) {
        Result<Void> result = Result.success();
        //店主退款任务
        if (decouplingConfig.antiClearingSwitch(record.getCustomerCode(), record.getCustomerType())) {
            DealTradeVO dealTradeRefundVO = new DealTradeVO();
            dealTradeRefundVO.setRemark("店主提现交易撤销");
            dealTradeRefundVO.setTranAmount(record.getAmount());
            dealTradeRefundVO.setCustomerCode(record.getCustomerCode());
            dealTradeRefundVO.setCustomerType(record.getCustomerType());
            dealTradeRefundVO.setOrderNo(record.getWithdrawNo());
            dealTradeRefundVO.setUniqueKey(record.getWithdrawNo());
            PostActionItemBO refund = PostActionItemBO.builder()
                    .bizId("NM" + record.getWithdrawNo()).paramObject(dealTradeRefundVO).remark("店主微信提现交易撤销")
                    .actionType(PostActionTypes.PINGAN_REFUND.getName()).status(PostActionExecStatus.EXECUTE.value())
                    .nextRetryTime(LocalDateTime.now().plusSeconds(15)).build();
            postActionService.addAction(refund);
        }
        //调用账户中心进行金额操作
        TradeInfo tradeInfoRefund = generateTradeInfo(record, AccountKeyConstants.NM.getName(), DetailTypeConstants.TRADE_TYPE_466.name());
        com.akucun.common.Result<Void> tradeRefundResult = accountCenterService.dealTrade(tradeInfoRefund);
        if (!tradeRefundResult.isSuccess()) {
            result = Results.error(CommonConstants.GENERAL_CODE, tradeRefundResult.getMessage());
            Logger.error("queryWithdrawResp 店主提现返还失败 :{}", tradeRefundResult.getMessage());
        }
        return result;
    }

    /**
     * 构建tradeInfo
     *
     * @param record
     * @return
     */
    private TradeInfo generateTradeInfo(WithdrawApplyRecord record, String accountTypeKey, String tradeType) {
        TradeInfo tradeInfo = new TradeInfo();
        tradeInfo.setAccountTypeKey(accountTypeKey);
        tradeInfo.setTradeType(tradeType);
        tradeInfo.setSourceBillNo(record.getWithdrawNo());
        tradeInfo.setTradeNo(record.getWithdrawNo());
        tradeInfo.setAmount(record.getAmount());
        tradeInfo.setCustomerCode(record.getCustomerCode());
        tradeInfo.setRemark(record.getRemark());
        return tradeInfo;
    }

    private Result<Void> decouplingShopAgentWechatWithdrawFail(WithdrawApplyRecord record) {
        Logger.info("decouplingShopAgentWechatWithdrawFail record:{}", DataMask.toJSONString(record));
        Result<Void> result = Result.success();

        Result<Void> checkResult = duplicateCheckService.checkDuplicate("NMDL_REFUND_" + record.getWithdrawNo(), Boolean.FALSE);
        if (!checkResult.getSuccess()) {
            Logger.info("decouplingShopAgentWechatWithdrawFail 店长微信提现触发幂等:{}", "NMDL_REFUND_" + record.getWithdrawNo());
            return result;
        }
        try {
            //调用账户中心进行金额操作
            TradeInfo tradeInfo = generateTradeInfo(record, AccountKeyConstants.NMDL.getName(), DetailTypeConstants.TRADE_TYPE_273.name());
            //非白名单店长退款任务
            wechatWithdrawAccountRollback(record, record.getCustomerCode(), record.getCustomerType(), "店长提现交易撤销");
            Logger.info("decouplingShopAgentWechatWithdrawFail accountCenterService.dealTrade tradeInfo:{}", DataMask.toJSONString(tradeInfo));
            com.akucun.common.Result<Void> tradeResult = accountCenterService.dealTrade(tradeInfo);
            if (!tradeResult.isSuccess()) {
                Logger.error("decouplingShopAgentWechatWithdrawFail 调用账户中心失败{}", tradeResult.getMessage());
                result.setSuccess(Boolean.FALSE);
                result.setMessage(tradeResult.getMessage());
            }
        } catch (Exception e) {
            result.setMessage("代理提现失败返金异常");
            result.setSuccess(Boolean.FALSE);
            Logger.error(result.getMessage(), e);
        }
        return result;
    }


    /**
     * 店长提现失败处理
     *
     * @param record
     * @return
     */
    private Result<Void> shopAgentWechatWithdrawFail(WithdrawApplyRecord record) {
        Logger.info("shopAgentWechatWithdrawFail record:{}", DataMask.toJSONString(record));
        Result<Void> result = Result.success();

        Result<Void> checkResult = duplicateCheckService.checkDuplicate("NMDL_REFUND_" + record.getWithdrawNo(), Boolean.FALSE);
        if (!checkResult.getSuccess()) {
            Logger.info("shopAgentWechatWithdrawFail 店长微信提现触发幂等:{}", "NMDL_REFUND_" + record.getWithdrawNo());
            return result;
        }
        try {
            //查询代理白名单
            AgentWhiteListAccount whiteAccount = agentWhiteListAccountService.getOne(new LambdaQueryWrapper<AgentWhiteListAccount>().eq(AgentWhiteListAccount::getShopId, record.getShopId()).eq(AgentWhiteListAccount::getStatus, 1));
            Logger.info("shopAgentWechatWithdrawFail whiteAccount:{}", DataMask.toJSONString(whiteAccount));
            //调用账户中心进行金额操作
            TradeInfo tradeInfo = generateTradeInfo(record, AccountKeyConstants.NMDL.getName(), DetailTypeConstants.TRADE_TYPE_273.name());
            //白名单代理处理逻辑
            if (whiteAccount != null) {
                TradeInfo tradeNew = new TradeInfo();
                //代理处理
                tradeInfo.setAccountTypeKey(AccountKeyConstants.NM.getName());
                tradeInfo.setCustomerCode(whiteAccount.getCustomerCode());
                tradeInfo.setTradeType(DetailTypeConstants.TRADE_TYPE_466.name());
                BeanUtils.copyProperties(tradeInfo, tradeNew);
                tradeNew.setCustomerCode(record.getCustomerCode());
                tradeNew.setTradeType(DetailTypeConstants.TRADE_TYPE_273.name());
                tradeNew.setAccountTypeKey(AccountKeyConstants.NMDL.getName());
                PostActionItemBO itemBO = PostActionItemBO.builder()
                        .bizId(record.getWithdrawNo())
                        .paramObject(tradeNew)
                        .remark("白名单代理微信提现失败返还")
                        .actionType(PostActionTypes.ACCOUNT_CENTER_COMPENSATE.getName())
                        .status(PostActionExecStatus.EXECUTE.value()).build();
                postActionService.addAction(itemBO);
                //白名单店主退款任务
                wechatWithdrawAccountRollback(record, whiteAccount.getCustomerCode(), CustomerType.NM.getName(), "白名单店主提现交易撤销");
            } else {
                //非白名单店长退款任务
                wechatWithdrawAccountRollback(record, record.getCustomerCode(), record.getCustomerType(), "店长提现交易撤销");
            }
            Logger.info("shopAgentWechatWithdrawFail accountCenterService.dealTrade tradeInfo:{}", DataMask.toJSONString(tradeInfo));
            com.akucun.common.Result<Void> tradeResult = accountCenterService.dealTrade(tradeInfo);
            if (!tradeResult.isSuccess()) {
                Logger.error("shopAgentWechatWithdrawFail 调用账户中心失败{}", tradeResult.getMessage());
                result.setSuccess(Boolean.FALSE);
                result.setMessage(tradeResult.getMessage());
            }
        } catch (Exception e) {
            result.setMessage("代理提现失败返金异常");
            result.setSuccess(Boolean.FALSE);
            Logger.error(result.getMessage(), e);
        }
        return result;
    }

    /**
     * 提现交易撤销
     *
     * @param record
     * @param customerCode
     */
    private void wechatWithdrawAccountRollback(WithdrawApplyRecord record, String customerCode, String customerType, String remark) {
        if (decouplingConfig.antiClearingSwitch(customerCode, customerType)) {
            DealTradeVO dealTradeRefundVO = new DealTradeVO();
            dealTradeRefundVO.setRemark(remark);
            dealTradeRefundVO.setTranAmount(record.getAmount());
            dealTradeRefundVO.setCustomerCode(customerCode);
            dealTradeRefundVO.setCustomerType(customerType);
            dealTradeRefundVO.setOrderNo(record.getWithdrawNo());
            dealTradeRefundVO.setUniqueKey(record.getWithdrawNo());
            //退款异步任务
            PostActionItemBO refund = PostActionItemBO.builder()
                    .bizId("NMDL" + record.getWithdrawNo()).paramObject(dealTradeRefundVO).remark(remark)
                    .actionType(PostActionTypes.PINGAN_REFUND.getName()).status(PostActionExecStatus.EXECUTE.value())
                    .nextRetryTime(LocalDateTime.now().plusSeconds(15)).build();
            postActionService.addAction(refund);
        }

    }


}
