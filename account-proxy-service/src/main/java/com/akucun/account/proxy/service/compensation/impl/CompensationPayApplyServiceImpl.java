package com.akucun.account.proxy.service.compensation.impl;

import com.aikucun.common2.log.Logger;
import com.akucun.account.proxy.dao.mapper.CompensationPayApplyMapper;
import com.akucun.account.proxy.dao.model.CompensationPayApply;
import com.akucun.account.proxy.facade.stub.enums.CompensationFillPayTypeEnum;
import com.akucun.account.proxy.facade.stub.others.dto.req.CompensationFillPayApplyReq;
import com.akucun.account.proxy.service.compensation.CompensationPayApplyService;
import com.akucun.account.proxy.service.compensation.task.CompensationPayApplyTask;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mengxiang.transaction.framework.executor.InsurableTaskExecutor;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Create on : 2024/11/26 10:40
 **/
@Service
public class CompensationPayApplyServiceImpl extends ServiceImpl<CompensationPayApplyMapper, CompensationPayApply> implements CompensationPayApplyService {


    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private InsurableTaskExecutor insurableTaskExecutor;

    /**
     * 补款申请
     *
     * @param req
     * @return
     */
    @Override
    public Pair<Boolean, String> deal(CompensationFillPayApplyReq req) {
        //01-参数校验
        CompensationFillPayTypeEnum payTypeEnum = CompensationFillPayTypeEnum.getByCode(req.getType());
        CompensationPayApply existRecord = getOne(new LambdaQueryWrapper<CompensationPayApply>()
                .eq(CompensationPayApply::getSourceBusinessNo, req.getSourceBusinessNo())
                .eq(CompensationPayApply::getMerchantCode, req.getMerchantCode())
                .eq(CompensationPayApply::getTenantId, req.getTenantId()));
        if (!ObjectUtils.isEmpty(existRecord)) {
            return Pair.of(true, "该笔补款申请已申请");
        }

        //02-落地数据
        Boolean opFlag = Boolean.TRUE;
        String msg = "业务执行成功";
        try {
            save(buildCompensationPayApply(req));
            Logger.info("CompensationPayApplyServiceImpl save suss:{}",JSON.toJSONString(req));
        } catch(Exception e) {
            if(e instanceof DuplicateKeyException && ((DuplicateKeyException)e).getMessage().contains("Duplicate entry")){
                Logger.info("重复请求：数据已落地并执行成功-{}",req.getSourceBusinessNo());
            }else{
                opFlag = Boolean.FALSE;
                msg = e.getMessage();
                Logger.error("CompensationPayApplyServiceImpl deal fail:{}", JSON.toJSONString(req),e);
            }
        }

        //03-提交请求到一致性框架
        CompensationPayApplyTask payApplyTask = new CompensationPayApplyTask(req);
        insurableTaskExecutor.execute(payApplyTask);
        Logger.info("CompensationPayApplyServiceImpl deal suss:{}",JSON.toJSONString(req));

        return Pair.of(opFlag, msg);

        /*//03-业务执行
        Result<Void> voidResult = transactionTemplate.execute(new TransactionCallback<Result<Void>>() {
            @Override
            public Result<Void> doInTransaction(TransactionStatus transactionStatus) {
                Result<Void> result = new Result<Void>();

                try {
                    //01-业务调用
                    //01-1:运费补偿、补货款类型 -> 回调通知给售后系统
                    if (payTypeEnum == CompensationFillPayTypeEnum.SHIPPING_FEE_COMPENSATION
                            || payTypeEnum == CompensationFillPayTypeEnum.GOODS_FEE_COMPENSATION) {
                        com.akucun.common.Result<Void> resultTemp =  callBackOrderFeign.newSubsidiesCallback(JSON.toJSONString(refundNotifyResDTO));
                        if(ObjectUtils.isEmpty(resultTemp) || !resultTemp.isSuccess()){
                            result.setSuccess(false);
                            result.setMessage("业务执行异常");
                        }
                    }else if(payTypeEnum == CompensationFillPayTypeEnum.SHIPPING_INSURANCE_CLAIM){

                    }

                    //02-落地数据
                    save(buildCompensationPayApply(req));
                } catch(Exception e) {
                    Logger.error("CompensationPayApplyServiceImpl deal fail:{}", JSON.toJSONString(req),e);
                    if(e instanceof DuplicateKeyException && ((DuplicateKeyException)e).getMessage().contains("Duplicate entry")){
                        result.setSuccess(true);
                        result.setMessage("重复请求：数据已落地并执行成功");
                    }else{
                        result.setSuccess(false);
                        result.setMessage("业务执行异常");

                        transactionStatus.setRollbackOnly();
                    }
                } finally {

                }

                return result;
            }
        });*/

    }


    private CompensationPayApply buildCompensationPayApply(CompensationFillPayApplyReq req){
        CompensationPayApply payApply = new CompensationPayApply();
        BeanUtils.copyProperties(req, payApply);
        if(!ObjectUtils.isEmpty(req.getExts())) {
            payApply.setExts(JSON.toJSONString(req.getExts()));
        }

        return payApply;
    }
}
