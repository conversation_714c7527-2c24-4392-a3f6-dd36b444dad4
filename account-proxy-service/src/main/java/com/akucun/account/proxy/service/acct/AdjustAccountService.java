package com.akucun.account.proxy.service.acct;

import com.aikucun.common2.base.Pagination;
import com.akucun.account.proxy.facade.stub.others.dto.req.AccountAdjustReq;
import com.akucun.account.proxy.facade.stub.others.dto.req.AdjustAccountQueryReq;
import com.akucun.account.proxy.facade.stub.others.dto.req.PinganAdjustReq;
import com.akucun.account.proxy.facade.stub.others.dto.res.AdjustAccountQueryRes;
import org.apache.commons.lang3.tuple.Pair;

import java.util.Date;

public interface AdjustAccountService {

    /**
     * 根据请求号更新状态
     * @return
     */
    Boolean updateStatusByRequestNo(String status,String requestNo);

    /**
     * 根据请求号更新状态、来源单号、交易流水号
     * @param status
     * @param sourceBillNo
     * @param tradeNo
     * @param requestNo
     * @return
     */
    Boolean updateValuesByRequestNo(String status,String sourceBillNo,String tradeNo,String requestNo);

    /**
     * 平安调账插入数据
     * @param pinganAdjustReq
     * @return
     */
    Pair<Integer,String> insertPinganAdjudt(PinganAdjustReq pinganAdjustReq);

    /**
     * 账户调账插入数据
     * @param accountAdjustReq
     * @return
     */
    Pair<Integer,String> insertAccountAdjust(AccountAdjustReq accountAdjustReq);

    /**
     * 调账分页查询
     * @param adjustAccountQueryReq
     * @return
     */
    Pagination<AdjustAccountQueryRes> adjustAccountQuery(AdjustAccountQueryReq adjustAccountQueryReq);

    /**
     * 查询配置分钟内是否被操作
     * @param opreator
     * @param customter
     * @param limitTime
     * @return
     */
    Boolean selectOperation(String opreator, String customter, Date limitTime);


}
