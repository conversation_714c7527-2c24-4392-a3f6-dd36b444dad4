package com.akucun.account.proxy.service.tradeflow.script.phaseexec;

import com.aikucun.common2.log.Logger;
import com.aikucun.common2.utils.datamasking.DataMask;
import com.akucun.account.center.client.model.AccountInfo;
import com.akucun.account.center.feign.AccountCenterService;
import com.akucun.account.proxy.common.enums.TenantTypeEnum;
import com.akucun.account.proxy.common.utils.CustomerUtils;
import com.akucun.account.proxy.common.utils.EncryptUtils;
import com.akucun.account.proxy.dao.mapper.AccountTenantCustomerMapper;
import com.akucun.account.proxy.dao.mapper.AccountTenantMerchantMapper;
import com.akucun.account.proxy.dao.model.AccountTenantCustomer;
import com.akucun.account.proxy.dao.model.AccountTenantMerchant;
import com.akucun.account.proxy.facade.stub.enums.CustomerType;
import com.akucun.account.proxy.facade.stub.enums.IdentifyEnum;
import com.akucun.account.proxy.facade.stub.others.trade.res.TradePhaseStatusEnum;
import com.akucun.account.proxy.service.common.SpringContextHolder;
import com.akucun.account.proxy.service.tradeflow.constants.ErrorCodes;
import com.akucun.account.proxy.service.tradeflow.dto.PhaseExecResult;
import com.akucun.account.proxy.service.tradeflow.dto.bizinfo.BizInfoRegister;
import com.akucun.account.proxy.service.tradeflow.script.IPhaseExecScript;
import com.akucun.cloud.security.CodeUtils;
import com.akucun.fps.common.entity.Result;
import com.akucun.fps.pingan.client.vo.RegisterRealVO;
import com.akucun.fps.pingan.feign.api.merchant.MerchantServiceApi;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.commons.lang3.StringUtils;

public class ScriptPhaseExecRegister implements IPhaseExecScript {

    @Override
    public PhaseExecResult exec(Object bizInfo) {
        BizInfoRegister register = (BizInfoRegister) bizInfo;
        String convertCustomerCode = CustomerUtils.convertCustomerCode(register.getCustomerCode(), register.getCustomerType(), register.getAccountType());

        //企业饷店店主店长存表
        if((CustomerType.NM.name().equals(register.getCustomerType()) || CustomerType.NMDL.name().equals(register.getCustomerType()))
            && (register.getTenantType() == TenantTypeEnum.tenantType2.getValue()
                || register.getTenantType() == TenantTypeEnum.OPEN_SUPPLY_CONSIGNMENT_COLLECTING.getValue())) {
            AccountTenantCustomer customer = new AccountTenantCustomer();
            customer.setCustomerType(register.getCustomerType());
            customer.setCustomerCode(convertCustomerCode);
            customer.setTenantId(register.getTenantId());
            //查询是否存在
            LambdaQueryWrapper<AccountTenantCustomer> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(AccountTenantCustomer::getCustomerCode, customer.getCustomerCode())
                    .eq(AccountTenantCustomer::getCustomerType, customer.getCustomerType())
                    .eq(AccountTenantCustomer::getTenantId, customer.getTenantId());
            AccountTenantCustomer accountTenantCustomer = SpringContextHolder.getBean(AccountTenantCustomerMapper.class).selectOne(wrapper);
            if (accountTenantCustomer == null) {
                SpringContextHolder.getBean(AccountTenantCustomerMapper.class).insert(customer);
            }
        }

        //租户存表
        if(CustomerType.AT.name().equals(register.getCustomerType())) {
            AccountTenantMerchant merchant = new AccountTenantMerchant();
            merchant.setTenantId(register.getTenantId());
            merchant.setTenantType(String.valueOf(register.getTenantType()));//租户类型  tenant_type  0 爱库存， 1.三方小程序， 2.企业饷店，3.SASS标准类型
            merchant.setTenantName(register.getCustomerName());
            LambdaQueryWrapper<AccountTenantMerchant> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(AccountTenantMerchant::getTenantId, merchant.getTenantId())
                    .eq(AccountTenantMerchant::getTenantType, merchant.getTenantType())
                    .eq(AccountTenantMerchant::getStatus, 0);
            AccountTenantMerchant accountTenantMerchant = SpringContextHolder.getBean(AccountTenantMerchantMapper.class).selectOne(wrapper);
            if (accountTenantMerchant == null) {
                SpringContextHolder.getBean(AccountTenantMerchantMapper.class).insert(merchant);
            }
        }

        //开平安账户
        RegisterRealVO realVO = new RegisterRealVO();
        realVO.setCustomerType(CustomerUtils.convertPinganCustomerType(register.getCustomerType(), register.getAccountType()));
        realVO.setCustomerCode(convertCustomerCode);
        realVO.setCustomerName(register.getCustomerName());
        realVO.setIdType(IdentifyEnum.valueOf(register.getIdType()).getCode());
        if(!IdentifyEnum.CREDIT_CODE.name().equals(register.getIdType())) {
            String decryptIdCode = EncryptUtils.decrypt(register.getIdCode());
            if(StringUtils.isBlank(decryptIdCode)) {
                return new PhaseExecResult(TradePhaseStatusEnum.FAIL, ErrorCodes.DATA_ERROR, "bizInfo.idCode解密失败");
            }
            realVO.setIdCode(decryptIdCode);
        } else {
            realVO.setIdCode(register.getIdCode());
        }
        if(StringUtils.isNotBlank(register.getPhone())) {
            realVO.setMobilePhone(CodeUtils.decrypt(register.getPhone()).getData());
        }
        realVO.setCustProperty(CustomerUtils.getCustProperty(register.getCustomerType()).getCode());
        realVO.setBusinessFlag(2);
        Logger.info("ScriptPhaseExecRegister平安开户，请求：{}", DataMask.toJSONString(realVO));
        Result<String> pinganResult = SpringContextHolder.getBean(MerchantServiceApi.class).registerReal(realVO);
        Logger.info("ScriptPhaseExecRegister平安开户，请求：{}，返回：{}", DataMask.toJSONString(realVO), DataMask.toJSONString(pinganResult));
        if(!pinganResult.isSuccess()) {
            return new PhaseExecResult(TradePhaseStatusEnum.FAIL, ErrorCodes.CHANNEL_ERROR, pinganResult.getErrorMessage());
        }

        //开账户中心账户
        AccountInfo accountInfo = new AccountInfo();
        accountInfo.setAccountTypeKey(getAccountTypeKey(register.getAccountType()));
        accountInfo.setCustomerCode(convertCustomerCode);
        accountInfo.setCustomerName(register.getCustomerName());
        accountInfo.setOperationType("CREATE");
        Logger.info("ScriptPhaseExecRegister账户中心开户，请求：{}", DataMask.toJSONString(accountInfo));
        com.akucun.common.Result<Void> accountResult = SpringContextHolder.getBean(AccountCenterService.class).dealAccount(accountInfo);
        Logger.info("ScriptPhaseExecRegister账户中心开户，请求：{}，返回：{}", DataMask.toJSONString(accountInfo), DataMask.toJSONString(accountResult));
        if(!accountResult.isSuccess()) {
            return new PhaseExecResult(TradePhaseStatusEnum.EXCEPTION, ErrorCodes.CHANNEL_ERROR, accountResult.getMessage());
        }

        return new PhaseExecResult(TradePhaseStatusEnum.SUCCESS, null, null);
    }

    private String getAccountTypeKey(String accountType) {
        switch (accountType) {
            case "XD_NM":
                return "8D256656F0A9E0A959024F16A8C910B3";
            case "XD_NMDL":
                return "6A55B6EA4B16E697ED8F30DE17AFBA34";
            case "WALLET" :
                return "D310397BA71383DCD8208A5DD49F25C1";
            case "AT":
                return "CD1CB5580F8C4307E986FD77ED98DC1E";
            case "SH":
                return "1B0224AA3F894DA4687F82D231D7F0CE";
            case "OP":
                return "AEC6DAA921F51796F05CC3AE025A04EC";
            default:
                return null;
        }
    }

}
