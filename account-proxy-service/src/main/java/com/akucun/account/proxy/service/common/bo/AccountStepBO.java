package com.akucun.account.proxy.service.common.bo;

import lombok.Builder;
import lombok.Data;

import java.util.Map;

/**
 * @Author: silei
 * @Date: 2020/8/26
 * @desc:
 */
@Data
@Builder
public class AccountStepBO {

    /**
     * 账户交易类型
     */
    private String tradeType;

    /**
     * 账户交易子类型
     */
    private String subTradeType;

    /**
     * 是否是流程第一步：N 不是，Y 是
     */
    private String isHead;

    /**
     * 步骤具体类名
     */
    private String stepRefClass;

    /**
     * 执行具体类名
     */
    private String execRefClass;

    /**
     * 查询具体类名
     */
    private String queryRefClass;

    /**
     * 扩展字段
     */
    private String extMap;

    /**
     * 后续流程配置信息
     */
    private Map<String,String> nextStepMap;
}
