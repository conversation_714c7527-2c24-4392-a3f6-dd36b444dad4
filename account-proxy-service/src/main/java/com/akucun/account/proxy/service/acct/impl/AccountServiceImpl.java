package com.akucun.account.proxy.service.acct.impl;

import com.aikucun.common2.utils.CollectionUtils;
import com.aikucun.common2.utils.datamasking.DataMask;
import com.akucun.account.center.client.model.AccountInfo;
import com.akucun.account.center.client.model.TradeInfo;
import com.akucun.account.center.client.model.account.AccountBookDO;
import com.akucun.account.center.client.model.query.AccountQuery;
import com.akucun.account.center.feign.AccountCenterService;
import com.akucun.account.proxy.client.bonus.AccountMemberClient;
import com.akucun.account.proxy.client.center.AccountCenterClient;
import com.akucun.account.proxy.client.pingan.MerchantClient;
import com.akucun.account.proxy.common.config.ApolloConfigCenter;
import com.akucun.account.proxy.common.constant.ApplyStatus;
import com.akucun.account.proxy.common.enums.ErrorCodeConstants;
import com.akucun.account.proxy.common.enums.PostActionExecStatus;
import com.akucun.account.proxy.common.enums.PostActionTypes;
import com.akucun.account.proxy.common.utils.AccountUtils;
import com.akucun.account.proxy.dao.mapper.AccountTenantCustomerMapper;
import com.akucun.account.proxy.dao.mapper.WithdrawApplyRecordMapper;
import com.akucun.account.proxy.dao.model.AccountTenantCustomer;
import com.akucun.account.proxy.dao.model.WithdrawApplyRecord;
import com.akucun.account.proxy.facade.stub.enums.AccountKeyConstants;
import com.akucun.account.proxy.facade.stub.enums.CustomerType;
import com.akucun.account.proxy.facade.stub.others.dto.req.AccountInfoReq;
import com.akucun.account.proxy.facade.stub.others.dto.res.AccountInfoResp;
import com.akucun.account.proxy.service.acct.AccountService;
import com.akucun.account.proxy.service.help.FinClearingCoreFacadeHelp;
import com.akucun.account.proxy.service.postaction.PostActionService;
import com.akucun.account.proxy.service.postaction.bo.PostActionItemBO;
import com.akucun.account.proxy.service.postaction.task.AccountMonitorTask;
import com.akucun.common.Result;
import com.akucun.fps.common.entity.ResultList;
import com.akucun.fps.pingan.client.constants.BankCardStatusConstants;
import com.akucun.fps.pingan.client.constants.CustomerNatureType;
import com.akucun.fps.pingan.client.model.po.PinganAccount;
import com.akucun.fps.pingan.client.model.query.BindCardQueryReq;
import com.akucun.fps.pingan.client.model.query.PinganCardQueryDO;
import com.akucun.fps.pingan.client.vo.PinganCardVO;
import com.akucun.fps.pingan.feign.api.merchantquery.MerchantQueryServiceApi;
import com.akucun.fps.pingan.feign.api.settlement.SettlementServiceApi;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mengxiang.base.common.log.Logger;
import org.apache.commons.collections.map.HashedMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.*;

/**
 * @Author: silei
 * @Date: 2020/10/22
 * @desc:
 */
@Service
public class AccountServiceImpl implements AccountService {

    @Autowired
    private AccountCenterService accountCenterService;
    @Resource
    private MerchantQueryServiceApi merchantQueryServiceApi;
    @Autowired
    private AccountTenantCustomerMapper accountTenantCustomerMapper;
    @Autowired
    private WithdrawApplyRecordMapper withdrawApplyRecordMapper;
    @Autowired
    private MerchantClient merchantClient;

    @Value("${account.upgrade.withdraw.check.time.create:-30}")
    private int withdrawCheckTimeCreate;//限制查询最近30分钟内的提现，走索引
    @Value("${account.upgrade.withdraw.check.time.fail:-10}")
    private int withdrawCheckTimeFail;//最近10秒没有提现失败记录
    @Value("${account.upgrade.withdraw.check.time.process:-30}")
    private int withdrawCheckTimeProcess;//最近30分钟没有提现处理中记录
    @Value("${sms.time.control:false}")
    private boolean smsTimeControl;
    @Autowired
    private PostActionService postActionService;

    @Autowired
    private ApolloConfigCenter apolloConfigCenter;

    @Autowired
    private AccountMemberClient accountMemberClient;

    @Value("${account.bonus.switch:true}")
    private boolean accountBonusSwitch;

    @Value("${dgDecouple:false}")
    private boolean dgDecouple;

    @Resource
    private SettlementServiceApi settlementServiceApi;

    @Autowired
    private FinClearingCoreFacadeHelp finClearingCoreFacadeHelp;

    @Override
    public void lockAccount(String customerCode, String customerType, Long time) {
        AccountInfo info = new AccountInfo();
        info.setCustomerCode(customerCode);
        //FREEZE：冻结账户,不能进也不能出
        info.setOperationType("FREEZE");
        info.setAccountTypeKey(convertAccountTypeKey(customerType));
        accountCenterService.dealAccount(info);
    }

    @Override
    public void unlockAccount(String customerCode, String customerType) {
        AccountInfo info = new AccountInfo();
        info.setCustomerCode(customerCode);
        //UNFREEZE：解冻账户
        info.setOperationType("UNFREEZE");
        info.setAccountTypeKey(convertAccountTypeKey(customerType));
        accountCenterService.dealAccount(info);
    }

    @Override
    public List<AccountInfoResp> queryAccountInfo(List<AccountInfoReq> list) {

        List<AccountInfoResp> respList = new ArrayList<>();
        for (AccountInfoReq req : list) {
            AccountQuery query = new AccountQuery();
            query.setCustomerCode(AccountUtils.getSellerCode(req.getCustomerCode(), req.getCustomerType()));
            query.setAccountTypeKey(convertAccountTypeKey(req.getCustomerType()));
            Result<AccountBookDO> result = accountCenterService.queryAccount(query);
            Logger.info("AccountServiceImpl queryAccountInfo result:{}", DataMask.toJSONString(result));
            if (result != null && result.isSuccess() && result.getData() != null) {
                AccountInfoResp resp = AccountInfoResp.builder()
                        .customerCode(req.getCustomerCode())
                        .customerType(req.getCustomerType())
                        .amount(result.getData().getAmount())
                        .balance(result.getData().getBalance())
                        .build();
                respList.add(resp);
            }
        }
        return respList;
    }

    @Override
    public Boolean queryAccountBindStatus(String customerCode, String customerType) {
        AccountQuery query = new AccountQuery();
        query.setCustomerCode(AccountUtils.getSellerCode(customerCode, customerType));
        query.setAccountTypeKey(convertAccountTypeKey(customerType));
        Result<AccountBookDO> result = accountCenterService.queryAccount(query);
        if (result != null && result.isSuccess() && result.getData() != null) {
            AccountBookDO accountBookDO = result.getData();
            //账户未冻结时，检查有无绑卡记录
            if (!accountBookDO.isOptionFreeze()) {
                BindCardQueryReq queryReq = new BindCardQueryReq();
                queryReq.setCustomerCode(customerType + customerCode);
                queryReq.setStatus(BankCardStatusConstants.BINDACTIVE);
                ResultList<PinganCardVO> resultList = merchantQueryServiceApi.selectBindCardByStatus(queryReq);
                return resultList != null && resultList.isSuccess() && resultList.getDatalist().size() > 0;
            }else {
                Logger.info("queryAccountBindStatus customerCode:{} is freezing! ",customerCode);
            }
        }else {
            Logger.info("queryAccountBindStatus queryAccount result:{}",DataMask.toJSONString(result));
        }
        return false;
    }

    @Override
    public Boolean queryAccountLockStatus(String customerCode, String customerType) {
        AccountQuery query = new AccountQuery();
        query.setCustomerCode(customerCode);
        query.setAccountTypeKey(convertAccountTypeKey(customerType));
        Logger.info("queryAccountLockStatus query :{}",DataMask.toJSONString(query));
        Result<AccountBookDO> result = accountCenterService.queryAccount(query);
        Logger.info("queryAccountLockStatus result :{}",DataMask.toJSONString(result));
        if (result != null && result.isSuccess() && result.getData() != null) {
            AccountBookDO accountBookDO = result.getData();
            if (!accountBookDO.isOptionFreeze()) {
                return true;
            }else {
                Logger.info("queryAccountBindStatus customerCode:{} is freezing! ",customerCode);
            }
        }
        return false;
    }

    /**
     * 判断有无绑卡记录，查询失败返回null
     */
    @Override
    public Boolean hasBindRecord(String customerCode, String customerType) {
        BindCardQueryReq req = new BindCardQueryReq();
        req.setCustomerCode(AccountUtils.getSellerCode(customerCode, customerType));
        req.setCustomerType((CustomerNatureType) CustomerNatureType.getEnum(CustomerNatureType.class, customerType));
        try {
            com.akucun.fps.common.entity.Result<Integer> result = merchantQueryServiceApi.isHasBindSuccRecord(req);
            if (Objects.nonNull(result) && result.isSuccess()) {
                if(result.getData() > 0) {
                    return true;
                } else {
                    Logger.info("customerCode:{} has no bind record!", customerCode);
                    return false;
                }
            } else {
                return null;
            }
        } catch (Exception e) {
            Logger.error("hasBindRecord error", e);
            return null;
        }
    }

    @Override
    public Boolean hasPinganAccount(String customerCode, String customerType) {
        com.aikucun.common2.base.Result<PinganAccount> pinganResult = merchantClient.selectPinganAccount(customerCode, customerType);
        if(!pinganResult.getSuccess()) {
            Logger.error("AccountService.hasPinganAccount, 查询平安账户失败，{}-{}", customerType, customerCode);
            return null;
        }
        if(pinganResult.getData() == null) {
            return false;
        } else {
            return true;
        }
    }

    @Override
    public boolean isTenantCustomer(String customerCode, String customerType) {
        LambdaQueryWrapper<AccountTenantCustomer> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AccountTenantCustomer::getCustomerCode, AccountUtils.getSellerCode(customerCode, customerType))
                .eq(AccountTenantCustomer::getCustomerType, customerType);
        AccountTenantCustomer accountTenantCustomer = accountTenantCustomerMapper.selectOne(wrapper);
        return accountTenantCustomer != null;
    }

    @Override
    public boolean hasProcessingTrade(String customerCode, String customerType) {
        //转换客户编码（提现记录和锁定账户用到的店主编码是带NM的，但accountOpTrade中店主编码不带NM）
        String customerCode2 = AccountUtils.getSellerCode(customerCode, customerType);

        LambdaQueryWrapper<WithdrawApplyRecord> wrapperFail = new LambdaQueryWrapper<>();
        wrapperFail.eq(WithdrawApplyRecord::getCustomerCode, customerCode2)
                .eq(WithdrawApplyRecord::getCustomerType, customerType)
                .gt(WithdrawApplyRecord::getCreateTime, Date.from(LocalDateTime.now().plusMinutes(withdrawCheckTimeCreate).atZone(ZoneId.systemDefault()).toInstant()))
                .gt(WithdrawApplyRecord::getUpdateTime, Date.from(LocalDateTime.now().plusSeconds(withdrawCheckTimeFail).atZone(ZoneId.systemDefault()).toInstant()))
                .eq(WithdrawApplyRecord::getApplyStatus, ApplyStatus.FAIL.getName())
                .last(" limit 1");
        WithdrawApplyRecord recordFail = withdrawApplyRecordMapper.selectOne(wrapperFail);
        if(recordFail != null){//存在10秒内的提现失败记录（等待提现失败后完成冲正，否则锁定账户会导致冲正异常）
            Logger.info("AccountUpgradeJob customerCode:{}, withdraw fail record:{}",
                    customerCode2, recordFail.getWithdrawNo());
            return true;
        }

        LambdaQueryWrapper<WithdrawApplyRecord> wrapperProcess = new LambdaQueryWrapper<>();
        wrapperProcess.eq(WithdrawApplyRecord::getCustomerCode, customerCode2)
                .eq(WithdrawApplyRecord::getCustomerType, customerType)
                .gt(WithdrawApplyRecord::getCreateTime, Date.from(LocalDateTime.now().plusMinutes(withdrawCheckTimeCreate).atZone(ZoneId.systemDefault()).toInstant()))
                .gt(WithdrawApplyRecord::getUpdateTime, Date.from(LocalDateTime.now().plusMinutes(withdrawCheckTimeProcess).atZone(ZoneId.systemDefault()).toInstant()))
                .eq(WithdrawApplyRecord::getApplyStatus, ApplyStatus.DOING.getName())
                .last(" limit 1");
        WithdrawApplyRecord recordProcess = withdrawApplyRecordMapper.selectOne(wrapperProcess);
        if(recordProcess != null){//存在30分钟内的提现处理中记录（等待提现处理完成，否则锁定账户会导致提现异常）
            Logger.info("AccountUpgradeJob customerCode:{}, withdraw process record:{}",
                    customerCode2, recordProcess.getWithdrawNo());
            return true;
        }
        return false;
    }

    @Override
    public String convertAccountTypeKey(String customerType) {
        if (CustomerType.NM.getName().equals(customerType)) {
            return AccountKeyConstants.NM.getName();
        } else if (CustomerType.NMDL.getName().equals(customerType)) {
            return AccountKeyConstants.NMDL.getName();
        }else if(CustomerType.DG.getName().equals(customerType)){
            return AccountKeyConstants.DG.getName();
        }else if(CustomerType.DCC.getName().equals(customerType)){
            return AccountKeyConstants.DCC.getName();
        }else if (CustomerType.AT.getName().equals(customerType)){
            return AccountKeyConstants.AT.getName();
        }else if (CustomerType.TB.getName().equals(customerType)){
            return AccountKeyConstants.TB.getName();
        }else if (CustomerType.QDS.getName().equals(customerType)){
            return AccountKeyConstants.QDS.getName();
        }else if (CustomerType.DXQDS.getName().equals(customerType)){
            return AccountKeyConstants.DXQDS.getName();
        }
        return null;
    }

    @Override
    public void monitor(TradeInfo tradeInfo, Result<Void> result) {
        try {
            String remark = "账户监控";
            Boolean balanceNotEnough = apolloConfigCenter.getBalanceNotEnoughCodeMap().get(Integer.toString(result.getCode()));
            if (null != balanceNotEnough && balanceNotEnough) {
                remark = AccountMonitorTask.BalanceNotEnoughRemark;
            }
            // 任务
            PostActionItemBO itemBO = PostActionItemBO.builder()
                    .bizId(tradeInfo.getTradeNo())
                    .paramObject(tradeInfo)
                    .remark(remark)
                    .actionType(PostActionTypes.ACCOUNT_MONITOR.getName())
                    .status(PostActionExecStatus.EXECUTE.value())
                    .nextRetryTime(calTime())
                    .retryNums(0)
                    .build();
            postActionService.addAction(itemBO);
        } catch (Exception e) {
            Logger.error("账户监听处理异常:{}", e.getMessage(), e);
        }
    }

    public static void main(String[] args) {
        // calTime();
    }

    // 计算白天接收短信时间
    public LocalDateTime calTime() {
        // 获取当前时间
        LocalTime now = LocalTime.now();
        LocalDateTime t = LocalDateTime.now();
        LocalDateTime result = t;

        if (smsTimeControl) {
            // 判断当前时间是否小于9点
            if (now.isBefore(LocalTime.of(9, 0))) {
                result = t.withHour(9)
                        .withMinute(30)
                        .withSecond(0)
                        .withNano(0);
            } else if (now.isAfter(LocalTime.of(17, 0))) {
                result = t.plusDays(1)
                        .withHour(9)
                        .withMinute(0)
                        .withSecond(0)
                        .withNano(0);
            } else {
                result = t.withHour(17)
                        .withMinute(30)
                        .withSecond(0)
                        .withNano(0);
            }
        }

        Logger.info("账户监听，时间:{}", result);
        return result;
    }

    @Override
    public Result<Void> dealTrade(TradeInfo tradeInfo) {
        if (dgDecouple && tradeInfo.getAccountTypeKey().equals(AccountKeyConstants.DG.getName()) && tradeInfo.getTradeType().equalsIgnoreCase("TRADE_TYPE_183")) {
            return requestClearingCore(tradeInfo);
        }
        if (accountBonusSwitch && tradeInfo.getAccountTypeKey().equals(AccountKeyConstants.AWARD.getName())) {
            accountMemberClient.bonusAccountTrade(tradeInfo);
        }
        Result<Void> result = accountCenterService.dealTrade(tradeInfo);
        //TODO 明确要调用过老奖励金系统的，如果跑账户中心报错，如果无法冲正或重试补偿，则需要重点告警监控，否则两边数据肯定不一致

        if (null != apolloConfigCenter.getAccountMonitorMap()) {
            Boolean tmp = apolloConfigCenter.getAccountMonitorMap().get(tradeInfo.getAccountTypeKey() + "_" + tradeInfo.getTradeType());
            if(!ObjectUtils.isEmpty(tmp) && tmp) {
                this.monitor(tradeInfo, result);
            }
        }
        return result;
    }

    public Result<Void> requestClearingCore(TradeInfo tradeInfo) {
        Result<Void> result = Result.success();
        //调用平安查询绑卡信息
        com.akucun.fps.common.entity.Query<PinganCardQueryDO> var1 = new com.akucun.fps.common.entity.Query<>();
        PinganCardQueryDO pinganCardQueryDO = new PinganCardQueryDO();
        pinganCardQueryDO.setCustomerCode(tradeInfo.getCustomerCode());
        var1.setData(pinganCardQueryDO);
        com.akucun.fps.common.entity.ResultList<PinganCardVO> pinganCardList = settlementServiceApi.selectCardByCustomerCodePage(var1);
        //校验绑卡信息是否为空
        if(null == pinganCardList || null == pinganCardList.getDatalist() || CollectionUtils.isEmpty(pinganCardList.getDatalist())){
            result = Result.error(ErrorCodeConstants.ACCORE_101519.getErrorCode(), ErrorCodeConstants.ACCORE_101519.getErrorMessage());
            return result;
        }
        List<PinganCardVO> datalist = (List<PinganCardVO>) pinganCardList.getDatalist();
        PinganCardVO pinganCardVO = datalist.get(0);

        com.akucun.account.proxy.task.request.FinTaskAcceptRequest finTaskAcceptRequest = new com.akucun.account.proxy.task.request.FinTaskAcceptRequest();
        finTaskAcceptRequest.setRequestNo(UUID.randomUUID().toString());
        finTaskAcceptRequest.setRequestPlatform("ACCOUNT");
        finTaskAcceptRequest.setCreateUserId(tradeInfo.getCustomerCode());
        finTaskAcceptRequest.setCreateUserType(com.akucun.account.proxy.common.constant.CustomerType.DG.getCname());
        finTaskAcceptRequest.setBizCategory("PINGAN_WITHDRAW");
        String withdrawNo = tradeInfo.getTradeNo();
        finTaskAcceptRequest.setBizNo(withdrawNo);
        finTaskAcceptRequest.setSource("account-proxy");
        Map<String, String> paramMap = new HashedMap();
        paramMap.put("customerCode", tradeInfo.getCustomerCode());
        paramMap.put("customerType", com.akucun.account.proxy.common.constant.CustomerType.DG.name());
        paramMap.put("customerName", pinganCardVO.getCustomerName());
        paramMap.put("withdrawNo", withdrawNo);
        paramMap.put("amount", tradeInfo.getAmount().toString());
        paramMap.put("bankNo", pinganCardVO.getBankCardCode());
        paramMap.put("tranFee", BigDecimal.ZERO.toPlainString());
        paramMap.put("remark", tradeInfo.getRemark());
        finTaskAcceptRequest.setBizInfo(paramMap);
        Logger.info("平安渠道提现请求：{}，withdrawNo：{}", JSON.toJSONString(finTaskAcceptRequest), withdrawNo);
        com.aikucun.common2.base.Result<Void> finTaskVOResult = finClearingCoreFacadeHelp.accept(finTaskAcceptRequest);
        Logger.info("平安渠道提现响应：{}，withdrawNo：{}", JSON.toJSONString(finTaskVOResult), withdrawNo);
        if(!finTaskVOResult.getSuccess()) {
            result.setSuccess(finTaskVOResult.getSuccess());
            result.setMessage(finTaskVOResult.getMessage());
        }
        return result;
    }
}
