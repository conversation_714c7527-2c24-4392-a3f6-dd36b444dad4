package com.akucun.account.proxy.service.oa.handler;

import com.aikucun.common2.base.Result;
import com.aikucun.common2.base.exception.IErrorCode;
import com.aikucun.common2.log.Logger;
import com.akucun.account.proxy.common.utils.RestTemplateUtils;
import com.akucun.account.proxy.dao.model.PostActionItem;
import com.akucun.account.proxy.facade.stub.enums.OAWorkflowBusinessTypeEnum;
import com.akucun.account.proxy.facade.stub.others.oa.req.OAWorkflowStatusNotifyRequest;
import com.akucun.account.proxy.facade.stub.others.oa.resp.OAWorkflowResponseInfo;
import com.akucun.account.proxy.service.common.SpringContextHolder;
import com.akucun.account.proxy.service.oa.bo.OAWorkflowStatusPollingBo;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.ParameterizedTypeReference;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Create on : 2025/1/15 10:40
 **/
public class MentorBonusOAWorkflowPollingHandler extends AbstractOAWorkflowPollingHandler {

    public MentorBonusOAWorkflowPollingHandler(PostActionItem postActionItem, OAWorkflowStatusPollingBo pollingBo) {
        super(postActionItem, pollingBo);
    }

    @Override
    protected Result<Void> doHandle(OAWorkflowResponseInfo responseInfo) {
        OAWorkflowStatusNotifyRequest notifyRequest = null;
        //状态不一致则需要进行回调通知
        OAWorkflowResponseInfo lastOAWorkflowResponseInfo = pollingBo.getLastOAWorkflowResponseInfo();
        if (lastOAWorkflowResponseInfo == null || !StringUtils.equals(lastOAWorkflowResponseInfo.getStatus(), responseInfo.getStatus())) {
            notifyRequest = new OAWorkflowStatusNotifyRequest();
            notifyRequest.setWorkflowNo(responseInfo.getWorkflowNo());
            notifyRequest.setRequestId(responseInfo.getRequestId());
            notifyRequest.setStatus(responseInfo.getStatus());
            notifyRequest.setStatusDesc(responseInfo.getStatus());
            notifyRequest.setBizNo(pollingBo.getBizNo());
            if (notifyRequest.isCanceled()) {
                notifyRequest.setErrorMsg("流程作废，需要重新提交");
            }

            //回调内部系统方法
            Logger.info("OA流程回调通知上游系统，请求参数：{}", JSONObject.toJSONString(notifyRequest));
            com.akucun.common.Result<Boolean> notifyResult = SpringContextHolder.getApplicationContext().getBean(RestTemplateUtils.class).doPost(
                    pollingBo.getNotifyUrl(),
                    notifyRequest,
                    new ParameterizedTypeReference<com.akucun.common.Result<Boolean>>() {
                    }, null);
            Logger.info("OA流程回调通知上游系统，返回结果：{}，bizNo：{}", JSONObject.toJSONString(notifyResult), notifyRequest.getBizNo());
            if (!notifyResult.isSuccess() || !BooleanUtils.isTrue(notifyResult.getData())) {
                return Result.error(IErrorCode.SYSTEM_ERROR, "OA流程回调上游系统失败");
            }

            //将最新状态更新到异步任务参数上
            pollingBo.setLastOAWorkflowResponseInfo(responseInfo);
            postActionItem.setParam(JSONObject.toJSONString(pollingBo));
        }
        if (ObjectUtils.isNotEmpty(notifyRequest)) {
            if (notifyRequest.isFinished() || notifyRequest.isCanceled()) {
                return Result.success();
            }
        }
        return Result.error(IErrorCode.SYSTEM_ERROR, "OA流程非终态，待继续重试");
    }

    @Override
    protected String getBusinessType() {
        return OAWorkflowBusinessTypeEnum.MENTOR_BONUS.name();
    }

}
