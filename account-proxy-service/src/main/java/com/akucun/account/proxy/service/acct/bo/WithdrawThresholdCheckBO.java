package com.akucun.account.proxy.service.acct.bo;

import java.io.Serializable;

/**
 * 提现阈值判断
 *
 * <AUTHOR>
 * @version [版本号, 2020年9月30日]
 */
public class WithdrawThresholdCheckBO implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = -1608002332081266563L;

    private Boolean success;

    /**
     * 101524-本月累计提现达到阈值需升级到临时个体
     * 101525-本月累计提现达到阈值需升级到个体工商户
     * 101526-本月累计提现达到阈值需升级到企业
     */
    private Integer code;

    private String tip;

    /**
     * 是否允许微信提现，个体工商户和企业不允许提现到微信
     */
    private Boolean wechatWithdrawFlag;

    private String wechatWithdrawTip;

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getTip() {
        return tip;
    }

    public void setTip(String tip) {
        this.tip = tip;
    }

    public Boolean getWechatWithdrawFlag() {
        return wechatWithdrawFlag;
    }

    public void setWechatWithdrawFlag(Boolean wechatWithdrawFlag) {
        this.wechatWithdrawFlag = wechatWithdrawFlag;
    }

    public String getWechatWithdrawTip() {
        return wechatWithdrawTip;
    }

    public void setWechatWithdrawTip(String wechatWithdrawTip) {
        this.wechatWithdrawTip = wechatWithdrawTip;
    }

    @Override
    public String toString() {
        return "WithdrawThresholdCheckBO{" +
                "success=" + success +
                ", code=" + code +
                ", tip='" + tip + '\'' +
                ", wechatWithdrawFlag=" + wechatWithdrawFlag +
                ", wechatWithdrawTip='" + wechatWithdrawTip + '\'' +
                '}';
    }
}
