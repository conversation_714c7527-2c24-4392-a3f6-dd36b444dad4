package com.akucun.account.proxy.service.transfer.impl;

import com.akucun.account.proxy.client.transfer.WechatTransferClient;
import com.akucun.account.proxy.client.transfer.bo.WxTransferRespBO;
import com.akucun.account.proxy.common.enums.ResponseEnum;
import com.akucun.account.proxy.dao.model.PaymentTransfer;
import com.akucun.account.proxy.facade.stub.enums.TransferStatusEnum;
import com.akucun.account.proxy.facade.stub.enums.TransferTypeEnum;
import com.akucun.account.proxy.service.transfer.bo.PaymentTransferBO;
import com.akucun.account.proxy.service.transfer.bo.TransferGatewayBO;
import com.akucun.account.proxy.service.transfer.factory.TransferService;
import com.akucun.pay.gateway.wxv2.facade.stub.dto.req.WxTransferQueryReq;
import com.akucun.pay.gateway.wxv2.facade.stub.dto.req.WxTransferReq;
import com.mengxiang.base.common.log.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 * @date 2021/01/28 19:46
 */
@Service
public class WxTransferServiceImpl implements TransferService {

    @Autowired
    private WechatTransferClient wechatTransferClient;


    @Override
    public PaymentTransferBO transfer(PaymentTransfer transfer, TransferGatewayBO transferGatewayBO) {
        PaymentTransferBO result = new PaymentTransferBO();
        result.setSuccess(false);
        try {
            WxTransferReq transferReq = new WxTransferReq();
            BeanUtils.copyProperties(transferGatewayBO, transferReq);
            transferReq.setPartnerTradeNo(transfer.getTradeNo());
            transferReq.setOpenid(transfer.getOpenId());
            transferReq.setCheckName(transfer.getCheckName());
            transferReq.setReUserName(transfer.getCustomerName());
            transferReq.setAmount(transfer.getAmount().toString());
            transferReq.setDesc(transfer.getRemark());
            transferReq.setSpbillCreateIp(transfer.getTerminalIp());
            WxTransferRespBO wxTransferResp = wechatTransferClient.transfer(transferReq);
            BeanUtils.copyProperties(wxTransferResp, result);
            // 付款查询时状态以已网关返回的status为准
            if(TransferStatusEnum.S.getCode().equals(result.getStatus())) {
                result.setSuccess(true);
            } else {
                result.setSuccess(false);
            }
        } catch (Exception e) {
            Logger.error("调用微信网关付款异常，请求参数：{}，", transfer.getTradeNo(), e);
            result.setGatewayMsg(ResponseEnum.TRANSFER_ERROR.getMessage());
        }
        return result;
    }

    @Override
    public PaymentTransferBO transferQuery(String tradeNo, TransferGatewayBO transferGatewayBO) {
        PaymentTransferBO result = new PaymentTransferBO();
        result.setSuccess(false);
        try {
            WxTransferQueryReq transferReq = new WxTransferQueryReq();
            BeanUtils.copyProperties(transferGatewayBO, transferReq);
            transferReq.setTradeNo(tradeNo);
            WxTransferRespBO wxTransferRespBO = wechatTransferClient.queryTransfer(transferReq);
            BeanUtils.copyProperties(wxTransferRespBO, result);
            // 付款查询时状态以已网关返回的status为准
            result.setSuccess(TransferStatusEnum.S.getCode().equals(result.getStatus()));
        } catch (Exception e) {
            Logger.warn("调用微信网关付款查询异常，请求参数：{}，", tradeNo, e);
            result.setGatewayMsg(ResponseEnum.TRANSFER_ERROR.getMessage());
        }
        return result;
    }

    @Override
    public boolean isSupport(String transferType) {
        if(TransferTypeEnum.WECHAT.getCode().equals(transferType)) {
            return true;
        } else {
            return false;
        }
    }
}
