package com.akucun.account.proxy.service.transfer;

import com.akucun.account.proxy.dao.model.TransferChannelGateway;
import com.akucun.account.proxy.facade.stub.others.dto.req.TransferChannelGatewayReq;
import com.akucun.account.proxy.service.transfer.bo.TransferGatewayBO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 付款渠道网关关系表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-26
 */
public interface TransferChannelGatewayService extends IService<TransferChannelGateway> {

    TransferGatewayBO queryGatewayByChannelCode(String channelCode, String tenantId,String appId);

    void removeRedisCache(String channelCode);

    void saveUpdate(TransferChannelGatewayReq transferChannelGatewayReq);

    TransferGatewayBO queryTransferGateway(String channelCode, String tenantId);
}
