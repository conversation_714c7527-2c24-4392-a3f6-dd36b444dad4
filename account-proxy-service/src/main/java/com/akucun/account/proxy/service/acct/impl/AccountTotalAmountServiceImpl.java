package com.akucun.account.proxy.service.acct.impl;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.dao.mapper.AccountTotalAmountMapper;
import com.akucun.account.proxy.dao.model.AccountTotalAmount;
import com.akucun.account.proxy.facade.stub.enums.CustomerType;
import com.akucun.account.proxy.facade.stub.others.account.vo.AccountTotalVO;
import com.akucun.account.proxy.service.acct.AccountTotalAmountService;
import com.akucun.fps.account.client.api.AccountServiceClient;
import com.akucun.fps.account.client.model.AccountTotalDO;
import com.alibaba.dubbo.config.annotation.Reference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * @Author: silei
 * @Date: 2021/3/15
 * @desc:
 */
@Service
public class AccountTotalAmountServiceImpl extends ServiceImpl<AccountTotalAmountMapper, AccountTotalAmount> implements AccountTotalAmountService {

    @Reference(check = false)
    private AccountServiceClient accountServiceClient;

    @Override
    public Result<AccountTotalVO> queryAccountTotalAmount(AccountTotalVO accountTotalVO) {
        LambdaQueryWrapper<AccountTotalAmount> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AccountTotalAmount::getCustomerCode,accountTotalVO.getCustomerCode())
                .eq(AccountTotalAmount::getCustomerType,accountTotalVO.getCustomerType());

        AccountTotalAmount accountTotalAmount = this.baseMapper.selectOne(wrapper);
        if (accountTotalAmount == null){
            AccountTotalDO accountTotalDO = new AccountTotalDO();
            accountTotalDO.setAccountCode(accountTotalVO.getCustomerCode());
            accountTotalDO.setCustomerType(accountTotalVO.getCustomerType());
            com.akucun.fps.common.entity.Result<AccountTotalDO> result = accountServiceClient.selectAccountTotal(accountTotalDO);
            if (result.isSuccess() && result.getData()!=null){
                BeanUtils.copyProperties(result.getData(),accountTotalVO);
                accountTotalVO.setCustomerCode(accountTotalVO.getCustomerCode());
            }
        }else {
            BeanUtils.copyProperties(accountTotalAmount, accountTotalVO);
        }
        return Result.success(accountTotalVO);
    }
}
