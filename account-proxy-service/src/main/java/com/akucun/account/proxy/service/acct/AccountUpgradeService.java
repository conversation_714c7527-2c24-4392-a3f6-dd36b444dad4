package com.akucun.account.proxy.service.acct;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.facade.stub.others.dto.req.AccountUpgradePreCheckRequest;
import com.akucun.account.proxy.facade.stub.others.dto.req.AccountUpgradeRequest;
import com.akucun.account.proxy.facade.stub.others.dto.res.AccountUpgradeResp;
import com.akucun.account.proxy.service.acct.bo.AccountUpgradeBO;

import java.util.List;

/**
 * @Author: RJF
 * @desc:
 */
public interface AccountUpgradeService {

    Result<Void> upgradePreCheck(AccountUpgradePreCheckRequest request);

    Result<Void> accountUpgrade(AccountUpgradeBO accountUpgradeBO);

    Result<List<AccountUpgradeResp>> queryUpgradeStatus(List<AccountUpgradeRequest> list);

    boolean hasRecentUpgradeProcessing(String customerCode, String customerType);

}
