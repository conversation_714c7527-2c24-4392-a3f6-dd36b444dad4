package com.akucun.account.proxy.service.postaction.task;

import java.time.LocalDateTime;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.common.constant.WithdrawChannelConstants;
import com.akucun.account.proxy.common.enums.PostActionExecStatus;
import com.akucun.account.proxy.common.enums.PostActionTypes;
import com.akucun.account.proxy.dao.model.PostActionItem;
import com.akucun.account.proxy.service.common.SpringContextHolder;
import com.akucun.account.proxy.service.postaction.PostActionService;
import com.akucun.account.proxy.service.postaction.bo.PostActionItemBO;
import com.akucun.account.proxy.service.postaction.common.AbsPostActionExecutor;
import com.akucun.account.proxy.service.receipt.AbstractWithdrawReceiptDownloadHandler;
import com.akucun.account.proxy.service.receipt.bo.WithdrawReceiptDownloadBO;
import com.akucun.fps.common.util.GsonUtils;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.mengxiang.base.common.log.Logger;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;

import lombok.Data;

@Component
public class WithdrawReceiptDownloadTask extends AbsPostActionExecutor {

	@ApolloJsonValue("${withdraw.receipt.download.retry.config:{}}")
	private TaskRetryConfig taskRetryConfig;

	@Value("#{'${wechat.withdraw.receipt.download.support.customer.types:NM,NMDL}'.split(',')}")
	private List<String> wechatWithdrawReceiptDownloadSupportCustomerTypes;

	@Value("#{'${pingan.withdraw.receipt.download.support.customer.types:SH}'.split(',')}")
	private List<String> pingAnWithdrawReceiptDownloadSupportCustomerTypes;

	@Value("${wechat.withdraw.receipt.download.delay.hours:24}")
	private Long wechatWithdrawReceiptDownloadDelayHours;

	@Value("${pingan.withdraw.receipt.download.delay.hours:24}")
	private Long pingAnWithdrawReceiptDownloadDelayHours;

	@Autowired
    private PostActionService postActionService;

	@Override
	protected String getActionType() {
		return PostActionTypes.WITHDRAW_RECEIPT_DOWNLOAD.getName();
	}

	/**
	 * 最大重试次数，超过则不再重试
	 * @return
	 */
	@Override
	public int getMaxRetryTimes() {
		return taskRetryConfig.getMaxRetryTimes();
	}

	/**
	 * 计算下一次执行时间，按小时梯次执行
	 * @param item
	 * @return
	 */
	@Override
	public LocalDateTime calculateNextExecuteTime(PostActionItem item) {
		return LocalDateTime.now().plusMinutes(taskRetryConfig.getIntervalMinute());
	}

	/**
	 * 默认捞30天内创建的，超过则不再执行
	 * @return
	 */
	@Override
	public int getDayOff() {
		return taskRetryConfig.getDayOff();
	}

	@XxlJob("WithdrawReceiptDownloadTask")
	public ReturnT<String> execute(String param) {
		if (StringUtils.isNotBlank(param)) {
            String[] arr = param.split(",");
            long start = Long.parseLong(arr[0]);
            long end = Long.parseLong(arr[1]);
            int retryTimes = Integer.parseInt(arr[2]);
            List<PostActionItem> list = postActionService.selectByIndex(start, end, retryTimes, getActionType());
            list.forEach(t -> postActionService.processAction(t));
			return ReturnT.SUCCESS;
		} else {
			return this.executeEntranceLtNextRetryTime();
		}

    }

	@Override
	public Result<Void> execute(PostActionItem item) {
		Result<Void> resp = Result.success();
		try {
			WithdrawReceiptDownloadBO bo = GsonUtils.getInstance().fromJson(item.getParam(), WithdrawReceiptDownloadBO.class);
			// 下载提现回单
			resp = AbstractWithdrawReceiptDownloadHandler.getHandler(bo).handle();

			// 如果下载失败, 则重新设置参数, 调用过程中有修改bo对象中的属性值, 比如微信回单下载
			if (!resp.getSuccess()) {
				item.setParam(GsonUtils.getInstance().toJson(bo));
			}

			//设置失败原因
			item.setErrorLog(resp.getMessage());
		}catch (Exception e) {
			resp.setSuccess(false);
			resp.setMessage(e.getMessage());
			Logger.error("提现回单下载任务执行异常bizId：{}", item.getBizId(), e);
		}
		 return resp;
	}

	@Data
	public static class TaskRetryConfig {

		// 最大重试次数，超过则不再重试, 7天*24小时*60分钟/60分钟=168次
		private Integer maxRetryTimes = 168;

		// 默认捞7天内的，超过则不再执行
		private Integer dayOff = -7;

		// 重试间隔时间，单位：分钟
		private Integer intervalMinute = 60;

	}

	/**
	 * 初始化提现回执单下载任务
	 * @param withdrawChannel
	 * @param withdrawNo
	 * @param batchNo
	 * @param merchantCode
	 * @param isTenantCustomer
	 */
	public void init(String withdrawChannel, String withdrawNo, String batchNo, String merchantCode,
					 Boolean isTenantCustomer, String customerType) {
		String remark = null;
		LocalDateTime nextRetryTime = null;
		if (StringUtils.equals(withdrawChannel, WithdrawChannelConstants.WECHAT.getName())) {
			if (!wechatWithdrawReceiptDownloadSupportCustomerTypes.contains(customerType)) {
				Logger.info("暂不支持该类型用户下载微信提现回单，withdrawNo：{}，withdrawChannel：{}，customerType：{}", withdrawNo, withdrawChannel, customerType);
				return;
			}
			nextRetryTime = LocalDateTime.now().plusHours(wechatWithdrawReceiptDownloadDelayHours);
			remark = "微信提现回执单下载";
		} else if (StringUtils.equals(withdrawChannel, WithdrawChannelConstants.PINGAN.getName())) {
			if (!pingAnWithdrawReceiptDownloadSupportCustomerTypes.contains(customerType)) {
				Logger.info("暂不支持该类型用户下载平安提现回单，withdrawNo：{}，withdrawChannel：{}，customerType：{}", withdrawNo, withdrawChannel, customerType);
				return;
			}
			nextRetryTime = LocalDateTime.now().plusHours(pingAnWithdrawReceiptDownloadDelayHours);
			remark = "平安提现回执单下载";
		} else {
			Logger.warn("暂不支持未知提现渠道提现回执单下载，withdrawNo：{}，withdrawChannel：{}", withdrawNo, withdrawChannel);
			return;
		}

		WithdrawReceiptDownloadBO bo = WithdrawReceiptDownloadBO.builder()
                .withdrawNo(withdrawNo)
                .withdrawChannel(withdrawChannel)
                .isTenantCustomer(isTenantCustomer)
                .merchantCode(merchantCode)
                .batchNo(batchNo)
				.customerType(customerType)
				.build();
        PostActionItemBO itemBO = PostActionItemBO.builder()
                .bizId(withdrawNo)
                .paramObject(bo)
                .remark(remark)
                .actionType(PostActionTypes.WITHDRAW_RECEIPT_DOWNLOAD.getName())
                .status(PostActionExecStatus.EXECUTE.value())
				.nextRetryTime(nextRetryTime)
				.build();
        SpringContextHolder.getBean(PostActionService.class).addAction(itemBO);
	}

	public static void main(String[] args) {
		System.out.println(LocalDateTime.now().plusDays(1L));
	}
}