package com.akucun.account.proxy.service.acct;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.facade.stub.others.account.vo.WechatInfoBindVO;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @Author: silei
 * @Date: 2021/5/2
 * @desc:
 */
public interface WechatAuthService {

    /**
     * 微信信息绑定
     *
     * @param vo
     * @return
     */
    Result<Void> bindInfo(@RequestBody WechatInfoBindVO vo);

    /**
     * 微信信息解绑
     *
     * @param customerCode
     * @param customerType
     * @return
     */
    Result<Void> unbind(String customerCode, String customerType);

    /**
     * 微信绑定信息查询
     *
     * @param customerCode
     * @param customerType
     * @return
     */
    Result<WechatInfoBindVO> queryBindInfo(String customerCode, String customerType);
}
