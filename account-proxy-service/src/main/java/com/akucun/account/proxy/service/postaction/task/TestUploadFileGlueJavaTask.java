package com.akucun.account.proxy.service.postaction.task;

import com.aikucun.common2.base.Result;
import com.aikucun.common2.log.Logger;
import com.akucun.account.proxy.client.file.FileUploadClient;
import com.akucun.account.proxy.service.acct.AccountWithdrawService;
import com.akucun.account.proxy.service.common.SpringContextHolder;
import com.akucun.bcs.channel.facade.stub.dto.WechatWithdrawRequest;
import com.akucun.fps.common.util.GsonUtils;
import com.alibaba.fastjson.JSON;
import com.mengxiang.fileupload.dto.FileInfo;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.log.XxlJobLogger;

import org.apache.commons.lang3.StringUtils;

import java.util.UUID;

import javax.annotation.Resource;

/**
 * @Description
 * @aurhor lee
 * @date: 2022/9/13 14:34
 */
public class TestUploadFileGlueJavaTask extends IJobHandler {

    @Resource
    private AccountWithdrawService accountWithdrawService;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        Logger.info("FixMerchantWechatWithdrawQueryGlueJavaTask执行参数：{}", param);
        if (StringUtils.isBlank(param)) {
            Logger.info("FixMerchantWechatWithdrawQueryGlueJavaTask执行异常：参数不能为空");
            return ReturnT.SUCCESS;
        }
        
            // 下载提现回单
            FileUploadClient fileUploadClient = SpringContextHolder.getBean(FileUploadClient.class);
            byte[] fileBytes = fileUploadClient.getHttpFileBytes(param);

            // 将base64字符串转换为pdf字节数组, 通过字节数组上传至OBS
            //byte[] fileBytes = Base64.getDecoder().decode(receiptBase64String);

            // 生成文件名
            String fileName = String.format("%s.pdf", UUID.randomUUID().toString());
            // 上传OBS
            Result<FileInfo> uploadFileResult = SpringContextHolder.getBean(FileUploadClient.class).upload(fileBytes, fileName, fileName, FileUploadClient.WECHAT_WITHDRAW_RECEIPT_PARENT_DIR);
            if (!uploadFileResult.getSuccess()) {
               return new ReturnT<>(ReturnT.FAIL_CODE, "上传OBS失败: " + uploadFileResult.getMessage());
            }
            XxlJobLogger.log("上传OBS成功: " + JSON.toJSONString(uploadFileResult));
        return ReturnT.SUCCESS;
    }

}
