package com.akucun.account.proxy.service.common.impl;

import com.akucun.account.proxy.dao.mapper.AccountStepControlMapper;
import com.akucun.account.proxy.dao.model.AccountStepControl;
import com.akucun.account.proxy.service.common.AccountStepControlService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: silei
 * @Date: 2020/8/26
 * @desc:
 */
@Service
public class AccountStepControlServiceImpl extends ServiceImpl<AccountStepControlMapper, AccountStepControl> implements AccountStepControlService {

    @Override
    public List<AccountStepControl> load() {
        return this.baseMapper.selectList(new LambdaQueryWrapper<AccountStepControl>().eq(AccountStepControl::getIsDelete, 0));
    }
}
