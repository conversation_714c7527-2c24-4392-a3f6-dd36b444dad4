package com.akucun.account.proxy.service.tradeflow.context;

import com.akucun.account.proxy.service.tradeflow.domain.Trade;
import com.akucun.account.proxy.service.tradeflow.domain.TradePhase;

import java.util.ArrayList;
import java.util.List;

public class TradeContext {

    private static ThreadLocal<TradeContext> contextHolder = new ThreadLocal<>();

    private Trade trade;

    private List<TradePhase> phases;

    public Trade getTrade() {
        return trade;
    }

    public void setTrade(Trade trade) {
        this.trade = trade;
    }

    public List<TradePhase> getPhases() {
        return phases;
    }

    public void setPhases(List<TradePhase> phases) {
        this.phases = phases;
    }

    public void addPhase(TradePhase phase) {
        if(phases == null) {
            phases = new ArrayList<>();
        }
        phases.add(phase);
    }

    public static TradeContext get() {
        return contextHolder.get();
    }

    public static void init() {
        TradeContext context = new TradeContext();
        contextHolder.set(context);
    }

    public static void clean() {
        contextHolder.remove();
    }

}
