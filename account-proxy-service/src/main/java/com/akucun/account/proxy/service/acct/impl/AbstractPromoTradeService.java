package com.akucun.account.proxy.service.acct.impl;

import com.akucun.account.proxy.client.invoice.InvoiceService;
import com.akucun.account.proxy.common.config.ApolloConfigCenter;
import com.akucun.account.proxy.common.enums.ErrorCodeConstants;
import com.akucun.account.proxy.common.enums.MemberGrade;
import com.akucun.account.proxy.common.enums.PostActionExecStatus;
import com.akucun.account.proxy.common.enums.PostActionTypes;
import com.akucun.account.proxy.dao.model.RewardApply;
import com.akucun.account.proxy.facade.stub.enums.CustomerType;
import com.akucun.account.proxy.facade.stub.enums.RewardTypeEnum;
import com.akucun.account.proxy.facade.stub.others.dto.req.BatchBonusPayReq;
import com.akucun.account.proxy.facade.stub.others.dto.req.BonusPayReq;
import com.akucun.account.proxy.facade.stub.others.dto.req.BonusPaySubDTO;
import com.akucun.account.proxy.facade.stub.others.dto.req.BonusPaySubmitReq;
import com.akucun.account.proxy.facade.stub.others.dto.res.BonusPayResp;
import com.akucun.account.proxy.facade.stub.others.dto.res.OANotifyDTO;
import com.akucun.account.proxy.insurable.PromoNotifyInsurableTask;
import com.akucun.account.proxy.service.acct.PromoTradeHandler;
import com.akucun.account.proxy.service.acct.RewardApplyService;
import com.akucun.account.proxy.service.acct.bo.NotifyPromoBO;
import com.akucun.account.proxy.service.acct.job.NotifyPayRsltTask;
import com.akucun.account.proxy.service.common.SpringContextHolder;
import com.akucun.account.proxy.service.common.TaxService;
import com.akucun.account.proxy.service.enums.PromoTradeStatusEnum;
import com.akucun.account.proxy.service.postaction.PostActionService;
import com.akucun.account.proxy.service.postaction.bo.PostActionItemBO;
import com.akucun.common.DateUtils;
import com.akucun.common.Result;
import com.akucun.member.audit.model.dto.auth.QueryAuthStatusRespDTO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.mengxiang.base.common.log.Logger;
import com.mengxiang.transaction.framework.executor.InsurableTaskExecutor;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

public abstract class AbstractPromoTradeService implements PromoTradeHandler {
    static final String NEED_INVOICE_AUTH_NAME = "enterprise";
    //注意：该类为抽象类，不能直接在成员变量上autoWired注入对象，使用set方法注入
    private RewardApplyService rewardApplyService;
    private PostActionService postActionService;
    private TransactionTemplate transactionTemplate;
    private TaxService taxService;
    private InvoiceService invoiceService;
    private ApolloConfigCenter apolloConfigCenter;

    //============================= 受理任务接口 ===========================

    /**
     * 批量受理
     *
     * @param batchBonusPayReq
     * @return
     */
    @Override
    public Result<BonusPayResp> batchDeal(BatchBonusPayReq batchBonusPayReq) {
        //01-预检查
        Pair<Boolean, String> checkRslt = preCheck(batchBonusPayReq);
        if (ObjectUtils.isEmpty(checkRslt) || !checkRslt.getLeft()) {
            Logger.info("PromoTradeHandler preCheck fail:req={},rslt={}", JSON.toJSONString(batchBonusPayReq), JSON.toJSONString(checkRslt));
            return Result.error(ObjectUtils.isEmpty(checkRslt) ? "系统异常" : checkRslt.getRight());
        }

        //02-数据是否已落地：如果存在已落地数据，而且失败状态，则支持再次发起重复活动的申请
        List<RewardApply> existRecords = rewardApplyService.queryRewardApplyList(null, batchBonusPayReq.getBizType(), batchBonusPayReq.getTransBillDate(), batchBonusPayReq.getActivityNo());
        if (!CollectionUtils.isEmpty(existRecords)) {
            RewardApply rewardApplyRecord = existRecords.stream().filter(record-> !StringUtils.isEmpty(record.getExt1()) && !StringUtils.isEmpty(record.getExt2()) ).findFirst().orElse(null);
            if(PromoTradeStatusEnum.SUBMIT.getCode().equals(rewardApplyRecord.getStatus())){
                return Result.error("当前任务已提交，请勿重复提交");
            }else if(PromoTradeStatusEnum.PAYING.getCode().equals(rewardApplyRecord.getStatus())){
                return Result.error("OA出款中，不支持重新提交");
            }else if(PromoTradeStatusEnum.SUSS.getCode().equals(rewardApplyRecord.getStatus())){
                Logger.warn("当前OA出款任务[{}]已成功({} VS {})",batchBonusPayReq.getActivityNo(),batchBonusPayReq.getTradeNo(),rewardApplyRecord.getRequestNo());
                BonusPayResp bonusPayResp = new BonusPayResp();
                bonusPayResp.setTradeNo(rewardApplyRecord.getRequestNo());
                bonusPayResp.setStatus(PromoTradeStatusEnum.SUSS.getCode());
                bonusPayResp.setStatusDesc(PromoTradeStatusEnum.SUSS.getCodeDesc());
                return Result.success(bonusPayResp);
            }else if(PromoTradeStatusEnum.FAIL.getCode().equals(rewardApplyRecord.getStatus())){
                Logger.warn("当前OA出款任务[{}]已失败(可尝试重试申请)：",batchBonusPayReq.getActivityNo(),JSON.toJSONString(rewardApplyRecord));
                if(batchBonusPayReq.getTradeNo().equals(rewardApplyRecord.getRequestNo())){
                    return Result.error("当前OA出款任务已失败，请尝试使用新的流水号再次发起申请(历史失败流水号:"+rewardApplyRecord.getRequestNo());
                }
            }else if(PromoTradeStatusEnum.INIT.getCode().equals(rewardApplyRecord.getStatus())){
                if(!batchBonusPayReq.getTradeNo().equals(rewardApplyRecord.getRequestNo())){
                    Logger.warn("当前OA出款任务[{}]已申请未提交，但是申请流水号不同：{} VS {}",batchBonusPayReq.getActivityNo(),batchBonusPayReq.getTradeNo(),rewardApplyRecord.getRequestNo());
                }
                BonusPayResp bonusPayResp = new BonusPayResp();
                bonusPayResp.setTradeNo(rewardApplyRecord.getRequestNo());
                bonusPayResp.setStatus(PromoTradeStatusEnum.INIT.getCode());
                bonusPayResp.setStatusDesc(PromoTradeStatusEnum.INIT.getCodeDesc());
                return Result.success(bonusPayResp);
            }
        }

        //03-数据落地
        List<RewardApply> records = buildRewardApplyList(batchBonusPayReq);
        rewardApplyService.batchSave(records);

        //04-构建第一次落地数据的返回结果
        BonusPayResp bonusPayResp = new BonusPayResp();
        bonusPayResp.setTradeNo(batchBonusPayReq.getTradeNo());
        bonusPayResp.setStatus(PromoTradeStatusEnum.INIT.getCode());
        bonusPayResp.setStatusDesc(PromoTradeStatusEnum.INIT.getCodeDesc());
        return Result.success(bonusPayResp);
    }

    /**
     * 单个任务申请处理
     * @param bonusPayReq
     * @return
     */
    @Override
    public Result<BonusPayResp> dealTrade(BonusPayReq bonusPayReq) {
        //01-预检查：个性化业务入参校验(子类实现)
        Pair<Boolean, String> checkRslt = preCheckPayReq(bonusPayReq);
        if (ObjectUtils.isEmpty(checkRslt) || !checkRslt.getLeft()) {
            Logger.info("PromoTradeHandler preCheckPayReq fail:req={},rslt={}", JSON.toJSONString(bonusPayReq), JSON.toJSONString(checkRslt));
            return Result.error(ObjectUtils.isEmpty(checkRslt) ? "系统异常" : checkRslt.getRight());
        }

        //02-店主身份认证：上游没有上送则以结算的查询结果为准
        if(ObjectUtils.isEmpty(bonusPayReq.getUserGrade())) {
            Result<QueryAuthStatusRespDTO> authResult = taxService.queryUserHighestAuth(bonusPayReq.getUserType(), bonusPayReq.getUserCode());
            if (ObjectUtils.isEmpty(authResult) || !authResult.isSuccess() || ObjectUtils.isEmpty(authResult.getData())) {
                if (ObjectUtils.isEmpty(authResult)) {
                    return Result.error("查询用户最高认证状态失败:会员查询结果为空");
                } else if (!authResult.isSuccess()) {
                    return Result.error(StringUtils.isEmpty(authResult.getMessage()) ? "查询用户最高认证状态失败:会员查询失败" : authResult.getMessage());
                } else if (ObjectUtils.isEmpty(authResult.getData())) {
                    return Result.error("查询用户最高认证状态失败:会员查询结果为空");
                }
            }
            QueryAuthStatusRespDTO authBO = authResult.getData();
            bonusPayReq.setUserGrade(authBO.getCurrentHighestAuthType());
            if(!ObjectUtils.isEmpty(authBO.getChannel())) {
                bonusPayReq.setGradeChannel(authBO.getChannel());
            }
        }
        MemberGrade memberGrade =MemberGrade.getByCode(bonusPayReq.getUserGrade());

        //03-幂等校验
        Triple<Boolean,String,RewardApply> idemptentRslt = idempotentCheck(bonusPayReq);
        if(!idemptentRslt.getLeft()){
            return Result.error(idemptentRslt.getMiddle());
        }else if(!ObjectUtils.isEmpty(idemptentRslt.getRight())){
            //03-1 是否支持重复请求
            if(PromoTradeStatusEnum.supportRepeatReq(idemptentRslt.getRight().getStatus())) {
                RewardApply existRecord = idemptentRslt.getRight();
                Pair<Boolean,String> dbOperateRst =  transactionTemplate.execute(new TransactionCallback<Pair<Boolean,String>>() {
                     @Override
                     public Pair<Boolean, String> doInTransaction(TransactionStatus transactionStatus) {
                        try{
                            existRecord.setStatus(PromoTradeStatusEnum.SUBMIT.getCode());
                            rewardApplyService.updatesStatusById(existRecord);

                            PostActionItemBO postActionItemBO = PostActionItemBO.builder()
                                    .actionType(PostActionTypes.PROMO_PAY.getName())
                                    .bizId(existRecord.getRequestNo())
                                    .paramObject(existRecord)
                                    .remark(existRecord.getBusiType()+"-重试出款任务")
                                    .status(PostActionExecStatus.EXECUTE.value())
                                    .build();
                            postActionService.addAction(postActionItemBO);

                            return Pair.of(Boolean.TRUE,"suss");
                        } catch (Exception e) {
                            //回滚事务
                            transactionStatus.setRollbackOnly();
                            Logger.error("PromoTradeHandler-重复发起付款-落地任务发生异常：", e);
                            return Pair.of(Boolean.FALSE,e.getMessage());
                        }
                     }
                 });

                if(dbOperateRst.getLeft()) {
                    return Result.success(buildBonusPayResp(existRecord));
                }else{
                    return Result.error(dbOperateRst.getRight());
                }
            }else{
                return Result.success(buildBonusPayResp(idemptentRslt.getRight()));
            }
        }
        //03-2 首次请求，构建数据entity
        RewardApply rewardApplyEntity = buildRewardApply(bonusPayReq,memberGrade);

        //04-个体工商户/企业认证：开票
        String userAuthType = MemberGrade.getAuthName(memberGrade.getCode());
        if(NEED_INVOICE_AUTH_NAME.equalsIgnoreCase(userAuthType)) {
            //04-1 获取店主的企业主体信息(替换为所有店主使用默认NM555555商户号)
            //04-2 发票系统：生成申请开票记录
            com.aikucun.common2.base.Result<Pair<String,String>> invoiceApplyRslt = invoiceService.invoiceApply4Platform(bonusPayReq);
            if(ObjectUtils.isEmpty(invoiceApplyRslt) || !invoiceApplyRslt.getSuccess()){
                return Result.error(ObjectUtils.isEmpty(invoiceApplyRslt) ? "平台申请店主开票异常" : invoiceApplyRslt.getMessage());
            }
            Pair<String,String> invoiceApplyNo = invoiceApplyRslt.getData();

            //04-3 设置落地状态 和 发票申请单号
            rewardApplyEntity.setStatus(PromoTradeStatusEnum.INVOICE_WAIT_UPLOAD.getCode());
            rewardApplyEntity.setBatchNo(invoiceApplyNo.getRight());
            HashMap<String,String> exts = JSON.parseObject(rewardApplyEntity.getExt2(), new TypeReference<HashMap<String, String>>() {});
            exts.put("awaitingApplyNo",invoiceApplyNo.getLeft());
            rewardApplyEntity.setExt2(JSON.toJSONString(exts));
        }

        //05-数据落地：事务一致性
        Pair<Boolean,String> dbOperateRst =  transactionTemplate.execute(new TransactionCallback<Pair<Boolean,String>>() {
            @Override
            public Pair<Boolean, String> doInTransaction(TransactionStatus transactionStatus) {
                try{
                    //05-1 保存申请记录
                    rewardApplyService.saveRecord(rewardApplyEntity);

                    //05-2 如果不需要收集发票，落地异步的付款任务
                    if(!NEED_INVOICE_AUTH_NAME.equalsIgnoreCase(userAuthType)) {
                        PostActionItemBO postActionItemBO = PostActionItemBO.builder()
                                .actionType(PostActionTypes.PROMO_PAY.getName())
                                .bizId(rewardApplyEntity.getRequestNo())
                                .paramObject(rewardApplyEntity)
                                .remark(rewardApplyEntity.getBusiType()+"-启动出款任务")
                                .status(PostActionExecStatus.EXECUTE.value())
                                .build();
                        postActionService.addAction(postActionItemBO);
                    }
                    return Pair.of(Boolean.TRUE,"suss");
                } catch (Exception e) {
                    //回滚事务
                    transactionStatus.setRollbackOnly();
                    Logger.error("PromoTradeHandler-落地任务发生异常：", e);
                    return Pair.of(Boolean.FALSE,e.getMessage());
                }
            }
        });

        //06-返回结果
        if(dbOperateRst.getLeft()) {
            return Result.success(buildBonusPayResp(rewardApplyEntity));
        }else{
            return Result.error(dbOperateRst.getRight());
        }
    }

    //==================== 任务提交接口 ======================
    @Override
    public Result<BonusPayResp> submit(BonusPaySubmitReq bonusPaySubmitReq) {
        //01-校验：确认任务已受理
        List<RewardApply> existRecords = rewardApplyService.queryRewardApplyList(bonusPaySubmitReq.getTradeNo(), bonusPaySubmitReq.getBizType(), bonusPaySubmitReq.getTransBillDate(), bonusPaySubmitReq.getActivityNo());
        RewardApply rewardApplyRecord = existRecords.stream().filter(record-> !StringUtils.isEmpty(record.getExt1()) && !StringUtils.isEmpty(record.getExt2()) ).findFirst().orElse(null);
        //当前任务状态的判断
        boolean changeFj = Boolean.FALSE;
        if(!PromoTradeStatusEnum.INIT.getCode().equals(rewardApplyRecord.getStatus())){
            if(PromoTradeStatusEnum.SUBMIT.getCode().equals(rewardApplyRecord.getStatus())){
                return Result.error("当前任务已提交，请勿重复提交");
            }else if(PromoTradeStatusEnum.PAYING.getCode().equals(rewardApplyRecord.getStatus())){
                return Result.error("OA出款中，不支持重新提交");
            }else if(PromoTradeStatusEnum.SUSS.getCode().equals(rewardApplyRecord.getStatus())){
                return Result.error("当前任务已出款完成，不支持重新提交");
            }else if(PromoTradeStatusEnum.FAIL.getCode().equals(rewardApplyRecord.getStatus())){
                //判断附件是否有修改:TODO(MD5验证)
                Map<String,String> hisFjs = JSON.parseObject(rewardApplyRecord.getAttachmentPaths(), new TypeReference<List<String>>() {})
                        .stream().collect(Collectors.toMap(String::toString,null, (v1, v2) -> v1));
                Map<String,String> newFjs = bonusPaySubmitReq.getAllFjs().stream().collect(Collectors.toMap(String::toString,null, (v1, v2) -> v1));
                if(hisFjs.size()!=newFjs.size()){
                    changeFj = Boolean.TRUE;
                }else{
                    for(String keyTmp:newFjs.keySet()){
                        if(!hisFjs.containsKey(keyTmp)){
                            changeFj = Boolean.TRUE;
                            break;
                        }
                    }
                }
                if(!changeFj){
                    Logger.warn("OA出款任务[{}]重新提交：附件无任务变更，请谨慎",bonusPaySubmitReq.getActivityNo());
                }
            }
        }

        //02-任务更新：事务一致性
        Pair<Boolean,String> dbOperateRst =  transactionTemplate.execute(new TransactionCallback<Pair<Boolean,String>>() {
            @Override
            public Pair<Boolean,String> doInTransaction(TransactionStatus transactionStatus) {
                try{
                    //02-完善数据：更新附件和备注信息，状态更新为已提交
                    HashMap<String,String> ext2 = JSON.parseObject(rewardApplyRecord.getExt2(), new TypeReference<HashMap<String, String>>() {});
                    ext2.put("applyUserNo",bonusPaySubmitReq.getApplyUserNo());
                    ext2.put("applyUserName",bonusPaySubmitReq.getApplyUserName());
                    rewardApplyRecord.setExt2(JSON.toJSONString(ext2));
                    rewardApplyRecord.setApplyUserId(bonusPaySubmitReq.getApplyUserNo());
                    rewardApplyRecord.setRemark(bonusPaySubmitReq.getRemark());
                    rewardApplyRecord.setStatus(PromoTradeStatusEnum.SUBMIT.getCode());
                    rewardApplyRecord.setAttachmentPaths(JSON.toJSONString(bonusPaySubmitReq.getAllFjs()));
                    rewardApplyRecord.setBatchNo(String.format("%s%s_%s",rewardApplyRecord.getActivityNo(),rewardApplyRecord.getTransBillDate(), DateUtils.format(new Date(), "yyyyMMddHHmmss")));
                    rewardApplyService.updatesStatusById(rewardApplyRecord);

                    //03-创建异步任务
                    PostActionItemBO postActionItemBO = PostActionItemBO.builder()
                            .actionType(PostActionTypes.PROMO_REWARD_START.getName())
                            .bizId(rewardApplyRecord.getBatchNo())
                            .paramObject(rewardApplyRecord)
                            .remark("奖励金下发-启动出款任务")
                            .status(PostActionExecStatus.EXECUTE.value())
                            .build();
                    postActionService.addAction(postActionItemBO);

                    //04-异步通知提交状态
                    PromoNotifyInsurableTask promoNotifyInsurableTask = new PromoNotifyInsurableTask(rewardApplyRecord);
                    SpringContextHolder.getBean(InsurableTaskExecutor.class).execute(promoNotifyInsurableTask);
                    return Pair.of(Boolean.TRUE,"suss");
                } catch (Exception e) {
                    //回滚事务
                    transactionStatus.setRollbackOnly();
                    Logger.error("奖励金下发-启动出款任务时发生异常：", e);
                    return Pair.of(Boolean.FALSE,e.getMessage());
                }
            }
        });

        //03-返回结果
        if(dbOperateRst.getLeft()) {
            BonusPayResp bonusPayResp = new BonusPayResp();
            bonusPayResp.setTradeNo(rewardApplyRecord.getRequestNo());
            bonusPayResp.setStatus(PromoTradeStatusEnum.SUBMIT.getCode());
            return Result.success(bonusPayResp);
        }else{
            return Result.error(dbOperateRst.getRight());
        }
    }

    //==================== 异步通知接口 ======================
    @Override
    public Result<Void> syncNotify(String tradeNo, String bizType, String transBillDate, String newStatus, String errorMsg) {
        //01-加载发放记录
        List<RewardApply> existRecords = rewardApplyService.queryRewardApplyList(tradeNo, null, null, null);
        if(CollectionUtils.isEmpty(existRecords)){
            Logger.warn("syncNotify异步通知上游-数据异常:记录不存在：{}",tradeNo);
            return Result.error(ErrorCodeConstants.ACCORE_101900.getErrorCode(),String.format("PromoPayTask支付结果回调-数据异常:记录不存在：%s",tradeNo));
        }
        RewardApply existRecord = existRecords.get(0);

        //02-状态判断
        String oldStatus = existRecord.getStatus();
        if(oldStatus.equalsIgnoreCase(newStatus)){
            Logger.warn("PromoPayTask支付结果回调[{}-{}-{}]-跳过更新-当前任务状态一致:{}", tradeNo,newStatus,errorMsg,oldStatus);
            return Result.success();
        }

        //03-数据库变更
        existRecord.setStatus(newStatus);
        Integer effectNum = rewardApplyService.updatesStatusById(existRecord);

        //04-异步通知:一致性框架
        NotifyPayRsltTask notifyPayRsltTask = new NotifyPayRsltTask(buildNotifyPayRsltReq(existRecord,errorMsg));
        SpringContextHolder.getBean(InsurableTaskExecutor.class).execute(notifyPayRsltTask);

        return Result.success();
    }

    //==================== 辅助接口(建议重载) ======================

    /**
     * 批量提交的预校验
     * -- 强烈建议覆盖该方法
     *
     * @param batchBonusPayReq
     * @return
     */
    Pair<Boolean, String> preCheck(BatchBonusPayReq batchBonusPayReq) {
        return Pair.of(true, "suss");
    }

    /**
     * 单个提交的预校验
     * -- 强烈建议覆盖该方法
     *
     * @param bonusPayReq
     * @return
     */
    Pair<Boolean, String> preCheckPayReq(BonusPayReq bonusPayReq) {
        return Pair.of(true, "suss");
    }

    //=============== 辅助接口(不建议重载) ======================
    protected List<RewardApply> buildRewardApplyList(BatchBonusPayReq batchBonusPayReq) {
        List<RewardApply> records = new ArrayList<>();

        int i = 0;
        for (BonusPaySubDTO bonusPaySub : batchBonusPayReq.getDetails()) {
            RewardApply temp = new RewardApply();
            //基础信息
            temp.setRequestNo(batchBonusPayReq.getTradeNo());
            temp.setBusiType(batchBonusPayReq.getBizType());
            temp.setTransBillDate(batchBonusPayReq.getTransBillDate());
            temp.setTradeTime(batchBonusPayReq.getTransTime());
            temp.setActivityNo(batchBonusPayReq.getActivityNo());
            temp.setSourceSysId(batchBonusPayReq.getSystemId());
            //子业务信息
            temp.setUserType(bonusPaySub.getUserType());
            if(StringUtils.isEmpty(bonusPaySub.getUserType())){
                temp.setUserType(CustomerType.NM.getName());
            }
            temp.setUserCode(bonusPaySub.getUserCode());
            temp.setTradeAmt(bonusPaySub.getAmount());
            temp.setTaxAmt(bonusPaySub.getTaxAmount());
            //获取个人认证信息 TODO：获取失败后异步二次获取
            try {
                if(ObjectUtils.isEmpty(bonusPaySub.getUserGrade()) || ObjectUtils.isEmpty(bonusPaySub.getGradeChannel())) {
                    Result<QueryAuthStatusRespDTO> authResult = taxService.queryUserHighestAuth(bonusPaySub.getUserType(), bonusPaySub.getUserCode());
                    if (ObjectUtils.isEmpty(authResult) || !authResult.isSuccess() || ObjectUtils.isEmpty(authResult.getData())) {
                        //
                    } else {
                        QueryAuthStatusRespDTO authBO = authResult.getData();
                        temp.setUserGrade(authBO.getCurrentHighestAuthType());
                        temp.setGradeChannel(authBO.getChannel());
                    }
                }else{
                    temp.setUserGrade(bonusPaySub.getUserGrade());
                    temp.setGradeChannel(bonusPaySub.getGradeChannel());
                }
            }catch (Exception e){
                Logger.warn("查询爱豆最高认证级别异常",e);
            }
            //其他默认信息
            temp.setStatus(PromoTradeStatusEnum.INIT.getCode());

            if(i == 0){
                temp.setRemark(batchBonusPayReq.getRemark());
                //扩展信息
                temp.setExt1(JSON.toJSONString(batchBonusPayReq.getExt1()));
                HashMap<String,String> ext2 = new HashMap<>();
                ext2.put("totalAmount",batchBonusPayReq.getTotalAmount().toString());
                ext2.put("totalTaxAmount",batchBonusPayReq.getTotalTaxAmount().toString());
                temp.setExt2(JSON.toJSONString(ext2));
                //附件的追加
                if(batchBonusPayReq.getBizType().equalsIgnoreCase(RewardTypeEnum.MENTOR_BONUS.getCode())){
                    if(!ObjectUtils.isEmpty(batchBonusPayReq.getExt1())) {
                        List<String> fjs = new ArrayList<>();
                        if (batchBonusPayReq.getExt1().containsKey(MentorBonusServiceImpl.FP)) {
                            String[] fps1 = batchBonusPayReq.getExt1().get(MentorBonusServiceImpl.FP).split(",");
                            if (fps1.length > 0) {
                                fjs.addAll(Arrays.asList(fps1));
                            }
                        }
                        if (batchBonusPayReq.getExt1().containsKey(MentorBonusServiceImpl.MX)) {
                            String[] mxs1 = batchBonusPayReq.getExt1().get(MentorBonusServiceImpl.MX).split(",");
                            if (mxs1.length > 0) {
                                fjs.addAll(Arrays.asList(mxs1));
                            }
                        }
                        if (batchBonusPayReq.getExt1().containsKey(MentorBonusServiceImpl.FJ)) {
                            String[] fjs1 = batchBonusPayReq.getExt1().get(MentorBonusServiceImpl.FJ).split(",");
                            if(fjs1.length > 0){
                                fjs.addAll(Arrays.asList(fjs1));
                            }
                        }
                        if(fjs.size() > 0) {
                            temp.setAttachmentPaths(JSON.toJSONString(fjs));
                        }
                    }
                }
            }

            records.add(temp);
            i++;
        }

        return records;
    }

    /**
     * 构建返回结果
     * @param rewardApplyRecord
     * @return
     */
    public BonusPayResp buildBonusPayResp(RewardApply rewardApplyRecord){
        BonusPayResp bonusPayResp = new BonusPayResp();
        bonusPayResp.setTradeNo(rewardApplyRecord.getRequestNo());
        bonusPayResp.setStatus(rewardApplyRecord.getStatus());
        bonusPayResp.setStatusDesc(PromoTradeStatusEnum.getByCode(rewardApplyRecord.getStatus()).getCodeDesc());
        //如果需要发票，则发票申请编码返回
        if(!StringUtils.isEmpty(rewardApplyRecord.getBatchNo())){
            bonusPayResp.setRequestNo(rewardApplyRecord.getBatchNo());
        }

        return bonusPayResp;
    }

    private RewardApply buildRewardApply(BonusPayReq bonusPayReq,MemberGrade memberGrade){
        RewardApply temp = new RewardApply();

        temp.setRequestNo(bonusPayReq.getTradeNo());
        temp.setBusiType(bonusPayReq.getBizType());
        temp.setTransBillDate(bonusPayReq.getTransBillDate());
        temp.setTradeTime(bonusPayReq.getTransTime());
        temp.setActivityNo(bonusPayReq.getActivityNo());
        temp.setSourceSysId(bonusPayReq.getSystemId());

        temp.setUserType(bonusPayReq.getUserType());
        if(StringUtils.isEmpty(bonusPayReq.getUserType())){
            temp.setUserType(CustomerType.NM.getName());
        }
        temp.setUserCode(bonusPayReq.getUserCode());
        temp.setTradeAmt(bonusPayReq.getAmount());
        temp.setUserGrade(bonusPayReq.getUserGrade());
        temp.setGradeChannel(bonusPayReq.getGradeChannel());
        //其他默认信息
        temp.setStatus(PromoTradeStatusEnum.SUBMIT.getCode());
        //备注信息用于资金操作的描述，不能为空
        if(StringUtils.isEmpty(bonusPayReq.getRemark())){
            String busiMonth = bonusPayReq.getTransBillDate();
            String busiMonthStr = String.format("%s年%s月",busiMonth.substring(0, busiMonth.length()-2),busiMonth.substring(busiMonth.length()-2));
            if(busiMonth.length() == 4){
                busiMonthStr = busiMonth+"年";
            }
            String remarkDesc = String.format("%s%s奖金发放",busiMonthStr,RewardTypeEnum.getByCode(bonusPayReq.getBizType()).getCodeDesc());
            bonusPayReq.setRemark(remarkDesc);
        }
        temp.setRemark(bonusPayReq.getRemark());
        //扩展信息1：仅用于上游的扩展字段，同步会返回给上游
        temp.setExt1(JSON.toJSONString(bonusPayReq.getExt1()));
        //扩展信息2：业务平台自己的扩展字段，不会返回给上游
        HashMap<String,String> ext2 = new HashMap<>();
        ext2.put("notifyUrl",bonusPayReq.getNotifyUrl());
        temp.setExt2(JSON.toJSONString(ext2));

        return temp;
    }

    private Triple<Boolean,String,RewardApply> idempotentCheck(BonusPayReq bonusPayReq){
        //01- 任务是否存在的校验
        /*List<RewardApply> existRecords = rewardApplyService.queryRewardApplyRecords(null, bonusPayReq.getBizType(), bonusPayReq.getTransBillDate(), bonusPayReq.getUserCode(), bonusPayReq.getUserType());
        if(CollectionUtils.isEmpty(existRecords)){
            return Triple.of(Boolean.TRUE,"任务不存在",null);
        }else{
            RewardApply existRecord = existRecords.get(0);

            if(!existRecord.getRequestNo().equalsIgnoreCase(bonusPayReq.getTradeNo())){
                return Triple.of(Boolean.FALSE,"任务已存在,且业务流水号不一致",existRecords.get(0));
            }
            //终态则返回成功
            if(PromoTradeStatusEnum.SUSS.getCode().equals(existRecord.getStatus()) || PromoTradeStatusEnum.FAIL.getCode().equals(existRecord.getStatus()) ||
                    PromoTradeStatusEnum.INVOICE_REJECT.getCode().equals(existRecord.getStatus())){
                return Triple.of(Boolean.TRUE,"任务已存在且为终态",existRecords.get(0));
            }
        }*/

        //02-幂等校验：
        List<RewardApply> existRecords = rewardApplyService.queryRewardApplyList(bonusPayReq.getTradeNo(), null, null,null);
        if(!CollectionUtils.isEmpty(existRecords)){
            Logger.warn("幂等校验-业务流水号已存在:{}->id={}",bonusPayReq.getTradeNo(),existRecords.get(0).getId());
            return Triple.of(Boolean.TRUE,"业务流水号已存在",existRecords.get(0));
        }

        return Triple.of(Boolean.TRUE,"任务不存在",null);
    }

    public NotifyPromoBO buildNotifyPayRsltReq(RewardApply rewardApplyRecord, String errorMsg) {
        NotifyPromoBO notifyPromoBO = new NotifyPromoBO();

        HashMap<String,String> exts = JSON.parseObject(rewardApplyRecord.getExt2(), new TypeReference<HashMap<String, String>>() {});
        notifyPromoBO.setNotifyUrl(exts.get("notifyUrl"));
        String envActiveStr = SpringContextHolder.getApplicationContext().getBean(Environment.class).getProperty("spring.profiles.active");
        notifyPromoBO.setGroupCode(envActiveStr.equalsIgnoreCase("stable")?apolloConfigCenter.getTestEnvGroup():null);

        OANotifyDTO notifyDTO = new OANotifyDTO();
        notifyPromoBO.setNotifyDTO(notifyDTO);
        notifyDTO.setTradeNo(rewardApplyRecord.getRequestNo());
        notifyDTO.setRequestId(rewardApplyRecord.getBatchNo());
        notifyDTO.setBizType(rewardApplyRecord.getBusiType());
        notifyDTO.setTransBillDate(rewardApplyRecord.getTransBillDate());
        notifyDTO.setActivityNo(rewardApplyRecord.getActivityNo());
        notifyDTO.setStatus(rewardApplyRecord.getStatus());
        notifyDTO.setStatusDesc(PromoTradeStatusEnum.getByCode(rewardApplyRecord.getStatus()).getCodeDesc());
        notifyDTO.setErrorMsg(errorMsg);
        notifyDTO.setUserCode(rewardApplyRecord.getUserCode());
        notifyDTO.setUserType(rewardApplyRecord.getUserType());

        return notifyPromoBO;
    }

    //================== set注入依赖的Bean =====================

    //使用@Autowired在setter方法，需要final 关键字，这样子类便不能覆盖setter方法。否则，注解将无法正常运行。
    @Autowired
    public final void setRewardApplyService(RewardApplyService rewardApplyService) {
        this.rewardApplyService = rewardApplyService;
    }
    @Autowired
    public final void setPostActionService(PostActionService postActionService) {
        this.postActionService = postActionService;
    }
    @Autowired
    public final void setTransactionTemplate(TransactionTemplate transactionTemplate) {
        this.transactionTemplate = transactionTemplate;
    }
    @Autowired
    public final void setTaxService(TaxService taxService) {
        this.taxService = taxService;
    }
    @Autowired
    public final void setInvoiceService(InvoiceService invoiceService){
        this.invoiceService = invoiceService;
    }
    @Autowired
    public final void setApolloConfigCenter(ApolloConfigCenter apolloConfigCenter) {
        this.apolloConfigCenter = apolloConfigCenter;
    }
}
