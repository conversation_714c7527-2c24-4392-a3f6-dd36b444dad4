package com.akucun.account.proxy.domain.model;

import lombok.Data;
import org.apache.commons.lang3.tuple.Pair;

import java.math.BigDecimal;
import java.util.Map;

@Data
public abstract class Adjust {
    /**
     *调账类型：1：分账，2：转账，3：罚扣，4：账户中心调账
     */
    private String adjustmentType;
    /**
     *用户编码
     */
    private String customerCode;
    /**
     *金额
     */
    private BigDecimal amount;
    /**
     *备注
     */
    private String remark;
    /**
     * 拓展
     */
    private Map<String, String> ext;

    /**
     * 调账
     */
    public abstract Pair<Integer,String> done();
}
