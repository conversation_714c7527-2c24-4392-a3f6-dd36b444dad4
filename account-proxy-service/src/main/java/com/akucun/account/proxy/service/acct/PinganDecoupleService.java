package com.akucun.account.proxy.service.acct;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.dao.model.PinganDecoupleWhitelist;
import com.akucun.account.proxy.facade.stub.others.account.vo.PinganDecoupleAccountVO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Author: silei
 * @Date: 2021/12/20
 * @desc:
 */
public interface PinganDecoupleService extends IService<PinganDecoupleWhitelist> {


    /**
     * 保存灰度白名单账户
     * @param vo
     * @return
     */
    Result<Void> saveOrUpdateData(PinganDecoupleAccountVO vo);


    /**
     * 检查是否灰度白名单账户
     * @param customerCode
     * @param customerType
     * @return
     */
    Boolean checkGrayWhitelist(String customerCode, String customerType);

    /**
     * 平安账户反清分
     * @param beginIndex
     * @param endIndex
     */
    void accountAntiClearing(int beginIndex, int endIndex);

    /**
     * 账户反清分处理
     * @param customerCode
     * @param customerType
     * @param antiClearAmount
     * @return
     */
    boolean antiClearingProcess(String customerCode, String customerType, String antiClearAmount, String withdrawNo);

    /**
     * 提现失败账户反清分
     * @param customerCode
     * @param customerType
     * @param antiClearAmount
     * @param withdrawNo
     * @return
     */
    boolean antiClearing4WithdrawFail(String customerCode, String customerType, String antiClearAmount, String withdrawNo);

    /**
     * 检查是否灰度白名单店长 true:是， false:否
     * @param customerCode
     * @param customerType
     * @return
     */
    boolean checkShopAgentWhitelist(String customerCode, String customerType);


    /**
     * 检查灰度账户反清分是否完成
     * @param customerCode
     * @param customerType
     * @return
     */
    boolean checkAntiClearing(String customerCode, String customerType);


    /**
     * 白名单店长可用余额转冻结金额处理
     * @param beginIndex
     * @param endIndex
     */
    void shopAgentWhitelistProcess(int beginIndex, int endIndex);
    
    /**
     * 租户灰度
     * @param customerCode
     * @param customerType
     * @return
     */
    Boolean tenantGray(String customerCode, String customerType);
}
