package com.akucun.account.proxy.service.postaction.task;

import com.aikucun.common2.base.Result;
import com.aikucun.common2.utils.CollectionUtils;
import com.aikucun.common2.utils.datamasking.DataMask;
import com.akucun.account.center.client.model.TradeInfo;
import com.akucun.account.center.client.model.account.AccountBookDO;
import com.akucun.account.center.client.model.config.DomainConfigDO;
import com.akucun.account.center.client.model.query.AccountQuery;
import com.akucun.account.center.feign.AccountCenterService;
import com.akucun.account.proxy.common.config.ApolloConfigCenter;
import com.akucun.account.proxy.common.enums.PostActionTypes;
import com.akucun.account.proxy.common.utils.RedisUtil;
import com.akucun.account.proxy.dao.model.PostActionItem;
import com.akucun.account.proxy.service.postaction.common.AbsPostActionExecutor;
import com.akucun.cloud.security.CodeUtils;
import com.akucun.fps.common.util.GsonUtils;
import com.akucun.sms.model.dto.SmsBatchParam;
import com.akucun.sms.remote.SmsFeignClient;
import com.alibaba.fastjson.JSON;
import com.mengxiang.base.common.log.Logger;
import com.mengxiang.saas.service.facade.common.feign.tenant.TenantUserFeign;
import com.mengxiang.saas.service.facade.common.feign.tenant.TenantWuyouConfigFeign;
import com.mengxiang.saas.service.facade.common.response.tenant.TenantExtendResp;
import com.mengxiang.saas.service.facade.common.response.tenant.TenantWuyouConfigQueryResp;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.awt.event.ItemListener;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


/**
 * @desc: 账户监控
 */
@Component
public class AccountMonitorTask extends AbsPostActionExecutor {

    public static String BalanceNotEnoughRemark = "账户余额不足监控";

    @Value("${account.monitor.times:2}")
    private int maxRetryTimes;

    @XxlJob("accountMonitorTask")
    public ReturnT<String> execute(String param) {
        return this.executeEntrance();
    }

    @Autowired
    private ApolloConfigCenter apolloConfigCenter;

    @Autowired
    private TenantUserFeign tenantUserFeign;

    @Autowired
    private TenantWuyouConfigFeign tenantWuyouConfigFeign;

    @Autowired
    private SmsFeignClient smsFeignClient;

    @Autowired
    private AccountCenterService accountCenterService;

    @Autowired
    private RedisUtil redisUtil;
    @Value("${market.account.monitor.sms:false}")
    private boolean marketAccountMarketMonitorSms;

    @Value("${insurance.business.code:2MXJT23092001}")
    private String insuranceBusinessCode;

    @Value("${balance.not.enough.business.code:2MXJT23111701}")
    private String balanceNotEnoughBusinessCode;

    @Value("${balance.threshold.ward.business.code:2MXJT23111702}")
    private String balanceThresholdWarnBusinessCode;

    @Value("${defaultBusinessCode:2MXJT24041601}")
    private String defaultBusinessCode;

    @Value("${market.monitor.sms.flag:true}")
    private Boolean marketMonitorSmsFlag;

    @Value("#{'${marketAccountMarketMonitorSmsWarnPhoneList:***********,***********}'.split(',')}")
    private List<String> marketAccountMarketMonitorSmsWarnPhoneList;

    @Value("${marketAccountMarketMonitorSmsWarnBusinessCode:2MXJT24041602}")
    private String marketAccountMarketMonitorSmsWarnBusinessCode;

    @Value("${sourec.code:account-proxy}")
    private String sourecCode;

    @Value("${market.account.monitor.sms.prefix.redis:market:account:monitor:sms:}")
    private String marketAccountMonitorSmsPrefixRedis;

    @Value("${market.account.monitor.sms.redis.time:43200}")
    private Long marketAccountMonitorSmsRedisTime;
    @Value("${insurance.trade:TRADE_TYPE_176}")
    private String insuranceTrade;

    @Value("${freeze.trade:TRADE_TYPE_171}")
    private String freezeTrade;

    @Value("${unfreezepay.trade:TRADE_TYPE_177}")
    private String unfreezepay;

    @Override
    public int getMaxRetryTimes() {
        return maxRetryTimes;
    }

    @Override
    protected String getActionType() {
        return PostActionTypes.ACCOUNT_MONITOR.getName();
    }

    @Override
    public Result<Void> execute(PostActionItem item) {
        Result<Void> result = Result.success();
        TradeInfo tradeInfo = null;
        try {
            tradeInfo = GsonUtils.getInstance().fromJson(item.getParam(), TradeInfo.class);
            if (null != apolloConfigCenter.getAccountMonitorMap() && apolloConfigCenter.getAccountMonitorMap().get(tradeInfo.getAccountTypeKey() + "_" + tradeInfo.getTradeType())) {
                return marketAccountMonitor(item, tradeInfo);
            }
        } catch (Exception e) {
            Logger.error("AccountMonitorTask execute error : {}", DataMask.toJSONString(tradeInfo), e);
            return Result.error(e);
        }
        return result;
    }

    private Result<Void> marketAccountMonitor(PostActionItem item, TradeInfo tradeInfo) {
        Logger.info("AccountMonitorTask execute，营销账户监控开始");
        Result<Void> result = Result.success();
        String tenantId = tradeInfo.getCustomerCode();

        // 查询营销账户告警阀值，并且余额达到阀值
        BigDecimal availableAmountMonitoringThreshold = new BigDecimal(3000);
        BigDecimal availableAmount;

        String businessCode = "";
        if (insuranceTrade.equalsIgnoreCase(tradeInfo.getTradeType())) {
            businessCode = insuranceBusinessCode;
        }  else {
            businessCode = balanceNotEnoughBusinessCode;
        }

        if (StringUtils.isNotBlank(defaultBusinessCode)) {
            businessCode = defaultBusinessCode;
        }

        // 检查此租户是否已通知
        String redisKey = marketAccountMonitorSmsPrefixRedis + tenantId + businessCode;
        if (null != redisUtil.get(redisKey)) {
            Logger.info("AccountMonitorTask execute，在短信不发送周期内，不发短信, 租户ID:{}", tenantId);
            return result;
        }

        if (insuranceTrade.equalsIgnoreCase(tradeInfo.getTradeType())) {
            // 尊享运费无忧场景：获取租户配置，有则继续走后续通知流程
            Logger.info("AccountMonitorTask execute，接口请求租户营销配置 : {}", DataMask.toJSONString(Long.parseLong(tenantId)));
            com.mengxiang.base.common.model.result.Result<TenantWuyouConfigQueryResp> result1 = tenantWuyouConfigFeign.queryWuyouConfigByTenantId(Long.parseLong(tenantId));
            Logger.info("AccountMonitorTask execute，接口响应租户营销配置 : {}, 租户ID:{}", DataMask.toJSONString(result1), tenantId);
            if (null == result1 || null == result1.getData() || null == result1.getData().getTenantReturnRate()) {
                Logger.info("AccountMonitorTask execute，租户未开通营销业务，不发短信, 租户ID:{}", tenantId);
                return result;
            }
        }

        if (!BalanceNotEnoughRemark.equalsIgnoreCase(item.getRemark())) {
            // 当交易余额不足时，不再检测，直接走后续发短信逻辑。如果任务延迟会误报，能接受。
            Logger.info("AccountMonitorTask execute，营销账户设置查询,查询配置请求:{}", JSON.toJSONString(Collections.singletonList(tenantId + "availableAmountMonitoringThreshold")));
            com.akucun.common.Result<List<DomainConfigDO>> result4 = accountCenterService.queryDomainConfig(Collections.singletonList(tenantId + "availableAmountMonitoringThreshold"));
            Logger.info("AccountMonitorTask execute，营销账户设置查询,查询配置响应:{}, 租户ID:{}", JSON.toJSONString(result4), tenantId);
            if (result4.isSuccess() && !result4.getData().isEmpty()) {
                availableAmountMonitoringThreshold = new BigDecimal(result4.getData().get(0).getConfigValue());
            } else {
                Logger.info("AccountMonitorTask execute，营销账户设置查询,查询配置请求:{}, 租户ID:{}", JSON.toJSONString(Collections.singletonList("availableAmountMonitoringThreshold")), tenantId);
                com.akucun.common.Result<List<DomainConfigDO>> result3 = accountCenterService.queryDomainConfig(Collections.singletonList("availableAmountMonitoringThreshold"));
                Logger.info("AccountMonitorTask execute，营销账户设置查询,查询配置响应:{}, 租户ID:{}", JSON.toJSONString(result3), tenantId);
                if (result3.isSuccess() && !result3.getData().isEmpty()) {
                    availableAmountMonitoringThreshold = new BigDecimal(result3.getData().get(0).getConfigValue());
                }
            }

            AccountQuery accountQuery = new AccountQuery();
            accountQuery.setAccountTypeKey(tradeInfo.getAccountTypeKey());
            accountQuery.setCustomerCode(tradeInfo.getCustomerCode());
            com.akucun.common.Result<AccountBookDO> accountBookDOResult = accountCenterService.queryAccount(accountQuery);
            Logger.info("AccountMonitorTask execute，营销账户设置查询,查询租户账户:{}, 租户ID:{}", JSON.toJSONString(accountBookDOResult), tenantId);
            if (accountBookDOResult.isSuccess() && accountBookDOResult.getData() != null) {
                availableAmount = accountBookDOResult.getData().getBalance();
                if (availableAmount.compareTo(availableAmountMonitoringThreshold) > 0) {
                    Logger.info("AccountMonitorTask execute，余额足够，不发短信, 租户ID:{}", tenantId);
                    return result;
                } else {
                    businessCode = balanceThresholdWarnBusinessCode;
                }
            }

        }

        // 获取租户营销账户告警手机号，如果手机号未设置，获取租户注册手机号
        List<String> warnPhoneList = new ArrayList<>();
        com.akucun.common.Result<List<DomainConfigDO>> result2 = accountCenterService.queryDomainConfig(Collections.singletonList(tenantId + "_market_warn_phone_list"));
        Logger.info("AccountMonitorTask execute，查询配置响应状态:{}, 租户ID:{}", result2.isSuccess(), tenantId);
        if (result2.isSuccess() && !result2.getData().isEmpty()) {
            Logger.info("AccountMonitorTask execute，查询配置响应:{}, 租户ID:{}", result2.getData().size(), tenantId);
            warnPhoneList = JSON.parseArray(result2.getData().get(0).getConfigValue(), String.class);
        } else {
            Logger.info("AccountMonitorTask execute，营销账户设置查询,查询租户手机号请求, 租户ID:{}", tenantId);
            com.mengxiang.base.common.model.result.Result<TenantExtendResp> respResult = tenantUserFeign.getTenantExtendByTenantId(Long.parseLong(tenantId));
            Logger.info("AccountMonitorTask execute，营销账户设置查询,查询租户手机号响应:{}, 租户ID:{}", respResult.isSuccess(), tenantId);
            if (null != respResult && respResult.isSuccess() && respResult.getData() != null && respResult.getData().getPhoneEncrypt() != null) {
                String phone = CodeUtils.decrypt(respResult.getData().getPhoneEncrypt()).getData();
                warnPhoneList = Collections.singletonList(phone);
            }
        }

        if (StringUtils.isNotBlank(defaultBusinessCode)) {
            businessCode = defaultBusinessCode;
        }

        // 发送短信
        if (marketAccountMarketMonitorSms) {
            // 租户发短信
            SmsBatchParam smsBatchParam = SmsBatchParam.builder().mobiles(warnPhoneList)
                    .businessCode(businessCode)
                    .source(sourecCode)
                    .build();
            Logger.info("AccountMonitorTask execute，invoke SmsFeignClient begin:{}, 租户ID:{}", JSON.toJSONString(smsBatchParam), tenantId);
            com.x.base.common.util.Result result4 = smsFeignClient.batchSendSms(smsBatchParam);
            Logger.info("AccountMonitorTask execute，invoke SmsFeignClient end:{}, 租户ID:{}", JSON.toJSONString(result4), tenantId);
            if (!result4.isSuccess()) {
                Logger.error("AccountMonitorTask execute error，invoke SmsFeignClient fail:{}, 租户ID:{}", JSON.toJSONString(result4), tenantId);
            }

            // 运营人员发短信
            if(marketMonitorSmsFlag) {
                Map<String, Object> params = new HashMap<>();
                params.put("0", tenantId);
                List<String> validPhones = marketAccountMarketMonitorSmsWarnPhoneList.stream().filter(t -> !org.springframework.util.StringUtils.isEmpty(t)).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(validPhones) && StringUtils.isNotBlank(marketAccountMarketMonitorSmsWarnBusinessCode)) {
                    SmsBatchParam smsBatchParam2 = SmsBatchParam.builder().mobiles(validPhones)
                            .businessCode(marketAccountMarketMonitorSmsWarnBusinessCode)
                            .source(sourecCode)
                            .params(params)
                            .build();
                    Logger.info("AccountMonitorTask execute，invoke SmsFeignClient begin:{}, 租户ID:{}", JSON.toJSONString(smsBatchParam2), tenantId);
                    com.x.base.common.util.Result result5 = smsFeignClient.batchSendSms(smsBatchParam2);
                    Logger.info("AccountMonitorTask execute，invoke SmsFeignClient end:{}, 租户ID:{}", JSON.toJSONString(result5), tenantId);
                    if (!result5.isSuccess()) {
                        Logger.error("AccountMonitorTask execute error，invoke SmsFeignClient fail:{}, 租户ID:{}", JSON.toJSONString(result5), tenantId);
                    }
                }
            }
        }

        // 设置标识，一天只通知一次
        Logger.info("AccountMonitorTask execute，设置缓存, 租户ID:{}", tenantId);
        redisUtil.set(redisKey, "1", marketAccountMonitorSmsRedisTime);

        Logger.info("AccountMonitorTask execute，营销账户监控结束, 租户ID:{}", tenantId);
        return result;
    }

}
