package com.akucun.account.proxy.service.common;

import com.akucun.account.proxy.facade.stub.others.dto.req.BatchTaxReq;
import com.akucun.account.proxy.facade.stub.others.dto.req.TaxReq;
import com.akucun.account.proxy.facade.stub.others.dto.res.TaxQueryResp;
import com.akucun.common.Result;
import com.akucun.member.audit.model.dto.auth.QueryAuthStatusRespDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : 算税相关
 * @Create on : 2025/1/15 20:53
 **/
public interface TaxService {
    Result<TaxQueryResp> calc(TaxReq taxQueryReq);

    Result<List<TaxQueryResp>> batchCalc(BatchTaxReq batchTaxReq);

    Result<QueryAuthStatusRespDTO> queryUserHighestAuth(String userType, String userCode);

    Integer queryUserHighestAuthStatus(String userType, String userCode);
}
