package com.akucun.account.proxy.service.tradeflow.script.phasebuild;

import com.akucun.account.proxy.service.tradeflow.domain.Trade;
import com.akucun.account.proxy.service.tradeflow.dto.bizinfo.BizInfoRegister;
import com.akucun.account.proxy.service.tradeflow.dto.bizinfo.BizInfoRegisterBindcard;
import com.akucun.account.proxy.service.tradeflow.script.IPhaseBuildScript;

public class ScriptPhaseBuildRegisterBindcardRegister implements IPhaseBuildScript {

    @Override
    public Object build(Trade trade) {
        BizInfoRegisterBindcard bindcard = (BizInfoRegisterBindcard) trade.getBizInfo();
        BizInfoRegister register = new BizInfoRegister();
        register.setCustomerType(bindcard.getCustomerType());
        register.setCustomerCode(bindcard.getCustomerCode());
        register.setAccountType(bindcard.getAccountType());
        register.setIdType(bindcard.getIdType());
        register.setIdCode(bindcard.getIdCode());
        register.setCustomerName(bindcard.getCustomerName());
        register.setTenantId(bindcard.getTenantId());
        register.setTenantType(bindcard.getTenantType());
        register.setPhone(bindcard.getPhone());
        return register;
    }

}
