package com.akucun.account.proxy.service.acct;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.dao.model.AccountTotalAmount;
import com.akucun.account.proxy.facade.stub.others.account.vo.AccountTotalVO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Author: silei
 * @Date: 2021/3/15
 * @desc:
 */
public interface AccountTotalAmountService extends IService<AccountTotalAmount> {

    /**
     * 查询汇总金额数据表
     * @param accountTotalAmount
     * @return
     */
    Result<AccountTotalVO> queryAccountTotalAmount(AccountTotalVO accountTotalAmount);

}
