package com.akucun.account.proxy.service.postaction.task;

import com.akucun.account.proxy.dao.mapper.CallLogMapper;
import com.mengxiang.base.common.log.Logger;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

@Component
public class CallLogCleanTask {

	@Resource
	private CallLogMapper callLogMapper;

	/**
	 * 定时删除多少天以前的任务
	 */
	@Value("${call.log.expire.days:30}")
	private int callLogExpireDays;

	/**
	 * 定时清楚任务最大循环执行次数，防止数据量大导致的数据库繁忙
	 */
	private int MAX_LOOP_TIMES = 100;

	/**
	 * 每批删除条数
	 */
	private int BATCH_DELETE_SIZE = 1000;

	@XxlJob(value = "CallLogCleanTask" )
	public ReturnT<String>  execute(String arg) {
		try {
			if (StringUtils.isNotBlank(arg)) {
				String[] split = arg.split(",", -1);
				if (StringUtils.isNotBlank(split[0])) {
					callLogExpireDays = Integer.valueOf(split[0]);
				}
				if (StringUtils.isNotBlank(split[1])) {
					BATCH_DELETE_SIZE = Integer.valueOf(split[1]);
				}
			}

			// 计算时间
			Date expireDate = DateUtils.addDays(new Date(), -1 * callLogExpireDays);

			int count = 0;
			int loopTimes =0;
			do {
				//每次删除1000条，循环删除
				count = count+ callLogMapper.delete(expireDate, null, BATCH_DELETE_SIZE);

				loopTimes++;

			} while(count > 0 && loopTimes < MAX_LOOP_TIMES);

			Logger.info("callLog表定时清理任务执行成功，本次共计删除" + count + "条数据");

		} catch (Exception e) {
			Logger.error("callLog表定时清理任务执行异常", e);
		}
		return ReturnT.SUCCESS;
	}
}
