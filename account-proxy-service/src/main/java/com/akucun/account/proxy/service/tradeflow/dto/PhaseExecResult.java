package com.akucun.account.proxy.service.tradeflow.dto;

import com.akucun.account.proxy.facade.stub.others.trade.res.TradePhaseStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class PhaseExecResult {

    private TradePhaseStatusEnum status;

    private String errorCode;

    private String errorMessage;

}
