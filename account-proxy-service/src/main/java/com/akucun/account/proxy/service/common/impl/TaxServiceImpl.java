package com.akucun.account.proxy.service.common.impl;

import com.akucun.account.proxy.facade.stub.others.dto.req.BatchTaxReq;
import com.akucun.account.proxy.facade.stub.others.dto.req.TaxDTO;
import com.akucun.account.proxy.facade.stub.others.dto.req.TaxReq;
import com.akucun.account.proxy.facade.stub.others.dto.res.TaxQueryResp;
import com.akucun.account.proxy.service.common.TaxService;
import com.akucun.common.Result;
import com.akucun.member.audit.facade.stub.fallback.api.FeignMemberAuthService;
import com.akucun.member.audit.model.dto.auth.QueryAuthStatusReqDTO;
import com.akucun.member.audit.model.dto.auth.QueryAuthStatusRespDTO;
import com.alibaba.fastjson.JSON;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.mengxiang.base.common.log.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : 算税相关
 * @Create on : 2025/1/15 20:54
 **/
@Service
public class TaxServiceImpl implements TaxService {

    @Resource
    private FeignMemberAuthService feignMemberAuthService;

    //每个业务类型的税率配置
    @ApolloJsonValue("${promo.reward.biztype.rate.mapper}")
    private Map<String, Map<Integer, String>> rate4Biztype;

    @Override
    public Result<TaxQueryResp> calc(TaxReq taxQueryReq) {
        TaxQueryResp resp = new TaxQueryResp();
        resp.setBizType(taxQueryReq.getBizType());
        resp.setUserType(taxQueryReq.getUserType());
        resp.setUserCode(taxQueryReq.getUserCode());
        resp.setAmount(taxQueryReq.getAmount());
        resp.setFeeRate(BigDecimal.ZERO);
        resp.setTaxAmount(BigDecimal.ZERO);

        try {

            //01-查询最高认证级别
            Result<QueryAuthStatusRespDTO> authResult = queryUserHighestAuth(taxQueryReq.getUserType(), taxQueryReq.getUserCode());
            if (ObjectUtils.isEmpty(authResult) || !authResult.isSuccess() || ObjectUtils.isEmpty(authResult.getData())) {
                if (ObjectUtils.isEmpty(authResult)) {
                    return Result.error("查询用户最高认证状态失败:会员查询结果为空");
                } else if (!authResult.isSuccess()) {
                    return Result.error(StringUtils.isEmpty(authResult.getMessage()) ? "查询用户最高认证状态失败:会员查询失败" : authResult.getMessage());
                } else if (ObjectUtils.isEmpty(authResult.getData())) {
                    return Result.error("查询用户最高认证状态失败:会员查询结果为空");
                }
            }
            QueryAuthStatusRespDTO authBO = authResult.getData();

            //02-根据最高认证级别，计算税率
            resp.setUserGrade(authBO.getCurrentHighestAuthType());
            resp.setGradeChannel(authBO.getChannel());
            if (rate4Biztype.containsKey(taxQueryReq.getBizType())) {
                Map<Integer, String> map = rate4Biztype.get(taxQueryReq.getBizType());
                if (map.containsKey(authBO.getCurrentHighestAuthType())) {
                    String rateStr = map.get(authBO.getCurrentHighestAuthType());
                    resp.setFeeRate(new BigDecimal(rateStr));
                    resp.setTaxAmount(taxQueryReq.getAmount().multiply(resp.getFeeRate()).setScale(2, BigDecimal.ROUND_HALF_UP));
                }
            }

            return Result.success(resp);
        } catch (Exception e) {
            Logger.error("查询用户最高认证级别并算税失败", e);
            return Result.error("查询用户最高认证级别并算税失败");
        }
    }

    @Override
    public Result<List<TaxQueryResp>> batchCalc(BatchTaxReq batchTaxReq) {
        List<TaxQueryResp> resp = new ArrayList<>();

        //TODO:并行执行
        for (TaxDTO taxDTOTmp : batchTaxReq.getTaxList()) {
            TaxReq tmp = new TaxReq();
            BeanUtils.copyProperties(taxDTOTmp, tmp);
            tmp.setBizType(batchTaxReq.getBizType());

            Result<TaxQueryResp> respTmp = calc(tmp);
            if (!ObjectUtils.isEmpty(respTmp) && respTmp.isSuccess()) {
                resp.add(respTmp.getData());
            }else{
                TaxQueryResp queryTmp = new TaxQueryResp();
                queryTmp.setQueryFlag(Boolean.FALSE);
                queryTmp.setErrorMsg(ObjectUtils.isEmpty(respTmp)?"查询用户最高认证状态失败":respTmp.getMessage());
                queryTmp.setUserCode(taxDTOTmp.getUserCode());
                queryTmp.setUserType(taxDTOTmp.getUserType());
                queryTmp.setBizType(batchTaxReq.getBizType());
                resp.add(queryTmp);
            }
        }

        if (resp.size() > 0) {
            return Result.success(resp);
        } else {
            return Result.error("查询异常：没有符合条件的数据");
        }
    }

    /**
     * 查询个人认证级别
     *
     * @param userType
     * @param userCode
     * @return
     */
    @Override
    public Result<QueryAuthStatusRespDTO> queryUserHighestAuth(String userType, String userCode) {
        //01-构建查询条件
        QueryAuthStatusReqDTO query = new QueryAuthStatusReqDTO();
        //店长是distributorId,店主是userCode
        query.setUserCode(userCode);
        //店主/店长类型转换:2:店主，3:店长
        int userTypeNum = 2;
        if (StringUtils.isEmpty(userType) || userType.equalsIgnoreCase("NM")) {
            userTypeNum = 2;
            query.setUserCode(userCode.startsWith("NM") ? userCode.substring(2) : userCode);
        } else if (userType.equalsIgnoreCase("NMDL")) {
            userTypeNum = 3;
        }
        query.setUserType(userTypeNum);

        //02-查询
        Logger.info("查询爱豆最高认证状态req:{}", userType + userCode, JSON.toJSONString(query));
        Result<QueryAuthStatusRespDTO> authResult = feignMemberAuthService.queryUserHighestAuthStatusV2(query);
        Logger.info("查询爱豆最高认证状态resp:{}->{}", userType + userCode, JSON.toJSONString(authResult));

        return authResult;
    }

    /**
     * 查询个人认证级别
     *
     * @param userType
     * @param userCode
     * @return 参考-MemberGrade
     */
    @Override
    public Integer queryUserHighestAuthStatus(String userType, String userCode) {
        Result<QueryAuthStatusRespDTO> authResult = queryUserHighestAuth(userType, userCode);
        if (ObjectUtils.isEmpty(authResult) || !authResult.isSuccess() || ObjectUtils.isEmpty(authResult.getData())) {
            return null;
        }

        return authResult.getData().getCurrentHighestAuthType();
    }

    public static void main(String[] args) {
        Map<String, Map<Integer, String>> rates = new HashMap<>();
        Map<Integer, String> map1 = new HashMap<>();
        map1.put(1, "0.025");
        map1.put(3, "0.025");
        rates.put("MENTOR_BONUS", map1);
        Map<Integer, String> map2 = new HashMap<>();
        map2.put(1, "0.025");
        map2.put(3, "0.025");
        rates.put("EMPOWERMENT_CAMP", map2);
        Map<Integer, String> map3 = new HashMap<>();
        map3.put(1, "0.025");
        map3.put(3, "0.025");
        rates.put("MONTHLY_BONUS", map3);
        System.out.println(JSON.toJSONString(rates));
    }
}
