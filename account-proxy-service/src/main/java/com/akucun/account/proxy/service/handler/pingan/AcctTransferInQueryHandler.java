package com.akucun.account.proxy.service.handler.pingan;

import com.aikucun.common2.utils.datamasking.DataMask;
import com.akucun.account.proxy.common.constant.CommonConstants;
import com.akucun.account.proxy.common.enums.ResponseEnum;
import com.akucun.account.proxy.common.enums.ResultStatus;
import com.akucun.account.proxy.common.exception.AccountProxyException;
import com.akucun.account.proxy.dao.model.AccountOpTransferAmount;
import com.akucun.account.proxy.service.acct.AccountOpTransferAmountService;
import com.akucun.account.proxy.service.common.bo.AccountExecStepContext;
import com.akucun.account.proxy.service.common.bo.AccountReq;
import com.akucun.account.proxy.service.common.bo.AccountResp;
import com.akucun.account.proxy.service.common.step.AbstractHandler;
import com.akucun.fps.common.entity.Result;
import com.akucun.fps.pingan.client.model.po.PinganDivision;
import com.akucun.fps.pingan.client.model.query.PinganDivisionQueryReq;
import com.akucun.fps.pingan.feign.api.assets.AssetsServiceApi;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mengxiang.base.common.log.Logger;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 账户余额划归到个人账户查询
 */
@Component
public class AcctTransferInQueryHandler extends AbstractHandler {

    @Resource
    private AssetsServiceApi assetsServiceApi;
    @Autowired
    private AccountOpTransferAmountService accountTransferAmountService;

    @Override
    protected boolean preCheck(AccountExecStepContext accountExecStepContext) {
        AccountReq req = accountExecStepContext.getAccountReq();
        AccountResp resp = accountExecStepContext.getAccountResp();
        //参数校验
        if (StringUtils.isBlank(req.getDetailOrderNo())) {
            resp.setStatus(ResultStatus.F.getCode());
            resp.setReplyMsg("请求流水号为空");
        }
        return !ResultStatus.F.getCode().equals(resp.getStatus());
    }

    @Override
    protected void doSubmitBefore(AccountExecStepContext accountExecStepContext) {
        AccountReq req = accountExecStepContext.getAccountReq();
        AccountResp resp = accountExecStepContext.getAccountResp();

        //查询交易金额
        long amount = 0L;
        LambdaQueryWrapper<AccountOpTransferAmount> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AccountOpTransferAmount::getAccountTradeId, req.getAccountTradeId())
                .eq(AccountOpTransferAmount::getCustomerCode, req.getCustomerCode())
                .eq(AccountOpTransferAmount::getCustomerType, req.getCustomerType())
                .eq(AccountOpTransferAmount::getCollectStatus, ResultStatus.S.getCode());
        List<AccountOpTransferAmount> list = accountTransferAmountService.getBaseMapper().selectList(wrapper);
        if (!CollectionUtils.isEmpty(list)) {
            amount = list.stream().mapToLong(AccountOpTransferAmount::getAmount).sum();
        }
        //组装查询参数
        PinganDivisionQueryReq queryReq = new PinganDivisionQueryReq();
        if (amount > 0) {
            queryReq.setCustomerType(req.getCustomerType());
            queryReq.setBillType("AU");
            queryReq.setBillNo(req.getDetailOrderNo());
            //会员间交易查询
            queryReq.setFuncFlag("2");
            queryReq.setTranAmount(amount);
        } else {
            Logger.error("AcctTransferInQueryHandler doSubmitBefore accountUpdateError, transferAmount is null! customerCode:{}, customerType:{}", req.getCustomerCode(), req.getCustomerType());
            return;
        }
        //发起交易查询
        Result<PinganDivision> result = assetsServiceApi.queryTradeStatus(queryReq);
        Logger.info("AcctTransferInQueryHandler queryTradeStatus result:{}", DataMask.toJSONString(result));
        if (result.isSuccess() && Objects.nonNull(result.getData()) && result.getData().getStatus().equals(0)) {
            //更新资金划归状态
            int i = accountTransferAmountService.getBaseMapper().update(AccountOpTransferAmount.builder().allocateStatus(ResultStatus.S.getCode()).build(),
                    new LambdaQueryWrapper<AccountOpTransferAmount>().eq(AccountOpTransferAmount::getAccountTradeId, req.getAccountTradeId())
                            .eq(AccountOpTransferAmount::getCollectStatus, ResultStatus.S.getCode()));
            if (i == 0) {
                Logger.error("AcctTransferInQueryHandler doSubmitBefore accountUpdateError, transferAmount exception! customerCode:{}, customerType:{}", req.getCustomerCode(), req.getCustomerType());
                throw new AccountProxyException(ResponseEnum.AMOUNT_ALLOCATE_EXCEPTION);
            }
            resp.setStatus(ResultStatus.S.getCode());
            resp.setReplyCode(CommonConstants.SUCC_CODE);
            resp.setReplyMsg(ResultStatus.S.getDesc());
        } else if (!result.isSuccess() && Objects.nonNull(result.getData()) && result.getData().getStatus().equals(1)) {
            resp.setStatus(ResultStatus.F.getCode());
            resp.setReplyCode(result.getErrorCode() + "");
            resp.setReplyMsg(result.getErrorMessage());
        }

    }

    @Override
    protected void doSubmit(AccountExecStepContext accountExecStepContext) {

    }

    @Override
    protected void doSubmitAfter(AccountExecStepContext accountExecStepContext) throws Exception {

    }
}
