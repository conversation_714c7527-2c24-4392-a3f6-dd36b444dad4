package com.akucun.account.proxy.service.acct.bo;

import com.akucun.account.proxy.common.enums.MemberChannel;
import com.akucun.account.proxy.common.enums.MemberGrade;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

@Getter
@Setter
public class CustomerAuthBO {

    /**
     * 店铺认证等级
     */
    private MemberGrade grade;

    /**
     * 店铺认证渠道
     */
    private Integer channel;

    /**
     * 是否包含店主身份
     */
    private Boolean isIncludeSeller;

    /**
     * 店主的爱库存用户ID
     */
    private String userId;

    /**
     * 店长的店主身份爱豆编号
     */
    private String sellerCustomerCode;

    /**
     * 申税注册ID
     */
    private String taxId;

    /**
     * 统一社会信用代码
     */
    private String creditCode;

    /**
     * 公司名称
     */
    private String corpName;

    /**
     * 法定代表人姓名
     */
    private String corpRepName;

    public boolean includeSeller() {
        return StringUtils.isNotEmpty(this.sellerCustomerCode);
    }

}
