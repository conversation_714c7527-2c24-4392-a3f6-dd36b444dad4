package com.akucun.account.proxy.service.handler.trade;


import com.akucun.account.proxy.common.constant.CommonConstants;
import com.akucun.account.proxy.common.enums.ResultStatus;
import com.akucun.account.proxy.dao.model.AccountTrade;
import com.akucun.account.proxy.service.common.bo.AccountExecStepContext;
import com.akucun.account.proxy.service.common.bo.AccountResp;
import com.akucun.account.proxy.service.common.bo.AccountTradeReq;
import com.akucun.account.proxy.service.common.step.AbstractHandler;
import com.akucun.account.proxy.service.trade.AccountTradeCommonService;
import com.akucun.account.proxy.service.trade.AccountTradeService;
import com.akucun.common.Result;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mengxiang.account.finpointcore.service.facade.common.enums.AcctTypeEnum;
import com.mengxiang.account.finpointcore.service.facade.common.enums.BizTypeEnum;
import com.mengxiang.account.finpointcore.service.facade.common.enums.UserTypeEnum;
import com.mengxiang.account.finpointcore.service.facade.common.feign.FinanceAccountFacade;
import com.mengxiang.account.finpointcore.service.facade.common.request.PointTransferRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.UUID;


/**
 * <AUTHOR>
 * @date 2020/12/21 16:45
 */
@Component
public class PointAcctTradeHandler extends AbstractHandler {

    @Autowired
    private AccountTradeCommonService accountTradeCommonService;
    @Resource
    private FinanceAccountFacade financeAccountFacade;
    @Autowired
    private AccountTradeService accountTradeService;

    @Override
    protected boolean preCheck(AccountExecStepContext accountExecStepContext) {
        return true;
    }

    @Override
    protected void doSubmitBefore(AccountExecStepContext accountExecStepContext) {

    }

    @Override
    protected void doSubmit(AccountExecStepContext accountExecStepContext) {
        AccountTradeReq req = accountExecStepContext.getAccountTradeReq();
        Result result = Result.success();

        PointTransferRequest pointRequest = new PointTransferRequest();
        pointRequest.setRequestNo(UUID.randomUUID().toString());
        pointRequest.setAccountType(AcctTypeEnum.POINTS.getName());
        pointRequest.setAmount(req.getAmount());
        pointRequest.setBizType(BizTypeEnum.BIZ_TYPE_008.getName());
        pointRequest.setSourceAccountNo(req.getCustomerCode());
        pointRequest.setSourceUserType(UserTypeEnum.c.getName());
        pointRequest.setSourceBizNo(req.getSourceNo());
        pointRequest.setSystemId("PAYMENT");
        pointRequest.setTenantId(req.getTenantId());
        pointRequest.setTradeNo(req.getTradeNo());
        pointRequest.setTransTime(new Date());
        pointRequest.setRemark("金币兑换商品");

        com.mengxiang.base.common.model.result.Result<String> tradeResult = financeAccountFacade.pointDealTrade(pointRequest);

        if (null != tradeResult && tradeResult.isSuccess()) {
            result.setSuccess(Boolean.TRUE);
        } else {
            result.setSuccess(Boolean.FALSE);
            result.setCode(Integer.parseInt(tradeResult.getCode()));
            result.setMessage(tradeResult.getMessage());
        }
        accountExecStepContext.setRespMessage(result);
    }

    @Override
    protected void doSubmitAfter(AccountExecStepContext accountExecStepContext) throws Exception {
        Result<Void> result = (Result<Void>) accountExecStepContext.getRespMessage();
        AccountResp resp = accountExecStepContext.getAccountResp();
        AccountTradeReq req = accountExecStepContext.getAccountTradeReq();

        LambdaQueryWrapper wrapper =
                new LambdaQueryWrapper<AccountTrade>()
                        .eq(AccountTrade::getTradeNo, req.getTradeNo())
                        .eq(AccountTrade::getTradeType, CommonConstants.BONUS_ACCOUNT_PAY_TRADE_TYPE)
                        .eq(AccountTrade::getCustomerCode, req.getCustomerCode());
        if(result.isSuccess()) {
            // 更新为成功
            AccountTrade accountTrade = AccountTrade.builder().status(ResultStatus.S.getCode()).build();
            accountTradeService.update(accountTrade, wrapper);
            resp.setStatus(ResultStatus.S.getCode());
            resp.setReplyCode(CommonConstants.SUCC_CODE);
            resp.setReplyMsg(ResultStatus.S.getDesc());
        } else {
            // 更新为失败
            AccountTrade accountTrade = AccountTrade.builder().status(ResultStatus.P.getCode()).build();
            accountTradeService.update(accountTrade, wrapper);
            resp.setStatus(ResultStatus.F.getCode());
            resp.setReplyCode(Integer.toString(result.getCode()));
            resp.setReplyMsg(result.getMessage());
        }
    }

}
