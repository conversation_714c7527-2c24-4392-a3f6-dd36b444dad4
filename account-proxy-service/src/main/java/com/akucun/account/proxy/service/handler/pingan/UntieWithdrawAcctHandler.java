package com.akucun.account.proxy.service.handler.pingan;

import com.aikucun.common2.utils.datamasking.DataMask;
import com.akucun.account.proxy.common.constant.CommonConstants;
import com.akucun.account.proxy.common.enums.ResponseEnum;
import com.akucun.account.proxy.common.enums.ResultStatus;
import com.akucun.account.proxy.common.exception.AccountProxyException;
import com.akucun.account.proxy.service.common.bo.AccountExecStepContext;
import com.akucun.account.proxy.service.common.bo.AccountReq;
import com.akucun.account.proxy.service.common.bo.AccountResp;
import com.akucun.account.proxy.service.common.step.AbstractHandler;
import com.akucun.fps.common.entity.Result;
import com.akucun.fps.common.entity.ResultList;
import com.akucun.fps.pingan.client.model.query.PinganCardQueryDO;
import com.akucun.fps.pingan.client.vo.PinganCardVO;
import com.akucun.fps.pingan.client.vo.UntieWithdrawVO;
import com.akucun.fps.pingan.feign.api.merchant.MerchantServiceApi;
import com.akucun.fps.pingan.feign.api.settlement.SettlementServiceApi;
import com.alibaba.fastjson.JSON;
import com.mengxiang.base.common.log.Logger;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @Author: silei
 * @Date: 2020/9/1
 * @desc: 解绑提现账户
 */
@Component
public class UntieWithdrawAcctHandler extends AbstractHandler {

    @Resource
    private SettlementServiceApi settlementServiceApi;
    @Resource
    private MerchantServiceApi merchantServiceApi;

    @Override
    protected boolean preCheck(AccountExecStepContext accountExecStepContext) {
        AccountResp resp = accountExecStepContext.getAccountResp();
        AccountReq req = accountExecStepContext.getAccountReq();
        if (StringUtils.isBlank(req.getCustomerCode())) {
            resp.setStatus(ResultStatus.F.getCode());
            resp.setReplyMsg("用户客户编码为空");
        }
        if (StringUtils.isBlank(req.getCustomerType())) {
            resp.setStatus(ResultStatus.F.getCode());
            resp.setReplyMsg("客户类型为空");
        }
        return !ResultStatus.F.getCode().equals(resp.getStatus());
    }

    @Override
    protected void doSubmitBefore(AccountExecStepContext accountExecStepContext) {
        AccountReq req = accountExecStepContext.getAccountReq();
        Logger.info("UnbindWithdrawAcctHandler doSubmitBefore req:{}", DataMask.toJSONString(req));
        //查询绑卡列表
        PinganCardQueryDO cardQuery = new PinganCardQueryDO();
        //店主店长客户编码都要拼接
        cardQuery.setCustomerCode(req.getCustomerType() + req.getCustomerCode());
        cardQuery.setCustomerType(req.getCustomerType());
        try {
            ResultList<PinganCardVO> list = settlementServiceApi.selectBindCardRecord(cardQuery);
            Logger.info("UnbindWithdrawAcctHandler doSubmitBefore PinganCardVO list:{}", DataMask.toJSONString(list));
            if (Objects.nonNull(list) && list.isSuccess() && !CollectionUtils.isEmpty(list.getDatalist())) {
                accountExecStepContext.setReqMessage(list);
            } else {
                Logger.info("UnbindWithdrawAcctHandler accountUpdateError query cardList is null!");
            }
        } catch (Exception e) {
            Logger.error("UnbindWithdrawAcctHandler doSubmitBefore customerCode:{}, customerType:{}, accountUpdateError:{}", req.getCustomerCode(), req.getCustomerType(), e);
            throw new AccountProxyException(ResponseEnum.SUBMIT_EXCEPTION);
        }
    }

    private UntieWithdrawVO formParam(PinganCardVO req) {
        UntieWithdrawVO vo = new UntieWithdrawVO();
        vo.setBankCardCode(req.getBankCardCode());
        //绑卡记录中店主店长customerCode全部已拼接customerType
        vo.setCustomerCode(req.getCustomerCode());
        if ("NMDL".equals(req.getCustomerType())){
            //解绑接口中店长customerCode不用拼接customerType
            vo.setCustomerCode(req.getCustomerCode().substring(4));
        }
        vo.setCustomerType(req.getCustomerType());
        return vo;
    }

    @Override
    protected void doSubmit(AccountExecStepContext accountExecStepContext) {
        AccountReq req = accountExecStepContext.getAccountReq();
        ResultList<PinganCardVO> list = (ResultList<PinganCardVO>) accountExecStepContext.getReqMessage();
        if (Objects.nonNull(list)) {
            boolean flag = true;
            try {
                for (PinganCardVO vo : list.getDatalist()) {
                    UntieWithdrawVO untieWithdrawVO = formParam(vo);
                    Logger.info("UnbindWithdrawAcctHandler doSubmit untieWithdrawVO:{}", DataMask.toJSONString(vo));
                    Result<String> result = merchantServiceApi.accountUntieWithdraw(untieWithdrawVO);
                    if (Objects.isNull(result) || !result.isSuccess()) {
                        flag = false;
                        Logger.info("UnbindWithdrawAcctHandler doSubmit untieWithdraw result:{}", DataMask.toJSONString(result));
                        break;
                    }
                }
            } catch (Exception e) {
                Logger.error("UnbindWithdrawAcctHandler doSubmit customerCode:{}, customerType:{}, accountUpdateError:{}", req.getCustomerCode(), req.getCustomerType(), e);
                throw new AccountProxyException(ResponseEnum.SUBMIT_EXCEPTION);
            }
            accountExecStepContext.setRespMessage(flag);
        }
    }

    @Override
    protected void doSubmitAfter(AccountExecStepContext accountExecStepContext) {
        AccountResp resp = accountExecStepContext.getAccountResp();
        resp.setStatus(ResultStatus.P.getCode());
        Boolean flag = (Boolean) accountExecStepContext.getRespMessage();
        if (Objects.nonNull(flag)) {
            if (flag) {
                resp.setStatus(ResultStatus.S.getCode());
                resp.setReplyMsg(ResultStatus.S.getDesc());
                resp.setReplyCode(CommonConstants.SUCC_CODE);
            } else {
                resp.setStatus(ResultStatus.F.getCode());
                resp.setReplyMsg(ResultStatus.F.getDesc());
                resp.setReplyCode(CommonConstants.FAIL_CODE);
                AccountReq req = accountExecStepContext.getAccountReq();
                Logger.error("UnbindWithdrawAcctHandler accountUpdateError, customerCode:{}, customerType:{}", req.getCustomerCode(), req.getCustomerType());
            }
        } else {
            //无绑卡记录
            resp.setStatus(ResultStatus.S.getCode());
            resp.setReplyMsg(ResultStatus.S.getDesc());
            resp.setReplyCode(CommonConstants.SUCC_CODE);
        }
    }
}
