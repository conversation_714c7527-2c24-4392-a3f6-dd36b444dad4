package com.akucun.account.proxy.service.tradeflow.script;

import com.aikucun.common2.log.Logger;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class TradeScripts {

    private final static Map<String, IBizInfoValidScript> bizInfoValidScripts = new ConcurrentHashMap<>();
    private final static Map<String, IPhaseBuildScript> phaseBuildScripts = new ConcurrentHashMap<>();
    private final static Map<String, IPhaseExecScript> phaseExecScripts = new ConcurrentHashMap<>();

    public static IBizInfoValidScript getBizInfoValidScript(String name) {
        IBizInfoValidScript script = bizInfoValidScripts.get(name);
        if(script != null) {
            return script;
        }
        synchronized (bizInfoValidScripts) {
            script = bizInfoValidScripts.get(name);
            if(script != null) {
                return script;
            }
            try {
                script = (IBizInfoValidScript) Class.forName("com.akucun.account.proxy.service.tradeflow.script.bizinfovalid." + name).newInstance();
            } catch (Exception e) {
                Logger.error("获取业务信息校验脚本异常：{}", name, e);
            }
            if(script != null) {
                bizInfoValidScripts.put(name, script);
            }
            return script;
        }
    }

    public static IPhaseBuildScript getPhaseBuildScript(String name) {
        IPhaseBuildScript script = phaseBuildScripts.get(name);
        if(script != null) {
            return script;
        }
        synchronized (phaseBuildScripts) {
            script = phaseBuildScripts.get(name);
            if(script != null) {
                return script;
            }
            try {
                script = (IPhaseBuildScript) Class.forName("com.akucun.account.proxy.service.tradeflow.script.phasebuild." + name).newInstance();
            } catch (Exception e) {
                Logger.error("获取阶段构建脚本异常：{}", name, e);
            }
            if(script != null) {
                phaseBuildScripts.put(name, script);
            }
            return script;
        }
    }

    public static IPhaseExecScript getPhaseExecScript(String name) {
        IPhaseExecScript script = phaseExecScripts.get(name);
        if(script != null) {
            return script;
        }
        synchronized (phaseExecScripts) {
            script = phaseExecScripts.get(name);
            if(script != null) {
                return script;
            }
            try {
                script = (IPhaseExecScript) Class.forName("com.akucun.account.proxy.service.tradeflow.script.phaseexec." + name).newInstance();
            } catch (Exception e) {
                Logger.error("获取阶段执行脚本异常：{}", name, e);
            }
            if(script != null) {
                phaseExecScripts.put(name, script);
            }
            return script;
        }
    }

}
