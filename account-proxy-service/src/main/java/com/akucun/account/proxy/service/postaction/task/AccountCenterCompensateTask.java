package com.akucun.account.proxy.service.postaction.task;

import com.aikucun.common2.base.Result;
import com.aikucun.common2.log.Logger;
import com.akucun.account.center.client.model.TradeInfo;
import com.akucun.account.center.feign.AccountCenterService;
import com.akucun.account.proxy.common.enums.PostActionTypes;
import com.akucun.account.proxy.dao.model.PostActionItem;
import com.akucun.account.proxy.service.postaction.common.AbsPostActionExecutor;
import com.akucun.fps.common.util.GsonUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @Author: silei
 * @Date: 2021/3/13
 * @desc: 账户中心交易补偿任务
 */
@Component
public class AccountCenterCompensateTask extends AbsPostActionExecutor {

    @Resource
    private AccountCenterService accountCenterService;

    @XxlJob("accountCenterCompensateTask")
    public ReturnT<String> execute(String param) {
        return this.executeEntrance();
    }

    @Override
    protected String getActionType() {
        return PostActionTypes.ACCOUNT_CENTER_COMPENSATE.getName();
    }

    @Override
    public Result<Void> execute(PostActionItem item) {
        Result<Void> result = Result.success();
        try {
            TradeInfo tradeInfo = GsonUtils.getInstance().fromJson(item.getParam(),TradeInfo.class);
            com.akucun.common.Result <Void> schedule = accountCenterService.dealTrade(tradeInfo);
            if (!schedule.isSuccess()){
                Logger.error("交易补偿任务执行失败"+schedule.getMessage());
                result.setSuccess(Boolean.FALSE);
                result.setMessage(schedule.getMessage());
            }
        } catch (Exception e) {
            result.setSuccess(false);
            result.setMessage(e.getMessage());
            Logger.error("交易补偿任务执行失败",e);
        }
        return result;
    }
}
