package com.akucun.account.proxy.service.tradeflow.domain;

import com.aikucun.common2.log.Logger;
import com.aikucun.common2.utils.datamasking.DataMask;
import com.akucun.account.proxy.facade.stub.others.trade.res.TradePhaseStatusEnum;
import com.akucun.account.proxy.service.common.SpringContextHolder;
import com.akucun.account.proxy.service.tradeflow.agreement.TradeAgreementPhase;
import com.akucun.account.proxy.service.tradeflow.constants.ErrorCodes;
import com.akucun.account.proxy.service.tradeflow.dto.PhaseExecResult;
import com.akucun.account.proxy.service.tradeflow.repository.TradePhaseRepository;
import com.akucun.account.proxy.service.tradeflow.script.TradeScripts;
import com.alibaba.fastjson.JSON;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class TradePhase {

    /**
     * id
     */
    private Long id;

    /**
     * 交易流程ID
     */
    private Long tradeId;

    /**
     * 阶段业务信息
     */
    private Object bizInfo;

    /**
     * 阶段状态
     */
    private TradePhaseStatusEnum status;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 阶段协议
     */
    private TradeAgreementPhase agreement;

    public void execute() {
        try {
            PhaseExecResult execResult = TradeScripts.getPhaseExecScript(this.agreement.getExecScript()).exec(this.bizInfo);
            this.status = execResult.getStatus();
            this.errorCode = execResult.getErrorCode();
            this.errorMessage = execResult.getErrorMessage();
        } catch (Exception e) {
            Logger.error("阶段执行异常，阶段ID：{}，业务信息：{}", id, DataMask.toJSONString(bizInfo), e);
            this.status = TradePhaseStatusEnum.EXCEPTION;
            this.errorCode = ErrorCodes.PHASE_EXCEPTION;
            this.errorMessage = "阶段执行异常";
        }
        SpringContextHolder.getBean(TradePhaseRepository.class).updateResult(this);
    }

}
