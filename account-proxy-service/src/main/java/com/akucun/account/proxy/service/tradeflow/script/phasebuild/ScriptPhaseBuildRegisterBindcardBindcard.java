package com.akucun.account.proxy.service.tradeflow.script.phasebuild;

import com.akucun.account.proxy.service.tradeflow.domain.Trade;
import com.akucun.account.proxy.service.tradeflow.dto.bizinfo.BizInfoBindcard;
import com.akucun.account.proxy.service.tradeflow.dto.bizinfo.BizInfoRegisterBindcard;
import com.akucun.account.proxy.service.tradeflow.script.IPhaseBuildScript;

public class ScriptPhaseBuildRegisterBindcardBindcard implements IPhaseBuildScript {

    @Override
    public Object build(Trade trade) {
        BizInfoRegisterBindcard registerBindcard = (BizInfoRegisterBindcard) trade.getBizInfo();
        BizInfoBindcard bindcard = new BizInfoBindcard();
        bindcard.setCustomerType(registerBindcard.getCustomerType());
        bindcard.setCustomerCode(registerBindcard.getCustomerCode());
        bindcard.setAccountType(registerBindcard.getAccountType());
        bindcard.setIdType(registerBindcard.getIdType());
        bindcard.setIdCode(registerBindcard.getIdCode());
        bindcard.setCustomerName(registerBindcard.getCustomerName());
        bindcard.setBindWay(registerBindcard.getBindWay());
        bindcard.setSbankCode(registerBindcard.getSbankCode());
        bindcard.setSbankName(registerBindcard.getSbankName());
        bindcard.setBankCode(registerBindcard.getBankCode());
        bindcard.setBankName(registerBindcard.getBankName());
        bindcard.setBankCardCode(registerBindcard.getBankCardCode());
        bindcard.setMobilePhone(registerBindcard.getMobilePhone());
        return bindcard;
    }

}
