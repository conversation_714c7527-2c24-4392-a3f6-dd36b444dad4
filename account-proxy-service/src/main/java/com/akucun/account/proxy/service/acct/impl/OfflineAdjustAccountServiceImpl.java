/*
 * @Author: Lee
 * @Date: 2025-04-16 15:49:46
 * @Description: 线下调账服务实现类
 * @License: Copyright (c) 2025, Lee
 */
package com.akucun.account.proxy.service.acct.impl;

import com.aikucun.common2.utils.DateUtils;
import com.akucun.account.center.client.model.TradeInfo;
import com.akucun.account.center.client.model.account.AccountBookDO;
import com.akucun.account.proxy.client.bonus.AccountMemberClient;
import com.akucun.account.proxy.client.center.AccountCenterClient;
import com.akucun.account.proxy.client.marketaccount.MerchantBondAccountOperateClient;
import com.akucun.account.proxy.client.marketaccount.MerchantMarketAccountOperateClient;
import com.akucun.account.proxy.common.constant.CustomerType;
import com.akucun.account.proxy.common.exception.AccountProxyException;
import com.akucun.account.proxy.dao.mapper.OfflineAdjustAccountMapper;
import com.akucun.account.proxy.dao.model.OfflineAdjustAccount;
import com.akucun.account.proxy.facade.stub.enums.AccountKeyConstants;
import com.akucun.account.proxy.facade.stub.enums.OfflineAdjustAccountAuditStatusEnum;
import com.akucun.account.proxy.facade.stub.enums.OfflineAdjustAccountStatusEnum;
import com.akucun.account.proxy.facade.stub.others.account.req.OfflineAdjustAccountPageQueryReq;
import com.akucun.account.proxy.facade.stub.others.account.req.OfflineAdjustAccountSubmitReq;
import com.akucun.account.proxy.facade.stub.others.account.vo.OfflineAdjustAccountBatchStatisticVO;
import com.akucun.account.proxy.facade.stub.others.account.vo.OfflineAdjustAccountVO;
import com.akucun.account.proxy.service.acct.AccountService;
import com.akucun.account.proxy.service.acct.OfflineAdjustAccountService;
import com.akucun.account.proxy.service.acct.bo.OfflineAdjustAccountMappingBO;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.google.common.collect.Lists;
import com.mengxiang.base.common.log.Logger;
import com.mengxiang.base.common.model.result.Pagination;
import com.mengxiang.base.common.model.result.Result;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class OfflineAdjustAccountServiceImpl implements OfflineAdjustAccountService {

    @Autowired
    private OfflineAdjustAccountMapper offlineAdjustAccountMapper;

    // 废弃, 改用supportAccountTradeTypeMap
    @Deprecated
    @ApolloJsonValue("${offline.adjust.account.support.account.type.key.list}")
    private List<String> supportAccountTypeKeyList;

    @ApolloJsonValue("${offline.adjust.account.support.account.trade.type.mapping}")
    private List<OfflineAdjustAccountMappingBO> supportAccountTradeTypeMappingList;

    @Autowired
    private AccountCenterClient accountCenterClient;

    @Autowired
    private AccountMemberClient accountMemberClient;

    @Autowired
    private AccountService accountService;

    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private MerchantMarketAccountOperateClient merchantMarketAccountOperateClient;

    @Resource
    private MerchantBondAccountOperateClient merchantBondAccountOperateClient;

    @Override
    public Result<Void> submit(OfflineAdjustAccountSubmitReq addVO) {
        try {
            // 校验账户类型是否支持
            OfflineAdjustAccountMappingBO supportAccountMappingBO =  supportAccountTradeTypeMappingList.stream().filter(mappingBO ->
                    StringUtils.equals(mappingBO.getAccountTypeKey(), addVO.getAccountTypeKey())).findFirst().orElse(null);
            if (supportAccountMappingBO == null) {
                return Result.error("A0506","不支持的账户类型");
            }

            // 校验交易类型是否支持, 如果映射的交易类型不为空, 则需要校验, 如果映射的交易类型为空则默认都支持
            List<String> supportTradeTypeList = supportAccountMappingBO.getTradeTypeList();
            if (CollectionUtils.isNotEmpty(supportTradeTypeList)) {
                if (!supportTradeTypeList.contains(addVO.getTradeType())) {
                    return Result.error("A0506","当前账户不支持该交易类型调账");
                }
            }

            // 查询是否存在
            OfflineAdjustAccount exist = offlineAdjustAccountMapper.selectByUniqueKey(addVO.getCustomerCode(), addVO.getAccountTypeKey(), addVO.getTradeNo(), addVO.getTradeType());
            if (exist != null) {
                return Result.error("A0506","数据已存在");
            }

            // 前置校验
            Result<Void> beforeCheckResult = beforeCheck(addVO);
            if (!beforeCheckResult.isSuccess()) {
                return beforeCheckResult;
            }

            OfflineAdjustAccount insert = new OfflineAdjustAccount();
            insert.setCustomerCode(addVO.getCustomerCode());
            insert.setCustomerType(addVO.getCustomerType());
            insert.setAccountTypeKey(addVO.getAccountTypeKey());
            insert.setTradeNo(addVO.getTradeNo());
            insert.setSourceBillNo(addVO.getSourceBillNo());
            insert.setTradeType(addVO.getTradeType());
            insert.setAmount(addVO.getAmount());
            insert.setRemark(addVO.getRemark());
            insert.setAuditStatus(OfflineAdjustAccountAuditStatusEnum.PENDING.getCode());
            insert.setAdjustStatus(OfflineAdjustAccountStatusEnum.WAIT_ADJUST.getCode());
            insert.setIsDelete((byte) 0);
            insert.setCreateBy(addVO.getCreateBy());
            int count = offlineAdjustAccountMapper.insert(insert);
            if (count == 0) {
                return Result.error("新增线下调账记录失败");
            }
            return Result.success();
        } catch (DuplicateKeyException e) {
            Logger.warn("新增线下调账记录数据已存在", e);
            return Result.error("A0506","数据已存在");
        } catch (Exception e) {
            Logger.error("新增线下调账记录异常", e);
            return Result.error("系统异常");
        }
    }

    @Override
    public Result<Pagination<OfflineAdjustAccountVO>> pageQuery(OfflineAdjustAccountPageQueryReq queryReq) {
        try {
            Page<OfflineAdjustAccount> page =new Page<>(queryReq.getPageIndex(),queryReq.getPageSize());
            LambdaQueryWrapper<OfflineAdjustAccount> wrapper = normalLambdaQueryWrapper(queryReq, true);
            Page<OfflineAdjustAccount> pageResult = offlineAdjustAccountMapper.selectPage(page, wrapper);

            List<OfflineAdjustAccountVO> voList = pageResult.getRecords().stream().map(item -> {
                OfflineAdjustAccountVO vo = new OfflineAdjustAccountVO();
                BeanUtils.copyProperties(item, vo);
                return vo;
            }).collect(Collectors.toList());
            return Result.success(new Pagination<>(queryReq, Long.valueOf(pageResult.getTotal()), voList));
        } catch (Exception e) {
            Logger.error("分页查询线下调账记录异常", e);
            return Result.error("系统异常");
        }
    }

    /**
     * 构建查询条件, 需要同步修改normalQueryWrapper
     * @param queryReq
     * @param isOrder
     * @return
     */
    private LambdaQueryWrapper<OfflineAdjustAccount> normalLambdaQueryWrapper(OfflineAdjustAccountPageQueryReq queryReq, boolean isOrder) {
        LambdaQueryWrapper<OfflineAdjustAccount> wrapper = new LambdaQueryWrapper<>();
        if(StringUtils.isNotBlank(queryReq.getCustomerCode())){
            wrapper.eq(OfflineAdjustAccount::getCustomerCode,queryReq.getCustomerCode());
        }
        if(queryReq.getAuditStatus() != null){
            wrapper.eq(OfflineAdjustAccount::getAuditStatus,queryReq.getAuditStatus());
        }
        if(StringUtils.isNotBlank(queryReq.getTradeNo())){
            wrapper.eq(OfflineAdjustAccount::getTradeNo,queryReq.getTradeNo());
        }
        if (StringUtils.isNotBlank(queryReq.getSourceBillNo())) {
            wrapper.eq(OfflineAdjustAccount::getSourceBillNo,queryReq.getSourceBillNo());
        }
        if(StringUtils.isNotBlank(queryReq.getAccountTypeKey())){
            wrapper.eq(OfflineAdjustAccount::getAccountTypeKey,queryReq.getAccountTypeKey());
        }
        if(StringUtils.isNotBlank(queryReq.getTradeType())){
            wrapper.eq(OfflineAdjustAccount::getTradeType,queryReq.getTradeType());
        }
        if(StringUtils.isNotBlank(queryReq.getRemark())){
            wrapper.eq(OfflineAdjustAccount::getRemark,queryReq.getRemark());
        }
        if (StringUtils.isNotBlank(queryReq.getCreateTimeStart())) {
            wrapper.ge(OfflineAdjustAccount::getCreateTime,queryReq.getCreateTimeStart());
        }
        if (StringUtils.isNotBlank(queryReq.getCreateTimeEnd())) {
            wrapper.le(OfflineAdjustAccount::getCreateTime,queryReq.getCreateTimeEnd());
        }
        if (ObjectUtils.isNotEmpty(queryReq.getMinId())) {
            wrapper.gt(OfflineAdjustAccount::getId, queryReq.getMinId());
        }
        if (ObjectUtils.isNotEmpty(queryReq.getAdjustStatus())) {
            wrapper.eq(OfflineAdjustAccount::getAdjustStatus, queryReq.getAdjustStatus());
        }
        wrapper.eq(OfflineAdjustAccount::getIsDelete, 0);
        if (isOrder) {
            if (StringUtils.isNotBlank(queryReq.getOrderBy())) {
                wrapper.last(" order by " + queryReq.getOrderBy());
            } else {
                wrapper.orderByDesc(OfflineAdjustAccount::getId);
            }
        }
        return wrapper;
    }

    /**
     * 构建查询条件, 需要同步修改normalLambdaQueryWrapper
     * @param queryReq
     * @param isOrder
     * @return
     */
    private QueryWrapper<OfflineAdjustAccount> normalQueryWrapper(OfflineAdjustAccountPageQueryReq queryReq, boolean isOrder) {
        QueryWrapper<OfflineAdjustAccount> wrapper = new QueryWrapper<>();
        if(StringUtils.isNotBlank(queryReq.getCustomerCode())){
            wrapper.eq("customer_code",queryReq.getCustomerCode());
        }
        if(queryReq.getAuditStatus() != null){
            wrapper.eq("audit_status",queryReq.getAuditStatus());
        }
        if(StringUtils.isNotBlank(queryReq.getTradeNo())){
            wrapper.eq("trade_no",queryReq.getTradeNo());
        }
        if (StringUtils.isNotBlank(queryReq.getSourceBillNo())) {
            wrapper.eq("source_bill_no",queryReq.getSourceBillNo());
        }
        if(StringUtils.isNotBlank(queryReq.getAccountTypeKey())){
            wrapper.eq("account_type_key",queryReq.getAccountTypeKey());
        }
        if(StringUtils.isNotBlank(queryReq.getTradeType())){
            wrapper.eq("trade_type",queryReq.getTradeType());
        }
        if(StringUtils.isNotBlank(queryReq.getRemark())){
            wrapper.eq("remark",queryReq.getRemark());
        }
        if (StringUtils.isNotBlank(queryReq.getCreateTimeStart())) {
            wrapper.ge("create_time",queryReq.getCreateTimeStart());
        }
        if (StringUtils.isNotBlank(queryReq.getCreateTimeEnd())) {
            wrapper.le("create_time",queryReq.getCreateTimeEnd());
        }
        if (ObjectUtils.isNotEmpty(queryReq.getMinId())) {
            wrapper.gt("id", queryReq.getMinId());
        }
        if (ObjectUtils.isNotEmpty(queryReq.getAdjustStatus())) {
            wrapper.eq("adjust_status", queryReq.getAdjustStatus());
        }
        wrapper.eq("is_delete", 0);
        if (isOrder) {
            if (StringUtils.isNotBlank(queryReq.getOrderBy())) {
                wrapper.last(" order by " + queryReq.getOrderBy());
            } else {
                wrapper.orderByDesc("id");
            }
        }
        return wrapper;
    }

    @Override
    public Result<OfflineAdjustAccountBatchStatisticVO> statistic(OfflineAdjustAccountPageQueryReq queryReq) {
        try {
            // 1. 构建查询条件
            QueryWrapper<OfflineAdjustAccount> statisticWrapper = normalQueryWrapper(queryReq, false);
            // 2. 聚合统计
            statisticWrapper.select("COUNT(1) AS count", "COALESCE(SUM(amount), 0) AS sum");
            // 3. 执行查询
            Map<String, Object> result = offlineAdjustAccountMapper.selectMaps(statisticWrapper).get(0);
            // 4. 封装统计结果
            OfflineAdjustAccountBatchStatisticVO statisticVO = new OfflineAdjustAccountBatchStatisticVO();
            statisticVO.setCount(Long.valueOf(result.get("count").toString()));
            statisticVO.setAmount(new BigDecimal(result.get("sum").toString()));
            return Result.success(statisticVO);
        } catch (Exception e) {
            Logger.error("统计线下调账记录异常", e);
            return Result.error("系统异常");
        }
    }

    @Override
    public Result<Integer> batchDelete(List<Long> ids, String operator) {
        try {
            // 根据ID查询 + 审核状态查询, 只能删除待审核
            LambdaQueryWrapper<OfflineAdjustAccount> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(OfflineAdjustAccount::getId, ids);
            queryWrapper.in(OfflineAdjustAccount::getAuditStatus, Arrays.asList(OfflineAdjustAccountAuditStatusEnum.PENDING.getCode()));
            queryWrapper.eq(OfflineAdjustAccount::getIsDelete, 0);
            int count = offlineAdjustAccountMapper.selectCount(queryWrapper);
            if (count != ids.size()) {
                return Result.error("存在审核通过或审核拒绝或已删除的记录");
            }
            int deleteCount = offlineAdjustAccountMapper.batchDelete(ids, operator, DateUtils.format(new Date(), "yyyyMMddHHmmss"));
            return Result.success(deleteCount);
        } catch (Exception e) {
            Logger.error("批量删除线下调账记录异常", e);
            return Result.error("系统异常");
        }
    }

    @Override
    public Result<Void> onKeyDelete(OfflineAdjustAccountPageQueryReq queryReq, String operator) {
        try {
            LambdaQueryWrapper<OfflineAdjustAccount> queryWrapper = normalLambdaQueryWrapper(queryReq, true);
            queryWrapper.select(OfflineAdjustAccount::getId);
            List<OfflineAdjustAccount> waitDeleteList = offlineAdjustAccountMapper.selectList(queryWrapper);
            Logger.info("一键删除线下调账记录开始，待删除记录数量：{}", waitDeleteList.size());
            if (CollectionUtils.isEmpty(waitDeleteList)) {
                return Result.success();
            }

            String deleteFlag = DateUtils.format(new Date(), "yyyyMMddHHmmss");
            return transactionTemplate.execute(new TransactionCallback<Result<Void>>() {
                @Override
                public Result<Void> doInTransaction(TransactionStatus status) {
                    try {
                        List<List<OfflineAdjustAccount>> partitions = Lists.partition(waitDeleteList, 1000);
                        for (List<OfflineAdjustAccount> partition : partitions) {
                            List<Long> ids = partition.stream().map(OfflineAdjustAccount::getId).collect(Collectors.toList());
                            offlineAdjustAccountMapper.batchDelete(ids, operator, deleteFlag);
                        }
                        return Result.success();
                    } catch (Exception e) {
                        status.setRollbackOnly();
                        Logger.error("一键删除线下调账记录数据库操作异常", e);
                        return Result.error(e);
                    }
                }
            });
        } catch (Exception e) {
            Logger.error("一键删除线下调账记录异常", e);
            return Result.error("系统异常");
        }
    }
    @Override
    public Result<Void> auditPass(Long id, String operator) {
        try {
            // 根据ID查询 + 审核状态查询, 只能审核待审核
            LambdaQueryWrapper<OfflineAdjustAccount> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(OfflineAdjustAccount::getId, id);
            queryWrapper.eq(OfflineAdjustAccount::getAuditStatus, OfflineAdjustAccountAuditStatusEnum.PENDING.getCode());
            queryWrapper.eq(OfflineAdjustAccount::getIsDelete, 0);
            OfflineAdjustAccount offlineAdjustAccount = offlineAdjustAccountMapper.selectOne(queryWrapper);
            if (offlineAdjustAccount == null) {
                return Result.error("待审核记录不存在");
            }

            // 1、更新审核状态
            Result<Void> updateAuditResult = transactionTemplate.execute(new TransactionCallback<Result<Void>>() {
                @Override
                public Result<Void> doInTransaction(TransactionStatus status) {
                    try {
                        OfflineAdjustAccount update = new OfflineAdjustAccount();
                        update.setAuditStatus(OfflineAdjustAccountAuditStatusEnum.APPROVED.getCode());
                        update.setAuditTime(new Date());
                        update.setUpdateBy(operator);
                        update.setAuditBy(operator);
                        if (offlineAdjustAccountMapper.update(update, queryWrapper) != 1) {
                            status.setRollbackOnly();
                            return Result.error("审核操作更新审核状态发生并发更新");
                        }
                        return Result.success();
                    } catch (Exception e) {
                        status.setRollbackOnly();
                        Logger.error("审核操作更新审核状态发生异常", e);
                        return Result.error("审核操作更新审核状态发生异常");
                    }
                }
            });

            // 失败就返回
            if (!updateAuditResult.isSuccess()) {
                return updateAuditResult;
            }

            // 2、执行调账 & 处理结果
            return transactionTemplate.execute(new TransactionCallback<Result<Void>>() {
                @Override
                public Result<Void> doInTransaction(TransactionStatus status) {
                    try {
                        // 执行调账
                        Result<Void> result = executeAdjust(offlineAdjustAccount);

                        // 更新调账结果
                        OfflineAdjustAccount updateExecuteResultObj = new OfflineAdjustAccount();
                        updateExecuteResultObj.setId(id);
                        updateExecuteResultObj.setUpdateBy(operator);
                        if (!result.isSuccess()) {
                            updateExecuteResultObj.setAdjustStatus(OfflineAdjustAccountStatusEnum.ADJUST_FAILED.getCode());
                            updateExecuteResultObj.setFailReason(result.getMessage());
                        } else {
                            updateExecuteResultObj.setAdjustStatus(OfflineAdjustAccountStatusEnum.ADJUST_SUCCESS.getCode());
                        }
                        if (updateAdjustStatusByWaitAdjust(updateExecuteResultObj) != 1) {
                            status.setRollbackOnly();
                            return Result.error("审核线下调账更新调账状态发生并发更新");
                        }
                        return Result.success();
                    } catch (Exception e) {
                        status.setRollbackOnly();
                        Logger.error("审核线下调账更新调账状态发生异常", e);
                        return Result.error("审核线下调账更新调账状态发生异常");
                    }
                }
            });
        } catch (Exception e) {
            Logger.error("审核线下调账异常", e);
            return Result.error("系统异常");
        }
    }


    /**
     * 更新调账状态
     * @param updateObj
     * @return
     */
    private int updateAdjustStatusByWaitAdjust(OfflineAdjustAccount updateObj) {
        return offlineAdjustAccountMapper.update(null, new LambdaUpdateWrapper<OfflineAdjustAccount>()
                .eq(OfflineAdjustAccount::getId, updateObj.getId())
                .eq(OfflineAdjustAccount::getAuditStatus, OfflineAdjustAccountAuditStatusEnum.APPROVED.getCode())
                .eq(OfflineAdjustAccount::getAdjustStatus, OfflineAdjustAccountStatusEnum.WAIT_ADJUST.getCode())
                .eq(OfflineAdjustAccount::getIsDelete, 0)
                .set(OfflineAdjustAccount::getAdjustStatus, updateObj.getAdjustStatus())
                .set(OfflineAdjustAccount::getFailReason, updateObj.getFailReason())
                .set(OfflineAdjustAccount::getUpdateBy, updateObj.getUpdateBy()));
    }

    /**
     * 更新调账结果
     * @param updateObj
     * @return
     */
    private int updateAdjustResultByAdjustFailed(OfflineAdjustAccount updateObj) {
        return offlineAdjustAccountMapper.update(null, new LambdaUpdateWrapper<OfflineAdjustAccount>()
                    .eq(OfflineAdjustAccount::getId, updateObj.getId())
                    .eq(OfflineAdjustAccount::getAuditStatus, OfflineAdjustAccountAuditStatusEnum.APPROVED.getCode())
                    .eq(OfflineAdjustAccount::getAdjustStatus, OfflineAdjustAccountStatusEnum.ADJUST_FAILED.getCode())
                    .eq(OfflineAdjustAccount::getIsDelete, 0)
                    .set(OfflineAdjustAccount::getUpdateTime, new Date())
                    .set(OfflineAdjustAccount::getAdjustStatus, updateObj.getAdjustStatus())
                    .set(OfflineAdjustAccount::getFailReason, updateObj.getFailReason())
                    .set(OfflineAdjustAccount::getUpdateBy, updateObj.getUpdateBy()));
    }

    @Override
    public Result<Void> auditRefuse(Long id, String operator) {
        try {
            // 根据ID查询 + 审核状态查询, 只能审核待审核
            LambdaUpdateWrapper<OfflineAdjustAccount> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(OfflineAdjustAccount::getId, id);
            updateWrapper.eq(OfflineAdjustAccount::getAuditStatus, OfflineAdjustAccountAuditStatusEnum.PENDING.getCode());
            updateWrapper.eq(OfflineAdjustAccount::getIsDelete, 0);

            OfflineAdjustAccount update = new OfflineAdjustAccount();
            update.setAuditStatus(OfflineAdjustAccountAuditStatusEnum.REJECTED.getCode());
            update.setAuditTime(new Date());
            update.setAuditBy(operator);
            update.setUpdateBy(operator);

            // 执行更新
            return transactionTemplate.execute(new TransactionCallback<Result<Void>>() {
                @Override
                public Result<Void> doInTransaction(TransactionStatus status) {
                    try {
                        if (offlineAdjustAccountMapper.update(update, updateWrapper) != 1) {
                            status.setRollbackOnly();
                            return Result.error("驳回线下调账更新审核状态发生并发更新");
                        }
                        return Result.success();
                    } catch (Exception e) {
                        status.setRollbackOnly();
                        Logger.error("驳回线下调账更新审核状态发生异常", e);
                        return Result.error("驳回线下调账更新审核状态发生异常");
                    }
                }
            });
        } catch (Exception e) {
            Logger.error("审核拒绝线下调账异常", e);
            return Result.error("系统异常");
        }
    }

    @Override
    public Result<Void> retry(Long id, String operator) {
        try {
            // 根据ID查询 + 审核状态查询, 只能重试待调账
            LambdaQueryWrapper<OfflineAdjustAccount> queryWrapper = new LambdaQueryWrapper<>(); 
            queryWrapper.eq(OfflineAdjustAccount::getId, id);
            queryWrapper.eq(OfflineAdjustAccount::getAuditStatus, OfflineAdjustAccountAuditStatusEnum.APPROVED.getCode());
            queryWrapper.eq(OfflineAdjustAccount::getAdjustStatus, OfflineAdjustAccountStatusEnum.ADJUST_FAILED.getCode());
            queryWrapper.eq(OfflineAdjustAccount::getIsDelete, 0);
            OfflineAdjustAccount offlineAdjustAccount = offlineAdjustAccountMapper.selectOne(queryWrapper);
            if (offlineAdjustAccount == null) {
                return Result.error("待异常重试的记录不存在");
            }

            // 执行更新
            return transactionTemplate.execute(new TransactionCallback<Result<Void>>() {
                @Override
                public Result<Void> doInTransaction(TransactionStatus status) {
                    try {
                        // 1、执行调账
                        Result<Void> result = executeAdjust(offlineAdjustAccount);

                        // 2、更新调账结果
                        OfflineAdjustAccount updateObj = new OfflineAdjustAccount();
                        updateObj.setId(id);
                        updateObj.setUpdateBy(operator);
                        if (!result.isSuccess()) {
                            updateObj.setAdjustStatus(OfflineAdjustAccountStatusEnum.ADJUST_FAILED.getCode());
                            updateObj.setFailReason(result.getMessage());
                        } else {
                            updateObj.setAdjustStatus(OfflineAdjustAccountStatusEnum.ADJUST_SUCCESS.getCode());
                        }
                        if (updateAdjustResultByAdjustFailed(updateObj) != 1) {
                            return Result.error("重试线下调账发生并发更新");
                        }
                        return Result.success();
                    } catch (Exception e) {
                        status.setRollbackOnly();
                        Logger.error("重试线下调账发生系统异常", e);
                        return Result.error("重试线下调账发生系统异常");
                    }
                }   
            });
        } catch (Exception e) {
            Logger.error("线下调账异常重试异常", e);
            return Result.error("系统异常");
        }      
    }

    private Result<Void> beforeCheck(OfflineAdjustAccountSubmitReq addVO) {
        try {
            String accountCustomerCode = convertAccountCustomerCode(addVO.getCustomerCode(), addVO.getAccountTypeKey());
            AccountBookDO accountBook = accountCenterClient.queryAccountBook(accountCustomerCode, addVO.getAccountTypeKey());
            if(accountBook == null) {
                return Result.error("账户不存在");
            }

            if (accountBook.isOptionFreeze()) {
                return Result.error("账户被冻结");
            }

            // 老奖励金账户需要校验会员是否存在
            if (AccountKeyConstants.AWARD.getName().equals(addVO.getAccountTypeKey())) {
                String userId = accountMemberClient.convertCustomerCodeToUserId(accountCustomerCode);
                if (StringUtils.isBlank(userId)) {
                    return Result.error("会员账户不存在");
                }
            }
            return Result.success();
        } catch (Exception e) {
            Logger.error("线下调账前置校验异常", e);
            return Result.error("系统异常");
        }
    }

    /**
     * 转换账户中心账户编码
     * @param customerCode
     * @param accountTypeKey
     * @return
     */
    private String convertAccountCustomerCode(String customerCode, String accountTypeKey) {
        if(AccountKeyConstants.OP.getName().equals(accountTypeKey)) {
            return CustomerType.OP.getName() + customerCode;
        }
        if(AccountKeyConstants.NM.getName().equals(accountTypeKey)) {
            return CustomerType.NM.getName() + customerCode;
        }
        return customerCode;
    }

    /**
     * 执行调账
     * @param offlineAdjustAccount
     * @return
     */
    private Result<Void> executeAdjust(OfflineAdjustAccount offlineAdjustAccount) {
        try {
            // 账户中心交易编码
            String accountCustomerCode = convertAccountCustomerCode(offlineAdjustAccount.getCustomerCode(), offlineAdjustAccount.getAccountTypeKey());
            // 商家营销账户、商家保证金账户需要单独处理
            if(AccountKeyConstants.SH_MARKET.getName().equals(offlineAdjustAccount.getAccountTypeKey())) {
                merchantMarketAccountOperateClient.marketUpdateAccount(offlineAdjustAccount.getAmount(), offlineAdjustAccount.getTradeType(),
                        offlineAdjustAccount.getTradeNo(), offlineAdjustAccount.getSourceBillNo(), accountCustomerCode,
                        null, null, offlineAdjustAccount.getRemark(), false);
            } else if(AccountKeyConstants.SH_BOND.getName().equals(offlineAdjustAccount.getAccountTypeKey())) {
                merchantBondAccountOperateClient.changeBondAccountAmount(accountCustomerCode,
                        offlineAdjustAccount.getTradeType(), offlineAdjustAccount.getAmount(), offlineAdjustAccount.getTradeNo(),
                        offlineAdjustAccount.getSourceBillNo(), offlineAdjustAccount.getRemark(), offlineAdjustAccount.getCreateBy());
            } else {
                TradeInfo tradeInfo = new TradeInfo();
                tradeInfo.setCustomerCode(accountCustomerCode);
                tradeInfo.setAccountTypeKey(offlineAdjustAccount.getAccountTypeKey());
                tradeInfo.setTradeType(offlineAdjustAccount.getTradeType());
                tradeInfo.setTradeNo(offlineAdjustAccount.getTradeNo());
                tradeInfo.setSourceBillNo(offlineAdjustAccount.getSourceBillNo());
                tradeInfo.setAmount(offlineAdjustAccount.getAmount());
                tradeInfo.setRemark(offlineAdjustAccount.getRemark());
                Logger.info("执行调账, tradeInfo:{}", JSON.toJSONString(tradeInfo));
                com.akucun.common.Result<Void> tradeResult = accountService.dealTrade(tradeInfo);
                Logger.info("执行调账结果, tradeResult:{}, tradeInfo:{}", JSON.toJSONString(tradeResult), JSON.toJSONString(tradeInfo));
                if (!tradeResult.isSuccess()) {
                    return Result.error("调账失败:" + tradeResult.getMessage());
                }
            }
            return Result.success();
        } catch (AccountProxyException e) {
            Logger.warn("执行调账发生业务异常, tradeNo:{}", offlineAdjustAccount.getTradeNo(), e);
            return Result.error(e.getMessage());
        } catch (Exception e) {
            Logger.error("执行调账发生异常, tradeNo:{}", offlineAdjustAccount.getTradeNo(), e);
            return Result.error("系统异常");
        }
    }
}
