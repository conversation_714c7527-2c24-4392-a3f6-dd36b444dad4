package com.akucun.account.proxy.service.acct.impl;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.dao.mapper.WechatBindInfoMapper;
import com.akucun.account.proxy.dao.model.WechatBindInfo;
import com.akucun.account.proxy.facade.stub.others.account.vo.WechatInfoBindVO;
import com.akucun.account.proxy.service.acct.WechatAuthService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mengxiang.base.common.log.Logger;
import com.mengxiang.base.common.utils.datamasking.DataMask;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * @Author: silei
 * @Date: 2021/5/2
 * @desc:
 */
@Service
public class WechatAuthServiceImpl extends ServiceImpl<WechatBindInfoMapper, WechatBindInfo> implements WechatAuthService {

    @Override
    public Result<Void> bindInfo(WechatInfoBindVO vo) {
        Logger.info("WechatAuthServiceImpl bindInfo vo :{}", DataMask.toJSONString(vo));
//        LambdaQueryWrapper<WechatBindInfo> wrapper = new LambdaQueryWrapper<>();
//        wrapper.eq(WechatBindInfo::getCustomerCode, vo.getCustomerCode())
//                .eq(WechatBindInfo::getCustomerType, vo.getCustomerType())
//                .eq(WechatBindInfo::getStatus, 0);
//        WechatBindInfo info = baseMapper.selectOne(wrapper);
//        if (info != null) {
//            return Result.success();
//        }
        //已存在绑定关系，解绑再重新绑定
        unbind(vo.getCustomerCode(), vo.getCustomerType());

        WechatBindInfo info = new WechatBindInfo();
        BeanUtils.copyProperties(vo, info);
        info.setStatus(0);
        baseMapper.insert(info);
        Logger.info("WechatAuthServiceImpl bindInfo success customerCode :{}", vo.getCustomerCode());
        return Result.success();
    }

    @Override
    public Result<Void> unbind(String customerCode, String customerType) {
        Logger.info("WechatAuthServiceImpl bindInfo customerCode :{}, customerType:{}", customerCode, customerType);
        LambdaQueryWrapper<WechatBindInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WechatBindInfo::getCustomerCode, customerCode)
                .eq(WechatBindInfo::getCustomerType, customerType)
                .eq(WechatBindInfo::getStatus, 0);
        WechatBindInfo info = baseMapper.selectOne(wrapper);
        if (info == null) {
            return Result.success();
        }
        info.setStatus(1);

        this.baseMapper.update(info, wrapper);
        return Result.success();
    }

    @Override
    public Result<WechatInfoBindVO> queryBindInfo(String customerCode, String customerType) {
        LambdaQueryWrapper<WechatBindInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WechatBindInfo::getCustomerCode, customerCode)
                .eq(WechatBindInfo::getCustomerType, customerType)
                .eq(WechatBindInfo::getStatus, 0);
        WechatBindInfo info = baseMapper.selectOne(wrapper);
        if (info != null) {
            WechatInfoBindVO vo = new WechatInfoBindVO();
            BeanUtils.copyProperties(info, vo);
            return Result.success(vo);
        }
        return Result.success(null);
    }
}
