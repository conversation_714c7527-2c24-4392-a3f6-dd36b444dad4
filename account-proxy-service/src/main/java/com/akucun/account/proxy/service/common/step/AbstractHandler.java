package com.akucun.account.proxy.service.common.step;

import com.akucun.account.proxy.common.enums.ResultStatus;
import com.akucun.account.proxy.common.exception.AccountProxyException;
import com.akucun.account.proxy.service.common.bo.AccountExecStepContext;
import com.mengxiang.base.common.log.Logger;

/**
 * @Author: silei
 * @Date: 2020/8/26
 * @desc:
 */
public abstract class AbstractHandler {

    protected abstract boolean preCheck(AccountExecStepContext accountExecStepContext);

    protected abstract void doSubmitBefore(AccountExecStepContext accountExecStepContext);

    protected abstract void doSubmit(AccountExecStepContext accountExecStepContext);

    protected abstract void doSubmitAfter(AccountExecStepContext accountExecStepContext) throws Exception;


    public void exec(AccountExecStepContext accountExecStepContext) {

        if (preCheck(accountExecStepContext)) {
            try {
                doSubmitBefore(accountExecStepContext);
                doSubmit(accountExecStepContext);
                doSubmitAfter(accountExecStepContext);
            } catch (AccountProxyException e) {
                Logger.error("AbstractHandler exec exception AccountProxyException:", e);
                accountExecStepContext.setException(e);
                accountExecStepContext.getAccountResp().setStatus(ResultStatus.P.getCode());
                //异常是否直接失败
            } catch (Exception e) {
                Logger.error("AbstractHandler exec exception Exception", e);
                accountExecStepContext.getAccountResp().setStatus(ResultStatus.P.getCode());
                accountExecStepContext.setException(e);
            }
        }

    }

    public String getDetailOrderNo(String str, String suffix) {
        return str + "D" + suffix;
    }


}
