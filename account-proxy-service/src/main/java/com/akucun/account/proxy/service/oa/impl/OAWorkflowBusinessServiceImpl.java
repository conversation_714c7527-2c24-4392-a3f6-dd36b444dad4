package com.akucun.account.proxy.service.oa.impl;

import com.aikucun.common2.base.Result;
import com.aikucun.common2.base.exception.IErrorCode;
import com.aikucun.common2.log.Logger;
import com.aikucun.common2.utils.DateUtils;
import com.akucun.account.proxy.facade.stub.enums.OAWorkflowBusinessTypeEnum;
import com.akucun.account.proxy.facade.stub.others.oa.req.OAWorkflowCreateRequest;
import com.akucun.account.proxy.facade.stub.others.oa.req.OAWorkflowDetailTable;
import com.akucun.account.proxy.facade.stub.others.oa.req.OAWorkflowTableField;
import com.akucun.account.proxy.facade.stub.others.oa.req.business.MultiSupplierCorporatePaymentRequest;
import com.akucun.account.proxy.facade.stub.others.oa.resp.OAWorkflowCreateResponse;
import com.akucun.account.proxy.service.oa.OAWorkflowBaseService;
import com.akucun.account.proxy.service.oa.OAWorkflowBusinessService;
import com.alibaba.dubbo.common.URL;
import com.alibaba.fastjson.JSON;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Create on : 2025/1/14 21:11
 **/
@Service
public class OAWorkflowBusinessServiceImpl implements OAWorkflowBusinessService {

    @Autowired
    private OAWorkflowBaseService oaWorkflowBaseService;

    @Value("${multi.supplier.corporate.payment.workflow.id:8241}")
    private String multiSupplierCorporatePaymentWorkflowId;

    @Value("${multi.supplier.corporate.payment.workflow.title.prefix:613-多供应商对公付款}")
    private String multiSupplierCorporatePaymentWorkflowTitlePrefix;

    @Override
    public Result<String> multiSupplierCorporatePayment(MultiSupplierCorporatePaymentRequest request) {
        Result<String> result = null;
        try {
            Logger.info("多供应商对公付款参数：{}", JSON.toJSONString(request));
            Set<ConstraintViolation<MultiSupplierCorporatePaymentRequest>> validate = Validation.buildDefaultValidatorFactory().getValidator().validate(request);
            if (CollectionUtils.isNotEmpty(validate)) {
                String validMessage = validate.stream().map(ConstraintViolation::getMessage).collect(Collectors.joining(";"));
                result = Result.error(IErrorCode.ARGUMENT_ERROR, String.format("参数错误：validMessage=%s",validMessage));
            } else {
                OAWorkflowCreateRequest createRequest = new OAWorkflowCreateRequest();
                createRequest.setBizNo(request.getBizNo());
                createRequest.setWorkflowId(multiSupplierCorporatePaymentWorkflowId);
                String workflowTitle = String.format("%s-%s-%s", multiSupplierCorporatePaymentWorkflowTitlePrefix, request.getApplyUserName(), DateUtils.formatToDate(request.getApplyTime()));
                createRequest.setWorkflowTitle(workflowTitle);
                createRequest.setCreator(request.getApplyUserNo());
                //目前只有一种业务，此处先默认写死，多种业务情况的可根据bizCategory做映射
                createRequest.setBusinessType(OAWorkflowBusinessTypeEnum.MENTOR_BONUS);
                createRequest.setNotifyUrl(request.getNotifyUrl());

                List<OAWorkflowTableField> mainFields = new ArrayList<>();
                //付款金额
                OAWorkflowTableField amountField = new OAWorkflowTableField();
                amountField.setFieldName("je");
                amountField.setFieldValue(request.getAmount().toString());
                mainFields.add(amountField);

                //税金
                OAWorkflowTableField taxAmountField = new OAWorkflowTableField();
                taxAmountField.setFieldName("sjhj");
                taxAmountField.setFieldValue(request.getTaxAmount().toString());
                mainFields.add(taxAmountField);

                //调用方式，用来区分是接口还是OA页面发起的
                OAWorkflowTableField methodField = new OAWorkflowTableField();
                methodField.setFieldName("method");
                methodField.setFieldValue("api");
                mainFields.add(methodField);

                //业务场景
                OAWorkflowTableField sceneField = new OAWorkflowTableField();
                sceneField.setFieldName("secne");
                sceneField.setFieldValue(request.getBizCategory());
                mainFields.add(sceneField);

                //附件
                CollectionUtils.emptyIfNull(request.getAttachment()).stream().forEach(attachment -> {
                    OAWorkflowTableField attachmentField = new OAWorkflowTableField();
                    attachmentField.setFieldName("fj");
                    attachmentField.setFieldValue(attachment);
                    attachmentField.setFieldType(extractProtocolAndFileNameFromUrl(attachment));
                    //重新塞入编码的字段，OA系统不支持中文
                    attachmentField.setFieldValue(encodeFileUrl(attachment));
                    mainFields.add(attachmentField);
                });

                //付款内容
                if (StringUtils.isNotBlank(request.getContent())) {
                    OAWorkflowTableField attachmentField = new OAWorkflowTableField();
                    attachmentField.setFieldName("fknr");
                    attachmentField.setFieldValue(request.getContent());
                    mainFields.add(attachmentField);
                }
                //主表
                createRequest.setMainFields(mainFields);

                //明细
                OAWorkflowDetailTable detailTable = new OAWorkflowDetailTable();
                detailTable.setDetailTableId("3");//固定值
                List<MultiSupplierCorporatePaymentRequest.PaymentItem> paymentItems = request.getPaymentItems();
                List<List<OAWorkflowTableField>> fields = new ArrayList<>();
                for (int i = 0; i < paymentItems.size(); i++) {
                    MultiSupplierCorporatePaymentRequest.PaymentItem paymentItem = paymentItems.get(i);
                    List<OAWorkflowTableField> tableFields = new ArrayList<>();
                    OAWorkflowTableField descriptionField = new OAWorkflowTableField();
                    descriptionField.setFieldName("fyms");
                    descriptionField.setFieldValue(paymentItem.getDescription());
                    tableFields.add(descriptionField);

                    OAWorkflowTableField itemAmountField = new OAWorkflowTableField();
                    itemAmountField.setFieldName("fyje");
                    itemAmountField.setFieldValue(paymentItem.getAmount().toString());
                    tableFields.add(itemAmountField);

                    OAWorkflowTableField supplierIdField = new OAWorkflowTableField();
                    supplierIdField.setFieldName("gys");
                    supplierIdField.setFieldValue(paymentItem.getSupplierId());
                    tableFields.add(supplierIdField);
                    fields.add(tableFields);
                }
                detailTable.setFields(fields);
                createRequest.setDetailTables(Arrays.asList(detailTable));

                Result<OAWorkflowCreateResponse> responseResult = oaWorkflowBaseService.create(createRequest);
                if (responseResult.getSuccess()) {
                    result = Result.success(responseResult.getData().getRequestId());
                } else {
                    result = Result.error(responseResult.getCode(), responseResult.getMessage());
                }
            }
        } catch (Exception e) {
            Logger.error("多供应商对公付款发生异常，bizNo：{}", request.getBizNo(), e);
            result = Result.error(e);
        } finally {
            Logger.info("多供应商对公付款result：{}，bizNo：{}", JSON.toJSONString(result), request.getBizNo());
        }
        return result;
    }

    /**
     * 从完整的 URL 中提取协议头和文件名
     * @param url 完整的 URL
     * @return 协议头和文件名的组合字符串，格式为 "protocol:filename"
     */
    public static String extractProtocolAndFileNameFromUrl(String url) {
        if (url == null || url.isEmpty()) {
            return null;
        }

        // 使用正则表达式提取协议头
        String protocol = url.split("://")[0];

        // 使用 lastIndexOf 方法找到最后一个 '/' 的位置
        int lastIndex = url.lastIndexOf('/');
        if (lastIndex == -1 || lastIndex == url.length() - 1) {
            return null;
        }

        // 提取文件名
        String fileName = url.substring(lastIndex + 1);

        // 返回协议头和文件名的组合字符串
        return protocol + ":" + fileName;
    }

    public static String encodeFileUrl(String fileUrl) {
        // 提取文件名
        int lastIndex = fileUrl.lastIndexOf('/');
        String fileName = fileUrl.substring(lastIndex + 1);

        // 提取前缀
        String prefix = fileUrl.substring(0, lastIndex);
        System.out.println(prefix);
        return prefix + "/" + URL.encode(fileName);
    }

    public static void main(String[] args) {
        System.out.println(encodeFileUrl("https://filetest-oss.aikucun.com/sp-manager/sp-manager-excel-temp/2501160421_爱豆发票1737093307684.ZIP"));
    }

}
