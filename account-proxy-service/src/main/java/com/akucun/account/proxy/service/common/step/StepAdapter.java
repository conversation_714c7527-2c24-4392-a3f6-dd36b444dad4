package com.akucun.account.proxy.service.common.step;

import com.akucun.account.proxy.service.common.bo.AccountExecStepContext;

import java.util.Objects;

/**
 * @Author: silei
 * @Date: 2020/8/26
 * @desc: 明细流程适配
 */
public class StepAdapter {

    private AbstractHandler execHandler;

    private AbstractHandler queryHandler;

    public StepAdapter(AbstractHandler execHandler, AbstractHandler queryHandler) {
        this.execHandler = execHandler;
        this.queryHandler = queryHandler;
    }

    public AbstractHandler getExecHandler() {
        return execHandler;
    }

    public AbstractHandler getQueryHandler() {
        return queryHandler;
    }

    void exec(AccountExecStepContext accountExecStepContext) {
        execHandler.exec(accountExecStepContext);
    }


    void query(AccountExecStepContext accountExecStepContext) {
        if (Objects.nonNull(queryHandler)){
            queryHandler.exec(accountExecStepContext);
        }
    }
}
