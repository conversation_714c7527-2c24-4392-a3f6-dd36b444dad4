package com.akucun.account.proxy.service.handler.trade;

import com.aikucun.common2.utils.StringUtils;
import com.akucun.account.center.client.model.TradeInfo;
import com.akucun.account.center.client.model.account.AccountBookDetailDO;
import com.akucun.account.proxy.client.account.AccountClient;
import com.akucun.account.proxy.common.constant.CommonConstants;
import com.akucun.account.proxy.common.constant.CustomerType;
import com.akucun.account.proxy.common.constant.DetailTypeConstants;
import com.akucun.account.proxy.common.enums.ResponseEnum;
import com.akucun.account.proxy.common.exception.AccountProxyException;
import com.akucun.account.proxy.common.utils.AccountUtils;
import com.akucun.account.proxy.common.utils.WechatNotifyTool;
import com.akucun.account.proxy.facade.stub.enums.AccountKeyConstants;
import com.akucun.account.proxy.facade.stub.enums.TradeChannel;
import com.akucun.account.proxy.service.common.bo.AccountExecStepContext;
import com.akucun.account.proxy.service.common.bo.AccountTradeReq;
import com.akucun.account.proxy.service.common.step.AbstractHandler;
import com.akucun.account.proxy.service.trade.AccountTradeCommonService;
import com.akucun.common.Result;
import com.akucun.fps.pingan.feign.api.member.MemberServiceApi;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Objects;


/**
 * <AUTHOR>
 * @date 2020/12/21 16:45
 */
@Component
public class XdAcctRefundHandler extends AbstractHandler {

    @Autowired
    private AccountClient accountClient;
    @Autowired
    private AccountTradeCommonService accountTradeCommonService;
    @Resource
    private MemberServiceApi memberService;
    @Autowired
    private WechatNotifyTool wechatNotifyTool;

    @Override
    protected boolean preCheck(AccountExecStepContext accountExecStepContext) {
        return true;
    }

    @Override
    protected void doSubmitBefore(AccountExecStepContext accountExecStepContext) {

    }

    @Override
    protected void doSubmit(AccountExecStepContext accountExecStepContext) {
        AccountTradeReq req = accountExecStepContext.getAccountTradeReq();
        Result result = Result.success();
        TradeInfo tradeInfo = new TradeInfo();

        if(CommonConstants.USER_ROLE_3.equals(req.getUserRole())) {
            tradeInfo.setAccountTypeKey(AccountKeyConstants.NMDL.getName());
            tradeInfo.setTradeType(DetailTypeConstants.TRADE_TYPE_268.getName());
        } else if(CommonConstants.USER_ROLE_2.equals(req.getUserRole())) {
            tradeInfo.setAccountTypeKey(AccountKeyConstants.NM.getName());
            tradeInfo.setTradeType(DetailTypeConstants.TRADE_TYPE_041.getName());
        }

        if(StringUtils.isNullOrEmpty(tradeInfo.getAccountTypeKey()) || StringUtils.isNullOrEmpty(tradeInfo.getTradeType())){
            throw new AccountProxyException(ResponseEnum.TRADE_REFUND_NOT_CONFIG);
        }
        //店主编号需要拼接
        String customerType = CommonConstants.USER_ROLE_2.equals(req.getUserRole()) ? CustomerType.NM.getName() : CustomerType.NMDL.getName();
        String customerCode = AccountUtils.getSellerCode(req.getCustomerCode(), customerType);
        tradeInfo.setCustomerCode(customerCode);
        tradeInfo.setAmount(req.getAmount().setScale(2, BigDecimal.ROUND_HALF_UP));
        tradeInfo.setTradeNo(req.getTradeNo());
        tradeInfo.setRemark(TradeChannel.queryDescByCode(req.getChannel()) + ":" + req.getSourceNo());

        // 查询支付数据，获取sourceBillNo
        AccountBookDetailDO bookDetailDO = accountClient.queryAccountDetail(customerCode, req.getSourceNo(), tradeInfo.getAccountTypeKey());
        if (Objects.nonNull(bookDetailDO)) {
            tradeInfo.setSourceBillNo(bookDetailDO.getSourceBillNo());
        }

        Result<Void> tradeResult = accountClient.newBalanceTrade(tradeInfo);

        if (null != tradeResult && tradeResult.isSuccess()) {
            result.setSuccess(Boolean.TRUE);
        } else {
            result.setSuccess(Boolean.FALSE);
            result.setCode(tradeResult.getCode());
            result.setMessage(tradeResult.getMessage());
        }
        accountExecStepContext.setRespMessage(result);
    }

    @Override
    protected void doSubmitAfter(AccountExecStepContext accountExecStepContext) throws Exception {
        accountTradeCommonService.handlerErrorAfterCommit(accountExecStepContext);
    }


}
