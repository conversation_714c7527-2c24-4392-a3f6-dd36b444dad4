package com.akucun.account.proxy.service.acct.repository;

import com.akucun.account.proxy.common.enums.ResponseEnum;
import com.akucun.account.proxy.common.enums.ResultStatus;
import com.akucun.account.proxy.common.exception.AccountProxyException;
import com.akucun.account.proxy.dao.mapper.AccountOpTradeDetailMapper;
import com.akucun.account.proxy.dao.mapper.AccountOpTradeMapper;
import com.akucun.account.proxy.dao.model.AccountOpTrade;
import com.akucun.account.proxy.dao.model.AccountOpTradeDetail;
import com.akucun.account.proxy.service.acct.bo.AccountOpTradeBO;
import com.akucun.account.proxy.service.acct.bo.AccountOpTradeDetailBO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.mengxiang.base.common.log.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @Author: silei
 * @Date: 2020/8/26
 * @desc:
 */
@Repository
public class AccountOpTradeRepository {

    @Resource
    private AccountOpTradeMapper accountOpTradeMapper;
    @Resource
    private AccountOpTradeDetailMapper accountOpTradeDetailMapper;


    /**
     * 查询最后一条明细交易记录
     *
     * @param tradeBO
     * @return
     */
    public AccountOpTradeBO queryAcctOpTradeDetail(AccountOpTradeBO tradeBO) {
        LambdaQueryWrapper<AccountOpTradeDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AccountOpTradeDetail::getAccountTradeId, tradeBO.getId())
                .last(" ORDER BY id DESC LIMIT 1");
        AccountOpTradeDetailBO detailBO = new AccountOpTradeDetailBO();
        AccountOpTradeDetail detail = accountOpTradeDetailMapper.selectOne(wrapper);
        if (Objects.nonNull(detail)) {
            BeanUtils.copyProperties(detail, detailBO);
            tradeBO.setAccountOpTradeDetailBO(detailBO);
        }
        return tradeBO;
    }

    /**
     * 查询主交易的全部子交易
     * @param id
     * @return
     */
    public List<AccountOpTradeDetailBO> queryAcctOpTradeDetailList(Long id) {
        List<AccountOpTradeDetailBO> list = Lists.newArrayList();
        LambdaQueryWrapper<AccountOpTradeDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AccountOpTradeDetail::getAccountTradeId, id);
        List<AccountOpTradeDetail> poList = accountOpTradeDetailMapper.selectList(wrapper);
        poList.forEach(t -> {
            AccountOpTradeDetailBO detailBO = new AccountOpTradeDetailBO();
            BeanUtils.copyProperties(t, detailBO);
            list.add(detailBO);
        });
        return list;

    }

    public int updateAccountOpTradeDetailStatus(AccountOpTradeDetailBO detailBO) {
        AccountOpTradeDetail detail = new AccountOpTradeDetail();
        BeanUtils.copyProperties(detailBO, detail);
        int i = accountOpTradeDetailMapper.updateById(detail);
        if (i != 1) {
            throw new AccountProxyException(ResponseEnum.STEP_EXEC_CONCURRENCY.getCode(), ResponseEnum.STEP_EXEC_CONCURRENCY.getMessage());
        }
        return i;
    }

    public int updateAccountOpTradeStatus(AccountOpTradeBO tradeBO) {
        AccountOpTrade trade = new AccountOpTrade();
        BeanUtils.copyProperties(tradeBO, trade);
        int i = accountOpTradeMapper.updateById(trade);
        if (i != 1) {
            throw new AccountProxyException(ResponseEnum.STEP_EXEC_CONCURRENCY.getCode(), ResponseEnum.STEP_EXEC_CONCURRENCY.getMessage());
        }
        return i;
    }

    public void saveAccountOpTrade(AccountOpTrade accountTrade) {
        accountOpTradeMapper.insert(accountTrade);
    }


    public List<AccountOpTrade> queryAccountUpgrade(Date date) {
        LambdaQueryWrapper<AccountOpTrade> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(AccountOpTrade::getStatus, ResultStatus.I.getCode(),ResultStatus.P.getCode())
                .ge(AccountOpTrade::getCreateTime, date)
                .eq(AccountOpTrade::getIsDelete, 0)
                .orderByAsc(AccountOpTrade::getCreateTime)
                .last("LIMIT 100");
        return accountOpTradeMapper.selectList(wrapper);
    }

    public void addAccountOpTradeDetail(AccountOpTradeDetailBO bo) {
        AccountOpTradeDetail detail = new AccountOpTradeDetail();
        BeanUtils.copyProperties(bo, detail);
        Logger.info("AccountOpTradeRepository 新增 AccountOpTradeDetail :{}", detail);
        accountOpTradeDetailMapper.insert(detail);
        bo.setId(detail.getId());
    }

    /**
     * 查询升级状态（只查询最新的记录状态）
     * @param customerCode
     * @param customerType
     * @return
     */
    public AccountOpTrade queryLatestAccountOpTrade(String customerCode, String customerType) {
        LambdaQueryWrapper<AccountOpTrade> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AccountOpTrade::getCustomerCode, customerCode)
                .eq(AccountOpTrade::getCustomerType, customerType)
                .eq(AccountOpTrade::getIsDelete, 0)
                .last("order by id desc limit 1");
        return accountOpTradeMapper.selectOne(wrapper);
    }

    public boolean hasRecentProcessingAccountOpTrade(String customerCode, String customerType, int recentMinutes) {
        LambdaQueryWrapper<AccountOpTrade> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AccountOpTrade::getCustomerCode, customerCode)
                .eq(AccountOpTrade::getCustomerType, customerType)
                .eq(AccountOpTrade::getIsDelete, 0)
                .in(AccountOpTrade::getStatus, ResultStatus.I.getCode(), ResultStatus.P.getCode())
                .gt(AccountOpTrade::getCreateTime, Date.from(LocalDateTime.now().minusMinutes(recentMinutes).atZone(ZoneId.systemDefault()).toInstant()));
        List<AccountOpTrade> list = accountOpTradeMapper.selectList(wrapper);
        return !list.isEmpty();
    }

    public AccountOpTrade queryOpTradeById(long id) {
       return accountOpTradeMapper.selectById(id);
    }

    public List<AccountOpTrade> queryProcessingAccountOpTrade(String customerCode, String customerType) {
        LambdaQueryWrapper<AccountOpTrade> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AccountOpTrade::getCustomerCode, customerCode)
                .eq(AccountOpTrade::getCustomerType, customerType)
                .eq(AccountOpTrade::getIsDelete, 0)
                .in(AccountOpTrade::getStatus, ResultStatus.I.getCode(), ResultStatus.P.getCode());
        return accountOpTradeMapper.selectList(wrapper);
    }

    public List<AccountOpTrade> queryAccountOpTradeList(long begin, long end) {
        LambdaQueryWrapper<AccountOpTrade> wrapper = new LambdaQueryWrapper<>();
        wrapper.ge(AccountOpTrade::getId, begin)
                .le(AccountOpTrade::getId, end)
                .eq(AccountOpTrade::getIsDelete, 0);
        return accountOpTradeMapper.selectList(wrapper);
    }

    public void updateAccountOpTrade(AccountOpTrade trade) {
        accountOpTradeMapper.updateById(trade);
    }
}
