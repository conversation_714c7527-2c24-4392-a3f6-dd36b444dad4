package com.akucun.account.proxy.service.postaction.task;

import com.aikucun.common2.base.Result;
import com.aikucun.common2.log.Logger;
import com.akucun.account.proxy.common.enums.PostActionTypes;
import com.akucun.account.proxy.dao.model.PostActionItem;
import com.akucun.account.proxy.dao.model.WithdrawApplyRecord;
import com.akucun.account.proxy.service.acct.WithdrawTaxService;
import com.akucun.account.proxy.service.postaction.common.AbsPostActionExecutor;
import com.akucun.fps.common.util.GsonUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class WithdrawServiceFeeFailTask extends AbsPostActionExecutor {
    @Autowired
    private WithdrawTaxService withdrawTaxService;

    @Override
    protected String getActionType() {
        return PostActionTypes.WITHDRAWSERVICEFEE_FAILAMOUNT.getName();
    }


    @XxlJob("withdrawServiceFeeFailTask")
    public ReturnT<String> execute(String param) {
        return this.executeEntrance();
    }

    @Override
    public Result<Void> execute(PostActionItem item) {
        Result<Void> result = Result.success();
        try {
            WithdrawApplyRecord record = GsonUtils.getInstance().fromJson(item.getParam(),WithdrawApplyRecord.class);
            //手续费交易
            result = withdrawTaxService.withdrawServiceFeeFailAmount(record);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setMessage(e.getMessage());
            Logger.error("提现扣手续费失败金额更新任务失败", e);
        }
        return result;
    }
}
