package com.akucun.account.proxy.service.handler.pingan;

import com.aikucun.common2.utils.datamasking.DataMask;
import com.akucun.account.proxy.common.constant.CommonConstants;
import com.akucun.account.proxy.common.enums.ResponseEnum;
import com.akucun.account.proxy.common.enums.ResultStatus;
import com.akucun.account.proxy.common.exception.AccountProxyException;
import com.akucun.account.proxy.common.utils.AccountUtils;
import com.akucun.account.proxy.service.common.bo.AccountExecStepContext;
import com.akucun.account.proxy.service.common.bo.AccountReq;
import com.akucun.account.proxy.service.common.bo.AccountResp;
import com.akucun.account.proxy.service.common.step.AbstractHandler;
import com.akucun.fps.common.entity.Result;
import com.akucun.fps.common.entity.ResultList;
import com.akucun.fps.pingan.client.constants.SelectFlagTypeConstants;
import com.akucun.fps.pingan.client.model.AccountSearchReqDO;
import com.akucun.fps.pingan.client.model.PingAnAccountVO;
import com.akucun.fps.pingan.client.model.po.PinganAccount;
import com.akucun.fps.pingan.client.vo.AccountRegisterVO;
import com.akucun.fps.pingan.feign.api.merchant.MerchantServiceApi;
import com.akucun.fps.pingan.feign.api.settlement.SettlementServiceApi;
import com.alibaba.fastjson.JSON;
import com.mengxiang.base.common.log.Logger;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;

/**
 * @Author: silei
 * @Date: 2020/11/25
 * @desc:
 */
@Component
public class CancelAcctQueryHandler extends AbstractHandler {

    @Resource
    private SettlementServiceApi settlementServiceApi;

    @Resource
    private MerchantServiceApi merchantServiceApi;

    @Value("${pingan.account.not.exist.respCode:90006}")
    private int accountNotExistCode;

    @Override
    protected boolean preCheck(AccountExecStepContext accountExecStepContext) {
        AccountReq req = accountExecStepContext.getAccountReq();
        AccountResp resp = accountExecStepContext.getAccountResp();
        //参数校验
        if (StringUtils.isBlank(req.getCustomerCode())) {
            resp.setStatus(ResultStatus.F.getCode());
            resp.setReplyMsg("用户客户编码为空");
        }
        if (StringUtils.isBlank(req.getCustomerType())) {
            resp.setStatus(ResultStatus.F.getCode());
            resp.setReplyMsg("用户客户类型为空");
        }
        return !ResultStatus.F.getCode().equals(resp.getStatus());
    }

    @Override
    protected void doSubmitBefore(AccountExecStepContext accountExecStepContext) {

    }

    @Override
    protected void doSubmit(AccountExecStepContext accountExecStepContext) {
        try {
            AccountReq req = accountExecStepContext.getAccountReq();
            AccountResp resp = accountExecStepContext.getAccountResp();
            Logger.info("CancelAcctQueryHandler doSubmit customerCode:{},customerType:{}", req.getCustomerCode(), req.getCustomerType());
            AccountRegisterVO registerVO = new AccountRegisterVO();
            registerVO.setCustomerCode(AccountUtils.getSellerCode(req.getCustomerCode(), req.getCustomerType()));
            registerVO.setCustomerType(req.getCustomerType());
            Result<PinganAccount> accountResult = merchantServiceApi.selectPinganAccount(registerVO);
            if (accountResult != null && accountResult.isSuccess()) {
                //本地账户查询为空，说明账户已注销（先注销平安账户再更新本地账户状态），返回成功
                if (accountResult.getData() == null) {
                    Logger.info("CancelAcctQueryHandler account is canceled, customerCode:{}", req.getCustomerCode());
                    resp.setStatus(ResultStatus.S.getCode());
                    resp.setReplyCode(CommonConstants.SUCC_CODE);
                    resp.setReplyMsg(ResultStatus.S.getDesc());
                } else {
                    //本地账户存在，查询平安侧账户是否存在
                    AccountSearchReqDO searchReqDO = new AccountSearchReqDO();
                    searchReqDO.setSelectFlag(String.valueOf(SelectFlagTypeConstants.GENERAL.getValue()));
                    searchReqDO.setCustAcctId(accountResult.getData().getPinganAccountCode());
                    searchReqDO.setCustomerType(req.getCustomerType());
                    Logger.info("CancelAcctQueryHandler doSubmit searchReqDO:{}", DataMask.toJSONString(searchReqDO));
                    ResultList<PingAnAccountVO> resultList = settlementServiceApi.selectPlatformAccount(searchReqDO);
                    Logger.info("CancelAcctQueryHandler doSubmit resultList:{}", DataMask.toJSONString(resultList));
                    if (resultList != null) {
                        //平安账户不存在，先更新本地账户状态为失效，再返回成功
                        if (!resultList.isSuccess() && accountNotExistCode == resultList.getErrorCode()) {
                            PinganAccount account = new PinganAccount();
                            account.setCustomerCode(accountResult.getData().getCustomerCode());
                            account.setCustomerType(accountResult.getData().getCustomerType());
                            //账户禁用
                            account.setStatus(1);
                            Result<Void> result = merchantServiceApi.updatePinganAccount(account);
                            if (result != null && result.isSuccess()){
                                resp.setStatus(ResultStatus.S.getCode());
                                resp.setReplyCode(CommonConstants.SUCC_CODE);
                                resp.setReplyMsg(ResultStatus.S.getDesc());
                            }
                        } else if (resultList.isSuccess() && !CollectionUtils.isEmpty(resultList.getDatalist())) {
                            //平安账户存在，返回失败
                            resp.setStatus(ResultStatus.F.getCode());
                            resp.setReplyCode(CommonConstants.FAIL_CODE);
                            resp.setReplyMsg(ResultStatus.F.getDesc());
                        }
                    }

                }
            }
        } catch (Exception e) {
            Logger.info("CancelAcctQueryHandler doSubmit error", e);
            throw new AccountProxyException(ResponseEnum.SUBMIT_EXCEPTION);
        }
    }

    @Override
    protected void doSubmitAfter(AccountExecStepContext accountExecStepContext) throws Exception {

    }
}
