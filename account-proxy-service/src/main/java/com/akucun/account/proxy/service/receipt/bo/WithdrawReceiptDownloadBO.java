/*
 * @Author: Lee
 * @Date: 2025-03-21 15:16:06
 * @Description: 提现回单下载BO
 * @License: Copyright (c) 2025, Lee
 */
package com.akucun.account.proxy.service.receipt.bo;

import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Builder
@Accessors(chain = true)
public class WithdrawReceiptDownloadBO {

    /**
     * 提现单号
     */
    private String withdrawNo;

    /**
     * 提现渠道
     */
    private String withdrawChannel;

    /**
     * 是否为企业饷店店主/店长提现
     */
    private Boolean isTenantCustomer;

    /**
     * 商户号, 用于微信提现回单下载
     * 目前爱豆微信提现回单下载需要传入
     */
    private String merchantCode;

    /**
     * 提现专项批次号, 用于微信提现回单下载
     * 仅爱豆微信提现回单下载需要传入
     */
    private String batchNo;

    /**
     * 用于灰度判断
     */
    private String customerType;

    /**
     * 是否调用pay-gateway-wx申请转账回单成功
     */
    private Boolean wechatApplySuccess;

}
