package com.akucun.account.proxy.service.postaction.bo;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * @Author: silei
 * @Date: 2020/12/8
 * @desc:
 */
@Data
@Builder
public class PostActionItemBO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 任务名称
     */
    private String actionType;

    /**
     * 业务ID
     */
    private String bizId;

    /**
     * 业务参数
     */
    private Object paramObject;

    /**
     * 最近一次错误日志
     */
    private String errorLog;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 客户编码
     */
    private Integer retryNums;

    /**
     * 下次重试时间unix数字
     */
    private LocalDateTime nextRetryTime;

    /**
     * 状态：1.成功；2.失败需要重试；3.失败不需要重试
     */
    private Integer bizStatus;

    /**
     * 是否可执行 1可执行 0 不可执行
     */
    private Integer status;


}
