package com.akucun.account.proxy.service.transfer.impl;


import com.akucun.account.proxy.dao.mapper.PaymentTransferMapper;
import com.akucun.account.proxy.dao.model.PaymentTransfer;
import com.akucun.account.proxy.service.transfer.PaymentTransferService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 企业付款到零钱 服务实现类
 *
 * <AUTHOR>
 * @since 2021-01-26
 */
@Service
public class PaymentTransferServiceImpl extends ServiceImpl<PaymentTransferMapper, PaymentTransfer> implements PaymentTransferService {
    @Override
    public PaymentTransfer queryBySourceNo(String sourceCode, String sourceNo) {
        LambdaQueryWrapper<PaymentTransfer> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PaymentTransfer::getIsDelete, 0)
                .eq(PaymentTransfer::getSourceCode, sourceCode)
                .eq(PaymentTransfer::getSourceNo, sourceNo)
                .last(" order by id desc limit 1");
        return this.getOne(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveOne(PaymentTransfer paymentTransfer) {
        return this.save(paymentTransfer);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateOne(PaymentTransfer paymentTransfer) {
        LambdaQueryWrapper<PaymentTransfer> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PaymentTransfer::getSourceCode, paymentTransfer.getSourceCode())
                .eq(PaymentTransfer::getSourceNo, paymentTransfer.getSourceNo())
                .eq(PaymentTransfer::getId, paymentTransfer.getId());
        return this.update(paymentTransfer, wrapper);
    }
}
