package com.akucun.account.proxy.service.common.impl;

import com.akucun.account.proxy.dao.mapper.AccountStepInfoMapper;
import com.akucun.account.proxy.dao.model.AccountStepInfo;
import com.akucun.account.proxy.service.common.AccountStepInfoService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: silei
 * @Date: 2020/8/26
 * @desc:
 */
@Service
public class AccountStepInfoServiceImpl extends ServiceImpl<AccountStepInfoMapper, AccountStepInfo> implements AccountStepInfoService {


    @Override
    public List<AccountStepInfo> load() {
        return this.baseMapper.selectList(new LambdaQueryWrapper<AccountStepInfo>().eq(AccountStepInfo::getIsDelete, 0));
    }
}
