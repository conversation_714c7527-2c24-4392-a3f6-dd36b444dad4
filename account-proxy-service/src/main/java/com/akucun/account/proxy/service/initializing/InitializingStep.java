package com.akucun.account.proxy.service.initializing;

import com.aikucun.common2.utils.StringUtils;
import com.akucun.account.proxy.common.enums.StepEnum;
import com.akucun.account.proxy.dao.model.AccountStepControl;
import com.akucun.account.proxy.dao.model.AccountStepInfo;
import com.akucun.account.proxy.service.common.AccountStepControlService;
import com.akucun.account.proxy.service.common.AccountStepInfoService;
import com.akucun.account.proxy.service.common.bo.AccountStepBO;
import com.akucun.account.proxy.service.common.step.AbstractHandler;
import com.akucun.account.proxy.service.common.step.Step;
import com.akucun.account.proxy.service.common.step.StepAdapter;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.mengxiang.base.common.log.Logger;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * @Author: silei
 * @Date: 2020/8/26
 * @desc: 流程步骤配置信息
 */
@Component
public class InitializingStep implements InitializingBean {

    @Autowired
    ApplicationContext applicationContext;
    @Autowired
    private AccountStepControlService accountStepControlService;
    @Autowired
    private AccountStepInfoService accountStepInfoService;


    //普通流程
    private static final Map<String, Step> stepCache = new ConcurrentHashMap<>();
    //第一步流程
    private static final Map<String, Step> firstStepCache = new ConcurrentHashMap<>();

    public void initStepCache() {

        List<AccountStepControl> controlInfos = accountStepControlService.load();
        List<AccountStepInfo> stepInfos = accountStepInfoService.load();

        for (AccountStepControl info : controlInfos) {
            AccountStepInfo stepInfo = stepInfos.stream().filter(t -> t.getTradeType().equals(info.getTradeType()) && t.getSubTradeType().equals(info.getSubTradeType())).collect(Collectors.toList()).get(0);
            AccountStepBO stepBO = buildAccountStepBO(info, stepInfo);

            Step step = this.buildCommonStep(stepBO);
            if (null == step) {continue;}
            if (!StringUtils.isNullOrBlank(stepBO.getExtMap())) {
                JSONObject json = JSONObject.parseObject(stepBO.getExtMap());
                Set<String> keys = json.keySet();
                for (String key : keys) {
                    step.getExtMap().put(key, json.getString(key));
                }
            }
            stepCache.put(getStepKey(stepBO.getTradeType(), stepBO.getSubTradeType()), step);
            if (StepEnum.IsHead.Y.getCode().equals(stepBO.getIsHead())) {
                firstStepCache.put(stepBO.getTradeType(), step);
            }
        }
        Logger.info("InitializingStep stepCache: {}", stepCache);
        Logger.info("InitializingStep firstStepCache: {}", firstStepCache);
    }

    /**
     * 获取流程第一步
     * @param tradeType
     * @return
     */
    public Step getStep(String tradeType) {
        return firstStepCache.get(tradeType);
    }

    /**
     * 获取正常流程step
     * @param tradeType
     * @param subTradeType
     * @return
     */
    public Step getStep(String tradeType, String subTradeType) {
        return stepCache.get(getStepKey(tradeType, subTradeType));
    }

    private String getStepKey(String tradeType, String subTradeType) {
        return tradeType + "_" + subTradeType;
    }

    private AccountStepBO buildAccountStepBO(AccountStepControl info, AccountStepInfo stepInfo) {
        Map<String, String> nextStepMap = Maps.newHashMap();
        JSONObject json = JSONObject.parseObject(info.getNextStepMap());
        for (String key : json.keySet()) {
            nextStepMap.put(key, json.getString(key));
        }
        return AccountStepBO.builder()
                .tradeType(info.getTradeType())
                .subTradeType(info.getSubTradeType())
                .nextStepMap(nextStepMap)
                .isHead(info.getIsHead())
                .stepRefClass(stepInfo.getStepRefClass())
                .execRefClass(stepInfo.getExecRefClass())
                .queryRefClass(stepInfo.getQueryRefClass())
                .extMap(stepInfo.getExtMap())
                .build();
    }


    private Step buildCommonStep(AccountStepBO bo) {
        try {
            Step step = (Step) applicationContext.getBean(bo.getStepRefClass());
            step.setTradeType(bo.getTradeType());
            step.setSubTradeType(bo.getSubTradeType());

            AbstractHandler execHandler = StringUtils.isNullOrBlank(bo.getExecRefClass()) ? null : (AbstractHandler) applicationContext.getBean(bo.getExecRefClass());
            AbstractHandler queryHandler = StringUtils.isNullOrBlank(bo.getQueryRefClass()) ? null : (AbstractHandler) applicationContext.getBean(bo.getQueryRefClass());
            StepAdapter stepAdapter = new StepAdapter(execHandler, queryHandler);
            step.setStepAdapter(stepAdapter);
            step.getNextStepMap().putAll(bo.getNextStepMap());
            return step;
        } catch (Exception e) {
            Logger.error("buildCommonStep getBean error -->:{}", bo.getExecRefClass());
        }
        return null;
    }

    @Override
    public void afterPropertiesSet() {
        initStepCache();
    }
}
