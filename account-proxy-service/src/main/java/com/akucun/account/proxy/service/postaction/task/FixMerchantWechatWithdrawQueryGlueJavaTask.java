package com.akucun.account.proxy.service.postaction.task;

import com.aikucun.common2.log.Logger;
import com.akucun.account.proxy.service.acct.AccountWithdrawService;
import com.akucun.bcs.channel.facade.stub.dto.WechatWithdrawRequest;
import com.akucun.fps.common.util.GsonUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;

/**
 * @Description
 * @aurhor lee
 * @date: 2022/9/13 14:34
 */
public class FixMerchantWechatWithdrawQueryGlueJavaTask extends IJobHandler {

    @Resource
    private AccountWithdrawService accountWithdrawService;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        Logger.info("FixMerchantWechatWithdrawQueryGlueJavaTask执行参数：{}", param);
        if (StringUtils.isBlank(param)) {
            Logger.info("FixMerchantWechatWithdrawQueryGlueJavaTask执行异常：参数不能为空");
            return ReturnT.SUCCESS;
        }
        WechatWithdrawRequest request = GsonUtils.getInstance().fromJson(param, WechatWithdrawRequest.class);
        accountWithdrawService.queryMerchantWechatWithdraw(request);
        return ReturnT.SUCCESS;
    }

}
