package com.akucun.account.proxy.service.acct.bo;


import lombok.Data;

import java.io.Serializable;

/**
 * 税库银三方协议申请记录BO
 * <AUTHOR>
 */
@Data
public class TaxTripPactionApplyBO implements Serializable {


    private static final long serialVersionUID = 2795149956073205872L;
    /**
     * 用户名
     */
    private String userName;

    /**
     * 客户编码
     */
    private String customerCode;

    /**
     * 客户类型
     */
    private String customerType;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 银行编码
     */
    private String bankCode;

    /**
     * 银行卡号
     */
    private String bankCardNo;

    /**
     * 协议图片路径
     */
    private String pactionImgUrl;

    /**
     * 状态 0：审核中，1：审核通过，2：审核未通过
     */
    private int status;

    /**
     * 驳回原因
     */
    private String rejectReason;

    /**
     * 客服侧审核工单编号
     */
    private String auditNo;

    /**
     *社会信用代码
     */
    private String socialCreditCode;

    /**
     *商家名称
     */
    private String merchantName;

    /**
     *法人姓名
     */
    private String legalPersonName;
}
