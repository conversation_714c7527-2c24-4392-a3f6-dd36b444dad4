package com.akucun.account.proxy.service.handler.pingan;

import com.aikucun.common2.utils.datamasking.DataMask;
import com.akucun.account.proxy.common.constant.CommonConstants;
import com.akucun.account.proxy.common.enums.AccountPropertyType;
import com.akucun.account.proxy.common.enums.MemberGrade;
import com.akucun.account.proxy.common.enums.ResponseEnum;
import com.akucun.account.proxy.common.enums.ResultStatus;
import com.akucun.account.proxy.common.exception.AccountProxyException;
import com.akucun.account.proxy.common.utils.AccountUtils;
import com.akucun.account.proxy.common.utils.EncryptUtils;
import com.akucun.account.proxy.common.utils.IdentifyUtils;
import com.akucun.account.proxy.facade.stub.enums.CustomerType;
import com.akucun.account.proxy.facade.stub.enums.IdentifyEnum;
import com.akucun.account.proxy.service.acct.AccountService;
import com.akucun.account.proxy.service.common.bo.AccountExecStepContext;
import com.akucun.account.proxy.service.common.bo.AccountReq;
import com.akucun.account.proxy.service.common.bo.AccountResp;
import com.akucun.account.proxy.service.common.step.AbstractHandler;
import com.akucun.fps.common.entity.Result;
import com.akucun.fps.pingan.client.vo.RegisterRealVO;
import com.akucun.fps.pingan.feign.api.merchant.MerchantServiceApi;
import com.akucun.member.audit.facade.stub.fallback.api.FeignMemberAuthService;
import com.akucun.member.audit.facade.stub.fallback.api.FeignMemberIndentityService;
import com.akucun.member.audit.model.dto.auth.QueryAuthStatusReqDTO;
import com.akucun.member.audit.model.dto.auth.QueryAuthStatusRespDTO;
import com.akucun.member.audit.model.dto.auth.QueryUserAuthBaseReqDTO;
import com.akucun.member.audit.model.dto.auth.QueryUserAuthBaseRespDTO;
import com.akucun.member.audit.model.dto.identity.MemberIdentityVerifyQueryReqDTO;
import com.akucun.member.audit.model.dto.identity.MemberIdentityVerifyRespDTO;
import com.alibaba.fastjson.JSON;
import com.mengxiang.base.common.log.Logger;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @Author: silei
 * @Date: 2020/9/3
 * @desc: 注册平安账户流程
 */
@Component
public class RegisterAcctHandler extends AbstractHandler {

    @Resource
    private MerchantServiceApi merchantServiceApi;

    @Autowired
    private AccountService accountService;

    @Autowired
    private FeignMemberAuthService feignMemberAuthService;

    @Autowired
    private FeignMemberIndentityService feignMemberIndentityService;

    @Value("${query.user.highest.auth.status.switch:false}")
    private Boolean queryUserHighestAuthStatusSwitch;

    @Override
    protected boolean preCheck(AccountExecStepContext accountExecStepContext) {
        AccountResp resp = accountExecStepContext.getAccountResp();
        AccountReq req = accountExecStepContext.getAccountReq();
        Logger.info("RegisterAcctHandler preCheck req:{}", DataMask.toJSONString(req));
        //参数校验
        if (StringUtils.isBlank(req.getCustomerCode())) {
            resp.setStatus(ResultStatus.F.getCode());
            resp.setReplyMsg("客户编码为空");
        }
        if (StringUtils.isBlank(req.getCustomerType())) {
            resp.setStatus(ResultStatus.F.getCode());
            resp.setReplyMsg("客户类型为空");
        }
//        if (StringUtils.isBlank(req.getMobile())) {
//            resp.setStatus(ResultStatus.F.getCode());
//            resp.setReplyMsg("客户手机号为空");
//        }
//        if (StringUtils.isBlank(req.getCustomerName())) {
//            resp.setStatus(ResultStatus.F.getCode());
//            resp.setReplyMsg("客户姓名为空");
//        }
        return !ResultStatus.F.getCode().equals(resp.getStatus());
    }

    @Override
    protected void doSubmitBefore(AccountExecStepContext accountExecStepContext) {
        AccountReq req = accountExecStepContext.getAccountReq();
        AccountResp resp = accountExecStepContext.getAccountResp();
        if(!accountService.isTenantCustomer(req.getCustomerCode(), req.getCustomerType())) {//饷店账户在绑卡时再开通
            resp.setStatus(ResultStatus.S.getCode());
            resp.setReplyCode(CommonConstants.SUCC_CODE);
            resp.setReplyMsg(ResultStatus.S.getDesc());
            return;
        }
        RegisterRealVO vo = new RegisterRealVO();
        vo.setCustomerType(req.getCustomerType());
        vo.setCustomerCode(AccountUtils.getSellerCode(req.getCustomerCode(), req.getCustomerType()));
        if(StringUtils.isNotBlank(req.getAccountProperty())) {
            vo.setCustProperty(req.getAccountProperty());
        } else {
            vo.setCustProperty(AccountPropertyType.GENERAL.getCode());
        }
        vo.setBusinessFlag(2);

        QueryAuthStatusReqDTO reqDTO = new QueryAuthStatusReqDTO();
        reqDTO.setUserCode(req.getCustomerCode());
        reqDTO.setUserType(CustomerType.NM.name().equals(req.getCustomerType()) ? 2 : 3);
        Logger.info("账户升级查询最高认证，请求：{}", DataMask.toJSONString(reqDTO));
        com.akucun.common.Result<QueryAuthStatusRespDTO> authResult = null;
        if (queryUserHighestAuthStatusSwitch) {
            authResult = feignMemberAuthService.queryUserHighestAuthStatus(reqDTO);
        } else {
            // 会员升级：-3（个体工商户注销）代表个人等逻辑；
            authResult = feignMemberAuthService.queryUserHighestAuthStatusV2(reqDTO);
        }
        Logger.info("账户升级查询最高认证，请求：{}，返回：{}", DataMask.toJSONString(reqDTO), DataMask.toJSONString(authResult));
        if(!authResult.isSuccess()) {
            Logger.error("账户升级查询最高认证失败，请求：{}，返回：{}", DataMask.toJSONString(reqDTO), DataMask.toJSONString(authResult));
            throw new AccountProxyException(ResponseEnum.STEP_EXEC_EXCEPTION);
        }

        QueryUserAuthBaseReqDTO authInfoReq = new QueryUserAuthBaseReqDTO();
        authInfoReq.setUserCode(reqDTO.getUserCode());
        authInfoReq.setUserType(reqDTO.getUserType());
        authInfoReq.setAuthType(authResult.getData().getCurrentHighestAuthType());
        authInfoReq.setMoreRoleIncludeSellerFlag(false);
        Logger.info("账户升级查询店铺认证，请求：{}", DataMask.toJSONString(authInfoReq));
        com.akucun.common.Result<QueryUserAuthBaseRespDTO> authInfoResult =  feignMemberAuthService.queryUserAuthByUserIdUserType(authInfoReq);
        Logger.info("账户升级查询店铺认证，请求：{}，返回：{}", DataMask.toJSONString(authInfoReq), DataMask.toJSONString(authInfoResult));
        if(!authInfoResult.isSuccess()) {
            Logger.error("账户升级查询店铺认证失败，请求：{}，返回：{}", DataMask.toJSONString(authInfoReq), DataMask.toJSONString(authInfoResult));
            throw new AccountProxyException(ResponseEnum.STEP_EXEC_EXCEPTION);
        }

        if(authResult.getData().getCurrentHighestAuthType().equals(MemberGrade.PERSON_AUTH.getCode())
            || authResult.getData().getCurrentHighestAuthType().equals(MemberGrade.ENTERPRISE_AUTH.getCode())) {//个体或企业
            vo.setIdType(IdentifyEnum.CREDIT_CODE.getCode());
            vo.setIdCode(authInfoResult.getData().getCreditCode());
            vo.setCustomerName(authInfoResult.getData().getMerchantName());
        } else {//个人
            MemberIdentityVerifyQueryReqDTO idInfoReq = new MemberIdentityVerifyQueryReqDTO();
            idInfoReq.setUserType(reqDTO.getUserType());
            idInfoReq.setUserId(authInfoResult.getData().getUserId());
            idInfoReq.setChannel("bc41849ca6b4483ab461b2638f93dd5e");//无用但必传参数
            Logger.info("账户升级查询实名信息，请求：{}", DataMask.toJSONString(idInfoReq));
            com.akucun.common.Result<MemberIdentityVerifyRespDTO> idInfoResult = feignMemberIndentityService.queryIdentityInfoByUserId(idInfoReq);
            Logger.info("账户升级查询实名信息，请求：{}，返回：{}", DataMask.toJSONString(idInfoReq), DataMask.toJSONString(idInfoResult));
            if(!idInfoResult.isSuccess()) {
                Logger.error("账户升级查询实名信息失败，请求：{}，返回：{}", DataMask.toJSONString(idInfoReq), DataMask.toJSONString(idInfoResult));
                throw new AccountProxyException(ResponseEnum.STEP_EXEC_EXCEPTION);
            }
            vo.setIdType(IdentifyUtils.convertIdTypeMember2Pingan(idInfoResult.getData().getCardType()).getCode());
            String idCode = EncryptUtils.decrypt(idInfoResult.getData().getIDNo());
            if(idCode == null) {
                throw new AccountProxyException(ResponseEnum.STEP_EXEC_EXCEPTION);
            }
            vo.setIdCode(idCode);
            vo.setCustomerName(idInfoResult.getData().getRealName());
        }

        Logger.info("RegisterAcctHandler doSubmitBefore result:{}", DataMask.toJSONString(vo));
        accountExecStepContext.setReqMessage(vo);
    }

    @Override
    protected void doSubmit(AccountExecStepContext accountExecStepContext) {
        AccountReq req = accountExecStepContext.getAccountReq();
        AccountResp resp = accountExecStepContext.getAccountResp();
        if(ResultStatus.S.getCode().equals(resp.getStatus())) {
            return;
        }
        try {
            RegisterRealVO vo = (RegisterRealVO) accountExecStepContext.getReqMessage();
            Logger.info("账户升级实名开户，请求：{}", DataMask.toJSONString(vo));
            Result<String> result = merchantServiceApi.registerReal(vo);
            Logger.info("账户升级实名开户，请求：{}，返回：{}", DataMask.toJSONString(vo), DataMask.toJSONString(result));
            accountExecStepContext.setRespMessage(result);
        } catch (Exception e) {
            Logger.error("AcctTransferInHandler doSubmit customerCode:{}, customerType:{},accountUpdateError:{}", req.getCustomerCode(), req.getCustomerType(), e);
            throw new AccountProxyException(ResponseEnum.SUBMIT_EXCEPTION);
        }
    }

    @Override
    protected void doSubmitAfter(AccountExecStepContext accountExecStepContext) {
        AccountResp resp = accountExecStepContext.getAccountResp();
        if(ResultStatus.S.getCode().equals(resp.getStatus())) {
            return;
        }
        resp.setStatus(ResultStatus.P.getCode());
        Result<String> result = (Result<String>) accountExecStepContext.getRespMessage();
        if (Objects.nonNull(result)) {
            if (result.isSuccess()) {
                resp.setStatus(ResultStatus.S.getCode());
                resp.setReplyCode(CommonConstants.SUCC_CODE);
                resp.setReplyMsg(ResultStatus.S.getDesc());
            }
        }
    }
}
