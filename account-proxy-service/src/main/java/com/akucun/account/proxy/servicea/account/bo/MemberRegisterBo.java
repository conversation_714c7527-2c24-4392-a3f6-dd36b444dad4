package com.akucun.account.proxy.servicea.account.bo;

import java.io.Serializable;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;

import com.akucun.account.proxy.common.enums.MemberRegisterType;

import lombok.Data;

/**
 * 会员注册
 * <AUTHOR>
 *
 */
@Data
public class MemberRegisterBo implements Serializable{

	private final static String TENANT_ID = "151738493257170900";
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 8806613098216428372L;
	
	
	/**
	 * 租户id
	 */
	private Long tenantId;
	
	/**
	 * 用户id
	 * type:1 时 为普通会员注册消息，对应user_id
	 * type:2 时 为店主注册消息，对应seller_id
	 * type:3 时 为店长注册消息，对应distributor_id
	 */
	private String userId;
	
	/**
	 *  爱库存代购编号
	 */
	private String resellerId;
	
	/**
	 * type:1 时 为普通会员注册消息
	 * type:2 时 为店主注册消息
	 * type:3 时 为店长注册消息
	 */
	private Integer type;
	
	public Boolean  check() {
		if(Objects.isNull(tenantId)) {
			return Boolean.FALSE;
		}
		if(StringUtils.isBlank(userId)) {
			return Boolean.FALSE;
		}
		if(Objects.isNull(type)) {
			return Boolean.FALSE;
		}
		if(type.intValue()==MemberRegisterType.type1.getValue().intValue()) {
			return Boolean.FALSE;
		}
		return Boolean.TRUE;
	}

}
