package com.akucun.account.proxy.service.acct.bo;

import com.akucun.account.proxy.facade.stub.others.dto.res.OANotifyDTO;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : 异步通知业务对象
 * @Create on : 2025/2/17 13:34
 **/
@Data
@NoArgsConstructor
public class NotifyPromoBO {
    String notifyUrl;
    String groupCode;
    OANotifyDTO notifyDTO;

    public NotifyPromoBO(String notifyUrl, String groupCode, OANotifyDTO notifyDTO) {
        this.notifyUrl = notifyUrl;
        this.groupCode = groupCode;
        this.notifyDTO = notifyDTO;
    }
}
