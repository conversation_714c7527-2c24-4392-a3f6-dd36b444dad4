package com.akucun.account.proxy.service.postaction.task;

import com.aikucun.common2.utils.datamasking.DataMask;
import com.akucun.account.proxy.client.center.AccountCenterClient;
import com.akucun.account.proxy.dao.mapper.AccountTenantMerchantMapper;
import com.akucun.account.proxy.dao.model.AccountTenantMerchant;
import com.akucun.account.proxy.facade.stub.enums.AccountKeyConstants;
import com.akucun.account.proxy.facade.stub.others.account.req.AccountOperateInfoReq;
import com.akucun.common.Result;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mengxiang.base.common.log.Logger;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @Author: silei
 * @Date: 2021/5/13
 * @desc: 租户开户补偿任务
 */
@Component
public class TenantUserCompensateTask {

    @Autowired
    private AccountCenterClient accountCenterClient;
    @Resource
    private AccountTenantMerchantMapper accountTenantMerchantMapper;

    @XxlJob("tenantUserCompensateTask")
    public ReturnT<String> execute(String param) {
        Logger.info("TenantUserCompensateTask param:{}", param);

        if (StringUtils.isBlank(param)) {
            return ReturnT.FAIL;
        }
        String[] arr = param.split(",");
        String tenantId = arr[0];
        String tenantName = arr[1];
        String tenantType = arr[2];
        String appId = arr[3];
        //参数校验
        if (StringUtils.isEmpty(tenantId) || StringUtils.isEmpty(tenantName)
                || StringUtils.isEmpty(tenantType)) {
            Logger.error("TenantUserCompensateTask message 缺少必要开户参数!");
            return ReturnT.FAIL;
        }

        LambdaQueryWrapper<AccountTenantMerchant> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AccountTenantMerchant::getTenantId, tenantId)
                .eq(AccountTenantMerchant::getTenantType, tenantType)
                .eq(AccountTenantMerchant::getStatus, 0);
        AccountTenantMerchant merchant = accountTenantMerchantMapper.selectOne(wrapper);
        if (merchant != null) {
            Logger.warn("TenantUserCompensateTask merchant exist, merchant:{}", DataMask.toJSONString(merchant));
            return ReturnT.SUCCESS;
        }
        merchant = new AccountTenantMerchant();
        //租户类型  tenant_type  0 爱库存， 1.三方小程序， 2.企业饷店，3.SASS标准类型
        merchant.setTenantId(tenantId);
        merchant.setTenantType(tenantType);
        merchant.setTenantName(tenantName);
        merchant.setAppId(appId);
        accountTenantMerchantMapper.insert(merchant);

        AccountOperateInfoReq req = new AccountOperateInfoReq();
        req.setCustomerCode(tenantId);
        req.setCustomerName(tenantName);
        req.setAccountTypeKey(AccountKeyConstants.AT.getName());
        req.setOperationType("CREATE");
        Result<Void> result = accountCenterClient.accountOperate(req);

        return result.isSuccess() ? ReturnT.SUCCESS : ReturnT.FAIL;

    }
}
