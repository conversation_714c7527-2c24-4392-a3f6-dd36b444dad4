package com.akucun.account.proxy.service.tradeflow.domain;

import com.akucun.account.proxy.facade.stub.others.trade.res.TradePhaseStatusEnum;
import com.akucun.account.proxy.facade.stub.others.trade.res.TradeStatusEnum;
import com.akucun.account.proxy.service.common.SpringContextHolder;
import com.akucun.account.proxy.service.tradeflow.agreement.TradeAgreement;
import com.akucun.account.proxy.service.tradeflow.agreement.TradeAgreementPhase;
import com.akucun.account.proxy.service.tradeflow.agreement.TradeAgreementService;
import com.akucun.account.proxy.service.tradeflow.context.TradeContext;
import com.akucun.account.proxy.service.tradeflow.repository.TradePhaseRepository;
import com.akucun.account.proxy.service.tradeflow.repository.TradeRepository;
import com.akucun.account.proxy.service.tradeflow.script.TradeScripts;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class Trade {

    /**
     * id
     */
    private Long id;

    /**
     * 交易流水号，幂等控制字段
     */
    private String tradeNo;

    /**
     * 业务单号
     */
    private String bizNo;

    /**
     * 产品线：饷店、企业饷店、商家、豆联盟
     */
    private String productCategory;

    /**
     * 业务类型：绑卡、开户、分佣、补偿、提现
     */
    private String bizType;

    /**
     * 请求平台编码：IOS、安卓、小程序、H5
     */
    private String requestPlatform;

    /**
     * 业务参数
     */
    private Object bizInfo;

    /**
     * 备注
     */
    private String remark;

    /**
     * 交易状态
     */
    private TradeStatusEnum status;

    /**
     * 协议
     */
    private TradeAgreement agreement;

    public void execute() {
        TradePhase currentPhase = null;
        while(true) {
            TradeAgreementPhase agreementPhase = SpringContextHolder.getBean(TradeAgreementService.class).nextAgreementPhase(this.agreement, currentPhase);
            if(agreementPhase == null) {
                break;
            }
            currentPhase = initPhase(agreementPhase);
            currentPhase.execute();
            TradeStatusEnum currentStatus = currentPhase.getAgreement().getStatusMap().get(currentPhase.getStatus());
            if(this.status != currentStatus) {
                this.status = currentStatus;
                SpringContextHolder.getBean(TradeRepository.class).updateResult(this);
                if(isEnd()) {
                    break;
                }
            }
        }
    }

    public boolean isEnd() {
        if (this.status == TradeStatusEnum.SUCCESS || this.status == TradeStatusEnum.FAIL) {
            return true;
        } else {
            return false;
        }
    }

    private TradePhase initPhase(TradeAgreementPhase agreementPhase) {
        TradePhase tradePhase = new TradePhase();
        tradePhase.setTradeId(this.id);
        Object bizInfo = TradeScripts.getPhaseBuildScript(agreementPhase.getBuildScript()).build(this);
        tradePhase.setBizInfo(bizInfo);
        tradePhase.setStatus(TradePhaseStatusEnum.PROCESSING);
        tradePhase.setAgreement(agreementPhase);
        SpringContextHolder.getBean(TradePhaseRepository.class).persist(tradePhase);
        TradeContext.get().addPhase(tradePhase);
        return tradePhase;
    }

}
