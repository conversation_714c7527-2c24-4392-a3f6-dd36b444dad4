package com.akucun.account.proxy.service.handler.pingan;

import com.akucun.account.proxy.common.constant.CommonConstants;
import com.akucun.account.proxy.common.enums.ResponseEnum;
import com.akucun.account.proxy.common.enums.ResultStatus;
import com.akucun.account.proxy.common.exception.AccountProxyException;
import com.akucun.account.proxy.dao.model.AccountOpTransferAmount;
import com.akucun.account.proxy.service.acct.AccountOpTransferAmountService;
import com.akucun.account.proxy.service.common.bo.AccountExecStepContext;
import com.akucun.account.proxy.service.common.bo.AccountReq;
import com.akucun.account.proxy.service.common.bo.AccountResp;
import com.akucun.account.proxy.service.common.step.AbstractHandler;
import com.akucun.fps.common.entity.Result;
import com.akucun.fps.pingan.client.api.expose.MemberService;
import com.akucun.fps.pingan.client.model.PinganTradeDO;
import com.akucun.fps.pingan.feign.api.member.MemberServiceApi;
import com.alibaba.dubbo.config.annotation.Reference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mengxiang.base.common.log.Logger;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 账户余额归集到公司账户查询
 * <AUTHOR>
 */

@Component
public class AcctTransferOutQueryHandler extends AbstractHandler {

    @Resource
    private MemberServiceApi memberService;
    @Autowired
    private AccountOpTransferAmountService amountService;

    @Override
    protected boolean preCheck(AccountExecStepContext accountExecStepContext) {
        AccountReq req = accountExecStepContext.getAccountReq();
        AccountResp resp = accountExecStepContext.getAccountResp();
        //参数校验
        if (StringUtils.isBlank(req.getDetailOrderNo())) {
            resp.setStatus(ResultStatus.F.getCode());
            resp.setReplyMsg("请求流水号为空");
        }
        return !ResultStatus.F.getCode().equals(resp.getStatus());
    }

    @Override
    protected void doSubmitBefore(AccountExecStepContext accountExecStepContext) {
        AccountReq req = accountExecStepContext.getAccountReq();
        AccountResp resp = accountExecStepContext.getAccountResp();
        Result<PinganTradeDO> result = memberService.selectOneByOrderNo(req.getDetailOrderNo());
        if (result.isSuccess()&& "SUCC".equals(result.getData().getPinganStatus())) {
            int i = amountService.getBaseMapper().update(AccountOpTransferAmount.builder().collectStatus(ResultStatus.S.getCode()).build(),
                    new LambdaQueryWrapper<AccountOpTransferAmount>().eq(AccountOpTransferAmount::getAccountTradeId, req.getAccountTradeId())
                            .eq(AccountOpTransferAmount::getDetailOrderNo, req.getDetailOrderNo()));
            if (i != 1) {
                Logger.error("AcctTransferOutHandler doSubmitAfter accountUpdateError, transferAmount exception! customerCode:{}, customerType:{}", req.getCustomerCode(), req.getCustomerType());
                throw new AccountProxyException(ResponseEnum.AMOUNT_COLLECT_EXCEPTION);
            }

            resp.setStatus(ResultStatus.S.getCode());
            resp.setReplyCode(CommonConstants.SUCC_CODE);
            resp.setReplyMsg(ResultStatus.S.getDesc());
        } else if(!result.isSuccess()) {
            resp.setStatus(ResultStatus.F.getCode());
            resp.setReplyCode(result.getErrorCode() + "");
            resp.setReplyMsg(result.getErrorMessage());
            //失败变更转出金额记录为失败
            amountService.getBaseMapper().update(AccountOpTransferAmount.builder().collectStatus(ResultStatus.S.getCode()).build(),
                    new LambdaQueryWrapper<AccountOpTransferAmount>().eq(AccountOpTransferAmount::getAccountTradeId, req.getAccountTradeId())
                            .eq(AccountOpTransferAmount::getDetailOrderNo, req.getDetailOrderNo()));

        }
    }


    @Override
    protected void doSubmit(AccountExecStepContext accountExecStepContext) {

    }

    @Override
    protected void doSubmitAfter(AccountExecStepContext accountExecStepContext) throws Exception {

    }
}
