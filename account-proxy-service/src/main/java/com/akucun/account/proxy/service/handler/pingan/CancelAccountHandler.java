package com.akucun.account.proxy.service.handler.pingan;

import com.akucun.account.proxy.common.enums.AccountPropertyType;
import com.akucun.account.proxy.common.enums.ResponseEnum;
import com.akucun.account.proxy.common.enums.ResultStatus;
import com.akucun.account.proxy.common.exception.AccountProxyException;
import com.akucun.account.proxy.common.utils.AccountUtils;
import com.akucun.account.proxy.service.common.bo.AccountExecStepContext;
import com.akucun.account.proxy.service.common.bo.AccountReq;
import com.akucun.account.proxy.service.common.bo.AccountResp;
import com.akucun.account.proxy.service.common.step.AbstractHandler;
import com.akucun.cloud.security.CodeUtils;
import com.akucun.fps.pingan.client.constants.CustomerNatureType;
import com.akucun.fps.pingan.client.vo.MemBerRegisterVO;
import com.akucun.fps.pingan.feign.api.merchant.MerchantServiceApi;
import com.mengxiang.base.common.log.Logger;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


/**
 * @Author: silei
 * @Date: 2020/9/1
 * @desc: 注销平安账户
 */
@Component
public class CancelAccountHandler extends AbstractHandler {

    @Resource
    private MerchantServiceApi merchantServiceApi;

    @Autowired
    private ThreadPoolTaskExecutor executor;

    @Override
    protected boolean preCheck(AccountExecStepContext accountExecStepContext) {
        AccountResp resp = accountExecStepContext.getAccountResp();
        AccountReq req = accountExecStepContext.getAccountReq();
        Logger.info("CancelAccountHandler preCheck req:{}", req);
        //参数校验
        if (StringUtils.isBlank(req.getCustomerCode())) {
            resp.setStatus(ResultStatus.F.getCode());
            resp.setReplyMsg("客户编码为空");
        }
        if (StringUtils.isBlank(req.getCustomerType())) {
            resp.setStatus(ResultStatus.F.getCode());
            resp.setReplyMsg("客户类型为空");
        }
        return !ResultStatus.F.getCode().equals(resp.getStatus());
    }

    @Override
    protected void doSubmitBefore(AccountExecStepContext accountExecStepContext) {
        AccountReq req = accountExecStepContext.getAccountReq();
        MemBerRegisterVO vo = formParam(req);
        Logger.info("CancelAccountHandler doSubmitBefore req:{}", vo);
        accountExecStepContext.setReqMessage(vo);
    }

    private MemBerRegisterVO formParam(AccountReq req) {
        MemBerRegisterVO vo = new MemBerRegisterVO();
        // 店主用户编码拼接
        vo.setCustomerCode(AccountUtils.getSellerCode(req.getCustomerCode(), req.getCustomerType()));
        vo.setCustomerNatureType((CustomerNatureType) CustomerNatureType.getEnum(CustomerNatureType.class, req.getCustomerType()));
        if(StringUtils.isNotBlank(req.getAccountProperty())) {
            vo.setCustProperty(req.getAccountProperty());
        } else {
            vo.setCustProperty(AccountPropertyType.GENERAL.getCode());
        }
        vo.setNickName(req.getNickName());
        if (StringUtils.isNotEmpty(req.getMobile()) && !StringUtils.isNumeric(req.getMobile())){
            vo.setMobile(CodeUtils.decrypt(req.getMobile()).getData());
        }
        return vo;
    }

    @Override
    protected void doSubmit(AccountExecStepContext accountExecStepContext) {
        MemBerRegisterVO vo = (MemBerRegisterVO) accountExecStepContext.getReqMessage();
        try {
            //平安注销接口返回时间较长，结果依赖查询接口
            executor.execute(()-> merchantServiceApi.cancelPinganAccount(vo));
        } catch (Exception e) {
            Logger.error("CancelAccountHandler doSubmit customerCode:{}, customerType:{}, accountUpdateError:{}", vo.getCustomerCode(), vo.getCustomerNatureType().getName(), e);
            throw new AccountProxyException(ResponseEnum.SUBMIT_EXCEPTION);
        }
    }

    @Override
    protected void doSubmitAfter(AccountExecStepContext accountExecStepContext) {
        AccountResp resp = accountExecStepContext.getAccountResp();
        resp.setStatus(ResultStatus.P.getCode());
    }

}
