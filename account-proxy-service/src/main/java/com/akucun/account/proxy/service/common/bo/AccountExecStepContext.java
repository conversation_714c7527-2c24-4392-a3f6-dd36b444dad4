package com.akucun.account.proxy.service.common.bo;

import com.akucun.account.proxy.common.constant.StepConstant;
import com.akucun.account.proxy.common.enums.ResultStatus;
import com.akucun.account.proxy.common.enums.StepEnum;
import com.akucun.account.proxy.service.acct.bo.AccountOpTradeDetailBO;
import com.akucun.account.proxy.service.trade.bo.AccountTradeDetailBO;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: silei
 * @Date: 2020/8/26
 * @desc: 账户明细流程上下文
 */
@Data
public class AccountExecStepContext {

    AccountReq accountReq;

    AccountResp accountResp;

    AccountTradeReq accountTradeReq;
    //渠道请求报文
    private Object reqMessage;
    //渠道响应报文
    private Object respMessage;
    //扩展字段
    private Map<String, String> extMap = new HashMap<>();
    //流程是否继续
    private boolean isContinue = true;
    //执行类型，查询 或 执行
    private String action;
    //执行过程中的异常
    private Throwable exception;
    //账户升级明细
    private AccountOpTradeDetailBO accountOpTradeDetailBO;
    //账户交易明细
    private AccountTradeDetailBO accountTradeDetailBO;

    //判断当前流程是否正常结束
    public boolean isEnd() {
        return ResultStatus.S.getCode().equals(getStepStatus())
                || (ResultStatus.F.getCode().equals(getStepStatus()) && !StepEnum.IsRetryAble.Y.getCode().equals(extMap.get(StepConstant.IS_RETRYABLE)));
    }

    public String getStepStatus() {
        if (accountResp == null) {
            return null;
        }
        return accountResp.getStatus();
    }

    public void setStepStatus(String status) {
        if (accountResp == null) {
            accountResp = new AccountResp();
        }
        accountResp.setStatus(status);
    }
}
