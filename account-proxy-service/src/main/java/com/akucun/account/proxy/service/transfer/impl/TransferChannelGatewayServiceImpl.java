package com.akucun.account.proxy.service.transfer.impl;

import com.akucun.account.proxy.common.constant.CommonConstants;
import com.akucun.account.proxy.common.utils.RedisUtil;
import com.akucun.account.proxy.dao.mapper.TransferChannelGatewayMapper;
import com.akucun.account.proxy.dao.model.TransferChannelGateway;
import com.akucun.account.proxy.dao.model.TransferGateway;
import com.akucun.account.proxy.facade.stub.others.dto.req.TransferChannelGatewayReq;
import com.akucun.account.proxy.service.transfer.TransferChannelGatewayService;
import com.akucun.account.proxy.service.transfer.TransferGatewayService;
import com.akucun.account.proxy.service.transfer.bo.TransferGatewayBO;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mengxiang.base.common.log.Logger;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 付款渠道网关关系表 服务实现类
 *
 * <AUTHOR>
 * @since 2021-01-26
 */
@Service
public class TransferChannelGatewayServiceImpl extends ServiceImpl<TransferChannelGatewayMapper, TransferChannelGateway> implements TransferChannelGatewayService {

    @Autowired
    private TransferGatewayService transferGatewayService;
    @Autowired
    private RedisUtil redisUtil;
    @Value("${new.branch.switch:false}")
    private boolean newBranchSwitch;

    @Value("${query.gatewayByAppId.switch:false}")
    private boolean queryGatewayByAppId;

    @Override
    public TransferGatewayBO queryGatewayByChannelCode(String channelCode, String tenantId, String appId) {

        // 获取redis缓存
        String redisKey = String.format(CommonConstants.TRANSFER_MERCHANT_INFO_PREFIX, channelCode + tenantId);
        if(queryGatewayByAppId && StringUtils.isNotBlank(appId)){
            redisKey = String.format(CommonConstants.TRANSFER_MERCHANT_INFO_PREFIX, channelCode + tenantId+appId);
        }
        Object transferGateway = redisUtil.get(redisKey);
        TransferGatewayBO transferGatewayBO = Objects.nonNull(transferGateway) ? JSON.parseObject(transferGateway.toString(), TransferGatewayBO.class) : null;
        if(Objects.nonNull(transferGatewayBO)) {
            Logger.info("查询缓存付款商户信息：{}", transferGateway.toString());
            if(newBranchSwitch){
                transferGatewayBO.setAppId(appId);
            }
            return transferGatewayBO;
        }

        // 查询网关渠道关系记录
        LambdaQueryWrapper<TransferChannelGateway> channelGatewayWrapper = new LambdaQueryWrapper<>();
        channelGatewayWrapper.eq(TransferChannelGateway::getChannelCode, channelCode);
        channelGatewayWrapper.eq(TransferChannelGateway::getTenantId, tenantId);
        if(StringUtils.isNotBlank(appId)&&queryGatewayByAppId){
            channelGatewayWrapper.eq(TransferChannelGateway::getAppId, appId);
        }
        channelGatewayWrapper.eq(TransferChannelGateway::getStatus, 0);
        channelGatewayWrapper.eq(TransferChannelGateway::getIsDelete, 0);
        channelGatewayWrapper.last(" order by id desc limit 1");
        TransferChannelGateway channelGateway = this.getOne(channelGatewayWrapper);
        if(Objects.isNull(channelGateway)) {
            return null;
        }
        if(!StringUtils.equals(channelGateway.getAppId(), appId)){
            Logger.info("付款到零钱查询路由获取到的appId与参数不匹配：{},参数：{},{},{}", channelGateway.getAppId(), appId,channelCode,tenantId);
        }
        // 查询网关记录
        LambdaQueryWrapper<TransferGateway> gatewayWrapper = new LambdaQueryWrapper<>();
        gatewayWrapper.eq(TransferGateway::getGatewayCode, channelGateway.getGatewayCode())
                .eq(TransferGateway::getStatus, 0)
                .eq(TransferGateway::getIsDelete, 0)
                .last(" order by id desc limit 1");
        TransferGateway gateway = transferGatewayService.getOne(gatewayWrapper);
        if(Objects.isNull(gateway)) {
            return null;
        }
        TransferGatewayBO result = new TransferGatewayBO();
        BeanUtils.copyProperties(gateway, result);
        if(!newBranchSwitch){
            result.setAppId(channelGateway.getAppId());
        }else {
            result.setAppId(appId);
        }

        // 放入缓存
        redisUtil.set(redisKey, JSON.toJSONString(result), 60*60L);

        return result;
    }

    @Override
    public void removeRedisCache(String channelCode) {
        String redisKey = String.format(CommonConstants.TRANSFER_MERCHANT_INFO_PREFIX, channelCode);
        redisUtil.del(redisKey);
    }

    @Override
    public void saveUpdate(TransferChannelGatewayReq req) {

        Logger.info("TransferChannelGatewayServiceImpl saveUpdate req:{}", req);
        LambdaQueryWrapper<TransferChannelGateway> channelGatewayWrapper = new LambdaQueryWrapper<>();
        channelGatewayWrapper.eq(TransferChannelGateway::getChannelCode, req.getChannelCode())
                .eq(TransferChannelGateway::getTenantId, req.getTenantId())
                .eq(TransferChannelGateway::getAppId, req.getAppId())
                .eq(TransferChannelGateway::getStatus, 0)
                .eq(TransferChannelGateway::getIsDelete, 0);
        TransferChannelGateway channelGateway = this.getOne(channelGatewayWrapper);
        if (channelGateway != null) {
            channelGateway.setGatewayCode(req.getGatewayCode());
            if (req.getStatus()!= null){
                channelGateway.setStatus(req.getStatus());
            }
            this.updateById(channelGateway);
        }else {
            channelGateway = new TransferChannelGateway();
            channelGateway.setChannelCode(req.getChannelCode());
            channelGateway.setAppId(req.getAppId());
            channelGateway.setGatewayCode(req.getGatewayCode());
            channelGateway.setTenantId(req.getTenantId());
            this.save(channelGateway);
        }

    }

    @Override
    public TransferGatewayBO queryTransferGateway(String channelCode, String tenantId) {
        // 获取redis缓存
        String redisKey = String.format(CommonConstants.TRANSFER_MERCHANT_INFO_PREFIX, channelCode + tenantId);
        Object transferGateway = redisUtil.get(redisKey);
        TransferGatewayBO transferGatewayBO = Objects.nonNull(transferGateway) ? JSON.parseObject(transferGateway.toString(), TransferGatewayBO.class) : null;
        if(Objects.nonNull(transferGatewayBO)) {
            Logger.info("queryTransferGateway 查询缓存付款商户信息：{}", transferGateway.toString());
            return transferGatewayBO;
        }

        // 查询网关渠道关系记录
        LambdaQueryWrapper<TransferChannelGateway> channelGatewayWrapper = new LambdaQueryWrapper<>();
        channelGatewayWrapper.eq(TransferChannelGateway::getChannelCode, channelCode)
                .eq(TransferChannelGateway::getTenantId, tenantId)
                .eq(TransferChannelGateway::getStatus, 0)
                .eq(TransferChannelGateway::getIsDelete, 0)
                .last(" order by id desc limit 1");
        TransferChannelGateway channelGateway = this.getOne(channelGatewayWrapper);
        if(Objects.isNull(channelGateway)) {
            return null;
        }
        // 查询网关记录
        LambdaQueryWrapper<TransferGateway> gatewayWrapper = new LambdaQueryWrapper<>();
        gatewayWrapper.eq(TransferGateway::getGatewayCode, channelGateway.getGatewayCode())
                .eq(TransferGateway::getStatus, 0)
                .eq(TransferGateway::getIsDelete, 0)
                .last(" order by id desc limit 1");
        TransferGateway gateway = transferGatewayService.getOne(gatewayWrapper);
        if(Objects.isNull(gateway)) {
            return null;
        }
        TransferGatewayBO result = new TransferGatewayBO();
        BeanUtils.copyProperties(gateway, result);
        result.setAppId(channelGateway.getAppId());
        // 放入缓存
        redisUtil.set(redisKey, JSON.toJSONString(result), 60*60L);

        return result;
    }

}
