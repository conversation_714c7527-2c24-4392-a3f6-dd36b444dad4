package com.akucun.account.proxy.service.tradeflow.dto.bizinfo;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class BizInfoRegisterBindcard {

    /**
     * 用户类型
     */
    private String customerType;

    /**
     * 用户编码
     */
    private String customerCode;

    /**
     * 账户类型
     */
    private String accountType;

    /**
     * 会员证件类型（IDCARD-身份证，CREDIT_CODE，社会统一信用代码）
     */
    private String idType;

    /**
     * 会员证件号码
     */
    private String idCode;

    /**
     * 客户名称（个人-真实姓名，个体/企业-公司名称）
     */
    private String customerName;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 租户类型
     */
    private Integer tenantType;

    /**
     * 用户手机号
     */
    private String phone;

    /**
     * 绑卡方式（SMS-短信验证，SMA-小额鉴权）
     */
    private String bindWay;

    /**
     * 超网行号
     */
    private String sbankCode;

    /**
     * 总行名称
     */
    private String sbankName;

    /**
     * 联行号
     */
    private String bankCode;

    /**
     * 开户行支行名称
     */
    private String bankName;

    /**
     * 银行账号
     */
    private String bankCardCode;

    /**
     * 银行预留手机号
     */
    private String mobilePhone;

}
