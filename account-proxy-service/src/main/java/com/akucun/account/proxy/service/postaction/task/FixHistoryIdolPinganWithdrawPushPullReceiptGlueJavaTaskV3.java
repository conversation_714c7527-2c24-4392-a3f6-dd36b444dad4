/*
 * @Author: Lee
 * @Date: 2025-03-28 17:00:20
 * @Description: Do not edit
 * @License: Copyright (c) 2025, Lee
 */
package com.akucun.account.proxy.service.postaction.task;

import cn.hutool.core.util.RandomUtil;
import com.aikucun.common2.log.Logger;
import com.akucun.account.proxy.common.constant.WithdrawChannelConstants;
import com.akucun.account.proxy.common.enums.PostActionExecStatus;
import com.akucun.account.proxy.common.enums.PostActionTypes;
import com.akucun.account.proxy.dao.mapper.PaymentTransferMapper;
import com.akucun.account.proxy.dao.mapper.PostActionItemMapper;
import com.akucun.account.proxy.dao.mapper.TenantWithdrawApplyMapper;
import com.akucun.account.proxy.dao.mapper.WithdrawApplyRecordMapper;
import com.akucun.account.proxy.dao.model.PostActionItem;
import com.akucun.account.proxy.dao.model.TenantWithdrawApply;
import com.akucun.account.proxy.dao.model.WithdrawApplyRecord;
import com.akucun.account.proxy.service.common.SpringContextHolder;
import com.akucun.account.proxy.service.postaction.PostActionService;
import com.akucun.account.proxy.service.postaction.bo.PostActionItemBO;
import com.akucun.account.proxy.service.receipt.bo.WithdrawReceiptDownloadBO;
import com.akucun.account.proxy.service.transfer.PaymentTransferService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @Description
 * @aurhor lee
 * @date: 2022/9/13 14:34
 */
public class FixHistoryIdolPinganWithdrawPushPullReceiptGlueJavaTaskV3 extends IJobHandler {

    @Resource
    private WithdrawApplyRecordMapper withdrawApplyRecordMapper;

    @Resource
    private PaymentTransferMapper paymentTransferMapper;

    @Resource
    private PaymentTransferService paymentTransferService;

    @Resource
    private PostActionItemMapper postActionItemMapper;

    @Resource
    private TenantWithdrawApplyMapper tenantWithdrawApplyMapper;

    private String EXPORT_RECEIPT_TMP_PATH = System.getProperty("java.io.tmpdir") + "/account-proxy-tmp";

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        Logger.info("FixHistoryIdolPinganWithdrawPushPullReceiptGlueJavaTaskV2执行参数：{}", param);
        if (StringUtils.isBlank(param)) {
            Logger.info("FixHistoryIdolPinganWithdrawPushPullReceiptGlueJavaTaskV2执行异常：参数不能为空");
            return ReturnT.SUCCESS;
        }
		String withdrawNoStrs = "************************,************************,************************,************************,************************,************************,************************,************************,************************,************************,************************,************************,************************,************************,************************,************************,************************,************************,************************,************************,************************,************************,TX2505073440572700606000,TX2505073440587300606000,TX2505073440562600606000,TX2505293478752900606000,TX2505293478759200606000,TX2505073440569400606000,TX2505293478774700606000,TX2505073440586800606000,TX2505293478746900606000,TX2505073440557300606000,TX2505073440573400606000,TX2505293478762600606000,TX2505293478762200606000,TX2505073440573100606000,TX2505293478768100606000,TX2505073440578900606000,TX2505073440579700606000,TX2505073440553500606000,TX2505073440552400606000,TX2505293478773900606000,TX2505073440585800606000,TX2505293478749900606000,TX2505073440559000606000,TX2505073440581900606000,TX2505073440556800606000,TX2505293478747900606000,TX2505073440571700606000,TX2505293478760900606000,TX2505073440553800606000,TX2505293478755800606000,TX2505073440565800606000,TX2505073440556400606000,TX2505073440571300606000,TX2505293478761100606000,TX2505073440572300606000,TX2505293478761800606000,TX2505073440575300606000,TX2505073440583300606000,TX2505073440569700606000,TX2505293478759300606000,TX2505293478765400606000,TX2505073440577200606000,TX2505073440560300606000,TX2505293478757200606000,TX2505073440567500606000,TX2505073440562200606000,TX2505293478752300606000,TX2505293478773400606000,TX2505073440585400606000,TX2505073440575100606000,TX2505293478758800606000,TX2505073440568800606000,TX2505293478756100606000,TX2505073440566000606000,TX2505073440583200606000,TX2505293478757500606000,TX2505073440567700606000,TX2505073440562900606000,TX2505293478752500606000,TX2505073440564400606000,TX2505293478754600606000,TX2505073440571800606000,TX2505293478764300606000,TX2505073440576100606000,TX2505073440582700606000,TX2505293478771100606000,TX2505073440552100606000,TX2505293478744000606000,TX2505293478774500606000,TX2505073440586400606000,TX2505293478770700606000,TX2505073440582100606000,TX2505073440561300606000,TX2505293478751800606000,TX2505293478746400606000,TX2505073440553000606000,TX2505293478768500606000,TX2505073440579500606000,TX2505293478775300606000,TX2505073440587800606000,TX2505073440577000606000,TX2505293478765300606000,TX2505293478766900606000,TX2505073440578300606000,TX2505293478764600606000,TX2505073440576200606000,TX2505083443401300606000,TX2505073440570300606000,TX2505293478759800606000,TX2505293478745300606000,TX2505073440581200606000,TX2505293478770100606000,TX2505293478760200606000,TX2505073440570900606000,TX2505073440583900606000,TX2505293478772000606000,TX2505073440551300606000,TX2505073440580300606000,TX2505293478769500606000,TX2505293478749300606000,TX2505073440558600606000,TX2505073440584400606000,TX2505293478772600606000,TX2505293478772900606000,TX2505073440585000606000,TX2505073440573900606000,TX2505293478762800606000,TX2505073440556200606000,TX2505293478768200606000,TX2505073440579000606000,TX2505293478743800606000,TX2505073440552200606000,TX2505073440590600606000,TX2505293478777800606000,TX2505293478775000606000,TX2505073440587200606000,TX2505293478754400606000,TX2505073440564500606000,TX2505073440557700606000,TX2505293478748600606000,TX2505293478774600606000,TX2505073440586500606000,TX2505293478744100606000,TX2505293478771000606000,TX2505073440582400606000,TX2505293478775100606000,TX2505073440587600606000,TX2505293478766800606000,TX2505073440578100606000,TX2505293478770500606000,TX2505073440581700606000,TX2505073440557200606000,TX2505293478748100606000,TX2505073440578800606000,TX2505293478768000606000,TX2505293478747500606000,TX2505073440558300606000,TX2505073440552900606000,TX2505293478761000606000,TX2505073440571400606000,TX2505293478772800606000,TX2505073440584800606000,TX2505293478761900606000,TX2505073440572500606000,TX2505293478746700606000,TX2505073440565100606000,TX2505293478754900606000,TX2505293478770300606000,TX2505073440581600606000,TX2505073440566900606000,TX2505293478756800606000,TX2505073440571200606000,TX2505293478760500606000,TX2505073440574700606000,TX2505073440558200606000,TX2505293478749000606000,TX2505293478769100606000,TX2505073440580200606000,TX2505163457514500606000,TX2505293478743200606000,TX2505073440551200606000,TX2505073440572100606000,TX2505073440574900606000,TX2505073440580800606000,TX2505293478776500606000,TX2505073440589100606000,TX2505293478746100606000,TX2505293478776600606000,TX2505073440589300606000,TX2505293478747700606000,TX2505073440557000606000,TX2505073440579300606000,TX2505293478768300606000,TX2505293478773600606000,TX2505073440585500606000,TX2505293478767800606000,TX2505073440578500606000,TX2505293478746300606000,TX2505293478777200606000,TX2505073440589900606000,TX2505073440567000606000,TX2505073440567900606000,TX2505293478757700606000,TX2505073440564600606000,TX2505293478754700606000,TX2505293478769400606000,TX2505073440580400606000,TX2505073440568400606000,TX2505293478747300606000,TX2505073440589500606000,TX2505293478777000606000,TX2505293478764900606000,TX2505073440576900606000,TX2505293478752000606000,TX2505073440561700606000,TX2505073440569500606000,TX2505293478759000606000,TX2505293478759400606000,TX2505073440569800606000,TX2505293478763100606000,TX2505073440574500606000,TX2505293478764100606000,TX2505073440575900606000,TX2505073440552500606000,TX2505073440578200606000,TX2505293478767200606000,TX2505293478750800606000,TX2505073440560100606000,TX2505293478766500606000,TX2505073440577700606000,TX2505073440551900606000,TX2505293478744200606000,TX2505293478771500606000,TX2505073440583000606000,TX2505073440556700606000,TX2505293478747600606000,TX2505293478767900606000,TX2505073440579100606000,TX2505073440580500606000,TX2505293478769600606000,TX2505293478747200606000,TX2505073440581300606000,TX2505293478773000606000,TX2505073440585100606000,TX2505293478763300606000,TX2505073440574200606000,TX2505073440559300606000,TX2505073440577800606000,TX2505293478766700606000,TX2505293478764700606000,TX2505073440576500606000,TX2505293478748900606000,TX2505073440557900606000,TX2505293478769000606000,TX2505073440579900606000,TX2505073440554400606000,TX2505293478744900606000,TX2505293478751300606000,TX2505073440560800606000,TX2505073440563700606000,TX2505293478753500606000,TX2505073440582600606000,TX2505073440556100606000,TX2505073440565300606000,TX2505293478755300606000,TX2505073440588900606000,TX2505293478776000606000,TX2505293478774100606000,TX2505073440586200606000,TX2505293478745500606000,TX2505293478755200606000,TX2505073440565000606000,TX2505293478773700606000,TX2505073440586100606000,TX2505073440585700606000,TX2505073440589800606000,TX2505293478776400606000,TX2505073440555300606000,TX2505293478769700606000,TX2505073440580700606000,TX2505073440572600606000,TX2505293478761500606000,TX2505293478748200606000,TX2505073440557400606000,TX2505073440561600606000,TX2505293478752100606000,TX2505073440555000606000,TX2505073440576600606000,TX2505293478764800606000,TX2505073440555900606000,TX2505293478776100606000,TX2505073440588600606000,TX2505073440585600606000,TX2505293478773500606000,TX2505293478749600606000,TX2505073440559100606000,TX2505073440563800606000,TX2505293478753300606000,TX2505073440573300606000,TX2505293478753600606000,TX2505073440564100606000,TX2505293478750900606000,TX2505073440560400606000,TX2505073440551400606000,TX2505073440588800606000,TX2505073440579600606000,TX2505293478768700606000,TX2505073440566400606000,TX2505293478756400606000,TX2505073440585200606000,TX2505293478773200606000,TX2505293478748000606000,TX2505073440590000606000,TX2505293478777400606000,TX2505073440567600606000,TX2505293478777900606000,TX2505073440590500606000,TX2505073440560700606000,TX2505073440568300606000,TX2505293478757900606000,TX2505073440568200606000,TX2505293478758100606000,TX2505293478776900606000,TX2505073440589700606000,TX2505073440589600606000,TX2505293478776800606000,TX2505073440573800606000,TX2505293478762400606000,TX2505293478769800606000,TX2505073440580900606000,TX2505293478755700606000,TX2505073440565600606000,TX2505073440578600606000,TX2505293478767400606000,TX2505073440559800606000,TX2505293478751000606000,TX2505073440581400606000,TX2505293478770200606000,TX2505073440584700606000,TX2505293478772700606000,TX2505293478753400606000,TX2505073440563500606000,TX2505073440575600606000,TX2505293478771900606000,TX2505073440583800606000,TX2505073440550900606000,TX2505293478762100606000,TX2505073440572900606000,TX2505293478760100606000,TX2505073440570800606000,TX2505293478745900606000,TX2505293478772100606000,TX2505073440584000606000,TX2505073440559200606000,TX2505293478750000606000,TX2505073440571600606000,TX2505293478760800606000,TX2505073440562500606000,TX2505293478753100606000,TX2505293478743500606000,TX2505073440551600606000,TX2505073440576700606000,TX2505293478765000606000,TX2505293478762300606000,TX2505073440572400606000,TX2505073440588200606000,TX2505293478775700606000,TX2505073440583100606000,TX2505293478765100606000,TX2505073440576800606000,TX2505293478750200606000,TX2505073440560600606000,TX2505073440574300606000,TX2505293478760600606000,TX2505293478774300606000,TX2505073440586700606000,TX2505293478778000606000,TX2505073440590700606000,TX2505293478768600606000,TX2505293478763600606000,TX2505293478751500606000,TX2505073440563100606000,TX2505073440578700606000,TX2505293478767300606000,TX2505293478774000606000,TX2505073440585900606000,TX2505293478756700606000,TX2505073440566500606000,TX2505073440576000606000,TX2505073440562000606000,TX2505293478752400606000,TX2505073440561100606000,TX2505293478751100606000,TX2505293478749400606000,TX2505073440558500606000,TX2505073440554900606000,TX2505073440584300606000,TX2505293478772400606000,TX2505293478761300606000,TX2505073440572200606000,TX2505293478774800606000,TX2505073440586900606000,TX2505073440555500606000,TX2505073440571000606000,TX2505293478760300606000,TX2505073440555600606000,TX2505073440574000606000,TX2505293478762900606000,TX2505073440583400606000,TX2505293478771600606000,TX2505293478760400606000,TX2505073440571100606000,TX2505073440588300606000,TX2505293478775400606000,TX2505293478750400606000,TX2505073440559900606000,TX2505293478749100606000,TX2505073440558100606000,TX2505073440583600606000,TX2505293478771700606000,TX2505293478743700606000,TX2505073440551800606000,TX2505293478756900606000,TX2505073440567200606000,TX2505073440585300606000,TX2505073440563300606000,TX2505073440562700606000,TX2505293478752700606000,TX2505073440566700606000,TX2505293478756300606000,TX2505293478777500606000,TX2505073440590400606000,TX2505073440579400606000,TX2505293478768400606000,TX2505073440577900606000,TX2505293478767100606000,TX2505293478758000606000,TX2505073440568500606000,TX2505293478765600606000,TX2505073440577500606000,TX2505073440570500606000,TX2505293478760000606000,TX2505073440564700606000,TX2505293478754500606000,TX2505073440565500606000,TX2505293478755600606000,TX2505293478762000606000,TX2505073440572800606000,TX2505293478744300606000,TX2505073440564800606000,TX2505293478755100606000,TX2505073440561800606000,TX2505293478769200606000,TX2505073440580000606000,TX2505073440582500606000,TX2505293478771200606000,TX2505293478745100606000,TX2505073440552600606000,TX2505073440563400606000,TX2505293478753700606000,TX2505293478747800606000,TX2505073440557100606000,TX2505293478748400606000,TX2505073440557500606000,TX2505073440574100606000,TX2505293478763000606000,TX2505293478771800606000,TX2505073440583700606000,TX2505073440554300606000,TX2505293478746200606000,TX2505293478756200606000,TX2505073440566300606000,TX2505073440590100606000,TX2505293478775200606000,TX2505073440588100606000,TX2505073440555700606000,TX2505073440555800606000,TX2505293478757100606000,TX2505073440567300606000,TX2505073440554600606000,TX2505073440567800606000,TX2505293478757400606000,TX2505073440575700606000,TX2505293478764500606000,TX2505073440564200606000,TX2505293478753900606000,TX2505073440563000606000,TX2505293478753200606000,TX2505293478754800606000,TX2505073440564900606000,TX2505073440563600606000,TX2505293478754000606000,TX2505073440574400606000,TX2505293478763200606000,TX2505073440562400606000,TX2505293478753000606000,TX2505073440569100606000,TX2505293478758700606000,TX2505293478755900606000,TX2505073440587900606000,TX2505293478775600606000,TX2505293478758400606000,TX2505073440568600606000,TX2505073440568100606000,TX2505293478757800606000,TX2505073440552800606000,TX2505293478751200606000,TX2505073440565400606000,TX2505293478755400606000,TX2505073440587700606000,TX2505073440565900606000,TX2505293478756000606000,TX2505073440570000606000,TX2505073440565700606000,TX2505293478755500606000,TX2505073440590300606000,TX2505293478777700606000,TX2505073440561500606000,TX2505073440568900606000,TX2505293478758500606000,TX2505293478774900606000,TX2505073440587000606000,TX2505073440582900606000,TX2505293478771400606000,TX2505293478744400606000,TX2505073440553100606000,TX2505073440581100606000,TX2505293478770000606000,TX2505073440582000606000,TX2505293478770800606000,TX2505073440566800606000,TX2505293478759700606000,TX2505073440570100606000,TX2505073440586300606000,TX2505293478750100606000,TX2505073440560500606000,TX2505073440577400606000,TX2505293478766100606000,TX2505293478773800606000,TX2505073440586000606000,TX2505293478759600606000,TX2505073440569900606000,TX2505073440579800606000,TX2505293478768900606000,TX2505073440587100606000,TX2505293478775800606000,TX2505073440588000606000,TX2505073440586600606000,TX2505293478774400606000,TX2505073440567100606000,TX2505293478757000606000,TX2505073440556900606000,TX2505073440589200606000,TX2505073440570200606000,TX2505073440582300606000,TX2505293478757300606000,TX2505073440567400606000,TX2505293478763700606000,TX2505073440575000606000,TX2505073440555100606000,TX2505293478745700606000,TX2505293478743900606000,TX2505293478752200606000,TX2505073440562300606000,TX2505073440579200606000,TX2505293478743400606000,TX2505293478776200606000,TX2505293478763900606000,TX2505073440575400606000,TX2505293478758200606000,TX2505073440568700606000,TX2505073440562800606000,TX2505293478752800606000,TX2505293478768800606000,TX2505073440566200606000,TX2505293478756500606000,TX2505293478751900606000,TX2505073440563200606000,TX2505293478773100606000,TX2505073440584900606000,TX2505293478759500606000,TX2505073440569600606000,TX2505073440576400606000,TX2505073440553400606000,TX2505073440560200606000,TX2505293478750700606000,TX2505293478778100606000,TX2505073440554000606000,TX2505073440588700606000,TX2505293478744600606000,TX2505293478775900606000,TX2505073440588500606000,TX2505073440583500606000,TX2505293478762700606000,TX2505073440573600606000,TX2505293478749500606000,TX2505073440558700606000,TX2505293478748700606000,TX2505073440558000606000,TX2505293478749800606000,TX2505073440558800606000,TX2505293478753800606000,TX2505073440564000606000,TX2505073440581800606000,TX2505293478770600606000,TX2505073440570700606000,TX2505073440575200606000,TX2505293478763800606000,TX2505293478767700606000,TX2505073440578400606000,TX2505293478764200606000,TX2505073440575800606000,TX2505293478778200606000,TX2505293478749700606000,TX2505073440558900606000,TX2505293478773300606000,TX2505073440573700606000,TX2505293478770400606000,TX2505073440581500606000,TX2505293478755000606000,TX2505073440565200606000,TX2505293478760700606000,TX2505073440571500606000,TX2505073440560900606000,TX2505293478751400606000,TX2505073440582800606000,TX2505293478765500606000,TX2505073440577300606000,TX2505293478762500606000,TX2505073440573500606000,TX2505293478769900606000,TX2505073440581000606000,TX2505073440569000606000,TX2505293478758600606000,TX2505293478744500606000,TX2505073440576300606000,TX2505293478764400606000,TX2505073440574600606000,TX2505293478763400606000,TX2505293478777100606000,TX2505293478761200606000,TX2505073440572000606000,TX2505073440568000606000,TX2505293478757600606000,TX2505293478744800606000,TX2505073440584200606000,TX2505293478772500606000,TX2505293478772200606000,TX2505073440584500606000,TX2505293478777300606000,TX2505073440578000606000,TX2505293478766300606000,TX2505293478745600606000,TX2505073440555400606000,TX2505293478776300606000,TX2505073440589000606000,TX2505073440559600606000,TX2505293478748800606000,TX2505073440557800606000,TX2505073440588400606000,TX2505293478775500606000,TX2505293478776700606000,TX2505073440589400606000,TX2505073440574800606000,TX2505293478763500606000,TX2505073440577100606000,TX2505293478765200606000,TX2505293478770900606000,TX2505073440582200606000,TX2505293478772300606000,TX2505073440584100606000,TX2505293478745200606000,TX2505293478759900606000,TX2505073440570400606000,TX2505293478751700606000,TX2505073440561400606000,TX2505073440577600606000,TX2505073440551500606000,TX2505073440559700606000,TX2505073440556600606000,TX2505293478747400606000,TX2505073440555200606000,TX2505293478745800606000,TX2505073440557600606000,TX2505293478748500606000,TX2505073440558400606000,TX2505293478749200606000,TX2505073440571900606000,TX2505293478761400606000,TX2505073440551100606000,TX2505073440580100606000,TX2505293478769300606000,TX2505293478746500606000";
        String[] withdrawNos = withdrawNoStrs.split(",");
		List<String> withdrawNoList = Arrays.asList(withdrawNos);

		// // 读取csv文件, 获取需要过滤的withdrawNo
        // String csvUrl = params[2];
        // List<String> needFilterWithdrawNos = readCsvAndOperate(csvUrl);
        List<String> needFilterWithdrawNos = null;

        QueryWrapper<WithdrawApplyRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("withdraw_channel", "PINGAN");
        queryWrapper.eq("apply_status", "SUCC");
        queryWrapper.in("withdraw_no", withdrawNoList);
        queryWrapper.isNull("receipt_url");
        //只返回withdrawNo, customerType
        queryWrapper.select("withdraw_no", "customer_type");
        List<WithdrawApplyRecord> list = withdrawApplyRecordMapper.selectList(queryWrapper); 

        QueryWrapper<TenantWithdrawApply> tenantQueryWrapper = new QueryWrapper<>();
        tenantQueryWrapper.eq("withdraw_channel", "PINGAN");
        tenantQueryWrapper.eq("apply_status", "SUCC");
        tenantQueryWrapper.in("withdraw_no", withdrawNoList);
        tenantQueryWrapper.isNull("receipt_url");
        //只返回withdrawNo, customerType
        tenantQueryWrapper.select("withdraw_no", "customer_type");
        List<TenantWithdrawApply> tenantList = tenantWithdrawApplyMapper.selectList(tenantQueryWrapper); 

        XxlJobLogger.log("FixHistoryIdolPinganWithdrawPushPullReceiptGlueJavaTaskV2执行: {}, 共{}条数据", param, list.size());
        XxlJobLogger.log("FixHistoryIdolPinganWithdrawPushPullReceiptGlueJavaTaskV2执行样例：{}", JSONObject.toJSONString(list.get(0)));

        for (WithdrawApplyRecord withdrawApplyRecord : list) {
            if (StringUtils.isBlank(withdrawApplyRecord.getWithdrawNo()) || StringUtils.isBlank(withdrawApplyRecord.getCustomerType())) {
                Logger.info("FixHistoryIdolPinganWithdrawPushPullReceiptGlueJavaTaskV2执行参数为空：{}", JSON.toJSONString(withdrawApplyRecord));
                continue;
            }

            if (CollectionUtils.isNotEmpty(needFilterWithdrawNos) && needFilterWithdrawNos.contains(withdrawApplyRecord.getWithdrawNo())) {
                continue;
            }

            Logger.info("FixHistoryIdolPinganWithdrawPushPullReceiptGlueJavaTaskV2执行参数：{}", JSON.toJSONString(withdrawApplyRecord));
            QueryWrapper<PostActionItem> PostActionItemwrapper = new QueryWrapper<>();
            PostActionItemwrapper.eq("biz_id", withdrawApplyRecord.getWithdrawNo())
                   .eq("action_type", PostActionTypes.WITHDRAW_RECEIPT_DOWNLOAD.getName());
            List<PostActionItem> selectList = postActionItemMapper.selectList(PostActionItemwrapper);
            if (CollectionUtils.isEmpty(selectList)) {
                WithdrawReceiptDownloadBO bo = WithdrawReceiptDownloadBO.builder()
                    .withdrawNo(withdrawApplyRecord.getWithdrawNo())
                    .withdrawChannel(WithdrawChannelConstants.PINGAN.getName())
                    .customerType(withdrawApplyRecord.getCustomerType())
                    .build();
                PostActionItemBO itemBO = PostActionItemBO.builder()
                        .bizId(withdrawApplyRecord.getWithdrawNo())
                        .paramObject(bo)
                        .remark("平安提现回执单下载")
                        .actionType(PostActionTypes.WITHDRAW_RECEIPT_DOWNLOAD.getName())
                        .status(PostActionExecStatus.EXECUTE.value())
                        .nextRetryTime(LocalDateTime.now().plusMinutes(RandomUtil.randomLong(1, 10)))
                        .build();
                SpringContextHolder.getBean(PostActionService.class).addAction(itemBO);
            }
        }

        for (TenantWithdrawApply tenantWithdrawApply : tenantList) {
            if (StringUtils.isBlank(tenantWithdrawApply.getWithdrawNo()) || StringUtils.isBlank(tenantWithdrawApply.getCustomerType())) {
                Logger.info("FixHistoryIdolPinganWithdrawPushPullReceiptGlueJavaTaskV2执行参数为空：{}", JSON.toJSONString(tenantWithdrawApply));
                continue;
            }

            if (CollectionUtils.isNotEmpty(needFilterWithdrawNos) && needFilterWithdrawNos.contains(tenantWithdrawApply.getWithdrawNo())) {
                continue;
            }

            Logger.info("FixHistoryIdolPinganWithdrawPushPullReceiptGlueJavaTaskV2执行参数：{}", JSON.toJSONString(tenantWithdrawApply));
            QueryWrapper<PostActionItem> PostActionItemwrapper = new QueryWrapper<>();
            PostActionItemwrapper.eq("biz_id", tenantWithdrawApply.getWithdrawNo())
                   .eq("action_type", PostActionTypes.WITHDRAW_RECEIPT_DOWNLOAD.getName());
            List<PostActionItem> selectList = postActionItemMapper.selectList(PostActionItemwrapper);
            if (CollectionUtils.isEmpty(selectList)) {
                WithdrawReceiptDownloadBO bo = WithdrawReceiptDownloadBO.builder()
                    .withdrawNo(tenantWithdrawApply.getWithdrawNo())
                    .withdrawChannel(WithdrawChannelConstants.PINGAN.getName())
                    .isTenantCustomer(true)
                    .customerType(tenantWithdrawApply.getCustomerType())
                    .build();
                PostActionItemBO itemBO = PostActionItemBO.builder()
                        .bizId(tenantWithdrawApply.getWithdrawNo())
                        .paramObject(bo)
                        .remark("平安提现回执单下载")
                        .actionType(PostActionTypes.WITHDRAW_RECEIPT_DOWNLOAD.getName())
                        .status(PostActionExecStatus.EXECUTE.value())
                        .nextRetryTime(LocalDateTime.now().plusMinutes(RandomUtil.randomLong(1, 10)))
                        .build();
                SpringContextHolder.getBean(PostActionService.class).addAction(itemBO);
            }
        }
        return ReturnT.SUCCESS;
    }

//    //将网络csv文件先放到本地, 然后读取本地文件进行逐行解析, 避免网络链接长期关闭, 解析完成后删除本地文件
//	private List<String> readCsvAndOperate(String csvUrl) throws Exception {
//		File tempFile = null;
//		try {
//			// 1. 下载网络csv文件到本地临时文件
//			tempFile = getTempFile(csvUrl, "pingan_withdraw_receipt_pull_202501_" + System.currentTimeMillis() + ".csv", EXPORT_RECEIPT_TMP_PATH);
//			if (tempFile == null) {
//				Logger.warn("FixHistoryIdolPinganWithdrawPushPullReceiptGlueJavaTaskV2-下载网络csv文件失败, csvUrl: {}", csvUrl);
//				return null;
//			}
//
//			XxlJobLogger.log("CSV文件已下载到本地临时文件: " + tempFile.getAbsolutePath());
//			// 2. 从本地文件读取并逐行处理
//			List<String> results = new ArrayList<>();
//			BufferedReader reader = null;
//			FileReader fileReader = null;
//			try {
//				fileReader = new FileReader(tempFile);
//				reader = new BufferedReader(fileReader);
//				String line;
//				while ((line = reader.readLine()) != null) {
//					// 处理每一行数据
//					if (StringUtils.isNotBlank(line)) {
//						// TODO: 3. 调用提现回单导出接口
//						String[] split = line.split(",", -1);
//						String withdrawNo = split[0];
//						results.add(withdrawNo);
//					}
//				}
//			} finally {
//				if (reader != null) {
//					reader.close();
//				}
//				if (fileReader != null) {
//					fileReader.close();
//				}
//			}
//			XxlJobLogger.log("CSV文件已读取完成: " + tempFile.getAbsolutePath() + ", 共读取到" + results.size() + "条数据");
//			return results;
//		} catch (Exception e) {
//			Logger.error("FixHistoryIdolPinganWithdrawPushPullReceiptGlueJavaTaskV2-处理CSV文件失败", e);
//			throw e;
//		} finally {
//			// 4. 删除临时文件
//			if (tempFile != null && tempFile.exists()) {
//				boolean deleted = tempFile.delete();
//				if (deleted) {
//					Logger.info("FixHistoryIdolPinganWithdrawPushPullReceiptGlueJavaTaskV2-临时文件已删除: " + tempFile.getAbsolutePath());
//				} else {
//					Logger.warn("FixHistoryIdolPinganWithdrawPushPullReceiptGlueJavaTaskV2-临时文件删除失败: " + tempFile.getAbsolutePath());
//				}
//			}
//		}
//	}
//
//    // 获取本地临时文件
//	private File getTempFile(String httpUrl, String tempFileName, String parentDirectory) throws Exception {
//		URLConnection connection = null;
//		InputStream inputStream = null;
//		// 输出流
//		FileOutputStream outputStream = null;
//		try {
//			// 1. 下载网络csv文件到本地临时文件
//			URL url = new URL(httpUrl);
//			connection = url.openConnection();
//			inputStream = connection.getInputStream();
//
//			// 创建临时文件
//			File tempFile = new File(parentDirectory, tempFileName);
//			if (!tempFile.getParentFile().exists()) {
//				tempFile.getParentFile().mkdirs();
//			}
//
//			// 将网络文件保存到本地临时文件
//			outputStream = new FileOutputStream(tempFile);
//			byte[] buffer = new byte[1024];
//			int bytesRead;
//			while ((bytesRead = inputStream.read(buffer)) != -1) {
//				outputStream.write(buffer, 0, bytesRead);
//			}
//			return tempFile;
//		} catch (Exception e) {
//			Logger.error("FixHistoryIdolPinganWithdrawPushPullReceiptGlueJavaTaskV2-获取本地临时文件失败", e);
//			throw e;
//		} finally {
//			if (outputStream != null) {
//				try {
//					outputStream.close();
//				} catch (IOException e) {
//					Logger.error("FixHistoryIdolPinganWithdrawPushPullReceiptGlueJavaTaskV2-关闭输出流失败", e);
//				}
//			}
//			if (inputStream != null) {
//				try {
//					inputStream.close();
//				} catch (IOException e) {
//					Logger.error("FixHistoryIdolPinganWithdrawPushPullReceiptGlueJavaTaskV2-关闭输入流失败", e);
//				}
//			}
//			if (connection instanceof HttpURLConnection) {
//				((HttpURLConnection) connection).disconnect();
//			}
//		}
//	}

}
