package com.akucun.account.proxy.task.request;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Objects;

import com.akucun.account.proxy.common.constant.CustomerType;
import com.akucun.account.proxy.dao.model.TenantWithdrawApply;

import com.akucun.account.proxy.dao.model.WithdrawTaxDetail;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 【租户店主、店长提现  】
 * <AUTHOR>
 *
 */
@Data
@NoArgsConstructor
public class EmployeeWithdrawBizInfo implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	/**
	 * 用户编号
	 */
	private String customerCode;
	
	/**
	 * 用户类型
	 */
	private String customerType;
	/**
	 * 用户名称
	 */
	private String customerName;
	/**
	 * 提现编号
	 */
	private String withdrawNo;
	/**
	 * 申请金额
	 */
	private BigDecimal amount;
	/**
	 * 实际到账金额
	 */
	private BigDecimal withdrawAmt;
	/**
	 * 备注
	 */
	private String remark;
	/**
	 * 银行卡号
	 */
	private String bankNo;
	
	/**
	 * 租户编号
	 */
	private String tenementCode;
	
	/**
	 * 租户类型
	 */
	private String tenementType;
	
	/**
	 * 手续费
	 */
	private BigDecimal tranFee;
	
	public EmployeeWithdrawBizInfo(TenantWithdrawApply apply, WithdrawTaxDetail taxDetail, String bankNo) {
		this.customerCode = apply.getCustomerCode();
		this.customerType = apply.getCustomerType();
		this.customerName = apply.getApplyStatus();
		this.withdrawNo = apply.getWithdrawNo();
		//apply的amount为提现申请金额
		this.amount = apply.getAmount();
		//提现申请明细的withdraw为实际提现金额：扣除了手续费和税费
		// ********新增逻辑: 扣税记录可能为空, 如果为空则默认提现金额为申请金额
		this.withdrawAmt = taxDetail != null ? taxDetail.getWithdraw() : this.amount;
		this.remark = apply.getRemark();
		this.bankNo = bankNo;
		this.tenementCode = apply.getTenantId();
		this.tenementType = CustomerType.AT.getName();
		//手续费:一旦手续费存在，则提现申请金额要加上手续费（平安提现超过n次要手续费的）
		this.tranFee = new BigDecimal(0);
		if(Objects.nonNull(apply.getServiceAmount())) {
			this.tranFee = apply.getServiceAmount();
			this.withdrawAmt = this.withdrawAmt.add(this.tranFee);
		}
	}

}
