package com.akucun.account.proxy.service.tradeflow.script.bizinfovalid;

import com.akucun.account.proxy.facade.stub.enums.IdentifyEnum;
import com.akucun.account.proxy.service.tradeflow.constants.ErrorCodes;
import com.akucun.account.proxy.service.tradeflow.dto.ErrorDTO;
import com.akucun.account.proxy.service.tradeflow.dto.bizinfo.BizInfoRegisterBindcard;
import com.akucun.account.proxy.service.tradeflow.exception.TradeValidException;
import com.akucun.account.proxy.service.tradeflow.script.IBizInfoValidScript;
import org.apache.commons.lang3.StringUtils;

public class ScriptBizValidRegisterBindcard implements IBizInfoValidScript {

    @Override
    public void valid(Object bizInfo) throws TradeValidException {
        BizInfoRegisterBindcard bindcard = (BizInfoRegisterBindcard) bizInfo;
        if(StringUtils.isBlank(bindcard.getCustomerType())) {
            throw new TradeValidException(new ErrorDTO(ErrorCodes.PARAM_ERROR, "bizInfo.customerType为空"));
        }
        if(StringUtils.isBlank(bindcard.getCustomerCode())) {
            throw new TradeValidException(new ErrorDTO(ErrorCodes.PARAM_ERROR, "bizInfo.customerCode为空"));
        }
        if(StringUtils.isBlank(bindcard.getAccountType())) {
            throw new TradeValidException(new ErrorDTO(ErrorCodes.PARAM_ERROR, "bizInfo.accountType为空"));
        }
        if(StringUtils.isBlank(bindcard.getIdType())) {
            throw new TradeValidException(new ErrorDTO(ErrorCodes.PARAM_ERROR, "bizInfo.idType为空"));
        }
        if(!IdentifyEnum.IDCARD.name().equals(bindcard.getIdType()) && !IdentifyEnum.CREDIT_CODE.name().equals(bindcard.getIdType())
                && !IdentifyEnum.HONGKONG_MACAO_TAI_TRAVEL_PERMIT.name().equals(bindcard.getIdType())
                && !IdentifyEnum.CHINESE_PASSPORT.name().equals(bindcard.getIdType()) && !IdentifyEnum.FOREIGN_PASSPORT.name().equals(bindcard.getIdType())) {
            throw new TradeValidException(new ErrorDTO(ErrorCodes.PARAM_ERROR, "bizInfo.idType错误"));
        }
        if(StringUtils.isBlank(bindcard.getIdCode())) {
            throw new TradeValidException(new ErrorDTO(ErrorCodes.PARAM_ERROR, "bizInfo.idCode为空"));
        }
        if(StringUtils.isBlank(bindcard.getCustomerName())) {
            throw new TradeValidException(new ErrorDTO(ErrorCodes.PARAM_ERROR, "bizInfo.customerName为空"));
        }
        if("NM".equals(bindcard.getCustomerType()) || "NMDL".equals(bindcard.getCustomerType()) || "AT".equals(bindcard.getCustomerType())) {
            if(StringUtils.isBlank(bindcard.getTenantId())) {
                throw new TradeValidException(new ErrorDTO(ErrorCodes.PARAM_ERROR, "bizInfo.tenantId为空"));
            }
            if(bindcard.getTenantType() == null) {
                throw new TradeValidException(new ErrorDTO(ErrorCodes.PARAM_ERROR, "bizInfo.tenantType为空"));
            }
        }

        if(StringUtils.isBlank(bindcard.getBindWay())) {
            throw new TradeValidException(new ErrorDTO(ErrorCodes.PARAM_ERROR, "bizInfo.bindWay为空"));
        }
        if(!"SMS".equals(bindcard.getBindWay()) && !"SMA".equals(bindcard.getBindWay())) {
            throw new TradeValidException(new ErrorDTO(ErrorCodes.PARAM_ERROR, "bizInfo.bindWay错误"));
        }
        if("SMS".equals(bindcard.getBindWay())) {
            if(StringUtils.isBlank(bindcard.getSbankCode())) {
                throw new TradeValidException(new ErrorDTO(ErrorCodes.PARAM_ERROR, "bizInfo.sbankCode为空"));
            }
        }
        if(StringUtils.isBlank(bindcard.getSbankName())) {
            throw new TradeValidException(new ErrorDTO(ErrorCodes.PARAM_ERROR, "bizInfo.sbankName为空"));
        }
        if("SMA".equals(bindcard.getBindWay())) {
            if(StringUtils.isBlank(bindcard.getBankCode())) {
                throw new TradeValidException(new ErrorDTO(ErrorCodes.PARAM_ERROR, "bizInfo.bankCode为空"));
            }
            if(StringUtils.isBlank(bindcard.getBankName())) {
                throw new TradeValidException(new ErrorDTO(ErrorCodes.PARAM_ERROR, "bizInfo.bankName为空"));
            }
        }
        if(StringUtils.isBlank(bindcard.getBankCardCode())) {
            throw new TradeValidException(new ErrorDTO(ErrorCodes.PARAM_ERROR, "bizInfo.bankCardCode为空"));
        }
        if(StringUtils.isBlank(bindcard.getMobilePhone())) {
            throw new TradeValidException(new ErrorDTO(ErrorCodes.PARAM_ERROR, "bizInfo.mobilePhone为空"));
        }
    }

}
