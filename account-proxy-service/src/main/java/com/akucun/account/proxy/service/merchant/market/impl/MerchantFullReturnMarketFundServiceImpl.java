package com.akucun.account.proxy.service.merchant.market.impl;

import com.aikucun.common2.base.Result;
import com.aikucun.common2.log.Logger;
import com.akucun.account.center.client.model.TradeInfo;
import com.akucun.account.center.client.model.account.AccountBookDetailDO;
import com.akucun.account.proxy.client.center.AccountCenterClient;
import com.akucun.account.proxy.client.marketaccount.AwardEventCallbackClient;
import com.akucun.account.proxy.client.marketaccount.MerchantMarketAccountOperateClient;
import com.akucun.account.proxy.client.marketaccount.feign.entity.AssetAccountEventCallbackRequest;
import com.akucun.account.proxy.common.constant.CommonConstants;
import com.akucun.account.proxy.common.enums.PostActionExecStatus;
import com.akucun.account.proxy.common.enums.PostActionTypes;
import com.akucun.account.proxy.common.enums.ResponseEnum;
import com.akucun.account.proxy.common.exception.AccountProxyException;
import com.akucun.account.proxy.common.utils.RedisLock;
import com.akucun.account.proxy.facade.stub.enums.MerchantFullReturnObjectTypeEnum;
import com.akucun.account.proxy.facade.stub.enums.PromoActivityIncentiveTypeEnum;
import com.akucun.account.proxy.facade.stub.enums.PromoActivityTypeEnum;
import com.akucun.account.proxy.facade.stub.others.merchant.market.MerchantFullReturnMarketAddFreezeRequest;
import com.akucun.account.proxy.facade.stub.others.merchant.market.MerchantFullReturnMarketFreezeRequest;
import com.akucun.account.proxy.facade.stub.others.merchant.market.MerchantFullReturnMarketSettleRequest;
import com.akucun.account.proxy.facade.stub.others.merchant.market.MerchantFullReturnMarketUnFreezeRequest;
import com.akucun.account.proxy.service.merchant.market.MerchantFullReturnMarketFundService;
import com.akucun.account.proxy.service.merchant.market.constants.MerchantMarketConstants;
import com.akucun.account.proxy.service.postaction.PostActionService;
import com.akucun.account.proxy.service.postaction.bo.PostActionItemBO;
import com.akucun.bcs.bill.facade.stub.api.BcsBillIntegrationApi;
import com.akucun.bcs.bill.facade.stub.enums.RspCodeEnum;
import com.akucun.bcs.bill.facade.stub.model.req.integration.SimpleReceiveBillOrderRequest;
import com.akucun.bcs.bill.facade.stub.model.res.integration.SimpleReceiveBillIntegrationRes;
import com.akucun.bond.client.model.response.AccountBookRes;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Create on : 2024/5/7 10:36
 **/
@Service
public class MerchantFullReturnMarketFundServiceImpl implements MerchantFullReturnMarketFundService {

    @Resource
    private MerchantMarketAccountOperateClient merchantMarketAccountOperateClient;

    @Resource
    private AccountCenterClient accountCenterClient;

    @Resource
    private BcsBillIntegrationApi bcsBillIntegrationApi;

    @Autowired
    private RedisTemplate redisTemplate;

    @Resource
    private PostActionService postActionService;

    //商家营销账户类型
    @Value("${merchant.market.account.key:B19002B29407357DF8FAD51471DBFFA5}")
    private String merchantMarketAccountKey;

    //平台营销账户编码
    @Value("${platform.market.account.customer.code:XDJZ9220001}")
    private String platformMarketAccountCustomerCode;

    //平台营销账户key
    @Value("${platform.market.account.type.key:ABADDC568174068C084A4B8E486F173A}")
    private String platformMarketAccountTypeKey;

    //idol奖励金账户key
    @Value("${ido.bonus.account.type.key:2721679B9C013EC7FC5B31C673494413}")
    private String idoBonusAccountTypeKey;

    //idol余额账户key
    @Value("${ido.cash.account.type.key:8D256656F0A9E0A959024F16A8C910B3}")
    private String idoCashAccountTypeKey;

    //店长余额账户key
    @Value("${distributor.balance.account.type.key:6A55B6EA4B16E697ED8F30DE17AFBA34}")
    private String distributorBalanceAccountTypeKey;

    @Value("${merchant.market.account.trade.query.actual.max.size:200}")
    private Integer merchantMarketAccountTradeQueryActualMaxSize;

    @Override
    public void freeze(MerchantFullReturnMarketFreezeRequest request) {
        String lockKey = String.format(CommonConstants.MERCHANT_FULL_RETURN_MARKET_LOCK_PREFIX + "freeze:%s_%s", request.getMerchantCode(), request.getPromoActivityId());
        RedisLock lock = new RedisLock(redisTemplate, lockKey, 60);
        try {
            /*//货款帐扣模式下无需操作冻结报名
            if (MerchantFullReturnApplyWayEnum.ACCOUNT_DEDUCT.name().equals(request.getApplyWay())) {
                throw new AccountProxyException(ResponseEnum.SYSTEM_EXCEPTION, "货款帐扣报名模式无需冻结余额");
            }*/

            if (!lock.tryLock()) {
                throw new AccountProxyException(ResponseEnum.CONCURRENT_REQUEST);
            }
            //报名申请冻结金额
            BigDecimal freezeAmount = request.getAmount().add(request.getTaxAmount());
            if (freezeAmount.compareTo(BigDecimal.ZERO) == 0) {
                Logger.warn("营销活动报名申请冻结金额为0，忽略，bizNo：{}，merchantCode：{}，objectId：{}，amount：{}，taxAmount：{}",
                        request.getBizNo(), request.getMerchantCode(), request.getObjectId(), request.getAmount(), request.getTaxAmount());
                return;
            }

            //商家编码
            String merchantCode = request.getMerchantCode();
            AccountBookRes accountBookRes = merchantMarketAccountOperateClient.queryMarketAccountBook(merchantCode);
            if (accountBookRes == null) {
                throw new AccountProxyException(ResponseEnum.NO_CENTER_ACCOUNT, "商家营销账户不存在");
            }
            if (freezeAmount.compareTo(accountBookRes.getBalance()) > 0) {
                throw new AccountProxyException(ResponseEnum.ACCORE_101503, "商家营销账户余额不足");
            }

            //源单号=营销活动编号+店铺编码，此处为了兼容同一个商家多个店铺报名同一场平台营销的场景，便于后续出款解冻区分
            String sourceBillNo = String.format("%s_%s", request.getPromoActivityId(), request.getObjectId());
            //活动名称
            String activityName = request.getBizExplain();
            //活动类型
            String activityType = PromoActivityTypeEnum.findEnumByValue(request.getPromoActivityType()).getDesc();
            merchantMarketAccountOperateClient.marketUpdateAccount(freezeAmount, MerchantMarketConstants.MARKET_ACCOUNT_FREEZE_TRADE_TYPE, request.getBizNo(), sourceBillNo,
                    merchantCode, activityName, request.getPromoActivityId(), "活动报名/" + activityType, true);
        } finally {
            lock.unlock();
        }
    }

    @Override
    public void unFreeze(MerchantFullReturnMarketUnFreezeRequest request) {
        String lockKey = String.format(CommonConstants.MERCHANT_FULL_RETURN_MARKET_LOCK_PREFIX + "unFreeze:%s_%s", request.getMerchantCode(), request.getPromoActivityId());
        RedisLock lock = new RedisLock(redisTemplate, lockKey, 60);
        try {
            /*//货款帐扣模式下无需操作解冻
            if (MerchantFullReturnApplyWayEnum.ACCOUNT_DEDUCT.name().equals(request.getApplyWay())) {
                throw new AccountProxyException(ResponseEnum.SYSTEM_EXCEPTION, "货款帐扣报名模式无需解冻余额");
            }*/

            if (!lock.tryLock()) {
                throw new AccountProxyException(ResponseEnum.CONCURRENT_REQUEST);
            }

            //商家编码
            String merchantCode = request.getMerchantCode();
            AccountBookDetailDO freezeTradeDetailDO = accountCenterClient.queryAccountDetailByUnqCondition(merchantCode, request.getBizNo(), merchantMarketAccountKey, MerchantMarketConstants.MARKET_ACCOUNT_FREEZE_TRADE_TYPE);
            if (freezeTradeDetailDO == null) {
                throw new AccountProxyException(ResponseEnum.SYSTEM_EXCEPTION, "未找到活动报名冻结交易");
            }

            //源单号=营销活动编号+店铺编码，此处为了兼容同一个商家多个店铺报名同一场平台营销的场景，便于后续出款解冻区分
            String sourceBillNo = String.format("%s_%s", request.getPromoActivityId(), request.getObjectId());
            //解冻金额 = 冻结交易的金额
            BigDecimal unFreezeAmount = freezeTradeDetailDO.getAmount().abs();
            //活动名称
            String activityName = request.getBizExplain();
            merchantMarketAccountOperateClient.marketUpdateAccount(unFreezeAmount, MerchantMarketConstants.MARKET_ACCOUNT_UNFREEZE_TRADE_TYPE, request.getBizNo(), sourceBillNo,
                    merchantCode, activityName, request.getPromoActivityId(), "活动关闭", true);
        } finally {
            lock.unlock();
        }
    }

    @Override
    public void addFreeze(MerchantFullReturnMarketAddFreezeRequest request) {
        String lockKey = String.format(CommonConstants.MERCHANT_FULL_RETURN_MARKET_LOCK_PREFIX + "addFreeze:%s_%s", request.getMerchantCode(), request.getPromoActivityId());
        RedisLock lock = new RedisLock(redisTemplate, lockKey, 60);
        try {
            /*//充值 & 短期激励时无加冻结操作
            if (MerchantFullReturnApplyWayEnum.RECHARGE.name().equals(request.getApplyWay())
                    && PromoActivityIncentiveTypeEnum.SHORT.name().equals(request.getIncentiveType())) {
                throw new AccountProxyException(ResponseEnum.SYSTEM_EXCEPTION, "短期激励场景下充值报名模式不支持加冻");
            }*/
            if (!lock.tryLock()) {
                throw new AccountProxyException(ResponseEnum.CONCURRENT_REQUEST);
            }

            //活动结束根据预期花费金额进行预冻结
            BigDecimal addFreezeAmount = request.getAmount().add(request.getTaxAmount());
            if (addFreezeAmount.compareTo(BigDecimal.ZERO) == 0) {
                Logger.warn("营销活动预期加冻金额为0，忽略，bizNo：{}，merchantCode：{}，objectId：{}，amount：{}，taxAmount：{}",
                        request.getBizNo(), request.getMerchantCode(), request.getObjectId(), request.getAmount(), request.getTaxAmount());
                return;
            }

            //商家编码
            String merchantCode = request.getMerchantCode();
            //源单号=营销活动编号+店铺编码，此处为了兼容同一个商家多个店铺报名同一场平台营销的场景，便于后续出款解冻区分
            String sourceBillNo = String.format("%s_%s", request.getPromoActivityId(), request.getObjectId());
            //活动名称
            String activityName = request.getBizExplain();
            //活动类型
            String activityType = PromoActivityTypeEnum.findEnumByValue(request.getPromoActivityType()).getDesc();
            //备注
            String remark = "";
            if (PromoActivityIncentiveTypeEnum.SHORT.name().equals(request.getIncentiveType())) {
                /*merchantMarketAccountOperateClient.marketUpdateAccount(addFreezeAmount, MARKET_ACCOUNT_FREEZE_TRADE_TYPE, request.getBizNo(),
                        request.getObjectId(), activityName, request.getPromoActivityId(), "活动结束/"+activityType);*/
                remark = "活动结束";
            } else if (PromoActivityIncentiveTypeEnum.LONG.name().equals(request.getIncentiveType())) {
                remark = "活动周期冻结";
            }

            //调用账户中心查询营销预付冻结、营销预付解冻交易记录
            List<AccountBookDetailDO> accountBookDetailDOS = accountCenterClient.queryAccountDetails(merchantCode, merchantMarketAccountKey, null,
                    sourceBillNo, Arrays.asList(MerchantMarketConstants.MARKET_ACCOUNT_FREEZE_TRADE_TYPE, MerchantMarketConstants.MARKET_ACCOUNT_UNFREEZE_TRADE_TYPE), true);

            //统计当前商家+活动累计营销预付冻结金额
            BigDecimal sumFreezeAmount = accountBookDetailDOS.stream().filter(accountBookDetailDO -> StringUtils.equals(accountBookDetailDO.getTradeType(), MerchantMarketConstants.MARKET_ACCOUNT_FREEZE_TRADE_TYPE))
                    .map(AccountBookDetailDO::getAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO).abs();

            //统计当前商家+活动累计营销预付解冻金额
            BigDecimal sumUnFreezeAmount = accountBookDetailDOS.stream().filter(accountBookDetailDO -> StringUtils.equals(accountBookDetailDO.getTradeType(), MerchantMarketConstants.MARKET_ACCOUNT_UNFREEZE_TRADE_TYPE))
                    .map(AccountBookDetailDO::getAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO).abs();
            if (sumUnFreezeAmount.compareTo(BigDecimal.ZERO) > 0) {
                throw new AccountProxyException(ResponseEnum.SYSTEM_EXCEPTION, String.format("营销活动结束前存在异常解冻数据，customerCode=%s，objectId=%s，sourceBillNo=%s", request.getMerchantCode(), request.getObjectId(), sourceBillNo));
            }

            //计算历史剩余冻结金额
            BigDecimal historyRemainingFreezeAmount = sumFreezeAmount.subtract(sumUnFreezeAmount);

            //如果历史剩余冻结金额大于本次加冻金额
            if (historyRemainingFreezeAmount.compareTo(addFreezeAmount) >= 0) {
                Logger.warn("营销活动加冻金额低于历史剩余冻结金额，忽略，historyRemainingFreezeAmount：{}，request：{}", historyRemainingFreezeAmount, JSONObject.toJSONString(request));
                return;
            }
            //本次实际加冻金额
            BigDecimal actualAddFreezeAmount = addFreezeAmount.subtract(historyRemainingFreezeAmount);
            merchantMarketAccountOperateClient.marketUpdateAccount(actualAddFreezeAmount, MerchantMarketConstants.MARKET_ACCOUNT_FREEZE_TRADE_TYPE, request.getBizNo(), sourceBillNo,
                    merchantCode, activityName, request.getPromoActivityId(), remark + "/" + activityType, true);
        } finally {
            lock.unlock();
        }
    }

    @Override
    public void settle(MerchantFullReturnMarketSettleRequest request) {
        String lockKey = String.format(CommonConstants.MERCHANT_FULL_RETURN_MARKET_LOCK_PREFIX + "addFreeze:%s_%s_%s",
                MerchantFullReturnObjectTypeEnum.SHOP.name().equals(request.getObjectType()) ? request.getMerchantCode() : request.getObjectId(), request.getPromoActivityId(), request.getBizNo());
        RedisLock lock = new RedisLock(redisTemplate, lockKey);
        try {
            if (!lock.tryLock()) {
                throw new AccountProxyException(ResponseEnum.CONCURRENT_REQUEST);
            }

            MerchantFullReturnObjectTypeEnum objectType = MerchantFullReturnObjectTypeEnum.valueOf(request.getObjectType());
            switch (objectType) {
                //case MERCHANT:
                case SHOP:
                    merchantSettleHandle(request);
                    break;
                case PLATFORM:
                    platformSettleHandle(request);
                    break;
                case COMMUNITY:
                    communitySettleHandle(request);
                    break;
                case SELLER:
                case SCHOOL:
                case DISTRIBUTOR:
                    idolSettleHandle(request);
                    break;
                default:
                    throw new AccountProxyException(ResponseEnum.SYSTEM_EXCEPTION, "未能找到主体类型");
            }

            //回调营销活动系统
            awardEventAsyncCallback(request);
        } finally {
            lock.unlock();
        }
    }

    /**
     * 回调营销活动系统
     * @param request
     */
    private void awardEventAsyncCallback(MerchantFullReturnMarketSettleRequest request) {
        try {
            BigDecimal amount = StringUtils.equals(MerchantFullReturnObjectTypeEnum.MERCHANT.name(), request.getObjectType()) ? request.getAmount().add(request.getTaxAmount()) : request.getAmount();

            AssetAccountEventCallbackRequest assetAccountEventCallbackRequest = AwardEventCallbackClient.buildAssetAccountEventCallbackRequest(
                    request.getPromoActivityId(), request.getObjectId(), request.getObjectType(), request.getBizNo(),
                    amount, request.getWay(), request.getSourceScene(), request.getSourceNo());

            PostActionItemBO itemBO = PostActionItemBO.builder()
                    .bizId(request.getBizNo() + "-" + request.getObjectId())
                    .paramObject(assetAccountEventCallbackRequest)
                    .remark("营销活动结算结果异步回调通知")
                    .actionType(PostActionTypes.MARKET_FULL_RETURN_CALLBACK_NOTIFY.getName())
                    .status(PostActionExecStatus.EXECUTE.value())
                    .nextRetryTime(LocalDateTime.now().plusSeconds(1))
                    .retryNums(0)
                    .build();
            postActionService.addAction(itemBO);
        } catch (Exception e) {
            Logger.error("添加营销活动结算结果异步回调通知任务异常", e);
        }
    }

    /**
     * 商家出资扣款
     *
     * @param request
     */
    private void merchantSettleHandle(MerchantFullReturnMarketSettleRequest request) {
        if (StringUtils.isBlank(request.getMerchantCode())) {
            throw new AccountProxyException(ResponseEnum.PARAM_EXCEPTION, "merchantCode不能为空");
        }

        //商家编码
        String merchantCode = request.getMerchantCode();
        //源单号=营销活动编号+店铺编码，此处为了兼容同一个商家多个店铺报名同一场平台营销的场景，便于后续出款解冻区分
        String sourceBillNo = String.format("%s_%s", request.getPromoActivityId(), request.getObjectId());

        //调用账户中心查询营销预付冻结、营销预付解冻交易记录
        List<AccountBookDetailDO> accountBookDetailDOS = accountCenterClient.queryAccountDetails(merchantCode, merchantMarketAccountKey, null,
                sourceBillNo, Arrays.asList(MerchantMarketConstants.MARKET_ACCOUNT_FREEZE_TRADE_TYPE, MerchantMarketConstants.MARKET_ACCOUNT_UNFREEZE_TRADE_TYPE), false);

        //统计当前商家+活动累计营销预付冻结金额
        BigDecimal sumFreezeAmount = accountBookDetailDOS.stream().filter(accountBookDetailDO -> StringUtils.equals(accountBookDetailDO.getTradeType(), MerchantMarketConstants.MARKET_ACCOUNT_FREEZE_TRADE_TYPE))
                .map(AccountBookDetailDO::getAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO).abs();

        //统计当前商家+活动累计营销预付解冻金额
        BigDecimal sumUnFreezeAmount = accountBookDetailDOS.stream().filter(accountBookDetailDO -> StringUtils.equals(accountBookDetailDO.getTradeType(), MerchantMarketConstants.MARKET_ACCOUNT_UNFREEZE_TRADE_TYPE))
                .map(AccountBookDetailDO::getAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO).abs();

        //计算历史剩余冻结金额
        BigDecimal historyRemainingFreezeAmount = sumFreezeAmount.subtract(sumUnFreezeAmount);

        //活动名称
        String activityName = request.getBizExplain();
        //活动类型
        String activityType = PromoActivityTypeEnum.findEnumByValue(request.getPromoActivityType()).getDesc();
        //解冻当前活动剩余的冻结金额
        merchantMarketAccountOperateClient.marketUpdateAccount(historyRemainingFreezeAmount, MerchantMarketConstants.MARKET_ACCOUNT_UNFREEZE_TRADE_TYPE, request.getBizNo(), sourceBillNo,
                merchantCode, activityName, request.getPromoActivityId(), "活动售后期结束/" + activityType, true);

        //获取当前营销活动商家出资金额并发起扣款
        BigDecimal merchantContributionAmount = request.getAmount().add(request.getTaxAmount());
        merchantMarketAccountOperateClient.marketUpdateAccount(merchantContributionAmount, MerchantMarketConstants.MARKET_ACCOUNT_DEDUCT_TRADE_TYPE, request.getBizNo(), sourceBillNo,
                merchantCode, activityName, request.getPromoActivityId(), "活动售后期结束/" + activityType, null);
    }

    /**
     * 平台出资扣款
     *
     * @param request
     */
    private void platformSettleHandle(MerchantFullReturnMarketSettleRequest request) {
        //平台出资金额
        BigDecimal platformContributionAmount = request.getAmount();
        //活动名称
        String activityName = request.getBizExplain();

        if (platformContributionAmount.compareTo(BigDecimal.ZERO) <= 0) {
            Logger.warn("商家营销满返活动平台出资扣款金额小于0，忽略，request：{}", JSONObject.toJSONString(request));
            return;
        }

        //平台出资扣款
        accountCenterClient.dealTrade(platformMarketAccountCustomerCode, platformMarketAccountTypeKey, request.getBizNo(),
                request.getPromoActivityId(), platformContributionAmount, MerchantMarketConstants.PLATFORM_DEDUCT_TRADE_TYPE, activityName);
    }

    /**
     * 队员发奖
     *
     * @param request
     */
    private void communitySettleHandle(MerchantFullReturnMarketSettleRequest request) {
        List<String> idols = request.getRule();
        if (CollectionUtils.isEmpty(idols)) {
            throw new AccountProxyException(ResponseEnum.PARAM_EXCEPTION, "队员(rule)不能为空");
        }
        if (request.getAmount().compareTo(BigDecimal.ZERO) == 0) {
            Logger.info("communitySettleHandle奖励金额为0，暂不处理，activityNo：{}，bizNo：{}，customerCode：{}", request.getPromoActivityId(), request.getBizNo(), JSON.toJSONString(request.getRule()));
            return;
        }
        //活动名称
        String activityName = request.getBizExplain();
        //活动ID
        String promoActivityId = request.getPromoActivityId();
        //总奖励金额
        BigDecimal totalAwardAmount = request.getAmount();
        //待发奖爱豆数量
        int idolSize = idols.size();
        //奖励平均金额
        BigDecimal average = totalAwardAmount.divide(BigDecimal.valueOf(idolSize), 2, RoundingMode.HALF_UP);

        List<TradeInfo> tradeInfos = new ArrayList<>();
        for (Integer i = 0; i < idolSize; i++) {
            //ido编号
            String ido = idols.get(i);
            //当前爱豆发奖金额
            BigDecimal awardAmount = i.compareTo(idolSize - 1) == 0 ? totalAwardAmount : average;
            //发奖
            switch (request.getWay()) {
                case "BONUS":
                    //iDoAccountAwardBonus(awardAmount, promoActivityId, ido, activityName, promoActivityId);
                    TradeInfo bonusTradeInfo = AccountCenterClient.buildTradeInfo(ido, idoBonusAccountTypeKey, promoActivityId, promoActivityId, awardAmount, MerchantMarketConstants.BONUS_ACCOUNT_AWARD_TRADE_TYPE, activityName);
                    tradeInfos.add(bonusTradeInfo);
                    break;
                case "CASH":
                    //iDoAccountAwardCash(awardAmount, promoActivityId, ido, activityName, promoActivityId);
                    TradeInfo cashTradeInfo = AccountCenterClient.buildTradeInfo("NM" + ido, idoCashAccountTypeKey, promoActivityId, promoActivityId, awardAmount, MerchantMarketConstants.IDOL_BALANCE_ACCOUNT_AWARD_TRADE_TYPE, activityName);
                    tradeInfos.add(cashTradeInfo);
                    break;
                default:
                    throw new AccountProxyException(ResponseEnum.PARAM_EXCEPTION, "无效的发奖方式");
            }
            totalAwardAmount = totalAwardAmount.subtract(average);
        }

        //给队员批量发奖
        accountCenterClient.batchDealTrade(tradeInfos);
    }

    /**
     * idol发奖
     *
     * @param request
     */
    private void idolSettleHandle(MerchantFullReturnMarketSettleRequest request) {
        //待结算金额
        BigDecimal amount = request.getAmount();
        //活动名称
        String activityName = request.getBizExplain();
        //活动标识
        String activityNo = request.getPromoActivityId();
        //活动类型
        String promoActivityTypeKey = request.getPromoActivityType();
        //用户类型 1真实 ， 2打包
        String dataType = request.getDataType();

        //如果非真实用户，则落平台应收科目，调用结算落销返技术服务费数据
        if (StringUtils.equals(dataType, "2")) {
            doGeneratePlatformReceivableBill(request.getBizNo(), amount, activityName, activityNo, promoActivityTypeKey);
        } else {
            //给真实爱豆发奖
            switch (request.getWay()) {
                case "BONUS":
                    iDoAccountAwardBonus(amount, request.getBizNo(), request.getObjectId(), activityName, activityNo, request.getObjectType());
                    break;
                case "CASH":
                    iDoAccountAwardCash(amount, request.getBizNo(), request.getObjectId(), activityName, activityNo, request.getObjectType());
                    break;
                default:
                    throw new AccountProxyException(ResponseEnum.PARAM_EXCEPTION, "无效的发奖方式");
            }
        }
    }

    /**
     * ido发现金
     */
    private void iDoAccountAwardCash(BigDecimal awardAmount, String bizNo, String customerCode, String activityName, String sourceBillNo, String objectType) {
        if (awardAmount.compareTo(BigDecimal.ZERO) == 0) {
            Logger.info("iDoAccountAwardCash奖励金额为0，暂不处理，activityNo：{}，bizNo：{}，customerCode：{}", sourceBillNo, bizNo, customerCode);
            return;
        }
        if (MerchantFullReturnObjectTypeEnum.DISTRIBUTOR.name().equals(objectType)) {
            accountCenterClient.dealTrade(customerCode, distributorBalanceAccountTypeKey, bizNo, sourceBillNo, awardAmount, MerchantMarketConstants.DISTRIBUTOR_BALANCE_ACCOUNT_AWARD_TRADE_TYPE, activityName);
        } else {
            accountCenterClient.dealTrade("NM" + customerCode, idoCashAccountTypeKey, bizNo, sourceBillNo, awardAmount, MerchantMarketConstants.IDOL_BALANCE_ACCOUNT_AWARD_TRADE_TYPE, activityName);
        }
    }

    /**
     * ido发奖励金
     */
    private void iDoAccountAwardBonus(BigDecimal awardAmount, String bizNo, String customerCode, String activityName, String sourceBillNo, String objectType) {
        if (awardAmount.compareTo(BigDecimal.ZERO) == 0) {
            Logger.info("iDoAccountAwardBonus奖励金额为0，暂不处理，activityNo：{}，bizNo：{}，customerCode：{}", sourceBillNo, bizNo, customerCode);
            return;
        }
        if (MerchantFullReturnObjectTypeEnum.DISTRIBUTOR.name().equals(objectType)) {
            throw new AccountProxyException(ResponseEnum.SYSTEM_EXCEPTION, "店长奖励不支持发放至奖励金账户");
        } else {
            accountCenterClient.dealTrade(customerCode, idoBonusAccountTypeKey, bizNo, sourceBillNo, awardAmount, MerchantMarketConstants.BONUS_ACCOUNT_AWARD_TRADE_TYPE, activityName);
        }
    }

    /**
     * 生成营销满返平台技术服务费
     *
     * @param amount
     * @param activityName
     * @param activityNo
     * @param promoActivityTypeKey
     */
    private void doGeneratePlatformReceivableBill(String bizNo, BigDecimal amount, String activityName, String activityNo, String promoActivityTypeKey) {
        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            Logger.info("doGeneratePlatformReceivableBill 满返生成平台服务费操作交易金额小于等于0，暂不处理，activityNo：{}", activityNo);
            return;
        }
        SimpleReceiveBillOrderRequest request = new SimpleReceiveBillOrderRequest();
        request.setAmount(amount);
        request.setBusinessDate(new Date());
        //固定渠道
        request.setChannel("MARKET");
        //固定值
        request.setEventCode("SALE_FULL_RETURN");
        request.setSourceBusinessNo(bizNo);
        Map<String, Object> exts = new HashMap<>();
        exts.put("promoActivityNo", activityNo);
        exts.put("promoActivityName", activityName);
        exts.put("promoActivityType", promoActivityTypeKey);
        request.setExts(exts);
        com.mengxiang.base.common.log.Logger.info("doGeneratePlatformReceivableBill 满返生成平台服务费操作 request: {}", JSON.toJSONString(request));
        Result<SimpleReceiveBillIntegrationRes> result = bcsBillIntegrationApi.simpleReceiveBillOrder(request);
        com.mengxiang.base.common.log.Logger.info("doGeneratePlatformReceivableBill 满返生成平台服务费操作 request: {}，response: {}", JSON.toJSONString(request), JSON.toJSONString(result));
        if (result.getSuccess() && result.getData() != null && StringUtils.equals(RspCodeEnum.SUCCESS.getCode(), result.getData().getRspCode())) {
        } else {
            throw new AccountProxyException(ResponseEnum.SYSTEM_EXCEPTION, result.getMessage());
        }
    }
}
