package com.akucun.account.proxy.service.receipt;

import java.util.Base64;

import com.akucun.account.proxy.common.constant.Constant;
import com.akucun.account.proxy.dao.model.PaymentTransfer;
import com.akucun.account.proxy.service.transfer.PaymentTransferService;
import org.apache.commons.lang3.StringUtils;

import com.aikucun.common2.base.Result;
import com.aikucun.common2.base.exception.IErrorCode;
import com.akucun.account.proxy.client.file.FileUploadClient;
import com.akucun.account.proxy.client.wechat.PayGatewayWxClient;
import com.akucun.account.proxy.common.constant.ApplyStatus;
import com.akucun.account.proxy.common.constant.WithdrawChannelConstants;
import com.akucun.account.proxy.common.enums.ResponseEnum;
import com.akucun.account.proxy.dao.mapper.WithdrawApplyRecordMapper;
import com.akucun.account.proxy.dao.model.WithdrawApplyRecord;
import com.akucun.account.proxy.service.common.SpringContextHolder;
import com.akucun.account.proxy.service.receipt.bo.WithdrawReceiptDownloadBO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.mengxiang.base.common.log.Logger;
import com.mengxiang.fileupload.dto.FileInfo;

public class WechatWithdrawReceiptDownloadHandler extends AbstractWithdrawReceiptDownloadHandler {

    public WechatWithdrawReceiptDownloadHandler(WithdrawReceiptDownloadBO bo) {
        super(bo);  
    }

    @Override
    public Result<Void> handle() {
        try {
            if (StringUtils.isBlank(bo.getWithdrawNo())) {
                return Result.error(IErrorCode.ARGUMENT_ERROR, "参数缺失, 无法下载微信提现回单");
            }
            WithdrawApplyRecordMapper withdrawApplyRecordMapper = SpringContextHolder.getBean(WithdrawApplyRecordMapper.class);
            // 查询提现单据
            WithdrawApplyRecord withdrawApplyRecord = withdrawApplyRecordMapper.selectOne(new LambdaQueryWrapper<WithdrawApplyRecord>().eq(WithdrawApplyRecord::getWithdrawNo, bo.getWithdrawNo()));
            if (withdrawApplyRecord == null) {
                return Result.error(IErrorCode.ARGUMENT_ERROR, "未查询到提现单据");
            }
            if (!ApplyStatus.SUCC.getName().equals(withdrawApplyRecord.getApplyStatus())) {
                return Result.error(IErrorCode.ARGUMENT_ERROR, "提现未成功, 无法下载回单");
            }
            if (!WithdrawChannelConstants.WECHAT.getName().equals(withdrawApplyRecord.getWithdrawChannel())) {
                return Result.error(IErrorCode.ARGUMENT_ERROR, "非微信提现, 无法下载回单");
            }

            //如果请求中透传的批次号、付款商户号为空，则从数据库获取提现单据的商户号和批次号
            if (StringUtils.isBlank(bo.getBatchNo())) {
                bo.setBatchNo(withdrawApplyRecord.getBatchNo());
            }
            if (StringUtils.isBlank(bo.getMerchantCode())) {
                PaymentTransfer paymentTransfer = SpringContextHolder.getBean(PaymentTransferService.class).queryBySourceNo(Constant.XD_WECHAT, bo.getWithdrawNo());
                if (paymentTransfer == null) {
                    return Result.error(IErrorCode.ARGUMENT_ERROR, "未查询到转账单据");
                }
                bo.setMerchantCode(paymentTransfer.getMchCode());
            }

            if (StringUtils.isAnyBlank(bo.getWithdrawNo(), bo.getMerchantCode(), bo.getBatchNo())) {
                return Result.error(IErrorCode.ARGUMENT_ERROR, "参数缺失, 无法下载微信提现回单");
            }

            Result<String> result = SpringContextHolder.getBean(PayGatewayWxClient.class).downloadTransferReceipt(bo.getBatchNo(), bo.getWithdrawNo(), bo.getMerchantCode(), bo.getWechatApplySuccess());
            if (!result.getSuccess() || StringUtils.isBlank(result.getData())) {
                // 如果调用pay-gateway-wx申请转账回单成功, 则将下载任务中的参数设置为true, 下一次则直接进行查询
                if (ResponseEnum.BATCH_TRANSFER_RECEIPT_APPLY_SUBMITTED.getCode().equals(result.getCode())) {
                    bo.setWechatApplySuccess(Boolean.TRUE);
                }
                return Result.error(IErrorCode.SYSTEM_ERROR, "下载微信转账回单失败:" + result.getMessage());
            }

            String receiptFileTmpUrl = result.getData();
            // 下载提现回单
            FileUploadClient fileUploadClient = SpringContextHolder.getBean(FileUploadClient.class);
            byte[] fileBytes = fileUploadClient.getHttpFileBytes(receiptFileTmpUrl);

            // 将base64字符串转换为pdf字节数组, 通过字节数组上传至OBS
            //byte[] fileBytes = Base64.getDecoder().decode(receiptBase64String);

            // 生成文件名
            String fileName = String.format("%s_%s.pdf", bo.getWithdrawNo(), withdrawApplyRecord.getCustomerCode());
            // 上传OBS
            Result<FileInfo> uploadFileResult = SpringContextHolder.getBean(FileUploadClient.class).upload(fileBytes, fileName, fileName, FileUploadClient.WECHAT_WITHDRAW_RECEIPT_PARENT_DIR);
            if (!uploadFileResult.getSuccess()) {
                return Result.error(IErrorCode.ARGUMENT_ERROR, "微信提现回单上传OBS失败");
            }
            FileInfo fileInfo = uploadFileResult.getData();
            if (fileInfo == null || StringUtils.isBlank(fileInfo.getFileUrl())) {
                return Result.error(IErrorCode.ARGUMENT_ERROR, "微信提现回单上传OBS失败");
            }

            // 更新回单地址
            WithdrawApplyRecord updateRecord = new WithdrawApplyRecord();
            updateRecord.setReceiptUrl(fileInfo.getFileUrl());
            withdrawApplyRecordMapper.update(updateRecord, new LambdaUpdateWrapper<WithdrawApplyRecord>().eq(WithdrawApplyRecord::getWithdrawNo, bo.getWithdrawNo()));            
            return Result.success();
        } catch (Exception e) {
            Logger.error("微信提现回单下载异常, withdrawNo:{}", bo.getWithdrawNo(), e);
            return Result.error(IErrorCode.SYSTEM_ERROR, "微信提现回单下载异常");
        }
    }

}
