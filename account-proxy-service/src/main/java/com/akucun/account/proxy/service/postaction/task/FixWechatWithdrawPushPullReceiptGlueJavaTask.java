/*
 * @Author: Lee
 * @Date: 2025-03-28 17:00:20
 * @Description: Do not edit
 * @License: Copyright (c) 2025, Lee
 */
package com.akucun.account.proxy.service.postaction.task;

import com.aikucun.common2.log.Logger;
import com.akucun.account.proxy.common.constant.Constant;
import com.akucun.account.proxy.common.constant.WithdrawChannelConstants;
import com.akucun.account.proxy.common.enums.PostActionExecStatus;
import com.akucun.account.proxy.common.enums.PostActionTypes;
import com.akucun.account.proxy.dao.mapper.PaymentTransferMapper;
import com.akucun.account.proxy.dao.mapper.PostActionItemMapper;
import com.akucun.account.proxy.dao.mapper.WithdrawApplyRecordMapper;
import com.akucun.account.proxy.dao.model.PaymentTransfer;
import com.akucun.account.proxy.dao.model.PostActionItem;
import com.akucun.account.proxy.dao.model.WithdrawApplyRecord;
import com.akucun.account.proxy.service.acct.AccountWithdrawService;
import com.akucun.account.proxy.service.common.SpringContextHolder;
import com.akucun.account.proxy.service.postaction.PostActionService;
import com.akucun.account.proxy.service.postaction.bo.PostActionItemBO;
import com.akucun.account.proxy.service.receipt.bo.WithdrawReceiptDownloadBO;
import com.akucun.account.proxy.service.transfer.PaymentTransferService;
import com.akucun.bcs.channel.facade.stub.dto.WechatWithdrawRequest;
import com.akucun.fps.common.util.GsonUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.log.XxlJobLogger;

import cn.hutool.core.util.RandomUtil;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Random;

import javax.annotation.Resource;

/**
 * @Description
 * @aurhor lee
 * @date: 2022/9/13 14:34
 */
public class FixWechatWithdrawPushPullReceiptGlueJavaTask extends IJobHandler {

    @Resource
    private WithdrawApplyRecordMapper withdrawApplyRecordMapper;

    @Resource
    private PaymentTransferMapper paymentTransferMapper;

    @Resource
    private PaymentTransferService paymentTransferService;

    @Resource
    private PostActionItemMapper postActionItemMapper;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        /**
         * SELECT *  FROM post_action_item WHERE biz_id like 'TX2501%' and action_type = 'WITHDRAW_RECEIPT_DOWNLOAD' and biz_status != 1 
and param like '%"wechatApplySuccess":true%'  
         */
//        QueryWrapper<PostActionItem> queryWrapper = new QueryWrapper<>();
//        queryWrapper.eq("action_type", "WITHDRAW_RECEIPT_DOWNLOAD");
//        queryWrapper.ne("biz_status", 1);
//        queryWrapper.like("param", "\"wechatApplySuccess\":true");
//        queryWrapper.like("biz_id", "TX2501%");
//        List<PostActionItem> list = postActionItemMapper.selectList(queryWrapper);
//        XxlJobLogger.log("FixWechatWithdrawPushPullReceiptGlueJavaTask执行: {}, 共{}条数据", param, list.size());
//
//
//
//
//        for (PostActionItem postActionItem : list) {
//            WithdrawReceiptDownloadBO bo = GsonUtils.getInstance().fromJson(postActionItem.getParam(), WithdrawReceiptDownloadBO.class);
//            bo.setWechatApplySuccess(Boolean.FALSE);
//            postActionItem.setParam(GsonUtils.getInstance().toJson(bo));
//            postActionItemMapper.updateById(postActionItem);
//        }
        PostActionItem postActionItem = postActionItemMapper.selectById(711267801);
        if (postActionItem != null) {
            postActionItem.setNextRetryTime(LocalDateTime.now());
            postActionItem.setRetryNums(1);
            postActionItemMapper.updateById(postActionItem);
        }
        return ReturnT.SUCCESS;
    }

}
