package com.akucun.account.proxy.service.tradeflow.instruction;

import com.alibaba.fastjson.JSON;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfig;
import org.springframework.stereotype.Service;

@Service
public class TradeInstructionService {

    @ApolloConfig
    private Config config;

    private final static String INSTRUCTION_PREFIX = "INSTRUCTION";
    private final static String DELIMITER = ".";

    public TradeInstruction getInstruction(String bizType, String productCategory, String requestPlatform) {
        String instructionStr = config.getProperty(String.join(DELIMITER, INSTRUCTION_PREFIX, bizType, productCategory, requestPlatform), null);
        if(instructionStr == null) {
            instructionStr = config.getProperty(String.join(DELIMITER, INSTRUCTION_PREFIX, bizType, productCategory), null);
        }
        if(instructionStr == null) {
            instructionStr = config.getProperty(String.join(DELIMITER, INSTRUCTION_PREFIX, bizType), null);
        }
        if(instructionStr == null) {
            return null;
        } else {
            return JSON.parseObject(instructionStr, TradeInstruction.class);
        }
    }

}
