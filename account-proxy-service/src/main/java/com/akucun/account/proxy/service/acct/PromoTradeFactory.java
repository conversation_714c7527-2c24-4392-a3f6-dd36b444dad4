package com.akucun.account.proxy.service.acct;

import com.akucun.account.proxy.facade.stub.enums.RewardTypeEnum;
import com.alibaba.fastjson.JSON;
import com.mengxiang.base.common.log.Logger;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : 奖励下发工厂
 * @Create on : 2025/1/15 14:02
 **/
@Component
public class PromoTradeFactory implements InitializingBean {
    @Autowired
    Map<String, PromoTradeHandler> recvHandlerMap;

    /**
     * 服务KV映射map
     * K：taskType
     * V: 任务处理器
     */
    Map<RewardTypeEnum, PromoTradeHandler> recvServiceMap;

    /**
     * 获取服务
     *
     * @param rewardTypeEnum
     * @return
     */
    public PromoTradeHandler getHandler(RewardTypeEnum rewardTypeEnum) {
        return recvServiceMap.get(rewardTypeEnum);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        Logger.info("-------->初始化奖励下发处理器begin...");
        recvServiceMap = new HashMap<>();

        for (Map.Entry<String, PromoTradeHandler> entry : recvHandlerMap.entrySet()) {
            if (!recvServiceMap.containsKey(entry.getValue().getBusiType())) {
                recvServiceMap.put(entry.getValue().getBusiType(), entry.getValue());
            }
        }
        Logger.info("奖励下发处理器:{}", JSON.toJSONString(recvServiceMap));

        Logger.info("-------->初始化奖励下发处理器complete...");
    }
}
