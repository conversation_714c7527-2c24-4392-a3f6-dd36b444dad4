package com.akucun.account.proxy.service.common.bo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2020/12/21 17:35
 */
@Data
public class AccountTradeReq {

    /**
     * 订单来源系统编码
     */
    private String sourceCode;

    /**
     * 交易渠道
     */
    private String channel;

    /**
     * 来源单号
     */
    private String sourceNo;

    /**
     * 交易单号
     */
    private String tradeNo;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 客户编码
     */
    private String customerCode;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 客户类型
     */
    private String customerType;

    /**
     * 支付金额（元）
     */
    private BigDecimal amount;

    /**
     * 交易备注
     */
    private String remark;

    private String tradeType;

    private String bizRefundNo;

    private String userRole;

    private String tenantId;

}
