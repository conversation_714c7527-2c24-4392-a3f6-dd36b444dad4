package com.akucun.account.proxy.task.request;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

import lombok.Builder;
import lombok.Data;

/**
 * 任务受理请求
 */
@Data
public class FinTaskAcceptRequest implements Serializable {
    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	/**
     * 请求序号：请求方生成。每次请求生成，同一个请求序号只会被授理一次。
     */
    private String requestNo;

    /**
     * 来源平台：任务中心分配。商家云(YUN)，交付平台(DELIVER)
     */
    private String requestPlatform;
    /**
     * 创建任务的用户类型：任务中心分配。商家(MERCHANT)，系统(SYSTEM)
     */
    private String createUserType;
    /**
     * 创建任务的用户ID：商家标识ID，系统(SYSTEM)
     */
    private String createUserId;
    /**
     * 业务分类：任务中心分配。导入(IMPORT)，导出(EXPORT)，交付自履约-积分充值(DELIVER_INTEGRAL_RECHARGE)
     */
    private String bizCategory;
    /**
     * 业务单号：外部业务传入。
     */
    private String bizNo;
    /**
     * 业务信息：与业务协商制订。用户名称(userName)，任务维度通知地址(taskNotifyUrl)，业务数据维度通知地址(bizNotifyUrl)，...
     */
    private Map<String, String> bizInfo = new HashMap<>();
    /**
     * 回传参数：业务自行定义。异步通知业务会原样回传。
     */
    private String returnParameter;
    /**
     * 任务说明：供后台展示，供人阅读。
     */
    private String memo;
    /**
     * 任务拓展信息：与业务协商制订。外部可通过此参数作用于task上。
     */
    private Map<String, String> ext = new HashMap<>();
    /**
     * 用户IP.
     */
    private String userIp;
    /**
     * 业务来源，个人小程序(applet), 饷店(h5)等
     */
    private String source;
}
