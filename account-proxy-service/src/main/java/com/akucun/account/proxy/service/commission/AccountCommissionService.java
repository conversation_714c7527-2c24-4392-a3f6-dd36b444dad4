package com.akucun.account.proxy.service.commission;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.dao.model.AccountCommissionTrade;
import com.akucun.account.proxy.service.commission.bo.AccountCommissionBO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Author: silei
 * @Date: 2020/12/8
 * @desc:
 */
public interface AccountCommissionService extends IService<AccountCommissionTrade> {

    /**
     * 分佣
     * @param bo
     * @return
     */
    Result<Void> allocate(AccountCommissionBO bo);

    /**
     * 异步分佣
     * @param trade
     * @return
     */
    Result<Void> asyncAllocate(AccountCommissionTrade trade);
}
