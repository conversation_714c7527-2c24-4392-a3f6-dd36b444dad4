package com.akucun.account.proxy.service.handler.trade;

import com.aikucun.common2.utils.datamasking.DataMask;
import com.akucun.account.center.client.model.TradeInfo;
import com.akucun.account.proxy.client.account.AccountClient;
import com.akucun.account.proxy.common.constant.CommonConstants;
import com.akucun.account.proxy.common.enums.ResponseEnum;
import com.akucun.account.proxy.common.exception.AccountProxyException;
import com.akucun.account.proxy.common.utils.AmountUtils;
import com.akucun.account.proxy.facade.stub.enums.TradeChannel;
import com.akucun.account.proxy.service.common.bo.AccountExecStepContext;
import com.akucun.account.proxy.service.common.bo.AccountTradeReq;
import com.akucun.account.proxy.service.common.step.AbstractHandler;
import com.akucun.account.proxy.service.trade.AccountTradeCommonService;
import com.akucun.account.proxy.service.trade.AccountTradeService;
import com.akucun.common.Result;
import com.akucun.member.api.vo.MoneyAddVO;
import com.alibaba.fastjson.JSON;
import com.mengxiang.base.common.log.Logger;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2020/12/21 16:45
 */
@Component
public class BonusAcctRefundHandler extends AbstractHandler {

    @Autowired
    private AccountTradeService accountTradeService;
    @Autowired
    private AccountClient accountClient;
    @Autowired
    private AccountTradeCommonService accountTradeCommonService;

    @Override
    protected boolean preCheck(AccountExecStepContext accountExecStepContext) {
        return true;
    }

    @Override
    protected void doSubmitBefore(AccountExecStepContext accountExecStepContext) {

    }

    @Override
    protected void doSubmit(AccountExecStepContext accountExecStepContext) {
        AccountTradeReq accountTradeReq = accountExecStepContext.getAccountTradeReq();
        Result result = null;
        try {
            // 获取账户灰度
            String branch = accountClient.getBalanceBranch(accountTradeReq.getCustomerCode());
            if (StringUtils.isEmpty(branch)) {
                throw new AccountProxyException(ResponseEnum.TRADE_GET_ACCOUNT_BRANCH_ERROR);
            }
            if(CommonConstants.BRANCH_MEMBER.equals(branch)) {
                result = addMoney(accountTradeReq);
                //余额系统支付成功再次通知新账户系统，后面灰度成功删除
                if (result.isSuccess()) {
                    try {
                        dealTrade(accountTradeReq);
                    } catch (Exception e) {
                        Logger.warn("余额退款成功,再次调用新余额系统失败：{}", e);
                    }
                }
            } else if(CommonConstants.BRANCH_ACCOUNT_CENTER.equals(branch)) {
                result = dealTrade(accountTradeReq);
                //新余额系统支付成功再次通知老账户系统，后面灰度成功删除
                if (result.isSuccess()) {
                    try {
                        addMoney(accountTradeReq);
                    } catch (Exception e) {
                        Logger.warn("新余额退款成功,再次调用老余额系统失败：{}", e);
                    }
                }
            }
        } catch (Exception e) {
            Logger.error("余额退款异常，请求参数：{}，异常信息：{}", DataMask.toJSONString(accountTradeReq), e);
            throw new AccountProxyException(ResponseEnum.TRADE_PAY_ERROR);
        }
        if(Objects.isNull(result)) {
            Logger.error("余额退款异常，请求参数：{}，结果为空", DataMask.toJSONString(accountTradeReq));
            throw new AccountProxyException(ResponseEnum.TRADE_PAY_RETURN_NULL);
        }
        accountExecStepContext.setRespMessage(result);
    }

    @Override
    protected void doSubmitAfter(AccountExecStepContext accountExecStepContext) throws Exception {
        accountTradeCommonService.handlerAfterCommit(accountExecStepContext);
    }

    /**
     * 老账户余额支付
     *
     * @param accountTradeReq
     * @return
     */
    private Result<Void> addMoney(AccountTradeReq accountTradeReq) {
        Result<Void> result = Result.success();
        MoneyAddVO req = new MoneyAddVO();
        Map<String, Object> reqdate = new HashMap<String, Object>();
        req.setUserid(accountTradeReq.getUserId());
        req.setJine(AmountUtils.yuan2fen(accountTradeReq.getAmount().setScale(2, BigDecimal.ROUND_HALF_UP).toString()).intValue());
        req.setYuanyin(3);
        req.setParams(reqdate);
        reqdate.put("tuikuanjiluID", accountTradeReq.getTradeNo());
        reqdate.put("dingdanID", accountTradeReq.getSourceNo());
        reqdate.put("transId", accountTradeReq.getTradeNo());
        boolean isSuccess = accountClient.balanceRefund(req);
        if (isSuccess) {
            result.setSuccess(Boolean.TRUE);
        } else {
            result.setSuccess(Boolean.FALSE);
        }
        return result;
    }


    /**
     * 新余额退款
     *
     * @param accountTradeReq
     * @return
     */
    private Result<Void> dealTrade(AccountTradeReq accountTradeReq) {
        Result<Void> result = Result.success();
        TradeInfo tradeInfo = new TradeInfo();
        tradeInfo.setAccountTypeKey(CommonConstants.NEW_BALANCE_CUSTOMER_KEY);
        tradeInfo.setCustomerCode(accountTradeReq.getCustomerCode());
        tradeInfo.setTradeType(CommonConstants.NEW_REFUND_BALANCE_CUSTOMER_TYPE);
        tradeInfo.setAmount(accountTradeReq.getAmount());
        tradeInfo.setTradeNo(accountTradeReq.getTradeNo());
        tradeInfo.setSourceBillNo(accountTradeReq.getSourceNo());
        tradeInfo.setRemark(TradeChannel.queryDescByCode(accountTradeReq.getChannel()) + ":" + accountTradeReq.getBizRefundNo());
        Result<Void> tradeResult = accountClient.newBalanceTrade(tradeInfo);
        if (null != tradeResult && tradeResult.isSuccess()) {
            result.setSuccess(Boolean.TRUE);
        } else {
            result.setSuccess(Boolean.FALSE);
            result.setCode(tradeResult.getCode());
            result.setMessage(tradeResult.getMessage());
            Logger.warn("新余额退款失败result={}", DataMask.toJSONString(tradeResult));
        }
        return result;
    }
    
}
