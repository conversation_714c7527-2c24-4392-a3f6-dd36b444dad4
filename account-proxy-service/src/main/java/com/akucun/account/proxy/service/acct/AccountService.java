package com.akucun.account.proxy.service.acct;

import com.akucun.account.center.client.model.TradeInfo;
import com.akucun.account.proxy.facade.stub.others.dto.req.AccountInfoReq;
import com.akucun.account.proxy.facade.stub.others.dto.res.AccountInfoResp;
import com.akucun.common.Result;

import java.util.List;

/**
 * @Author: silei
 * @Date: 2020/10/22
 * @desc:
 */
public interface AccountService {

    /**
     * 锁定账户
     * @param customerCode
     * @param customerType
     * @param time
     */
    void lockAccount(String customerCode, String customerType, Long time);

    /**
     * 解锁账户
     * @param customerCode
     * @param customerType
     */
    void unlockAccount(String customerCode, String customerType);

    /**
     * 查询账户（余额）信息
     * @param list
     * @return
     */
    List<AccountInfoResp> queryAccountInfo(List<AccountInfoReq> list);

    /**
     * 账户升级绑卡状态查询
     * @param customerCode
     * @param customerType
     * @return
     */
    Boolean queryAccountBindStatus(String customerCode, String customerType);

    /**
     * 账户升级锁定状态查询
     * @param customerCode
     * @param customerType
     * @return
     */
    Boolean queryAccountLockStatus(String customerCode, String customerType);

    /**
     * 是否绑过卡，查询失败返回null
     * @param customerCode
     * @param customerType
     * @return
     */
    Boolean hasBindRecord(String customerCode, String customerType);

    /**
     * 是否有平安账户，查询失败返回null
     * @param customerCode
     * @param customerType
     * @return
     */
    Boolean hasPinganAccount(String customerCode, String customerType);

    /**
     * 是否租户
     * @param customerCode
     * @param customerType
     * @return
     */
    boolean isTenantCustomer(String customerCode, String customerType);

    /**
     * 是否存在处理中的交易
     * @param customerCode
     * @param customerType
     * @return
     */
    boolean hasProcessingTrade(String customerCode, String customerType);

    /**
     * 根据客户类型获取账户类型key
     * @param customerType
     * @return
     */
    String convertAccountTypeKey(String customerType);

    /**
     * 监控
     */
    void monitor(TradeInfo tradeInfo, Result<Void> result);

    /**
     * 处理交易
     * @param tradeInfo
     * @return
     */
    Result<Void> dealTrade(TradeInfo tradeInfo);
}
