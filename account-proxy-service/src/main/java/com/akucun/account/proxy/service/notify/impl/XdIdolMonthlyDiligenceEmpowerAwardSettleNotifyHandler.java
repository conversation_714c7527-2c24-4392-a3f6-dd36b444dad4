package com.akucun.account.proxy.service.notify.impl;

import com.akucun.account.proxy.facade.stub.others.account.req.NotifyReq;
import com.akucun.account.proxy.service.common.SpringContextHolder;
import com.akucun.account.proxy.service.notify.Handler;
import com.akucun.account.proxy.service.postaction.task.PromoPayTask;
import com.akucun.common.Result;
import org.springframework.stereotype.Service;

/**
 * 月勤奖/赋能营奖励发放自动提现结果通知
 * @description XdIdolMonthlyDiligenceEmpowerAwardSettleNotifyHandler
 * <AUTHOR>
 * @date 2025/2/14 11:21
 * @version v1.0.0
 */
@Service("ACCOUNT_MD_EMP_AWARD_SETTLE_HANDLER")
public class XdIdolMonthlyDiligenceEmpowerAwardSettleNotifyHandler implements Handler<Result<Void>, NotifyReq> {

	@Override
	public Result<Void> deal(NotifyReq notifyReq) {
		com.aikucun.common2.base.Result<Void> result = SpringContextHolder.getBean(PromoPayTask.class)
				.payCallback(notifyReq.getBizNo(), notifyReq.isSuccess(), notifyReq.getErrorMessage());
		if (!result.getSuccess()) {
			return Result.error(result.getCode(), result.getMessage());
		}
		return Result.success();
	}

}
