package com.akucun.account.proxy.service.common.bo;

import com.mengxiang.base.common.utils.datamasking.Sensitive;
import com.mengxiang.base.common.utils.datamasking.SensitiveType;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: silei
 * @Date: 2020/8/26
 * @desc: 每一个具体流程请求体
 */
@Data
public class AccountReq {

    private Long accountTradeId;
    /**
     * 交易类型
     */
    private String tradeType;
    /**
     * 子交易类型
     */
    private String subTradeType;
    /**
     * 明细交易单号
     */
    private String detailOrderNo;
    /**
     * 客户编码
     */
    private String customerCode;
    /**
     * 客户名称
     */
    private String customerName;
    /**
     * 昵称
     */
    private String nickName;
    /**
     * 手机号
     */
    @Sensitive(type = SensitiveType.Phone)
    private String mobile;
    /**
     * 客户类型
     */
    private String customerType;
    /**
     * 银行卡号
     */
    @Sensitive(type = SensitiveType.BankCard)
    private String bankCardCode;
    /**
     * 账户属性
     */
    private String accountProperty;
    /**
     * 账户可用状态
     */
    private Integer accountStatus;
    /**
     * 其他信息
     */
    private Map<String, String> reqMap = new HashMap<>();

    @Override
    public String toString() {
        return "AccountReq{" +
                "accountTradeId=" + accountTradeId +
                ", tradeType='" + tradeType + '\'' +
                ", subTradeType='" + subTradeType + '\'' +
                ", detailOrderNo='" + detailOrderNo + '\'' +
                ", customerCode='" + customerCode + '\'' +
                ", customerName='" + customerName + '\'' +
                ", nickName='" + nickName + '\'' +
                ", mobile='" + mobile + '\'' +
                ", customerType='" + customerType + '\'' +
                ", bankCardCode='" + bankCardCode + '\'' +
                ", accountProperty='" + accountProperty + '\'' +
                ", accountStatus=" + accountStatus +
                ", reqMap=" + reqMap +
                '}';
    }
}
