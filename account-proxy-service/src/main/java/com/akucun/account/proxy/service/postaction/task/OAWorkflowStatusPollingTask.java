package com.akucun.account.proxy.service.postaction.task;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.common.enums.PostActionTypes;
import com.akucun.account.proxy.dao.model.PostActionItem;
import com.akucun.account.proxy.service.oa.handler.AbstractOAWorkflowPollingHandler;
import com.akucun.account.proxy.service.postaction.common.AbsPostActionExecutor;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.mengxiang.base.common.log.Logger;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.Data;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Component
public class OAWorkflowStatusPollingTask extends AbsPostActionExecutor {

	@ApolloJsonValue("${oa.workflow.status.polling.retry.config:{}}")
	private TaskRetryConfig taskRetryConfig;

	@Override
	protected String getActionType() {
		return PostActionTypes.OA_WORKFLOW_STATUS_POLLING.getName();
	}

	/**
	 * 最大重试次数，超过则不再重试
	 * @return
	 */
	@Override
	public int getMaxRetryTimes() {
		return taskRetryConfig.getMaxRetryTimes();
	}

	/**
	 * 计算下一次执行时间，按小时梯次执行
	 * @param item
	 * @return
	 */
	@Override
	public LocalDateTime calculateNextExecuteTime(PostActionItem item) {
		return LocalDateTime.now().plusMinutes(taskRetryConfig.getIntervalMinute());
	}

	/**
	 * 默认捞30天内创建的，超过则不再执行
	 * @return
	 */
	@Override
	public int getDayOff() {
		return taskRetryConfig.getDayOff();
	}

	@XxlJob("OAWorkflowStatusPollingTask")
	public ReturnT<String> execute(String param) {
		return this.executeEntrance();
	}

	@Override
	public Result<Void> execute(PostActionItem item) {
		Result<Void> resp = Result.success();
		try {
			resp = AbstractOAWorkflowPollingHandler.getHandler(item).handle();

			//设置失败原因
			item.setErrorLog(resp.getMessage());
		}catch (Exception e) {
			resp.setSuccess(false);
			resp.setMessage(e.getMessage());
			Logger.error("OA工作流状态轮询任务执行异常bizId：{}", item.getBizId(), e);
		}
		 return resp;
	}

	@Data
	public static class TaskRetryConfig {

		// 最大重试次数，超过则不再重试
		private Integer maxRetryTimes = 720;

		// 默认捞30天内创建的，超过则不再执行
		private Integer dayOff = -30;

		// 重试间隔时间，单位：分钟
		private Integer intervalMinute = 60;

	}
}
