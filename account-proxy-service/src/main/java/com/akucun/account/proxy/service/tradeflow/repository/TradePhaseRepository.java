package com.akucun.account.proxy.service.tradeflow.repository;

import com.akucun.account.proxy.dao.mapper.TradeMapper;
import com.akucun.account.proxy.dao.mapper.TradePhaseMapper;
import com.akucun.account.proxy.dao.model.TradePhaseDO;
import com.akucun.account.proxy.facade.stub.others.trade.res.TradePhaseStatusEnum;
import com.akucun.account.proxy.service.tradeflow.agreement.TradeAgreement;
import com.akucun.account.proxy.service.tradeflow.agreement.TradeAgreementPhase;
import com.akucun.account.proxy.service.tradeflow.agreement.TradeAgreementService;
import com.akucun.account.proxy.service.tradeflow.domain.TradePhase;
import com.akucun.account.proxy.service.tradeflow.dto.BizInfos;
import com.alibaba.fastjson.JSON;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class TradePhaseRepository {

    @Autowired
    private TradePhaseMapper tradePhaseMapper;

    @Autowired
    private TradeMapper tradeMapper;

    @Autowired
    private TradeAgreementService tradeAgreementService;

    public int persist(TradePhase phase) {
        TradePhaseDO phaseDO = convert(phase);
        int effect = tradePhaseMapper.insert(phaseDO);
        phase.setId(phaseDO.getId());
        return effect;
    }

    public int updateResult(TradePhase phase) {
        return tradePhaseMapper.updateResult(convert(phase));
    }

    public List<TradePhase> load(Long tradeId) {
        List<TradePhaseDO> phaseDOs = tradePhaseMapper.selectByTradeId(tradeId);
        return convert(tradeId, phaseDOs);
    }

    private TradePhaseDO convert(TradePhase phase) {
        TradePhaseDO phaseDO = new TradePhaseDO();
        phaseDO.setId(phase.getId());
        phaseDO.setTradeId(phase.getTradeId());
        phaseDO.setCode(phase.getAgreement().getCode());
        phaseDO.setBizInfo(JSON.toJSONString(phase.getBizInfo()));
        phaseDO.setStatus(phase.getStatus().name());
        phaseDO.setErrorCode(phase.getErrorCode());
        phaseDO.setErrorMessage(phase.getErrorMessage());
        return phaseDO;
    }

    private List<TradePhase> convert(Long tradeId, List<TradePhaseDO> phaseDOs) {
        TradeAgreement agreement = tradeAgreementService.getAgreement(tradeMapper.selectById(tradeId).getAgreementId());
        List<TradePhase> phases = new ArrayList<>();
        for(TradePhaseDO phaseDO : phaseDOs) {
            phases.add(convert(phaseDO, agreement));
        }
        return phases;
    }

    private TradePhase convert(TradePhaseDO phaseDO, TradeAgreement agreement) {
        TradeAgreementPhase agreementPhase = tradeAgreementService.getPhaseByCode(agreement, phaseDO.getCode());

        TradePhase phase = new TradePhase();
        phase.setId(phaseDO.getId());
        phase.setTradeId(phaseDO.getTradeId());
        phase.setBizInfo(BizInfos.getBizInfo(phaseDO.getBizInfo(), agreementPhase.getBizInfoCls()));
        phase.setStatus(TradePhaseStatusEnum.valueOf(phaseDO.getStatus()));
        phase.setErrorCode(phaseDO.getErrorCode());
        phase.setErrorMessage(phaseDO.getErrorMessage());
        phase.setAgreement(agreementPhase);
        return phase;
    }

}
