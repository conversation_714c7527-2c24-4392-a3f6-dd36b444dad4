package com.akucun.account.proxy.common.log;

import com.aikucun.common2.log.Logger;
import com.akucun.account.proxy.common.utils.SpringBeanUtil;
import com.akucun.account.proxy.dao.mapper.CallLogMapper;
import com.akucun.account.proxy.dao.model.CallLog;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Create on : 2024/12/13 14:50
 **/
public class CallLogUtils {

    public static void saveLog(String bizId, String type, String requestMessage, String responseMessage) {
        try {
            CallLog callLog = new CallLog();
            callLog.setBizId(bizId);
            callLog.setType(type);
            callLog.setRequestMessage(requestMessage);
            callLog.setResponseMessage(responseMessage);
            SpringBeanUtil.getBean("callLogMapper", CallLogMapper.class).insertSelective(callLog);
        } catch (Exception e) {
            Logger.warn("CallLogUtils.saveLog保存调用日志异常，bizId：{}，type：{}", bizId, type, e);
        }
    }

    public enum CallLogType{
        FULL_RETURN_FREEZE("满返冻结"),
        FULL_RETURN_ADD_FREEZE("满返加冻"),
        FULL_RETURN_UNFREEZE("满返解冻"),
        FULL_RETURN_SETTLE("满返结算");

        private String desc;

        public String getDesc() {
            return desc;
        }

        CallLogType(String desc) {
            this.desc = desc;
        }
    }

    public static void main(String[] args) {
        saveLog(null, null, "asdasd", "asdasdas");

        Logger.warn("CallLogUtils.saveLog保存调用日志异常，bizId：{}，type：{}", null, "");
    }

}
