package com.akucun.account.proxy.service.postaction.common;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.dao.model.PostActionItem;
import com.akucun.account.proxy.service.postaction.PostActionService;
import com.akucun.fps.schedule.model.ScheduleItem;
import com.mengxiang.base.common.log.Logger;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.util.ShardingUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * @Author: silei
 * @Date: 2020/12/8
 * @desc:
 */
@Component
public abstract class AbsPostActionExecutor {

    @Autowired
    PostActionProxy postActionProxy;

    @Autowired
    private PostActionService postActionService;

    @PostConstruct
    public void init() {
        postActionProxy.register(getActionType(), this);
    }

    protected static final int FETCH_NUM = 500;

    @Value("${post.day.off:-3}")
    private int dayOff;

    @Value("${post.max.retry.times:6}")
    private int maxRetryTimes;

    @Value("${post.retry.interval:5}")
    private int interval;

    public int getMaxRetryTimes() {
        return maxRetryTimes;
    }

    public int getDayOff() {
        return dayOff;
    }

    /**
     * 任务执行类型
     *
     * @return
     */
    protected abstract String getActionType();

    /**
     * 子类实现具体业务
     *
     * @param item
     * @return
     */
    public abstract Result<Void> execute(PostActionItem item);


    public ReturnT<String> executeEntrance() {
        boolean taskAllSuccess = true;
        try {
            LocalDate date = LocalDate.now().plusDays(getDayOff());
            String createTime = date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd 00:00:00"));
            ShardingUtil.ShardingVO shardVO = ShardingUtil.getShardingVo();
            List<PostActionItem> list = postActionService.selectPage(createTime, FETCH_NUM, shardVO.getTotal(), shardVO.getIndex(), this.getActionType(), getMaxRetryTimes());
            Logger.info("AbsPostActionExecutor ActionType:{} execute task with schedule items size [{}]", getActionType(), list.size());
            if (CollectionUtils.isEmpty(list)) {
                return ReturnT.SUCCESS;
            }
            for (PostActionItem postActionItem : list) {
                postActionService.processAction(postActionItem);
            }
        } catch (Exception e) {
            Logger.error("AbsPostActionExecutor ActionType:{} executeEntrance exception: ", getActionType(), e);
            taskAllSuccess = false;
        }
        return taskAllSuccess ? ReturnT.SUCCESS : ReturnT.FAIL;

    }

    /**
     * 计算下一次执行时间
     * @param item
     * @return
     */
    public LocalDateTime calculateNextExecuteTime(PostActionItem item) {
        return LocalDateTime.now().plusMinutes((long) item.getRetryNums() * interval);
	}

    public ReturnT<String> executeEntranceLtNextRetryTime() {
        boolean taskAllSuccess = true;
        try {
            //限制任务创建时间
            LocalDate date = LocalDate.now().plusDays(getDayOff());
            String createTime = date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd 00:00:00"));
            ShardingUtil.ShardingVO shardVO = ShardingUtil.getShardingVo();
            //获取执行器总数量，默认1
            int total = (shardVO == null ? 1 : shardVO.getTotal());
            //获取当前分片，默认0
            int index = (shardVO == null ? 0 : shardVO.getIndex());
            //指定执行时间
			String nextRetryTimeStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            List<PostActionItem> list = postActionService.selectPageByNextRetryTime(createTime, FETCH_NUM, total, index, this.getActionType(), getMaxRetryTimes(), nextRetryTimeStr);
            Logger.info("AbsPostActionExecutor.executeEntranceLtNextRetryTime ActionType:{}, CreateTime:{}, NextRetryTime:{} execute task with schedule items size [{}]", getActionType(), createTime, nextRetryTimeStr, list.size());
            if (CollectionUtils.isEmpty(list)) {
                return ReturnT.SUCCESS;
            }
            for (PostActionItem postActionItem : list) {
                postActionService.processAction(postActionItem);
            }
        } catch (Exception e) {
            Logger.error("AbsPostActionExecutor.executeEntranceLtNextRetryTime ActionType:{} executeEntrance exception: ", getActionType(), e);
            taskAllSuccess = false;
        }
        return taskAllSuccess ? ReturnT.SUCCESS : ReturnT.FAIL;

    }

}
