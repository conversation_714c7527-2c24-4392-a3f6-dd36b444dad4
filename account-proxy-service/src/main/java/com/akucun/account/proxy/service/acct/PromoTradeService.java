package com.akucun.account.proxy.service.acct;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.dao.model.RewardApply;
import com.akucun.account.proxy.facade.stub.others.dto.req.BonusPayQueryReq;
import com.akucun.account.proxy.facade.stub.others.dto.res.BonusPayInfoResp;
import com.akucun.account.proxy.facade.stub.others.dto.res.OANotifyDTO;
import com.akucun.account.proxy.facade.stub.others.oa.req.OAWorkflowStatusNotifyRequest;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : 营销-奖励下发-业务处理
 * @Create on : 2025/1/15 16:47
 **/
public interface PromoTradeService {

    BonusPayInfoResp queryTradeInfo(BonusPayQueryReq bonusPayQueryReq);

    Boolean notifyFromOA(OAWorkflowStatusNotifyRequest notifyMsg);

    OANotifyDTO queryStatus(BonusPayQueryReq queryReq);

    OANotifyDTO buildOANotifyDTO(RewardApply rewardApplyRecord);

    /**
     * 月勤赋能奖励结算
     * @param customerCode
     * @param customerType
     * @param bizNo
     * @param amount
     * @param remark
     * @param businessType
     * @return
     */
    Result<Void> monthlyDiligenceEmpowerAwardSettle(String customerCode, String customerType,
                                                String bizNo, BigDecimal amount, String remark,
                                                String businessType, String accountType,Integer userGrade);
}
