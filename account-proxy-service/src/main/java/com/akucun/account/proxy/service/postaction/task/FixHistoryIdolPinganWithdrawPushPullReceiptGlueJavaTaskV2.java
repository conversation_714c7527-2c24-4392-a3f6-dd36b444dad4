/*
 * @Author: Lee
 * @Date: 2025-03-28 17:00:20
 * @Description: Do not edit
 * @License: Copyright (c) 2025, Lee
 */
package com.akucun.account.proxy.service.postaction.task;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import com.aikucun.common2.log.Logger;
import com.akucun.account.proxy.common.constant.WithdrawChannelConstants;
import com.akucun.account.proxy.common.enums.PostActionExecStatus;
import com.akucun.account.proxy.common.enums.PostActionTypes;
import com.akucun.account.proxy.dao.mapper.PaymentTransferMapper;
import com.akucun.account.proxy.dao.mapper.PostActionItemMapper;
import com.akucun.account.proxy.dao.mapper.TenantWithdrawApplyMapper;
import com.akucun.account.proxy.dao.mapper.WithdrawApplyRecordMapper;
import com.akucun.account.proxy.dao.model.PostActionItem;
import com.akucun.account.proxy.dao.model.TenantWithdrawApply;
import com.akucun.account.proxy.dao.model.WithdrawApplyRecord;
import com.akucun.account.proxy.service.common.SpringContextHolder;
import com.akucun.account.proxy.service.postaction.PostActionService;
import com.akucun.account.proxy.service.postaction.bo.PostActionItemBO;
import com.akucun.account.proxy.service.receipt.bo.WithdrawReceiptDownloadBO;
import com.akucun.account.proxy.service.transfer.PaymentTransferService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.log.XxlJobLogger;

import cn.hutool.core.util.RandomUtil;

/**
 * @Description
 * @aurhor lee
 * @date: 2022/9/13 14:34
 */
public class FixHistoryIdolPinganWithdrawPushPullReceiptGlueJavaTaskV2 extends IJobHandler {

    @Resource
    private WithdrawApplyRecordMapper withdrawApplyRecordMapper;

    @Resource
    private PaymentTransferMapper paymentTransferMapper;

    @Resource
    private PaymentTransferService paymentTransferService;

    @Resource
    private PostActionItemMapper postActionItemMapper;

    @Resource
    private TenantWithdrawApplyMapper tenantWithdrawApplyMapper;

    private String EXPORT_RECEIPT_TMP_PATH = System.getProperty("java.io.tmpdir") + "/account-proxy-tmp";

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        Logger.info("FixHistoryIdolPinganWithdrawPushPullReceiptGlueJavaTaskV2执行参数：{}", param);
        if (StringUtils.isBlank(param)) {
            Logger.info("FixHistoryIdolPinganWithdrawPushPullReceiptGlueJavaTaskV2执行异常：参数不能为空");
            return ReturnT.SUCCESS;
        }
        String[] params = param.split(",");
        String startDate = params[0];
        String endDate = params[1];

        // // 读取csv文件, 获取需要过滤的withdrawNo
        // String csvUrl = params[2];
        // List<String> needFilterWithdrawNos = readCsvAndOperate(csvUrl);
        List<String> needFilterWithdrawNos = null;

        /**
         * SELECT *  FROM withdraw_apply_record WHERE create_time >= '2025-01-01 00:00:00' and create_time < '2025-03-29 00:00:00' 
            and withdraw_channel = 'PINGAN' and apply_status = 'SUCC' and receipt_url IS NULL ;
         */
        QueryWrapper<WithdrawApplyRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("withdraw_channel", "PINGAN");
        queryWrapper.eq("apply_status", "SUCC");
        queryWrapper.ge("create_time", startDate);
        queryWrapper.lt("create_time", endDate);
        queryWrapper.isNull("receipt_url");
        //只返回withdrawNo, customerType
        queryWrapper.select("withdraw_no", "customer_type");
        List<WithdrawApplyRecord> list = withdrawApplyRecordMapper.selectList(queryWrapper); 

        QueryWrapper<TenantWithdrawApply> tenantQueryWrapper = new QueryWrapper<>();
        tenantQueryWrapper.eq("withdraw_channel", "PINGAN");
        tenantQueryWrapper.eq("apply_status", "SUCC");
        tenantQueryWrapper.ge("create_time", startDate);
        tenantQueryWrapper.lt("create_time", endDate);
        tenantQueryWrapper.isNull("receipt_url");
        //只返回withdrawNo, customerType
        tenantQueryWrapper.select("withdraw_no", "customer_type");
        List<TenantWithdrawApply> tenantList = tenantWithdrawApplyMapper.selectList(tenantQueryWrapper); 

        XxlJobLogger.log("FixHistoryIdolPinganWithdrawPushPullReceiptGlueJavaTaskV2执行: {}, 共{}条数据", param, list.size());
        XxlJobLogger.log("FixHistoryIdolPinganWithdrawPushPullReceiptGlueJavaTaskV2执行样例：{}", JSONObject.toJSONString(list.get(0)));

        for (WithdrawApplyRecord withdrawApplyRecord : list) {
            if (StringUtils.isBlank(withdrawApplyRecord.getWithdrawNo()) || StringUtils.isBlank(withdrawApplyRecord.getCustomerType())) {
                Logger.info("FixHistoryIdolPinganWithdrawPushPullReceiptGlueJavaTaskV2执行参数为空：{}", JSON.toJSONString(withdrawApplyRecord));
                continue;
            }

            if (CollectionUtils.isNotEmpty(needFilterWithdrawNos) && needFilterWithdrawNos.contains(withdrawApplyRecord.getWithdrawNo())) {
                continue;
            }

            Logger.info("FixHistoryIdolPinganWithdrawPushPullReceiptGlueJavaTaskV2执行参数：{}", JSON.toJSONString(withdrawApplyRecord));
            QueryWrapper<PostActionItem> PostActionItemwrapper = new QueryWrapper<>();
            PostActionItemwrapper.eq("biz_id", withdrawApplyRecord.getWithdrawNo())
                   .eq("action_type", PostActionTypes.WITHDRAW_RECEIPT_DOWNLOAD.getName());
            List<PostActionItem> selectList = postActionItemMapper.selectList(PostActionItemwrapper);
            if (CollectionUtils.isEmpty(selectList)) {
                WithdrawReceiptDownloadBO bo = WithdrawReceiptDownloadBO.builder()
                    .withdrawNo(withdrawApplyRecord.getWithdrawNo())
                    .withdrawChannel(WithdrawChannelConstants.PINGAN.getName())
                    .customerType(withdrawApplyRecord.getCustomerType())
                    .build();
                PostActionItemBO itemBO = PostActionItemBO.builder()
                        .bizId(withdrawApplyRecord.getWithdrawNo())
                        .paramObject(bo)
                        .remark("平安提现回执单下载")
                        .actionType(PostActionTypes.WITHDRAW_RECEIPT_DOWNLOAD.getName())
                        .status(PostActionExecStatus.EXECUTE.value())
                        .nextRetryTime(LocalDateTime.now().plusMinutes(RandomUtil.randomLong(1, 10)))
                        .build();
                SpringContextHolder.getBean(PostActionService.class).addAction(itemBO);
            }
        }

        for (TenantWithdrawApply tenantWithdrawApply : tenantList) {
            if (StringUtils.isBlank(tenantWithdrawApply.getWithdrawNo()) || StringUtils.isBlank(tenantWithdrawApply.getCustomerType())) {
                Logger.info("FixHistoryIdolPinganWithdrawPushPullReceiptGlueJavaTaskV2执行参数为空：{}", JSON.toJSONString(tenantWithdrawApply));
                continue;
            }

            if (CollectionUtils.isNotEmpty(needFilterWithdrawNos) && needFilterWithdrawNos.contains(tenantWithdrawApply.getWithdrawNo())) {
                continue;
            }

            Logger.info("FixHistoryIdolPinganWithdrawPushPullReceiptGlueJavaTaskV2执行参数：{}", JSON.toJSONString(tenantWithdrawApply));
            QueryWrapper<PostActionItem> PostActionItemwrapper = new QueryWrapper<>();
            PostActionItemwrapper.eq("biz_id", tenantWithdrawApply.getWithdrawNo())
                   .eq("action_type", PostActionTypes.WITHDRAW_RECEIPT_DOWNLOAD.getName());
            List<PostActionItem> selectList = postActionItemMapper.selectList(PostActionItemwrapper);
            if (CollectionUtils.isEmpty(selectList)) {
                WithdrawReceiptDownloadBO bo = WithdrawReceiptDownloadBO.builder()
                    .withdrawNo(tenantWithdrawApply.getWithdrawNo())
                    .withdrawChannel(WithdrawChannelConstants.PINGAN.getName())
                    .isTenantCustomer(true)
                    .customerType(tenantWithdrawApply.getCustomerType())
                    .build();
                PostActionItemBO itemBO = PostActionItemBO.builder()
                        .bizId(tenantWithdrawApply.getWithdrawNo())
                        .paramObject(bo)
                        .remark("平安提现回执单下载")
                        .actionType(PostActionTypes.WITHDRAW_RECEIPT_DOWNLOAD.getName())
                        .status(PostActionExecStatus.EXECUTE.value())
                        .nextRetryTime(LocalDateTime.now().plusMinutes(RandomUtil.randomLong(1, 10)))
                        .build();
                SpringContextHolder.getBean(PostActionService.class).addAction(itemBO);
            }
        }
        return ReturnT.SUCCESS;
    }

    //将网络csv文件先放到本地, 然后读取本地文件进行逐行解析, 避免网络链接长期关闭, 解析完成后删除本地文件
	private List<String> readCsvAndOperate(String csvUrl) throws Exception {
		File tempFile = null;
		try {
			// 1. 下载网络csv文件到本地临时文件
			tempFile = getTempFile(csvUrl, "pingan_withdraw_receipt_pull_202501_" + System.currentTimeMillis() + ".csv", EXPORT_RECEIPT_TMP_PATH);
			if (tempFile == null) {
				Logger.warn("FixHistoryIdolPinganWithdrawPushPullReceiptGlueJavaTaskV2-下载网络csv文件失败, csvUrl: {}", csvUrl);
				return null;
			}

			XxlJobLogger.log("CSV文件已下载到本地临时文件: " + tempFile.getAbsolutePath());
			// 2. 从本地文件读取并逐行处理
			List<String> results = new ArrayList<>();
			BufferedReader reader = null;
			FileReader fileReader = null;
			try {
				fileReader = new FileReader(tempFile);
				reader = new BufferedReader(fileReader);
				String line;
				while ((line = reader.readLine()) != null) {
					// 处理每一行数据
					if (StringUtils.isNotBlank(line)) {
						// TODO: 3. 调用提现回单导出接口
						String[] split = line.split(",", -1);
						String withdrawNo = split[0];
						results.add(withdrawNo);
					}
				}
			} finally {
				if (reader != null) {
					reader.close();
				}
				if (fileReader != null) {
					fileReader.close();
				}
			}
			XxlJobLogger.log("CSV文件已读取完成: " + tempFile.getAbsolutePath() + ", 共读取到" + results.size() + "条数据");
			return results;
		} catch (Exception e) {
			Logger.error("FixHistoryIdolPinganWithdrawPushPullReceiptGlueJavaTaskV2-处理CSV文件失败", e);
			throw e;
		} finally {
			// 4. 删除临时文件
			if (tempFile != null && tempFile.exists()) {
				boolean deleted = tempFile.delete();
				if (deleted) {
					Logger.info("FixHistoryIdolPinganWithdrawPushPullReceiptGlueJavaTaskV2-临时文件已删除: " + tempFile.getAbsolutePath());
				} else {
					Logger.warn("FixHistoryIdolPinganWithdrawPushPullReceiptGlueJavaTaskV2-临时文件删除失败: " + tempFile.getAbsolutePath());
				}
			}
		}
	}

    // 获取本地临时文件
	private File getTempFile(String httpUrl, String tempFileName, String parentDirectory) throws Exception {
		URLConnection connection = null;
		InputStream inputStream = null;
		// 输出流
		FileOutputStream outputStream = null;
		try {
			// 1. 下载网络csv文件到本地临时文件
			URL url = new URL(httpUrl);
			connection = url.openConnection();
			inputStream = connection.getInputStream();
			
			// 创建临时文件
			File tempFile = new File(parentDirectory, tempFileName);
			if (!tempFile.getParentFile().exists()) {
				tempFile.getParentFile().mkdirs();
			}

			// 将网络文件保存到本地临时文件
			outputStream = new FileOutputStream(tempFile);
			byte[] buffer = new byte[1024];
			int bytesRead;
			while ((bytesRead = inputStream.read(buffer)) != -1) {
				outputStream.write(buffer, 0, bytesRead);
			}
			return tempFile;
		} catch (Exception e) {
			Logger.error("FixHistoryIdolPinganWithdrawPushPullReceiptGlueJavaTaskV2-获取本地临时文件失败", e);
			throw e;
		} finally {
			if (outputStream != null) {
				try {
					outputStream.close();
				} catch (IOException e) {
					Logger.error("FixHistoryIdolPinganWithdrawPushPullReceiptGlueJavaTaskV2-关闭输出流失败", e);
				}
			}
			if (inputStream != null) {
				try {
					inputStream.close();
				} catch (IOException e) {
					Logger.error("FixHistoryIdolPinganWithdrawPushPullReceiptGlueJavaTaskV2-关闭输入流失败", e);
				}
			}
			if (connection instanceof HttpURLConnection) {
				((HttpURLConnection) connection).disconnect();
			}
		}
	}

}
