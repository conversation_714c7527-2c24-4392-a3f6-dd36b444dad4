package com.akucun.account.proxy.service.oa.bo;

import com.akucun.account.proxy.facade.stub.others.oa.resp.OAWorkflowResponseInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Create on : 2025/1/15 01:12
 **/
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@Builder
@Data
public class OAWorkflowStatusPollingBo {

    //业务编号
    private String bizNo;

    //流程请求ID
    private Integer requestId;

    //员工编号对应的OA人员的userId，可不传
    private Integer userId;

    //业务类型 枚举
    private String businessType;

    //上一次轮询的流程信息
    private OAWorkflowResponseInfo lastOAWorkflowResponseInfo;

    //回调地址
    private String notifyUrl;

}
