package com.akucun.account.proxy.service.postaction.task;

import com.aikucun.common2.base.Result;
import com.aikucun.common2.log.Logger;
import com.akucun.account.proxy.common.enums.PostActionTypes;
import com.akucun.account.proxy.dao.model.PostActionItem;
import com.akucun.account.proxy.service.postaction.common.AbsPostActionExecutor;
import com.akucun.fps.common.util.GsonUtils;
import com.akucun.fps.pingan.client.vo.DealTradeVO;
import com.akucun.fps.pingan.feign.api.member.MemberServiceApi;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @Author: silei
 * @Date: 2021/3/11
 * @desc: 平安会员间交易撤销退款
 */
@Component
public class DealTradeRefundTask extends AbsPostActionExecutor {

    @Resource
    private MemberServiceApi memberServiceApi;

    @XxlJob("dealTradeRefundTask")
    public ReturnT<String> execute(String param) {
        return this.executeEntrance();
    }

    @Override
    protected String getActionType() {
        return PostActionTypes.PINGAN_REFUND.getName();
    }

    @Override
    public Result<Void> execute(PostActionItem item) {
        Result<Void> result = Result.success();
        try {
            DealTradeVO dealTradeVO = GsonUtils.getInstance().fromJson(item.getParam(), DealTradeVO.class);
            //调用退款接口
            com.akucun.fps.common.entity.Result<Void> unFreeResult = memberServiceApi.dealTradeRefund(dealTradeVO);
            if (!unFreeResult.isSuccess()) {
                Logger.error("退款任务执行失败" + unFreeResult.getErrorMessage());
                result.setSuccess(Boolean.FALSE);
                result.setMessage(unFreeResult.getErrorMessage());
            }
        } catch (Exception e) {
            result.setSuccess(false);
            result.setMessage(e.getMessage());
            Logger.error("退款任务执行异常", e);
        }
        return result;
    }
}
