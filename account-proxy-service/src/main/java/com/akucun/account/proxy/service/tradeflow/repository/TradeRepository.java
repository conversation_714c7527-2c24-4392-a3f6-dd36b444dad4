package com.akucun.account.proxy.service.tradeflow.repository;

import com.akucun.account.proxy.dao.mapper.TradeMapper;
import com.akucun.account.proxy.dao.model.TradeDO;
import com.akucun.account.proxy.facade.stub.others.trade.res.TradeStatusEnum;
import com.akucun.account.proxy.service.tradeflow.agreement.TradeAgreement;
import com.akucun.account.proxy.service.tradeflow.agreement.TradeAgreementService;
import com.akucun.account.proxy.service.tradeflow.domain.Trade;
import com.akucun.account.proxy.service.tradeflow.dto.BizInfos;
import com.alibaba.fastjson.JSON;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class TradeRepository {

    @Autowired
    private TradeMapper tradeMapper;

    @Autowired
    private TradeAgreementService tradeAgreementService;

    public int persist(Trade trade) {
        TradeDO tradeDO = convert(trade);
        int effect = tradeMapper.insert(tradeDO);
        trade.setId(tradeDO.getId());
        return effect;
    }

    public int updateResult(Trade trade) {
        return tradeMapper.updateResult(convert(trade));
    }

    public Trade load(String tradeNo) {
        TradeDO tradeDO = tradeMapper.selectByTradeNo(tradeNo);
        if(tradeDO == null) {
            return null;
        }
        return convert(tradeDO);
    }

    private TradeDO convert(Trade trade) {
        TradeDO tradeDO = new TradeDO();
        tradeDO.setId(trade.getId());
        tradeDO.setTradeNo(trade.getTradeNo());
        tradeDO.setBizNo(trade.getBizNo());
        tradeDO.setProductCategory(trade.getProductCategory());
        tradeDO.setBizType(trade.getBizType());
        tradeDO.setRequestPlatform(trade.getRequestPlatform());
        tradeDO.setBizInfo(JSON.toJSONString(trade.getBizInfo()));
        tradeDO.setRemark(trade.getRemark());
        tradeDO.setStatus(trade.getStatus().name());
        tradeDO.setAgreementId(trade.getAgreement().getAgreementId());
        return tradeDO;
    }

    private Trade convert(TradeDO tradeDO) {
        TradeAgreement agreement = tradeAgreementService.getAgreement(tradeDO.getAgreementId());

        Trade trade = new Trade();
        trade.setId(tradeDO.getId());
        trade.setTradeNo(tradeDO.getTradeNo());
        trade.setBizNo(tradeDO.getBizNo());
        trade.setProductCategory(tradeDO.getProductCategory());
        trade.setBizType(tradeDO.getBizType());
        trade.setRequestPlatform(tradeDO.getRequestPlatform());
        trade.setBizInfo(BizInfos.getBizInfo(tradeDO.getBizInfo(), agreement.getBizInfoCls()));
        trade.setRemark(tradeDO.getRemark());
        trade.setStatus(TradeStatusEnum.valueOf(tradeDO.getStatus()));
        trade.setAgreement(agreement);
        return trade;
    }

}
