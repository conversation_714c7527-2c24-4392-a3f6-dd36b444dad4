package com.akucun.account.proxy.facade.stub.others.oa.resp;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Create on : 2025/1/14 16:10
 **/
@Data
public class OAWorkflowResponseInfo {

    private String requestId; // 请求ID

    private String requestName; // 请求标题

    private String requestLevel; // 请求重要级别

    private String messageType; // 短信提醒

    private OAWorkflowBaseInfo workflowBaseInfo; // 流程类型

    private String currentNodeName; // 当前节点名称

    private String currentNodeId; // 当前节点Id

    private String status; // 流程状态

    private String creatorId; // 创建者

    private String createTime; // 创建时间

    private String lastOperatorName; // 最后操作者名称

    private String lastOperateTime; // 最后操作时间

    private Boolean canView; // 是否可查看

    private Boolean canEdit; // 是否可编辑

    private Boolean mustInputRemark; // 签字意见是否必填

    /*private WorkflowMainTableInfo[] workflowMainTableInfo; // 主表信息

    private WorkflowDetailTableInfo[] workflowDetailTableInfos; // 明细表信息

    private WorkflowRequestLog[] workflowRequestLogs; // 流转日志信息

    private String[] workflowHtmlTemplate; // HTML显示模板(0,ipad/1,iphone)

    private String[] workflowHtmlShow; // 解析后的HTML显示内容(0,ipad/1,iphone)

    private String beagentid; // 被代理人*/

    private List<List<String>> workflowPhrases; // 流程短语

    private String workflowNo; // 流程编号

    /**
     * 通过API接口创建的流程，如果状态包含“归档”则流程已完成
     * @return
     */
    private boolean isFinished() {
        return !StringUtils.isBlank(status) && status.contains("归档");
    }

    /**
     * 通过API接口创建的流程，非完成、非作废状态，即流程处理中
     * @return
     */
    private boolean isProcessing() {
        return !isFinished() && !isCanceled();
    }

    /**
     * 通过API接口创建的流程，创建即提交，流程状态不可能为“申请人提交”，当状态为“申请人提交”时，则属于异常驳回流程，OA针对接口提交并且状态为"申请人提交"时，
     * 不允许从OA发起提交，因此可认定为流程作废，需要重新从上游系统发起提交
     * @return
     */
    private boolean isCanceled() {
        return !StringUtils.isBlank(status) && status.equals("申请人提交");
    }

}
