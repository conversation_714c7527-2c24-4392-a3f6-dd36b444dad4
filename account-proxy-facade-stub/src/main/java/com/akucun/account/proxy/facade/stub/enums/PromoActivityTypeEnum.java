package com.akucun.account.proxy.facade.stub.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum PromoActivityTypeEnum {

    PROMO_RANK("1", "排名"),

    PROMO_FULL_RETURN("2", "满返");

    private String key;

    private String desc;

    public static PromoActivityTypeEnum findEnumByValue(String value){
        PromoActivityTypeEnum[] values = PromoActivityTypeEnum.values();
        for (PromoActivityTypeEnum promoActivityTypeEnum : values) {
            if (promoActivityTypeEnum.name().equals(value)) {
                return promoActivityTypeEnum;
            }
        }
        return  null;
    }

}
