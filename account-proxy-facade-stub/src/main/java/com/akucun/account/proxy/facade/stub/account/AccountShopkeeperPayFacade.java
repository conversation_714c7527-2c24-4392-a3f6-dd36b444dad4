package com.akucun.account.proxy.facade.stub.account;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.facade.stub.fallback.AccountShopkeeperPayFacadeFallbackFactory;
import com.akucun.account.proxy.facade.stub.others.account.req.AccountShopkeeperPayReq;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * @Author: huayanpei
 * @Date: 2020/12/31
 * @desc: 店主支付账户服务接口
 */
@FeignClient(value = "account-proxy", fallbackFactory = AccountShopkeeperPayFacadeFallbackFactory.class)
@RequestMapping("/api/account/proxy")
public interface AccountShopkeeperPayFacade {

    /**
     * 新增店主二维码信息
     * @param accountShopkeeperPayReq
     * @return
     */
    @PostMapping(value = "/addAccountShopkeeperPayList", produces = "application/json;charset=utf-8")
    Result<Void> addAccountShopkeeperPayList(@RequestBody AccountShopkeeperPayReq accountShopkeeperPayReq);
}
