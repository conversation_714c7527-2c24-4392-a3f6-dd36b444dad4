package com.akucun.account.proxy.facade.stub.trade;

import com.akucun.account.proxy.facade.stub.fallback.TradeFacadeFallbackFactory;
import com.akucun.account.proxy.facade.stub.others.trade.req.TradeReq;
import com.akucun.account.proxy.facade.stub.others.trade.res.TradeRes;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * 交易流程
 */
@FeignClient(value = "account-proxy", fallbackFactory = TradeFacadeFallbackFactory.class)
@RequestMapping("/api/trade")
public interface TradeFacade {

    /**
     * 交易
     * @param tradeReq
     * @return
     */
    @PostMapping(value = "")
    TradeRes trade(@RequestBody TradeReq tradeReq);

}
