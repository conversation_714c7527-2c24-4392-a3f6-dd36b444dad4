package com.akucun.account.proxy.facade.stub.account;

import com.akucun.account.proxy.facade.stub.fallback.PromoTradeFacadeFallbackFactory;
import com.akucun.account.proxy.facade.stub.others.dto.req.*;
import com.akucun.account.proxy.facade.stub.others.dto.res.BonusPayInfoResp;
import com.akucun.account.proxy.facade.stub.others.dto.res.BonusPayResp;
import com.akucun.account.proxy.facade.stub.others.dto.res.OANotifyDTO;
import com.akucun.account.proxy.facade.stub.others.dto.res.TaxQueryResp;
import com.akucun.account.proxy.facade.stub.others.oa.req.OAWorkflowStatusNotifyRequest;
import com.akucun.common.Result;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : 营销活动奖励发放接口
 * @Create on : 2025/1/7 11:31
 **/
@FeignClient(value = "account-proxy", fallbackFactory = PromoTradeFacadeFallbackFactory.class)
@RequestMapping("/api/account/proxy/promo")
public interface PromoTradeFacade {

    //=================== 业务受理 ==========================
    @PostMapping(value = "/dealTrade", produces = "application/json;charset=utf-8")
    Result<BonusPayResp> dealTrade(@RequestBody BonusPayReq bonusPayReq);

    @PostMapping(value = "/batchDealTrade", produces = "application/json;charset=utf-8")
    Result<BonusPayResp> batchDealTrade(@RequestBody BatchBonusPayReq batchBonusPayReq);

    //=================== 业务提交 =============================

    @PostMapping(value = "/queryTradeInfo", produces = "application/json;charset=utf-8")
    Result<BonusPayInfoResp> queryTradeInfo(@RequestBody BonusPayQueryReq bonusPayQueryReq);

    @PostMapping(value = "/submit", produces = "application/json;charset=utf-8")
    Result<BonusPayResp> submit(@RequestBody BonusPaySubmitReq bonusPaySubmitReq);

    //================ 其他接口 ================================

    /**
     * 费率计算
     *
     * @param taxQueryReq
     * @return
     */
    @PostMapping(value = "/calc", produces = "application/json;charset=utf-8")
    Result<TaxQueryResp> calc(@RequestBody TaxReq taxQueryReq);

    @PostMapping(value = "/batchCalc", produces = "application/json;charset=utf-8")
    Result<List<TaxQueryResp>> batchCalc(@RequestBody BatchTaxReq batchTaxReq);

    //================ OA相关接口 ===========================

    /**
     * 接收OA工作流状态异步通知
     *
     * @param notifyMsg
     * @return
     */
    @PostMapping(value = "/oa/workflow/notify", produces = "application/json;charset=utf-8")
    Result<Boolean> oaWorkflowNotify(@RequestBody OAWorkflowStatusNotifyRequest notifyMsg);

    /**
     * 主动查询OA最新状态
     *
     * @param queryReq
     * @return
     */
    @PostMapping(value = "/oa/status", produces = "application/json;charset=utf-8")
    Result<OANotifyDTO> queryStatus(@RequestBody BonusPayQueryReq queryReq);

    //================ 辅助接口 ===========================
    @PostMapping(value = "/saveOrUpdateMentorInfo", produces = "application/json;charset=utf-8")
    Result<Integer> saveOrUpdateMentorInfo(@RequestBody MentorInfoDTO mentorInfo);


}
