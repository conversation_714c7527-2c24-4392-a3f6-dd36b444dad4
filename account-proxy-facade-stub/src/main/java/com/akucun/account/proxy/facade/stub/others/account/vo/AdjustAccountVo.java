package com.akucun.account.proxy.facade.stub.others.account.vo;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

/**
 * 账户调账实体
 */
public class AdjustAccountVo {

    /**
     * id主键
     */
    private Integer id;
    /**
     * 请求号
     */
    private String requestNo;
    /**
     *调账类型：1：分账，2：转账，3：罚扣，4：账户中心调账
     */
    private String adjustmentType;
    /**
     *用户编码
     */
    private String customerCode;
    /**
     *用户类型
     */
    private String customerType;
    /**
     *状态：0：处理中，1：成功，2：失败，3：异常
     */
    private String status;
    /**
     *金额
     */
    private BigDecimal amount;
    /**
     *备注
     */
    private String remark;
    /**
     *账户类型-key
     */
    private String accountTypeKey;
    /**
     *操作人
     */
    private String operator;
    /**
     *创建时间
     */
    private Date createTime;
    /**
     *更新时间
     */
    private Date updateTime;

    public AdjustAccountVo() {
    }

    public AdjustAccountVo(Integer id, String requestNo, String adjustmentType, String customerCode, String customerType, String status, BigDecimal amount, String remark, String accountTypeKey, String operator, Date createTime, Date updateTime) {
        this.id = id;
        this.requestNo = requestNo;
        this.adjustmentType = adjustmentType;
        this.customerCode = customerCode;
        this.customerType = customerType;
        this.status = status;
        this.amount = amount;
        this.remark = remark;
        this.accountTypeKey = accountTypeKey;
        this.operator = operator;
        this.createTime = createTime;
        this.updateTime = updateTime;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getRequestNo() {
        return requestNo;
    }

    public void setRequestNo(String requestNo) {
        this.requestNo = requestNo;
    }

    public String getAdjustmentType() {
        return adjustmentType;
    }

    public void setAdjustmentType(String adjustmentType) {
        this.adjustmentType = adjustmentType;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getCustomerType() {
        return customerType;
    }

    public void setCustomerType(String customerType) {
        this.customerType = customerType;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getAccountTypeKey() {
        return accountTypeKey;
    }

    public void setAccountTypeKey(String accountTypeKey) {
        this.accountTypeKey = accountTypeKey;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
