package com.akucun.account.proxy.facade.stub.aggregation;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.facade.stub.fallback.AccountAggregationBankCardFacadeFallbackFactory;
import com.akucun.account.proxy.facade.stub.others.aggregation.req.BindCardInfoDTO;
import com.akucun.account.proxy.facade.stub.others.aggregation.req.BindCardVerifyDTO;
import com.akucun.account.proxy.facade.stub.others.aggregation.res.BindAuthResp;
import com.akucun.fps.common.entity.ResultList;
import com.akucun.fps.pingan.client.model.*;
import com.akucun.fps.pingan.client.model.po.BankNode;
import com.akucun.fps.pingan.client.model.query.BindCardQueryReq;
import com.akucun.fps.pingan.client.vo.PinganCardVO;
import com.akucun.fps.pingan.client.vo.UntieWithdrawVO;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * @Author: silei
 * @Date: 2020/11/12
 * @desc: 聚合层银行卡相关接口
 */
@FeignClient(value = "account-proxy", fallbackFactory = AccountAggregationBankCardFacadeFallbackFactory.class)
@RequestMapping("/api/account/aggregation/bankCard")
public interface AccountAggregationBankCardFacade {

    /**
     * 查询银联银行列表
     * @param channel
     * @return
     */
    @GetMapping(value = "/queryBankInfoManageByChannel", produces = "application/json;charset=utf-8")
    ResultList<BankInfoManageResp> queryBankInfoManageByChannel(@RequestParam("channel") String channel);


    /**
     * 银联鉴权申请接口
     * @param pinganCardVO
     * @return
     */
    @PostMapping(value = {"/unionPayAuthApply"},produces = {"application/json;charset=utf-8"})
    Result<String> unionPayAuthApply(@RequestBody PinganCardVO pinganCardVO);

    /**
     * 银联鉴权确认接口
     * @param registerAccountReqDO
     * @return
     */
    @PostMapping(value = {"/unionPayAuthConfirm"},produces = {"application/json;charset=utf-8"})
    Result<PingAnBindingRegisterAccountRespDO> unionPayAuthConfirm(@RequestBody PingAnBindingRegisterAccountReqDO registerAccountReqDO);

    /**
     * 解绑银行卡
     * @param untieWithdrawVO
     * @return
     */
    @PostMapping(value = {"/untieWithdraw"},produces = {"application/json;charset=utf-8"})
    Result<String> untieWithdraw(@RequestBody UntieWithdrawVO untieWithdrawVO);

    /**
     * 查询账户绑卡信息
     * @param bindCardQueryReq
     * @return
     */
    @PostMapping(value = {"/selectBindCardListByParams"}, produces = {"application/json;charset=utf-8"})
    ResultList<PinganCardVO> selectBindCardListByParams(@RequestBody BindCardQueryReq bindCardQueryReq);

    /**
     * 根据分支行名称模糊查询具体分支行名称
     * @param subBankInfoReq
     * @return
     */
    @PostMapping("/selectSubBankCityPage")
    ResultList<SubBankInfoDO> selectSubBankCityPage(@RequestBody SubBankInfoReq subBankInfoReq);

    /**
     * 查询省份
     * @return
     */
    @PostMapping("/selectProvince")
    ResultList<BankNode> selectProvince();

    /**
     * 根据银行名称模糊查询具体银行
     * @param bankName
     * @return
     */
    @PostMapping("/selectByBankName")
    ResultList<BankInfoManageResp> selectByBankName(@RequestParam("bankName") String bankName);

    /**
     * 根据省份查询市
     * @param nodeCode
     * @return
     */
    @PostMapping("/selectBankCity")
    ResultList<BankCityInfo> selectBankCity(@RequestParam("nodeCode") String nodeCode);

    /**
     * 绑卡发送小额鉴权申请
     *
     * @param
     * @param bindCardInfoDTO
     * @return
     */
    @PostMapping("/bindCardAndCheck")
    Result<Void> bindCardAndCheck(@RequestBody @Validated BindCardInfoDTO bindCardInfoDTO);

    /**
     * 小额鉴权鉴权金额回写验证
     * @param bindCardVerifyDTO
     * @return
     */
    @PostMapping("/bindCardForMerSec")
    Result<Void> bindCardForMerSec(@RequestBody @Validated BindCardVerifyDTO bindCardVerifyDTO);

    /**
     * 查询小额鉴权状态
     * @param customerCode
     * @param customerType
     * @return
     */
    @PostMapping("/authStatusCheck")
    Result<BindAuthResp> authStatusCheck(@RequestParam("customerCode") String customerCode, @RequestParam("customerType") String customerType);

    /**
     * 查询账户升级状态
     * @param customerCode
     * @param customerType
     * @return
     */
    @PostMapping("/queryAccountStatus")
    Result<Boolean> queryAccountStatus(@RequestParam("customerCode") String customerCode, @RequestParam("customerType") String customerType);
}
