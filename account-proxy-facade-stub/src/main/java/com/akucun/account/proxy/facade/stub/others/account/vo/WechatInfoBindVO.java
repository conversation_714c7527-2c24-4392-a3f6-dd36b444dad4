package com.akucun.account.proxy.facade.stub.others.account.vo;

import com.akucun.account.proxy.facade.stub.enums.CustomerType;
import com.mengxiang.base.common.utils.datamasking.Sensitive;
import com.mengxiang.base.common.utils.datamasking.SensitiveType;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;

/**
 * @Author: silei
 * @Date: 2021/5/1
 * @desc:
 */
public class WechatInfoBindVO implements Serializable {

    private static final long serialVersionUID = 1186322350623528011L;
    /**
     * 客户名称
     */
    @Sensitive(type = SensitiveType.Name)
    @NotBlank(message = "客户姓名不能为空")
    @ApiModelProperty("客户姓名")
    private String customerName;
    /**
     * 客户编码
     */
    @NotBlank(message = "客户编码不能为空")
    @ApiModelProperty("客户编码")
    private String customerCode;
    /**
     * 客户类型
     * @see CustomerType
     */
    @NotBlank(message = "客户类型不能为空")
    @ApiModelProperty("客户类型")
    private String customerType;

    @NotBlank(message = "微信appId不能为空")
    @ApiModelProperty("微信appId")
    private String appId;

    @NotBlank(message = "微信openId不能为空")
    @ApiModelProperty("微信openId")
    private String openId;

    @NotBlank(message = "订单channelCode不能为空")
    @ApiModelProperty("订单channelCode")
    private String channelCode;

    @NotBlank(message = "租户id不能为空")
    @ApiModelProperty("租户id")
    private String tenantId;

    @ApiModelProperty("状态：0生效，1失效")
    private Integer status;

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getCustomerType() {
        return customerType;
    }

    public void setCustomerType(String customerType) {
        this.customerType = customerType;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return "WechatInfoBindVO{" +
                "customerName='" + customerName + '\'' +
                ", customerCode='" + customerCode + '\'' +
                ", customerType='" + customerType + '\'' +
                ", appId='" + appId + '\'' +
                ", openId='" + openId + '\'' +
                ", channelCode='" + channelCode + '\'' +
                ", tenantId='" + tenantId + '\'' +
                ", status=" + status +
                '}';
    }
}
