package com.akucun.account.proxy.facade.stub.others.aggregation.res;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 提现扣税汇总返回参数
 *
 * <AUTHOR>
 * @version [版本号, 2020年7月7日]
 */
public class WithdrawTaxSummaryResp implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 3674187594985479575L;

    private Long summaryId;
    /**
     * 月份
     */
    private String month;
    /**
     * 身份证
     */
    private String identifyNo;
    /**
     * 名称
     */
    private String identifyName;
    /**
     * 身份类别
     */
    private String identifyCategory;
    /**
     * 提现总金额：元
     */
    private BigDecimal amount;
    /**
     * 实际到账金额：元
     */
    private BigDecimal withdraw;
    /**
     * 扣税金额：元
     */
    private BigDecimal taxFee;

    public String getMonth() {
        return month;
    }

    public void setMonth(String month) {
        this.month = month;
    }

    public String getIdentifyNo() {
        return identifyNo;
    }

    public void setIdentifyNo(String identifyNo) {
        this.identifyNo = identifyNo;
    }

    public String getIdentifyName() {
        return identifyName;
    }

    public void setIdentifyName(String identifyName) {
        this.identifyName = identifyName;
    }

    public String getIdentifyCategory() {
        return identifyCategory;
    }

    public void setIdentifyCategory(String identifyCategory) {
        this.identifyCategory = identifyCategory;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getWithdraw() {
        return withdraw;
    }

    public void setWithdraw(BigDecimal withdraw) {
        this.withdraw = withdraw;
    }

    public BigDecimal getTaxFee() {
        return taxFee;
    }

    public void setTaxFee(BigDecimal taxFee) {
        this.taxFee = taxFee;
    }

    public Long getSummaryId() {
        return summaryId;
    }

    public void setSummaryId(Long summaryId) {
        this.summaryId = summaryId;
    }
}
