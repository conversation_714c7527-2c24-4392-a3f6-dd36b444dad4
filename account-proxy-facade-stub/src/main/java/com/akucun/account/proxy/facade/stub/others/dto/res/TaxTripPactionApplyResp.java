package com.akucun.account.proxy.facade.stub.others.dto.res;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class TaxTripPactionApplyResp {

    /**
     * 用户名
     */
    private String userName;

    /**
     * 客户编码
     */
    private String customerCode;

    /**
     * 客户类型
     */
    private String customerType;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 银行编码
     */
    private String bankCode;

    /**
     * 银行卡号
     */
    private String bankCardNo;

    /**
     * 协议图片路径
     */
    private String pactionImgUrl;

    /**
     * 状态 0：审核中，1：审核通过，2：审核未通过
     */
    private int status;

    /**
     * 驳回原因
     */
    private String rejectReason;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}
