package com.akucun.account.proxy.facade.stub.fallback;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.facade.stub.aggregation.AccountAggregationBankCardFacade;
import com.akucun.account.proxy.facade.stub.others.aggregation.req.BindCardInfoDTO;
import com.akucun.account.proxy.facade.stub.others.aggregation.req.BindCardVerifyDTO;
import com.akucun.account.proxy.facade.stub.others.aggregation.res.BindAuthResp;
import com.akucun.fps.common.entity.ResultList;
import com.akucun.fps.pingan.client.model.*;
import com.akucun.fps.pingan.client.model.po.BankNode;
import com.akucun.fps.pingan.client.model.query.BindCardQueryReq;
import com.akucun.fps.pingan.client.vo.PinganCardVO;
import com.akucun.fps.pingan.client.vo.UntieWithdrawVO;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * @Author: silei
 * @Date: 2020/11/15
 * @desc:
 */
@Component
public class AccountAggregationBankCardFacadeFallbackFactory implements FallbackFactory<AccountAggregationBankCardFacade> {
    @Override
    public AccountAggregationBankCardFacade create(Throwable throwable) {
        return new AccountAggregationBankCardFacade() {
            @Override
            public ResultList<BankInfoManageResp> queryBankInfoManageByChannel(String channel) {
                ResultList result = new ResultList<>();
                result.setError(500, "","服务超时熔断");
                return result;
            }

            @Override
            public Result<String> unionPayAuthApply(PinganCardVO pinganCardVO) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<PingAnBindingRegisterAccountRespDO> unionPayAuthConfirm(PingAnBindingRegisterAccountReqDO registerAccountReqDO) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<String> untieWithdraw(UntieWithdrawVO untieWithdrawVO) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public ResultList<PinganCardVO> selectBindCardListByParams(BindCardQueryReq bindCardQueryReq) {
                ResultList result = new ResultList<>();
                result.setError(500, "","服务超时熔断");
                return result;
            }

            @Override
            public ResultList<SubBankInfoDO> selectSubBankCityPage(SubBankInfoReq subBankInfoReq) {
                ResultList result = new ResultList<>();
                result.setError(500, "","服务超时熔断");
                return result;
            }

            @Override
            public ResultList<BankNode> selectProvince() {
                ResultList result = new ResultList<>();
                result.setError(500, "","服务超时熔断");
                return result;
            }

            @Override
            public ResultList<BankInfoManageResp> selectByBankName(String bankName) {
                ResultList result = new ResultList<>();
                result.setError(500, "","服务超时熔断");
                return result;
            }

            @Override
            public ResultList<BankCityInfo> selectBankCity(String nodeCode) {
                ResultList result = new ResultList<>();
                result.setError(500, "","服务超时熔断");
                return result;
            }

            @Override
            public Result<Void> bindCardAndCheck(BindCardInfoDTO bindCardInfoDTO) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<Void> bindCardForMerSec(BindCardVerifyDTO bindCardVerifyDTO) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<BindAuthResp> authStatusCheck(String customerCode, String customerType) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<Boolean> queryAccountStatus(String customerCode, String customerType) {
                return Result.error(500, "服务超时熔断");
            }
        };
    }
}
