package com.akucun.account.proxy.facade.stub.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Create on : 2024/5/7 14:51
 **/
@Getter
@AllArgsConstructor
public enum MerchantFullReturnObjectTypeEnum {

    MERCHANT("商家"),

    SHOP("店铺"),

    PLATFORM("平台"),

    COMMUNITY("战队"),

    SELLER("店主"),

    SCHOOL("学校"),

    DISTRIBUTOR("店长");

    private String desc;

}
