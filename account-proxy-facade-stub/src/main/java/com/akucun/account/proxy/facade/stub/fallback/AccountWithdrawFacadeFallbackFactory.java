package com.akucun.account.proxy.facade.stub.fallback;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.facade.stub.account.AccountWithdrawFacade;
import com.akucun.account.proxy.facade.stub.others.account.req.ShopkeeperIncentiveAwardWithdrawCalcTaxReq;
import com.akucun.account.proxy.facade.stub.others.account.vo.AccountTotalVO;
import com.akucun.account.proxy.facade.stub.others.account.vo.AccountWithdrawVO;
import com.akucun.account.proxy.facade.stub.others.account.vo.NotifyWithdrawVO;
import com.akucun.account.proxy.facade.stub.others.account.vo.WithdrawTaxDetailVO;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * @Author: silei
 * @Date: 2021/3/5
 * @desc:
 */
@Component
public class AccountWithdrawFacadeFallbackFactory implements FallbackFactory<AccountWithdrawFacade> {
    @Override
    public AccountWithdrawFacade create(Throwable throwable) {
        return new AccountWithdrawFacade(){

            @Override
            public Result<String> applyWithdraw(AccountWithdrawVO accountWithdrawVO) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<Void> notifyWithdrawResp(NotifyWithdrawVO notifyWithdrawVO) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<AccountTotalVO> selectAccountTotal(AccountTotalVO accountTotalVO) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<String> applyWechatWithdraw(AccountWithdrawVO accountWithdrawVO) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<WithdrawTaxDetailVO> shopkeeperIncentiveAwardWithdrawCalcTax(ShopkeeperIncentiveAwardWithdrawCalcTaxReq req) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<String> addWithdrawRecord(AccountWithdrawVO accountWithdrawVO) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<String> getWithdrawReceiptDownloadUrl(String withdrawNo) {
                return Result.error(500, "服务超时熔断");
            }
        };
    }
}
