package com.akucun.account.proxy.facade.stub.others.dto.res;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/01/03 14:05
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("vip卡信息查询返回实体")
public class VipCardInfoResp implements Serializable {


    private static final long serialVersionUID = 4315322977846745589L;
    @ApiModelProperty(value = "vip卡号")
    private String cardNo;

    @ApiModelProperty(value = "邀请码（卡密）")
    private String inviteCode;

    @ApiModelProperty(value = "vip卡类型：奖励金发放卡-BONUS")
    private String cardType;

    @ApiModelProperty(value = "vip卡金额(分)")
    private Long amount;

    @ApiModelProperty(value = "vip卡金额(元)")
    private BigDecimal amountY;

    @ApiModelProperty(value = "vip卡有效期开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date beginTime;

    @ApiModelProperty(value = "vip卡有效期结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

}
