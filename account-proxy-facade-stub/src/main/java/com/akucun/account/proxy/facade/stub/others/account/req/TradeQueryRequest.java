package com.akucun.account.proxy.facade.stub.others.account.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@Data
public class TradeQueryRequest implements Serializable {
    private static final long serialVersionUID = -2268490547638541868L;

    @ApiModelProperty(value = "客户编码")
    private String customerCode;
    @ApiModelProperty(value = "业务类型，参考BusiTypeEnum")
    private String busiType;
    @ApiModelProperty(value = "账户类型，参考AccountTypeEnum")
    private String accountType;
    @ApiModelProperty(value = "开始时间：2023-08-12")
    private String beginDate;
    @ApiModelProperty(value = "开始时间：2023-08-13")
    private String endDate;
}
