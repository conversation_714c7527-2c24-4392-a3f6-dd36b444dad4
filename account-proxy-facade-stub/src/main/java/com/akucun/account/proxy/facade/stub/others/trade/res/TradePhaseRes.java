package com.akucun.account.proxy.facade.stub.others.trade.res;

import lombok.Getter;
import lombok.Setter;

/**
 * 交易阶段结果
 */
@Getter
@Setter
public class TradePhaseRes {

    /**
     * 阶段编码
     */
    private String phaseCode;

    /**
     * 阶段状态
     */
    private TradePhaseStatusEnum status;

    /**
     * 失败或异常编码
     */
    private String errorCode;

    /**
     * 失败或异常信息
     */
    private String errorMessage;

}
