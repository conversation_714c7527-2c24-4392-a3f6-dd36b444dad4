package com.akucun.account.proxy.facade.stub.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

@Getter
public enum TradeChannel {

    APP("AKCAPP", "爱库存订单"),
    OPENAPI("OPENAPI", "openApi订单"),
    POINT_MALL("POINT_MALL","积分兑换订单"),
    XIANGDIAN("XIANGDIAN","饷店订单"),
    POINT_RECHARGE("POINT_RECHARGE","积分充值订单"),
    OSM_AKC_CIRCLE("OSM_AKC_CIRCLE","素材圈子订单")
    ;

    private String code;
    private String codeDesc;

    TradeChannel(String code, String codeDesc) {
        this.code = code;
        this.codeDesc = codeDesc;
    }

    public static String queryDescByCode(String code) {
        TradeChannel resultEnum = null;
        for(TradeChannel tradeChannel : TradeChannel.values()) {
            if(tradeChannel.getCode().equals(code)) {
                resultEnum = tradeChannel;
            }
        }
        if(Objects.nonNull(resultEnum)) {
            return resultEnum.getCodeDesc();
        } else {
            return null;
        }
    }

    public static void main(String[] args) {
        String s = queryDescByCode("AKCAPP");
        System.out.println(s);
    }
}
