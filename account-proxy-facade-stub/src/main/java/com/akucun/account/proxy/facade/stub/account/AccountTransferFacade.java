package com.akucun.account.proxy.facade.stub.account;

import com.akucun.account.proxy.facade.stub.fallback.AccountTradeFacadeFallbackFactory;
import com.akucun.account.proxy.facade.stub.fallback.AccountTransferFacadeFallbackFactory;
import com.akucun.fps.account.client.model.TransferAccountDO;
import com.akucun.fps.account.client.model.query.TransferAccountQueryDO;
import com.akucun.fps.common.entity.Query;
import com.akucun.fps.common.entity.Result;
import com.akucun.fps.common.entity.ResultList;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * @Author: silei
 * @Date: 2021/2/3
 * @desc:转账申请
 */
@FeignClient(value = "account-proxy", fallbackFactory = AccountTransferFacadeFallbackFactory.class)
@RequestMapping("/api/account/proxy/transfer")
public interface AccountTransferFacade {

    /**
     * 转账申请 当前仅适用H5
     * @param transferAccountDO
     * @return
     */
    @PostMapping(value = "/transferApply")
    Result<String> transferApply(@RequestBody TransferAccountDO transferAccountDO);

    /**
     * 转账申请分页查询
     * @param query
     * @return
     */
    @PostMapping(value = "/selectPage")
    ResultList<TransferAccountDO> selectPage(@RequestBody Query<TransferAccountQueryDO> query);

}
