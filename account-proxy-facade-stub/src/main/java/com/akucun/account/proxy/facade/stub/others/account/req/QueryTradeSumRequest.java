package com.akucun.account.proxy.facade.stub.others.account.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class QueryTradeSumRequest implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "客户编码")
    private String customerCode;
    @ApiModelProperty(value = "开始时间：2023-08-12")
    private String beginDate;
    @ApiModelProperty(value = "开始时间：2023-08-13")
    private String endDate;
}
