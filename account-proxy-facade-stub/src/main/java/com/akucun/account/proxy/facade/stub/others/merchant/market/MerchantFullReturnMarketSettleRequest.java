package com.akucun.account.proxy.facade.stub.others.merchant.market;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Create on : 2024/4/26 16:44
 **/
@Data
@ApiModel
public class MerchantFullReturnMarketSettleRequest implements Serializable {

    /**
     * 营销活动ID = TaskAcceptRequest.bizNo
     */
    @ApiModelProperty("营销活动ID = TaskAcceptRequest.bizNo")
    @NotBlank(message = "promoActivityId不能为空")
    private String promoActivityId;

    /**
     * 营销活动类型 = TaskAcceptRequest.bizInfo.promoActivityType
     */
    @ApiModelProperty("营销活动类型 = TaskAcceptRequest.bizInfo.promoActivityType")
    @NotBlank(message = "promoActivityType不能为空")
    private String promoActivityType;

    /**
     * 活动出资/奖励金额 = TaskAcceptRequest.bizInfo.amount
     */
    @ApiModelProperty("活动出资/奖励金额 = TaskAcceptRequest.bizInfo.amount")
    @NotNull(message = "amount不能为空")
    @DecimalMin(value = "0", message = "amount不能小于0")
    private BigDecimal amount;

    /**
     * 业务单号（控幂等的） = TaskAcceptRequest.bizInfo.bizNo
     */
    @ApiModelProperty("业务单号（控幂等的） = TaskAcceptRequest.bizInfo.bizNo")
    @NotBlank(message = "bizNo不能为空")
    private String bizNo;

    /**
     * 说明 = TaskAcceptRequest.bizInfo.bizExplain
     */
    @ApiModelProperty("说明 = TaskAcceptRequest.bizInfo.bizExplain")
    @NotBlank(message = "bizExplain不能为空")
    private String bizExplain;

    /**
     * 用户编码 = TaskAcceptRequest.bizInfo.objectId
     */
    @ApiModelProperty("用户编码 = TaskAcceptRequest.bizInfo.objectId")
    @NotBlank(message = "objectId不能为空")
    private String objectId;

    /**
     * 用户类型 = TaskAcceptRequest.bizInfo.objectType
     * SHOP：店铺，MERCHANT：商家，PLATFORM：平台，COMMUNITY：战队，SELLER：店主，SCHOOL：学校，DISTRIBUTOR：店长
     */
    @ApiModelProperty("用户类型 = TaskAcceptRequest.bizInfo.objectType")
    @NotBlank(message = "objectType不能为空")
    private String objectType;

    /**
     * 请求平台 = TaskAcceptRequest.requestPlatform
     */
    @ApiModelProperty("请求平台 = TaskAcceptRequest.requestPlatform")
    @NotBlank(message = "requestPlatform不能为空")
    private String requestPlatform;

    /**
     * 奖励方式 = TaskAcceptRequest.bizInfo.way
     * CASH：现金，BONUS：奖励金
     */
    @ApiModelProperty("奖励方式 = TaskAcceptRequest.bizInfo.way，CASH：现金，BONUS：奖励金")
    @NotBlank(message = "way不能为空")
    private String way;

    /**
     * 来源场景 = TaskAcceptRequest.bizInfo.sourceScene
     * 通知结果时使用
     */
    @ApiModelProperty("来源场景 = TaskAcceptRequest.bizInfo.sourceScene")
    @NotBlank(message = "sourceScene不能为空")
    private String sourceScene;

    /**
     * 来源单号 = TaskAcceptRequest.bizInfo.sourceNo
     * 通知结果时使用
     */
    @ApiModelProperty("来源单号 = TaskAcceptRequest.bizInfo.sourceNo")
    private String sourceNo;

    /**
     * 用户类型 1真实 ，2打包 = TaskAcceptRequest.bizInfo.dataType
     *
     */
    @ApiModelProperty("用户类型 1真实 ，2打包 = TaskAcceptRequest.bizInfo.dataType")
    private String dataType;

    /**
     * 战队队员idol编号数组 = TaskAcceptRequest.bizInfo.rule
     */
    @ApiModelProperty("战队队员idol编号数组 = TaskAcceptRequest.bizInfo.rule")
    private List<String> rule;

    /**
     * 税点 = TaskAcceptRequest.bizInfo.taxPoint
     */
    @ApiModelProperty("税点 = TaskAcceptRequest.bizInfo.taxPoint")
    private BigDecimal taxPoint;

    /**
     * 商家承担税费 = TaskAcceptRequest.bizInfo.taxAmount
     */
    @ApiModelProperty("商家承担税费 = TaskAcceptRequest.bizInfo.taxAmount")
    private BigDecimal taxAmount;

    @ApiModelProperty("商家编码，新增字段")
    //@NotBlank(message = "merchantCode不能为空")
    private String merchantCode;

}
