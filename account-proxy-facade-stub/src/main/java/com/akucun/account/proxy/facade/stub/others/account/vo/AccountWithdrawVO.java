package com.akucun.account.proxy.facade.stub.others.account.vo;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.mengxiang.base.common.utils.datamasking.Sensitive;
import com.mengxiang.base.common.utils.datamasking.SensitiveType;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: silei
 * @Date: 2021/3/5
 * @desc:
 */
public class AccountWithdrawVO implements Serializable {

    private static final long serialVersionUID = -7237632421549573338L;

    private Long id;
    private String withdrawNo;
    private String customerCode;
    @Sensitive(type = SensitiveType.Name)
    private String customerName;
    private String customerType;
    private String sourceBillNo;
    private String bankName;
    @Sensitive(type = SensitiveType.BankCard)
    private String bankNo;
    private BigDecimal amount;
    private BigDecimal serviceAmount;
    private String applyStatus;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updateTime;
    private String modifyUser;
    @Sensitive(type = SensitiveType.Name)
    private String applyUser;
    private String remark;
    private String failReason;
    private String withdrawChannel;
    private String shopId;
    private String identifyNo;
    private String month;
    private String bankLogo;
    private BigDecimal taxFee;
    private BigDecimal withdraw;
    private String customerGrade;
    private BigDecimal currMonthSummary;
    private String feeRate;
    private BigDecimal freeFeeAmount;

    /**
     * subMerchantId 二级商户id（必传）
     */
    private String subMerchantId;
    /**
     * bankMemo 银行赠言（必传）
     */
    private String bankMemo;
    /**
     *     BASIC：基本账户\n" +
     *     "OPERATION：运营账户\n" +
     *     "FEES：手续费账户  平台提现所传类型
     *     （必传）
     */
    private String accountType;

    /**
     * withdrawType SUB 二级商户 PLAT 平台
     * （必传）
     */
    private String withdrawType;

    /**
     * 提现到微信余额
     * @return
     */
    private String openId;
    private String channelCode;
    private String tenantId;
    
    /**
     * 扩展参数
     */
    private String ext;



    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getWithdrawNo() {
        return withdrawNo;
    }

    public void setWithdrawNo(String withdrawNo) {
        this.withdrawNo = withdrawNo;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getCustomerType() {
        return customerType;
    }

    public void setCustomerType(String customerType) {
        this.customerType = customerType;
    }

    public String getSourceBillNo() {
        return sourceBillNo;
    }

    public void setSourceBillNo(String sourceBillNo) {
        this.sourceBillNo = sourceBillNo;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getBankNo() {
        return bankNo;
    }

    public void setBankNo(String bankNo) {
        this.bankNo = bankNo;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getServiceAmount() {
        return serviceAmount;
    }

    public void setServiceAmount(BigDecimal serviceAmount) {
        this.serviceAmount = serviceAmount;
    }

    public String getApplyStatus() {
        return applyStatus;
    }

    public void setApplyStatus(String applyStatus) {
        this.applyStatus = applyStatus;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getModifyUser() {
        return modifyUser;
    }

    public void setModifyUser(String modifyUser) {
        this.modifyUser = modifyUser;
    }

    public String getApplyUser() {
        return applyUser;
    }

    public void setApplyUser(String applyUser) {
        this.applyUser = applyUser;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getFailReason() {
        return failReason;
    }

    public void setFailReason(String failReason) {
        this.failReason = failReason;
    }

    public String getWithdrawChannel() {
        return withdrawChannel;
    }

    public void setWithdrawChannel(String withdrawChannel) {
        this.withdrawChannel = withdrawChannel;
    }

    public String getShopId() {
        return shopId;
    }

    public void setShopId(String shopId) {
        this.shopId = shopId;
    }

    public String getIdentifyNo() {
        return identifyNo;
    }

    public void setIdentifyNo(String identifyNo) {
        this.identifyNo = identifyNo;
    }

    public String getMonth() {
        return month;
    }

    public void setMonth(String month) {
        this.month = month;
    }

    public BigDecimal getTaxFee() {
        return taxFee;
    }

    public void setTaxFee(BigDecimal taxFee) {
        this.taxFee = taxFee;
    }

    public BigDecimal getWithdraw() {
        return withdraw;
    }

    public void setWithdraw(BigDecimal withdraw) {
        this.withdraw = withdraw;
    }

    public String getCustomerGrade() {
        return customerGrade;
    }

    public void setCustomerGrade(String customerGrade) {
        this.customerGrade = customerGrade;
    }

    public BigDecimal getCurrMonthSummary() {
        return currMonthSummary;
    }

    public void setCurrMonthSummary(BigDecimal currMonthSummary) {
        this.currMonthSummary = currMonthSummary;
    }

    public String getFeeRate() {
        return feeRate;
    }

    public void setFeeRate(String feeRate) {
        this.feeRate = feeRate;
    }

    public String getSubMerchantId() {
        return subMerchantId;
    }

    public void setSubMerchantId(String subMerchantId) {
        this.subMerchantId = subMerchantId;
    }

    public String getBankMemo() {
        return bankMemo;
    }

    public void setBankMemo(String bankMemo) {
        this.bankMemo = bankMemo;
    }

    public String getAccountType() {
        return accountType;
    }

    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }

    public String getWithdrawType() {
        return withdrawType;
    }

    public void setWithdrawType(String withdrawType) {
        this.withdrawType = withdrawType;
    }

    public String getBankLogo() {
        return bankLogo;
    }

    public void setBankLogo(String bankLogo) {
        this.bankLogo = bankLogo;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public BigDecimal getFreeFeeAmount() {
        return freeFeeAmount;
    }

    public void setFreeFeeAmount(BigDecimal freeFeeAmount) {
        this.freeFeeAmount = freeFeeAmount;
    }

    public String getExt() {
		return ext;
	}

	public void setExt(String ext) {
		this.ext = ext;
	}

	@Override
    public String toString() {
        return "AccountWithdrawVO{" +
                "id=" + id +
                ", withdrawNo='" + withdrawNo + '\'' +
                ", customerCode='" + customerCode + '\'' +
                ", customerName='" + customerName + '\'' +
                ", customerType='" + customerType + '\'' +
                ", sourceBillNo='" + sourceBillNo + '\'' +
                ", bankName='" + bankName + '\'' +
                ", bankNo='" + bankNo + '\'' +
                ", amount=" + amount +
                ", serviceAmount=" + serviceAmount +
                ", applyStatus='" + applyStatus + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", modifyUser='" + modifyUser + '\'' +
                ", applyUser='" + applyUser + '\'' +
                ", remark='" + remark + '\'' +
                ", failReason='" + failReason + '\'' +
                ", withdrawChannel='" + withdrawChannel + '\'' +
                ", shopId='" + shopId + '\'' +
                ", identifyNo='" + identifyNo + '\'' +
                ", month='" + month + '\'' +
                ", bankLogo='" + bankLogo + '\'' +
                ", taxFee=" + taxFee +
                ", withdraw=" + withdraw +
                ", customerGrade='" + customerGrade + '\'' +
                ", currMonthSummary=" + currMonthSummary +
                ", feeRate='" + feeRate + '\'' +
                ", freeFeeAmount='" + freeFeeAmount + '\'' +
                ", subMerchantId='" + subMerchantId + '\'' +
                ", bankMemo='" + bankMemo + '\'' +
                ", accountType='" + accountType + '\'' +
                ", withdrawType='" + withdrawType + '\'' +
                ", openId='" + openId + '\'' +
                ", channelCode='" + channelCode + '\'' +
                ", tenantId='" + tenantId + '\'' +
                '}';
    }

    public void setNoTaxFlag() {
        Map<String, String> map;
        if (StringUtils.isBlank(ext)) {
            map = new HashMap<>();
            map.put("noTaxFlag", "yes");
        } else {
            map = JSON.parseObject(ext, Map.class);
            map.put("noTaxFlag", "yes");
        }
        this.ext = JSON.toJSONString(map);
    }
}
