package com.akucun.account.proxy.facade.stub.commission;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.facade.stub.fallback.AccountCommissionFacadeFallbackFactory;
import com.akucun.account.proxy.facade.stub.others.trade.req.AccountCommissionReq;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * @Author: silei
 * @Date: 2020/12/7
 * @desc: 账户分佣facade
 */
@FeignClient(value = "account-proxy", fallbackFactory = AccountCommissionFacadeFallbackFactory.class)
@RequestMapping("/api/account")
public interface AccountCommissionFacade {

    /**
     * 账户分佣
     * @param req
     * @return
     */
    @PostMapping(value = "/allocateCommission")
    Result<Void> allocateCommission(@RequestBody AccountCommissionReq req);

}
