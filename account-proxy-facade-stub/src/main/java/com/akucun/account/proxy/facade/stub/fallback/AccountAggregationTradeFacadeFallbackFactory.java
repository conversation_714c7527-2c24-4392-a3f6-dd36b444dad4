package com.akucun.account.proxy.facade.stub.fallback;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.facade.stub.aggregation.AccountAggregationTradeFacade;
import com.akucun.account.proxy.facade.stub.others.account.req.WithdrawQueryReq;
import com.akucun.account.proxy.facade.stub.others.account.vo.AccountWithdrawVO;
import com.akucun.account.proxy.facade.stub.others.aggregation.req.TenantWithdrawQueryReq;
import com.akucun.account.proxy.facade.stub.others.aggregation.req.WithdrawTaxCalcReq;
import com.akucun.account.proxy.facade.stub.others.aggregation.res.WithdrawServiceFeeSwitchResp;
import com.akucun.account.proxy.facade.stub.others.aggregation.res.WithdrawTaxCalcResp;
import com.akucun.account.proxy.facade.stub.others.aggregation.vo.TenantWithdrawApplyVO;
import com.akucun.fps.account.client.model.vo.WithdrawTaxSwitchVO;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * @Author: silei
 * @Date: 2021/1/26
 * @desc:
 */
@Component
public class AccountAggregationTradeFacadeFallbackFactory implements FallbackFactory<AccountAggregationTradeFacade> {
    @Override
    public AccountAggregationTradeFacade create(Throwable throwable) {
        return new AccountAggregationTradeFacade(){


            @Override
            public Result<AccountWithdrawVO> queryWithdrawDetail(WithdrawQueryReq withdrawQueryReq) {
                return Result.error(500, "服务超时熔断");
            }

//            @Override
//            public Result<String> agentApplyWithDraw(WithdrawAuditDO withdrawAuditDO) {
//                return Result.error(500, "服务超时熔断");
//            }

            @Override
            public Result<String> applyBankWithDraw(AccountWithdrawVO accountWithdrawVO) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<WithdrawTaxCalcResp> taxClac(WithdrawTaxCalcReq withdrawTaxCalcReq) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<WithdrawTaxSwitchVO> taxSwitch(String customerCode) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<WithdrawServiceFeeSwitchResp> serviceFeeSwitch(String identifyNo) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<Boolean> queryProcessingWithdraw(String customerCode, String customerType) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<String> applyWechatWithDraw(AccountWithdrawVO accountWithdrawVO) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<String> tenantApplyWithDraw(TenantWithdrawApplyVO vo) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<TenantWithdrawApplyVO> queryTenantWithdrawDetail(TenantWithdrawQueryReq req) {
                return Result.error(500, "服务超时熔断");
            }
        };
    }
}
