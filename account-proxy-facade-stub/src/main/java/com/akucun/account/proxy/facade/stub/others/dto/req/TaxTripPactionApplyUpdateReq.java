package com.akucun.account.proxy.facade.stub.others.dto.req;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class TaxTripPactionApplyUpdateReq {

    /**
     * 审核工单编号
     */
    @NotBlank
    private String auditNo;

    /**
     * 审核结果，true:成功，false:驳回
     */
    @NotNull
    private Boolean auditResult;

    /**
     * 驳回原因,auditResult为false时，该字段不允许为空
     */
    private List<String> rejectReason;
}
