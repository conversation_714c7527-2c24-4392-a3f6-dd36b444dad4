package com.akucun.account.proxy.facade.stub.fallback;

import com.akucun.fps.common.entity.Result;
import com.akucun.fps.pingan.client.model.po.PinganAccount;
import com.akucun.fps.pingan.client.vo.*;
import com.akucun.account.proxy.facade.stub.pingan.MerchantFacade;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

@Component
public class MerchantFacadeFallbackFactory implements FallbackFactory<MerchantFacade> {
    @Override
    public MerchantFacade create(Throwable throwable) {
        return new MerchantFacade() {
            @Override
            public Result<String> registerPinganAccount(MemBerRegisterVO memBerRegisterVO) {
                Result<String> result = new Result<String>();
                result.setError(500, "SERVICE_ERROR", "服务超时熔断");
                return result;
            }

            @Override
            public Result<String> register(MerchantRegisterVO merchantRegisterVO) {
                Result<String> result = new Result<String>();
                result.setError(500, "SERVICE_ERROR", "服务超时熔断");
                return result;
            }

            @Override
            public Result<String> bindCardForMerFir(PinganCardVO pinganCardVO) {
                Result<String> result = new Result<String>();
                result.setError(500, "SERVICE_ERROR", "服务超时熔断");
                return result;
            }

            @Override
            public Result<String> bindCardForMerSec(PinganCardVercVO pinganCardVercVO) {
                Result<String> result = new Result<String>();
                result.setError(500, "SERVICE_ERROR", "服务超时熔断");
                return result;
            }

            @Override
            public Result<String> untieWithdraw(UntieWithdrawVO untieWithdrawVO) {
                Result<String> result = new Result<String>();
                result.setError(500, "SERVICE_ERROR", "服务超时熔断");
                return result;
            }

            @Override
            public Result<AuthCodeResultVO> getAuthCode(AuthCodeVO authCodeVO) {
                Result<AuthCodeResultVO> result = new Result<AuthCodeResultVO>();
                result.setError(500, "SERVICE_ERROR", "服务超时熔断");
                return result;
            }

            /**
             * 查询某笔小额鉴权转账状态
             *
             * @param
             * @return
             * @des 状态：0：成功，1：失败，2：待确认
             */
            @Override
            public Result<AuthStatusResultVO> selectAuthStatusByCode(AuthCodeVO authCodeVO) {
                Result<AuthStatusResultVO> result = new Result<AuthStatusResultVO>();
                result.setError(500, "SERVICE_ERROR", "服务超时熔断");
                return result;
            }

            @Override
            public Result<String> maintainWithdraw(MaintainWithdrawVO maintainWithdrawVO) {
                Result<String> result = new Result<String>();
                result.setError(500, "SERVICE_ERROR", "服务超时熔断");
                return result;
            }

            @Override
            public Result<PinganAccount> selectPinganAccount(AccountRegisterVO accountRegisterVO) {
                Result<PinganAccount> result = new Result<PinganAccount>();
                result.setError(500, "SERVICE_ERROR", "服务超时熔断");
                return result;
            }

            @Override
            public Result<String> untieWithdrawSelf(UntieWithdrawVO untieWithdrawVO) {
                Result<String> result = new Result<String>();
                result.setError(500, "SERVICE_ERROR", "服务超时熔断");
                return result;
            }

            @Override
            public Result<Void> cancelPinganAccount(MemBerRegisterVO memBerRegisterVO) {
                Result<Void> result = new Result<Void>();
                result.setError(500, "SERVICE_ERROR", "服务超时熔断");
                return result;
            }

            @Override
            public Result<Void> updatePinganAccount(PinganAccount pinganAccount) {
                Result<Void> result = new Result<Void>();
                result.setError(500, "SERVICE_ERROR", "服务超时熔断");
                return result;
            }
        };
    }
}
