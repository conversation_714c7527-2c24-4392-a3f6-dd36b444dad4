package com.akucun.account.proxy.facade.stub.others.dto.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: silei
 * @Date: 2020/8/26
 * @desc: 账户交易请求体
 */

@ApiModel("账户交易请求实体")
@Data
@ToString
@NoArgsConstructor
public class AccountTradeRequest implements Serializable {

    private static final long serialVersionUID = -1347899873556975884L;

    /**
     * 订单来源系统编码
     */
    @NotBlank(message = "来源系统编码不能为空")
    @ApiModelProperty(value = "订单来源系统编码", required = true)
    private String sourceCode;

    /**
     * 交易渠道
     */
    @NotBlank(message = "交易渠道不能为空")
    @ApiModelProperty(value = "交易渠道", required = true)
    private String channel;

    /**
     * 来源单号
     */
    @NotBlank(message = "来源单号不能为空")
    @ApiModelProperty(value = "来源单号", required = true)
    private String sourceNo;

    /**
     * 交易单号
     */
    @NotBlank(message = "交易单号不能为空")
    @ApiModelProperty(value = "交易单号", required = true)
    private String tradeNo;

    /**
     * 用户ID
     */
    //@NotBlank(message = "用户ID不能为空")
    @ApiModelProperty(value = "用户ID", required = true)
    private String userId;

    /**
     * 客户编码
     */
    //@NotBlank(message = "客户编码不能为空")
    @ApiModelProperty(value = "客户编码", required = true)
    private String customerCode;

    /**
     * 支付金额（元）
     */
    @NotBlank(message = "交易金额不能为空")
    @Min(value = 0, message = "交易金额不能小于0")
    @ApiModelProperty(value = "交易金额", required = true)
    private BigDecimal amount;

    /**
     * 交易备注
     */
    private String remark;

    /**
     * 退款第三方交易单号
     */
    private String bizRefundNo;

    @ApiModelProperty(value = "用户角色，2店主/3店长")
    private String userRole;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;
}
