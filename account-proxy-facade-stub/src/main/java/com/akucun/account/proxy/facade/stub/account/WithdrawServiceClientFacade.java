package com.akucun.account.proxy.facade.stub.account;

import com.akucun.account.proxy.facade.stub.fallback.WithdrawServiceClientFacadeFallbackFactory;
import com.akucun.fps.account.client.model.WithdrawAuditDO;
import com.akucun.fps.account.client.model.query.WithdrawQueryDO;
import com.akucun.fps.common.entity.Query;
import com.akucun.fps.common.entity.Result;
import com.akucun.fps.common.entity.ResultList;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * @Author: huayanpei
 * @Date: 2020/12/31
 * @desc: 提现服务接口
 */
@FeignClient(value = "account-proxy", fallbackFactory = WithdrawServiceClientFacadeFallbackFactory.class)
@RequestMapping("/api/account/proxy/accountWeb")
public interface WithdrawServiceClientFacade {

    /**
     * 提现申请接口
     * @param withdrawAuditDO
     * @return
     */
    @PostMapping(value = "/applyWithdraw", produces = "application/json;charset=utf-8")
    Result<Void> applyWithdraw(@RequestBody WithdrawAuditDO withdrawAuditDO);


    /**
     * 查询提现记录
     * @param query
     * @return
     */
    @PostMapping(value = "/selectPage", produces = "application/json;charset=utf-8")
    ResultList<WithdrawAuditDO> selectPage(@RequestBody Query<WithdrawQueryDO> query);
}
