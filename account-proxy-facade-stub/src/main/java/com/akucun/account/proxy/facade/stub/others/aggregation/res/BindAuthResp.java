package com.akucun.account.proxy.facade.stub.others.aggregation.res;

import lombok.Data;

import java.io.Serializable;

/**
 * @Author: silei
 * @Date: 2020/9/30
 * @desc:
 */
@Data
public class BindAuthResp<T> implements Serializable {

    private static final long serialVersionUID = 2300768565733717469L;

    /**
     * 0：未进行小额鉴权或小额鉴权已结束，
     * 1：成功，
     * 2：失败
     * 3：处理中
     */
    private Integer isAuth;

    private String resStatus;

    private String resMsg;

    private T data;


}
