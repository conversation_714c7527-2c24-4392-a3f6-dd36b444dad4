package com.akucun.account.proxy.facade.stub.fallback;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.facade.stub.compensation.IntegrationApplyFacade;
import com.akucun.account.proxy.facade.stub.others.dto.req.CompensationFillPayApplyReq;
import feign.hystrix.FallbackFactory;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Create on : 2024/11/25 19:52
 **/
public class IntegrationApplyFacadeFallbackFactory  implements FallbackFactory<IntegrationApplyFacade> {
    @Override
    public IntegrationApplyFacade create(Throwable throwable) {
        return new IntegrationApplyFacade(){
            @Override
            public Result<Boolean> fillapply(CompensationFillPayApplyReq req) {
                return Result.error(500, "服务超时熔断");
            }
        };
    }
}
