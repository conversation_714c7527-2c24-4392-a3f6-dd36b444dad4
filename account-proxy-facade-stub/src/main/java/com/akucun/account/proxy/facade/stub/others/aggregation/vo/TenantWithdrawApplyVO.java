package com.akucun.account.proxy.facade.stub.others.aggregation.vo;

import com.aikucun.common2.utils.datamasking.Sensitive;
import com.aikucun.common2.utils.datamasking.SensitiveType;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: silei
 * @Date: 2021/4/20
 * @desc: 租户店主店长提现申请
 */
public class TenantWithdrawApplyVO implements Serializable {

    private static final long serialVersionUID = 1052008606004008988L;
    /**
     * 提现编号
     */
    private String withdrawNo;

    /**
     * 租户ID
     */
    private String tenantId;
    /**
     * 客户编码
     */
    private String customerCode;
    /**
     * 客户名称
     */
    @Sensitive(type = SensitiveType.Name)
    private String customerName;
    /**
     * 客户类型
     */
    private String customerType;
    /**
     * 来源单号
     */
    private String sourceBillNo;
    /**
     * 银行名称
     */
    private String bankName;
    /**
     * 银行卡号
     */
    @Sensitive(type = SensitiveType.BankCard)
    private String bankNo;
    /**
     * 提现金额
     */
    private BigDecimal amount;
    /**
     * 服务费
     */
    private BigDecimal serviceAmount;
    /**
     * 申请状态
     */
    private String applyStatus;
    /**
     * 提现失败原因
     */
    private String failReason;
    /**
     * 提现渠道
     */
    private String withdrawChannel;
    /**
     * 店铺id
     */
    private String shopId;

    /**
     * 店铺id
     */
    private String identifyNo;
    /**
     * 银行logo
     */
    private String bankLogo;
    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    public String getWithdrawNo() {
        return withdrawNo;
    }

    public void setWithdrawNo(String withdrawNo) {
        this.withdrawNo = withdrawNo;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getCustomerType() {
        return customerType;
    }

    public void setCustomerType(String customerType) {
        this.customerType = customerType;
    }

    public String getSourceBillNo() {
        return sourceBillNo;
    }

    public void setSourceBillNo(String sourceBillNo) {
        this.sourceBillNo = sourceBillNo;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getBankNo() {
        return bankNo;
    }

    public void setBankNo(String bankNo) {
        this.bankNo = bankNo;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getServiceAmount() {
        return serviceAmount;
    }

    public void setServiceAmount(BigDecimal serviceAmount) {
        this.serviceAmount = serviceAmount;
    }

    public String getApplyStatus() {
        return applyStatus;
    }

    public void setApplyStatus(String applyStatus) {
        this.applyStatus = applyStatus;
    }

    public String getFailReason() {
        return failReason;
    }

    public void setFailReason(String failReason) {
        this.failReason = failReason;
    }

    public String getWithdrawChannel() {
        return withdrawChannel;
    }

    public void setWithdrawChannel(String withdrawChannel) {
        this.withdrawChannel = withdrawChannel;
    }

    public String getShopId() {
        return shopId;
    }

    public void setShopId(String shopId) {
        this.shopId = shopId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getBankLogo() {
        return bankLogo;
    }

    public void setBankLogo(String bankLogo) {
        this.bankLogo = bankLogo;
    }

    public String getIdentifyNo() {
        return identifyNo;
    }

    public void setIdentifyNo(String identifyNo) {
        this.identifyNo = identifyNo;
    }

    @Override
    public String toString() {
        return "TenantWithdrawApplyVO{" +
                "withdrawNo='" + withdrawNo + '\'' +
                ", tenantId='" + tenantId + '\'' +
                ", customerCode='" + customerCode + '\'' +
                ", customerName='" + customerName + '\'' +
                ", customerType='" + customerType + '\'' +
                ", sourceBillNo='" + sourceBillNo + '\'' +
                ", bankName='" + bankName + '\'' +
                ", bankNo='" + bankNo + '\'' +
                ", amount=" + amount +
                ", serviceAmount=" + serviceAmount +
                ", applyStatus='" + applyStatus + '\'' +
                ", failReason='" + failReason + '\'' +
                ", withdrawChannel='" + withdrawChannel + '\'' +
                ", shopId='" + shopId + '\'' +
                ", identifyNo='" + identifyNo + '\'' +
                ", bankLogo='" + bankLogo + '\'' +
                ", remark='" + remark + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}
