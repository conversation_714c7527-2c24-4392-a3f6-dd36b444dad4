package com.akucun.account.proxy.facade.stub.others.aggregation.req;

import java.io.Serializable;

/**
 * 提现扣税汇总查询请求
 *
 * <AUTHOR>
 * @version [版本号, 2020年7月7日]
 */
public class WithdrawTaxSummaryReq implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 9161677790928825008L;

    /**
     * 客户姓名
     */
    private String customerName;

    /**
     * 客户编码：非空
     */
    private String customerCode;

    /**
     * 唯一识别码
     */
    private String identifyNo;

    /**
     * 身份类别：PERSON-个人
     */
    private String identifyCategory;

    /**
     * 开始月份：yyyyMMdd，非空
     */
    private String startMonth;

    /**
     * 结束月份：yyyyMMdd，非空
     */
    private String endMonth;

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getStartMonth() {
        return startMonth;
    }

    public void setStartMonth(String startMonth) {
        this.startMonth = startMonth;
    }

    public String getEndMonth() {
        return endMonth;
    }

    public void setEndMonth(String endMonth) {
        this.endMonth = endMonth;
    }

    public String getIdentifyCategory() {
        return identifyCategory;
    }

    public void setIdentifyCategory(String identifyCategory) {
        this.identifyCategory = identifyCategory;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getIdentifyNo() {
        return identifyNo;
    }

    public void setIdentifyNo(String identifyNo) {
        this.identifyNo = identifyNo;
    }
}
