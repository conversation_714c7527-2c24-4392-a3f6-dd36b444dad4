package com.akucun.account.proxy.facade.stub.fallback;

import com.akucun.account.proxy.facade.stub.account.WithdrawServiceClientFacade;
import com.akucun.fps.account.client.model.WithdrawAuditDO;
import com.akucun.fps.account.client.model.query.WithdrawQueryDO;
import com.akucun.fps.common.entity.Query;
import com.akucun.fps.common.entity.Result;
import com.akucun.fps.common.entity.ResultList;
import com.akucun.fps.pingan.client.vo.PinganCardVO;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class WithdrawServiceClientFacadeFallbackFactory implements FallbackFactory<WithdrawServiceClientFacade> {
    @Override
    public WithdrawServiceClientFacade create(Throwable throwable) {
        return new WithdrawServiceClientFacade() {
            @Override
            public Result<Void> applyWithdraw(WithdrawAuditDO withdrawAuditDO) {
                Result<Void> result = new Result<>();
                result.setError(500, "SERVICE_ERROR", "服务超时熔断");
                return result;
            }

            @Override
            public ResultList<WithdrawAuditDO> selectPage(Query<WithdrawQueryDO> query) {
                ResultList<WithdrawAuditDO> resultList = new ResultList<>();
                resultList.setError(500, "SERVICE_ERROR", "服务超时熔断");
                return resultList;
            }
        };
    }
}
