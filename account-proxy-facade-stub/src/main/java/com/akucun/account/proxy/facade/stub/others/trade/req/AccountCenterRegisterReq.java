package com.akucun.account.proxy.facade.stub.others.trade.req;

import java.io.Serializable;

import lombok.Data;

/**
 * 企业饷店店长、店主的账户中心注册
 * <AUTHOR>
 *
 */
@Data
public class AccountCenterRegisterReq implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	/**
	 * 1.【三方小程序】， 2.【企业饷店】，3.【SASS标准类型】
	 */
	public Integer tenantType;
	
	/**
	 * 用户类型:NM【店主】，NMDL【店长】，AT【SaaS租户】
	 */
	private String customerType;
	
	/**
	 * 账户类型:XD_NM【饷店店主】，XD_NMDL【饷店店长】，AT【租户】
	 */
	private String accountType;
	
	/**
	 * 租户id
	 */
	private String tenantId;
	
	/**
	 * 用户编号
	 */
	private String customerCode;
	
	/**
	 * 用户姓名
	 */
	private String customerName;

}
