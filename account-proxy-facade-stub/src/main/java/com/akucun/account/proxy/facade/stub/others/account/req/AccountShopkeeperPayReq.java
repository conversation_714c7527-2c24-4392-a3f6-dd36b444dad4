package com.akucun.account.proxy.facade.stub.others.account.req;

import com.mengxiang.base.common.utils.datamasking.Sensitive;
import com.mengxiang.base.common.utils.datamasking.SensitiveType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: silei
 * @Date: 2021/1/7
 * @desc:
 */
@ApiModel("店主二维码付款")
@ToString
public class AccountShopkeeperPayReq implements Serializable {

    private static final long serialVersionUID = 7834249945673174204L;

    @ApiModelProperty("账户类型key")
    private String accountTypeKey;

    @ApiModelProperty("客户编码")
    private String customerCode;

    @ApiModelProperty("客户名称")
    @Sensitive(type = SensitiveType.Name)
    private String customerName;

    @ApiModelProperty("发生金额，非负")
    private BigDecimal amount;

    @ApiModelProperty("业务单号，如订单号")
    private String sourceBillNo;

    @ApiModelProperty("交易类型")
    private String tradeType;

    @ApiModelProperty("交易流水号(支付、退款编码)")
    private String tradeNo;

    @ApiModelProperty("审核人")
    private String surePeople;

    @ApiModelProperty("审核状态")
    private String sureStatus;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("创建时间")
    private Date createDate;

    @ApiModelProperty("修改时间")
    private Date modifyDate;

    @ApiModelProperty("删除标识")
    private Integer isDelete;

    public String getAccountTypeKey() {
        return accountTypeKey;
    }

    public void setAccountTypeKey(String accountTypeKey) {
        this.accountTypeKey = accountTypeKey;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getSourceBillNo() {
        return sourceBillNo;
    }

    public void setSourceBillNo(String sourceBillNo) {
        this.sourceBillNo = sourceBillNo;
    }

    public String getTradeType() {
        return tradeType;
    }

    public void setTradeType(String tradeType) {
        this.tradeType = tradeType;
    }

    public String getTradeNo() {
        return tradeNo;
    }

    public void setTradeNo(String tradeNo) {
        this.tradeNo = tradeNo;
    }

    public String getSurePeople() {
        return surePeople;
    }

    public void setSurePeople(String surePeople) {
        this.surePeople = surePeople;
    }

    public String getSureStatus() {
        return sureStatus;
    }

    public void setSureStatus(String sureStatus) {
        this.sureStatus = sureStatus;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }
}
