package com.akucun.account.proxy.facade.stub.others.aggregation.req;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 提现税费试算请求
 *
 * <AUTHOR>
 */
public class WithdrawTaxCalcReq implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 670941245916954194L;

    /**
     * 客户编码
     */
    private String customerCode;
    /**
     * 客户类型
     */
    private String customerType;
    /**
     * //提现金额
     */
    private BigDecimal amount;
    /**
     * 身份证
     */
    private String identifyNo;

    /**
     * 会员身份等级, 0-个人，2-个体工商户，3-企业
     */
    private Integer memberGrade;

    /**
     * 提现类型：WECHAT,BANK
     */
    private String withdrawType;

    /**
     * 渠道类型：XD-饷店，SAAS-企业饷店，空值默认为XD
     */
    private String channelCode;

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getCustomerType() {
        return customerType;
    }

    public void setCustomerType(String customerType) {
        this.customerType = customerType;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getIdentifyNo() {
        return identifyNo;
    }

    public void setIdentifyNo(String identifyNo) {
        this.identifyNo = identifyNo;
    }

    public Integer getMemberGrade() {
        return memberGrade;
    }

    public void setMemberGrade(Integer memberGrade) {
        this.memberGrade = memberGrade;
    }

    public String getWithdrawType() {
        return withdrawType;
    }

    public void setWithdrawType(String withdrawType) {
        this.withdrawType = withdrawType;
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }
}
