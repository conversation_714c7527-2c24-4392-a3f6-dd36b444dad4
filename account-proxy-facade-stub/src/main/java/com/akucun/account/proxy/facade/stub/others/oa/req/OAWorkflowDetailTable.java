package com.akucun.account.proxy.facade.stub.others.oa.req;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Create on : 2025/1/14 16:10
 **/
@Data
public class OAWorkflowDetailTable {

    //第几张明细表
    @NotBlank(message = "明细表ID不能为空")
    private String detailTableId;

    //明细表字段
    private List<List<OAWorkflowTableField>> fields;

}
