package com.akucun.account.proxy.facade.stub.others.dto.req;

import com.mengxiang.base.common.utils.datamasking.Sensitive;
import com.mengxiang.base.common.utils.datamasking.SensitiveType;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: silei
 * @Date: 2021/9/8
 * @desc:
 */
public class WechatWithdrawReq implements Serializable {

    private static final long serialVersionUID = -1778218312687914667L;

    @ApiModelProperty("业务方来源编号")
    @NotBlank(message = "业务方来源编号不能为空")
    private String sourceCode;

    @ApiModelProperty("用户渠道编码 H5-H5，XDAPPLET-小程序，3RD_MP-菜菜小程序")
    @NotBlank(message = "用户渠道编码不能为空")
    private String channelCode;

    @ApiModelProperty("用户openId")
    @NotBlank(message = "用户openId不能为空")
    private String openId;

    @ApiModelProperty("付款金额")
    @NotNull(message = "付款金额不能为空")
    private BigDecimal amount;

    @ApiModelProperty("业务方付款单号")
    @NotBlank(message = "业务方付款单号不能为空")
    private String withdrawNo;

    @ApiModelProperty("付款人客户编号")
    @NotBlank(message = "付款人客户编号不能为空")
    private String customerCode;

    @ApiModelProperty("付款人类型")
    private String customerType;

    @ApiModelProperty("付款人姓名")
    @Sensitive(type = SensitiveType.Name)
    private String customerName;

    @ApiModelProperty("付款备注")
    @NotBlank(message = "付款备注不能为空")
    private String remark;

    @ApiModelProperty("租户ID")
    private String tenantId;

    @ApiModelProperty("批次号")
    private String batchNo;

    public String getSourceCode() {
        return sourceCode;
    }

    public void setSourceCode(String sourceCode) {
        this.sourceCode = sourceCode;
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getWithdrawNo() {
        return withdrawNo;
    }

    public void setWithdrawNo(String withdrawNo) {
        this.withdrawNo = withdrawNo;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getCustomerType() {
        return customerType;
    }

    public void setCustomerType(String customerType) {
        this.customerType = customerType;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }
}
