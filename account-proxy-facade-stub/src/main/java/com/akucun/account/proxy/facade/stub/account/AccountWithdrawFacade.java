package com.akucun.account.proxy.facade.stub.account;

import com.aikucun.common2.base.Result;
import com.aikucun.common2.base.exception.BusinessException;
import com.akucun.account.proxy.facade.stub.fallback.AccountWithdrawFacadeFallbackFactory;
import com.akucun.account.proxy.facade.stub.others.account.req.ShopkeeperIncentiveAwardWithdrawCalcTaxReq;
import com.akucun.account.proxy.facade.stub.others.account.vo.AccountTotalVO;
import com.akucun.account.proxy.facade.stub.others.account.vo.AccountWithdrawVO;
import com.akucun.account.proxy.facade.stub.others.account.vo.NotifyWithdrawVO;
import com.akucun.account.proxy.facade.stub.others.account.vo.WithdrawTaxDetailVO;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @Author: silei
 * @Date: 2021/3/5
 * @desc:
 */
@FeignClient(value = "account-proxy", fallbackFactory = AccountWithdrawFacadeFallbackFactory.class/**,url="127.0.0.1:8083"**/)
@RequestMapping("/api/account/proxy/withdraw")
public interface AccountWithdrawFacade {

    /**
     * 平安银行提现申请接口
     * @param accountWithdrawVO
     * @return
     * @throws BusinessException
     */
    @PostMapping(value = "/applyWithdraw", produces = "application/json;charset=utf-8")
    Result<String> applyWithdraw(@RequestBody AccountWithdrawVO accountWithdrawVO);

    /**
     * 提现结果通知接口
     * @RequestBody
     * @param notifyWithdrawVO
     * @return
     */
    @PostMapping(value = "/notifyWithdrawResp", produces = "application/json;charset=utf-8")
    Result<Void> notifyWithdrawResp(@RequestBody NotifyWithdrawVO notifyWithdrawVO);


    /**
     * 查询账户汇总信息
     * @param accountTotalVO
     * @return
     */
    @PostMapping(value = "/selectAccountTotal", produces = "application/json;charset=utf-8")
    Result<AccountTotalVO> selectAccountTotal(@RequestBody AccountTotalVO accountTotalVO);

    /**
     * 申请提现到微信接口
     * @param accountWithdrawVO
     * @return
     * @throws BusinessException
     */
    @PostMapping(value = "/applyWechatWithdraw", produces = "application/json;charset=utf-8")
    Result<String> applyWechatWithdraw(@RequestBody AccountWithdrawVO accountWithdrawVO);

    /**
     * 店主奖励自动提现计税提交接口
     * @param req
     * @return
     */
    @PostMapping(value = "/shopkeeperIncentiveAwardWithdrawCalcTax", produces = "application/json;charset=utf-8")
    Result<WithdrawTaxDetailVO> shopkeeperIncentiveAwardWithdrawCalcTax(@RequestBody ShopkeeperIncentiveAwardWithdrawCalcTaxReq req);

    /**
     * 添加提现记录
     *
     * @param accountWithdrawVO
     * @return
     */
    @PostMapping(value = "/addWithdrawRecord", produces = "application/json;charset=utf-8")
    Result<String> addWithdrawRecord(@RequestBody AccountWithdrawVO accountWithdrawVO);

    /**
     * 获取提现回单下载地址
     * @param withdrawNo
     * @return
     */
    @GetMapping(value = "/getWithdrawReceiptDownloadUrl")
    Result<String> getWithdrawReceiptDownloadUrl(@RequestParam("withdrawNo") String withdrawNo);

}
