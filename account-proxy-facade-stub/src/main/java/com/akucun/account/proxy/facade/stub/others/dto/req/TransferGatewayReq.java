package com.akucun.account.proxy.facade.stub.others.dto.req;

import java.io.Serializable;

/**
 * @Author: silei
 * @Date: 2021/7/16
 * @desc:
 */
public class TransferGatewayReq implements Serializable {

    private static final long serialVersionUID = -3867587785381159921L;
    /**
     * 通道网关编号
     */
    private String gatewayCode;

    /**
     * 商户网关类型（ALIPAY,WECHAT）
     */
    private String gatewayType;

    /**
     * 商户号
     */
    private String mchCode;

    /**
     * 公司主体
     */
    private String companyName;

    /**
     * 网关信息
     */
    private String gatewayInfo;

    /**
     * 证书1
     */
    private String certificate1;

    /**
     * 证书2
     */
    private String certificate2;

    /**
     * 证书3
     */
    private String certificate3;

    /**
     * 证书4
     */
    private String certificate4;

    /**
     * 证书5
     */
    private String certificate5;

    /**
     * 证书过期时间
     */
    private String certExpireTime;

    /**
     * 状态 0 有效，1无效
     */
    private Long status;

    /**
     * 备注
     */
    private String remark;

    public String getGatewayCode() {
        return gatewayCode;
    }

    public void setGatewayCode(String gatewayCode) {
        this.gatewayCode = gatewayCode;
    }

    public String getGatewayType() {
        return gatewayType;
    }

    public void setGatewayType(String gatewayType) {
        this.gatewayType = gatewayType;
    }

    public String getMchCode() {
        return mchCode;
    }

    public void setMchCode(String mchCode) {
        this.mchCode = mchCode;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getGatewayInfo() {
        return gatewayInfo;
    }

    public void setGatewayInfo(String gatewayInfo) {
        this.gatewayInfo = gatewayInfo;
    }

    public String getCertificate1() {
        return certificate1;
    }

    public void setCertificate1(String certificate1) {
        this.certificate1 = certificate1;
    }

    public String getCertificate2() {
        return certificate2;
    }

    public void setCertificate2(String certificate2) {
        this.certificate2 = certificate2;
    }

    public String getCertificate3() {
        return certificate3;
    }

    public void setCertificate3(String certificate3) {
        this.certificate3 = certificate3;
    }

    public String getCertificate4() {
        return certificate4;
    }

    public void setCertificate4(String certificate4) {
        this.certificate4 = certificate4;
    }

    public String getCertificate5() {
        return certificate5;
    }

    public void setCertificate5(String certificate5) {
        this.certificate5 = certificate5;
    }

    public String getCertExpireTime() {
        return certExpireTime;
    }

    public void setCertExpireTime(String certExpireTime) {
        this.certExpireTime = certExpireTime;
    }

    public Long getStatus() {
        return status;
    }

    public void setStatus(Long status) {
        this.status = status;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public String toString() {
        return "TransferGatewayReq{" +
                "gatewayCode='" + gatewayCode + '\'' +
                ", gatewayType='" + gatewayType + '\'' +
                ", mchCode='" + mchCode + '\'' +
                ", companyName='" + companyName + '\'' +
                ", gatewayInfo='" + gatewayInfo + '\'' +
                ", certificate1='" + certificate1 + '\'' +
                ", certificate2='" + certificate2 + '\'' +
                ", certificate3='" + certificate3 + '\'' +
                ", certificate4='" + certificate4 + '\'' +
                ", certificate5='" + certificate5 + '\'' +
                ", certExpireTime='" + certExpireTime + '\'' +
                ", status=" + status +
                ", remark='" + remark + '\'' +
                '}';
    }
}
