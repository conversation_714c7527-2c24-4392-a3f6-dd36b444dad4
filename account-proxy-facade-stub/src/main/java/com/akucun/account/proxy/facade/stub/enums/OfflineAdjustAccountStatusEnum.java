/*
 * @Author: <PERSON>
 * @Date: 2025-04-16 17:54:50
 * @Description: 线下调账状态枚举
 * @License: Copyright (c) 2025, Lee
 */
package com.akucun.account.proxy.facade.stub.enums;

public enum OfflineAdjustAccountStatusEnum {
    /**
     * 待调账
     */
    WAIT_ADJUST(0),
    /**
     * 调账成功
     */
    ADJUST_SUCCESS(1),
    /**
     * 调账失败
     */
    ADJUST_FAILED(2);

    private int code;

    OfflineAdjustAccountStatusEnum(int code) {
        this.code = code;
    }

    public int getCode() {
        return code;
    }
}
