package com.akucun.account.proxy.facade.stub.others.dto.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : 奖励发放请求参数
 * @Create on : 2025/1/7 11:34
 **/
@Data
@NoArgsConstructor
public class BonusPayReq {
    @ApiModelProperty("业务流水号，业务唯一标识")
    @NotBlank(message = "业务流水号不能为空")
    private String tradeNo;

    @ApiModelProperty("业务类型:EMPOWERMENT_CAMP 赋能营 MONTHLY_BONUS 月勤奖 MENTOR_BONUS 导师激励")
    @NotBlank(message = "业务类型不能为空")
    private String bizType;

    @ApiModelProperty("业务发生账期：YYYYMM,固定格式")
    @NotBlank(message = "业务发生账期不能为空")
    private String transBillDate;

    @ApiModelProperty("下发金额，单位元，保持2为小数")
    @NotNull(message = "下发金额不能为空")
    private BigDecimal amount;

    @ApiModelProperty("业务发生时间")
    @NotNull(message = "业务发生时间不能为空")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date transTime;

    @ApiModelProperty("用户code")
    @NotBlank(message = "店主的爱豆编号不能为空")
    private String userCode;

    @ApiModelProperty("用户类型，不填默认NM")
    private String userType;

    @ApiModelProperty("认证类型，以会员提供的枚举值为准（-2 个人临登,-1:个人店铺声明,0：个人身份证认证，1:个体临时认证，2：个体自有渠道认证，3:企业认证）")
    @NotNull(message = "认证类型不能为空")
    private Integer userGrade;

    @ApiModelProperty("认证渠道，以会员提供的枚举值为准（认证渠道，以会员提供的枚举值为准）")
    private Integer gradeChannel;

    @ApiModelProperty("关联的业务单号")
    private String sourceBizNo;

    @ApiModelProperty("所属活动编码")
    private String activityNo;

    @ApiModelProperty("电子发票的OBS地址，长期有效")
    private String invoiceUrl;

    @ApiModelProperty("前段展示的业务描述，以产品定义的为准")
    //@NotBlank(message = "备注不能为空")
    private String remark;

    @ApiModelProperty("扩展字段1")
    private HashMap<String,String> ext1;

    //@ApiModelProperty("扩展字段2")
    //private HashMap<String,String> ext2;

    @ApiModelProperty("异步回调地址")
    private String notifyUrl;

    @ApiModelProperty("调用的系统来源:eg-营销=promo")
    private String systemId;
}
