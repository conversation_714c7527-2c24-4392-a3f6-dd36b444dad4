package com.akucun.account.proxy.facade.stub.others.aggregation.req;

import com.mengxiang.base.common.utils.datamasking.Sensitive;
import com.mengxiang.base.common.utils.datamasking.SensitiveType;
import lombok.Data;
import lombok.ToString;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;

/**
 * @Author: silei
 * @Date: 2020/9/30
 * @desc:
 */
@Data
@ToString
public class BindCardVerifyDTO implements Serializable {

    private static final long serialVersionUID = 8984831832454834701L;

    @NotBlank(message = "客户编码不能为空")
    private String customerCode;
    @NotBlank(message = "客户类型不能为空")
    private String customerType;

    private String tranAmount;
    @Sensitive(type = SensitiveType.BankCard)
    private String cardNo;
    @Sensitive(type = SensitiveType.Phone)
    private String mobile;

}
