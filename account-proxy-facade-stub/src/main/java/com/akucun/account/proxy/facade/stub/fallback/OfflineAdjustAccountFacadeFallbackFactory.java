/*
 * @Author: Lee
 * @Date: 2025-04-17 11:19:37
 * @Description: Do not edit
 * @License: Copyright (c) 2025, Lee
 */
package com.akucun.account.proxy.facade.stub.fallback;

import org.springframework.stereotype.Component;

import com.akucun.account.proxy.facade.stub.account.OfflineAdjustAccountFacade;
import com.akucun.account.proxy.facade.stub.others.account.req.OfflineAdjustAccountBatchRequest;
import com.akucun.account.proxy.facade.stub.others.account.req.OfflineAdjustAccountPageQueryReq;
import com.akucun.account.proxy.facade.stub.others.account.req.OfflineAdjustAccountSingleRequest;
import com.akucun.account.proxy.facade.stub.others.account.req.OfflineAdjustAccountSubmitReq;
import com.akucun.account.proxy.facade.stub.others.account.vo.OfflineAdjustAccountBatchStatisticVO;
import com.akucun.account.proxy.facade.stub.others.account.vo.OfflineAdjustAccountVO;
import com.mengxiang.base.common.model.result.Pagination;
import com.mengxiang.base.common.model.result.Result;

import feign.hystrix.FallbackFactory;

@Component
public class OfflineAdjustAccountFacadeFallbackFactory implements FallbackFactory<OfflineAdjustAccountFacade> {

    @Override
    public OfflineAdjustAccountFacade create(Throwable cause) {
        return new OfflineAdjustAccountFacade() {
            @Override
            public Result<Void> submit(OfflineAdjustAccountSubmitReq addVO) {
                return Result.error("服务超时熔断");
            }

            @Override
            public Result<Pagination<OfflineAdjustAccountVO>> pageQuery(OfflineAdjustAccountPageQueryReq queryReq) {
                return Result.error("服务超时熔断");
            }

            @Override
            public Result<Void> auditPass(OfflineAdjustAccountSingleRequest request) {
                return Result.error("服务超时熔断");
            }

            @Override
            public Result<Void> auditRefuse(OfflineAdjustAccountSingleRequest request) {
                return Result.error("服务超时熔断");
            }

            @Override
            public Result<Integer> batchDelete(OfflineAdjustAccountBatchRequest request) {
                return Result.error("服务超时熔断");
            }

            @Override
            public Result<Void> onKeyDelete(OfflineAdjustAccountPageQueryReq queryReq, String operator) {
                return Result.error("服务超时熔断");
            }

            @Override
            public Result<OfflineAdjustAccountBatchStatisticVO> statistic(OfflineAdjustAccountPageQueryReq queryReq) {
                return Result.error("服务超时熔断");
            }

            @Override
            public Result<Void> retry(OfflineAdjustAccountSingleRequest request) {
                return Result.error("服务超时熔断");
            }
        };
    }
}
