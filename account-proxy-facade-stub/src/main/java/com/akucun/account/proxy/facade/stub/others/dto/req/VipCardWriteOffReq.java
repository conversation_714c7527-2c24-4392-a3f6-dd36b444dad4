package com.akucun.account.proxy.facade.stub.others.dto.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import javax.validation.constraints.NotBlank;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/01/03 13:57
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("vip卡信息查询请求实体")
public class VipCardWriteOffReq implements Serializable {

    private static final long serialVersionUID = 7879325652032555134L;
    @NotBlank(message = "卡密或卡号不能为空")
    @ApiModelProperty(value = "卡密（卡号）", required = true)
    private String cardNo;

    @NotBlank(message = "邀请码不能为空")
    @ApiModelProperty(value = "邀请码（卡密）", required = true)
    private String inviteCode;

    @NotBlank(message = "爱豆编号不能为空")
    @ApiModelProperty(value = "爱豆编号", required = true)
    private String customerCode;

}
