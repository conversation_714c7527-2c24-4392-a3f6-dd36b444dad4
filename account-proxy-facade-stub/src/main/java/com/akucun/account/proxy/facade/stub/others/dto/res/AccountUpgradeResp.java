package com.akucun.account.proxy.facade.stub.others.dto.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

/**
 * @Author: silei
 * @Date: 2020/10/19
 * @desc:
 */
@ApiModel("账户升级状态实体")
@ToString
@NoArgsConstructor
public class AccountUpgradeResp implements Serializable {

    private static final long serialVersionUID = -1347899873556975883L;
    /**
     * 客户编码
     */
    @ApiModelProperty(value = "客户编码", required = true)
    private String customerCode;

    /**
     * 客户类型
     */
    @ApiModelProperty(value = "客户类型", required = true)
    private String customerType;

    /**
     * 升级类型
     */
    @ApiModelProperty(value = "升级类型", required = true)
    private String upgradeType;

    /**
     * 升级状态
     */
    @ApiModelProperty(value = "升级状态：S-成功，F-失败，P-处理中", required = true)
    private String upgradeStatus;

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getCustomerType() {
        return customerType;
    }

    public void setCustomerType(String customerType) {
        this.customerType = customerType;
    }

    public String getUpgradeType() {
        return upgradeType;
    }

    public void setUpgradeType(String upgradeType) {
        this.upgradeType = upgradeType;
    }

    public String getUpgradeStatus() {
        return upgradeStatus;
    }

    public void setUpgradeStatus(String upgradeStatus) {
        this.upgradeStatus = upgradeStatus;
    }
}
