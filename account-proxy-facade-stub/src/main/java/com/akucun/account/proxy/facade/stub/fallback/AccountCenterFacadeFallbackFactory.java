package com.akucun.account.proxy.facade.stub.fallback;

import com.akucun.account.center.client.model.*;
import com.akucun.account.center.client.model.account.AccountBookDO;
import com.akucun.account.center.client.model.account.AccountBookDetailDO;
import com.akucun.account.center.client.model.account.AccountDetailTotalInfoDO;
import com.akucun.account.center.client.model.query.AccountDetailQuery;
import com.akucun.account.center.client.model.query.AccountQuery;
import com.akucun.account.center.client.model.rsp.QueryTradeSumRsp;
import com.akucun.account.center.common.entity.Query;
import com.akucun.account.center.common.entity.ResultList;
import com.akucun.account.proxy.facade.stub.account.AccountCenterFacade;
import com.akucun.account.proxy.facade.stub.others.account.req.*;
import com.akucun.common.Result;
import com.akucun.member.api.vo.AccountRecordVO;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: silei
 * @Date: 2020/12/6
 * @desc:
 */
@Component
public class AccountCenterFacadeFallbackFactory implements FallbackFactory<AccountCenterFacade> {
    @Override
    public AccountCenterFacade create(Throwable throwable) {
        return new AccountCenterFacade() {
            @Override
            public Result<Void> dealAccount(AccountOperateInfoReq req) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<Void> dealTrade(TradeInfo tradeInfo) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<Void> transfer(TransferInfo transferInfo) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<AccountBookDO> queryAccount(AccountQuery accountQuery) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public ResultList<AccountBookDetailDO> queryAccountDetail(Query<AccountDetailQuery> query) {
                ResultList<AccountBookDetailDO> resultList = new ResultList<>();
                resultList.setError(500,"","服务超时熔断");
                return resultList;
            }

            @Override
            public Result<AccountDetailTotalInfoDO> queryAccountDetailTotalInfo(Query<AccountDetailQuery> query) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<AmountInfo> queryAccountDetailAmount(Query<AccountDetailQuery> query) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<String> getAdminSystem(String customerCode) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<Void> h5point2Award(H5PointTransferInfo h5PointTransferInfo) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<Boolean> buildWithdrawFlag(String customerCode, String customerType) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<Boolean> queryWithdrawFlag(String customerCode, String customerType) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<Void> unfreezeMerchantAccountAmount(AccountTradeInfo accountTradeInfo) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<Long> getAllBonus(String customerCode) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<Void> aytAwardTransfer(AYTTransferReq aytTransferInfo) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<Void> batchDealTrade(List<TradeInfo> tradeInfoList) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<List<QueryTradeSumRsp>> queryTradeSumForAyaTuan(List<QueryTradeSumRequest> tradeSumRequests) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<List<QueryTradeSumRsp>> queryTradeSum(List<TradeQueryRequest> tradeQueryRequestList) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<AccountBookDO> queryMemberBonusAccount(String userId) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<Void> memberBonusAccountDealTrade(MemberBonusAccountTradeInfo tradeInfo) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<List<AccountRecordVO>> checkAwardRecordByTradeNo(String customerCode, String tradeNo) {
                return Result.error(500, "服务超时熔断");
            }
        };
    }
}
