package com.akucun.account.proxy.facade.stub.fallback;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.facade.stub.account.AccountCheckFacade;
import com.akucun.account.proxy.facade.stub.others.dto.req.IsTenantCustomerReq;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * @Author: silei
 * @Date: 2021/3/5
 * @desc:
 */
@Component
public class AccountCheckFacadeFallbackFactory implements FallbackFactory<AccountCheckFacade> {
    @Override
    public AccountCheckFacade create(Throwable throwable) {
        return new AccountCheckFacade(){

            @Override
            public Result<Boolean> isTenantCustomer(IsTenantCustomerReq req) {
                return Result.error(500, "服务超时熔断");
            }

        };
    }
}
