package com.akucun.account.proxy.facade.stub.others.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : OA查询请求对象
 * @Create on : 2025/1/15 10:48
 **/
@Data
@NoArgsConstructor
public class BonusPayQueryReq {
    @ApiModelProperty("业务流水号，业务唯一标识")
    @NotBlank(message = "业务流水号不能为空")
    private String tradeNo;

    @ApiModelProperty("业务类型:EMPOWERMENT_CAMP 赋能营 MONTHLY_BONUS 月勤奖 MENTOR_BONUS 导师激励")
    @NotBlank(message = "业务类型不能为空")
    private String bizType;

    @ApiModelProperty("业务发生账期：YYYYMM,固定格式")
    private String transBillDate;

    @ApiModelProperty("所属活动编码")
    private String activityNo;
}
