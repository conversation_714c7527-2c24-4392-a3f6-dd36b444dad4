package com.akucun.account.proxy.facade.stub.others.account.vo;

import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;

/**
 * @Author: silei
 * @Date: 2021/12/20
 * @desc:
 */
public class PinganDecoupleAccountVO implements Serializable {

    private static final long serialVersionUID = -6768233521843228343L;

    /**
     * 客户编码
     */
    @NotBlank(message = "客户编码不能为空")
    private String customerCode;

    /**
     * 客户类型
     */
    @NotBlank(message = "客户类型不能为空")
    private String customerType;

    /**
     * 平安账户反清分状态：0 未反清分，1 已反清分
     */
    private Integer antiClearingStatus;

    /**
     * 是否白名单店长：0 否，1 是
     */
    private Integer whitelistShopAgentFlag;

    /**
     * 白名单店长处理状态：0 未处理，1 已处理
     */
    private Integer whitelistStatus;

    /**
     * 状态，0有效，1无效
     */
    private Integer status;

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getCustomerType() {
        return customerType;
    }

    public void setCustomerType(String customerType) {
        this.customerType = customerType;
    }

    public Integer getAntiClearingStatus() {
        return antiClearingStatus;
    }

    public void setAntiClearingStatus(Integer antiClearingStatus) {
        this.antiClearingStatus = antiClearingStatus;
    }

    public Integer getWhitelistShopAgentFlag() {
        return whitelistShopAgentFlag;
    }

    public void setWhitelistShopAgentFlag(Integer whitelistShopAgentFlag) {
        this.whitelistShopAgentFlag = whitelistShopAgentFlag;
    }

    public Integer getWhitelistStatus() {
        return whitelistStatus;
    }

    public void setWhitelistStatus(Integer whitelistStatus) {
        this.whitelistStatus = whitelistStatus;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return "PinganDecoupleAccountVO{" +
                "customerCode='" + customerCode + '\'' +
                ", customerType='" + customerType + '\'' +
                ", antiClearingStatus=" + antiClearingStatus +
                ", whitelistShopAgentFlag=" + whitelistShopAgentFlag +
                ", whitelistStatus=" + whitelistStatus +
                ", status=" + status +
                '}';
    }
}
