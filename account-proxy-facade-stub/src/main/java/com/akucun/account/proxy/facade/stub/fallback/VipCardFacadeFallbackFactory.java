package com.akucun.account.proxy.facade.stub.fallback;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.facade.stub.account.VipCardFacade;
import com.akucun.account.proxy.facade.stub.others.dto.req.VipCardInfoReq;
import com.akucun.account.proxy.facade.stub.others.dto.req.VipCardWriteOffReq;
import com.akucun.account.proxy.facade.stub.others.dto.res.VipCardInfoResp;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2021/01/03 13:51
 */
@Component
public class VipCardFacadeFallbackFactory implements FallbackFactory<VipCardFacade> {
    @Override
    public VipCardFacade create(Throwable throwable) {
        return new VipCardFacade() {
            @Override
            public Result<VipCardInfoResp> vipCardValidInfo(VipCardInfoReq vipCardInfoReq) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<Boolean> vipCardWriteOff(VipCardWriteOffReq vipCardWriteOffReq) {
                return Result.error(500, "服务超时熔断");
            }
        };
    }
}
