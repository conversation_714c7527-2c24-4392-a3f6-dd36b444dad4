package com.akucun.account.proxy.facade.stub.others.account.vo;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: silei
 * @Date: 2021/3/15
 * @desc: 账户汇总信息
 */
public class AccountTotalVO implements Serializable {

    private static final long serialVersionUID = 468823492539168914L;
    /**
     * 主键
     */
    private Long id;
    /**
     * 客户编码
     */
    private String customerCode;
    /**
     * 客户名称
     */
    private String customerName;
    /**
     * 客户类型
     */
    private String customerType;

    /**
     * 提现金额
     */
    private BigDecimal cashAmount;

    /**
     * 收入金额
     */
    private BigDecimal incomeAmount;

    /**
     * 备注
     */
    private String remark;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getCustomerType() {
        return customerType;
    }

    public void setCustomerType(String customerType) {
        this.customerType = customerType;
    }

    public BigDecimal getCashAmount() {
        return cashAmount;
    }

    public void setCashAmount(BigDecimal cashAmount) {
        this.cashAmount = cashAmount;
    }

    public BigDecimal getIncomeAmount() {
        return incomeAmount;
    }

    public void setIncomeAmount(BigDecimal incomeAmount) {
        this.incomeAmount = incomeAmount;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

}
