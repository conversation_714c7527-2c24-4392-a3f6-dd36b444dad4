package com.akucun.account.proxy.facade.stub.account;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.facade.stub.fallback.WechatAuthFacadeFallbackFactory;
import com.akucun.account.proxy.facade.stub.others.account.vo.WechatInfoBindVO;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * @Author: silei
 * @Date: 2021/5/1
 * @desc:
 */
@FeignClient(value = "account-proxy", fallbackFactory = WechatAuthFacadeFallbackFactory.class)
@RequestMapping("/api/account/proxy/wechat/auth")
public interface WechatAuthFacade {

    /**
     * 微信信息绑定
     *
     * @param vo
     * @return
     */
    @PostMapping("/bindInfo")
    Result<Void> bindInfo(@RequestBody WechatInfoBindVO vo);

    /**
     * 微信信息解绑
     *
     * @param customerCode
     * @param customerType
     * @return
     */
    @PostMapping("/unbind")
    Result<Void> unbind(@RequestParam("customerCode") String customerCode, @RequestParam("customerType") String customerType);

    /**
     * 微信绑定信息查询
     *
     * @param customerCode
     * @param customerType
     * @return
     */
    @GetMapping("/queryBindInfo")
    Result<WechatInfoBindVO> queryBindInfo(@RequestParam("customerCode") String customerCode, @RequestParam("customerType") String customerType);

}
