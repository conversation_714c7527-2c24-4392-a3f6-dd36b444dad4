package com.akucun.account.proxy.facade.stub.fallback;

import com.akucun.account.proxy.facade.stub.merchant.market.MerchantFullReturnMarketFundFacade;
import com.akucun.account.proxy.facade.stub.others.merchant.market.*;
import com.akucun.common.Result;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

/** 
 * @description MerchantFullReturnMarketFundFacadeFallbackFactory 
 * <AUTHOR>
 * @date 2024/4/26 17:02
 * @version v1.0.0
 */ 
@Component
public class MerchantFullReturnMarketFundFacadeFallbackFactory implements FallbackFactory<MerchantFullReturnMarketFundFacade> {
    @Override
    public MerchantFullReturnMarketFundFacade create(Throwable throwable) {
        return new MerchantFullReturnMarketFundFacade() {
            @Override
            public Result<Void> freeze(MerchantFullReturnMarketFreezeRequest request) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<Void> unFreeze(MerchantFullReturnMarketUnFreezeRequest request) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<Void> addFreeze(MerchantFullReturnMarketAddFreezeRequest request) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<Void> settle(MerchantFullReturnMarketSettleRequest request) {
                return Result.error(500, "服务超时熔断");
            }
        };
    }
}
