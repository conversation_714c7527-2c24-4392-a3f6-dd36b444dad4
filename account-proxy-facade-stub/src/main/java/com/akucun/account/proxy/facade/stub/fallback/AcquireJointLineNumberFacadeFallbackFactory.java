package com.akucun.account.proxy.facade.stub.fallback;

import com.akucun.account.proxy.facade.stub.pingan.AcquireJointLineNumberFacade;
import com.akucun.fps.common.entity.Result;
import com.akucun.fps.common.entity.ResultList;
import com.akucun.fps.pingan.client.model.*;
import com.akucun.fps.pingan.client.model.po.BankNode;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

@Component
public class AcquireJointLineNumberFacadeFallbackFactory implements FallbackFactory<AcquireJointLineNumberFacade> {
    @Override
    public AcquireJointLineNumberFacade create(Throwable throwable) {
        return new AcquireJointLineNumberFacade() {
            @Override
            public ResultList<BankInfoManageResp> selectByBankName(String bankName) {
                ResultList<BankInfoManageResp> resultList = new ResultList<BankInfoManageResp>();
                resultList.setError(500, "SERVICE_ERROR", "服务超时熔断");
                return resultList;
            }

            @Override
            public ResultList<BankInfoManageResp> selectBankInfoManage(BankInfoManageReq req) {
                ResultList<BankInfoManageResp> resultList = new ResultList<BankInfoManageResp>();
                resultList.setError(500, "SERVICE_ERROR", "服务超时熔断");
                return resultList;
            }

            @Override
            public Result<BankInfoManageResp> selectByName(String bankName) {
                Result<BankInfoManageResp> result = new Result<BankInfoManageResp>();
                result.setError(500, "SERVICE_ERROR", "服务超时熔断");
                return result;
            }

            @Override
            public Result<BankInfoManageResp> selectByBankCode(String bankCode) {
                Result<BankInfoManageResp> result = new Result<BankInfoManageResp>();
                result.setError(500, "SERVICE_ERROR", "服务超时熔断");
                return result;
            }

            @Override
            public ResultList<BankNode> selectProvince() {
                ResultList<BankNode> resultList = new ResultList<BankNode>();
                resultList.setError(500, "SERVICE_ERROR", "服务超时熔断");
                return resultList;
            }

            @Override
            public ResultList<BankCityInfo> selectBankCity(String code) {
                ResultList<BankCityInfo> resultList = new ResultList<BankCityInfo>();
                resultList.setError(500, "SERVICE_ERROR", "服务超时熔断");
                return resultList;
            }

            @Override
            public ResultList<SubBankInfoDO> selectSubBankCityPage(SubBankInfoReq request) {
                ResultList<SubBankInfoDO> resultList = new ResultList<SubBankInfoDO>();
                resultList.setError(500, "SERVICE_ERROR", "服务超时熔断");
                return resultList;
            }

            @Override
            public ResultList<SubBankInfoDO> selectSpecialSubBankCityPage(SubBankInfoReq request) {
                ResultList<SubBankInfoDO> resultList = new ResultList<SubBankInfoDO>();
                resultList.setError(500, "SERVICE_ERROR", "服务超时熔断");
                return resultList;
            }

            @Override
            public ResultList<BankInfoManageResp> selectBankInfoManageByChannel(String channel) {
                ResultList<BankInfoManageResp> resultList = new ResultList<BankInfoManageResp>();
                resultList.setError(500, "SERVICE_ERROR", "服务超时熔断");
                return resultList;
            }
        };
    }
}
