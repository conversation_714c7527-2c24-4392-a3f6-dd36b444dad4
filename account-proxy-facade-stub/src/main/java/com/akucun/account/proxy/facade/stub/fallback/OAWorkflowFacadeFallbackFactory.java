package com.akucun.account.proxy.facade.stub.fallback;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.facade.stub.oa.OAWorkflowFacade;
import com.akucun.account.proxy.facade.stub.others.oa.req.OAWorkflowCreateRequest;
import com.akucun.account.proxy.facade.stub.others.oa.req.OAWorkflowQueryRequest;
import com.akucun.account.proxy.facade.stub.others.oa.resp.OAWorkflowCreateResponse;
import com.akucun.account.proxy.facade.stub.others.oa.resp.OAWorkflowResponseInfo;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Create on : 2025/1/14 17:28
 **/
@Component
public class OAWorkflowFacadeFallbackFactory implements FallbackFactory<OAWorkflowFacade> {
    @Override
    public OAWorkflowFacade create(Throwable cause) {
        return new OAWorkflowFacade() {
            @Override
            public Result<OAWorkflowCreateResponse> create(OAWorkflowCreateRequest request) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<OAWorkflowResponseInfo> queryWorkflowByRequestId(OAWorkflowQueryRequest request) {
                return Result.error(500, "服务超时熔断");
            }
        };
    }
}
