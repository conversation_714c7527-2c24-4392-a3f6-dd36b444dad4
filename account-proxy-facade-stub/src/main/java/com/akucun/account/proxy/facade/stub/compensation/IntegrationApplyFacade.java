package com.akucun.account.proxy.facade.stub.compensation;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.facade.stub.fallback.IntegrationApplyFacadeFallbackFactory;
import com.akucun.account.proxy.facade.stub.others.dto.req.CompensationFillPayApplyReq;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : 统一申请接口
 * @Create on : 2024/11/25 19:50
 **/
@FeignClient(value = "account-proxy", fallbackFactory = IntegrationApplyFacadeFallbackFactory.class)
@RequestMapping("/api/account/proxy/supply/tenant/compensation")
public interface IntegrationApplyFacade {
    /**
     * 补款申请
     * @param
     * @return
     */
    @PostMapping("/fillapply")
    Result<Boolean> fillapply(@RequestBody @Validated CompensationFillPayApplyReq req);
}
