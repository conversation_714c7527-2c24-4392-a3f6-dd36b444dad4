package com.akucun.account.proxy.facade.stub.others.dto.res;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : OA异步通知
 * @Create on : 2025/1/15 10:44
 **/
@Data
@NoArgsConstructor
public class OANotifyDTO {

    @ApiModelProperty("业务流水号，业务唯一标识")
    private String tradeNo;

    @ApiModelProperty("流程请求ID")
    private String requestId;

    @ApiModelProperty("业务类型:EMPOWERMENT_CAMP 赋能营 MONTHLY_BONUS 月勤奖")
    private String bizType;

    @ApiModelProperty("业务发生账期：YYYYMM,固定格式")
    private String transBillDate;

    @ApiModelProperty("业务发生时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date transTime;

    @ApiModelProperty("所属活动编码")
    private String activityNo;

    @ApiModelProperty("OA工作流id")
    private String workflowId;

    @ApiModelProperty("OA工作流最新状态")
    private String status;

    @ApiModelProperty("OA工作流最新状态描述")
    private String statusDesc;

    @ApiModelProperty("OA工作流异常中断的描述")
    private String errorMsg;

    //============ 额外透传信息 =============
    @ApiModelProperty("用户类型，默认NM")
    private String userType;

    @ApiModelProperty("用户code")
    private String userCode;
}
