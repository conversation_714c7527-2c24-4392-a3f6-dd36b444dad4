package com.akucun.account.proxy.facade.stub.others.oa.req;

import com.akucun.account.proxy.facade.stub.enums.OAWorkflowBusinessTypeEnum;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Create on : 2025/1/14 16:10
 **/
@Data
public class OAWorkflowCreateRequest {

    @NotBlank(message = "业务编号不能为空")
    private String bizNo;

    @NotNull(message = "业务类型不能为空")
    private OAWorkflowBusinessTypeEnum businessType;

    //流程id
    @NotBlank(message = "流程id不能为空")
    private String workflowId;

    //流程标题
    @NotBlank(message = "流程标题不能为空")
    private String workflowTitle;

    //请求级别，0：正常 1：重要 2：紧急
    @NotBlank(message = "请求级别不能为空")
    private String requestLevel = "0";

    //创建人：工号
    @NotBlank(message = "创建人不能为空")
    private String creator;

    //0 创建后流程不流转,停留在创建节点 ,为空流转下一节点
    @NotNull(message = "是否流转下一节点不能为空")
    private String isNextFlow = "";

    //主表属性
    @Valid
    private List<OAWorkflowTableField> mainFields;

    //明细表属性
    @Valid
    private List<OAWorkflowDetailTable> detailTables;

    //流程状态回调地址
    private String notifyUrl;

    //是否需要轮询回调
    public boolean polling() {
        return StringUtils.isNotBlank(notifyUrl);
    }

}
