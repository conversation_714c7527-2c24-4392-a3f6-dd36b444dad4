package com.akucun.account.proxy.facade.stub.account;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.facade.stub.fallback.AccountFacadeFallbackFactory;
import com.akucun.account.proxy.facade.stub.others.dto.req.AccountInfoReq;
import com.akucun.account.proxy.facade.stub.others.dto.req.AccountUpgradePreCheckRequest;
import com.akucun.account.proxy.facade.stub.others.dto.req.AccountUpgradeRequest;
import com.akucun.account.proxy.facade.stub.others.dto.res.AccountInfoResp;
import com.akucun.account.proxy.facade.stub.others.dto.res.AccountUpgradeResp;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 账户升级接口（会员在调用）
 * <AUTHOR>
 */
@FeignClient(value = "account-proxy", fallbackFactory = AccountFacadeFallbackFactory.class)
@RequestMapping("/api/account/proxy")
public interface AccountFacade {

    /**
     * 实名变更预校验
     * @param request
     * @return
     */
    @PostMapping(value = "/upgradePreCheck")
    Result<Void> upgradePreCheck(@RequestBody AccountUpgradePreCheckRequest request);

    /**
     * 平安账户实名变更
     *
     * @param accountUpgradeRequest
     * @return
     */
    @PostMapping(value = "/upgrade")
    Result<Void> accountUpgrade(@RequestBody AccountUpgradeRequest accountUpgradeRequest);

    /**
     * 查询账户升级状态
     *
     * @param list
     * @return
     */
    @PostMapping("/queryUpgradeStatus")
    Result<List<AccountUpgradeResp>> queryUpgradeStatus(@RequestBody List<AccountUpgradeRequest> list);

    /**
     * 查询账户信息（余额）
     *
     * @param list
     * @return
     */
    @PostMapping("/queryAccountInfo")
    Result<List<AccountInfoResp>> queryAccountInfo(@RequestBody List<AccountInfoReq> list);

    /**
     * 查询账户升级后的绑卡状态
     * @param customerCode
     * @param customerType
     * @return
     */
    @PostMapping("/queryAccountBindStatus")
    Result<Boolean> queryAccountBindStatus(@RequestParam("customerCode") String customerCode, @RequestParam("customerType") String customerType);

}
