package com.akucun.account.proxy.facade.stub.pingan;

import com.akucun.fps.common.entity.Result;
import com.akucun.fps.pingan.client.model.po.PinganAccount;
import com.akucun.fps.pingan.client.vo.*;
import com.akucun.account.proxy.facade.stub.fallback.MerchantFacadeFallbackFactory;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * 对接商户系统
 * <AUTHOR>
 *
 */
@FeignClient(value = "account-proxy", fallbackFactory = MerchantFacadeFallbackFactory.class)
@RequestMapping("/api/account/proxy/pingan")
public interface MerchantFacade {
    /**
     * 账户注册
     */
    @PostMapping(value = "/registerPinganAccount", produces = "application/json;charset=utf-8")
    Result<String> registerPinganAccount(@RequestBody MemBerRegisterVO memBerRegisterVO);

    /**
     * 商户注册
     */
    @PostMapping(value = "/register", produces = "application/json;charset=utf-8")
    Result<String> register(@RequestBody MerchantRegisterVO merchantRegisterVO);

    /**
     * 商家小额鉴权绑卡
     */
    @PostMapping(value = "/bindCardForMerFir", produces = "application/json;charset=utf-8")
    Result<String> bindCardForMerFir(@RequestBody PinganCardVO pinganCardVO);

    /**
     * 小额鉴权验证
     * @param pinganCardVercVO
     * @return
     */
    @PostMapping(value = "/bindCardForMerSec", produces = "application/json;charset=utf-8")
    Result<String> bindCardForMerSec(@RequestBody PinganCardVercVO pinganCardVercVO);

    /**
     *  会员解绑提现账户【6065】
     * @param untieWithdrawVO
     * @return
     */
    @PostMapping(value = "/untieWithdraw", produces = "application/json;charset=utf-8")
    Result<String> untieWithdraw(@RequestBody UntieWithdrawVO untieWithdrawVO);

    /**
     * 申请提现或支付短信动态码【6082】
     */
    @PostMapping(value = "/getAuthCode", produces = "application/json;charset=utf-8")
    Result<AuthCodeResultVO> getAuthCode(@RequestBody AuthCodeVO authCodeVO);

    /**
     * 查询某笔小额鉴权转账状态
     * @param authCodeVO
     * @return
     * @des  状态：0：成功，1：失败，2：待确认
     */
    @PostMapping(value = "/selectAuthStatusByCode", produces = "application/json;charset=utf-8")
    Result<AuthStatusResultVO> selectAuthStatusByCode(@RequestBody AuthCodeVO authCodeVO);

    /**
     * 维护会员绑定提现账户联行号【6138】
     * @param maintainWithdrawVO
     * @return
     */
    @PostMapping(value = "/maintainWithdraw", produces = "application/json;charset=utf-8")
    Result<String> maintainWithdraw(@RequestBody MaintainWithdrawVO maintainWithdrawVO);

    /**
     * 查询平安注册账户信息
     * @param accountRegisterVO
     * @return
     */
    @PostMapping(value = "/selectPinganAccount", produces = "application/json;charset=utf-8")
    Result<PinganAccount> selectPinganAccount(@RequestBody AccountRegisterVO accountRegisterVO);

    /**
     *  对内解绑功能
     * @param untieWithdrawVO
     * @return
     */
    @PostMapping(value = "/untieWithdrawSelf", produces = "application/json;charset=utf-8")
    Result<String> untieWithdrawSelf(@RequestBody UntieWithdrawVO untieWithdrawVO);

    /**
     *  注销平安账户
     * @param memBerRegisterVO
     * @return
     */
    @PostMapping(value = "/cancelPinganAccount", produces = "application/json;charset=utf-8")
    Result<Void> cancelPinganAccount(@RequestBody MemBerRegisterVO memBerRegisterVO);

    /**
     * 更新平安账户信息
     * @param pinganAccount
     * @return
     */
    @PostMapping(value = "/updatePinganAccount", produces = "application/json;charset=utf-8")
    Result<Void> updatePinganAccount(@RequestBody PinganAccount pinganAccount);

}