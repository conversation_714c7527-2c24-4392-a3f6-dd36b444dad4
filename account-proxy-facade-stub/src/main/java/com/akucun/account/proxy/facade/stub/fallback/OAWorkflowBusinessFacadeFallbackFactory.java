package com.akucun.account.proxy.facade.stub.fallback;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.facade.stub.oa.OAWorkflowBusinessFacade;
import com.akucun.account.proxy.facade.stub.others.oa.req.business.MultiSupplierCorporatePaymentRequest;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Create on : 2025/1/14 17:28
 **/
@Component
public class OAWorkflowBusinessFacadeFallbackFactory implements FallbackFactory<OAWorkflowBusinessFacade> {
    @Override
    public OAWorkflowBusinessFacade create(Throwable cause) {
        return new OAWorkflowBusinessFacade() {
            @Override
            public Result<String> multiSupplierCorporatePayment(MultiSupplierCorporatePaymentRequest request) {
                return Result.error(500, "服务超时熔断");
            }
        };
    }
}
