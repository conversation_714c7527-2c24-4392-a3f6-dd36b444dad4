package com.akucun.account.proxy.facade.stub.account;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.facade.stub.fallback.AccountWithdrawFacadeFallbackFactory;
import com.akucun.account.proxy.facade.stub.others.dto.req.IsTenantCustomerReq;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * @Author: mapenghui
 * @Date: 2022/3/24
 * @desc:
 */
@FeignClient(value = "account-proxy", fallbackFactory = AccountWithdrawFacadeFallbackFactory.class)
@RequestMapping("/api/account/proxy/accountCheck")
public interface AccountCheckFacade {

    /**
     * 查询店主店长是否租户
     */
    @PostMapping(value = "/isTenantCustomer", produces = "application/json;charset=utf-8")
    Result<Boolean> isTenantCustomer(@RequestBody IsTenantCustomerReq req);

}
