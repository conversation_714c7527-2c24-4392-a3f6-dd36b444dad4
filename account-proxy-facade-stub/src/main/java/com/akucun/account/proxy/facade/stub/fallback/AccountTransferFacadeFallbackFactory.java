package com.akucun.account.proxy.facade.stub.fallback;

import com.akucun.account.proxy.facade.stub.account.AccountTransferFacade;
import com.akucun.fps.account.client.model.TransferAccountDO;
import com.akucun.fps.account.client.model.query.TransferAccountQueryDO;
import com.akucun.fps.common.entity.Query;
import com.akucun.fps.common.entity.Result;
import com.akucun.fps.common.entity.ResultList;
import com.akucun.fps.pingan.client.model.BankInfoManageResp;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * @Author: silei
 * @Date: 2021/2/3
 * @desc:
 */
@Component
public class AccountTransferFacadeFallbackFactory implements FallbackFactory<AccountTransferFacade> {

    @Override
    public AccountTransferFacade create(Throwable throwable) {
        return new AccountTransferFacade(){

            @Override
            public Result<String> transferApply(TransferAccountDO transferAccountDO) {
                Result<String> result = new Result<>();
                result.setError(500, "SERVICE_ERROR", "服务超时熔断");
                return result;
            }

            @Override
            public ResultList<TransferAccountDO> selectPage(Query<TransferAccountQueryDO> query) {
                ResultList<TransferAccountDO> resultList = new ResultList<>();
                resultList.setError(500, "SERVICE_ERROR", "服务超时熔断");
                return resultList;
            }
        };
    }
}
