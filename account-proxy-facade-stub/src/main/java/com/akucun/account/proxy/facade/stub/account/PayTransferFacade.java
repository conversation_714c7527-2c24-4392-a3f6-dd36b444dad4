package com.akucun.account.proxy.facade.stub.account;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.facade.stub.fallback.PayTransferFallbackFactory;
import com.akucun.account.proxy.facade.stub.others.dto.req.PaymentTransferReq;
import com.akucun.account.proxy.facade.stub.others.dto.res.PaymentTransferResp;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <AUTHOR>
 * @date 2021/02/01 21:34
 */
@FeignClient(value = "account-proxy", fallbackFactory = PayTransferFallbackFactory.class)
@RequestMapping("/api/account/proxy")
public interface PayTransferFacade {
    /**
     * 付款
     * @param request
     * @return
     */
    @PostMapping(value = "/payTransfer")
    Result<PaymentTransferResp> payTransfer(@RequestBody @Validated PaymentTransferReq request);
}
