package com.akucun.account.proxy.facade.stub.pingan;

import com.akucun.fps.common.entity.Result;
import com.akucun.fps.common.entity.ResultList;
import com.akucun.fps.pingan.client.model.query.BindCardQueryReq;
import com.akucun.fps.pingan.client.vo.PinganCardVO;
import com.akucun.account.proxy.facade.stub.fallback.MerchantQueryFacadeFallbackFactory;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 商戶查询
 * <AUTHOR>
 *
 */

@FeignClient(value = "account-proxy", fallbackFactory = MerchantQueryFacadeFallbackFactory.class)
@RequestMapping("/api/account/proxy/pingan")
public interface MerchantQueryFacade {
    /**
     * 查询绑定银行卡信息
     * @param customerCode
     * @return PinganCardVO
     */
    @PostMapping(value = "/selectBindCardByCodePage", produces = "application/json;charset=utf-8")
    ResultList<PinganCardVO> selectBindCardByCodePage(@RequestParam("customerCode") String customerCode);

    /**
     * 根据状态查询绑定银行卡信息
     * @param bindCardQueryReq
     * @return PinganCardVO
     */
    @PostMapping(value = "/selectBindCardByStatus", produces = "application/json;charset=utf-8")
    ResultList<PinganCardVO> selectBindCardByStatus(@RequestBody BindCardQueryReq bindCardQueryReq);

    /**
     * 根据卡号查询已绑定卡信息
     * @param bindCardQueryReq
     * @return
     */
    @PostMapping(value = "/selectBindCardByCardNo", produces = "application/json;charset=utf-8")
    Result<PinganCardVO> selectBindCardByCardNo(@RequestBody BindCardQueryReq bindCardQueryReq);

    /**
     * 根据条件查询绑卡信息
     * @param bindCardQueryReq
     * @return
     */
    @PostMapping(value = "/selectBindCardListByParams", produces = "application/json;charset=utf-8")
    ResultList<PinganCardVO> selectBindCardListByParams(@RequestBody BindCardQueryReq bindCardQueryReq);

    /**
     * 根据customerCode、customerType判断是否有绑卡成功记录
     * @return >=1：有绑卡成功记录 ，<1：无成功记录
     */
    @PostMapping(value = "/isHasBindSuccRecord", produces = "application/json;charset=utf-8")
    Result<Integer> isHasBindSuccRecord(@RequestBody BindCardQueryReq bindCardQueryReq);

}
