package com.akucun.account.proxy.facade.stub.others.dto.req;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

/**
 * 账户中心调账请求体
 */
@Data
public class AccountAdjustReq {

    /**
     * 请求号
     */
    @NotBlank(message = "请求号不能为空")
    private String requestNo;
    /**
     *操作人
     */
    @NotBlank(message = "操作人不能为空")
    private String operator;
    /**
     *账户类型-key
     */
    @NotBlank(message = "账户类型-key不能为空")
    private String accountTypeKey;
    /**
     *用户编码
     */
    @NotBlank(message = "用户编码不能为空")
    private String customerCode;
    /**
     *交易类型
     */
    @NotBlank(message = "交易类型不能为空")
    private String adjustmentType;
    /**
     *金额
     */
    @NotBlank(message = "金额不能为空")
    private BigDecimal amount;
    /**
     *备注
     */
    @NotBlank(message = "备注不能为空")
    private String remark;

    /**
     * 来源单号
     */
    private String sourceBillNo;

    /**
     * 交易流水号
     */
    private String tradeNo;
}
