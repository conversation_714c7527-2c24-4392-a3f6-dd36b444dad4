package com.akucun.account.proxy.facade.stub.others.dto.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @Author: silei
 * @Date: 2020/11/4
 * @desc:
 */
@ApiModel("账户余额信息查询")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AccountInfoReq implements Serializable {

    private static final long serialVersionUID = -980185380228875568L;

    @NotBlank(message = "客户类型不能为空")
    @ApiModelProperty(value = "客户类型", required = true)
    private String customerType;

    @NotBlank(message = "客户编码不能为空")
    @ApiModelProperty(value = "客户编码", required = true)
    private String customerCode;

    public String getCustomerType() {
        return customerType;
    }

    public void setCustomerType(String customerType) {
        this.customerType = customerType;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }
}
