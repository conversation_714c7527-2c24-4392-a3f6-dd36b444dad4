package com.akucun.account.proxy.facade.stub.enums;

import lombok.Getter;

@Getter
public enum TaxTripPactionApplyStatusEnum {

    AUDITING(0, "审核中"),
    AUDIT_PASS(1, "审核通过"),
    AUDIT_REJECT(2, "驳回");

    private int status;
    private String desc;

    private TaxTripPactionApplyStatusEnum(int status, String desc){
        this.status = status;
        this.desc = desc;
    }

    public static String getDescByStatus(int status){
        for(TaxTripPactionApplyStatusEnum statusEnum : values()){
            if(status == statusEnum.getStatus()){
                return statusEnum.getDesc();
            }
        }

        return null;
    }
}
