/*
 * @Author: Lee
 * @Date: 2025-04-17 11:02:18
 * @Description: 线下调账相关
 * @License: Copyright (c) 2025, Lee
 */
package com.akucun.account.proxy.facade.stub.account;

import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import com.akucun.account.proxy.facade.stub.fallback.OfflineAdjustAccountFacadeFallbackFactory;
import com.akucun.account.proxy.facade.stub.others.account.req.OfflineAdjustAccountBatchRequest;
import com.akucun.account.proxy.facade.stub.others.account.req.OfflineAdjustAccountPageQueryReq;
import com.akucun.account.proxy.facade.stub.others.account.req.OfflineAdjustAccountSingleRequest;
import com.akucun.account.proxy.facade.stub.others.account.req.OfflineAdjustAccountSubmitReq;
import com.akucun.account.proxy.facade.stub.others.account.vo.OfflineAdjustAccountBatchStatisticVO;
import com.akucun.account.proxy.facade.stub.others.account.vo.OfflineAdjustAccountVO;
import com.mengxiang.base.common.model.result.Pagination;
import com.mengxiang.base.common.model.result.Result;

@FeignClient(value = "account-proxy", fallbackFactory = OfflineAdjustAccountFacadeFallbackFactory.class)
@RequestMapping("/api/account/proxy/offlineAdjust")
public interface OfflineAdjustAccountFacade {

    /**
     * 新增线下调账记录
     * @param addVO
     * @return
     */
    @PostMapping(value = "/submit", produces = "application/json;charset=utf-8")
    Result<Void> submit(@RequestBody @Validated OfflineAdjustAccountSubmitReq addVO);

    /**
     * 分页查询线下调账记录
     * @param queryReq
     * @return
     */
    @PostMapping(value = "/page", produces = "application/json;charset=utf-8")
    Result<Pagination<OfflineAdjustAccountVO>> pageQuery(@RequestBody OfflineAdjustAccountPageQueryReq queryReq);

    /**
     * 统计线下调账记录
     * @param queryReq
     * @return
     */
    @PostMapping(value = "/statistic", produces = "application/json;charset=utf-8")
    Result<OfflineAdjustAccountBatchStatisticVO> statistic(@RequestBody OfflineAdjustAccountPageQueryReq queryReq);

    /**
     * 审核通过线下调账记录
     * @param id
     * @param operator
     * @return
     */     
    @PostMapping(value = "/auditPass", produces = "application/json;charset=utf-8")
    Result<Void> auditPass(@RequestBody OfflineAdjustAccountSingleRequest request);

    /**
     * 审核拒绝线下调账记录
     * @param id
     * @param operator
     * @return
     */     
    @PostMapping(value = "/auditRefuse", produces = "application/json;charset=utf-8")
    Result<Void> auditRefuse(@RequestBody OfflineAdjustAccountSingleRequest request);

    /**
     * 批量删除线下调账记录
     * @param ids
     * @param operator
     * @return
     */     
    @PostMapping(value = "/batchDelete", produces = "application/json;charset=utf-8")
    Result<Integer> batchDelete(@RequestBody OfflineAdjustAccountBatchRequest request);

    /**
     * 一键删除
     * @param queryReq
     * @param operator
     * @return
     */     
    @PostMapping(value = "/onKeyDelete", produces = "application/json;charset=utf-8")
    Result<Void> onKeyDelete(@RequestBody OfflineAdjustAccountPageQueryReq queryReq, @RequestParam("operator") String operator);

    /**
     * 重试
     * @param request
     * @return
     */
    @PostMapping(value = "/retry", produces = "application/json;charset=utf-8")
    Result<Void> retry(@RequestBody OfflineAdjustAccountSingleRequest request);
}