package com.akucun.account.proxy.facade.stub.account;

import com.akucun.account.center.client.model.*;
import com.akucun.account.center.client.model.account.AccountBookDO;
import com.akucun.account.center.client.model.account.AccountBookDetailDO;
import com.akucun.account.center.client.model.account.AccountDetailTotalInfoDO;
import com.akucun.account.center.client.model.query.AccountDetailQuery;
import com.akucun.account.center.client.model.query.AccountQuery;
import com.akucun.account.center.client.model.rsp.QueryTradeSumRsp;
import com.akucun.account.center.common.entity.Query;
import com.akucun.account.center.common.entity.ResultList;
import com.akucun.account.proxy.facade.stub.fallback.AccountCenterFacadeFallbackFactory;
import com.akucun.account.proxy.facade.stub.others.account.req.*;
import com.akucun.common.Result;
import com.akucun.member.api.vo.AccountRecordVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Author: silei
 * @Date: 2020/12/6
 * @desc: 账户中心接口
 */
@FeignClient(value = "account-proxy", fallbackFactory = AccountCenterFacadeFallbackFactory.class)
@RequestMapping("/api/account/proxy/center")
public interface AccountCenterFacade {

    /**
     * 账户处理
     * @param req
     * @return
     */
    @PostMapping(value = "/dealAccount", produces = "application/json;charset=utf-8")
    Result<Void> dealAccount(@RequestBody AccountOperateInfoReq req);

    /**
     * 交易处理
     * @param tradeInfo
     * @return
     */
    @PostMapping(value = "/dealTrade", produces = "application/json;charset=utf-8")
    Result<Void> dealTrade(@RequestBody TradeInfo tradeInfo);

    /**
     * 处理转账信息
     * @param transferInfo
     * @return
     */
    @PostMapping(value = "/transfer", produces = "application/json;charset=utf-8")
    Result<Void> transfer(@RequestBody TransferInfo transferInfo);

    /**
     * 查询账本信息
     * @param accountQuery
     * @return
     */
    @PostMapping(value = "/queryAccount", produces = "application/json;charset=utf-8")
    Result<AccountBookDO> queryAccount(@RequestBody AccountQuery accountQuery);

    /**
     * 查询账户交易明细
     * @param query
     * @return
     */
    @PostMapping(value = "/queryAccountDetail", produces = "application/json;charset=utf-8")
    ResultList<AccountBookDetailDO> queryAccountDetail(@RequestBody Query<AccountDetailQuery> query);

    /**
     * 查询账户交易收入支出汇总信息
     * @param query
     * @return
     */
    @PostMapping(value = "/queryAccountDetailTotalInfo", produces = "application/json;charset=utf-8")
    Result<AccountDetailTotalInfoDO> queryAccountDetailTotalInfo(@RequestBody Query<AccountDetailQuery> query);

    /**
     * 查询账本明细信息金额
     * @param query
     * @return
     */
    @PostMapping(value = "/queryAccountDetailAmount", produces = "application/json;charset=utf-8")
    Result<AmountInfo> queryAccountDetailAmount(@RequestBody Query<AccountDetailQuery> query);

    /**
     * 查询主要账户
     * @param customerCode
     * @return
     */
    @RequestMapping(value = "/getAdminSystem", produces = "application/json;charset=utf-8")
    Result<String> getAdminSystem(@RequestParam("customerCode") String customerCode);

    /**
     * H5积分转入爱库存奖励金账户
     * @param h5PointTransferInfo
     * @return
     */
    @PostMapping(value = "/h5point2Award", produces = "application/json;charset=utf-8")
    Result<Void> h5point2Award(@RequestBody H5PointTransferInfo h5PointTransferInfo);

    /**
     * 保存提现标识
     * @param customerCode
     * @param customerType
     * @return
     */
    @RequestMapping(value = "/buildWithdrawFlag", produces = "application/json;charset=utf-8")
    Result<Boolean> buildWithdrawFlag(@RequestParam("customerCode") String customerCode, @RequestParam("customerType") String customerType);

    /**
     * 查询提现标识
     * @param customerCode
     * @param customerType
     * @return
     */
    @RequestMapping(value = "/queryWithdrawFlag", produces = "application/json;charset=utf-8")
    Result<Boolean> queryWithdrawFlag(@RequestParam("customerCode") String customerCode, @RequestParam("customerType") String customerType);

    /**
     * 商户账户金额解冻(仅适用于商户首尾款场景)
     * @param accountTradeInfo
     * @return
     */
    @PostMapping(value = "/unfreezeMerchantAccountAmount", produces = "application/json;charset=utf-8")
    Result<Void> unfreezeMerchantAccountAmount(@RequestBody AccountTradeInfo accountTradeInfo);

    /**
     * 查询全部奖励金
     * @param customerCode
     * @return
     */
    @PostMapping(value = "/getAllBonus", produces = "application/json;charset=utf-8")
    Result<Long> getAllBonus(@RequestParam("customerCode") String customerCode);

    /**
     * 啊呀团奖励发放
     * @param aytTransferInfo
     * @return
     */
    @ApiOperation(value = "啊呀团奖励发放")
    @PostMapping(value = "/aytAwardTransfer", produces = "application/json;charset=utf-8")
    Result<Void> aytAwardTransfer(@RequestBody AYTTransferReq aytTransferInfo);

    /**
     * 批量交易处理
     * @param tradeInfoList
     * @return
     */
    @PostMapping(value = "/batchDealTrade", produces = "application/json;charset=utf-8")
    Result<Void> batchDealTrade(@RequestBody List<TradeInfo> tradeInfoList);

    /**
     * 查询啊呀团余额汇总信息
     * @param tradeSumRequests
     * @return
     */
    @PostMapping(value = "/queryTradeSumForAyaTuan", produces = "application/json;charset=utf-8")
    Result<List<QueryTradeSumRsp>> queryTradeSumForAyaTuan(@RequestBody List<QueryTradeSumRequest> tradeSumRequests);

    /**
     * 账户中心余额汇总
     * @param tradeQueryRequestList
     * @return
     */
    @PostMapping(value = "/queryTradeSum", produces = "application/json;charset=utf-8")
    Result<List<QueryTradeSumRsp>> queryTradeSum(@RequestBody List<TradeQueryRequest> tradeQueryRequestList);

    /**
     * 查询会员奖励金账本信息（刷数用）
     * @param userId
     * @return
     */
    @GetMapping(value = "/queryMemberBonusAccount")
    Result<AccountBookDO> queryMemberBonusAccount(@RequestParam("userId") String userId);

    /**
     * 会员奖励金账户交易处理
     * @param tradeInfo
     * @return
     */
    @PostMapping(value = "/memberBonusAccountDealTrade", produces = "application/json;charset=utf-8")
    Result<Void> memberBonusAccountDealTrade(@RequestBody MemberBonusAccountTradeInfo tradeInfo);

    @ApiOperation(value = "检查奖励金账户记录是否存在")
    @GetMapping(value = "/checkAwardRecordByTradeNo")
    Result<List<AccountRecordVO>> checkAwardRecordByTradeNo(@RequestParam(value = "customerCode") String customerCode, @RequestParam(value = "tradeNo") String tradeNo);
}
