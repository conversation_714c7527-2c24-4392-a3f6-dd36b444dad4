package com.akucun.account.proxy.facade.stub.fallback;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.facade.stub.account.AccountFacade;
import com.akucun.account.proxy.facade.stub.others.dto.req.AccountInfoReq;
import com.akucun.account.proxy.facade.stub.others.dto.req.AccountUpgradePreCheckRequest;
import com.akucun.account.proxy.facade.stub.others.dto.req.AccountUpgradeRequest;
import com.akucun.account.proxy.facade.stub.others.dto.res.AccountInfoResp;
import com.akucun.account.proxy.facade.stub.others.dto.res.AccountUpgradeResp;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class AccountFacadeFallbackFactory implements FallbackFactory<AccountFacade> {

    @Override
    public AccountFacade create(Throwable throwable) {
        return new AccountFacade() {

            @Override
            public Result<Void> upgradePreCheck(AccountUpgradePreCheckRequest request) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<Void> accountUpgrade(AccountUpgradeRequest accountUpgradeRequest) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<List<AccountUpgradeResp>> queryUpgradeStatus(List<AccountUpgradeRequest> list) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<List<AccountInfoResp>> queryAccountInfo(List<AccountInfoReq> list) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<Boolean> queryAccountBindStatus(String customerCode, String customerType) {
                return Result.error(500, "服务超时熔断");
            }
        };
    }
}
