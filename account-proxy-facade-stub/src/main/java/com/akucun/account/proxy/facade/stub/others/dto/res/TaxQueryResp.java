package com.akucun.account.proxy.facade.stub.others.dto.res;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : 税金查询等业务-响应参数
 * @Create on : 2025/1/14 15:55
 **/
@Data
@NoArgsConstructor
public class TaxQueryResp {
    @ApiModelProperty("业务类型:EMPOWERMENT_CAMP 赋能营 MONTHLY_BONUS 月勤奖 MENTOR_BONUS 导师激励")
    private String bizType;

    @ApiModelProperty("用户code")
    private String userCode;

    @ApiModelProperty("用户类型，不填默认NM")
    private String userType;

    @ApiModelProperty("业务金额")
    private BigDecimal amount;

    @ApiModelProperty("税金")
    private BigDecimal taxAmount;

    @ApiModelProperty("税率")
    private BigDecimal feeRate;

    @ApiModelProperty("认证类型，以会员提供的枚举值为准（-2 个人临登,-1:个人店铺声明,0：个人身份证认证，1:个体临时认证，2：个体自有渠道认证，3:企业认证）")
    private Integer userGrade;

    @ApiModelProperty("认证渠道，以会员提供的枚举值为准（认证渠道，以会员提供的枚举值为准）")
    private Integer gradeChannel;

    @ApiModelProperty("税率查询结果")
    private Boolean queryFlag = Boolean.TRUE;
    @ApiModelProperty("税率查询失败原因")
    private String errorMsg;
}
