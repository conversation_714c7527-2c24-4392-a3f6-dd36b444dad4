package com.akucun.account.proxy.facade.stub.others.oa.req;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Create on : 2025/1/14 16:10
 **/
@Data
@NoArgsConstructor
public class OAWorkflowTableField {

    //字段名称
    @NotBlank(message = "表单字段名称不能为空")
    private String fieldName;

    //字段值
    private String fieldValue;

    //当前只有附件字段使用，其他时候可以不传。附件时：传 http:1.jpg,http:附件链接类型，后面为文件名，多个用 “|”隔开
    private String fieldType;

}
