package com.akucun.account.proxy.facade.stub.others.oa.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Create on : 2025/1/15 11:25
 **/
@Data
public class OAWorkflowStatusNotifyRequest {

    @ApiModelProperty("业务编号")
    private String bizNo;

    @ApiModelProperty("流程请求ID")
    private String requestId;

    @ApiModelProperty("流程编号")
    private String workflowNo;

    @ApiModelProperty("OA工作流最新状态")
    private String status;

    @ApiModelProperty("OA工作流最新状态描述")
    private String statusDesc;

    @ApiModelProperty("流程终止原因")
    private String errorMsg;

    /**
     * 通过API接口创建的流程，如果状态包含“归档”则流程已完成
     * @return
     */
    public boolean isFinished() {
        return !StringUtils.isBlank(status) && status.contains("归档");
    }

    /**
     * 通过API接口创建的流程，非完成、非作废状态，即流程处理中
     * @return
     */
    public boolean isProcessing() {
        return !isFinished() && !isCanceled();
    }

    /**
     * 通过API接口创建的流程，创建即提交，当状态为“申请人提交”时，可认定为流程作废，需要重新提交
     * @return
     */
    public boolean isCanceled() {
        return !StringUtils.isBlank(status) && status.equals("申请人提交");
    }

}
