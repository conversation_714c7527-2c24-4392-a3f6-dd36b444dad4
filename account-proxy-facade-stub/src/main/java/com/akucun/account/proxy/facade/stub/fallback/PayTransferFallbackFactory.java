package com.akucun.account.proxy.facade.stub.fallback;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.facade.stub.account.PayTransferFacade;
import com.akucun.account.proxy.facade.stub.others.dto.req.PaymentTransferReq;
import com.akucun.account.proxy.facade.stub.others.dto.res.PaymentTransferResp;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2021/02/01 21:36
 */
@Component
public class PayTransferFallbackFactory implements FallbackFactory<PayTransferFacade> {
    @Override
    public PayTransferFacade create(Throwable throwable) {
       return new PayTransferFacade() {
           @Override
           public Result<PaymentTransferResp> payTransfer(PaymentTransferReq request) {
               return Result.error(500, "服务超时熔断");
           }
       };
    }
}
