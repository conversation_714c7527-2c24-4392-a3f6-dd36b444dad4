package com.akucun.account.proxy.facade.stub.others.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Create on : 2025/1/15 13:37
 **/
@Data
@NoArgsConstructor
public class BatchTaxReq {
    @ApiModelProperty("业务类型:EMPOWERMENT_CAMP 赋能营 MONTHLY_BONUS 月勤奖 MENTOR_BONUS 导师激励")
    //@NotBlank(message = "业务类型不能为空")
    private String bizType;

    @ApiModelProperty("明细")
    private List<TaxDTO> taxList;
}
