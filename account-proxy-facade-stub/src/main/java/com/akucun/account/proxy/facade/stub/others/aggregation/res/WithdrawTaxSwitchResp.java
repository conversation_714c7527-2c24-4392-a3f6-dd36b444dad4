package com.akucun.account.proxy.facade.stub.others.aggregation.res;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class WithdrawTaxSwitchResp implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 2260219306584845118L;

    /**
     * 个税开关：on-开，off-关
     */
    private String taxSwitch;

    /**
     * 个税开始时间：yyyyMMdd
     */
    private String taxStartDate;

    public String getTaxSwitch() {
        return taxSwitch;
    }

    public void setTaxSwitch(String taxSwitch) {
        this.taxSwitch = taxSwitch;
    }

    public String getTaxStartDate() {
        return taxStartDate;
    }

    public void setTaxStartDate(String taxStartDate) {
        this.taxStartDate = taxStartDate;
    }
}
