package com.akucun.account.proxy.facade.stub.others.dto.req;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

/**
 * 平安账户调账请求体
 */
@Data
public class PinganAdjustReq {

    /**
     * 请求号
     */
    @NotBlank(message = "请求号不能为空")
    private String requestNo;
    /**
     *操作人
     */
    @NotBlank(message = "操作人不能为空")
    private String operator;
    /**
     *调账类型：1：分账，2：转账，3：罚扣，4：账户中心调账
     */
    @NotBlank(message = "调账类型不能为空")
    private String adjustmentType;
    /**
     *用户编码
     */
    @NotBlank(message = "用户编码不能为空")
    private String customerCode;
    /**
     *用户类型
     */
    @NotBlank(message = "用户类型不能为空")
    private String customerType;
    /**
     *金额
     */
    @NotBlank(message = "金额不能为空")
    private BigDecimal amount;
    /**
     *备注
     */
    @NotBlank(message = "备注不能为空")
    private String remark;

}
