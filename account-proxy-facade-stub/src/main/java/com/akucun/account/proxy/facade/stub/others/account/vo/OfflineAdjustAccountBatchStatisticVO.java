/*
 * @Author: <PERSON>
 * @Date: 2025-04-17 11:04:29
 * @Description: 线下调账相关
 * @License: Copyright (c) 2025, Lee
 */
package com.akucun.account.proxy.facade.stub.others.account.vo;

import java.math.BigDecimal;

import lombok.Data;

@Data
public class OfflineAdjustAccountBatchStatisticVO {

    /**
     * 条数
     */
    private Long count;

    /**
     * 金额
     */
    private BigDecimal amount;
    
}