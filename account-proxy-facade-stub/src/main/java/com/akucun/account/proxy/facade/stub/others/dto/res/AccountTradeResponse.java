package com.akucun.account.proxy.facade.stub.others.dto.res;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: silei
 * @Date: 2020/8/26
 * @desc: 账户交易返回体
 */
@ApiModel("账户交易返回实体")
@Data
@ToString
@NoArgsConstructor
public class AccountTradeResponse implements Serializable {
    private static final long serialVersionUID = 133603804358893599L;

    /**
     * 支付状态
     */
    private String status;

    /**
     * 支付完成时间
     */
    private String successTime;

    /**
     * 子单商户订单号
     */
    private String tradeNo;

    /**
     * 金额
     */
    private BigDecimal amount;

}
