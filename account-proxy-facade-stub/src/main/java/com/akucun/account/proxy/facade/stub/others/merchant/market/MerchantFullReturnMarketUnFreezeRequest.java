package com.akucun.account.proxy.facade.stub.others.merchant.market;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Create on : 2024/4/26 16:44
 **/
@Data
@ApiModel
public class MerchantFullReturnMarketUnFreezeRequest implements Serializable {

    /**
     * 营销活动ID = TaskAcceptRequest.bizNo
     */
    @ApiModelProperty("营销活动ID = TaskAcceptRequest.bizNo")
    @NotBlank(message = "promoActivityId不能为空")
    private String promoActivityId;

    /**
     * 营销活动类型 = TaskAcceptRequest.bizInfo.promoActivityType
     */
    @ApiModelProperty("营销活动类型 = TaskAcceptRequest.bizInfo.promoActivityType")
    @NotBlank(message = "promoActivityType不能为空")
    private String promoActivityType;

    /**
     * 业务单号，同冻结时提交的单号（控幂等的） = TaskAcceptRequest.bizInfo.bizNo
     */
    @ApiModelProperty("业务单号，同冻结时提交的单号（控幂等的） = TaskAcceptRequest.bizInfo.bizNo")
    @NotBlank(message = "bizNo不能为空")
    private String bizNo;

    /**
     * 说明 = TaskAcceptRequest.bizInfo.bizExplain
     */
    @ApiModelProperty("说明 = TaskAcceptRequest.bizInfo.bizExplain")
    @NotBlank(message = "bizExplain不能为空")
    private String bizExplain;

    /**
     * 用户编码 = TaskAcceptRequest.bizInfo.objectId
     */
    @ApiModelProperty("用户编码 = TaskAcceptRequest.bizInfo.objectId")
    @NotBlank(message = "objectId不能为空")
    private String objectId;

    /**
     * 用户类型 = TaskAcceptRequest.bizInfo.objectType
     * SHOP：店铺，MERCHANT：商家，PLATFORM：平台，COMMUNITY：战队，SELLER：店主，SCHOOL：学校，DISTRIBUTOR：店长
     */
    @ApiModelProperty("用户类型 = TaskAcceptRequest.bizInfo.objectType")
    @NotBlank(message = "objectType不能为空")
    private String objectType;

    /**
     * 请求平台 = TaskAcceptRequest.requestPlatform
     */
    @ApiModelProperty("请求平台 = TaskAcceptRequest.requestPlatform")
    @NotBlank(message = "requestPlatform不能为空")
    private String requestPlatform;

    /**
     * 激励类型，新增字段
     * SHORT：短期激励，LONG：长期激励
     */
    @ApiModelProperty("激励类型，新增字段，SHORT：短期激励，LONG：长期激励")
    @NotBlank(message = "incentiveType不能为空")
    private String incentiveType;

    @ApiModelProperty("商家编码，新增字段")
    @NotBlank(message = "merchantCode不能为空")
    private String merchantCode;

}
