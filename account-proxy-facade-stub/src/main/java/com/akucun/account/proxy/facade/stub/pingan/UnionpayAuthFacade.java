package com.akucun.account.proxy.facade.stub.pingan;

import com.akucun.fps.common.entity.Result;
import com.akucun.fps.pingan.client.model.PingAnBindingRegisterAccountReqDO;
import com.akucun.fps.pingan.client.model.PingAnBindingRegisterAccountRespDO;
import com.akucun.fps.pingan.client.vo.PinganCardVO;
import com.akucun.account.proxy.facade.stub.fallback.UnionpayAuthFacadeFallbackFactory;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * 银联鉴权服务
 * @Author: huayanpei
 */
@FeignClient(value = "account-proxy", fallbackFactory = UnionpayAuthFacadeFallbackFactory.class)
@RequestMapping("/api/account/proxy/pingan")
public interface UnionpayAuthFacade {
    /**
     * 会员绑定提现账户(发送短信验证码)[6066]
     */
    @PostMapping(value = "/unionpayAuthApply", produces = "application/json;charset=utf-8")
    Result<String> unionpayAuthApply(@RequestBody PinganCardVO pinganCard);

    /**
     * 会员绑定提现账户(验证短信)[6067]
     */
    @PostMapping(value = "/unionpayAuthConfirm", produces = "application/json;charset=utf-8")
    Result<PingAnBindingRegisterAccountRespDO> unionpayAuthConfirm(@RequestBody PingAnBindingRegisterAccountReqDO account);
}
