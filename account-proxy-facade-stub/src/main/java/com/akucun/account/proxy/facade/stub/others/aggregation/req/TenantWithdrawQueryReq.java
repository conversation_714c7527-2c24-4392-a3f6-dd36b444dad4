package com.akucun.account.proxy.facade.stub.others.aggregation.req;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: silei
 * @Date: 2021/4/21
 * @desc: 租户提现查询
 */
public class TenantWithdrawQueryReq implements Serializable {

    private static final long serialVersionUID = -6992894154851339992L;

    /**
     * 客户编码
     */
    private String customerCode;
    /**
     * 客户类型
     */
    private String customerType;
    /**
     * 提现单号
     */
    private String withdrawNo;
    /**
     * 申请开始时间
     */
    private Date applyStartDate;
    /**
     * 申请结束时间
     */
    private Date applyEndDate;
    /**
     * 申请状态
     */
    private String applyStatus;
    /**
     * 来源单号
     */
    private String sourceBillNo;
    /**
     * 店铺ID
     */
    private String shopId;

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getCustomerType() {
        return customerType;
    }

    public void setCustomerType(String customerType) {
        this.customerType = customerType;
    }

    public String getWithdrawNo() {
        return withdrawNo;
    }

    public void setWithdrawNo(String withdrawNo) {
        this.withdrawNo = withdrawNo;
    }

    public Date getApplyStartDate() {
        return applyStartDate;
    }

    public void setApplyStartDate(Date applyStartDate) {
        this.applyStartDate = applyStartDate;
    }

    public Date getApplyEndDate() {
        return applyEndDate;
    }

    public void setApplyEndDate(Date applyEndDate) {
        this.applyEndDate = applyEndDate;
    }

    public String getApplyStatus() {
        return applyStatus;
    }

    public void setApplyStatus(String applyStatus) {
        this.applyStatus = applyStatus;
    }

    public String getSourceBillNo() {
        return sourceBillNo;
    }

    public void setSourceBillNo(String sourceBillNo) {
        this.sourceBillNo = sourceBillNo;
    }

    public String getShopId() {
        return shopId;
    }

    public void setShopId(String shopId) {
        this.shopId = shopId;
    }

    @Override
    public String toString() {
        return "WithdrawQueryReq{" +
                "customerCode='" + customerCode + '\'' +
                ", customerType='" + customerType + '\'' +
                ", withdrawNo='" + withdrawNo + '\'' +
                ", applyStartDate=" + applyStartDate +
                ", applyEndDate=" + applyEndDate +
                ", applyStatus='" + applyStatus + '\'' +
                ", sourceBillNo='" + sourceBillNo + '\'' +
                ", shopId='" + shopId + '\'' +
                '}';
    }
}
