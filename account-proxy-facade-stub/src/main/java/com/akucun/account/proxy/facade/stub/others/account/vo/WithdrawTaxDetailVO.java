package com.akucun.account.proxy.facade.stub.others.account.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 提现扣税明细记录
 */
@Data
public class WithdrawTaxDetailVO {
    /**
     * 身份证
     */
    private String identifyNo;

    /**
     * 身份类别
     */
    private String identifyCategory;

    /**
     * 客户编码
     */
    private String customerCode;
    /**
     * 客户类型
     */
    private String customerType;
    /**
     * 客户名称
     */
    private String customerName;
    /**
     * 提现申请编号
     */
    private String withdrawNo;
    /**
     * 申请金额
     */
    private BigDecimal amount;
    /**
     * 到账金额
     */
    private BigDecimal withdraw;
    /**
     * 税费金额
     */
    private BigDecimal taxFee;
    /**
     * 状态:INIT,DOING,SUCC,FAIL
     */
    private String status;
    /**
     * 税率
     */
    private String feeRate;
    /**
     * 备注
     */
    private String remark;
    /**
     * 手续费金额
     */
    private BigDecimal serviceFee;
}
