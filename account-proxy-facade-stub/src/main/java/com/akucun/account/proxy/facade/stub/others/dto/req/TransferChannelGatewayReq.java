package com.akucun.account.proxy.facade.stub.others.dto.req;

import java.io.Serializable;

/**
 * @Author: silei
 * @Date: 2021/7/16
 * @desc:
 */
public class TransferChannelGatewayReq implements Serializable {

    private static final long serialVersionUID = 477850190259080291L;
    /**
     * 网关代码
     */
    private String gatewayCode;

    /**
     * 渠道代码：H5页面，XDAPPLET小程序
     */
    private String channelCode;
    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 状态:启用0 禁用1
     */
    private Long status;

    /**
     * 备注
     */
    private String remark;

    public String getGatewayCode() {
        return gatewayCode;
    }

    public void setGatewayCode(String gatewayCode) {
        this.gatewayCode = gatewayCode;
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public Long getStatus() {
        return status;
    }

    public void setStatus(Long status) {
        this.status = status;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public String toString() {
        return "TransferChannelGatewayReq{" +
                "gatewayCode='" + gatewayCode + '\'' +
                ", channelCode='" + channelCode + '\'' +
                ", tenantId='" + tenantId + '\'' +
                ", appId='" + appId + '\'' +
                ", status=" + status +
                ", remark='" + remark + '\'' +
                '}';
    }
}
