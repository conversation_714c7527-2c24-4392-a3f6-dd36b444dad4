package com.akucun.account.proxy.facade.stub.account;


import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.facade.stub.fallback.VipCardFacadeFallbackFactory;
import com.akucun.account.proxy.facade.stub.others.dto.req.VipCardInfoReq;
import com.akucun.account.proxy.facade.stub.others.dto.req.VipCardWriteOffReq;
import com.akucun.account.proxy.facade.stub.others.dto.res.VipCardInfoResp;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * 金饷VIP卡接口
 */
@FeignClient(value = "account-proxy", fallbackFactory = VipCardFacadeFallbackFactory.class)
@RequestMapping("/api/vipcard")
public interface VipCardFacade {

    /**
     * 查询有效vip卡基础信息，如为无效或已使用卡密，则返回异常
     * @param vipCardInfoReq
     * @return
     */
    @PostMapping(value = "/valid/info")
    Result<VipCardInfoResp> vipCardValidInfo(@RequestBody @Validated VipCardInfoReq vipCardInfoReq);

    /**
     * vip卡核销
     * @param vipCardWriteOffReq
     * @return
     */
    @PostMapping(value = "/writeOff")
    Result<Boolean> vipCardWriteOff(@RequestBody @Validated VipCardWriteOffReq vipCardWriteOffReq);

}
