package com.akucun.account.proxy.facade.stub.enums;

/**
 * <AUTHOR>
 */
public class AccountKeyConstants extends MEnum<AccountKeyConstants> {

    private static final long serialVersionUID = -6830315043512012644L;

    /** 今日断码 **/
    public static final AccountKeyConstants DCC = (AccountKeyConstants)create("208CD48EB78508345258B1581ED05B47", 0, "今日断码");

    /** openApi **/
    public static final AccountKeyConstants OP = (AccountKeyConstants)create("AEC6DAA921F51796F05CC3AE025A04EC", 1, "openApi");

    /** 商家平安账户 **/
    public static final AccountKeyConstants SH = (AccountKeyConstants)create("1B0224AA3F894DA4687F82D231D7F0CE", 2, "商家微信账户");

    /** 商家宁波商量微信账户 **/
    public static final AccountKeyConstants WCSH = (AccountKeyConstants)create("C1D5EB3D0B246FAF71305DA31067C40E", 2, "商家宁波商量微信账户");

    /** 新媒体-店主 **/
    public static final AccountKeyConstants NM = (AccountKeyConstants)create("8D256656F0A9E0A959024F16A8C910B3", 3, "饷店店主账户");

    /** 新媒体代理-店长 **/
    public static final AccountKeyConstants NMDL = (AccountKeyConstants)create("6A55B6EA4B16E697ED8F30DE17AFBA34", 4, "饷店店长账户");

    /** 奖励金账户（店主有） **/
    public static final AccountKeyConstants AWARD = (AccountKeyConstants)create("2721679B9C013EC7FC5B31C673494413", 5, "爱库存奖励金账户");

    /** 代购 （爱库存普通会员） **/
    public static final AccountKeyConstants DG = (AccountKeyConstants)create("D310397BA71383DCD8208A5DD49F25C1", 6, "爱库存代购账户");

    /** 饷店店主积分账户 **/
    public static final AccountKeyConstants NMJF = (AccountKeyConstants)create("E91767EBCDD5F2A2EAE2099362CA06F2", 7, "饷店店主积分账户");

    /** 租户账户 **/
    public static final AccountKeyConstants AT = (AccountKeyConstants)create("CD1CB5580F8C4307E986FD77ED98DC1E", 8, "租户账户");

    /** 团长帮卖账户 **/
    public static final AccountKeyConstants TB = (AccountKeyConstants)create("B174B9B127F59CBA4B36DCEF0CF58581", 9, "团长帮卖账户");

    /** 商家上海商量微信账户 **/
    public static final AccountKeyConstants WCSS = (AccountKeyConstants)create("543D5D625B75C4D13C44AE28C3CAD69E", 10, "商家上海商量微信账户");
    /** 平台记账账户 **/
    public static final AccountKeyConstants PT = (AccountKeyConstants)create("ABADDC568174068C084A4B8E486F173A", 11, "平台记账账户");
    /** 经销渠道商账户 **/
    public static final AccountKeyConstants QDS = (AccountKeyConstants)create("E467E26D974D99913EC748E38E134A04", 12, "经销渠道商");
    /** 代销渠道商账户 **/
    public static final AccountKeyConstants DXQDS = (AccountKeyConstants)create("730A60B7F86DFD9E5CE648CBA84A641E", 13, "代销渠道商");

    /** 商家营销账户 **/
    public static final AccountKeyConstants SH_MARKET = (AccountKeyConstants)create("B19002B29407357DF8FAD51471DBFFA5", 14, "商家营销账户");
    /** 商家保证金账户 **/
    public static final AccountKeyConstants SH_BOND = (AccountKeyConstants)create("51E10381BC114D1505DB9A4473617D78", 15, "商家保证金账户");

}