package com.akucun.account.proxy.facade.stub.others.trade.req;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class TradeReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 交易流水号，幂等控制字段
     */
    private String tradeNo;

    /**
     * 业务单号
     */
    private String bizNo;

    /**
     * 产品线：饷店、企业饷店、商家、豆联盟
     */
    private String productCategory;

    /**
     * 业务类型：绑卡、开户、分佣、补偿、提现
     */
    private String bizType;

    /**
     * 请求平台编码：IOS、安卓、小程序、H5
     */
    private String requestPlatform;

    /**
     * JSON格式的业务参数，不同业务参数不同
     */
    private String bizInfo;

    /**
     * 备注
     */
    private String remark;
}
