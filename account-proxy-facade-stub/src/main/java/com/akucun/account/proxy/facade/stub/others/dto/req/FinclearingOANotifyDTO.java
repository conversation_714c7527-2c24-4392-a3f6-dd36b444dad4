package com.akucun.account.proxy.facade.stub.others.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : OA异步回调对象
 * @Create on : 2025/1/14 16:09
 **/
@Data
@NoArgsConstructor
public class FinclearingOANotifyDTO {
    @ApiModelProperty("业务方：请求id")
    private String busiNo;

    @ApiModelProperty("OA同步相应的id")
    private String remoteReqNo;

    @ApiModelProperty("OA工作流id")
    private String workflowId;

    @ApiModelProperty("OA工作流最新状态")
    private String status;

    @ApiModelProperty("OA工作流最新状态描述")
    private String statusDesc;

    @ApiModelProperty("OA工作流异常中断的描述")
    private String errorMsg;
}
