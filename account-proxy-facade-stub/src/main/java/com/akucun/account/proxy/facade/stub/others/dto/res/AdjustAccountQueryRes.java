package com.akucun.account.proxy.facade.stub.others.dto.res;

import com.akucun.account.proxy.facade.stub.others.account.vo.AdjustAccountVo;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 调账查询响应信息
 */
@Data
public class AdjustAccountQueryRes {
    /**
     * id主键
     */
    private Long id;
    /**
     * 请求号
     */
    private String requestNo;
    /**
     *调账类型：1：分账，2：转账，3：罚扣，4：账户中心调账
     */
    private String adjustmentType;
    /**
     *用户编码
     */
    private String customerCode;
    /**
     *用户类型
     */
    private String customerType;
    /**
     *状态：0：处理中，1：成功，2：失败，3：异常
     */
    private String status;
    /**
     *金额
     */
    private BigDecimal amount;
    /**
     *备注
     */
    private String remark;
    /**
     *账户类型-key
     */
    private String accountTypeKey;
    /**
     *操作人
     */
    private String operator;
    /**
     *创建时间
     */
    private Date createTime;
    /**
     *更新时间
     */
    private Date updateTime;
    /**
     * 来源单号
     */
    private String sourceBillNo;

    /**
     * 交易流水号
     */
    private String tradeNo;

}
