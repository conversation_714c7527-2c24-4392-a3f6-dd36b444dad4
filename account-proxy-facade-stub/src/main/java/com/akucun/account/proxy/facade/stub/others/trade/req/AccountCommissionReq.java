package com.akucun.account.proxy.facade.stub.others.trade.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Objects;

/**
 * @Author: silei
 * @Date: 2020/12/7
 * @desc: 分佣请求参数
 */
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("分佣请求参数")
public class AccountCommissionReq implements Serializable {

    private static final long serialVersionUID = -6837664097289809208L;
    /**
     * 客户编码
     */
    @NotBlank(message = "客户编码不能为空")
    @ApiModelProperty(value = "客户编码", required = true)
    private String customerCode;
    /**
     * 客户类型
     * @see com.akucun.account.proxy.facade.stub.enums.CustomerType
     */
    @NotBlank(message = "客户类型不能为空")
    @ApiModelProperty(value = "客户类型（店主：NM  店长：NMDL）", required = true)
    private String customerType;
    /**
     * 金额
     */
    @NotNull(message = "金额不能为空")
    @ApiModelProperty(value = "金额（单位：元）", required = true)
    private BigDecimal amount;
    /**
     * 交易类型
     * @see com.akucun.account.proxy.facade.stub.enums.DetailTypeConstants
     */
    @NotBlank(message = "交易类型不能为空")
    @ApiModelProperty(value = "交易类型", required = true)
    private String tradeType;
    /**
     * 交易单号
     */
    @NotBlank(message = "交易单号不能为空")
    @ApiModelProperty(value = "交易单号", required = true)
    private String tradeNo;
    /**
     * 来源单号
     */
    @NotBlank(message = "来源单号不能为空")
    @ApiModelProperty(value = "来源单号", required = true)
    private String sourceBillNo;
    /**
     * 店铺编号
     */
    @ApiModelProperty(value = "店铺编号（店长身份时必传）")
    private String shopId;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getCustomerType() {
        return customerType;
    }

    public void setCustomerType(String customerType) {
        this.customerType = customerType;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getTradeType() {
        return tradeType;
    }

    public void setTradeType(String tradeType) {
        this.tradeType = tradeType;
    }

    public String getTradeNo() {
        return tradeNo;
    }

    public void setTradeNo(String tradeNo) {
        this.tradeNo = tradeNo;
    }

    public String getSourceBillNo() {
        return sourceBillNo;
    }

    public void setSourceBillNo(String sourceBillNo) {
        this.sourceBillNo = sourceBillNo;
    }

    public String getShopId() {
        return shopId;
    }

    public void setShopId(String shopId) {
        this.shopId = shopId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof AccountCommissionReq)) {
            return false;
        }
        AccountCommissionReq req = (AccountCommissionReq) o;
        return Objects.equals(getCustomerCode(), req.getCustomerCode()) &&
                Objects.equals(getCustomerType(), req.getCustomerType()) &&
                Objects.equals(getAmount(), req.getAmount()) &&
                Objects.equals(getTradeType(), req.getTradeType()) &&
                Objects.equals(getTradeNo(), req.getTradeNo()) &&
                Objects.equals(getSourceBillNo(), req.getSourceBillNo()) &&
                Objects.equals(getShopId(), req.getShopId()) &&
                Objects.equals(getRemark(), req.getRemark());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getCustomerCode(), getCustomerType(), getAmount(), getTradeType(), getTradeNo(), getSourceBillNo(), getShopId(), getRemark());
    }

    @Override
    public String toString() {
        return "AccountCommissionReq{" +
                "customerCode='" + customerCode + '\'' +
                ", customerType='" + customerType + '\'' +
                ", amount=" + amount +
                ", tradeType='" + tradeType + '\'' +
                ", tradeNo='" + tradeNo + '\'' +
                ", sourceBillNo='" + sourceBillNo + '\'' +
                ", shopId='" + shopId + '\'' +
                ", remark='" + remark + '\'' +
                '}';
    }
}
