package com.akucun.account.proxy.facade.stub.others.account.vo;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 提现结果通知实体
 * <AUTHOR>
 */
public class NotifyWithdrawVO implements Serializable {

    private static final long serialVersionUID = -6768233521843228236L;
    /**
     * 客户编码
     */
    private String customerCode;
    /**
     * 提现金额 金额单位元
     */
    private BigDecimal amount;
    /**
     * 申请编号
     */
    private String sourceBillNo;
    /**
     * 提现结果 true成功 false失败
     */
    private Boolean result;
    /**
     * 提现失败原因 失败时必传
     */
    private String errorMessage;
    /**
     * 客户类型
     */
    private String customerType;

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getSourceBillNo() {
        return sourceBillNo;
    }

    public void setSourceBillNo(String sourceBillNo) {
        this.sourceBillNo = sourceBillNo;
    }

    public Boolean getResult() {
        return result;
    }

    public void setResult(Boolean result) {
        this.result = result;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getCustomerType() {
        return customerType;
    }

    public void setCustomerType(String customerType) {
        this.customerType = customerType;
    }

    @Override
    public String toString() {
        return "NotifyWithdrawDO{" +
                "customerCode='" + customerCode + '\'' +
                ", amount=" + amount +
                ", sourceBillNo='" + sourceBillNo + '\'' +
                ", result=" + result +
                ", errorMessage='" + errorMessage + '\'' +
                ", customerType='" + customerType + '\'' +
                '}';
    }
}
