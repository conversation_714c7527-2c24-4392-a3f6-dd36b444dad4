package com.akucun.account.proxy.facade.stub.aggregation;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.facade.stub.fallback.AccountAggregationTradeFacadeFallbackFactory;
import com.akucun.account.proxy.facade.stub.others.account.req.WithdrawQueryReq;
import com.akucun.account.proxy.facade.stub.others.account.vo.AccountWithdrawVO;
import com.akucun.account.proxy.facade.stub.others.aggregation.req.TenantWithdrawQueryReq;
import com.akucun.account.proxy.facade.stub.others.aggregation.req.WithdrawTaxCalcReq;
import com.akucun.account.proxy.facade.stub.others.aggregation.res.WithdrawServiceFeeSwitchResp;
import com.akucun.account.proxy.facade.stub.others.aggregation.res.WithdrawTaxCalcResp;
import com.akucun.account.proxy.facade.stub.others.aggregation.vo.TenantWithdrawApplyVO;
import com.akucun.fps.account.client.model.vo.WithdrawTaxSwitchVO;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * @Author: silei
 * @Date: 2020/11/12
 * @desc: 聚合层交易相关接口
 */
@FeignClient(value = "account-proxy", fallbackFactory = AccountAggregationTradeFacadeFallbackFactory.class/**,url="127.0.0.1:8083"**/)
@RequestMapping("/api/account/aggregation/trade")
public interface AccountAggregationTradeFacade {

    /**
     * 查询提现明细
     *
     * @param withdrawQueryReq
     * @return
     */
    @PostMapping(value = {"/queryWithdrawDetail"}, produces = {"application/json;charset=utf-8"})
    Result<AccountWithdrawVO> queryWithdrawDetail(@RequestBody WithdrawQueryReq withdrawQueryReq);

//    /**
//     * 店长提现
//     *
//     * @param withdrawAuditDO
//     * @return
//     */
//    @PostMapping(value = {"/agentApplyWithDraw"}, produces = {"application/json;charset=utf-8"})
//    Result<String> agentApplyWithDraw(@RequestBody WithdrawAuditDO withdrawAuditDO);

    /**
     * 饷店小程序-店主提现
     *
     * @param accountWithdrawVO
     * @return
     */
    @PostMapping(value = {"/applyBankWithDraw"}, produces = {"application/json;charset=utf-8"})
    Result<String> applyBankWithDraw(@RequestBody AccountWithdrawVO accountWithdrawVO);

    /**
     * 提现税费试算
     *
     * @param withdrawTaxCalcReq
     * @return
     */
    @PostMapping(value = {"/taxClac"}, produces = {"application/json;charset=utf-8"})
    Result<WithdrawTaxCalcResp> taxClac(@RequestBody WithdrawTaxCalcReq withdrawTaxCalcReq);

    /**
     * 提现扣税开关：on-开
     *
     * @param customerCode
     * @return
     */
    @GetMapping(value = {"/taxSwitch"}, produces = {"application/json;charset=utf-8"})
    Result<WithdrawTaxSwitchVO> taxSwitch(@RequestParam("customerCode") String customerCode);

    /**
     * 提现扣手续费开关：on-开
     * @param identifyNo
     * @return
     */
    @GetMapping(value = {"/serviceFeeSwitch"}, produces = {"application/json;charset=utf-8"})
    Result<WithdrawServiceFeeSwitchResp> serviceFeeSwitch(@RequestParam("identifyNo") String identifyNo);


    /**
     * 查询是否存在处理中的提现
     * @param customerCode
     * @param customerType
     * @return
     */
    @GetMapping(value = {"/queryProcessingWithdraw"}, produces = {"application/json;charset=utf-8"})
    Result<Boolean> queryProcessingWithdraw(@RequestParam("customerCode") String customerCode, @RequestParam("customerType") String customerType);


    /**
     * 申请微信提现
     * @param accountWithdrawVO
     * @return
     */
    @PostMapping(value = {"/applyWechatWithDraw"}, produces = {"application/json;charset=utf-8"})
    Result<String> applyWechatWithDraw(@RequestBody AccountWithdrawVO accountWithdrawVO);

    /**
     * 企业饷店-店主/长提现
     *
     * @param vo
     * @return
     */
    @PostMapping(value = {"/tenantApplyWithDraw"}, produces = {"application/json;charset=utf-8"})
    Result<String> tenantApplyWithDraw(@RequestBody TenantWithdrawApplyVO vo);


    /**
     * 查询提现明细
     *
     * @param req
     * @return
     */
    @PostMapping(value = {"/queryTenantWithdrawDetail"}, produces = {"application/json;charset=utf-8"})
    Result<TenantWithdrawApplyVO> queryTenantWithdrawDetail(@RequestBody TenantWithdrawQueryReq req);


}
