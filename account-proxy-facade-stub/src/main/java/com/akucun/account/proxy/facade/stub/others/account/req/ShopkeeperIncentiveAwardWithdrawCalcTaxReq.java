package com.akucun.account.proxy.facade.stub.others.account.req;

import com.mengxiang.base.common.utils.datamasking.Sensitive;
import com.mengxiang.base.common.utils.datamasking.SensitiveType;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Create on : 2025/2/13 18:22
 **/
@Data
public class ShopkeeperIncentiveAwardWithdrawCalcTaxReq {

    private String withdrawNo;

    private String customerCode;

    @Sensitive(type = SensitiveType.Name)
    private String customerName;

    private String customerType;

    private BigDecimal amount;

    private String identifyNo;

    //是否提交记税，默认为试算
    private Boolean submitTax = false;

    private String remark;

}
