package com.akucun.account.proxy.facade.stub.fallback;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.facade.stub.aggregation.TaxTripPactionApplyFacade;
import com.akucun.account.proxy.facade.stub.others.dto.req.TaxTripPactionApplyReq;
import com.akucun.account.proxy.facade.stub.others.dto.req.TaxTripPactionApplyUpdateReq;
import com.akucun.account.proxy.facade.stub.others.dto.res.TaxTripPactionApplyResp;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;
@Component
public class TaxTripPactionApplyFacadeFallbackFactory implements FallbackFactory<TaxTripPactionApplyFacade> {
    @Override
    public TaxTripPactionApplyFacade create(Throwable cause) {
        return new TaxTripPactionApplyFacade(){

            @Override
            public Result<String> apply(TaxTripPactionApplyReq taxTripPactionApplyReq) {
                Result<String> result = new Result<String>();
                result.error(500, "SERVICE_ERROR-服务超时熔断");
                return result;
            }

            @Override
            public Result<List<TaxTripPactionApplyResp>> findTaxTripPactionApply(String customerCode, String customerType) {
                Result<List<TaxTripPactionApplyResp>> result = new Result<List<TaxTripPactionApplyResp>>();
                result.error(500, "SERVICE_ERROR-服务超时熔断");
                return result;
            }

            @Override
            public Result<Boolean> updateApplyStatus(TaxTripPactionApplyUpdateReq taxTripPactionApplyUpdateReq) {
                Result<Boolean> result = new Result<Boolean>();
                result.error(500, "SERVICE_ERROR-服务超时熔断");
                return result;
            }
        };
    }
}
