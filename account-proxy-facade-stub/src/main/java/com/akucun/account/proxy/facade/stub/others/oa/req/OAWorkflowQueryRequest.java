package com.akucun.account.proxy.facade.stub.others.oa.req;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Create on : 2025/1/14 16:10
 **/
@Data
public class OAWorkflowQueryRequest {

    //流程请求ID
    @NotNull(message = "流程请求ID不能为空")
    private Integer requestId;

    //员工编号对应的OA人员的userId，可不传
    private Integer userId;

}
