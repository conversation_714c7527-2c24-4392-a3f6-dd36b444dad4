package com.akucun.account.proxy.facade.stub.enums;

/**
 * <AUTHOR>    mingruishen
 * @create 2018/2/25
 * app-parent
 */

public class MagicNumber {
    public static final int int_0 = 0;
    public static final int int_1 = 1;
    public static final int int_2 = 2;
    public static final int int_3 = 3;
    public static final int int_4 = 4;
    public static final int int_5 = 5;
    public static final int int_6 = 6;
    public static final int int_7 = 7;
    public static final int int_8 = 8;
    public static final int int_9 = 9;
    public static final int int_10 = 10;
    public static final int int_11 = 11;
    public static final int int_12 = 12;
    public static final int int_13 = 13;
    public static final int int_14 = 14;
    public static final int int_15 = 15;
    public static final int int_16 = 16;
    public static final int int_17 = 17;
    public static final int int_18 = 18;
    public static final int int_19 = 19;
    public static final int int_20 = 20;
    public static final int int_21 = 21;
    public static final int int_22 = 22;
    public static final int int_23 = 23;
    public static final int int_24 = 24;
    public static final int int_25 = 25;
    public static final int int_26 = 26;
    public static final int int_27 = 27;
    public static final int int_28 = 28;
    public static final int int_29 = 29;
    public static final int int_30 = 30;
    public static final int int_31 = 31;
    public static final int int_32 = 32;
    public static final int int_33 = 33;
    public static final int int_34 = 34;
    public static final int int_35 = 35;
    public static final int int_36 = 36;
    public static final int int_37 = 37;
    public static final int int_38 = 38;
    public static final int int_39 = 39;
    public static final int int_40 = 40;
    public static final int int_41 = 41;
    public static final int int_42 = 42;
    public static final int int_43 = 43;
    public static final int int_44 = 44;
    public static final int int_45 = 45;
    public static final int int_46 = 46;
    public static final int int_47 = 47;
    public static final int int_48 = 48;
    public static final int int_49 = 49;
    public static final int int_50 = 50;
    public static final int int_51 = 51;
    public static final int int_52 = 52;
    public static final int int_53 = 53;
    public static final int int_54 = 54;
    public static final int int_55 = 55;
    public static final int int_56 = 56;
    public static final int int_57 = 57;
    public static final int int_58 = 58;
    public static final int int_59 = 59;
    public static final int int_60 = 60;
    public static final int int_61 = 61;
    public static final int int_62 = 62;
    public static final int int_63 = 63;
    public static final int int_64 = 64;
    public static final int int_65 = 65;
    public static final int int_66 = 66;
    public static final int int_67 = 67;
    public static final int int_68 = 68;
    public static final int int_69 = 69;
    public static final int int_70 = 70;
    public static final int int_71 = 71;
    public static final int int_72 = 72;
    public static final int int_73 = 73;
    public static final int int_74 = 74;
    public static final int int_75 = 75;
    public static final int int_76 = 76;
    public static final int int_77 = 77;
    public static final int int_78 = 78;
    public static final int int_79 = 79;
    public static final int int_80 = 80;
    public static final int int_81 = 81;
    public static final int int_82 = 82;
    public static final int int_83 = 83;
    public static final int int_84 = 84;
    public static final int int_85 = 85;
    public static final int int_86 = 86;
    public static final int int_87 = 87;
    public static final int int_88 = 88;
    public static final int int_89 = 89;
    public static final int int_90 = 90;
    public static final int int_91 = 91;
    public static final int int_92 = 92;
    public static final int int_93 = 93;
    public static final int int_94 = 94;
    public static final int int_95 = 95;
    public static final int int_96 = 96;
    public static final int int_97 = 97;
    public static final int int_98 = 98;
    public static final int int_99 = 99;
    public static final int int_100 = 100;
    public static final int int_150 = 150;
    public static final int int_200 = 200;
    public static final int int_256 = 200;
    public static final int int_500 = 500;
    public static final int int_1024 = 1024;
    public static final int int_5000 = 5000;
    public static final int int_1000 = 1000;
    public static final int int_2000 = 2000;
    public static final int int_3000 = 3000;
    public static final int int_365 = 365;
    public static final int int_8888 = 8888;
    public static final int int_8192 = 8192;
    public static final int int_10000 = 10000;
    public static final int int_20000 = 20000;
    public static final int int_30000 = 30000;
    public static final int int_86400 = 86400;
    public static final int int_15000000 = 15000000;
    public static final int int_1073741824 = 1073741824;
    public static final long long_8192 = 8192L;
    public static final long long_1000 = 1000L;
    public static final long long_100 = 100L;
    public static final long long_4096 = 4096L;
    public static final long long_7 = 7L;
    public static final long long_24 = 24L;
    public static final long long_60 = 60L;
    public static final double double_100p0 = 100.0D;
    public static final double double_100 = 100.0D;
    public static final long long_2_power_32 = 8589934592L;
    public static final double row_height = 15.625D;
    public static final Double double_99999999999999_99 = Double.valueOf(9.999999999999998E13D);
    public static final Integer int_f1 = Integer.valueOf(-1);
    public static final Double double_f99999999999999_99 = Double.valueOf(-9.999999999999998E13D);

    /**
     * 对应数据库的数据长度验证：
     */
    public static final int PROP_LENG_20 = 20;
    public static final int PROP_LENG_50 = 50;
    public static final int PROP_LENG_200 = 200;
    public static final int PROP_LENG_1000 = 1000;
    public static final int PROP_LENG_4000 = 4000;

    /**
     * 定义一些常用数字
     */
    public static final int NUMERAL_ZORE = 0;

    public static final String NUMERAL_S_ZORE = "0";
    public static final String NUMERAL_S_ZERE = "0";
    public static final String NUMERAL_S_ONE = "1";
    public static final String NUMERAL_S_TWO = "2";
    public static final String NUMERAL_S_THREE = "3";
    public static final String NUMERAL_S_FOUR = "4";
    public static final String NUMERAL_S_FIVE = "5";
    public static final String NUMERAL_S_SIX = "6";
    public static final String NUMERAL_S_EIGHT = "8";

    public static final int NUMBER_0 = 0;
    public static final int NUMBER_1 = 1;
    public static final int NUMBER_2 = 2;
    public static final int NUMBER_3 = 3;
    public static final int NUMBER_4 = 4;
    public static final int NUMBER_5 = 5;
    public static final int NUMBER_6 = 6;
    public static final int NUMBER_7 = 7;
    public static final int NUMBER_8 = 8;
    public static final int NUMBER_9 = 9;
    public static final int NUMBER_10 = 10;
    public static final int NUMBER_11 = 11;
    public static final int NUMBER_12 = 12;
    public static final int NUMBER_13 = 13;
    public static final int NUMBER_14 = 14;
    public static final int NUMBER_15 = 15;
    public static final int NUMBER_16 = 16;
    public static final int NUMBER_17 = 17;
    public static final int NUMBER_18 = 18;
    public static final int NUMBER_19 = 19;
    public static final int NUMBER_20 = 20;
    public static final int NUMBER_21 = 21;
    public static final int NUMBER_22 = 22;
    public static final int NUMBER_23 = 23;
    public static final int NUMBER_24 = 24;
    public static final int NUMBER_25 = 25;
    public static final int NUMBER_26 = 26;
    public static final int NUMBER_27 = 27;
    public static final int NUMBER_28 = 28;
    public static final int NUMBER_29 = 29;
    public static final int NUMBER_30 = 30;
    public static final int NUMBER_31 = 31;
    public static final int NUMBER_32 = 32;
    public static final int NUMBER_33 = 33;
    public static final int NUMBER_34 = 34;
    public static final int NUMBER_35 = 35;
    public static final int NUMBER_36 = 36;
    public static final int NUMBER_37 = 37;
    public static final int NUMBER_38 = 38;
    public static final int NUMBER_39 = 39;
    public static final int NUMBER_40 = 40;
    public static final int NUMBER_41 = 41;
    public static final int NUMBER_42 = 42;
    public static final int NUMBER_43 = 43;
    public static final int NUMBER_50 = 50;
    public static final int NUMBER_59 = 59;
    public static final int NUMBER_60 = 60;
    public static final int NUMBER_70 = 70;
    public static final int NUMBER_80 = 80;
    public static final int NUMBER_90 = 90;
    public static final int NUMBER_100 = 100;
    public static final int NUMBER_110 = 110;
    public static final int NUMBER_130 = 130;
    public static final int NUMBER_180 = 180;
    public static final int NUMBER_150 = 150;
    public static final int NUMBER_200 = 200;
    public static final int NUMBER_201 = 201;
    public static final int NUMBER_250 = 250;
    public static final int NUMBER_255 = 255;
    public static final int NUMBER_256 = 256;
    public static final int NUMBER_300 = 300;
    public static final int NUMBER_330 = 330;
    public static final int NUMBER_350 = 350;
    public static final int NUMBER_360 = 360;
    public static final int NUMBER_270 = 270;
    public static final int NUMBER_400 = 400;
    public static final int NUMBER_407 = 407;
    public static final int NUMBER_450 = 450;
    public static final int NUMBER_480 = 480;
    public static final int NUMBER_500 = 500;
    public static final int NUMBER_520 = 520;
    public static final int NUMBER_531 = 531;
    public static final int NUMBER_550 = 550;
    public static final int NUMBER_600 = 600;
    public static final int NUMBER_666 = 666;
    public static final int NUMBER_900 = 900;
    public static final int NUMBER_999 = 999;
    public static final int NUMBER_700 = 700;
    public static final int NUMBER_800 = 800;
    public static final int NUMBER_1000 = 1000;
    public static final int NUMBER_1024 = 1024;
    public static final int NUMBER_1500 = 1500;
    public static final int NUMBER_1988 = 1988;
    public static final int NUMBER_2000 = 2000;
    public static final int NUMBER_2048 = 2048;
    public static final int NUMBER_2400 = 2400;
    public static final int NUMBER_2999 = 2999;
    public static final int NUMBER_3000 = 3000;
    public static final int NUMBER_3500 = 3500;
    public static final int NUMBER_3600 = 3600;
    public static final int NUMBER_5000 = 5000;
    public static final int NUMBER_86400 = 86400;
    public static final int NUMBER_15000 = 15000;
    public static final int NUMBER_24000 = 24000;
    public static final int NUMBER_23600 = 23600;

    public static final int NUMBER_99999999 = 99999999;

    public static final long LONG_0 = 0L;
    public static final long LONG_1 = 1L;
    public static final long LONG_7 = 7L;
    public static final long LONG_10 = 10L;
    public static final long LONG_14 = 14L;
    public static final long LONG_21 = 21L;
    public static final long LONG_24 = 24L;
    public static final long LONG_60 = 60L;
    public static final long LONG_100 = 100L;
    public static final long LONG_500 = 500L;
    public static final long LONG_1000 = 1000L;
    public static final long LONG_2000 = 2000L;
    public static final long LONG_2500 = 2500L;


    public static final double DOUBLE_0_72 = 0.72;

    public MagicNumber() {
    }
}

