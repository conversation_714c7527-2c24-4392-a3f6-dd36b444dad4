package com.akucun.account.proxy.facade.stub.others.dto.res;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : 奖励发放请求参数
 * @Create on : 2025/1/7 13:56
 **/
@Data
@NoArgsConstructor
public class BonusPayResp {
    @ApiModelProperty("业务流水号，业务唯一标识")
    private String tradeNo;

    @ApiModelProperty("发起流程的id(异步返回)")
    private String requestNo;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("状态描述")
    private String statusDesc;
}
