package com.akucun.account.proxy.facade.stub.merchant.market;

import com.akucun.account.proxy.facade.stub.fallback.MerchantFullReturnMarketFundFacadeFallbackFactory;
import com.akucun.account.proxy.facade.stub.others.merchant.market.*;
import com.akucun.common.Result;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * 商家营销满返活动资金变更接口
 * @description MerchantFullReturnMarketFundFacade
 * <AUTHOR>
 * @date 2024/4/26 16:42
 * @version v1.0.0
 */
@FeignClient(value = "account-proxy", fallbackFactory = MerchantFullReturnMarketFundFacadeFallbackFactory.class)
@RequestMapping("/api/account/proxy/merchant/fullReturnMarket/fund")
public interface MerchantFullReturnMarketFundFacade {

    /**
     * 活动报名（冻结金额）
     * @param request
     * @return
     */
    @PostMapping(value = "/freeze", produces = "application/json;charset=utf-8")
    Result<Void> freeze(@RequestBody @Validated MerchantFullReturnMarketFreezeRequest request);

    /**
     * 活动未开始取消（解冻金额）
     * @param request
     * @return
     */
    @PostMapping(value = "/unFreeze", produces = "application/json;charset=utf-8")
    Result<Void> unFreeze(@RequestBody @Validated MerchantFullReturnMarketUnFreezeRequest request);

    /**
     * 活动结束 或 定期加冻（加冻）
     * @param request
     * @return
     */
    @PostMapping(value = "/addFreeze", produces = "application/json;charset=utf-8")
    Result<Void> addFreeze(@RequestBody @Validated MerchantFullReturnMarketAddFreezeRequest request);

    /**
     * 活动售后期结束结算
     * @param request
     * @return
     */
    @PostMapping(value = "/settle", produces = "application/json;charset=utf-8")
    Result<Void> settle(@RequestBody @Validated MerchantFullReturnMarketSettleRequest request);

}
