package com.akucun.account.proxy.facade.stub.others.oa.req.business;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Create on : 2025/1/14 20:49
 **/
@NoArgsConstructor
@AllArgsConstructor
@Data
public class MultiSupplierCorporatePaymentRequest {

    @NotBlank(message = "业务编号不能为空")
    private String bizNo;

    //申请人员工编号
    @NotBlank(message = "申请人员工编号不能为空")
    private String applyUserNo;

    //申请人员工名称
    @NotBlank(message = "申请人员工名称不能为空")
    private String applyUserName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @NotNull(message = "申请日期不能为空")
    private Date applyTime;

    //申请金额
    @NotNull(message = "申请金额不能为空")
    @DecimalMin(value = "0.01", message = "申请金额不能小于0.01")
    private BigDecimal amount;

    //税金
    @NotNull(message = "税金不能为空")
    @DecimalMin(value = "0.00", message = "税金不能小于0.00")
    private BigDecimal taxAmount;

    //业务类型：导师激励
    @NotBlank(message = "业务类型不能为空")
    private String bizCategory;

    //付款内容
    private String content;

    //附件
    @NotNull(message = "附件不能为空")
    @Size(min = 1, message = "附件不能为空")
    private List<String> attachment;

    //付款明细
    @Valid
    @NotNull(message = "付款明细不能为空")
    @Size(min = 1, message = "付款明细不能为空")
    private List<PaymentItem> paymentItems;

    //流程状态回调地址
    private String notifyUrl;

    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    public static class PaymentItem {

        //费用描述
        @NotBlank(message = "费用描述不能为空")
        private String description;

        //金额
        @NotNull(message = "明细金额不能为空")
        @DecimalMin(value = "0.01", message = "明细金额不能小于0.01")
        private BigDecimal amount;

        //供应商ID
        @NotBlank(message = "供应商ID不能为空")
        private String supplierId;

    }

}
