package com.akucun.account.proxy.facade.stub.others.account.req;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 啊呀团团长奖励发放请求
 */
@Data
public class AYTTransferReq implements Serializable {
    private static final long serialVersionUID = -7724203829263536390L;

    /**
     * 业务单号，如订单号
     */
    private String sourceBillNo;
    /**
     * 交易流水号（多次请求相同会幂等，重试请保持不变）
     */
    private String tradeNo;

    /**
     * 代购编号
     */
    private String customerCode;

    /**
     * 转入金额,单位：元
     */
    private BigDecimal amount;

    /**
     * 备注
     */
    private String remark;


}
