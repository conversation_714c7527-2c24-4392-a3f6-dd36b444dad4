package com.akucun.account.proxy.facade.stub.pingan;

import com.akucun.account.proxy.facade.stub.fallback.AcquireJointLineNumberFacadeFallbackFactory;
import com.akucun.fps.common.entity.Result;
import com.akucun.fps.common.entity.ResultList;
import com.akucun.fps.pingan.client.model.*;
import com.akucun.fps.pingan.client.model.po.BankNode;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 对外提供获取大小银行联行号服务
 * <AUTHOR>
 *
 */

@FeignClient(value = "account-proxy", fallbackFactory = AcquireJointLineNumberFacadeFallbackFactory.class)
@RequestMapping("/api/account/proxy/pingan")
public interface AcquireJointLineNumberFacade {

    /**
     *根据输入名模糊查询银行（总行名）列表
     * @param bankName 银行名称
     * @return
     */
    @PostMapping(value = "/selectByBankName", produces = "application/json;charset=utf-8")
    ResultList<BankInfoManageResp> selectByBankName(@RequestParam("bankName") String bankName);


    /**
     * 模糊查询银行（总行名）列表
     * @param req
     * @return
     */
    @PostMapping(value = "/selectBankInfoManage", produces = "application/json;charset=utf-8")
    ResultList<BankInfoManageResp> selectBankInfoManage(@RequestBody BankInfoManageReq req);

    /**
     *根据银行名称精确查询银行信息
     * @param bankName
     * @return
     */
    @PostMapping(value = "/selectByName", produces = "application/json;charset=utf-8")
    Result<BankInfoManageResp> selectByName(@RequestParam("bankName") String bankName);

    /**
     * 根据bankCode获取银行信息
     * @param bankCode
     * @return
     */
    @PostMapping(value = "/selectByBankCode", produces = "application/json;charset=utf-8")
    Result<BankInfoManageResp> selectByBankCode(@RequestParam("bankCode") String bankCode);

    /**
     * 获取省列表
     * @return
     */
    @PostMapping(value = "/selectProvince", produces = "application/json;charset=utf-8")
    ResultList<BankNode> selectProvince();

    /**
     * 获取市级信息
     * @param code：省编号
     * @return
     */
    @PostMapping(value = "/selectBankCity", produces = "application/json;charset=utf-8")
    ResultList<BankCityInfo> selectBankCity(@RequestParam("code") String code);

    /**
     * 模糊查询支行信息
     * @return
     */
    @PostMapping(value = "/selectSubBankCityPage", produces = "application/json;charset=utf-8")
    ResultList<SubBankInfoDO> selectSubBankCityPage(@RequestBody SubBankInfoReq request);

    /**
     * 模糊查询农商行、合作社支行信息
     * @param request
     * @return
     */
    @PostMapping(value = "/selectSpecialSubBankCityPage", produces = "application/json;charset=utf-8")
    ResultList<SubBankInfoDO> selectSpecialSubBankCityPage(@RequestBody SubBankInfoReq request);

    /**
     * 查询银行（总行名）列表
     * @param channel
     * @return
     */
    @PostMapping(value = "/selectBankInfoManageByChannel", produces = "application/json;charset=utf-8")
    ResultList<BankInfoManageResp> selectBankInfoManageByChannel(@RequestParam("channel") String channel);
}
