/*
 * @Author: <PERSON>
 * @Date: 2025-04-16 15:32:48
 * @Description: Do not edit
 * @License: Copyright (c) 2025, Lee
 */
package com.akucun.account.proxy.facade.stub.others.account.req;

import com.mengxiang.base.common.model.request.PagingRequest;

import lombok.Data;

@Data
public class OfflineAdjustAccountPageQueryReq extends  PagingRequest {
    /**
     * 客户编码
     */
    private String customerCode;
    /**
     * 审核状态
     */
    private Integer auditStatus;
    /**
     * 交易单号
     */
    private String tradeNo;
    /**
     * 账户类型Key
     */
    private String accountTypeKey;
    /**
     * 交易类型
     */
    private String tradeType;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建时间开始
     */
    private String createTimeStart;
    /**
     * 创建时间结束
     */
    private String createTimeEnd;

    /**
     * 来源单号
     */
    private String sourceBillNo;

    /** 
     * 调账状态, 0:待调账, 1:调账成功, 2:调账失败
     */
    private Integer adjustStatus;

    /**
     * 排序字段
     */
    private String orderBy = "id desc";

    /**
     * 最小id, 导出时使用
     */
    private Long minId;
}