/*
 * @Author: <PERSON>
 * @Date: 2025-04-17 11:10:46
 * @Description: Do not edit
 * @License: Copyright (c) 2025, Lee
 */
package com.akucun.account.proxy.facade.stub.others.account.req;

import java.util.List;

import lombok.Data;

@Data
public class OfflineAdjustAccountBatchRequest {

    /**
     * 主键ID
     */
    private List<Long> ids;

    /**
     * 操作人
     */
    private String operator;

}
