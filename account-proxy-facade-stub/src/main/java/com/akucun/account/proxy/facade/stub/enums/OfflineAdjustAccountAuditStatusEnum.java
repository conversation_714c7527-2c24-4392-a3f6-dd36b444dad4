/*
 * @Author: <PERSON>
 * @Date: 2025-04-16 17:54:50
 * @Description: 线下调账审核状态枚举
 * @License: Copyright (c) 2025, Lee
 */
package com.akucun.account.proxy.facade.stub.enums;

public enum OfflineAdjustAccountAuditStatusEnum {
    /**
     * 待审核
     */
    PENDING(0),
    /**
     * 审核通过
     */
    APPROVED(1),
    /**
     * 审核拒绝
     */
    REJECTED(2);

    private int code;

    OfflineAdjustAccountAuditStatusEnum(int code) {
        this.code = code;
    }

    public int getCode() {
        return code;
    }
}
