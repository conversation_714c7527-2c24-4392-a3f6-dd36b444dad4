package com.akucun.account.proxy.facade.stub.others.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : 税金查询等业务-请求参数
 * @Create on : 2025/1/14 15:52
 **/
@Data
@NoArgsConstructor
public class TaxReq {
    @ApiModelProperty("业务类型:EMPOWERMENT_CAMP 赋能营 MONTHLY_BONUS 月勤奖 MENTOR_BONUS 导师激励")
    //@NotBlank(message = "业务类型不能为空")
    private String bizType;

    @ApiModelProperty("用户code")
    @NotBlank(message = "用户编码不能为空")
    private String userCode;

    @ApiModelProperty("用户类型，不填默认NM")
    private String userType;

    @ApiModelProperty("业务金额")
    @NotNull(message = "业务金额不能为空")
    private BigDecimal amount;
}
