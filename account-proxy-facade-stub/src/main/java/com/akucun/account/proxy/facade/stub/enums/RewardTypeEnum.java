package com.akucun.account.proxy.facade.stub.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Create on : 2025/1/15 13:52
 **/
public enum RewardTypeEnum {
    EMPOWERMENT_CAMP("EMPOWERMENT_CAMP", "赋能营"),
    MONTHLY_BONUS("MONTHLY_BONUS", "月勤奖"),
    MENTOR_BONUS("MENTOR_BONUS", "导师激励"),
    ;
    @Getter
    private String code;
    @Getter
    private String codeDesc;

    RewardTypeEnum(String code, String codeDesc) {
        this.code = code;
        this.codeDesc = codeDesc;
    }


    public static String queryDescByCode(String code) {
        for (RewardTypeEnum item : RewardTypeEnum.values()) {
            if (item.getCode().equalsIgnoreCase(code)) {
                return item.getCodeDesc();
            }
        }

        return null;
    }

    public static RewardTypeEnum getByCode(String code) {
        for (RewardTypeEnum item : RewardTypeEnum.values()) {
            if (item.getCode().equalsIgnoreCase(code)) {
                return item;
            }
        }

        return null;
    }

}
