package com.akucun.account.proxy.facade.stub.fallback;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.facade.stub.account.AccountShopkeeperPayFacade;
import com.akucun.account.proxy.facade.stub.others.account.req.AccountShopkeeperPayReq;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class AccountShopkeeperPayFacadeFallbackFactory implements FallbackFactory<AccountShopkeeperPayFacade> {

    @Override
    public AccountShopkeeperPayFacade create(Throwable throwable) {
        return new AccountShopkeeperPayFacade() {
            @Override
            public Result<Void> addAccountShopkeeperPayList(AccountShopkeeperPayReq accountShopkeeperPayReq) {
                return Result.error(500,"服务超时熔断");
            }
        };
    }
}
