package com.akucun.account.proxy.facade.stub.others.dto.req;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Create on : 2025/1/20 09:59
 **/
@Data
@NoArgsConstructor
public class MentorInfoDTO {
    private Long id;

    private String userCode;

    private String userType;

    private String userNickName;

    private String payeeName;

    private String payeeBankAddress;

    private String payeeBankNo;

    private String merchantId;

    private String merchantCode;

    private Date createTime;

    private Date updateTime;
}
