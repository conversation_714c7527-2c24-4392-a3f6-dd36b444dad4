package com.akucun.account.proxy.facade.stub.others.aggregation.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 提现请求参数
 * <AUTHOR>
 */
@ApiModel("提现请求参数")
@Data
public class WithdrawAuditReq implements Serializable {
    private static final long serialVersionUID = -8791282407340569661L;

    @NotBlank(message = "客户编码不能为空")
    @ApiModelProperty(value = "客户编码", required = true)
    private String customerCode;

    @NotBlank(message = "客户名称不能为空")
    @ApiModelProperty(value = "客户名称",required = true)
    private String customerName;

    @NotBlank(message = "客户类型不能为空")
    @ApiModelProperty(value = "客户类型", required = true)
    private String customerType;

    @ApiModelProperty(value = "来源单号")
    private String sourceBillNo;

    @NotBlank(message = "银行名称不能为空")
    @ApiModelProperty(value = "银行名称", required = true)
    private String bankName;

    @NotBlank(message = "银行卡号不能为空")
    @ApiModelProperty(value = "银行卡号", required = true)
    private String bankNo;

    @NotBlank(message = "提现金额不能为空")
    @ApiModelProperty(value = "提现金额", required = true)
    private BigDecimal amount;

    @ApiModelProperty(value = "服务费")
    private BigDecimal serviceAmount;

    @ApiModelProperty(value = "修改人")
    private String modifyUser;

    @NotBlank(message = "申请人不能为空")
    @ApiModelProperty(value = "申请人", required = true)
    private String applyUser;

    @ApiModelProperty(value = "备注")
    private String remark;

    private String decBankNo;

    private String desBankCode;

    private String failReason;

    @ApiModelProperty(value = "提现渠道")
    private String withdrawChannel;

    /**
     *  h5代理类型专用
     */
    @ApiModelProperty(value = "店主id")
    private String shopId;

    @ApiModelProperty(value = "微信openId")
    private String openId;

    @ApiModelProperty(value = "身份证")
    private String identifyNo;

    @ApiModelProperty(value = "备注")
    private String month;

    @ApiModelProperty(value = "税费")
    private BigDecimal taxFee;

    @ApiModelProperty(value = "实际到账")
    private BigDecimal withdraw;

    @ApiModelProperty(value = "银行图标")
    private String bankLogo;

    @ApiModelProperty(value = "客户等级")
    private String customerGrade;

}
