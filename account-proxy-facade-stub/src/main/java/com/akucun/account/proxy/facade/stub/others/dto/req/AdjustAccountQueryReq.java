package com.akucun.account.proxy.facade.stub.others.dto.req;

import com.akucun.account.proxy.facade.stub.others.account.vo.AdjustAccountVo;
import lombok.Data;

import java.util.Date;

/**
 * 调账查询请求体
 */
@Data
public class AdjustAccountQueryReq {

    /**
     * 用户编码
     */
    private String customerCode;
    /**
     *状态：0：处理中，1：成功，2：失败，3：异常
     */
    private String status;
    /**
     *开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;
    /**
     * 当前页码
     */
    private Integer pageNum;
    /**
     * 每页查询条数
     */
    private Integer pageSize;


}
