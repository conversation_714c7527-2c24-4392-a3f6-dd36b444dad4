package com.akucun.account.proxy.facade.stub.others.dto.req;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class TaxTripPactionApplyReq {

    /**
     * 用户ID，调用会员侧接口的入参
     */
    private String userId;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 客户编码
     */
    private String customerCode;

    /**
     * 客户类型
     */
    private String customerType;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 银行编码
     */
    private String bankCode;

    /**
     * 银行卡号
     */
    private String bankCardNo;

    /**
     * 协议图片路径
     */
    private String pactionImgUrl;
}
