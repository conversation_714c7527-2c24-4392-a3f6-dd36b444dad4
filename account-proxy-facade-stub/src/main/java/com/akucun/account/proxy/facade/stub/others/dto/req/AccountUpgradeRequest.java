package com.akucun.account.proxy.facade.stub.others.dto.req;

import com.mengxiang.base.common.utils.datamasking.Sensitive;
import com.mengxiang.base.common.utils.datamasking.SensitiveType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @Author: silei
 * @Date: 2020/8/26
 * @desc: 账户交易请求体
 */
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("账户升级请求实体")
public class AccountUpgradeRequest implements Serializable {

    private static final long serialVersionUID = -1347899873556975884L;
    /**
     * 客户编码
     */
    @NotBlank(message = "客户编码不能为空")
    @ApiModelProperty(value = "客户编码", required = true)
    private String customerCode;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    private String customerName;

    /**
     * 昵称
     */
    @ApiModelProperty(value = "昵称")
    private String nickName;
    /**
     * 手机号
     */
    @Sensitive(type = SensitiveType.Phone)
    @ApiModelProperty(value = "手机号")
    private String mobile;

    /**
     * 客户类型
     */
    @NotBlank(message = "客户类型不能为空")
    @ApiModelProperty(value = "客户类型", required = true)
    private String customerType;

    /**
     * 升级类型
     */
    @ApiModelProperty(value = "升级类型", required = true)
    private String upgradeType;

    /**
     * 保留域
     */
    @ApiModelProperty(value = "保留域")
    private String reserve;

    /**
     * 账户属性   注意：该字段是否要传，请咨询接口提供方
     * 对应枚举：AccountPropertyType
     */
    @ApiModelProperty(value = "账户属性 ")
    private String accountProperty;


    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getCustomerType() {
        return customerType;
    }

    public void setCustomerType(String customerType) {
        this.customerType = customerType;
    }

    public String getUpgradeType() {
        return upgradeType;
    }

    public void setUpgradeType(String upgradeType) {
        this.upgradeType = upgradeType;
    }

    public String getReserve() {
        return reserve;
    }

    public void setReserve(String reserve) {
        this.reserve = reserve;
    }

    public String getAccountProperty() {
        return accountProperty;
    }

    public void setAccountProperty(String accountProperty) {
        this.accountProperty = accountProperty;
    }

    @Override
    public String toString() {
        return "AccountUpgradeRequest{" +
                "customerCode='" + customerCode + '\'' +
                ", customerName='" + customerName + '\'' +
                ", nickName='" + nickName + '\'' +
                ", mobile='" + mobile + '\'' +
                ", customerType='" + customerType + '\'' +
                ", upgradeType='" + upgradeType + '\'' +
                ", reserve='" + reserve + '\'' +
                ", accountProperty='" + accountProperty + '\'' +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }

        if (!(o instanceof AccountUpgradeRequest)) {
            return false;
        }

        AccountUpgradeRequest request = (AccountUpgradeRequest) o;

        return new EqualsBuilder()
                .append(getCustomerCode(), request.getCustomerCode())
                .append(getCustomerName(), request.getCustomerName())
                .append(getNickName(), request.getNickName())
                .append(getMobile(), request.getMobile())
                .append(getCustomerType(), request.getCustomerType())
                .append(getUpgradeType(), request.getUpgradeType())
                .append(getReserve(), request.getReserve())
                .append(getAccountProperty(), request.getAccountProperty())
                .isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37)
                .append(getCustomerCode())
                .append(getCustomerName())
                .append(getNickName())
                .append(getMobile())
                .append(getCustomerType())
                .append(getUpgradeType())
                .append(getReserve())
                .append(getAccountProperty())
                .toHashCode();
    }
}
