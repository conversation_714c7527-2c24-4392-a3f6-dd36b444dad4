package com.akucun.account.proxy.facade.stub.account;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.facade.stub.fallback.PayTransferFallbackFactory;
import com.akucun.account.proxy.facade.stub.others.account.vo.PinganDecoupleAccountVO;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @Author: silei
 * @Date: 2021/12/20
 * @desc:
 */
@FeignClient(value = "account-proxy", fallbackFactory = PayTransferFallbackFactory.class/**,url="127.0.0.1:8083"**/)
@RequestMapping("/api/account/proxy/pinganDecouple")
public interface PinganDecoupleFacade {


    /**
     * 保存解耦灰度账户
     * @param pinganDecoupleAccountVO
     * @return
     */
    @Deprecated
    @PostMapping(value = "/saveOrUpdate")
    Result<Void> saveOrUpdate(@RequestBody @Validated PinganDecoupleAccountVO pinganDecoupleAccountVO);


    /**
     * 检查是否灰度白名单
     * @param pinganDecoupleAccountVO
     * @return
     */
    @PostMapping(value = "/checkWhiteList")
    Result<Boolean> checkGrayWhitelist(@RequestBody @Validated PinganDecoupleAccountVO pinganDecoupleAccountVO);


    @PostMapping(value = "/accountAntiClearing")
    Result<Void> accountAntiClearing(@RequestParam("beginIndex") int beginIndex, @RequestParam("endIndex") int endIndex);


    @PostMapping(value = "/shopAgentWhitelistProcess")
    Result<Void> shopAgentWhitelistProcess(@RequestParam("beginIndex") int beginIndex, @RequestParam("endIndex") int endIndex);
    
    @PostMapping(value = "/tenantGray")
    Result<Boolean> tenantGray(@RequestBody @Validated PinganDecoupleAccountVO vo);
}
