package com.akucun.account.proxy.facade.stub.others.aggregation.res;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 提现扣税明细返回参数
 *
 * <AUTHOR>
 * @version [版本号, 2020年7月8日]
 */
public class WithdrawTaxDetailResp implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 3029204703656344534L;

    private Long id;
    /**
     * 月份
     */
    private String month;
    /**
     * 身份证
     */
    private String identifyNo;
    /**
     * 客户编码
     */
    private String customerCode;
    /**
     * 客户类型:NM-店主，NMDL-店长
     */
    private String customerType;
    /**
     * 客户名称
     */
    private String customerName;
    /**
     * 身份类别
     */
    private String identifyCategory;
    /**
     * 提现申请编号
     */
    private String withdrawNo;
    /**
     * 提现总金额
     */
    private BigDecimal amount;
    /**
     * 实际到账金额
     */
    private BigDecimal withdraw;
    /**
     * 扣税金额
     */
    private BigDecimal taxFee;
    /**
     * 状态：DOING-提现中,SUCC-提现成功,FAIL-提现失败
     */
    private String status;
    /**
     * 备注
     */
    private String remark;
    /**
     * 提现申请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * 提现申请时间：yyyy-MM-dd HH:mm:ss
     */
    private String createTimeStr;

    public String getMonth() {
        return month;
    }

    public void setMonth(String month) {
        this.month = month;
    }

    public String getIdentifyNo() {
        return identifyNo;
    }

    public void setIdentifyNo(String identifyNo) {
        this.identifyNo = identifyNo;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getCustomerType() {
        return customerType;
    }

    public void setCustomerType(String customerType) {
        this.customerType = customerType;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getIdentifyCategory() {
        return identifyCategory;
    }

    public void setIdentifyCategory(String identifyCategory) {
        this.identifyCategory = identifyCategory;
    }

    public String getWithdrawNo() {
        return withdrawNo;
    }

    public void setWithdrawNo(String withdrawNo) {
        this.withdrawNo = withdrawNo;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getWithdraw() {
        return withdraw;
    }

    public void setWithdraw(BigDecimal withdraw) {
        this.withdraw = withdraw;
    }

    public BigDecimal getTaxFee() {
        return taxFee;
    }

    public void setTaxFee(BigDecimal taxFee) {
        this.taxFee = taxFee;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateTimeStr() {
        return createTimeStr;
    }

    public void setCreateTimeStr(String createTimeStr) {
        this.createTimeStr = createTimeStr;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
}
