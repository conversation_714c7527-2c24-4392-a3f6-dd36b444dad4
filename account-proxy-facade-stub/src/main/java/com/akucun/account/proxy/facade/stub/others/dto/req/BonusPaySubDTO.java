package com.akucun.account.proxy.facade.stub.others.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : 奖励金下发申请明细
 * @Create on : 2025/1/14 17:52
 **/
@Data
@NoArgsConstructor
public class BonusPaySubDTO {
    @ApiModelProperty("用户code")
    @NotBlank(message = "店主的爱豆编号不能为空")
    private String userCode;

    @ApiModelProperty("用户类型，不填默认NM")
    private String userType;

    @ApiModelProperty("下发金额，单位元，保持2为小数")
    @NotNull(message = "下发金额不能为空")
    private BigDecimal amount;

    @ApiModelProperty("税额，单位元，保持2为小数")
    @NotNull(message = "税额不能为空")
    private BigDecimal taxAmount;

    @ApiModelProperty("认证类型，以会员提供的枚举值为准（-2 个人临登,-1:个人店铺声明,0：个人身份证认证，1:个体临时认证，2：个体自有渠道认证，3:企业认证）")
    private Integer userGrade;

    @ApiModelProperty("认证渠道，以会员提供的枚举值为准（认证渠道，以会员提供的枚举值为准）")
    private Integer gradeChannel;

    @ApiModelProperty("关联的业务单号")
    private String sourceBizNo;

    @ApiModelProperty("电子发票的OBS地址，长期有效")
    private String invoiceUrl;

}
