package com.akucun.account.proxy.facade.stub.others.dto.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Create on : 2025/1/15 16:16
 **/
@Data
@NoArgsConstructor
public class BonusPaySubmitReq {
    @ApiModelProperty("业务流水号，业务唯一标识")
    @NotBlank(message = "业务流水号不能为空")
    private String tradeNo;

    @ApiModelProperty("业务类型:EMPOWERMENT_CAMP 赋能营 MONTHLY_BONUS 月勤奖 MENTOR_BONUS 导师激励")
    @NotBlank(message = "业务类型不能为空")
    private String bizType;

    @ApiModelProperty("请人员工编号")
    //@NotBlank(message = "请人员工编号不能为空")
    private String applyUserNo;

    @ApiModelProperty("申请人员工名称")
    //@NotBlank(message = "申请人员工名称不能为空")
    private String applyUserName;

    @ApiModelProperty("业务发生账期：YYYYMM,固定格式")
    private String transBillDate;

    @ApiModelProperty("总下发金额，单位元，保持2为小数")
    private BigDecimal totalAmount;

    @ApiModelProperty("总税额，单位元，保持2为小数")
    private BigDecimal totalTaxAmount;

    @ApiModelProperty("业务发生时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date transTime;

    @ApiModelProperty("所属活动编码")
    private String activityNo;

    @ApiModelProperty("备注信息")
    //@NotBlank(message = "备注信息不能为空")
    private String remark;

    @ApiModelProperty("扩展字段1")
    private HashMap<String,String> ext1;

    @ApiModelProperty("所有附件列表")
    private List<String> allFjs;
}
