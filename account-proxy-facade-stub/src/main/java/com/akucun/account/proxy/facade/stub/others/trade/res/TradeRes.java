package com.akucun.account.proxy.facade.stub.others.trade.res;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * 交易结果
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class TradeRes {

    private TradeStatusEnum status;//交易状态

    private String failCode;//失败编码

    private String failMessage;//失败信息

    private List<TradePhaseRes> phaseResList;//阶段结果

}
