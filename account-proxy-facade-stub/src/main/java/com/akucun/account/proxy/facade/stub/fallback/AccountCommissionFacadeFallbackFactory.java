package com.akucun.account.proxy.facade.stub.fallback;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.facade.stub.commission.AccountCommissionFacade;
import com.akucun.account.proxy.facade.stub.others.trade.req.AccountCommissionReq;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * @Author: silei
 * @Date: 2020/12/7
 * @desc:
 */
@Component
public class AccountCommissionFacadeFallbackFactory implements FallbackFactory<AccountCommissionFacade> {

    @Override
    public AccountCommissionFacade create(Throwable throwable) {
        return new AccountCommissionFacade() {

            @Override
            public Result<Void> allocateCommission(AccountCommissionReq req) {
                return Result.error(500, "服务超时熔断");
            }
        };
    }
}
