package com.akucun.account.proxy.facade.stub.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: silei
 * @Date: 2020/8/27
 * @desc:
 */
@AllArgsConstructor
@Getter
public enum ResponseEnum  {

    /**
     * 请求响应枚举
     */
    SUCCESS(0, "成功"),
    PROCESS(1, "处理中"),
    FAIL(2, "失败"),

    SYSTEM_ERROR(500, "服务器内部错误"),

    SYSTEM_EXCEPTION(100001, "系统异常"),
    PARAM_EXCEPTION(100002, "参数异常"),
    SUBMIT_EXCEPTION(100003, "提交异常"),
    SERVER_RESP_ERROR(100004, "服务响应异常，请稍后重试"),
    BIND_CARD_ERROR(100005, "您的银行卡还未绑定，请确认绑卡后再进行提现"),
    ENCRYPT_FAIL(100006, "调用加密服务返回状态失败"),
    ENCRYPT_ERROR(100007, "调用加密服务异常"),
    DUPLICATE_ERROR(100008, "重复请求记录"),
    WITHDRAW_INSERT_ERROR(100009, "提现记录入库异常"),
    TAX_INSERT_ERROR(100010, "税费记录入库异常"),
    WITHDRAW_TAX_ERROR(100011, "用户提现扣税并发异常"),
    ACCOUNT_CENTER_EXCEPTION(100012, "调用账户中心异常"),
    WITHDRAW_PARAM_EXCEPTION(100013, "申请提现参数缺失"),
    TAX_CALC_ERROR(100014, "税费试算异常"),

    ACCORE_101500(101500, "申请提现参数缺失"),
    ACCORE_101501(101501, "申请提现异常"),
    ACCORE_101502(101502, "提现账户不存在，请稍后重试"),
    ACCORE_101503(101503, "账户余额不足，请核实"),
    ACCORE_101504(101504, "调用加密服务异常"),
    ACCORE_101505(101505, "调用加密服务返回状态失败"),
    ACCORE_101506(101506, "提现申请事物执行异常"),
    ACCORE_101507(101507, "插入提现申请表失败"),
    ACCORE_101508(101508, "提现申请更改账户余额失败"),
    ACCORE_101509(101509, "提现金额需大于0"),
    ACCORE_101510(101510, "您的银行卡还未绑定，请确认绑卡后再进行提现"),
    ACCORE_101511(101511, "提现操作频繁,请稍后再试"),
    ACCORE_101512(101512, "提现税费计算异常,请稍后再试"),
    ACCORE_101513(101513, "店长提现shopId不能为空"),
    ACCORE_101515(101515, "提现审核账号解密异常"),
    ACCORE_101516(101516, "提现手续费计算异常,请稍后再试"),

    ACCORE_101520(101520, "微信申请提现参数缺失"),
    ACCORE_101521(101521, "微信提现超过限额"),
    ACCORE_101524(101524, "本月累计提现达到阈值需升级到临时个体"),
    ACCORE_101525(101525, "本月累计提现达到阈值需升级到个体工商户"),
    ACCORE_101526(101526, "本月累计提现达到阈值需升级到企业"),
    ACCORE_101527(101527, "账户不支持提现到微信"),
    ACCORE_101528(101528, "账户类型不支持提现到微信"),
    ACCORE_101529(101529, "微信提现累计金额计算异常,请稍后再试"),

    ACCORE_100900(100900, "插入去重表异常"),
    ACCORE_100901(100901, "添加删除去重任务异常"),
    ACCORE_100902(100902, "删除去重表数据异常"),
    ACCORE_100903(100903, "插入去重表出现并发"),


    AMOUNT_COLLECT_EXCEPTION(200001, "账户资金归集入库异常"),
    AMOUNT_ALLOCATE_EXCEPTION(200002, "账户资金归集状态更新异常"),
    STEP_EXEC_CONCURRENCY(200004, "步骤执行并发异常"),
    MERCHANT_NOT_EXIST(200003, "余额账户不存在"),
    FREEZE_AMOUNT_EXIST(200005, "存在在途金额"),
    RETRY_TIMES_LIMIT(200006, "重试次数超限"),
    PROCESSING_EXIST(200007, "存在处理中记录"),

    LACK_OF_PARAM(200008, "缺少必要参数"),
    NO_PINGAN_ACCOUNT(200009, "平安账户未开通，请开通后进行操作"),
    ACCOUNT_UPGRADE_LOCK(200010, "账户被锁定，请稍后重试"),
    ALLOCATE_COMMISSION_EXCEPTION(200011, "账户分佣异常，请稍后重试"),
    ACCOUNT_INFORMATION_EXCEPTION(200012, "获取会员信息失败，请稍后重试"),
    DEAL_ACCOUNT_ERROR(200013, "账户处理异常，请稍后重试"),
    BONUS_QUERY_ERROR(200014, "奖励金查询失败，请稍后重试"),
    BONUS_ACCT_QUERY_ERROR(200015, "奖励金账户查询失败，请稍后重试"),
    ACCOUNT_TYPE_KEY_ERROR(200016, "accountTypeKey无效"),
    BONUS_ACCOUNT_TRADE_ERROR(200017, "奖励金账户交易异常"),
    ALLOCATE_COMMISSION_PROCESSING(200018, "账户分佣处理中"),
    NO_CENTER_ACCOUNT(200019, "账户中心账户未开通，请开通后进行操作"),
    STEP_EXEC_EXCEPTION(200020, "步骤执行异常"),

    VIPCARD_RECORD_NOT_EXISTS(200100, "该卡片不存在！暂时无法使用"),
    VIPCARD_STATUS_ACTIVATE(200101, "该卡片已被激活！无法重复使用"),
    VIPCARD_TIME_EXPIRED(200102, "该卡片已过有效期！暂时无法使用"),
    VIPCARD_SAME_CUSTOMER_BIND(200103, "同一爱豆只能绑定一张VIP卡片"),
    VIPCARD_WRITEOFF_TOO_OFEN(200104, "该卡片正在激活中，请勿重复操作"),

    TRADE_GET_ACCOUNT_BRANCH_ERROR(300001, "调用新账户余额系统获取分支异常"),
    TRADE_PAY_ERROR(300002, "余额支付异常"),
    TRADE_PAY_RETURN_NULL(300003, "余额支付异常,返回空"),
    TRADE_NOT_EXIST(300004, "支付记录不存在"),
    TRADE_NOT_CONFIG(300005, "支付没有配置该渠道对应的账号key和type"),
    TRADE_NOT_SUPPORT_OPENAPI(300006, "渠道不支持信用支付"),
    TRADE_ROUTE_NOT_EXIST(300007, "支付路由记录不存在"),
    TRADE_REFUND_NOT_CONFIG(300008, "退款没有配置该渠道对应的账号key和type"),


    TRANSFER_TYPE_NOT_SUPPORT(300050, "付款方式不支持"),
    TRANSFER_SAVE_ERROR(300051, "付款数据保存失败"),
    TRANSFER_SYSTEM_EXCEPTION(300052, "付款系统异常"),
    TRANSFER_SYSTEM_ERROR(300053, "付款系统错误"),
    TRANSFER_QUERY_SYSTEM_EXCEPTION(300054, "付款查询系统异常"),
    TRANSFER_QUERY_SYSTEM_ERROR(300055, "付款查询系统错误"),
    TRANSFER_TOO_OFTEN(300056, "付款进行中，请勿重复操作"),
    TRANSFER_ERROR(300057, "付款失败"),
    TRANSFER_MERCHAT_NULL(300058, "付款商户号为空"),
    TRANSFER_QUERY_EMPTY_RESULT(300059, "查询结果为空"),


    BATCH_TRANSFER_QUERY_ERROR(300100,"批量转账查询结果异常"),
    BATCH_TRANSFER_RESULT_PARAM_ERROR(300101,"微信批量转账查询结果参数缺失"),
    BATCH_TRANSFER_EMPTY_RESULT(300102, "批量转账查询结果为空"),

    BONUS_PAY_EXCEPTION(300200, "奖励金支付异常"),

    UPGRADE_CHECK_ERROR(300300, "实名变更验证出错，请联系客服"),
    UPGRADE_CHECK_EXCEPTION(300301, "实名变更验证异常，请稍后重试"),
    UPGRADE_AMOUNT_REMAIN(300302, "账户余额不为0，不能进行实名变更"),
    UPGRADE_HAS_PROCESSING_TRADE(300303, "账户存在处理中的交易，请稍后重试"),

    ;

    private Integer code;

    private String message;


}
