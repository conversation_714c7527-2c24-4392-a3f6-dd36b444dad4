package com.akucun.account.proxy.facade.stub.enums;

import lombok.Getter;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Create on : 2024/11/26 13:58
 **/
@Getter
public enum CompensationFillPayTypeEnum {
    SHIPPING_FEE_COMPENSATION("SHIPPING_FEE_COMPENSATION","运费补偿"),
    GOODS_FEE_COMPENSATION("GOODS_FEE_COMPENSATION","补货款"),
    SHIPPING_INSURANCE_CLAIM("SHIPPING_INSURANCE_CLAIM","运费险理赔"),
    ORDER_COMPENSATION("ORDER_COMPENSATION","订单赔付"),
        ;

    private String code;
    private String msg;

    CompensationFillPayTypeEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static CompensationFillPayTypeEnum getByCode(String code){
        if(StringUtils.isEmpty(code)){
            return null;
        }

        for(CompensationFillPayTypeEnum typeEnum : CompensationFillPayTypeEnum.values()){
            if(code.equalsIgnoreCase(typeEnum.getCode())){
                return typeEnum;
            }
        }

        return null;
    }
}
