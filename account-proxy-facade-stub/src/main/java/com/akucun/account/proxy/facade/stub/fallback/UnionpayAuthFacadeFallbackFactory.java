package com.akucun.account.proxy.facade.stub.fallback;

import com.akucun.fps.common.entity.Result;
import com.akucun.fps.pingan.client.model.PingAnBindingRegisterAccountReqDO;
import com.akucun.fps.pingan.client.model.PingAnBindingRegisterAccountRespDO;
import com.akucun.fps.pingan.client.vo.PinganCardVO;
import com.akucun.account.proxy.facade.stub.pingan.UnionpayAuthFacade;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

@Component
public class UnionpayAuthFacadeFallbackFactory implements FallbackFactory<UnionpayAuthFacade> {
    @Override
    public UnionpayAuthFacade create(Throwable throwable) {
        return new UnionpayAuthFacade() {
            @Override
            public Result<String> unionpayAuthApply(PinganCardVO pinganCard) {
                Result<String> result = new Result<String>();
                result.setError(500, "SERVICE_ERROR", "服务超时熔断");
                return result;
            }

            @Override
            public Result<PingAnBindingRegisterAccountRespDO> unionpayAuthConfirm(PingAnBindingRegisterAccountReqDO account) {
                Result<PingAnBindingRegisterAccountRespDO> result = new Result<PingAnBindingRegisterAccountRespDO>();
                result.setError(500, "SERVICE_ERROR", "服务超时熔断");
                return result;
            }
        };
    }
}
