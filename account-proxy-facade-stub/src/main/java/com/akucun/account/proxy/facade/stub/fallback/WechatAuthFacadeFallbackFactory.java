package com.akucun.account.proxy.facade.stub.fallback;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.facade.stub.account.WechatAuthFacade;
import com.akucun.account.proxy.facade.stub.others.account.vo.WechatInfoBindVO;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * @Author: silei
 * @Date: 2021/5/1
 * @desc:
 */
@Component
public class WechatAuthFacadeFallbackFactory implements FallbackFactory<WechatAuthFacade> {
    @Override
    public WechatAuthFacade create(Throwable throwable) {
        return new WechatAuthFacade() {

            @Override
            public Result<Void> bindInfo(WechatInfoBindVO vo) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<Void> unbind(String customerCode, String customerType) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<WechatInfoBindVO> queryBindInfo(String customerCode, String customerType) {
                return Result.error(500, "服务超时熔断");
            }
        };
    }
}
