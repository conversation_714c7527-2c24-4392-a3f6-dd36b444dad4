package com.akucun.account.proxy.facade.stub.fallback;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.facade.stub.account.AccountTradeFacade;
import com.akucun.account.proxy.facade.stub.others.dto.req.AccountTradeRequest;
import com.akucun.account.proxy.facade.stub.others.dto.res.AccountTradeResponse;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2020/12/22 20:46
 */
@Component
public class AccountTradeFacadeFallbackFactory implements FallbackFactory<AccountTradeFacade> {
    @Override
    public AccountTradeFacade create(Throwable throwable) {
        return new AccountTradeFacade() {
            @Override
            public Result<Boolean> bonusAccountTrade(AccountTradeRequest request) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<AccountTradeResponse> bonusAccountQuery(String tradeNo, Integer tradeType) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<Boolean> akcAccountTrade(AccountTradeRequest request) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<AccountTradeResponse> akcAccountQuery(String tradeNo, Integer tradeType) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<Boolean> openApiAccountTrade(AccountTradeRequest request) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<AccountTradeResponse> openApiAccountQuery(String tradeNo, Integer tradeType) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<Boolean> bonusAccountRefund(AccountTradeRequest request) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<Boolean> akcAccountRefund(AccountTradeRequest request) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<Boolean> openApiAccountRefund(AccountTradeRequest request) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<Boolean> xdAccountTrade(AccountTradeRequest request) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<AccountTradeResponse> xdAccountQuery(String tradeNo, Integer tradeType) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<Boolean> pointAccountTrade(AccountTradeRequest request) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<AccountTradeResponse> pointAccountQuery(String tradeNo, Integer tradeType) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<Boolean> xdAccountRefund(AccountTradeRequest request) {
                return Result.error(500, "服务超时熔断");
            }
        };
    }
}
