package com.akucun.account.proxy.facade.stub.account;

import com.aikucun.common2.base.Pagination;
import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.facade.stub.fallback.AccountCenterFacadeFallbackFactory;
import com.akucun.account.proxy.facade.stub.others.dto.req.AccountAdjustReq;
import com.akucun.account.proxy.facade.stub.others.dto.req.AdjustAccountQueryReq;
import com.akucun.account.proxy.facade.stub.others.dto.req.PinganAdjustReq;
import com.akucun.account.proxy.facade.stub.others.dto.res.AdjustAccountQueryRes;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

/**
 * 账户调账接口
 */
@FeignClient(value = "account-proxy", fallbackFactory = AccountCenterFacadeFallbackFactory.class)
@RequestMapping("/api/account/proxy/adjust/account")
public interface AccountAdjustFacade {
    /**
     * 账户调账查询
     * @param adjustAccountQueryReq
     * @return
     */
    @PostMapping(value = "/query", produces = "application/json;charset=utf-8")
    Pagination<AdjustAccountQueryRes> adjustAccountQuery(@RequestBody AdjustAccountQueryReq adjustAccountQueryReq);

    /**
     * 平安账户调账
     * @param pinganAdjustReq
     * @return
     */
    @PostMapping(value = "/pingan/adjust", produces = "application/json;charset=utf-8")
    Result<Void> pinganAdjust(@RequestBody PinganAdjustReq pinganAdjustReq);

    /**
     * ddd
     * @param accountAdjustReq
     * @return
     */
    @PostMapping(value = "/account/adjust", produces = "application/json;charset=utf-8")
    Result<Void>  accountAdjust(@RequestBody AccountAdjustReq accountAdjustReq);




}
