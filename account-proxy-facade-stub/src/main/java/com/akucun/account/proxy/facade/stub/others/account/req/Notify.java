package com.akucun.account.proxy.facade.stub.others.account.req;

import java.io.Serializable;

import org.apache.commons.lang3.StringUtils;

import com.akucun.account.proxy.facade.stub.enums.ResponseEnum;
import com.akucun.account.proxy.facade.stub.exception.AccountProxyException;

import lombok.Data;

/**
 * 
 * <AUTHOR>
 *
 */
@Data
public class Notify implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	/**
	 * 金额
	 */
	private String amount;
	
	/**
	 * 用户类型
	 */
	private String customerType;
	
	/**
	 * 提现编号
	 */
	private String withdrawNo;
	
	/**
	 * 租户编号
	 */
	private String tenementCode;
	
	/**
	 * 银行卡号
	 */
	private String bankNo;
	
	/**
	 * 用户名称
	 */
	private String customerCode;
	
	/**
	 * 备注
	 */
	private String remark;
	
	/**
	 * 租户类型
	 */
	private String tenementType;
	
	/**
	 * 用户名称
	 */
	private String customerName;
	
	/**
	 * 手续费
	 */
	private String tranFee;
	
	public Notify amountCheck() {
		if(StringUtils.isBlank(this.amount)) {
			throw new AccountProxyException(ResponseEnum.PARAM_EXCEPTION,"amount不可以为空");
		}
		return this;
	}
	
	public Notify customerTypeCheck() {
		if(StringUtils.isBlank(this.customerType)) {
			throw new AccountProxyException(ResponseEnum.PARAM_EXCEPTION,"customerType不可以为空");
		}
		return this;
	}
	
	public Notify customerCodeCheck() {
		if(StringUtils.isBlank(this.customerCode)) {
			throw new AccountProxyException(ResponseEnum.PARAM_EXCEPTION,"customerCode不可以为空");
		}
		return this;
	}
	
	public Notify withdrawNoCodeCheck() {
		if(StringUtils.isBlank(this.withdrawNo)) {
			throw new AccountProxyException(ResponseEnum.PARAM_EXCEPTION,"withdrawNo不可以为空");
		}
		return this;
	}

}
