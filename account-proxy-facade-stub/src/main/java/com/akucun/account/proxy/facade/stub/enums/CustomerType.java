package com.akucun.account.proxy.facade.stub.enums;


/**
 * @Author: silei
 * @Date: 2020/12/11
 * @desc: 客户类型枚举
 */
public class CustomerType extends MEnum<CustomerType> {

    private static final long serialVersionUID = 5322321225173584105L;

    public static final CustomerType SH = (CustomerType)create("SH", 0, "商户");
    public static final CustomerType DG = (CustomerType)create("DG", 1, "代购");
    public static final CustomerType NM = (CustomerType)create("NM", 2, "H5");
    public static final CustomerType NMDL = (CustomerType)create("NMDL", 6, "H5代理");
    public static final CustomerType AT = (CustomerType)create("AT", 3, "SaaS租户");
    public static final CustomerType OP = (CustomerType)create("OP", 4, "openApi");
    public static final CustomerType CC = (CustomerType)create("CC", 5, "公司账户");
    public static final CustomerType DCC = (CustomerType)create("DCC", 7, "今日断码");
    public static final CustomerType SH_WC = (CustomerType)create("SH_WC", 8, "宁波商量微信商户");
    public static final CustomerType TB = (CustomerType)create("TB", 9, "团长帮卖");
    public static final CustomerType SS_WC = (CustomerType)create("SS_WC", 10, "上海商量微信商户");
    public static final CustomerType QDS = (CustomerType)create("QDS", 11, "经销渠道商");
    public static final CustomerType DXQDS = (CustomerType)create("DXQDS", 12, "代销渠道商");


}
