package com.akucun.account.proxy.facade.stub.others.dto.req;

import com.akucun.account.proxy.facade.stub.enums.TransferTypeEnum;
import com.mengxiang.base.common.utils.datamasking.Sensitive;
import com.mengxiang.base.common.utils.datamasking.SensitiveType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2021/01/28 10:59
 */
@ApiModel("企业付款到零钱请求实体")
public class PaymentTransferReq implements Serializable {

    private static final long serialVersionUID = -1778218312687914667L;

    @ApiModelProperty("业务方来源编号")
    @NotBlank(message = "业务方来源编号不能为空")
    private String sourceCode;

    @ApiModelProperty("用户渠道编码 H5-H5，XDAPPLET-小程序，3RD_MP-菜菜小程序")
    @NotBlank(message = "用户渠道编码不能为空")
    private String channelCode;

    @ApiModelProperty("用户openId")
    @NotBlank(message = "用户openId不能为空")
    private String openId;

    @ApiModelProperty("付款金额")
    @NotNull(message = "用户openId不能为空")
    private BigDecimal amount;

    @ApiModelProperty("业务方付款单号")
    @NotBlank(message = "业务方付款单号不能为空")
    private String sourceNo;

    @ApiModelProperty("付款人客户编号")
    @NotBlank(message = "付款人客户编号不能为空")
    private String customerCode;

    @ApiModelProperty("付款人类型")
    private String customerType;

    @ApiModelProperty("付款人姓名")
    @Sensitive(type = SensitiveType.Name)
    private String customerName;

    @ApiModelProperty("付款类型")
    @NotNull(message = "付款类型不能为空")
    private TransferTypeEnum transferType;

    @ApiModelProperty("是否校验付款人姓名")
    @NotNull(message = "是否校验付款人姓名不能为空")
    private Boolean checkName;

    @ApiModelProperty("终端IP")
    private String terminalIp;

    @ApiModelProperty("付款备注")
    @NotBlank(message = "付款备注不能为空")
    private String remark;

    @ApiModelProperty("租户ID")
    private String tenantId;

    @ApiModelProperty("批次号")
    private String batchNo;

    @ApiModelProperty("appId")
    private String appId;

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getSourceCode() {
        return sourceCode;
    }

    public void setSourceCode(String sourceCode) {
        this.sourceCode = sourceCode;
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getSourceNo() {
        return sourceNo;
    }

    public void setSourceNo(String sourceNo) {
        this.sourceNo = sourceNo;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getCustomerType() {
        return customerType;
    }

    public void setCustomerType(String customerType) {
        this.customerType = customerType;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public TransferTypeEnum getTransferType() {
        return transferType;
    }

    public void setTransferType(TransferTypeEnum transferType) {
        this.transferType = transferType;
    }

    public Boolean getCheckName() {
        return checkName;
    }

    public void setCheckName(Boolean checkName) {
        this.checkName = checkName;
    }

    public String getTerminalIp() {
        return terminalIp;
    }

    public void setTerminalIp(String terminalIp) {
        this.terminalIp = terminalIp;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }
}
