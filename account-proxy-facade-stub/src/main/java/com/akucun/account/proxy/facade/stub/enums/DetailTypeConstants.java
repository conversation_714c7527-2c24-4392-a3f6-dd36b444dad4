package com.akucun.account.proxy.facade.stub.enums;

/**
 * @desc: 交易明细类型枚举
 **/
public class DetailTypeConstants extends MEnum<DetailTypeConstants> {

    private static final long serialVersionUID = 4071798612572059393L;

    public static final DetailTypeConstants FIRST_PAY = (DetailTypeConstants) create("FIRST_PAY", 0, "首款划账");
    public static final DetailTypeConstants END_PAY = (DetailTypeConstants) create("END_PAY", 1, "尾款划账");
    public static final DetailTypeConstants FILL = (DetailTypeConstants) create("FILL", 26, "补款");
    public static final DetailTypeConstants BALANCE_PAY = (DetailTypeConstants) create("BALANCE_PAY", 2, " 余额支付");
    public static final DetailTypeConstants BALANCE_REFUND = (DetailTypeConstants) create("BALANCE_REFUND", 3, "余额退款");
    public static final DetailTypeConstants BALANCE_CH = (DetailTypeConstants) create("BALANCE_CH", 4, "余额充值");
    public static final DetailTypeConstants WITHDRAW = (DetailTypeConstants) create("WITHDRAW", 5, "余额提现");
    public static final DetailTypeConstants COMPENSATE = (DetailTypeConstants) create("COMPENSATE", 6, "退单补偿");
    public static final DetailTypeConstants RED_REWARD = (DetailTypeConstants) create("RED_REWARD", 7, "红包奖励");
    public static final DetailTypeConstants OTHER_REWARD = (DetailTypeConstants) create("OTHER_REWARD", 8, "其他奖励");
    public static final DetailTypeConstants CORRECTION = (DetailTypeConstants) create("CORRECTION", 9, "冲正");
    public static final DetailTypeConstants UN_WITHDRAW = (DetailTypeConstants) create("UN_WITHDRAW", 10, "余额提现失败返还");
    public static final DetailTypeConstants WITHDRAW_AG = (DetailTypeConstants) create("WITHDRAW_AG", 11, "提现审核通过");
    public static final DetailTypeConstants WITHDRAW_RF = (DetailTypeConstants) create("WITHDRAW_RF", 12, "提现审核拒绝");
    public static final DetailTypeConstants INIT_AMOUNT = (DetailTypeConstants) create("INIT_AMOUNT", 13, "账户金额初始化");
    public static final DetailTypeConstants PAY_BILL = (DetailTypeConstants) create("PAY_BILL", 61, "账单结算");
    public static final DetailTypeConstants REVOKE_PAY = (DetailTypeConstants)create("REVOKE_PAY", 27, "撤销扣款");
    public static final DetailTypeConstants ADVANCE_RETURN = (DetailTypeConstants) create("ADVANCE_RETURN", 20, "撤销返还");
    /**
     * 店主
     */
    public static final DetailTypeConstants TRADE_TYPE_043 = (DetailTypeConstants) create("TRADE_TYPE_043", 14, "佣金分润");
    public static final DetailTypeConstants TRADE_TYPE_044 = (DetailTypeConstants) create("TRADE_TYPE_044", 15, "提现到银行卡");
    public static final DetailTypeConstants TRADE_TYPE_045 = (DetailTypeConstants) create("TRADE_TYPE_045", 16, "提现到银行卡失败");
    public static final DetailTypeConstants TRADE_TYPE_046 = (DetailTypeConstants) create("TRADE_TYPE_046", 28, "补差价、质量问题、假货");
    public static final DetailTypeConstants TRADE_TYPE_047 = (DetailTypeConstants) create("TRADE_TYPE_047", 29, "开店佣金");
    public static final DetailTypeConstants TRADE_TYPE_040 = (DetailTypeConstants) create("TRADE_TYPE_040", 30, "支付");
    public static final DetailTypeConstants TRADE_TYPE_041 = (DetailTypeConstants) create("TRADE_TYPE_041", 31, "退款");
    public static final DetailTypeConstants TRADE_TYPE_042 = (DetailTypeConstants) create("TRADE_TYPE_042", 32, "充值");
    public static final DetailTypeConstants TRADE_TYPE_048 = (DetailTypeConstants) create("TRADE_TYPE_048", 33, "打赏");
    public static final DetailTypeConstants TRADE_TYPE_049 = (DetailTypeConstants) create("TRADE_TYPE_049", 34, "打赏失败返还");
    public static final DetailTypeConstants TRADE_TYPE_050 = (DetailTypeConstants) create("TRADE_TYPE_050", 36, "补偿运费");
    public static final DetailTypeConstants TRADE_TYPE_051 = (DetailTypeConstants) create("TRADE_TYPE_051", 42, "奖励发放");
    public static final DetailTypeConstants TRADE_TYPE_052 = (DetailTypeConstants) create("TRADE_TYPE_052", 46, "运费险理赔");
    public static final DetailTypeConstants TRADE_TYPE_053 = (DetailTypeConstants) create("TRADE_TYPE_053", 49, "资产费");
    public static final DetailTypeConstants TRADE_TYPE_054 = (DetailTypeConstants) create("TRADE_TYPE_054", 50, "服务费");
    public static final DetailTypeConstants TRADE_TYPE_056 = (DetailTypeConstants) create("TRADE_TYPE_056", 56, "商品服务收入");
    public static final DetailTypeConstants TRADE_TYPE_057 = (DetailTypeConstants) create("TRADE_TYPE_057", 57, "加码资产收入");
    public static final DetailTypeConstants TRADE_TYPE_058 = (DetailTypeConstants) create("TRADE_TYPE_058", 58, "加码商品服务收入");
    public static final DetailTypeConstants TRADE_TYPE_059 = (DetailTypeConstants) create("TRADE_TYPE_059", 59, "加码服务收入");
    public static final DetailTypeConstants TRADE_TYPE_460 = (DetailTypeConstants) create("TRADE_TYPE_460", 460, "补偿佣金");
    public static final DetailTypeConstants TRADE_TYPE_461 = (DetailTypeConstants) create("TRADE_TYPE_461", 461, "资产费-企业饷店");
    public static final DetailTypeConstants TRADE_TYPE_462 = (DetailTypeConstants) create("TRADE_TYPE_462", 462, "服务费-企业饷店");
    public static final DetailTypeConstants TRADE_TYPE_463 = (DetailTypeConstants) create("TRADE_TYPE_463", 463, "加码资产费-企业饷店");
    public static final DetailTypeConstants TRADE_TYPE_464 = (DetailTypeConstants) create("TRADE_TYPE_464", 464, "加码服务费-企业饷店");
    public static final DetailTypeConstants TRADE_TYPE_465 = (DetailTypeConstants) create("TRADE_TYPE_465", 465, "提现到微信");
    public static final DetailTypeConstants TRADE_TYPE_466 = (DetailTypeConstants) create("TRADE_TYPE_466", 466, "提现到微信失败");
    public static final DetailTypeConstants TRADE_TYPE_467 = (DetailTypeConstants) create("TRADE_TYPE_467", 467, "白名单店长服务费");
    public static final DetailTypeConstants TRADE_TYPE_468 = (DetailTypeConstants) create("TRADE_TYPE_468", 468, "白名单店长加码服务费");
    public static final DetailTypeConstants TRADE_TYPE_469 = (DetailTypeConstants) create("TRADE_TYPE_469", 469, "白名单店长红包收入");
    public static final DetailTypeConstants TRADE_TYPE_470 = (DetailTypeConstants) create("TRADE_TYPE_470", 470, "白名单补偿佣金");

    public static final DetailTypeConstants TRADE_TYPE_450 = (DetailTypeConstants) create("TRADE_TYPE_450", 450, "屏蔽扣款");
    public static final DetailTypeConstants TRADE_TYPE_451 = (DetailTypeConstants) create("TRADE_TYPE_451", 451, "屏蔽返还");

    /**
     * 新平台
     */
    public static final DetailTypeConstants TRADE_TYPE_024 = (DetailTypeConstants) create("TRADE_TYPE_024", 17, "结算");
    public static final DetailTypeConstants TRADE_TYPE_022 = (DetailTypeConstants) create("TRADE_TYPE_022", 18, "提现");
    public static final DetailTypeConstants TRADE_TYPE_026 = (DetailTypeConstants) create("TRADE_TYPE_026", 19, "提现失败");
    public static final DetailTypeConstants TRADE_TYPE_033 = (DetailTypeConstants) create("TRADE_TYPE_033", 41, "邀请入群结算");

    /**
     * app代购
     */
    public static final DetailTypeConstants TRADE_TYPE_184 = (DetailTypeConstants) create("TRADE_TYPE_184", 21, "提现失败");
    public static final DetailTypeConstants TRADE_TYPE_187 = (DetailTypeConstants) create("TRADE_TYPE_187", 22, "返点分润");
    public static final DetailTypeConstants TRADE_TYPE_189 = (DetailTypeConstants) create("TRADE_TYPE_189", 47, "运费险理赔");

    /**
     * 平台记账账户
     */
    public static final DetailTypeConstants TRADE_TYPE_245 = (DetailTypeConstants)create("TRADE_TYPE_245", 245, "饷店提现计税");
    /**
     * 店长
     */
    public static final DetailTypeConstants TRADE_TYPE_260 = (DetailTypeConstants) create("TRADE_TYPE_260", 23, "分佣");
    public static final DetailTypeConstants TRADE_TYPE_261 = (DetailTypeConstants) create("TRADE_TYPE_261", 24, "提现到银行卡");
    public static final DetailTypeConstants TRADE_TYPE_262 = (DetailTypeConstants) create("TRADE_TYPE_262", 25, "提现到银行卡失败");
    public static final DetailTypeConstants TRADE_TYPE_263 = (DetailTypeConstants) create("TRADE_TYPE_263", 35, "打赏收入");
    public static final DetailTypeConstants TRADE_TYPE_264 = (DetailTypeConstants) create("TRADE_TYPE_264", 40, "红包收入");
    public static final DetailTypeConstants TRADE_TYPE_265 = (DetailTypeConstants) create("TRADE_TYPE_265", 48, "服务费");
    public static final DetailTypeConstants TRADE_TYPE_266 = (DetailTypeConstants) create("TRADE_TYPE_266", 70, "加码服务收入");
    public static final DetailTypeConstants TRADE_TYPE_267 = (DetailTypeConstants) create("TRADE_TYPE_267", 267, "支付");
    public static final DetailTypeConstants TRADE_TYPE_268 = (DetailTypeConstants) create("TRADE_TYPE_268", 268, "退款");
    public static final DetailTypeConstants TRADE_TYPE_269 = (DetailTypeConstants) create("TRADE_TYPE_269", 269, "补偿佣金");
    public static final DetailTypeConstants TRADE_TYPE_270 = (DetailTypeConstants) create("TRADE_TYPE_270", 270, "服务费-企业饷店");
    public static final DetailTypeConstants TRADE_TYPE_271 = (DetailTypeConstants) create("TRADE_TYPE_271", 271, "加码服务费-企业饷店");
    public static final DetailTypeConstants TRADE_TYPE_272 = (DetailTypeConstants) create("TRADE_TYPE_272", 272, "提现到微信");
    public static final DetailTypeConstants TRADE_TYPE_273 = (DetailTypeConstants) create("TRADE_TYPE_273", 273, "提现到微信失败");
    public static final DetailTypeConstants TRADE_TYPE_274 = (DetailTypeConstants) create("TRADE_TYPE_274", 274, "服务费-白名单");
    public static final DetailTypeConstants TRADE_TYPE_275 = (DetailTypeConstants) create("TRADE_TYPE_275", 275, "加码服务费-白名单");
    public static final DetailTypeConstants TRADE_TYPE_276 = (DetailTypeConstants) create("TRADE_TYPE_276", 276, "红包收入-白名单");
    public static final DetailTypeConstants TRADE_TYPE_277 = (DetailTypeConstants) create("TRADE_TYPE_277", 277, "店铺贡献-白名单");
    public static final DetailTypeConstants TRADE_TYPE_278 = (DetailTypeConstants) create("TRADE_TYPE_278", 278, "补偿佣金-白名单");

    public static final DetailTypeConstants TRADE_TYPE_251 = (DetailTypeConstants) create("TRADE_TYPE_251", 251, "屏蔽扣款");
    public static final DetailTypeConstants TRADE_TYPE_252 = (DetailTypeConstants) create("TRADE_TYPE_252", 252, "屏蔽返还");

    /**
     * openApi交易枚举
     */
    public static final DetailTypeConstants TRADE_TYPE_120 = (DetailTypeConstants)create("TRADE_TYPE_120", 37, "支付");
    public static final DetailTypeConstants TRADE_TYPE_121 = (DetailTypeConstants)create("TRADE_TYPE_121", 38, "退款");
    public static final DetailTypeConstants TRADE_TYPE_124 = (DetailTypeConstants)create("TRADE_TYPE_124", 39, "充值");
    public static final DetailTypeConstants TRADE_TYPE_133 = (DetailTypeConstants)create("TRADE_TYPE_133", 60, "提现");
    public static final DetailTypeConstants TRADE_TYPE_134 = (DetailTypeConstants)create("TRADE_TYPE_134", 61, "提现失败");

    /**
     * 今日断码交易枚举
     */
    public static final DetailTypeConstants TRADE_TYPE_360 = (DetailTypeConstants)create("TRADE_TYPE_360", 45, "分润");
    public static final DetailTypeConstants TRADE_TYPE_361 = (DetailTypeConstants)create("TRADE_TYPE_361", 43, "提现");
    public static final DetailTypeConstants TRADE_TYPE_362 = (DetailTypeConstants)create("TRADE_TYPE_362", 44, "提现失败");

    /**
     * 商家宁波商量微信账户交易类型
     */
    public static final DetailTypeConstants TRADE_TYPE_400 = (DetailTypeConstants)create("TRADE_TYPE_400", 51, "分账");
    public static final DetailTypeConstants TRADE_TYPE_401 = (DetailTypeConstants)create("TRADE_TYPE_401", 52, "提现");
    public static final DetailTypeConstants TRADE_TYPE_402 = (DetailTypeConstants)create("TRADE_TYPE_402", 53, "提现失败");
    public static final DetailTypeConstants TRADE_TYPE_403 = (DetailTypeConstants)create("TRADE_TYPE_403", 403, "金额冻结");
    public static final DetailTypeConstants TRADE_TYPE_404 = (DetailTypeConstants)create("TRADE_TYPE_404", 404, "金额解冻");

    /**
     * 商家上海商量微信账户交易类型
     */
    public static final DetailTypeConstants TRADE_TYPE_480 = (DetailTypeConstants)create("TRADE_TYPE_480", 480, "分账");
    public static final DetailTypeConstants TRADE_TYPE_481 = (DetailTypeConstants)create("TRADE_TYPE_481", 481, "提现");
    public static final DetailTypeConstants TRADE_TYPE_482 = (DetailTypeConstants)create("TRADE_TYPE_482", 482, "提现失败");
    public static final DetailTypeConstants TRADE_TYPE_483 = (DetailTypeConstants)create("TRADE_TYPE_483", 483, "金额冻结");
    public static final DetailTypeConstants TRADE_TYPE_484 = (DetailTypeConstants)create("TRADE_TYPE_484", 484, "金额解冻");

    /**
     * 奖励金
     */
    public static final DetailTypeConstants TRADE_TYPE_000 = (DetailTypeConstants) create("TRADE_TYPE_000", 100, "初始化账户");
    public static final DetailTypeConstants TRADE_TYPE_001 = (DetailTypeConstants) create("TRADE_TYPE_001", 101, "退货退款");
    public static final DetailTypeConstants TRADE_TYPE_002 = (DetailTypeConstants) create("TRADE_TYPE_002", 102, "次品协商退款");
    public static final DetailTypeConstants TRADE_TYPE_003 = (DetailTypeConstants) create("TRADE_TYPE_003", 103, "运费返还");
    public static final DetailTypeConstants TRADE_TYPE_004 = (DetailTypeConstants) create("TRADE_TYPE_004", 104, "用户充值");
    public static final DetailTypeConstants TRADE_TYPE_005 = (DetailTypeConstants) create("TRADE_TYPE_005", 105, "奖励发放");
    public static final DetailTypeConstants TRADE_TYPE_006 = (DetailTypeConstants) create("TRADE_TYPE_006", 106, "补偿代购费");
    public static final DetailTypeConstants TRADE_TYPE_008 = (DetailTypeConstants) create("TRADE_TYPE_008", 108, "订单支付");
    public static final DetailTypeConstants TRADE_TYPE_010 = (DetailTypeConstants) create("TRADE_TYPE_010", 110, "金额冲正增加");
    public static final DetailTypeConstants TRADE_TYPE_011 = (DetailTypeConstants) create("TRADE_TYPE_011", 111, "金额冲正减少");
    public static final DetailTypeConstants TRADE_TYPE_012 = (DetailTypeConstants) create("TRADE_TYPE_012", 112, "用户注销");
    public static final DetailTypeConstants TRADE_TYPE_013 = (DetailTypeConstants) create("TRADE_TYPE_013", 113, "异常退款");
    public static final DetailTypeConstants TRADE_TYPE_014 = (DetailTypeConstants) create("TRADE_TYPE_014", 114, "奖励撤销");
    public static final DetailTypeConstants TRADE_TYPE_015 = (DetailTypeConstants) create("TRADE_TYPE_015", 115, "订单补偿");
    public static final DetailTypeConstants TRADE_TYPE_016 = (DetailTypeConstants) create("TRADE_TYPE_016", 116, "售后津贴");
    public static final DetailTypeConstants TRADE_TYPE_017 = (DetailTypeConstants) create("TRADE_TYPE_017", 117, "售后津贴充值");
    public static final DetailTypeConstants TRADE_TYPE_018 = (DetailTypeConstants) create("TRADE_TYPE_018", 118, "屏蔽扣款");
    public static final DetailTypeConstants TRADE_TYPE_019 = (DetailTypeConstants) create("TRADE_TYPE_019", 119, "屏蔽返还");

    /**
     * 租户
     */
    public static final DetailTypeConstants TRADE_TYPE_420 = (DetailTypeConstants)create("TRADE_TYPE_420", 420, "分账");
    public static final DetailTypeConstants TRADE_TYPE_421 = (DetailTypeConstants)create("TRADE_TYPE_421", 421, "提现");
    public static final DetailTypeConstants TRADE_TYPE_422 = (DetailTypeConstants)create("TRADE_TYPE_422", 422, "提现失败");
    public static final DetailTypeConstants TRADE_TYPE_423 = (DetailTypeConstants)create("TRADE_TYPE_423", 423, "分账冻结");
    public static final DetailTypeConstants TRADE_TYPE_424 = (DetailTypeConstants)create("TRADE_TYPE_424", 424, "资金分佣");
    public static final DetailTypeConstants TRADE_TYPE_425 = (DetailTypeConstants)create("TRADE_TYPE_425", 425, "服务分佣");
    public static final DetailTypeConstants TRADE_TYPE_426 = (DetailTypeConstants)create("TRADE_TYPE_426", 426, "加码资金分佣");
    public static final DetailTypeConstants TRADE_TYPE_427 = (DetailTypeConstants)create("TRADE_TYPE_427", 427, "加码服务分佣");
    public static final DetailTypeConstants TRADE_TYPE_428 = (DetailTypeConstants)create("TRADE_TYPE_428", 428, "分账解冻");
    public static final DetailTypeConstants TRADE_TYPE_429 = (DetailTypeConstants)create("TRADE_TYPE_429", 429, "加码分账冻结");
    public static final DetailTypeConstants TRADE_TYPE_430 = (DetailTypeConstants)create("TRADE_TYPE_430", 430, "加码分账解冻");

    /**
     * 团长帮卖
     */
    public static final DetailTypeConstants TRADE_TYPE_440 = (DetailTypeConstants)create("TRADE_TYPE_440", 440, "团长分账收入");
    public static final DetailTypeConstants TRADE_TYPE_441 = (DetailTypeConstants)create("TRADE_TYPE_441", 441, "帮卖分账收入");
    public static final DetailTypeConstants TRADE_TYPE_442 = (DetailTypeConstants)create("TRADE_TYPE_442", 442, "提现");
    public static final DetailTypeConstants TRADE_TYPE_443 = (DetailTypeConstants)create("TRADE_TYPE_443", 443, "提现失败");
    public static final DetailTypeConstants TRADE_TYPE_444 = (DetailTypeConstants) create("TRADE_TYPE_444", 444, "团长分账加价收入");
    public static final DetailTypeConstants TRADE_TYPE_445 = (DetailTypeConstants) create("TRADE_TYPE_445", 445, "帮卖分账加价收入");

}
