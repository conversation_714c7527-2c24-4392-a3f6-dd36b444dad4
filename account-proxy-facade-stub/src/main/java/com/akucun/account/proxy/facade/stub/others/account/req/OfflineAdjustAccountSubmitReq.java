/*
 * @Author: <PERSON>
 * @Date: 2025-04-16 15:28:39
 * @Description: Do not edit
 * @License: Copyright (c) 2025, Lee
 */
package com.akucun.account.proxy.facade.stub.others.account.req;

import lombok.Data;
import java.math.BigDecimal;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class OfflineAdjustAccountSubmitReq {
    /**
     * 客户编码
     */
    @NotBlank(message = "客户编码不能为空")
    private String customerCode;
    /**
     * 客户类型
     */
    //@NotBlank(message = "客户类型不能为空")
    private String customerType;
    /**
     * 账户类型Key
     */
    @NotBlank(message = "账户类型Key不能为空")
    private String accountTypeKey;
    /**
     * 交易单号
     */
    @NotBlank(message = "交易单号不能为空")
    private String tradeNo;
    /**
     * 来源单号
     */
    @NotBlank(message = "来源单号不能为空")
    private String sourceBillNo;
    /**
     * 交易类型
     */
    @NotBlank(message = "交易类型不能为空")
    private String tradeType;
    /**
     * 交易金额
     */
    @NotNull(message = "金额不能为空")
    @DecimalMin(value = "0", message = "金额不能小于0")
    private BigDecimal amount;
    /**
     * 备注
     */
    @NotBlank(message = "备注不能为空")
    private String remark;
    /**
     * 扩展字段
     */
    private String ext;

    /**
     * 创建人
     */
    @NotBlank(message = "创建人不能为空")
    private String createBy;
}