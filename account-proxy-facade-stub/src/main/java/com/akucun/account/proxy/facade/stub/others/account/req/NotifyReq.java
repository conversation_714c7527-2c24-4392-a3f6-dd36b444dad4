package com.akucun.account.proxy.facade.stub.others.account.req;

import java.io.Serializable;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;

import com.akucun.account.proxy.facade.stub.enums.ResponseEnum;
import com.akucun.account.proxy.facade.stub.enums.WithdrawStatusEnum;
import com.akucun.account.proxy.facade.stub.exception.AccountProxyException;

import lombok.Data;

/**
 * 
 * <AUTHOR>
 *
 */
@Data
public class NotifyReq implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 6706588574466368674L;

	/**
	 * 业务分类
	 */
	private String bizCategory;
	
	/**
	 * 业务信息
	 */
	private Notify bizInfo;
	
	/**
	 * 业务单
	 */
	private String bizNo;
	
	/**
	 * 请求序号
	 */
	private String requestNo;
	
	/**
	 * 来源平台
	 */
	private String requestPlatform;
	
	/**
	 * 通知消息
	 */
	private NotifyMsg result;
	
	/**
	 * 任务状态：INIT，PROCESSING，SUCCESS，FAIL
	 */
	private String status;
	
	/**
	 * 任务主键
	 */
	private String taskId;
	
	public Boolean isSuccess() {
		if(StringUtils.equals(this.status, WithdrawStatusEnum.SUCCESS.getCode())) {
			return Boolean.TRUE;
		}
		return Boolean.FALSE;
	}
	
	public String getErrorMessage() {
		if(Objects.isNull(this.result)) {
			return "";
		}
		return this.getResult().getFailReason();
	}
	
	public NotifyReq bizCategoryCheck() {
		if(StringUtils.isBlank(this.bizCategory)) {
			throw new AccountProxyException(ResponseEnum.PARAM_EXCEPTION,"bizCategory不可以为空");
		}
		return this;
	}

	public NotifyReq requestPlatformCheck() {
		if(StringUtils.isBlank(this.requestPlatform)) {
			throw new AccountProxyException(ResponseEnum.PARAM_EXCEPTION,"requestPlatform不可以为空");
		}
		return this;
	}
	
	public NotifyReq bizInfoCheck() {
		this.bizInfo.amountCheck().customerTypeCheck().customerCodeCheck().withdrawNoCodeCheck();
		return this;
	}
	
	public NotifyReq statusCheck() {
		if(StringUtils.isBlank(this.status)) {
			throw new AccountProxyException(ResponseEnum.PARAM_EXCEPTION,"status不可以为空");
		}
		return this;
	}
	
	public NotifyReq bizInfo() {
		if(Objects.isNull(this.bizInfo)) {
			throw new AccountProxyException(ResponseEnum.PARAM_EXCEPTION,"bizInfo不可以为空");
		}
		return this;
	}
}
