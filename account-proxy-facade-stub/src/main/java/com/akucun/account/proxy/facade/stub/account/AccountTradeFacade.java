package com.akucun.account.proxy.facade.stub.account;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.facade.stub.fallback.AccountTradeFacadeFallbackFactory;
import com.akucun.account.proxy.facade.stub.others.dto.req.AccountTradeRequest;
import com.akucun.account.proxy.facade.stub.others.dto.res.AccountTradeResponse;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2020/12/22 20:45
 */
@FeignClient(value = "account-proxy", fallbackFactory = AccountTradeFacadeFallbackFactory.class)
@RequestMapping("/api/account/proxy")
public interface AccountTradeFacade {

    /**
     * 奖励金账户支付
     * @param request
     * @return
     */
    @PostMapping(value = "/bonusAccountTrade")
    Result<Boolean> bonusAccountTrade(@RequestBody AccountTradeRequest request);

    /**
     * 奖励金支付查询
     * @param tradeNo
     * @return
     */
    @GetMapping(value = "/bonusAccountQuery")
    Result<AccountTradeResponse> bonusAccountQuery(@RequestParam(name="tradeNo") String tradeNo, @RequestParam(name="tradeType") Integer tradeType);

    /**
     * 奖励金账户退款
     * @param request
     * @return
     */
    @PostMapping(value = "/bonusAccountRefund")
    Result<Boolean> bonusAccountRefund(@RequestBody AccountTradeRequest request);

    /**
     * 钱包余额账户支付
     * @param request
     * @return
     */
    @PostMapping(value = "/akcAccountTrade")
    Result<Boolean> akcAccountTrade(@RequestBody AccountTradeRequest request);

    /**
     * 钱包余额支付查询
     * @param tradeNo
     * @return
     */
    @GetMapping(value = "/akcAccountQuery")
    Result<AccountTradeResponse> akcAccountQuery(@RequestParam(name="tradeNo") String tradeNo, @RequestParam(name="tradeType") Integer tradeType);

    /**
     * 钱包余额账户退款
     * @param request
     * @return
     */
    @PostMapping(value = "/akcAccountRefund")
    Result<Boolean> akcAccountRefund(@RequestBody AccountTradeRequest request);

    /**
     * 开放平台账户支付
     * @param request
     * @return
     */
    @PostMapping(value = "/openApiAccountTrade")
    Result<Boolean> openApiAccountTrade(@RequestBody AccountTradeRequest request);

    /**
     * 开放平台支付查询
     * @param tradeNo
     * @return
     */
    @GetMapping(value = "/openApiAccountQuery")
    Result<AccountTradeResponse> openApiAccountQuery(@RequestParam(name="tradeNo") String tradeNo, @RequestParam(name="tradeType") Integer tradeType);

    /**
     * 开放平台账户退款
     * @param request
     * @return
     */
    @PostMapping(value = "/openApiAccountRefund")
    Result<Boolean> openApiAccountRefund(@RequestBody AccountTradeRequest request);

    @PostMapping(value = "/xdAccountTrade")
    Result<Boolean> xdAccountTrade(@RequestBody AccountTradeRequest request);

    @GetMapping(value = "/xdAccountQuery")
    Result<AccountTradeResponse> xdAccountQuery(@RequestParam(name = "tradeNo") String tradeNo, @RequestParam(name = "tradeType") Integer tradeType);

    @PostMapping(value = "/pointAccountTrade")
    Result<Boolean> pointAccountTrade(@RequestBody AccountTradeRequest request);

    @GetMapping(value = "/pointAccountQuery")
    Result<AccountTradeResponse> pointAccountQuery(@RequestParam(name = "tradeNo") String tradeNo, @RequestParam(name = "tradeType") Integer tradeType);

    @PostMapping(value = "/xdAccountRefund")
    Result<Boolean> xdAccountRefund(@RequestBody AccountTradeRequest request);
}
