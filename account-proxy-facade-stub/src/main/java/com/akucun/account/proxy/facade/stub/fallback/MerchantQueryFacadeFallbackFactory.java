package com.akucun.account.proxy.facade.stub.fallback;

import com.akucun.fps.common.entity.Result;
import com.akucun.fps.common.entity.ResultList;
import com.akucun.fps.pingan.client.model.query.BindCardQueryReq;
import com.akucun.fps.pingan.client.vo.PinganCardVO;
import com.akucun.account.proxy.facade.stub.pingan.MerchantQueryFacade;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

@Component
public class MerchantQueryFacadeFallbackFactory implements FallbackFactory<MerchantQueryFacade> {

    @Override
    public MerchantQueryFacade create(Throwable throwable) {
        return new MerchantQueryFacade() {
            @Override
            public ResultList<PinganCardVO> selectBindCardByCodePage(String customerCode) {
                ResultList<PinganCardVO> resultList = new ResultList<PinganCardVO>();
                resultList.setError(500, "SERVICE_ERROR", "服务超时熔断");
                return resultList;
            }

            @Override
            public ResultList<PinganCardVO> selectBindCardByStatus(BindCardQueryReq bindCardQueryReq) {
                ResultList<PinganCardVO> resultList = new ResultList<PinganCardVO>();
                resultList.setError(500, "SERVICE_ERROR", "服务超时熔断");
                return resultList;
            }

            @Override
            public Result<PinganCardVO> selectBindCardByCardNo(BindCardQueryReq bindCardQueryReq) {
                Result<PinganCardVO> result = new Result<PinganCardVO>();
                result.setError(500, "SERVICE_ERROR", "服务超时熔断");
                return result;
            }

            @Override
            public ResultList<PinganCardVO> selectBindCardListByParams(BindCardQueryReq bindCardQueryReq) {
                ResultList<PinganCardVO> resultList = new ResultList<PinganCardVO>();
                resultList.setError(500, "SERVICE_ERROR", "服务超时熔断");
                return resultList;
            }

            @Override
            public Result<Integer> isHasBindSuccRecord(BindCardQueryReq bindCardQueryReq) {
                Result<Integer> result = new Result<Integer>();
                result.setError(500, "SERVICE_ERROR", "服务超时熔断");
                return result;
            }
        };
    }
}