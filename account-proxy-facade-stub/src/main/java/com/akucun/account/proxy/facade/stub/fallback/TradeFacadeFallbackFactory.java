package com.akucun.account.proxy.facade.stub.fallback;

import com.akucun.account.proxy.facade.stub.others.trade.req.TradeReq;
import com.akucun.account.proxy.facade.stub.others.trade.res.TradeRes;
import com.akucun.account.proxy.facade.stub.trade.TradeFacade;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

@Component
public class TradeFacadeFallbackFactory implements FallbackFactory<TradeFacade> {

    @Override
    public TradeFacade create(Throwable throwable) {
        return new TradeFacade() {

            @Override
            public TradeRes trade(TradeReq tradeReq) {
                throw new RuntimeException("服务超时熔断");
            }
        };
    }
}
