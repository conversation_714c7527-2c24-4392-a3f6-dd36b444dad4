package com.akucun.account.proxy.facade.stub.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Create on : 2025/1/14 20:03
 **/
@Getter
public enum OAWorkflowStatusEnum {

    CREATE(0,"创建"),
    APPROVE(1,"批准"),
    SUBMIT(2,"提交"),
    ARCHIVE(3,"归档"),
    WAIT(5,"等待"),
    AUTO(6,"自动处理");

    private Integer code;

    private String desc;

    OAWorkflowStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
