package com.akucun.account.proxy.facade.stub.others.aggregation.req;

import com.mengxiang.base.common.utils.datamasking.Sensitive;
import com.mengxiang.base.common.utils.datamasking.SensitiveType;
import lombok.ToString;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@ToString
public class BindCardInfoDTO implements Serializable {

    private static final long serialVersionUID = -1636326348537879362L;

    @NotBlank(message = "客户编码不能为空")
    private String customerCode;
    @NotBlank(message = "客户类型不能为空")
    private String customerType;
    @Sensitive(type = SensitiveType.Name)
    private String customerName;

    private String idType;
    private String idCode;

    @NotBlank(message = "银行预留手机号不能为空")
    @Sensitive(type = SensitiveType.Phone)
    private String reservedPhone;
    /**
     * 所属支行
     */
    @NotBlank(message = "银行支行名称不能为空")
    private String bankBranchName;

    /**
     * 所属银行
     */
    private String bankNameId;
    @NotBlank(message = "银行名称不能为空")
    private String bankName;
    private String bankTypeNo;

    /**
     * 银行开户账号
     */
    @NotBlank(message = "银行开户账号不能为空")
    @Sensitive(type = SensitiveType.BankCard)
    private String bankAccount;

    private String banknoEncrypt;
    @Sensitive(type = SensitiveType.Name)
    private String accountName;
    /**
     * 银行联行号
     */
    @NotBlank(message = "银行联行号不能为空")
    private String bankLineNumber;
    /**
     * 超级网银号
     */
    private String superBankCode;

    /**
     * 账户类型，0：普通账户，1：企业开户账户，2：企业结算账户
     */
    private String bankAccountType;
    private String bankAccountTypeDesc;
    /**
     * 预留手机号密文
     */
    private String reservedPhoneEncrypt;
    /**
     * 平安银行编号
     */
    private String pinganBankNo;
    /**
     * 证件号
     */
    private String contactsCardNumberEncrypt;

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getCustomerType() {
        return customerType;
    }

    public void setCustomerType(String customerType) {
        this.customerType = customerType;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getIdType() {
        return idType;
    }

    public void setIdType(String idType) {
        this.idType = idType;
    }

    public String getIdCode() {
        return idCode;
    }

    public void setIdCode(String idCode) {
        this.idCode = idCode;
    }

    public String getReservedPhone() {
        return reservedPhone;
    }

    public void setReservedPhone(String reservedPhone) {
        this.reservedPhone = reservedPhone;
    }

    public String getBankBranchName() {
        return bankBranchName;
    }

    public void setBankBranchName(String bankBranchName) {
        this.bankBranchName = bankBranchName;
    }

    public String getBankNameId() {
        return bankNameId;
    }

    public void setBankNameId(String bankNameId) {
        this.bankNameId = bankNameId;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getBankTypeNo() {
        return bankTypeNo;
    }

    public void setBankTypeNo(String bankTypeNo) {
        this.bankTypeNo = bankTypeNo;
    }

    public String getBankAccount() {
        return bankAccount;
    }

    public void setBankAccount(String bankAccount) {
        this.bankAccount = bankAccount;
    }

    public String getBanknoEncrypt() {
        return banknoEncrypt;
    }

    public void setBanknoEncrypt(String banknoEncrypt) {
        this.banknoEncrypt = banknoEncrypt;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getBankLineNumber() {
        return bankLineNumber;
    }

    public void setBankLineNumber(String bankLineNumber) {
        this.bankLineNumber = bankLineNumber;
    }

    public String getSuperBankCode() {
        return superBankCode;
    }

    public void setSuperBankCode(String superBankCode) {
        this.superBankCode = superBankCode;
    }

    public String getBankAccountType() {
        return bankAccountType;
    }

    public void setBankAccountType(String bankAccountType) {
        this.bankAccountType = bankAccountType;
    }

    public String getBankAccountTypeDesc() {
        return bankAccountTypeDesc;
    }

    public void setBankAccountTypeDesc(String bankAccountTypeDesc) {
        this.bankAccountTypeDesc = bankAccountTypeDesc;
    }

    public String getReservedPhoneEncrypt() {
        return reservedPhoneEncrypt;
    }

    public void setReservedPhoneEncrypt(String reservedPhoneEncrypt) {
        this.reservedPhoneEncrypt = reservedPhoneEncrypt;
    }

    public String getPinganBankNo() {
        return pinganBankNo;
    }

    public void setPinganBankNo(String pinganBankNo) {
        this.pinganBankNo = pinganBankNo;
    }

    public String getContactsCardNumberEncrypt() {
        return contactsCardNumberEncrypt;
    }

    public void setContactsCardNumberEncrypt(String contactsCardNumberEncrypt) {
        this.contactsCardNumberEncrypt = contactsCardNumberEncrypt;
    }
}
