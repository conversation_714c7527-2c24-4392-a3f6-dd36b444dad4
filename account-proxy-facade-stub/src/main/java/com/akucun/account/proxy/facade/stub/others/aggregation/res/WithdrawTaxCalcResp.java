package com.akucun.account.proxy.facade.stub.others.aggregation.res;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 提现税费试算响应
 *
 * <AUTHOR>
 */
public class WithdrawTaxCalcResp implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 2275124837190493195L;

	/**
	 * 本次提现金额
	 */
	private BigDecimal amount;
	/**
	 * 税费金额
	 */
    private BigDecimal taxFee;
	/**
	 * 到账金额
	 */
    private BigDecimal withdraw;
	/**
	 * 税率提示
	 */
    private String tip;

    /**
     * 税率提示2
     */
    private String tip2;
	/**
	 * 当月累计提现金额（包含本次提现金额）
	 */
    private BigDecimal accumAmount;
    /**
     * 手续费
     */
    private BigDecimal serviceFee;

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getTaxFee() {
        return taxFee;
    }

    public void setTaxFee(BigDecimal taxFee) {
        this.taxFee = taxFee;
    }

    public BigDecimal getWithdraw() {
        return withdraw;
    }

    public void setWithdraw(BigDecimal withdraw) {
        this.withdraw = withdraw;
    }

    public String getTip() {
        return tip;
    }

    public void setTip(String tip) {
        this.tip = tip;
    }

    public String getTip2() {
        return tip2;
    }

    public void setTip2(String tip2) {
        this.tip2 = tip2;
    }

    public BigDecimal getAccumAmount() {
        return accumAmount;
    }

    public void setAccumAmount(BigDecimal accumAmount) {
        this.accumAmount = accumAmount;
    }

    public BigDecimal getServiceFee() {
        return serviceFee;
    }

    public void setServiceFee(BigDecimal serviceFee) {
        this.serviceFee = serviceFee;
    }
}
