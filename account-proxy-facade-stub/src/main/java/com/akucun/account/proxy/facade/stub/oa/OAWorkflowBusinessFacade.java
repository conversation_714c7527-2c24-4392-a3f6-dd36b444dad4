package com.akucun.account.proxy.facade.stub.oa;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.facade.stub.fallback.OAWorkflowBusinessFacadeFallbackFactory;
import com.akucun.account.proxy.facade.stub.others.oa.req.business.MultiSupplierCorporatePaymentRequest;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Create on : 2025/1/14 16:50
 **/
@FeignClient(value = "account-proxy", fallbackFactory = OAWorkflowBusinessFacadeFallbackFactory.class)
@RequestMapping("/api/account/proxy/oa/workflow/business")
public interface OAWorkflowBusinessFacade {

    /**
     * 多供应商对公打款
     * @param request
     * @return
     */
    @PostMapping(value = "/multiSupplierCorporatePayment", produces = "application/json;charset=utf-8")
    Result<String> multiSupplierCorporatePayment(@RequestBody MultiSupplierCorporatePaymentRequest request);

}
