package com.akucun.account.proxy.facade.stub.others.dto.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : 供应链渠道商统一补款 请求对象
 * @Create on : 2024/11/25 20:00
 **/
@ApiModel("供应链渠道商统一补款请求实体")
@Data
@ToString
@NoArgsConstructor
public class CompensationFillPayApplyReq {
    @ApiModelProperty(value = "唯一业务单号", required = true)
    private String sourceBusinessNo;

    @ApiModelProperty(value = "商家编码", required = true)
    private String merchantCode;

    @ApiModelProperty(value = "商家名称")
    private String merchantName;

    @ApiModelProperty(value = "渠道商ID/租户ID", required = true)
    private String tenantId;

    @ApiModelProperty(value = "金额", required = true)
    private BigDecimal amount;

    @ApiModelProperty(value = "业务发生时间", required = true)
    private Date businessDate;

    @ApiModelProperty(value = "渠道，枚举值：INSURANCE、AFTER_SALE", required = true)
    private String channel;

    @ApiModelProperty(value = "补偿类型：运费补偿-SHIPPING_FEE_COMPENSATION，补货款-GOODS_FEE_COMPENSATION，运费险理赔-SHIPPING_INSURANCE_CLAIM", required = true)
    private String type;

    @ApiModelProperty(value = "订单渠道", required = true)
    private String orderChannel;

    @ApiModelProperty(value = "二级单号", required = true)
    private String secondOrderNo;

    /**
     * 优先使用orderSkuNo,若不存在则使用thirdOrderNo
     */
    @ApiModelProperty(value = "三级单号")
    private String thirdOrderNo;

    @ApiModelProperty(value = "三级ID")
    private String thirdOrderId;

    @ApiModelProperty(value = "SKU单号")
    private String orderSkuNo;

    @ApiModelProperty(value = "店铺code")
    private String shopCode;

    @ApiModelProperty(value = "扩展信息")
    private HashMap<String,String> exts;

}
