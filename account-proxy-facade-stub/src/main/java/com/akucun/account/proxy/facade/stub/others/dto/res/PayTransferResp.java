package com.akucun.account.proxy.facade.stub.others.dto.res;

import java.io.Serializable;

/**
 * @Author: silei
 * @Date: 2021/9/8
 * @desc:
 */
public class PayTransferResp implements Serializable {

    /**
     * 微信明细单号
     */
    private String detailId;
    /**
     * 商家明细单号
     */
    private String withDrawNo;
    /**
     * 明细状态
     */
    private String status;


    public String getDetailId() {
        return detailId;
    }

    public void setDetailId(String detailId) {
        this.detailId = detailId;
    }

    public String getWithDrawNo() {
        return withDrawNo;
    }

    public void setWithDrawNo(String withDrawNo) {
        this.withDrawNo = withDrawNo;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
