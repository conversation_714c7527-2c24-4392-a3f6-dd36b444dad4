package com.akucun.account.proxy.facade.stub.others.aggregation.res;

import java.io.Serializable;
import java.math.BigDecimal;

public class WithdrawServiceFeeSwitchResp implements Serializable{
    private static final long serialVersionUID = 2260219306851645118L;
    /**
     * 手续费开关：on-开，off-关
     */
    private String serviceFeeSwitch;

    /**
     * 手续费开始时间：yyyy年MM月dd日
     */
    private String serviceFeeStartDate;
    /**
     * 免手续费提现次数（5次，包含5次）
     */
    private long serviceFeeFreeNum;
    /**
     * 手续费金额
     */
    private BigDecimal serviceFeeAmount;
    /**
     * 本月已提现次数累计
     */
    private long withdrawTotalNum;
    /**
     * 本月剩余免手续费提现次数累计
     */
    private long surplusWithdrawTotalNum;
    /**
     * 文案模板号（1--模板1，2--模板2）
     */
    private long mouldNum;

    public String getServiceFeeSwitch() {
        return serviceFeeSwitch;
    }

    public void setServiceFeeSwitch(String serviceFeeSwitch) {
        this.serviceFeeSwitch = serviceFeeSwitch;
    }

    public String getServiceFeeStartDate() {
        return serviceFeeStartDate;
    }

    public void setServiceFeeStartDate(String serviceFeeStartDate) {
        this.serviceFeeStartDate = serviceFeeStartDate;
    }

    public long getServiceFeeFreeNum() {
        return serviceFeeFreeNum;
    }

    public void setServiceFeeFreeNum(long serviceFeeFreeNum) {
        this.serviceFeeFreeNum = serviceFeeFreeNum;
    }

    public BigDecimal getServiceFeeAmount() {
        return serviceFeeAmount;
    }

    public void setServiceFeeAmount(BigDecimal serviceFeeAmount) {
        this.serviceFeeAmount = serviceFeeAmount;
    }

    public long getWithdrawTotalNum() {
        return withdrawTotalNum;
    }

    public void setWithdrawTotalNum(long withdrawTotalNum) {
        this.withdrawTotalNum = withdrawTotalNum;
    }

    public long getSurplusWithdrawTotalNum() {
        return surplusWithdrawTotalNum;
    }

    public void setSurplusWithdrawTotalNum(long surplusWithdrawTotalNum) {
        this.surplusWithdrawTotalNum = surplusWithdrawTotalNum;
    }

    public long getMouldNum() {
        return mouldNum;
    }

    public void setMouldNum(long mouldNum) {
        this.mouldNum = mouldNum;
    }
}
