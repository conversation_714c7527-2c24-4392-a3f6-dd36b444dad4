package com.akucun.account.proxy.facade.stub.others.dto.res;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/01/28 10:59
 */
@ApiModel("企业付款到零钱返回实体")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PaymentTransferResp implements Serializable {

    private static final long serialVersionUID = -1211452265022924747L;
    @ApiModelProperty("业务方订单编号")
    private String sourceNo;

    @ApiModelProperty("付款交易单号")
    private String tradeNo;

    @ApiModelProperty("第三方交易流水号")
    private String gatewayTransactionId;

    @ApiModelProperty("付款成功时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date transferSuccessTime;

    @ApiModelProperty("付款金额，单位：元")
    private BigDecimal amount;

    public String getSourceNo() {
        return sourceNo;
    }

    public void setSourceNo(String sourceNo) {
        this.sourceNo = sourceNo;
    }

    public String getTradeNo() {
        return tradeNo;
    }

    public void setTradeNo(String tradeNo) {
        this.tradeNo = tradeNo;
    }

    public String getGatewayTransactionId() {
        return gatewayTransactionId;
    }

    public void setGatewayTransactionId(String gatewayTransactionId) {
        this.gatewayTransactionId = gatewayTransactionId;
    }

    public Date getTransferSuccessTime() {
        return transferSuccessTime;
    }

    public void setTransferSuccessTime(Date transferSuccessTime) {
        this.transferSuccessTime = transferSuccessTime;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }
}
