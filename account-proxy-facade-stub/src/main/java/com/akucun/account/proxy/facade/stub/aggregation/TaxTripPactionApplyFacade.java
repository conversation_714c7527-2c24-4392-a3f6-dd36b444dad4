package com.akucun.account.proxy.facade.stub.aggregation;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.facade.stub.fallback.TaxTripPactionApplyFacadeFallbackFactory;
import com.akucun.account.proxy.facade.stub.others.dto.req.TaxTripPactionApplyReq;
import com.akucun.account.proxy.facade.stub.others.dto.req.TaxTripPactionApplyUpdateReq;
import com.akucun.account.proxy.facade.stub.others.dto.res.TaxTripPactionApplyResp;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@FeignClient(value = "account-proxy", fallbackFactory = TaxTripPactionApplyFacadeFallbackFactory.class)
@RequestMapping("/api/account/aggregation/trippaction")
public interface TaxTripPactionApplyFacade {

    /**
     * 税库银三方协议审核申请接
     * @param taxTripPactionApplyReq
     * @return
     */
    @PostMapping("/apply")
    public Result<String> apply(@RequestBody TaxTripPactionApplyReq taxTripPactionApplyReq);

    /**
     * 查询所有申请记录
     * @param customerCode
     * @param customerType
     * @return
     */
    @GetMapping("/findTaxTripPactionApply")
    public Result<List<TaxTripPactionApplyResp>> findTaxTripPactionApply(@RequestParam("customerCode") String customerCode,
                                                                         @RequestParam("customerType") String customerType);

    /**
     * 更新申请状态
     * @param taxTripPactionApplyUpdateReq
     * @return
     */
    @PostMapping("/updateApplyStatus")
    public Result<Boolean> updateApplyStatus(@RequestBody TaxTripPactionApplyUpdateReq taxTripPactionApplyUpdateReq);

}
