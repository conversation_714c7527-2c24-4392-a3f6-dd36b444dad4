package com.akucun.account.proxy.facade.stub.others.dto.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: silei
 * @Date: 2020/11/4
 * @desc: 用户账户余额信息
 */
@ApiModel("用户账户余额信息")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AccountInfoResp implements Serializable {

    private static final long serialVersionUID = -8401576492403280160L;

    @ApiModelProperty(value = "客户类型", required = true)
    private String customerType;
    @ApiModelProperty(value = "客户编码", required = true)
    private String customerCode;
    @ApiModelProperty(value = "账户总金额", required = true)
    private BigDecimal amount;
    @ApiModelProperty(value = "账户可用余额", required = true)
    private BigDecimal balance;

    public String getCustomerType() {
        return customerType;
    }

    public void setCustomerType(String customerType) {
        this.customerType = customerType;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getBalance() {
        return balance;
    }

    public void setBalance(BigDecimal balance) {
        this.balance = balance;
    }
}
