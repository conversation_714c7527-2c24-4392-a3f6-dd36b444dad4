package com.akucun.account.proxy.facade.stub.others.account.req;

import com.akucun.account.center.client.constants.CurrencyTypeConstants;
import com.akucun.account.center.client.constants.OperationTypeConstants;
import com.akucun.fps.pingan.client.constants.AccountCustPropertyType;
import com.mengxiang.base.common.utils.datamasking.Sensitive;
import com.mengxiang.base.common.utils.datamasking.SensitiveType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: silei
 * @Date: 2021/1/13
 * @desc: 账户操作请求参数
 */
@ApiModel("账户操作请求参数")
@ToString
public class AccountOperateInfoReq implements Serializable {

    private static final long serialVersionUID = 4164091915582727456L;
    /**
     * 账户类型key
     * @see com.akucun.account.proxy.facade.stub.enums.AccountKeyConstants
     * accountTypeKey
     */
    @NotBlank(message = "账户类型key不能为空")
    @ApiModelProperty("账户类型key")
    private String accountTypeKey;

    /**
     * 客户编码
     */
    @NotBlank(message = "客户编码不能为空")
    @ApiModelProperty("客户编码")
    private String customerCode;

    /**
     * 客户名称
     */
    @NotBlank(message = "客户名称不能为空")
    @ApiModelProperty("客户名称")
    private String customerName;

    /**
     * 店铺ID
     */
    @ApiModelProperty("店铺ID，店长时shopId不能为空")
    private String shopId;
    /**
     * 真实名称
     */
    @ApiModelProperty("真实名称")
    @Sensitive(type = SensitiveType.Name)
    private String realName;

    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空")
    @ApiModelProperty("手机号")
    @Sensitive(type = SensitiveType.Phone)
    private String phone;

    /**
     * 操作类型,
     * CREATE：创建账户,
     * LOCK：锁定账户,能进不能出
     * UNLOCK：解锁账户
     * FREEZE：冻结账户,不能进也不能出
     * UNFREEZE：解冻账户
     * MODIFY：修改账户信息
     * @see OperationTypeConstants
     *
     */
    @NotBlank(message = "账户操作类型不能为空")
    @ApiModelProperty("账户操作类型")
    private String operationType;

    /**
     * 账户交易密码
     */
    @ApiModelProperty("账户交易密码")
    @Sensitive(type = SensitiveType.Password)
    private String password;

    /**
     * 账户免密交易额度
     */
    @ApiModelProperty("账户免密交易额度")
    private BigDecimal limit;

    /**
     * 币种
     * @see CurrencyTypeConstants
     */
    @ApiModelProperty("币种")
    private String currency;

    /**
     * 保留域
     */
    @ApiModelProperty("保留域")
    private String reserve;

    /**
     * 账户属性 code  注意：该字段是否要传，请咨询接口提供方
     * @see AccountCustPropertyType
     * @return
     */
    @ApiModelProperty("账户属性")
    private String custProperty;

    /**
     * 租户ID
     */
    @ApiModelProperty("租户ID，租户下的店主店长开户必传")
    private String tenantId;

    public String getAccountTypeKey() {
        return accountTypeKey;
    }

    public void setAccountTypeKey(String accountTypeKey) {
        this.accountTypeKey = accountTypeKey;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getShopId() {
        return shopId;
    }

    public void setShopId(String shopId) {
        this.shopId = shopId;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getOperationType() {
        return operationType;
    }

    public void setOperationType(String operationType) {
        this.operationType = operationType;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public BigDecimal getLimit() {
        return limit;
    }

    public void setLimit(BigDecimal limit) {
        this.limit = limit;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getReserve() {
        return reserve;
    }

    public void setReserve(String reserve) {
        this.reserve = reserve;
    }

    public String getCustProperty() {
        return custProperty;
    }

    public void setCustProperty(String custProperty) {
        this.custProperty = custProperty;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }
}
