package com.akucun.account.proxy.facade.stub.fallback;

import com.akucun.account.proxy.facade.stub.account.PromoTradeFacade;
import com.akucun.account.proxy.facade.stub.others.dto.req.*;
import com.akucun.account.proxy.facade.stub.others.dto.req.BatchBonusPayReq;
import com.akucun.account.proxy.facade.stub.others.dto.req.BonusPayReq;
import com.akucun.account.proxy.facade.stub.others.dto.req.TaxReq;
import com.akucun.account.proxy.facade.stub.others.dto.res.BonusPayInfoResp;
import com.akucun.account.proxy.facade.stub.others.dto.res.BonusPayResp;
import com.akucun.account.proxy.facade.stub.others.dto.res.OANotifyDTO;
import com.akucun.account.proxy.facade.stub.others.dto.res.TaxQueryResp;
import com.akucun.account.proxy.facade.stub.others.oa.req.OAWorkflowStatusNotifyRequest;
import com.akucun.common.Result;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Create on : 2025/1/7 13:59
 **/
@Component
public class PromoTradeFacadeFallbackFactory implements FallbackFactory<PromoTradeFacade> {
    @Override
    public PromoTradeFacade create(Throwable throwable) {
        return new PromoTradeFacade(){

            @Override
            public Result<BonusPayResp> dealTrade(BonusPayReq tradeInfo) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<BonusPayResp> batchDealTrade(BatchBonusPayReq batchBonusPayReq) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<BonusPayInfoResp> queryTradeInfo(BonusPayQueryReq bonusPayQueryReq) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<BonusPayResp> submit(BonusPaySubmitReq bonusPaySubmitReq) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<TaxQueryResp> calc(TaxReq taxQueryReq) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<List<TaxQueryResp>> batchCalc(BatchTaxReq batchTaxReq) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<Boolean> oaWorkflowNotify(OAWorkflowStatusNotifyRequest notifyMsg) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<OANotifyDTO> queryStatus(BonusPayQueryReq queryReq) {
                return Result.error(500, "服务超时熔断");
            }

            @Override
            public Result<Integer> saveOrUpdateMentorInfo(MentorInfoDTO mentorInfo) {
                return Result.error(500, "服务超时熔断");
            }
        };
    }
}
