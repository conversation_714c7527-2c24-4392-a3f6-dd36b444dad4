package com.akucun.account.proxy.facade.stub.enums;

import lombok.Getter;

@Getter
public enum IdentifyEnum {

    IDCARD(1, "身份证"),
    CREDIT_CODE(73, "统一社会信用代码"),
    HONGKONG_MACAO_TAI_TRAVEL_PERMIT(3, "港澳台居民通行证"),
    CHINESE_PASSPORT(4, "中国护照"),
    FOREIGN_PASSPORT(19, "外国护照");

    private int code;

    private String desc;

    IdentifyEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
