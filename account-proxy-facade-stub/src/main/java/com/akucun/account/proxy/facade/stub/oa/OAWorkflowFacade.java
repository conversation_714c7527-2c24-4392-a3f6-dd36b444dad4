package com.akucun.account.proxy.facade.stub.oa;

import com.aikucun.common2.base.Result;
import com.akucun.account.proxy.facade.stub.fallback.OAWorkflowFacadeFallbackFactory;
import com.akucun.account.proxy.facade.stub.others.oa.req.OAWorkflowCreateRequest;
import com.akucun.account.proxy.facade.stub.others.oa.req.OAWorkflowQueryRequest;
import com.akucun.account.proxy.facade.stub.others.oa.resp.OAWorkflowCreateResponse;
import com.akucun.account.proxy.facade.stub.others.oa.resp.OAWorkflowResponseInfo;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Create on : 2025/1/14 16:50
 **/
@FeignClient(value = "account-proxy", fallbackFactory = OAWorkflowFacadeFallbackFactory.class)
@RequestMapping("/api/account/proxy/oa/workflow")
public interface OAWorkflowFacade {

    /**
     * 创建OA工作流程
     * @param request
     * @return
     */
    @PostMapping(value = "/create", produces = "application/json;charset=utf-8")
    Result<OAWorkflowCreateResponse> create(@RequestBody OAWorkflowCreateRequest request);

    /**
     * 查询OA工作流程
     * @param request
     * @return
     */
    @PostMapping(value = "/queryWorkflowByRequestId", produces = "application/json;charset=utf-8")
    Result<OAWorkflowResponseInfo> queryWorkflowByRequestId(@RequestBody OAWorkflowQueryRequest request);

}
