<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.akucun.account.proxy</groupId>
    <artifactId>account-proxy</artifactId>
    <packaging>pom</packaging>
    <version>1.0.6</version>

    <parent>
        <groupId>com.akucun.cloud.parent</groupId>
        <artifactId>akucun-cloud-parent</artifactId>
        <version>1.0.10</version>
    </parent>

    <properties>
        <jsoup.version>1.11.3</jsoup.version>
        <hutool.version>5.0.7</hutool.version>
        <xxl.job.version>2.2.0</xxl.job.version>
        <mybatis-plus.version>3.3.0</mybatis-plus.version>
        <mybatis.version>3.5.3</mybatis.version>
        <dubbo.spring.boot.starter>2.0.0</dubbo.spring.boot.starter>
        <zookeeper.version>3.4.6</zookeeper.version>
        <zkclient.version>0.10</zkclient.version>
        <swagger.version>1.5.20</swagger.version>
        <validation-api.version>2.0.1.Final</validation-api.version>
        <hibernate-validator.version>6.0.13.Final</hibernate-validator.version>
        <sharding.version>3.0.0</sharding.version>

        <!--二方包-->
        <account-center-feign.version>1.4.3</account-center-feign.version>
        <pingan.feign.version>3.2.6</pingan.feign.version>
        <pay-base-common.version>1.0.12</pay-base-common.version>
        <mshop-member-core-service-facade-common.version>1.9.2</mshop-member-core-service-facade-common.version>
        <akucun-common-security.version>1.1.7</akucun-common-security.version>
        <account-client.version>3.3.20</account-client.version>
        <fps-common.version>2.0.4</fps-common.version>
        <maihaoche-rocketmq.version>2.0.3</maihaoche-rocketmq.version>
        <member.center.stub>1.0.1</member.center.stub>
        <mengxiang.base.version>1.0.16</mengxiang.base.version>
        <logback.version>1.2.11</logback.version>
        <fastjson.version>1.2.83</fastjson.version>
        <jackson.version>2.13.2</jackson.version>
        <xstream.version>1.4.18</xstream.version>
        <fin-clearing-core.version>1.0.1</fin-clearing-core.version>
        <akucun-sms-version>1.0.1</akucun-sms-version>
        <tenant-core-service-facade-common.version>1.2.68</tenant-core-service-facade-common.version>
        <fileupload-facade-version>1.3.0</fileupload-facade-version>
    </properties>

    
    <dependencies>
        <dependency>
            <groupId>com.aikucun.common2</groupId>
            <artifactId>common2-log</artifactId>
            <version>1.2.10</version>
        </dependency>
    </dependencies>
    
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.akucun.account.proxy</groupId>
                <artifactId>account-proxy-facade-stub</artifactId>
                <version>1.1.17</version>
            </dependency>
            <dependency>
                <groupId>com.akucun.account.proxy</groupId>
                <artifactId>account-proxy-service</artifactId>
                <version>${project.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.aikucun.common2</groupId>
                        <artifactId>common2-log</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.akucun.account.proxy</groupId>
                <artifactId>account-proxy-common</artifactId>
                <version>${project.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.aikucun.common2</groupId>
                        <artifactId>common2-log</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.akucun.account.proxy</groupId>
                <artifactId>account-proxy-dao</artifactId>
                <version>${project.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.aikucun.common2</groupId>
                        <artifactId>common2-log</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.akucun.account.proxy</groupId>
                <artifactId>account-proxy-client</artifactId>
                <version>${project.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.aikucun.common2</groupId>
                        <artifactId>common2-log</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
             <dependency>
                <groupId>com.mengxiang.fin.clearing.core</groupId>
                <artifactId>fin-clearing-core-service-facade-common</artifactId>
                <version>${fin-clearing-core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mengxiang.member</groupId>
                <artifactId>mshop-member-core-service-facade-common</artifactId>
                <version>${mshop-member-core-service-facade-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis</artifactId>
                <version>${mybatis.version}</version>
            </dependency>
            <!-- xxl Schedule -->
            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl.job.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <!--swagger-->
            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-annotations</artifactId>
                <version>${swagger.version}</version>
                <scope>compile</scope>
            </dependency>
            <!-- dubbo-springboot -->
            <dependency>
                <groupId>com.alibaba.spring.boot</groupId>
                <artifactId>dubbo-spring-boot-starter</artifactId>
                <version>${dubbo.spring.boot.starter}</version>
            </dependency>

            <!-- zookeeper -->
            <dependency>
                <groupId>org.apache.zookeeper</groupId>
                <artifactId>zookeeper</artifactId>
                <version>${zookeeper.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- zkclient -->
            <dependency>
                <groupId>com.101tec</groupId>
                <artifactId>zkclient</artifactId>
                <version>${zkclient.version}</version>
            </dependency>
            <dependency>
                <groupId>com.akucun.fps</groupId>
                <artifactId>pingan-feign</artifactId>
                <version>${pingan.feign.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.mybatis</groupId>
                        <artifactId>mybatis-spring</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.aikucun.common2</groupId>
                        <artifactId>common2-log</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>javax.validation</groupId>
                <artifactId>validation-api</artifactId>
                <version>${validation-api.version}</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate.validator</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>${hibernate-validator.version}</version>
            </dependency>
            <dependency>
                <groupId>com.akucun.pay.base</groupId>
                <artifactId>pay-base-common</artifactId>
                <version>${pay-base-common.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.aikucun.common2</groupId>
                        <artifactId>common2-base</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.aikucun.common2</groupId>
                        <artifactId>common2-apollo-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.aikucun.common2</groupId>
                        <artifactId>common2-log</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.micrometer</groupId>
                        <artifactId>micrometer-spring-legacy</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--account-center-->
            <dependency>
                <groupId>com.akucun.account.center</groupId>
                <artifactId>account-center-feign</artifactId>
                <version>${account-center-feign.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.aikucun.common2</groupId>
                        <artifactId>common2-log</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.akucun.fps</groupId>
                <artifactId>account-client</artifactId>
                <version>${account-client.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.aikucun.common2</groupId>
                        <artifactId>common2-log</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.akucun</groupId>
                <artifactId>akucun-common-security</artifactId>
                <version>${akucun-common-security.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.aikucun.common2</groupId>
                        <artifactId>common2-log</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.akucun.fps</groupId>
                <artifactId>fps-common</artifactId>
                <version>${fps-common.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>dubbo</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.mybatis</groupId>
                        <artifactId>mybatis-spring</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>druid</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.aikucun.common2</groupId>
                        <artifactId>common2-log</artifactId>
                    </exclusion>
                </exclusions>
                <scope>compile</scope>
            </dependency>
            <!-- sharding -->
            <dependency>
                <groupId>io.shardingsphere</groupId>
                <artifactId>sharding-jdbc-core</artifactId>
                <version>${sharding.version}</version>
            </dependency>
            <dependency>
                <groupId>com.akucun</groupId>
                <artifactId>member-api</artifactId>
                <version>1.0.1</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.akucun</groupId>
                        <artifactId>akucun-basic-common</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.aikucun.common2</groupId>
                        <artifactId>common2-log</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.maihaoche</groupId>
                <artifactId>spring-boot-starter-rocketmq</artifactId>
                <version>${maihaoche-rocketmq.version}</version>
            </dependency>
            <!--日志组件，其他日志组件全排掉-->
            
            <dependency>
                <groupId>com.mengxiang.base</groupId>
                <artifactId>common-log</artifactId>
                <version>${mengxiang.base.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.mengxiang.base</groupId>
                        <artifactId>common-apollo</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.akucun.pay.gateway.wxv2</groupId>
                <artifactId>pay-gateway-wx-v2-facade-stub</artifactId>
                <version>1.0.3</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.aikucun.common2</groupId>
                        <artifactId>common2-log</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.akucun.pay.gateway.wx</groupId>
                <artifactId>pay-gateway-wx-facade-stub</artifactId>
                <version>1.0.11</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.aikucun.common2</groupId>
                        <artifactId>common2-log</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.akucun.member.center</groupId>
                <artifactId>member-center-stub</artifactId>
                <version>${member.center.stub}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>akucun-common-starter</artifactId>
                        <groupId>com.akucun</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>akucun-common-base</artifactId>
                        <groupId>com.akucun</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.aikucun.common2</groupId>
                        <artifactId>common2-log</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <artifactId>member-center-audit-facade-stub</artifactId>
                <groupId>com.akucun.member.audit</groupId>
                <version>1.3.15</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.aikucun.common2</groupId>
                        <artifactId>common2-log</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.springframework.data</groupId>
                <artifactId>spring-data-redis</artifactId>
                <version>1.8.10.RELEASE</version>
                <exclusions>
                    <exclusion>
                        <groupId>ch.qos.logback</groupId>
                        <artifactId>logback-classic</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>ch.qos.logback</groupId>
                        <artifactId>logback-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- 分布式ID生成器 -->
            <dependency>
                <groupId>com.mengxiang.base</groupId>
                <artifactId>common-sequence</artifactId>
                <version>${mengxiang.base.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.mengxiang.base</groupId>
                        <artifactId>common-apollo</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.aikucun.common2</groupId>
                        <artifactId>common2-log</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--微信提现-->
            <dependency>
                <groupId>com.akucun.bcs.channel</groupId>
                <artifactId>bcs-channel-facade-stub</artifactId>
                <version>1.0.3</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.aikucun.common2</groupId>
                        <artifactId>common2-log</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.akucun.fps</groupId>
                <artifactId>bill-client</artifactId>
                <version>3.1.4</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.aikucun.common2</groupId>
                        <artifactId>common2-log</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.akucun</groupId>
                <artifactId>akucun-api</artifactId>
                <version>0.1.0</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.aikucun.common2</groupId>
                        <artifactId>common2-log</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.mengxiang.account.finpointcore</groupId>
                <artifactId>fin-point-core-service-facade-common</artifactId>
                <version>1.0.1</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.aikucun.common2</groupId>
                        <artifactId>common2-log</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.mengxiang.base</groupId>
                <artifactId>common-rpc-scan</artifactId>
                <version>1.0.12</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.aikucun.common2</groupId>
                        <artifactId>common2-log</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-classic</artifactId>
                <version>${logback.version}</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-core</artifactId>
                <version>${logback.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-annotations</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.thoughtworks.xstream</groupId>
                <artifactId>xstream</artifactId>
                <version>${xstream.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mengxiang.saas</groupId>
                <artifactId>tenant-core-service-facade-common</artifactId>
                <version>${tenant-core-service-facade-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mengxiang.base</groupId>
                <artifactId>base-metrics-client</artifactId>
                <version>1.0.5</version>
            </dependency>
            <dependency>
                <groupId>com.akucun.sms</groupId>
                <artifactId>akucun-sms-stub</artifactId>
                <version>${akucun-sms-version}</version>
            </dependency>

            <dependency>
                <groupId>com.mengxiang.base</groupId>
                <artifactId>base-sentinel-client</artifactId>
                <version>1.0.2</version>
            </dependency>

            <dependency>
                <groupId>com.akucun.bond</groupId>
                <artifactId>bond-client</artifactId>
                <version>0.3.4</version>
            </dependency>
            <dependency>
                <groupId>com.akucun.bond</groupId>
                <artifactId>bond-feign</artifactId>
                <version>0.3.4</version>
            </dependency>

            <dependency>
                <groupId>com.akucun.bcs.bill</groupId>
                <artifactId>bcs-bill-facade-stub</artifactId>
                <version>2.9.5</version>
            </dependency>

            <dependency>
                <groupId>com.akucun.aftersale</groupId>
                <artifactId>akucun-aftersale-facade-mgt-stub</artifactId>
                <version>3.2.2</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.x</groupId>
                        <artifactId>product-swagger</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.akucun.cs.common</groupId>
                        <artifactId>akucun-cs-common-util</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.akucun</groupId>
                        <artifactId>akucun-common-base</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.akucun</groupId>
                        <artifactId>akucun-common-starter</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>druid</artifactId>
                        <groupId>com.alibaba</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>druid-spring-boot-starter</artifactId>
                        <groupId>com.alibaba</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.micrometer</groupId>
                        <artifactId>micrometer-spring-legacy</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>akucun-cs-common-security</artifactId>
                        <groupId>com.akucun.cs.common</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.mengxiang.base</groupId>
                <artifactId>transaction-framework</artifactId>
                <version>1.1.2</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-csv</artifactId>
                <version>1.8</version>
            </dependency>
            <dependency>
                <groupId>com.aikucun</groupId>
                <artifactId>fileupload-facade</artifactId>
                <version>${fileupload-facade-version}</version>
            </dependency>
        </dependencies>

    </dependencyManagement>

    <modules>
        <module>account-proxy-common</module>
        <module>account-proxy-dao</module>
        <module>account-proxy-service</module>
        <module>account-proxy-facade</module>
        <module>account-proxy-facade-stub</module>
        <module>account-proxy-client</module>
    </modules>

</project>
